import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { AccentIcon } from './accent-icon';
import { Button } from '../button';

export const scope = { AccentIcon, Button };

An `AccentIcon` is larger than a regular `Icon` and is used to add more visual detail and interest to a page.

To see all available Accent Icons, please see [Foundations: Accent Icons](/docs/foundations-accent-icons--docs).

## Variations

### Default AccentIcon

The `AccentIcon` component displays a specific [Accent Icon](/docs/foundations-accent-icons--docs) via the required `name` prop.

export const defaultCode = `
  <div style={{ display: 'flex', gap: 'var(--evr-paragraph-spacing-sm)' }}>
    <AccentIcon name='pin' />
    <AccentIcon name="car" />
    <AccentIcon name="rings" />
    <AccentIcon name="creditCard" />
    <AccentIcon name="deviceComputer" />
  </div>
`;

<CodeExample scope={scope} code={defaultCode} />

### Sizing

Using the `size` prop, the size of the `AccentIcon` can be changed.
There are three available sizes:

| Size | Dimensions (px) | Dimensions (rem) |
| ---- | --------------- | ---------------- |
| sm   | 40x40           | 2.5x2.5          |
| md   | 48x48           | 3x3              |
| lg   | 56x56           | 3.5x3.5          |

export const sizeCode = `
    <div style={{ display: 'flex', gap: 'var(--evr-paragraph-spacing-sm)' }}>
        <AccentIcon name='compass' size="sm" />
        <AccentIcon name='compass' size="md" />
        <AccentIcon name='compass' size="lg" />
    </div>
`;

<CodeExample scope={scope} code={sizeCode} />

### Using baseFill and accentFill Props

Using the `baseFill` and `accentFill` props, the color of the `AccentIcon` can be changed.

export const fillCode = `
  <AccentIcon name='gift' size="lg" accentFill='--evr-borders-status-success' />
`;

<CodeExample scope={scope} code={fillCode} />

## Accessing AccentIcon using ref

Click on the Button to access the `AccentIcon`, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            columnGap: '10px'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
            <AccentIcon ref={ref} name='airplane'/>
        </Row>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Accent Icons are used to help convey complex ideas (ex. sub features, feature descriptions).
They can be the main entry point to a product category (Time away, benefits, etc) or flow.

[Icons](/docs/foundations-icons--docs) are distinct from [Accent Icons](/docs/foundations-accent-icons--docs), and should not be used interchangably.

## Accessibility

`AccentIcon` is a decorative component - it is not focusable or screen reader enabled. The `aria-hidden` attribute is set to true.
