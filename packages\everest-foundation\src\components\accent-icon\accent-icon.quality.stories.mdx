import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { AccentIcon } from '.';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/AccentIcon"
  component={AccentIcon}
  parameters={{
    chromatic: {
      chromatic: Chromatic.ENABLE_CI,
    },
  }}
  args={{
    name: 'currency',
    testId: 'test-id',
  }}
/>

# AccentIcon

## Live Demo

<Canvas>
  <Story name="Small">
    {(args) => (
      <>
        <AccentIcon {...args} size="sm" />
        <AccentIcon {...args} baseFill="--evr-interactive-status-error-default" size="sm" />
        <AccentIcon {...args} accentFill="--evr-interactive-status-warning-pressed" size="sm" />
      </>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Medium">
    {(args) => (
      <>
        <AccentIcon {...args} size="md" />
        <AccentIcon {...args} baseFill="--evr-interactive-status-error-default" size="md" />
        <AccentIcon {...args} accentFill="--evr-interactive-status-warning-pressed" size="md" />
      </>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Large">
    {(args) => (
      <>
        <AccentIcon {...args} size="lg" />
        <AccentIcon {...args} baseFill="--evr-interactive-status-error-default" size="lg" />
        <AccentIcon {...args} accentFill="--evr-interactive-status-warning-pressed" size="lg" />
      </>
    )}
  </Story>
</Canvas>
