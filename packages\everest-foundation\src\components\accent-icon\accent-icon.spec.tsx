import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';

import { AccentIcon } from './accent-icon';

// Mock the useIconContext hook
jest.mock('../icon-provider', () => ({
  useIconContext: jest.fn(() => ({
    accentIconManifest: {
      alert: { paths: '<svg><path data-evr-path="af"></path><path data-evr-path="bf"></path></svg>' },
    },
  })),
}));

// Mock getTokenValue function
jest.mock('@ceridianhcm/everest-cdk', () => ({
  getTokenValue: jest.fn((token) => {
    if (token === '--evr-interactive-primary-default') {
      return '#3067DB';
    }
    if (token === '--evr-content-primary-highemp') {
      return '#1F1F1F';
    }
    return '';
  }),
}));

describe('[AccentIcon]', () => {
  const testId = 'accent-icon-test-id';
  const testToken = '--evr-borders-status-error';

  it('should update the svg accentFill attribute', async () => {
    render(<AccentIcon testId={testId} name="alert" accentFill={testToken} size="md" />);
    const svgElement = await screen.findByTestId(testId);

    // Ensure that the SVG element is found
    expect(svgElement).toBeInTheDocument();

    // Find the path element inside the SVG
    const pathElements = await waitFor(() => svgElement.querySelectorAll('path'));
    const accentPathElement = pathElements[0]; // First path

    // Check if the fill attribute is set correctly
    expect(accentPathElement).toHaveAttribute('fill', `var(${testToken})`);
  });

  it('should update the svg baseFill attribute', async () => {
    render(<AccentIcon testId={testId} name="alert" baseFill={testToken} size="md" />);
    const svgElement = await screen.findByTestId(testId);

    // Ensure that the SVG element is found
    expect(svgElement).toBeInTheDocument();

    // Find the path element inside the SVG
    const pathElements = await waitFor(() => svgElement.querySelectorAll('path'));
    const basePathElement = pathElements[1]; // Second path

    // Check if the fill attribute is set correctly
    expect(basePathElement).toHaveAttribute('fill', `var(${testToken})`);
  });
});
