import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { AccentIcon, ACCENT_ICON_NAMES, ACCENT_ICON_COLORS } from './accent-icon';
import Examples from './accent-icon.examples.mdx';

<Meta
  title="Components/Icons/AccentIcon"
  component={AccentIcon}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/ej2kr7CiVtmRmPhREFWirB/Everest-Assets?node-id=610-648&p=f&m=dev',
    },
  }}
  argTypes={{
    name: {
      control: 'select',
      options: ACCENT_ICON_NAMES.sort(),
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
    accentFill: {
      control: 'select',
      options: ACCENT_ICON_COLORS.sort(),
    },
    baseFill: {
      control: 'select',
      options: ACCENT_ICON_COLORS.sort(),
    },
  }}
  args={{
    name: 'airplane',
    size: 'md',
    testId: 'test-id',
    accentFill: '--evr-interactive-primary-default',
    baseFill: '--evr-content-primary-highemp',
  }}
/>

# AccentIcon

<Examples />

## Live Demo

<Canvas>
  <Story name="AccentIcon">{(args) => <AccentIcon {...args} />}</Story>
</Canvas>

<ArgsTable story="AccentIcon" />
