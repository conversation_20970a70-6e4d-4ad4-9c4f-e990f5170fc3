import React, { useLayoutEffect, useRef } from 'react';
import { accentIconNames } from '@ceridianhcm/platform-df-assets';
import { MarkupUtil } from '@platform/core';
import classnames from 'classnames';

import { mergeRefs } from '../../utils';
import { ICON_COLORS } from '../icon';
import { useIconContext } from '../icon-provider';

import styles from './accent-icon.module.scss';

export const ACCENT_ICON_NAMES = accentIconNames;
export const ACCENT_ICON_COLORS = ICON_COLORS;

export type TAccentIconColor = (typeof ACCENT_ICON_COLORS)[number];
export type TAccentIconName = (typeof accentIconNames)[number];

export type TAccentIconSize = 'sm' | 'md' | 'lg';
export interface IAccentIconProps {
  /**
   * Name of the icon.
   */
  name: TAccentIconName;
  /**
   * Size of the icon.
   * @default md
   */
  size?: TAccentIconSize;
  /**
   * An id used for automation testing.
   */
  testId?: string;
  /**
   * Optional unique identifier.
   */
  id?: string;
  /**
   * Changes color of accent stroke.
   * @default --evr-interactive-primary-default
   */
  accentFill?: string;
  /**
   * Changes color of main stroke.
   * @default --evr-content-primary-highemp
   */
  baseFill?: string;
}

export const AccentIcon = React.forwardRef<SVGSVGElement, IAccentIconProps>((props: IAccentIconProps, ref) => {
  const {
    id,
    testId,
    baseFill = '--evr-content-primary-highemp',
    accentFill = '--evr-interactive-primary-default',
    name,
    size = 'md',
  } = props;
  const svgRef = useRef<SVGSVGElement | null>(null);

  const { accentIconManifest } = useIconContext() as unknown as {
    accentIconManifest: Record<TAccentIconName, { paths: string }>;
  };

  useLayoutEffect(() => {
    const svgElement = svgRef.current;
    if (!svgElement && !accentIconManifest) {
      return;
    }

    const elementsToUpdate = svgElement?.querySelectorAll('[data-evr-path]');
    if (!elementsToUpdate) {
      return;
    }

    // Define data-type to CSS variable mappings
    const tokenMappings = [
      { dataType: 'af', cssVar: accentFill, attribute: 'fill' }, // e.g., Accent color variable
      { dataType: 'as', cssVar: accentFill, attribute: 'stroke' }, // e.g., Accent color variable
      { dataType: 'bf', cssVar: baseFill, attribute: 'fill' }, // e.g., Primary color variable
      { dataType: 'bs', cssVar: baseFill, attribute: 'stroke' }, // e.g., Primary color variable
    ];

    tokenMappings.forEach(({ dataType, cssVar, attribute }) => {
      const elementsToStyle = Array.from(elementsToUpdate).filter((child) => {
        // Filter elements that match the specific data-type
        return child.getAttribute('data-evr-path') === dataType;
      });

      elementsToStyle.forEach((child) => {
        child.setAttribute(attribute, `var(${cssVar})`);
      });
    });
  }, [svgRef, accentIconManifest, accentFill, baseFill, name]);

  return (
    <svg
      id={id}
      data-evr-name={name}
      data-testid={testId}
      viewBox="0 0 40 40"
      className={classnames(styles.evrAccentIcon, styles[size])}
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden={true}
      ref={mergeRefs([ref, svgRef])}
      dangerouslySetInnerHTML={{
        // eslint-disable-next-line @typescript-eslint/naming-convention
        __html: MarkupUtil.replaceMarkupTags(accentIconManifest?.[name]?.paths || '').toString(),
      }}
    ></svg>
  );
});

AccentIcon.displayName = 'AccentIcon';
