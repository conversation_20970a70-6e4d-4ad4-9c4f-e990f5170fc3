import React, { PropsWithChildren, useContext } from 'react';
import classNames from 'classnames';

import { TAccordionSize, TAccordionVariant } from './accordion-context';
import { AccordionDetailsContext } from './accordion-detail-context';

import styles from './accordion.module.scss';

export interface IAccordionContent {
  testId?: string;
}

export const AccordionContent = React.forwardRef<HTMLDivElement, PropsWithChildren<IAccordionContent>>(
  (props, ref): JSX.Element => {
    const { id: detailsId, open, size, variant } = useContext(AccordionDetailsContext);
    const { testId, children } = props;

    const contentSpacing = (size: TAccordionSize | undefined, variant: TAccordionVariant | undefined) => {
      if (open) {
        if (variant === 'divider') {
          switch (size) {
            case 'sm':
            case 'md':
              return styles.contentSizeDivider;
            case 'lg':
              return styles.contentSizeDividerLG;
          }
        } else {
          return styles.contentSizeCard;
        }
      }
    };

    /**
     * we will use 2 divs to handle animations for opening and closing
     * If we use one div, when the animation for closing takes place it makes the padding disappear before the animation finishes
     */
    return (
      <div
        id={`${detailsId}-content`}
        data-testid={testId}
        ref={ref}
        role="region"
        aria-labelledby={`${detailsId}-summary`}
        className={classNames(styles.evrAccordionContent, {
          [styles.isOpen]: open,
        })}
      >
        <div className={classNames(styles.evrAccordionContentSpacing, contentSpacing(size, variant))}>{children}</div>
      </div>
    );
  }
);

AccordionContent.displayName = 'AccordionContent';
