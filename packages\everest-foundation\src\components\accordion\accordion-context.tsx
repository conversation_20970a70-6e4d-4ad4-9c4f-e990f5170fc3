import React from 'react';

import { TIconName } from '../icon';

export type TAccordionSize = 'sm' | 'md' | 'lg';
export type TAccordionVariant = 'divider' | 'card' | 'basic';
export type TAccordionIconPosition = 'start' | 'end' | 'startTop' | 'endTop';

export interface IAccordionContext {
  size?: TAccordionSize;
  variant?: TAccordionVariant;
  openIconName?: TIconName;
  closeIconName?: TIconName;
  iconPosition?: TAccordionIconPosition;
  dividerLowEmphasis?: boolean;
  elevated?: boolean;
  onToggle?: (id: string) => void;
}

export const defaultContext: IAccordionContext = {
  size: 'md',
  variant: 'card',
  iconPosition: 'end',
  openIconName: 'chevronUp',
  closeIconName: 'chevronDown',
  dividerLowEmphasis: false,
  elevated: false,
};

// esLint doesn't like this context object capitalized -- ignoring rule
// eslint-disable-next-line @typescript-eslint/naming-convention
export const AccordionContext = React.createContext<IAccordionContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  AccordionContext.displayName = 'AccordionContext';
}
