import React from 'react';

import { IAccordionContext, defaultContext as defaultAccordionContext } from './accordion-context';

export interface IAccordionDetailsContext extends IAccordionContext {
  id: string;
  open?: boolean;
}

const defaultContext: IAccordionDetailsContext = {
  id: '',
  ...defaultAccordionContext,
};

// esLint doesn't like this context object capitalized -- ignoring rule
// eslint-disable-next-line @typescript-eslint/naming-convention
export const AccordionDetailsContext = React.createContext<IAccordionDetailsContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  AccordionDetailsContext.displayName = 'AccordionDetailsContext';
}
