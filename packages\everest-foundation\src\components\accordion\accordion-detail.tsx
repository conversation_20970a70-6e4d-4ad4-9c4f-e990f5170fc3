import React, { PropsWithChildren, forwardRef, useMemo } from 'react';
import classNames from 'classnames';

import { AccordionContext, IAccordionContext } from './accordion-context';
import { AccordionDetailsContext } from './accordion-detail-context';
import { resolvePropsContext } from '../../utils/resolve-props-context';

import styles from './accordion.module.scss';

export interface IAccordionDetail extends IAccordionContext {
  id: string;
  testId?: string;
  open: boolean;
}

export const AccordionDetail = forwardRef<HTMLDivElement, PropsWithChildren<IAccordionDetail>>(
  (props, ref): JSX.Element => {
    const context = React.useContext(AccordionContext);
    const {
      id,
      testId,
      open,
      size,
      variant,
      elevated,
      dividerLowEmphasis,
      iconPosition,
      openIconName,
      closeIconName,
      onToggle,
    } = resolvePropsContext<IAccordionDetail, IAccordionContext>(props, context);

    const detailsContext = useMemo(
      () => ({
        id,
        open,
        size,
        variant,
        elevated,
        dividerLowEmphasis,
        iconPosition,
        openIconName,
        closeIconName,
        onToggle,
      }),
      [closeIconName, dividerLowEmphasis, elevated, id, onToggle, open, openIconName, iconPosition, size, variant]
    );
    const isCard = variant === 'card';
    const isElevatedCard = elevated;

    return (
      <AccordionDetailsContext.Provider value={detailsContext}>
        <div
          id={id}
          data-testid={testId}
          ref={ref}
          className={classNames(styles.evrAccordionDetail, {
            [styles.defaultCard]: isCard,
            [styles.elevatedCard]: isCard && isElevatedCard,
          })}
        >
          {props.children}
        </div>
      </AccordionDetailsContext.Provider>
    );
  }
);

AccordionDetail.displayName = 'AccordionDetail';
