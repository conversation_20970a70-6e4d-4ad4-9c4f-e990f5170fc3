import React, { PropsWithChildren } from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classNames from 'classnames';

import { TAccordionSize, TAccordionVariant } from './accordion-context';
import { AccordionDetailsContext } from './accordion-detail-context';
import { Icon, TIconName } from '../icon';

import styles from './accordion.module.scss';

export interface IAccordionSummary {
  testId?: string;
}

export const AccordionSummary = React.forwardRef<HTMLDivElement, PropsWithChildren<IAccordionSummary>>(
  (props, ref): JSX.Element => {
    const {
      id: detailsId,
      open,
      size,
      variant,
      dividerLowEmphasis,
      openIconName,
      closeIconName,
      iconPosition,
      onToggle,
    } = React.useContext(AccordionDetailsContext);
    const { testId } = props;

    const openIcon = <Icon name={openIconName as TIconName} size={'md'} fill="--evr-content-primary-highemp" />;
    const closeIcon = <Icon name={closeIconName as TIconName} size={'md'} fill="--evr-content-primary-highemp" />;

    const isDefaultBorder = variant === 'divider' && !dividerLowEmphasis;
    const isLowEmphasisBorder = variant === 'divider' && dividerLowEmphasis;
    const isCard = variant === 'card';
    const isBasic = variant === 'basic';

    const openIconGap = (size: TAccordionSize | undefined) => {
      switch (size) {
        case 'sm':
          return styles.openIconGapSM;
        case 'md':
          return styles.openIconGapMD;
        case 'lg':
          return styles.openIconGapLG;
      }
    };

    const summarySize = (size: TAccordionSize | undefined, variant: TAccordionVariant | undefined) => {
      if (variant === 'divider') {
        switch (size) {
          case 'sm':
            return styles.summarySize;
          case 'md':
            return styles.summarySize;
          case 'lg':
            return styles.summarySizeLG;
        }
      } else {
        // card variant
        return styles.summaryCard;
      }
    };

    const onClickHandler = () => {
      onToggle?.(detailsId);
    };

    const onKeyDownHandler = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onToggle?.(detailsId);
      }
    };

    const handleSummaryFocusRingStyleTransform = (focusRingStyles: React.CSSProperties) => {
      return {
        ...focusRingStyles,
        borderRadius: 'var(--evr-radius-sm)',
        zIndex: 'var(--evr-z-index-inner)', // this is required to render this above any surrounding borders/containers
        position: 'relative' as React.CSSProperties['position'], // this is required for z-index to apply
      };
    };

    return (
      <FocusRing
        canFocusFromMouse
        {...((isCard || isBasic) && { styleTransform: handleSummaryFocusRingStyleTransform })}
      >
        <div
          id={`${detailsId}-summary`}
          data-testid={testId}
          ref={ref}
          role="button"
          tabIndex={0}
          aria-expanded={open}
          aria-controls={`${detailsId}-content`}
          className={classNames(
            styles.evrAccordionSummary,
            {
              [styles.defaultBorder]: isDefaultBorder,
              [styles.lowEmpBorder]: isLowEmphasisBorder,
              [styles.endIconPosition]: iconPosition === 'end',
              [styles.placeIconTop]: iconPosition === 'startTop' || iconPosition === 'endTop',
            },
            summarySize(size, variant),
            openIconGap(size)
          )}
          onClick={onClickHandler}
          onKeyDown={onKeyDownHandler}
        >
          {(iconPosition === 'start' || iconPosition === 'startTop') && (open ? openIcon : closeIcon)}
          {/** Add style to take up full width of AccordionSummary
           * Added in https://dayforce.atlassian.net/browse/PWEB-17533 */}
          <div className={styles.fullWidth}>{props.children}</div>
          {(iconPosition === 'end' || iconPosition === 'endTop') && (open ? openIcon : closeIcon)}
        </div>
      </FocusRing>
    );
  }
);

AccordionSummary.displayName = 'AccordionSummary';
