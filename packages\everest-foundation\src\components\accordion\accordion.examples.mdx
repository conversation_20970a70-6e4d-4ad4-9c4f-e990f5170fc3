import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { Accordion, AccordionDetail, AccordionSummary, AccordionContent } from '.';
import { Icon } from '../icon';
import { Link } from '../link';
import { Divider } from '../divider';
import { ContainerBase, ContainerBodyBase } from '../container-base';

export const scope = {
  Accordion,
  AccordionDetail,
  AccordionSummary,
  AccordionContent,
  Icon,
  Link,
  ContainerBase,
  ContainerBodyBase,
  Divider,
};

The accordion component delivers large amounts of content in a small space through progressive disclosure. The user gets key details about the underlying content and can choose to expand that content within the constraints of the accordion.

## Variations

### Stacked Card Accordion

export const stackedCardAccordion = `() => {
  const [openDetailId, setOpenDetailId] = React.useState('');
  return (
    <div style={{width: '640px'}}>
      <Accordion
        id="accordion-stacked-card"
        openIconName="chevronUp"
        closeIconName="chevronDown"
        onToggle={(value) => {
          if (value === openDetailId) {
            setOpenDetailId('');
          } else {
            setOpenDetailId(value);
          }
        }}
      >
        <AccordionDetail id="accordion-stacked-card-1" open={'accordion-stacked-card-1' === openDetailId}>
          <AccordionSummary>
            <h3 className="evrHeading3">Heading 1</h3>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
              into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
              release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </AccordionContent>
        </AccordionDetail>
        <AccordionDetail id="accordion-stacked-card-2" open={'accordion-stacked-card-2' === openDetailId}>
          <AccordionSummary>
            <h3 className="evrHeading3">Heading 2</h3>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
              into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
              release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum.
            </p>
          </AccordionContent>
        </AccordionDetail>
      </Accordion>
    </div>
  )
}`;

<CodeExample scope={scope} code={stackedCardAccordion} />

### Accordion Variations

export const variationsCode = `() => {
  const styles = {
        accordionWidth: {
            width: '640px',
            marginBottom: '3.5rem'
        },
        textStyle: {
            marginBlockEnd: '0.625rem',
            width: '70%'
        },
    };  
    const Row = ({ children }) => (
        <div>{children}</div>
    );
    const HeaderText = ({ children }) => (
        <div style={styles.textStyle}><p className='evrBodyText evrBold'>{children}</p></div>
    );
    const DescriptionText = ({ children }) => (
        <div style={styles.textStyle}><span className='evrBodyText2'>{children}</span></div>
    );

    const [openDetailCardOutline, setOpenDetailCardOutline] = React.useState(false);
    const [openDetailCardElevated, setOpenDetailCardElevated] = React.useState(false);
    const [openDetailCardDivider, setOpenDetailCardDivider] = React.useState(false);
    const [openDetailCardDividerLowEmp, setOpenDetailCardDividerLowEmp] = React.useState(false);
    const [openDetailCardSmall, setOpenDetailCardSmall] = React.useState(false);
    const [openDetailCardStartTop, setOpenDetailCardStartTop] = React.useState(false);
    const [openBasicCardStartTop, setOpenBasicCardStartTop] = React.useState(false);


     return (
      <div>
          <HeaderText>Accordion with Card Outline</HeaderText>
          <DescriptionText>Set the variant prop to card</DescriptionText>
          <div style={styles.accordionWidth}>
            <AccordionDetail
              id="detail-card-outline"
              open={openDetailCardOutline}
              openIconName="chevronUp"
              closeIconName="chevronDown"
              iconPosition="end"
              onToggle={(value) => {
                setOpenDetailCardOutline(!openDetailCardOutline);
              }}
            >
              <AccordionSummary>
                <Icon name="sparkle" fill="--evr-interactive-neutral-default"/>
                <h3 className="evrHeading3" style={{ paddingLeft: '4px' }}>Heading 1</h3>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                  scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                  into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                  release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                  software like Aldus PageMaker including versions of Lorem Ipsum.
                  <Link id="link-id" href="https://www.dayforce.com" target="_blank">To Dayforce</Link>
                </p>
              </AccordionContent>
            </AccordionDetail>
          </div>

          <HeaderText>Accordion with Card Elevated</HeaderText>
          <DescriptionText>Set the variant prop to card and elevated prop to true</DescriptionText>
          <div style={styles.accordionWidth}>
            <AccordionDetail
              id="detail-card-elevated"
              open={openDetailCardElevated}
              openIconName="chevronUp"
              closeIconName="chevronDown"
              iconPosition="end"
              elevated={true}
              onToggle={(value) => {
                setOpenDetailCardElevated(!openDetailCardElevated);
              }}
            >
              <AccordionSummary>
                <h3 className="evrHeading3">Heading 1</h3>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                  scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                  into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                  release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                  software like Aldus PageMaker including versions of Lorem Ipsum.
                </p>
              </AccordionContent>
            </AccordionDetail>
          </div>

          <HeaderText>Medium Individual Accordion with Default Divider</HeaderText>
          <DescriptionText>Set the variant prop to divider and elevated prop to true</DescriptionText>
          <div style={styles.accordionWidth}>
            <AccordionDetail
              id="detail-divider-default"
              open={openDetailCardDivider}
              openIconName="chevronUp"
              closeIconName="chevronDown"
              iconPosition="end"
              variant="divider"
              onToggle={(value) => {
                setOpenDetailCardDivider(!openDetailCardDivider);
              }}
            >
              <AccordionSummary>
                <h4 className="evrHeading4">Heading 1</h4>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                  scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                  into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                  release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                  software like Aldus PageMaker including versions of Lorem Ipsum.
                </p>
              </AccordionContent>
            </AccordionDetail>
          </div>

          <HeaderText>Medium Individual Accordion with Low Emp Divider</HeaderText>
          <DescriptionText>Set the variant prop to divider, dividerLowEmphasis prop to true</DescriptionText>
          <div style={styles.accordionWidth}>
            <AccordionDetail
              id="detail-divider-low-emphasis"
              open={openDetailCardDividerLowEmp}
              openIconName="chevronUp"
              closeIconName="chevronDown"
              iconPosition="end"
              variant="divider"
              dividerLowEmphasis={true}
              onToggle={(value) => {
                setOpenDetailCardDividerLowEmp(!openDetailCardDividerLowEmp);
              }}
            >
              <AccordionSummary>
                <h4 className="evrHeading4">Heading 1</h4>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                  scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                  into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                  release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                  software like Aldus PageMaker including versions of Lorem Ipsum.
                </p>
              </AccordionContent>
            </AccordionDetail>
          </div>

          <HeaderText>Small Individual Accordion with Icon Position Start</HeaderText>
          <DescriptionText>Set the size prop to sm, iconPosition prop to start</DescriptionText>
           <div style={styles.accordionWidth}>
            <AccordionDetail
              id="detail-icon-position-start"
              open={openDetailCardSmall}
              openIconName="chevronUpSmall"
              closeIconName="chevronDownSmall"
              iconPosition="start"
              size="sm"
              variant="divider"
              onToggle={(value) => {
                setOpenDetailCardSmall(!openDetailCardSmall);
              }}
            >
              <AccordionSummary>
                <p className='evrBodyText evrBold'>Small Heading</p>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                  scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                  into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                  release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                  software like Aldus PageMaker including versions of Lorem Ipsum.
                </p>
              </AccordionContent>
            </AccordionDetail>
          </div>

          <HeaderText>Summary with Multiple Lines and Icon Position StartTop</HeaderText>
          <DescriptionText>Set the iconPosition prop to startTop</DescriptionText>
           <div style={styles.accordionWidth}>
            <AccordionDetail
              id="detail-icon-position-startTop"
              open={openDetailCardStartTop}
              openIconName="chevronUpSmall"
              closeIconName="chevronDownSmall"
              iconPosition="startTop"
              onToggle={(value) => {
                setOpenDetailCardStartTop(!openDetailCardStartTop);
              }}
            >
              <AccordionSummary>
                <div>
                  <h3 className="evrHeading3">Heading 1</h3>
                  <p className='evrBodyText'>Summary</p>
                </div>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                  scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                  into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                  release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                  software like Aldus PageMaker including versions of Lorem Ipsum.
                </p>
              </AccordionContent>
            </AccordionDetail>
          </div>

          <HeaderText>Basic Accordion</HeaderText>
          <DescriptionText>Set the variant prop to basic</DescriptionText>
           <div style={styles.accordionWidth}>
            <AccordionDetail
              id="basic-accordion-variant"
              testId="basic-accordion-variant-test"
              open={openBasicCardStartTop}
              openIconName="chevronUpSmall"
              closeIconName="chevronDownSmall"
              iconPosition="end"
              onToggle={(value) => {
                setOpenBasicCardStartTop(!openBasicCardStartTop);
              }}
              variant="basic"
            >
              <AccordionSummary>
                <h3 className="evrHeading3">Heading 1</h3>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">
                  Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                  industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                  scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                  into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                  release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                  software like Aldus PageMaker including versions of Lorem Ipsum.
                </p>
              </AccordionContent>
            </AccordionDetail>
          </div>
      </div>
    );

}
`;

<CodeExample scope={scope} code={variationsCode} />

### Accordion Wrapped inside ContainerBase

For accordions using the `basic` variant accordion, they can be wrapped inside a <LinkTo kind="Toolbox/ContainerBase">Container Base</LinkTo> component to achieve a customizable look.

export const containerBaseAccordion = `() => {
  const [open, setOpen] = React.useState(false);
  const css = \`
    .demo-container-class {
      width: 90%;
      border-radius: var(--evr-radius-sm);
      margin: var(--evr-spacing-md) auto;
    }

    .grid-item {
      padding: var(--evr-spacing-md);
    }

    .details-grid {
      display: grid;
      grid-template-columns: max-content 1fr;
      gap: var(--evr-spacing-sm) var(--evr-spacing-10xl);
    }

    .section-title {
      grid-column: 1 / -1;
      font-weight: bold;
    }
  \`;

  return (
    <>
      <style>{css}</style>
      <ContainerBase
        id="basic-accordion-container-base"
        testId="basic-accordion-container-base"
        className="demo-container-class"
      >
        <ContainerBodyBase>
          <AccordionDetail
            id="basic-variant-detail-in-container-base"
            testId="basic-variant-detail-in-container-base-test"
            open={open}
            onToggle={(value) => {
              setOpen(!open);
            }}
            openIconName="chevronUpSmall"
            closeIconName="chevronDownSmall"
            iconPosition="end"
            variant="basic"
          >
            <AccordionSummary>
              <h3 className="evrHeading3">Personal Information</h3>
            </AccordionSummary>
            <AccordionContent>
              <ContainerBase className="demo-container-class">
                <ContainerBodyBase>
                  <div className="grid-item details-grid">
                    <div><b>Marital status</b></div>
                    <div>Married</div>
                  </div>

                  <Divider />

                  <div className="grid-item details-grid">
                    <div className="section-title">Gender</div>

                    <div className="section-title">United States of America</div>
                    <div>Assigned sex</div>
                    <div>Female</div>

                    <div className="section-title">United States of America</div>
                    <div>State</div>
                    <div>New Jersey</div>
                    <div>Assigned sex</div>
                    <div>Female</div>
                    <div>Gender identity</div>
                    <div>Female</div>

                    <div className="section-title">United States of America</div>
                    <div>State</div>
                    <div>New York</div>
                    <div>Assigned sex</div>
                    <div>Female</div>
                    <div>Gender identity</div>
                    <div>Female</div>
                  </div>

                  <Divider />

                  <div className="grid-item details-grid">
                    <div><b>Dependents</b></div>
                    <div>David Watts</div>
                    <div></div>
                    <div>Mabel Watts</div>
                  </div>

                  <Divider />

                  <div className="grid-item details-grid">
                    <div><b>Beneficiaries</b></div>
                    <div>David Watts</div>
                  </div>
                </ContainerBodyBase>
              </ContainerBase>
            </AccordionContent>
          </AccordionDetail>
        </ContainerBodyBase>
        { /** If users want a footer, this is where ContainerFooterBase would go */ }
      </ContainerBase>
    </>
  )
}`;

<CodeExample scope={scope} code={containerBaseAccordion} />

## How to Use

The Accordion component is composed of four main components: `Accordion`, `AccordionDetail`, `AccordionContent` and `AccordionSummary`.

- `Accordion` is a layout container wrapping children in React context and can contain arbitrary `AccordionDetail`.
- `AccordionDetail` is a container to build a collapsible component, it takes `AccordionSummary` and `AccordionContent`.
- `AccordionSummary` is div with the role `button` and accepts arbitrary children.
- `AccordionContent` is a container that will handle the padding of the content given the size and will have a role of `region` for accessibility.

`Accordion` and `AccordionDetail` share similar props, the props can be placed on the `Accordion` component and will apply to all `AccordionDetail` components. This will help to avoid repetition of props on each `AccordionDetail`. In the scenario only one Accordion is needed, the props can be placed directly on the `AccordionDetail` component without needing the `Accordion` wrapper component. For example:

With props on Accordion component:

```typescript
<Accordion
  id="accordion-stacked-card"
  openIconName="chevronUp"
  closeIconName="chevronDown"
  size="lg"
  variant="divider"
  onToggle={(value) => {
    console.log(value);
  }}
>
  <AccordionDetail>...</AccordionDetail>
  <AccordionDetail>...</AccordionDetail>
</Accordion>
```

With Props on AccordionDetail component:

```typescript
<AccordionDetail
  id="accordion-stacked-card-1"
  openIconName="chevronUp"
  closeIconName="chevronDown"
  size="lg"
  variant="divider"
  onToggle={(value) => {
    console.log(value);
  }}
>
  ...
</AccordionDetail>
```

## Accessibility

The Accordion component is built with accessibility in mind. The `AccordionSummary` component has a role of `button` and the `AccordionContent` component has a role of `region`.

The `AccordionSummary` component has an `aria-expanded` attribute that will be toggled when the accordion is expanded or collapsed. It also has an `aria-controls` attribute that will be set to the `id` of the `AccordionContent` component. This will help screen readers to announce what content is controlled by the button.

The `AccordionContent` component has an `aria-labelledby` attribute that will be set to the `id` of the `AccordionSummary` component. This will help screen readers to announce what section the content belongs to.
