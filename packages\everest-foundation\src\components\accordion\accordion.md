# Accordion

## Summary

Research and document implementations for the Everest Accordion.

- Start Date: 2024-2-13
- Figma link: https://www.figma.com/file/oKgx1g4kLggyhj9DMj0sbc/%F0%9F%A7%AA-Accordion?type=design&node-id=2-351&mode=design&t=hJVHqzKkfQo3zalX-0

## Detailed Design

- The accordion consists of four elements: `Accordion`, `AccordionSummary`, `AccordionContent` and `AccordionDetail`
- `Accordion` is a layout container wrapping children in React context
- `AccordionSummary` is div with the role `button` and accepts arbitrary children.
- `AccordionDetail` is a container to build a collapsible component. It accepts arbitrary children.
- `AccordionContent` will be a wrapper for arbitrary children within `AccordionDetail`. This will handle the padding of the content given the size and will have a role of `region` for accessibility.
- Use of the `open` prop on a particular AccordionDetail toggles expanded/collaspsed state.
- As a controlled component, the user can compare the current detail id to a specific id or an array of ids held in state to determine which details are expanded or collaspsed
- Use context to pass down the variant, size, onToggle, openIconName, iconPosition, and possibly other props to `AccordionContent`, `AccordionSummary` and `AccordionDetail`. These components can also be used outside of `Accordion`.
- The `AccordionDetail` will take `AccordionSummary` and `AccordionContent` as children
- Each `AccordionDetail` will place its own `onToggle` function on it's button `onClick`, calling the context's `onToggle` when appropriate
- A `variant` prop with values `"divider"`, `"card"` The default is `"card"`
- Cursor is a pointer on hover
- `Accordion` and `AccordionDetail` will share a similar interface

## API

### Accordion

1. **id**: `string`
1. **testId?**: `string`
1. **variant?**: `"divider" | "card" | "basic"
   The default is an elevated 'card'.
1. **size?**: `"sm" | "md" | "lg"`  
   Default is `"md"`. Used only for variant `divider`.
1. **dividerLowEmphasis?**: `boolean`  
   Used only for variant `divider`.
1. **elevated?**: `boolean`  
   Used only for variant `card`.
1. **onToggle**: `(value: string) => void`;  
   Callback passed to children and run called by their `onToggle` as appropriate.
1. **openIconName?**: `TIconName`
1. **closedIconName?**: `TIconName`
1. **iconPosition?**: `"start" | "end"`

### AccordionSummary

1. **id**: `string`
1. **testId?**: `string`

### AccordionContent

1. **id**: `string`
1. **testId?**: `string`

### AccordionDetail

1. **id**: `string`
1. **testId?**: `string`
1. **open**: `boolean`
1. **variant?**: `"divider" | "card" | "basic"
   The default is an elevated 'card'.
1. **size?**: `"sm" | "md" | "lg"`  
   Default is `"md"`. Used only for variant `divider`.
1. **dividerLowEmphasis?**: `boolean`  
   Used only for variant `divider`.
1. **elevated?**: `boolean`  
   Used only for variant `card`.
1. **openIconName?**: `TIconName`
1. **closedIconName?**: `TIconName`
1. **iconPosition?**: `"start" | "end"`  
   Default is `"end"`.
1. **onToggle?**: `React.ReactEventHandler<HTMLElement>`

## Basic Usage

The below illustrates the basic usage of `Accordion`.

```
() => {
  const [openDetailId, setOpenDetailId] = useState('');
  <Accordion
    id="accordion"
    variant="card"
    onToggle={(value)=> {setOpenDetailId(value)}}
  >
    <AccordionDetail
      id="detail-1"
      open={"detail-1" === openDetailId}
      openIconName="chevronDown"
      >
      <AccordionSummary>
        <Icon name="sparkle" fill="--evr-interactive-neutral-default"/>
        <h3>Heading 1</h3>
      </AccordionSummary>
      <AccordionContent>
         <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
      </AccordionContent>
    </AccordionDetail>
    <AccordionDetail
      id="detail-2"
      open={"detail-2" === openDetailId}
      openIconName="chevronDown"
      >
      <AccordionSummary>
        <Icon name="rocketship" fill="--evr-interactive-neutral-default"/>
        <h3>Heading 2</h3>
      </AccordionSummary>
      <AccordionContent>
         <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
      </AccordionContent>
    </AccordionDetail>
    <AccordionDetail
      id="detail-2"
      open={"detail-2" === openDetailId}
      openIconName="chevronDown"
      variant="basic"
      >
      <AccordionSummary>
        <Icon name="rocketship" fill="--evr-interactive-neutral-default"/>
        <h3>Heading 2</h3>
      </AccordionSummary>
      <AccordionContent>
         <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
      </AccordionContent>
    </AccordionDetail>
  </Accordion>
}
```

## Basic Variant

- As part of the Employee Experience UI Modernization, the new design of the accordions resembles a barebones accordion with no styling. In contrast to the other variants, the `basic` variant will render an accordion without any styling.

- There are no new props required, or any drop drilling since all components pull `variant` from context already. We simply need to just pass `variant="basic"`.

- Here's how it will be used with ContainerBase

```
<ContainerBase
   id="basic-container-id"
   status="error"
   ariaLabel="Basic container example"
   className="demo-container-class">
      <ContainerBodyBase id="basic-body-id">
             <AccordionDetail
                  id="detail-2"
                  open={"detail-2" === openDetailId}
                  openIconName="chevronDown"
                  variant="basic"
            >
               <AccordionSummary>
                   <Icon name="rocketship" fill="--evr-interactive-neutral-default"/>
                   <h3>Heading 2</h3>
             </AccordionSummary>
            <AccordionContent>
                <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
             </AccordionContent>
           </AccordionDetail>
      </ContainerBodyBase>
</ContainerBase>
```

## Accessibility

- Keyboard navigation will include space or enter key allowing collapsing or expanding of the accordion, along with 'expanded' and 'collaspsed' aria messages on VO

## Design Q&A

Q: Are there nested accordions?  
A: No plans for this now

Q: Are there different size mockups for divider (sm, md, lg)?
A: Yes, inspect Figma

Q:Is there a mockup for a low emphasis border?
A: Yes, inspect Figma

Q: Is there a disabled mockup?
A: There is no disable state.

Q: Should Focus ring be outside (vs inside on Figma)?
A: The focus ring should be outside, confirmed with the designer

Q: Why do we have separate open and close icons?
A: Design specifically asked for flexible/separate open and close icons instead of rotating the open

Q: Why did we move away from using details and summary tags?
A: Details has an internal state which will need to be overridden to make it a fully controlled component. Also animating `details` tag is not straightforward. Without animation it flickers on the screen when it is opened or closed.

In order to animate the details tags we will need to use a solution involving JS and CSS. In which we will have to prevent default behavior of the details tag when a user clicks on it, add and remove css classes depending if it is open or not currently. Requires uses of refs and event listeners, if a ref is not available then we will have user clicking the accordion several time before it opens or closes. This is not a good user experience.

Another thing we will have to constantly check is if the browser is having specific issues like for chrome 119 and safari 16 and implement animations for it or work around it. This will not provide our users with a consistent experience and resulting in an influx of support questions.

example of details being animated but not straightforward: https://linkedlist.ch/animate_details_element_60/ issues in chrome 119 and safari 16.

Combined with the issues above and looking at other design systems(all of them are pretty much using divs), we decided to use a custom solution.

## Other Design Systems

**MUI**

- https://mui.com/material-ui/react-accordion/

**Carbon**

- https://react.carbondesignsystem.com/?path=/docs/components-accordion--overview

**Spectrum**

- https://opensource.adobe.com/spectrum-web-components/components/accordion/
- there is no react flavor

**Ant Design**

- https://ant.design/components/collapse

## Changelog

08/07/2024: EDS-4601 - Fix the Accordion Content getting focus in a collapsed state
02/5/2025: PWEB-17620 - Add options of`startTop` and `endTop` to `iconPosition`
