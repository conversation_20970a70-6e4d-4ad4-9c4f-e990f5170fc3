@use '../../variables.scss';

.evrAccordionDetail {
  .evrAccordionContent {
    display: grid;
    grid-template-rows: 0fr;
    transition: grid-template-rows 0.2s ease-out;
    visibility: hidden;

    &.isOpen {
      visibility: visible;
      grid-template-rows: 1fr;
    }
  }

  .evrAccordionContentSpacing {
    overflow: hidden;
    &.contentSizeDividerLG {
      padding: var(--evr-spacing-sm) var(--evr-spacing-md);
    }

    &.contentSizeDivider {
      padding: var(--evr-spacing-sm) var(--evr-spacing-xs);
    }

    &.contentSizeCard {
      padding: 0 var(--evr-spacing-md) var(--evr-spacing-md);
    }
  }

  .evrAccordionSummary {
    cursor: pointer;
    display: flex;
    align-items: center;

    > * {
      display: flex;
    }

    &.placeIconTop {
      align-items: self-start;
    }

    &.openIconGapSM {
      gap: var(--evr-spacing-3xs);
    }

    &.openIconGapMD {
      gap: var(--evr-spacing-2xs);
    }

    &.openIconGapLG {
      gap: var(--evr-spacing-xs);
    }

    &.endIconPosition {
      justify-content: space-between;
    }

    &.summaryCard {
      padding: var(--evr-spacing-md);
    }

    &.summarySizeLG {
      padding: var(--evr-spacing-md);
    }

    &.summarySize {
      padding: var(--evr-spacing-xs);
    }

    &.defaultBorder {
      border-block-end: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-default);
    }

    &.lowEmpBorder {
      border-block-end: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
    }

    .fullWidth {
      width: 100%;
    }
  }

  &.defaultCard {
    margin-block-end: var(--evr-spacing-sm);
    border-radius: var(--evr-radius-sm);
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
    background: var(--evr-surfaces-primary-default);
  }

  &.elevatedCard {
    margin-block-end: var(--evr-spacing-sm);
    box-shadow: var(--evr-depth-02);
  }
}
