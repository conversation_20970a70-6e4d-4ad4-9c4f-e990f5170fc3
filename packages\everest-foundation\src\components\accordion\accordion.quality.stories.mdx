import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Accordion, AccordionDetail, AccordionSummary, AccordionContent } from '.';
import { Card, Icon, Link, Tab, TabGroup } from '../..';
import { CardHeader } from '../card/card-header';
import { expect, screen, userEvent, waitFor } from '@storybook/test';
import { useEffect, useState } from 'react';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';
import { ContainerBase, ContainerBodyBase } from '../container-base';
import { Divider } from '../divider';

export const testId = 'toolbar-test-id';
export const contentText =
  "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.";

<Meta
  title="Testing/Automation Test Cases/Accordion"
  component={Accordion}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'accordion',
    testId: 'accordion-test-id',
    size: 'md',
    variant: 'card',
    openIconName: 'chevronUp',
    closeIconName: 'chevronDown',
    iconPosition: 'end',
  }}
/>

## Card Accordion

<Canvas>
  <Story name="Card Accordion">
    {(args) => {
      const [openDetailId, setOpenDetailId] = useState('card-accordion-detail-1');
      return (
        <Accordion
          {...args}
          onToggle={(value) => {
            if (value === openDetailId) {
              setOpenDetailId('');
            } else {
              setOpenDetailId(value);
            }
          }}
        >
          <AccordionDetail
            id="card-accordion-detail-1"
            testId="card-accordion-detail-1"
            open={'card-accordion-detail-1' === openDetailId}
          >
            <AccordionSummary testId="card-accordion-details-summary-1">
              <h4 className="evrHeading3">Heading 1</h4>
            </AccordionSummary>
            <AccordionContent testId="card-accordion-details-content-1">
              <p className="evrBodyText">{contentText}</p>
            </AccordionContent>
          </AccordionDetail>
          <AccordionDetail
            id="card-accordion-detail-2"
            testId="card-accordion-detail-2"
            open={'card-accordion-detail-2' === openDetailId}
          >
            <AccordionSummary testId="card-accordion-details-summary-2">
              <h3 className="evrHeading3">Heading 2</h3>
            </AccordionSummary>
            <AccordionContent testId="card-accordion-details-content-2">
              <p className="evrBodyText">{contentText}</p>
            </AccordionContent>
          </AccordionDetail>
        </Accordion>
      );
    }}
  </Story>
</Canvas>

## Basic Accordion

<Canvas>
  <Story name="Basic Accordion">
    {() => {
        const [open, setOpen] = React.useState(true);
        return (
          <AccordionDetail
            id="basic-accordion-detail"
            testId="basic-accordion"
            open={open}
            onToggle={() => {
              setOpen(!open);
            }}
            openIconName="chevronUpSmall"
            closeIconName="chevronDownSmall"
            iconPosition="end"
            variant="basic"
          >
            <AccordionSummary>
                <h3 className="evrHeading3">Heading 1</h3>
            </AccordionSummary>
            <AccordionContent>
              <p className="evrBodyText demo-p-class">
                Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                software like Aldus PageMaker including versions of Lorem Ipsum.
              </p>
            </AccordionContent>
          </AccordionDetail>
        )
    }}

  </Story>
</Canvas>

## Divider Accordion

<Canvas>
  <Story name="Divider Accordion">
    {(args) => {
      const [openDetailId, setOpenDetailId] = useState('divider-detail-1');
      return (
        <Accordion
          {...args}
          variant="divider"
          onToggle={(value) => {
            if (value === openDetailId) {
              setOpenDetailId('');
            } else {
              setOpenDetailId(value);
            }
          }}
        >
          <AccordionDetail id="divider-detail-1" testId="divider-detail-1" open={'divider-detail-1' === openDetailId}>
            <AccordionSummary testId="divider-accordion-details-summary-1">
              <h4 className="evrHeading3">Heading 1</h4>
            </AccordionSummary>
            <AccordionContent testId="divider-accordion-details-content-1">
              <p className="evrBodyText">
                {contentText}
                <Link id="link-id" testId="link-id" href="https://www.dayforce.com" target="_blank">
                  To Dayforce
                </Link>
              </p>
            </AccordionContent>
          </AccordionDetail>
          <AccordionDetail id="divider-detail-2" testId="divider-detail-2" open={'divider-detail-2' === openDetailId}>
            <AccordionSummary testId="divider-accordion-details-summary-2">
              <h3 className="evrHeading3">Heading 2</h3>
            </AccordionSummary>
            <AccordionContent testId="divider-accordion-details-content-2">
              <p className="evrBodyText">{contentText}</p>
            </AccordionContent>
          </AccordionDetail>
        </Accordion>
      );
    }}
  </Story>
</Canvas>

## Elevated Card Accordion

<Canvas>
  <Story name="Elevated Card Accordion" decorators={[ChromaticDecorators.padStory]}>
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="elevated-detail-1"
          open={open}
          {...args}
          elevated={true}
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <h3 className="evrHeading3">Heading 1</h3>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## LowEmphasis Divider Accordion

<Canvas>
  <Story name="LowEmphasis Divider Accordion">
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="low-emp-detail-1"
          open={open}
          {...args}
          variant="divider"
          dividerLowEmphasis={true}
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <h3 className="evrHeading3">Heading 1</h3>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## iconPosition start Prop

<Canvas>
  <Story name="iconPosition start Prop">
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="icon-start-detail-1"
          open={open}
          {...args}
          variant="divider"
          iconPosition="start"
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <h3 className="evrHeading3">Heading 1</h3>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## iconPosition startTop Prop

{/* This is example for PWEB-17620 to add iconPosition `startTop` and `endTop` options */}

<Canvas>
  <Story name="iconPosition startTop Prop">
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="icon-startTop-detail-1"
          open={open}
          {...args}
          variant="divider"
          iconPosition="startTop"
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <div>
              <h3 className="evrHeading3"> Heading 1</h3>
              <p className="evrBodyText">
                Set the prop iconPosition to `startTop` or `endTop` for summaries with multiple lines to address the
                issue that the chevron icon appears in the middle
              </p>
            </div>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## iconPosition endTop Prop

<Canvas>
  <Story name="iconPosition endTop Prop">
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="icon-startEnd-detail-1"
          open={open}
          {...args}
          variant="divider"
          iconPosition="endTop"
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <div>
              <h3 className="evrHeading3"> Heading 1</h3>
              <p className="evrBodyText">
                Set the prop iconPosition to `startTop` or `endTop` for summaries with multiple lines to address the
                issue that the chevron icon appears in the middle
              </p>
            </div>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## Full width content in AccordionSummary

<Canvas>
  <Story name="Full width content in AccordionSummary">
    {() => {
      return (
        <AccordionDetail id="full-width-detail-1">
          <AccordionSummary>
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              <h3 className="evrHeading3">Heading</h3>
              <p className="evrBodyText">Click here</p>
            </div>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## Multiple icons in the AccordionSummary

<Canvas>
  <Story name="Multiple icons in the AccordionSummary">
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="multiple-icons-detail-1"
          open={open}
          {...args}
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <Icon name="sparkle" fill="--evr-interactive-neutral-default" />
            <h3 className="evrHeading3" style={{ paddingLeft: '4px' }}>
              Heading 1
            </h3>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## Large Size Accordion

<Canvas>
  <Story name="Large Size Accordion">
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="large-size-detail-1"
          open={open}
          {...args}
          variant="divider"
          size="lg"
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <h3 className="evrHeading3">Heading 1</h3>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## Small Size Accordion

<Canvas>
  <Story name="Small Size Accordion">
    {(args) => {
      const [open, setOpen] = useState(true);
      return (
        <AccordionDetail
          id="small-size-detail-1"
          open={open}
          {...args}
          variant="divider"
          size="sm"
          onToggle={(value) => {
            setOpen(!open);
          }}
        >
          <AccordionSummary>
            <p className="evrBodyText evrBold">Small Heading</p>
          </AccordionSummary>
          <AccordionContent>
            <p className="evrBodyText">{contentText}</p>
          </AccordionContent>
        </AccordionDetail>
      );
    }}
  </Story>
</Canvas>

## Accordion within a Tab Component

<Canvas>
  <Story name="Accordion within a Tab Component"
  play={
    async () => {
      await userEvent.click(screen.getByText("Tab Label 2"));
    }}
  >
    {(args) => {
      const [open, setOpen] = useState(true);
      const [activeId, setActiveId] = useState('tab-label-id-1');
      const handleChange = (newlySelectedTabId) => {
        newlySelectedTabId && setActiveId(newlySelectedTabId);
      };
      const textMap = {
        viewAllLabel: 'View All',
        viewAllAriaLabel: 'View All Tabs',
        overflowButtonPrevAriaLabel: 'Scroll to previous',
        overflowButtonNextAriaLabel: 'Scroll to next',
      };
      return (
        <TabGroup id="tab-group-containing-accordion" activeId={activeId} onActiveIdChange={handleChange} textMap={textMap}>
          <Tab id="tab-label-id-1" label="Tab Label 1">
            {contentText}
          </Tab>
          <Tab id="tab-label-id-2" label="Tab Label 2">
            <div style={{ padding: '20px' }}>
              <AccordionDetail
                id="small-size-detail-1"
                open={open}
                {...args}
                size="sm"
                onToggle={(value) => {
                  setOpen(!open);
                }}
              >
                <AccordionSummary>
                  <p className="evrBodyText evrBold">Heading</p>
                </AccordionSummary>
                <AccordionContent>
                  {contentText}
                </AccordionContent>
              </AccordionDetail>
            </div>
          </Tab>
        </TabGroup>
      );
    }}

  </Story>
</Canvas>

## Accordion with Card component

<Canvas>
  <Story name="Accordion with Card component">
    {(args) => {
      const [openDetailId, setOpenDetailId] = useState('detail-1');
      return (
        <>
          <Accordion
            id="accordion-with-cards"
            testId="accordion-test-id"
            onToggle={(value) => {
              if (value === openDetailId) {
                setOpenDetailId('');
              } else {
                setOpenDetailId(value);
              }
            }}
          >
            <AccordionDetail id="detail-1" open={'detail-1' === openDetailId}>
              <AccordionSummary>
                <h3 className="evrHeading3">Heading 1</h3>
              </AccordionSummary>
              <AccordionContent id="details-content-1">
                <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                  <div style={{ margin: '15px' }}>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-content-id">
                        <CardHeader title="Mount Everest" id="card-with-header-header-id"></CardHeader>
                      </Card.Content>
                    </Card>
                  </div>
                </div>
              </AccordionContent>
            </AccordionDetail>
            <AccordionDetail id="detail-2" open={'detail-2' === openDetailId}>
              <AccordionSummary>
                <h3 className="evrHeading3">Heading 2</h3>
              </AccordionSummary>
              <AccordionContent>
                <p className="evrBodyText">{contentText}</p>
              </AccordionContent>
            </AccordionDetail>
          </Accordion>
        </>
      );
    }}
  </Story>
</Canvas>

## Playwright User Interactions

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => {
      const [openDetailId, setOpenDetailId] = useState('');
      return (
        <Accordion
          {...args}
          onToggle={(value) => {
            setOpenDetailId(value === openDetailId ? '' : value);
          }}
        >
          <AccordionDetail
            id="accordion-detail-1"
            testId="accordion-detail-1"
            open={'accordion-detail-1' === openDetailId}
          >
            <AccordionSummary testId="accordion-summary-1">
              <h4 className="evrHeading3">Heading 1</h4>
            </AccordionSummary>
            <AccordionContent testId="accordion-content-1">
              <p className="evrBodyText">{contentText}</p>
            </AccordionContent>
          </AccordionDetail>
          <AccordionDetail
            id="accordion-detail-2"
            testId="accordion-detail-2"
            open={'accordion-detail-2' === openDetailId}
          >
            <AccordionSummary testId="accordion-summary-2">
              <h3 className="evrHeading3">Heading 2</h3>
            </AccordionSummary>
            <AccordionContent testId="accordion-content-2">
              <p className="evrBodyText">{contentText}</p>
            </AccordionContent>
          </AccordionDetail>
          <AccordionDetail
            id="accordion-detail-3"
            testId="accordion-detail-3"
            open={'accordion-detail-3' === openDetailId}
          >
            <AccordionSummary testId="accordion-summary-3">
              <h3 className="evrHeading3">Heading 3</h3>
            </AccordionSummary>
            <AccordionContent testId="accordion-content-3">
              <p className="evrBodyText">
                {contentText}
                <Link id="accordion-link-3" testId="accordion-link-3" href="https://www.dayforce.com" target="_blank">
                  To Dayforce
                </Link>
              </p>
            </AccordionContent>
          </AccordionDetail>
          <AccordionDetail
            id="accordion-detail-4"
            testId="accordion-detail-4"
            open={'accordion-detail-4' === openDetailId}
          >
            <AccordionSummary testId="accordion-summary-4">
              <h3 className="evrHeading3">Heading 4</h3>
            </AccordionSummary>
            <AccordionContent testId="accordion-content-4">
              <p className="evrBodyText">{contentText}</p>
            </AccordionContent>
          </AccordionDetail>
        </Accordion>
      );
    }}
  </Story>
</Canvas>

## Accordion - First Load

{/* This example is to test the fix in EDS-4814 */}

<Canvas>
  <Story
    name="Accordion - First Load"
    play={async () => {
      await waitFor(() => expect(screen.getByRole('region', { name: 'Heading 1' })).toHaveTextContent(contentText));
    }}
  >
    {(args) => {
      const [openDetailId, setOpenDetailId] = useState('first-load-accordion-detail-1');
      const [content, setContent] = useState('');
      useEffect(() => {
        const timer = setTimeout(() => {
          setContent(contentText);
        }, 1000);
        return () => clearTimeout(timer);
      }, []);
      return (
        <Accordion
          {...args}
          onToggle={(value) => {
            if (value === openDetailId) {
              setOpenDetailId('');
            } else {
              setOpenDetailId(value);
            }
          }}
        >
          <AccordionDetail
            id="first-load-accordion-detail-1"
            testId="first-load-accordion-detail-1"
            open={'first-load-accordion-detail-1' === openDetailId}
          >
            <AccordionSummary testId="first-load-accordion-details-summary-1">
              <h4 className="evrHeading3">Heading 1</h4>
            </AccordionSummary>
            <AccordionContent testId="first-load-accordion-details-content-1">
              <p className="evrBodyText">{content}</p>
            </AccordionContent>
          </AccordionDetail>
          <AccordionDetail
            id="first-load-accordion-detail-2"
            testId="first-load-accordion-detail-2"
            open={'first-load-accordion-detail-2' === openDetailId}
          >
            <AccordionSummary testId="first-load-accordion-details-summary-2">
              <h3 className="evrHeading3">Heading 2</h3>
            </AccordionSummary>
            <AccordionContent testId="first-load-accordion-details-content-2">
              <p className="evrBodyText">{content}</p>
            </AccordionContent>
          </AccordionDetail>
        </Accordion>
      );
    }}
  </Story>
</Canvas>
