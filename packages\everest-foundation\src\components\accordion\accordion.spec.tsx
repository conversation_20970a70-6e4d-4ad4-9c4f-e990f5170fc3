import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { mockResizeObserver } from '../../test-utils';

import { Accordion, AccordionDetail, AccordionSummary, AccordionContent } from '.';
const onToggle = jest.fn();

const accordionDetailTestId = 'accordion-detail-test-id';
const accordionSummaryTestId = 'accordion-summary-test-id';
const accordionContentTestId = 'accordion-content-test-id';

const stackedAccordionDetail1Id = 'accordion-stacked-card-1';
const stackedAccordionDetail2Id = 'accordion-stacked-card-2';

const stackedAccordionDetail1TestId = 'accordion-stacked-card-1-test-id';
const stackedAccordionDetail2TestId = 'accordion-stacked-card-2-test-id';
const stackedAccordionSummary1TestId = 'accordion-stacked-card-1-summary-test-id';
const stackedAccordionSummary2TestId = 'accordion-stacked-card-2-summary-test-id';
const stackedAccordionContent1TestId = 'accordion-stacked-card-1-content-test-id';
const stackedAccordionContent2TestId = 'accordion-stacked-card-2-content-test-id';

const renderStackedAccordion = (openId = stackedAccordionDetail1Id) => {
  return (
    <Accordion
      id="accordion-stacked-card"
      openIconName="chevronUp"
      closeIconName="chevronDown"
      onToggle={onToggle}
      size={'md'}
      variant={'card'}
      iconPosition={'end'}
    >
      <AccordionDetail
        id={stackedAccordionDetail1Id}
        open={openId === stackedAccordionDetail1Id}
        testId={stackedAccordionDetail1TestId}
      >
        <AccordionSummary testId={stackedAccordionSummary1TestId}>
          <h3 className="evrHeading3">Heading 1</h3>
        </AccordionSummary>
        <AccordionContent testId={stackedAccordionContent1TestId}>
          <p className="evrBodyText">
            Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
            industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
            scrambled it to make a type specimen book.
          </p>
        </AccordionContent>
      </AccordionDetail>

      <AccordionDetail
        id={stackedAccordionDetail2Id}
        open={openId === stackedAccordionDetail2Id}
        testId={stackedAccordionDetail2TestId}
      >
        <AccordionSummary testId={stackedAccordionSummary2TestId}>
          <h3 className="evrHeading3">Heading 2</h3>
        </AccordionSummary>
        <AccordionContent testId={stackedAccordionContent2TestId}>
          <p className="evrBodyText">
            Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
            industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
            scrambled it to make a type specimen book.
          </p>
        </AccordionContent>
      </AccordionDetail>
    </Accordion>
  );
};

const renderAccordionDetail = () => {
  return (
    <AccordionDetail
      id="detail-card"
      testId={accordionDetailTestId}
      open={true}
      openIconName="chevronUp"
      closeIconName="chevronDown"
      iconPosition="end"
      onToggle={onToggle}
      size={'md'}
      variant={'card'}
    >
      <AccordionSummary testId={accordionSummaryTestId}>
        <h3 className="evrHeading3">Heading 1</h3>
      </AccordionSummary>
      <AccordionContent testId={accordionContentTestId}>
        <p className="evrBodyText">
          Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industrys
          standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to
          make a type specimen book.
        </p>
      </AccordionContent>
    </AccordionDetail>
  );
};
describe('[Accordion]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockResizeObserver();
  });

  it('should render the accordion detail', () => {
    render(renderAccordionDetail());

    const accordionDetail = screen.getByTestId(accordionDetailTestId);
    expect(accordionDetail).toBeInTheDocument();
  });

  it('should render the accordion summary and have role button', () => {
    render(renderAccordionDetail());

    const summary = screen.getByTestId(accordionSummaryTestId);
    expect(summary).toBeInTheDocument();
    expect(summary).toHaveAttribute('role', 'button');
  });

  it('should render the accordion content', () => {
    render(renderAccordionDetail());
    const content = screen.getByTestId(accordionContentTestId);
    expect(content).toBeInTheDocument();
    expect(content).toHaveAttribute('role', 'region');
  });

  it('should have an aria-controls attribute on the summary', () => {
    render(renderAccordionDetail());
    const summary = screen.getByTestId(accordionSummaryTestId);
    const content = screen.getByTestId(accordionContentTestId);
    expect(summary).toHaveAttribute('aria-controls', content.id);
  });

  it('should have an aria-expanded attribute on the summary', () => {
    render(renderAccordionDetail());
    const summary = screen.getByTestId(accordionSummaryTestId);
    expect(summary).toHaveAttribute('aria-expanded', 'true');
  });

  it('should have an aria-labelledby attribute on the content', () => {
    render(renderAccordionDetail());
    const content = screen.getByTestId(accordionContentTestId);
    const summary = screen.getByTestId(accordionSummaryTestId);
    expect(content).toHaveAttribute('aria-labelledby', summary.id);
  });

  describe('stacked accordion', () => {
    it('should render the accordion with multiple details', () => {
      render(renderStackedAccordion());

      const accordionDetail1 = screen.getByTestId(stackedAccordionDetail1TestId);
      const accordionDetail2 = screen.getByTestId(stackedAccordionDetail2TestId);
      expect(accordionDetail1).toBeInTheDocument();
      expect(accordionDetail2).toBeInTheDocument();
    });

    it('should render the accordion with multiple summaries and have role button', () => {
      render(renderStackedAccordion());

      const summary1 = screen.getByTestId(stackedAccordionSummary1TestId);
      const summary2 = screen.getByTestId(stackedAccordionSummary2TestId);
      expect(summary1).toBeInTheDocument();
      expect(summary2).toBeInTheDocument();
      expect(summary1).toHaveAttribute('role', 'button');
      expect(summary2).toHaveAttribute('role', 'button');
    });

    it('should render the accordion with multiple contents and have role region', () => {
      render(renderStackedAccordion());

      const content1 = screen.getByTestId(stackedAccordionContent1TestId);
      const content2 = screen.getByTestId(stackedAccordionContent2TestId);
      expect(content1).toBeInTheDocument();
      expect(content2).toBeInTheDocument();
      expect(content1).toHaveAttribute('role', 'region');
      expect(content2).toHaveAttribute('role', 'region');
    });

    it('should open the first detail and close the second detail when clicking on the summary', async () => {
      render(renderStackedAccordion());

      const summary1 = screen.getByTestId(stackedAccordionSummary1TestId);
      const summary2 = screen.getByTestId(stackedAccordionSummary2TestId);
      fireEvent.click(summary1);
      fireEvent.click(summary2);
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(2);
      });
    });

    it('should call onToggle when pressing enter on the summary', async () => {
      render(renderStackedAccordion());

      const summary1 = screen.getByTestId(stackedAccordionSummary1TestId);
      summary1.focus();
      userEvent.keyboard('[Enter]');
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(1);
      });
    });

    it('should call onToggle when pressing space on the summary', async () => {
      render(renderStackedAccordion());

      const summary1 = screen.getByTestId(stackedAccordionSummary1TestId);
      summary1.focus();
      userEvent.keyboard(' ');
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('onToggle', () => {
    it('should call onToggle when clicking on the summary', async () => {
      render(renderAccordionDetail());
      const summary = screen.getByTestId(accordionSummaryTestId);
      fireEvent.click(summary);
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(1);
      });
    });

    it('should not call onToggle when clicking on the content', async () => {
      render(renderAccordionDetail());
      const content = screen.getByTestId(accordionContentTestId);
      fireEvent.click(content);
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(0);
      });
    });
  });

  describe('onKeyDown ', () => {
    it('should call onToggle when pressing enter on the summary', async () => {
      render(renderAccordionDetail());
      const summary = screen.getByTestId(accordionSummaryTestId);
      summary.focus();
      userEvent.keyboard('[Enter]');
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(1);
      });
    });

    it('should call onToggle when pressing space on the summary', async () => {
      render(renderAccordionDetail());
      const summary = screen.getByTestId(accordionSummaryTestId);
      summary.focus();
      userEvent.keyboard(' ');
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(1);
      });
    });

    it('should not call onToggle when pressing other key on the summary', async () => {
      render(renderAccordionDetail());
      const summary = screen.getByTestId(accordionSummaryTestId);
      summary.focus();
      userEvent.keyboard('{ctrl}');
      await waitFor(() => {
        expect(onToggle).toHaveBeenCalledTimes(0);
      });
    });
  });
});
