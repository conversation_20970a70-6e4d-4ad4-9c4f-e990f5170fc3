import { useState } from 'react';
import { <PERSON>a, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Accordion, AccordionDetail, AccordionSummary, AccordionContent } from '.';
import { Icon, ICON_NAMES } from '../icon';
import Examples from './accordion.examples.mdx';

<Meta
  title="Components/Accordion"
  component={Accordion}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/oKgx1g4kLggyhj9DMj0sbc/%F0%9F%A7%AA-Accordion?node-id=8960%3A24144&mode=dev',
    },
  }}
  argTypes={{
    id: {
      control: 'text',
      type: 'text',
      description: 'Mandatory. Sets the `id` attribute on the accordion.',
    },
    testId: {
      control: 'text',
      type: 'text',
      description: 'Optional. Sets the `data-testid` attribute on the accordion.',
    },
    size: {
      control: {
        type: 'select',
        options: ['sm', 'md', 'lg'],
      },
      table: {
        defaultValue: { summary: 'md' },
      },
      description: 'Changes the size of the accordion.',
    },
    variant: {
      control: {
        type: 'select',
        options: ['divider', 'card'],
      },
      table: {
        defaultValue: { summary: 'card' },
      },
      description: 'Changes the variant of the accordion.',
    },
    dividerLowEmphasis: {
      control: {
        type: 'boolean',
      },
      table: {
        defaultValue: { summary: false },
      },
      description: 'Changes the divider to low emphasis. Only works when `variant` is set to `divider`.',
    },
    elevated: {
      control: {
        type: 'boolean',
      },
      table: {
        defaultValue: { summary: false },
      },
      description: 'Adds elevation to the accordion. Only works when `variant` is set to `card`.',
    },
    openIconName: {
      control: 'select',
      options: ICON_NAMES.sort(),
      description: 'The icon used when the accordion is open.',
      table: {
        defaultValue: { summary: 'chevronUp' },
      },
    },
    closeIconName: {
      control: 'select',
      options: ICON_NAMES.sort(),
      description: 'The icon used when the accordion is closed.',
      table: {
        defaultValue: { summary: 'chevronDown' },
      },
    },
    iconPosition: {
      control: {
        type: 'select',
        options: ['start', 'end'],
      },
      table: {
        defaultValue: { summary: 'end' },
      },
      description: 'Changes the position of the icon.',
    },
    onToggle: {
      control: '-',
      description: 'Optional callback fired when user clicks on the accordion.',
    },
  }}
  args={{
    id: 'accordion',
    testId: 'accordion-test-id',
    size: 'md',
    variant: 'divider',
    dividerLowEmphasis: false,
    openIconName: 'chevronUp',
    closeIconName: 'chevronDown',
    iconPosition: 'end',
  }}
/>

# Accordion

<Examples />

## Live Demo

<Canvas>
  <Story name="Accordion">
    {(args) => {
      const [openDetailId, setOpenDetailId] = useState('detail-1');
      return (
        <Accordion
          {...args}
          onToggle={(value) => {
            if (value === openDetailId) {
              setOpenDetailId('');
            } else {
              setOpenDetailId(value);
            }
          }}
        >
          <AccordionDetail id="detail-1" open={'detail-1' === openDetailId}>
            <AccordionSummary>
              <h3 className="evrHeading3">Heading 1</h3>
            </AccordionSummary>
            <AccordionContent>
              <p className="evrBodyText">
                Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                software like Aldus PageMaker including versions of Lorem Ipsum.
              </p>
            </AccordionContent>
          </AccordionDetail>
          <AccordionDetail id="detail-2" open={'detail-2' === openDetailId}>
            <AccordionSummary>
              <h3 className="evrHeading3">Heading 2</h3>
            </AccordionSummary>
            <AccordionContent>
              <p className="evrBodyText">
                Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                software like Aldus PageMaker including versions of Lorem Ipsum.
              </p>
            </AccordionContent>
          </AccordionDetail>
        </Accordion>
      );
    }}

  </Story>
</Canvas>

## Props

### Accordion Props

<ArgsTable story="Accordion" />

### AccordionDetail Props

AccordionDetail shares the same props as Accordion besides the `open` prop

<ArgsTable of={AccordionDetail} />

### AccordionSummary Props

<ArgsTable of={AccordionSummary} />

### AccordionContent Props

<ArgsTable of={AccordionContent} />
