import React, { PropsWithChildren, useMemo } from 'react';

import { IAccordionContext, AccordionContext, defaultContext as defaultAccordionContext } from './accordion-context';
import { resolvePropsContext } from '../../utils/resolve-props-context';

export interface IAccordion extends IAccordionContext {
  id: string;
  testId?: string;
}

export const Accordion = (props: PropsWithChildren<IAccordion>): JSX.Element => {
  const {
    id,
    testId,
    size,
    variant,
    dividerLowEmphasis,
    iconPosition,
    openIconName,
    closeIconName,
    elevated,
    onToggle,
  } = resolvePropsContext<IAccordion, IAccordionContext>(props, defaultAccordionContext);

  const context = useMemo(
    () => ({
      size,
      variant,
      elevated,
      dividerLowEmphasis,
      iconPosition,
      openIconName,
      closeIconName,
      onToggle,
    }),
    [closeIconName, dividerLowEmphasis, elevated, onToggle, openIconName, iconPosition, size, variant]
  );
  return (
    <AccordionContext.Provider value={context}>
      <div id={id} data-testid={testId}>
        {props.children}
      </div>
    </AccordionContext.Provider>
  );
};

Accordion.displayName = 'Accordion';
