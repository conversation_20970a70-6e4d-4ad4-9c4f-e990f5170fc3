import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { ListBox } from '../list-box';
import { <PERSON><PERSON> } from '../button';
import { AnchoredOverlay, FocusRing, IPlacementFallback } from '@ceridianhcm/everest-cdk';

export const scope = { AnchoredOverlay, ListBox, Button, FocusRing };

The anchored overlay is a styleless utility component that positions and controls the visibility of a floating panel on the screen. The anchored overlay creates an "overlay" element positioned relative to a target element (usually the element that triggers the overlay, for example, a button that opens a tooltip).

Get started to create a component that uses anchored overlay by importing the component from `@ceridianhcm/everest-cdk`.

```tsx
import { AnchoredOverlay } from '@ceridianhcm/everest-cdk';
```

## Visibility

The Anchored Overlay is a controlled component. Pass a boolean value to the `visible` property to control its visibility. When `visible` is true, the anchored overlay renders an overlay element with an `id` attribute.

```tsx
<AnchoredOverlay visible={true} id="anchored-overlay-id">
  overlay content
</AnchoredOverlay>
```

### Portal

Under the hood, the anchored overlay is a `Portal` component from `@ceridianhcm/everest-cdk` that creates an element using the [React Portal API](https://reactjs.org/docs/portals.html). This implementation allows the anchored overlay component rendered outside the DOM of its React parent component. If the parent of the anchored overlay has css property `overflow: hidden`, the anchored overlay will not get cut off by the parent element.

By default, the anchored overlay component is appended to a DOM element `everest-portal-container`, which is the last child of the HTML body. You can specify a container for the anchored overlay by providing the container id via the optional `portalContainerId` prop.

If multiple anchored overlay components are open on the screen, all the anchored overlay will be appended to the portal container based on the opening order. The anchored overlay opens last sits on the top of the rest of the overlays.

Once the `visible` prop is toggled to `false`, both of the anchored overlay and the portal container will be removed from DOM.

The following snippet shows the how an anchored overlay component is rendered to DOM. The portal container is a flex container, and each overlay element has the style `z-index` which is can be set by using `zIndexCategory` prop, the default `zIndexCategory` value is `modal`. This setup is to create a [stacking context](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context#the_stacking_context) on each overlay element so that overlays are displayed following the correct stacking order.

The `zIndexCategory` is based on the <LinkTo kind="foundations-tokens--page#z-index">z-index tokens</LinkTo>

```html
<body>
  ...
  <div id="everest-portal-container" style="display: flex;">
    <div id="anchored-overlay-id" style="z-index: var(--evr-z-index-modal);">
      <div style="position: absolute; top: 0; left: 0;">overlay content</div>
    </div>
  </div>
</body>
```

## Positioning

The anchored overlay is rendered outside its React parent component, the anchored overlay is absolute positioned to the HTML body by default. It is required to pass the target element's ref to the anchored overlay so that the relative position of the overlay could be updated properly.

The anchored overlay position will be defined by

`anchorOrigin` defined the anchor point of the trigger element.

- vertical (`top`, `bottom`, _number_)
- horizontal (`left`, `right`, _number_)

When using _number_ to define its vertical or horizontal point, the _number_ is positioned relative to the top left corner of the trigger element.

`transformOrigin` defined the point of the overlay which will attach to the anchor

- vertical (`top`, `bottom`)
- horizontal (`left`, `right`)

One common accessibility concern of overlay is that the content may get cut off on the screen. The `fitToScreen` prop configures the anchored overlay to reposition itself to display within the viewport.

The `fitToScreen` prop will configure the anchored overlay to shift its position so that it is visible within the viewport and will maintain its position with respect to the trigger element.

## Usage

### Basic Anchored Overlay

The `offset` prop takes a number value to create a gap between the target element and the overlay in pixels. Positive `horizontal` values move the overlay to the right, while negative values move it to the left. Positive `vertical` values move the overlay downward, whereas negative values move it upward.

The `width` and the `height` props are used to configure the custom width and height of the anchored overlay.

The `style` prop is used to provide custom styles of the anchored overlay.

export const basicOverlay = `() => {
  const styles = {
    overlay: {
      backgroundColor: 'rgb(135,206,250)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
  const triggerRef = React.useRef(null);
  const [open, setOpen] = React.useState(false);
  const onToggleOverlay = () => setOpen((prev) => !prev);
  return (
    <div>
      <div ref={triggerRef}>
        <Button
          id='click-open-top-btn'
          label="click to open on top"
          onClick={onToggleOverlay}
        />
      </div>
      <AnchoredOverlay
        triggerRef={triggerRef}
        visible={open}
        anchorOrigin={{
          vertical: "top", 
          horizontal: "left"
        }}
        transformOrigin={{
          vertical: "bottom", 
          horizontal: "left"
        }}
        offset={{horizontal:"-0.25rem", vertical:"-0.25rem"}}
        style={styles.overlay}
        width="12rem"
        height="3rem"
        id="basic-overlay"
      >
        overlay
      </AnchoredOverlay>
    </div>
  )
}`;

<CodeExample scope={scope} code={basicOverlay} />

### Anchored Overlay on mouse pointer

Anchored Overlay position is based on mouse pointer  
_Experimental not for production_ (We don't have use case yet)

export const pointerOverlay = `() => {
  const styles = {
    overlay: {
      backgroundColor: 'rgb(135,206,250)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
  const triggerRef = React.useRef(null);
  const [open, setOpen] = React.useState(false);
  const [pointerX, setPointerX] = React.useState(null)
  const [pointerY, setPointerY] = React.useState(null)
  const onToggleOverlay = (e) => {
    setOpen((prev) => !prev)   
    if (triggerRef) {
      var rect = (triggerRef.current).getBoundingClientRect();
      setPointerX(String(e.clientX - rect.left));
      setPointerY(String(e.clientY - rect.top));
    } 
  };
  return (
    <div>
      <div ref={triggerRef}>
        <Button
          id='click-open-mouse-pointer-btn'
          label="click to open on mouse pointer"
          onClick={onToggleOverlay}
        />
      </div>
      <AnchoredOverlay
        triggerRef={triggerRef}
        visible={open}
        anchorOrigin={{
          vertical: String(pointerY), 
          horizontal: String(pointerX)
        }}
        transformOrigin={{
          vertical: "top", 
          horizontal: "left"
        }}
        style={styles.overlay}
        width="12rem"
        height="3rem"
        id="basic-overlay"
      >
        overlay
      </AnchoredOverlay>
    </div>
  )
}`;

<CodeExample scope={scope} code={pointerOverlay} />

### Anchored Overlay with placementFallbacks

Anchored Overlay repositions itself using the positions provided in the `placementFallbacks` if the overlay is not visible within the viewport on the first load. It will iterate through the `placementFallbacks` values to find a position that fits within the view. If none of the positions fit then it will fallback to the initial position.

To see the repositioned position, place the overlay toggle button at the top of the screen and click to open the overlay. The overlay will reposition itself to the right or bottom of the trigger button.

export const placementFallbacksOverlay = `() => {
  const styles = {
    overlay: {
      backgroundColor: 'rgb(135,206,250)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
  const triggerRef = React.useRef(null);
  const [open, setOpen] = React.useState(false);
  const onToggleOverlay = () => setOpen((prev) => !prev);
  const placementFallbacks: IPlacementFallback[] = [
    {
      anchorOrigin: { vertical: 'top', horizontal: 'left' },
      transformOrigin: { vertical: 'bottom', horizontal: 'right' },
    },
    {
      anchorOrigin: { vertical: 'top', horizontal: 'center' },
      transformOrigin: { vertical: 'bottom', horizontal: 'left' },
    },
    {
      anchorOrigin: { vertical: 'top', horizontal: 'right' },
      transformOrigin: { vertical: 'bottom', horizontal: 'left' },
    },
    {
      anchorOrigin: { vertical: 'center', horizontal: 'right' },
      transformOrigin: { vertical: 'top', horizontal: 'left' },
    },
    {
      anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
      transformOrigin: { vertical: 'top', horizontal: 'left' },
    },
    {
      anchorOrigin: { vertical: 'bottom', horizontal: 'center' },
      transformOrigin: { vertical: 'top', horizontal: 'left' },
    },
    {
      anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
      transformOrigin: { vertical: 'top', horizontal: 'right' },
    },
    {
      anchorOrigin: { vertical: 'center', horizontal: 'left' },
      transformOrigin: { vertical: 'top', horizontal: 'right' },
    },
  ];
  return (
    <div>
      <div ref={triggerRef}>
        <Button
          id='click-open-auto-placement-overlay-btn'
          label="click to open placementFallbacks overlay"
          onClick={onToggleOverlay}
        />
      </div>
      <AnchoredOverlay
        triggerRef={triggerRef}
        visible={open}
        anchorOrigin={{
          vertical: "top", 
          horizontal: "center"
        }}
        transformOrigin={{
          vertical: "bottom", 
          horizontal: "left"
        }}
        placementFallbacks={placementFallbacks}
        style={styles.overlay}
        width="18rem"
        height="6rem"
        id="basic-overlay"
      >
        overlay
      </AnchoredOverlay>
    </div>
  )
}`;

<CodeExample scope={scope} code={placementFallbacksOverlay} />

### Anchored Overlay onInitPosition

The `onInitPosition` callback would trigger when the overlay's initial position is set and the overlay is visible.

This prop provides the capability of follow-up actions once the overlay has been defined.  
This should eliminate the necessity of using _useTimeout_ method, which could cause a race condition.

export const onInitPositionOverlay = `() => {
  const styles = {
    overlay: {
      backgroundColor: 'rgb(135,206,250)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
  const triggerRef = React.useRef(null);
  const [open, setOpen] = React.useState(false);
  const onToggleOverlay = () => setOpen((prev) => !prev);
  return (
    <div>
      <div ref={triggerRef}>
        <Button
          id='click-open-overlay-btn'
          label="click to open the overlay"
          onClick={onToggleOverlay}
        />
      </div>
      <AnchoredOverlay
        triggerRef={triggerRef}
        visible={open}
        anchorOrigin={{
          vertical: "bottom", 
          horizontal: "right"
        }}
        transformOrigin={{
          vertical: "top", 
          horizontal: "left"
        }}
        offset={{horizontal:"0.25rem", vertical:"0.25rem"}}
        style={styles.overlay}
        width="12rem"
        height="3rem"
        id="onInitPosition-overlay"
        onInitPosition={()=>alert('onInitPosition triggered')}
      >
        overlay
      </AnchoredOverlay>
    </div>
  )
}`;

<CodeExample scope={scope} code={onInitPositionOverlay} />

### Use Anchored Overlay with Everest Components

The anchored overlay component can be used with other Everest components. The following code example creates a custom component reusing anchored overlay, button and the list box components.

export const overlayIntegration = `() => {
  const styles = {
    overlay: {
      backgroundColor: 'white',
      border: '1px solid grey',
      borderRadius: '4px'
    },
  };
  const triggerRef = React.useRef(null);
  const [open, setOpen] = React.useState(false);
  const onToggleOverlay = () => setOpen((prev) => !prev);
  const label = open ? 'close list' : 'open list';
  return (
    <div>
      <div ref={triggerRef}>
        <Button
          id='toggle-overlay-btn'
          label={label}
          onClick={onToggleOverlay}
        />
      </div>
      <AnchoredOverlay
        visible={open}
        triggerRef={triggerRef}
        anchorOrigin={{
          vertical: "bottom", 
          horizontal: "left"
        }}
        transformOrigin={{
          vertical: "top", 
          horizontal: "left"
        }}
        style={styles.overlay}
        id="list-overlay"
      >
      <ListBox
        ariaLabel="List Box"
        options={[
          {title: 'First Option', id: '1'},
          {title: 'Second Option', id: '2'},
          {title: 'Third Option', id: '3'}
        ]}
      />
      </AnchoredOverlay>
    </div>
  )
}`;

<CodeExample scope={scope} code={overlayIntegration} />

### Use Anchored Overlay with Focus Ring

The anchored overlay doesn't use the focus ring component to handle focus indicator by default. You can opt in to use focus ring by providing the `focusRingOptions`. This object specifies how the focus ring should be applied to the overlay. If not provided, the overlay will not use focus ring. Read more about how to configure <LinkTo kind="Toolbox/FocusRing">Focus Ring</LinkTo>.

export const focusRingOverlay = `() => {
  const styles = {
    overlay: {
      backgroundColor: 'rgb(135,206,250)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
  };
  const triggerRef = React.useRef(null);
  const triggerRingRef = React.useRef(null);
  const overlayFocusingRef = React.useRef(null);
  const [open, setOpen] = React.useState(false);
  const onToggleOverlay = () => setOpen((prev) => !prev);
  return (
    <div>
      <div ref={triggerRef}>
        <FocusRing ref={triggerRingRef} manual>
          <Button id='tap-open-btn' label="Tap to open" onClick={onToggleOverlay} />
        </FocusRing>
      </div>
      <AnchoredOverlay
        visible={open}
        anchorOrigin={{
          vertical: "top", 
          horizontal: "right"
        }}
        transformOrigin={{
          vertical: "top", 
          horizontal: "left"
        }}
        triggerRef={triggerRef}
        style={styles.overlay}
        width="10rem"
        height="3rem"
        offset={{horizontal:"0.25rem", vertical:"-0.25rem"}}
        focusRingOptions={{
          defaultShow: triggerRingRef.current ? !triggerRingRef.current.isMouseTrigger() : false
        }}
        id="focus-overlay"
      >
      overlay content
      </AnchoredOverlay>
    </div>
  )
}`;

<CodeExample scope={scope} code={focusRingOverlay} />

### Closing Behavior

When the trigger element is hidden or removed from the page, the Anchored Overlay is closed despite its `visible` prop value. You can test out this behavior by opening the overlay in the following code demo, then click on the 'remove trigger' button. It would close both of the trigger button and the anchored overlay.

export const closeOverlayExample = `() => {
  const styles = {
    wrapper: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '0.5rem',
    },
    overlay: {
      backgroundColor: 'rgb(135,206,250)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: '10rem',
      height: '3rem',
    },
  };
  const triggerRef = React.useRef(null);
  const [open, setOpen] = React.useState(false);
  const [triggerHidden, setTriggerHidden] = React.useState(false);
  const onToggleOverlay = () => setOpen((prev) => !prev);
  return (
    <div style={styles.wrapper}>
      <div>
          <Button
            id='remove-trigger-btn'
            variant='tertiary'
            label="Remove Trigger"
            onClick={() => setTriggerHidden(true)}
          />
          <Button
            id='reset-trigger-btn'
            variant='tertiary'
            label="Reset Trigger"
            onClick={() => setTriggerHidden(false)}
          />
      </div>
      {
        !triggerHidden && (
        <div>
          <div ref={triggerRef}>
            <Button
              id='open-ontoggle-overlay-btn'
              label="Open Overlay"
              onClick={onToggleOverlay}
            />
          </div>
          <AnchoredOverlay 
            triggerRef={triggerRef}
            visible={open}         
            anchorOrigin={{
              vertical: "bottom", 
              horizontal: "left"
            }}
            transformOrigin={{
              vertical: "top", 
              horizontal: "left"
            }}
            style={styles.overlay} 
            id="close-overlay"
          >
            overlay
          </AnchoredOverlay>
        </div>
      )}
    </div>
  )
}`;

<CodeExample scope={scope} code={closeOverlayExample} />
