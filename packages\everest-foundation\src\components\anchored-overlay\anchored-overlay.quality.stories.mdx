import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { userEvent, screen, within } from '@storybook/test';
import { AnchoredOverlay, getPosition } from '@ceridianhcm/everest-cdk';
import { useState, useRef, cloneElement, Children } from 'react';
import { Button } from '../button';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Anchored Overlay"
  component={AnchoredOverlay}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      delay: 1000,
    },
  }}
  args={{
    id: 'test-overlay',
    width: '80px',
    height: '60px',
    anchorOrigin: {
      vertical: 'top',
      horizontal: 'left',
    },
    offset: { horizontal: '10px', vertical: '10px' },
    style: {
      border: '1px solid grey',
      borderRadius: '2px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'white',
      opacity: 0.5,
    },
  }}
/>

export const TestContainer = ({ children, left }) => {
  const [visible, setVisible] = useState(false);
  const triggerRef = useRef(null);
  const onToggle = () => setVisible(!visible);
  return (
    <div
      style={{
        display: 'flex',
        height: '90vh',
        justifyContent: left ? 'left' : 'center',
        alignItems: 'center',
      }}
      className="evrBodyText2"
    >
      <Button id="test-overlay-btn" label="trigger" onClick={onToggle} ref={triggerRef} />
      {React.Children.map(children, (child) => {
        return (
          <>
            {cloneElement(child, {
              triggerRef,
              visible,
            })}
          </>
        );
      })}
    </div>
  );
};

export const clickTrigger = async (canvasElement) => {
  await new Promise((resolve) => setTimeout(resolve, 500));
  let canvas = within(canvasElement);
  await userEvent.click(canvas.getByRole('button'));
};

## Live Demo

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - left; transformOrigin: vertical - top, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
            transformOrigin={{ vertical: 'top', horizontal: 'left' }}
            offset={{ horizontal: '-10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - left; transformOrigin: vertical - top, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            offset={{ horizontal: '-10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - left; transformOrigin: vertical - bottom, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            offset={{ horizontal: '-10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - left; transformOrigin: vertical - bottom, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            offset={{ horizontal: '-10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - right; transformOrigin: vertical - top, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'left' }}
            offset={{ horizontal: '10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - right; transformOrigin: vertical - top, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            offset={{ horizontal: '10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - right; transformOrigin: vertical - bottom, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            offset={{ horizontal: '10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - top, horizontal - right; transformOrigin: vertical - bottom, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            offset={{ horizontal: '10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - left; transformOrigin: vertical - top, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            transformOrigin={{ vertical: 'top', horizontal: 'left' }}
            offset={{ horizontal: '-10px', vertical: '10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - left; transformOrigin: vertical - top, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            offset={{ horizontal: '-10px', vertical: '10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - left; transformOrigin: vertical - bottom, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            offset={{ horizontal: '-10px', vertical: '10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - left; transformOrigin: vertical - bottom, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            offset={{ horizontal: '-10px', vertical: '10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - right; transformOrigin: vertical - top, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'left' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - right; transformOrigin: vertical - top, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - right; transformOrigin: vertical - bottom, horizontal - left"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'left' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="anchorOrigin: vertical - bottom, horizontal - right; transformOrigin: vertical - bottom, horizontal - right"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer>
          <AnchoredOverlay
            {...args}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="fitToScreen - true"
    play={async ({ canvasElement }) => {
      await clickTrigger(canvasElement);
    }}
  >
    {(args) => {
      return (
        <TestContainer left>
          <AnchoredOverlay
            {...args}
            fitToScreen
            anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            offset={{ horizontal: '10px', vertical: '-10px' }}
          >
            overlay
          </AnchoredOverlay>
        </TestContainer>
      );
    }}
  </Story>
</Canvas>
