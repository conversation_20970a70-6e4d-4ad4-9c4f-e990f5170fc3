import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { AnchoredOverlay } from '@ceridianhcm/everest-cdk';
import { Button } from '../button';
import Examples from './anchored-overlay.examples.mdx';

<Meta
  title="Toolbox/AnchoredOverlay"
  component={AnchoredOverlay}
  parameters={{
    controls: {
      exclude: ['triggerRef'],
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description:
        'Unique id of the overlay. Used to create test-id for the overlay and the id for the responding portal div.',
    },
    portalContainerId: {
      type: 'string',
      control: 'text',
      description: 'Specify where the portal should attach to, default to div everest-portal-container',
      table: {
        defaultValue: { summary: 'everest-portal-container' },
      },
    },
    triggerRef: {
      type: 'object',
      control: 'object',
      description: 'The ref of the target element the overlay is positioning relative to',
    },
    focusRingOptions: {
      type: 'object',
      control: 'object',
      description:
        'Specify how the focus ring should be applied to the overlay. If not provided, the overlay will not use focus ring',
    },
    visible: {
      type: 'boolean',
      control: 'boolean',
      description: 'Specify whether the overlay is open',
      table: {
        defaultValue: { summary: false },
      },
    },
    placementFallbacks: {
      type: { name: 'IPlacementFallback[]' },
      description:
        "When provided, the overlay will attempt to reposition itself according to the provided `placementFallbacks` when the overlay doesn't fit the viewport on the first load.",
    },
    anchorOrigin: {
      type: 'object',
      control: 'object',
      description: "The point on the trigger element's anchor where overlay will attach to",
    },
    transformOrigin: {
      type: 'object',
      control: 'object',
      description: "The point on the overlay which will attach to the trigger element's anchor",
    },
    /* fitToScreen: {
      type: 'boolean',
      control: 'boolean',
      description:
        'Specify whether the overlay placement should reposition itself to fit in viewport',
      table: {
        defaultValue: { summary: false },
      },
    }, */
    offset: {
      type: 'object',
      description:
        'Margin between the trigger element and the overlay. Accepts "rem"and "px" values. Positive `horizontal` values move the overlay to the right, while negative values move it to the left. Positive `vertical` values move the overlay downward, whereas negative values move it upward.',
    },
    width: {
      type: 'string',
      control: 'text',
      description: 'Specify the width of the overlay. Accepts "rem"and "px" values.',
    },
    height: {
      type: 'string',
      control: 'text',
      description: 'Specify the height of the overlay. Accepts "rem"and "px" values.',
    },
    style: {
      type: 'object',
      control: 'object',
      description: 'Custom styles of the overlay',
    },
  }}
  args={{
    id: 'test-overlay',
    visible: false,
    width: '6rem',
    height: '4rem',
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'left',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'left',
    },
    placementFallbacks: [
      {
        anchorOrigin: { vertical: 'top', horizontal: 'left' },
        transformOrigin: { vertical: 'bottom', horizontal: 'right' },
      },
      {
        anchorOrigin: { vertical: 'top', horizontal: 'center' },
        transformOrigin: { vertical: 'bottom', horizontal: 'left' },
      },
      {
        anchorOrigin: { vertical: 'top', horizontal: 'right' },
        transformOrigin: { vertical: 'bottom', horizontal: 'left' },
      },
      {
        anchorOrigin: { vertical: 'center', horizontal: 'right' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      },
      {
        anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      },
      {
        anchorOrigin: { vertical: 'bottom', horizontal: 'center' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      },
      {
        anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
        transformOrigin: { vertical: 'top', horizontal: 'right' },
      },
      {
        anchorOrigin: { vertical: 'center', horizontal: 'left' },
        transformOrigin: { vertical: 'top', horizontal: 'right' },
      },
    ],
    /* fitToScreen: false, */
    offset: { horizontal: '10px', vertical: '10px' },
    style: {
      border: '1px solid grey',
      borderRadius: '2px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'white',
    },
  }}
/>

# Anchored Overlay

<Examples />

## Live Demo

<Canvas>
  <Story name="AnchoredOverlay">
    {(args) => {
      const [{ visible }, updateArgs] = useArgs();
      const onToggle = () => updateArgs({ visible: !visible });
      const styles = {
        demoWrapper: {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '320px',
          height: '200px',
          backgroundColor: '#f5f5f5',
          overflow: 'hidden',
        },
      };
      const triggerRef = React.useRef(null);
      const props = {
        ...args,
        triggerRef: triggerRef,
      };
      return (
        <div style={styles.demoWrapper}>
          <div ref={triggerRef}>
            <Button id="trigger-btn" label="trigger" onClick={onToggle} />
          </div>
          <AnchoredOverlay {...props}>overlay</AnchoredOverlay>
        </div>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="AnchoredOverlay" />
