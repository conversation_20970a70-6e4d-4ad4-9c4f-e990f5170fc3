import React from 'react';

export const fileSearchImage = (
  <svg width="200" height="202" viewBox="0 0 200 202" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Anchored/File Search">
      <path
        id="Subtract"
        d="M80.5185 168.999C79.4548 152.816 71.9811 131.772 45.7109 124.489C43.9471 124 42.1857 125.193 42.0238 127.016C41.016 138.366 43.1894 156.177 57.4726 168.999H80.5185Z"
        fill="#39BFAC"
        stroke="white"
        strokeWidth="2"
      />
      <path
        id="Vector 7178"
        d="M44.2151 125.377C47.9326 139.52 51.549 147.18 64.389 161.28"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        id="Rectangle 3474245"
        d="M49 40C49 36.134 52.134 33 56 33H125.586C127.494 33 129.32 33.779 130.64 35.1567L149.054 54.3711C150.303 55.6743 151 57.4095 151 59.2144V162C151 165.866 147.866 169 144 169H56C52.134 169 49 165.866 49 162V40Z"
        fill="#C7E5FF"
        stroke="#1F1F1F"
        strokeWidth="2"
      />
      <path
        id="Rectangle 3474246"
        d="M56 42C56 40.8954 56.8954 40 58 40H133.143C133.69 40 134.214 40.2243 134.591 40.6207L143.448 49.9207C143.802 50.2926 144 50.7864 144 51.3V160C144 161.105 143.105 162 142 162H58C56.8954 162 56 161.105 56 160V42Z"
        fill="white"
      />
      <path
        id="Subtract_2"
        d="M140.515 54.2884C140.515 57.1172 142.808 59.4105 145.637 59.4105H162.218C163.09 59.4105 163.544 58.3734 162.954 57.7327L140.515 33.3906V54.2884Z"
        fill="#3067DB"
      />
      <path
        id="Rectangle 3474244"
        d="M49 40C49 36.134 52.134 33 56 33H125.586C127.494 33 129.32 33.779 130.64 35.1567L149.054 54.3711C150.303 55.6743 151 57.4095 151 59.2144V162C151 165.866 147.866 169 144 169H56C52.134 169 49 165.866 49 162V40Z"
        stroke="#1F1F1F"
        strokeWidth="2"
      />
      <path
        id="Subtract_3"
        d="M157.048 166.894C157.706 154.703 164.197 139.064 189.837 135.231C191.636 134.962 193.248 136.336 193.139 138.152C192.62 146.764 188.69 160.159 177.79 168.999H157.805C157.299 168.448 157.004 167.71 157.048 166.894Z"
        fill="#39BFAC"
        stroke="white"
        strokeWidth="2"
      />
      <path
        id="Subtract_4"
        d="M144.283 168.999C142.663 153.354 146.195 131.623 169.894 120.552C171.565 119.772 173.51 120.684 173.944 122.478C176.795 134.278 177.167 154.022 161.816 168.999H144.283Z"
        fill="#39BFAC"
        stroke="white"
        strokeWidth="2"
      />
      <path
        id="Vector 7454"
        d="M191.5 136C188.053 147.705 182.5 154.5 172.5 164"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        id="Vector 7456"
        d="M171.588 121.068C169.983 134.839 167.53 142.544 156.906 157.681"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <rect id="Shadow_01" x="70" y="178" width="60" height="16" rx="8" fill="#F5F5F5" />
      <rect id="Shadow_01_2" x="43" y="170" width="114" height="16" rx="8" fill="#F5F5F5" />
      <path id="Vector 7452" d="M13 169L187 169" stroke="#1F1F1F" strokeWidth="2" strokeLinecap="round" />
      <rect
        id="Rectangle 3474254"
        x="94.5963"
        y="112.338"
        width="6"
        height="18"
        transform="rotate(-45 94.5963 112.338)"
        fill="white"
        stroke="#1F1F1F"
        strokeWidth="2"
      />
      <rect
        id="Rectangle 3474255"
        x="99.546"
        y="120.117"
        width="10"
        height="36"
        rx="5"
        transform="rotate(-45 99.546 120.117)"
        fill="#3067DB"
        stroke="#1F1F1F"
        strokeWidth="2"
      />
      <circle
        id="Ellipse 2987"
        cx="74.0902"
        cy="87.5898"
        r="32"
        transform="rotate(-45 74.0902 87.5898)"
        fill="#C7E5FF"
        stroke="#1F1F1F"
        strokeWidth="2"
      />
      <path
        id="Ellipse 2985"
        d="M87.707 86.5632C87.5596 83.439 86.8887 80.3946 85.7438 77.6035C84.5991 74.8128 83.0052 72.3351 81.0658 70.3013C79.1273 68.2684 76.8777 66.7145 74.4477 65.7157C72.0179 64.717 69.4404 64.2866 66.8566 64.4539"
        stroke="white"
        strokeWidth="4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
  </svg>
);
