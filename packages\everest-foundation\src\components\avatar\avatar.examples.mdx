import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Info } from '../../../.storybook/docs/shared/status-banner';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { Avatar } from './avatar';
import { Button } from '../button';
import mountain from '../../../assets/images/mountain.jpg';
import place from '../../../assets/images/place.jpg';

export const scope = { Avatar, Button, mountain, place };

Avatar component is a complex component which consists of multiple elements and supports multiple variations.
Avatar component is always circular.

<Info>
  An <LinkTo dayforceStoryPath="docs/components-avatar--docs">Everest Dayforce Avatar</LinkTo> is available from
  `@ceridianhcm/everest-dayforce`. It automatically loads the appropriate profile picture and initials for the provided
  Dayforce `userId`. The `userId` prop is required for the Dayforce Avatar component.
</Info>

## Size

There are 6 sizes available:

| Size | Dimensions (px) | Dimensions (rem) |
| ---- | --------------- | ---------------- |
| xs   | 24x24           | 1.5x1.5          |
| sm   | 32x32           | 2x2              |
| md   | 40x40           | 2.5x2.5          |
| lg   | 48x48           | 3x3              |
| xl   | 64x64           | 4x4              |
| 2xl  | 80x80           | 5x5              |

export const sizeCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            gap: '20px, 10px'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Column>
            <Row>
                <Avatar id='size-avatar-xs' ariaLabel='This is an Avatar with picture - size xs' size='xs' src={mountain} />
                <Avatar id='size-avatar-sm' ariaLabel='This is an Avatar with picture - size sm' size='sm' src={mountain} />
                <Avatar id='size-avatar-md' ariaLabel='This is an Avatar with picture - size md' size='md' src={mountain} />
                <Avatar id='size-avatar-lg' ariaLabel='This is an Avatar with picture - size lg' size='lg' src={mountain} />
                <Avatar id='size-avatar-xl' ariaLabel='This is an Avatar with picture - size xl' size='xl' src={mountain} />
                <Avatar id='size-avatar-2xl' ariaLabel='This is an Avatar with picture - size 2xl' size='2xl' src={mountain} />
            </Row>
        </Column>
    );

}`;

<CodeExample scope={scope} code={sizeCode} />

## States

### Avatar with Picture

This is a default and preferred state. Avatar component will render a picture when the `src` prop is provided and the image url is valid.

export const imageCode = `
    <Avatar id='picture-avatar' title='This is a title' src={mountain} size="lg" />
`;

<CodeExample scope={scope} code={imageCode} />

### Avatar with Initials

This state is rendered when there is no image url provided, or the image url is not valid.

export const initialsCode = `
    <Avatar id='initials-avatar' initials="ML" title='This is a title - ML' size="lg" />
`;

<CodeExample scope={scope} code={initialsCode} />

### Avatar with Gender Neutral Icon

This state is rendered when picture is not provided and the `initial` prop is also missing.

export const iconCode = `
    <Avatar id='gender-neutral-avatar' ariaLabel='This is an Avatar with icon' size="lg" />
`;

<CodeExample scope={scope} code={iconCode} />

## Usage

### Props "src" and "initials" are set

Avatar with Picture is the preferred state.

export const srcInitialsCode = `
    <Avatar id='with-picture-avatar' ariaLabel='This is an aria-label' src={mountain} initials="ml" size="lg" />
`;

<CodeExample scope={scope} code={srcInitialsCode} />

### Picture failed to load

The Avatar will be rendered with initials if the `initials` prop is set. Otherwise, the Avatar will render with a gender-neutral icon.

export const srcInitialsIconCode = `
    <Avatar id='failed-load-picture-avatar' ariaLabel='This is an aria-label' src='http://www.helloeverestdesignsystem.com/thislinkdoesnotexist' initials="ml" size="lg" />
`;

<CodeExample scope={scope} code={srcInitialsIconCode} />

### Prop "initials" set in lowercase

Initials by default will be rendered in uppercase.

export const initialsLowercaseCode = `
    <Avatar id='initials-lowercase-avatar' ariaLabel='This is an aria-label' initials="ml" size="lg" />
`;

<CodeExample scope={scope} code={initialsLowercaseCode} />

### srcSet is set

Set of images that allow the browser to choose the image to load.

export const srcsetCode = `
    <Avatar id='with-srcset-avatar' ariaLabel='This is an aria-label' size="xl" src={mountain}  srcSet="${mountain} 600w, ${place} 1000w" />
`;

<CodeExample scope={scope} code={srcsetCode} />

### Accessing Avatar using ref

Click on the Button to access the Avatar, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '0.625rem'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
        <Column>
            <Row>
                <Button id='click-access-el-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
            </Row>
            <Row>
                <Avatar id='with-ref-avatar' ref={ref} src={mountain} />
            </Row>
        </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## Accessibility

Avatar is non-clickable and `ariaLabel` is used to communicate the purpose of Avatar when navigated through the screen reader.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
