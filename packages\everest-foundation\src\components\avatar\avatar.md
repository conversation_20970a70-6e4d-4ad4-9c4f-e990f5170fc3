#Summary
Research and documenting on the Everest Avatar Component was made based on the Figma file:
https://www.figma.com/file/QBYnlfek5irAVD5cPXVhDI/%F0%9F%A7%AAAvatar?node-id=2%3A6

#Design
Avatar component is a complex component which consists of multiple elements and supports multiple variations.
Avatar component is always circular.

This component has 4 states overall:

1. **Picture** - this is a default and preferred state. Avatar component will render a picture when the "src" prop is provided and the image url is valid.
2. **Initials** - this variant is rendered when there is no image url provided, or the image url is not valid.
3. **Gender Neutral Icon** - this variant is rendered when image is not provided and the person name is also missing.
4. **Avatar + badge** - the badge is rendered for notifications.

An avatar component can have a badge rendered in the bottom right corner (for notification purposes only). There will be a 2px gap between the circular image / span with the initials and the badge.
Here is a little pen proving that we can get a 2px transparent border between the circle image (or a span with initials) and the badge:
https://codepen.io/artemloshkovrangle/pen/bGoaJNe

#Ways to implement

1. **Using an &lt;img&gt; tag**
   This looks like a preferred way of implementing an avatar component.
   We might not be able to use the Image Container component since we will need to assign custom styles (image mask) to get a border between a circle image and a badge.
2. **Use background-image CSS attribute on a div container to render an image**
   Browsers do not provide any special information on background images to assistive technology. This is important primarily for screen readers, as a screen reader will not announce its presence and therefore convey nothing to its users.
   Also, rendering a backgorund image takes more time because of css processing.

**Team Decision:** proceed with the **&lt;img&gt;** tag implementation.

#Q&A
**Is an avatar option with an image based on the image container component?**  
We will use the &lt;img&gt; element since we need to apply some custom styles to the &lt;img&gt; element directly (css mask). Image Container component doesn't accept custom styles.

**Do we need a gap between avatar and badge?**  
Yes, 2px. badge is rendered in the left bottom corner

**What is the difference between badgeFill-color and badge-background-color?**  
Only use badge-background-color for now since we don't need to render numbers inside a badge

**Is the Avatar component supposed to be a focusable / clickable element? Do we need to add an onhover mouse icon?**  
The Avatar component needs to be clickable and focusable if the onClick function is passed as a prop. Cursor - pointer. If onClick is not provided - Avatar will be a non-clickable and non-focusable element.

**Do we have any hover styles when avatar is clickable/focusable?**  
Change the cursor to pointer;

**How do we render an image if it is not square?**  
Use object-fit: cover;

**Do we need to support srcSet prop?**  
Yes, we should support srcSet property and render different images at different screen resolutions and/or screens with different pixel density.

Usage example:

```html
<img src="small.jpg" srcset="medium.jpg 1000w, large.jpg 2000w" alt="yah" />
```

In this case **1000w** and **2000w** are two images which are 1000 and 2000 pixels in width respectively. The browser will choose a better image based on the window width and pixel-density (e.g. retina displays can be 2x, 3x, etc).

**Do we need to support avatar groups?**  
Not in scope at this time. Avatar groups will be a different component which we will develop in the future.

**Once focused - do we support an onKeyDown event?**  
Yes, we should trigger a callback function passed into the **onClick** prop which on both space and enter key down.

**Support different variants of the badge? primary | danger | warning | etc**  
No need to support different variants of the badge at the moment. No need to implement a badge with the digits inside as well.

**Do we need to add design tokens for sizes?**  
Yes, we can use the design tokens. We need to add a design token for 80px size.

**Do we need to support icon avatars?**  
No need to support icon avatars right now.

**Circular | rounded | square avatars?**  
No need to support them right now.

**Do we go with the &lt;img&gt; or background image approach?**  
We will proceed with the &lt;img&gt; tag, which has some accessibility advatages over the background image implementation and is also less resource consuming way of rendering an image on a screen.

**Do we need to support accessibleLabel prop or set aria-label value to the person's name?**  
We will use the aria-label attribute to render the person's name.

**Do we need to support RTL and LTR?**  
This is not in scope.

**Do we pass the person's name as an alt tag to the image component?**  
We are not using alt. We are using aria-label as an alternative for alt.

**How do we handle initials (accept a prop or calculate based on the person's name)?**  
Initials will be implemented as a prop of the Avatar component. However, it will be hidden from the consumers until the globalization utility is ready and can get the initials properly generated.

#Accessibility

- We will use the **aria-label** attribute on an avatar component and will pass it the value from the ariaLabel prop which will be announced with a screen reader. If the avatar component is not focusable (onClick prop not passed), the screen reader will still announce the aria-label value while navigating through the screen with up and bottom arrow keys.
- When an avatar component is passed an onClick callback function, it will become a focusable element (users will be able to tab into it). Also, once the avatar component is focused, users can press Enter or Space key to trigger the callback function.

#Badge

- Badge will be a separate component, please refer to [badge.md](../badge/badge.md)

#API

1. **size**: `xl | lg | md | sm`  
   Sets the size of the avatar. There are 4 options available:
   - xl (80x80px | 5x5rem)
   - lg (48x48px | 3x3rem)
   - md (32x32px | 2x2rem)
   - sm (24x24px | 1.5x1.5rem)
2. **src**: `string`  
   Sets the profile image url.
3. **srcSet**: `string`
   List of images to use for different screen dimensions. If **srcSet** prop is provided, then **src** prop value will be ignored. We should provide a use case or a link to the srcSet attribute (for &lt;img&gt; element) in storybook.
4. **initials**: `string`
   Person's initials which will be rendered if an image url is not provided. This prop will be hidden until the Ceridian's globalization utility is ready to generate the initials.
5. **badge**: `boolean` (Deprecated)
   Specifies whether a badge needs to be rendered in the bottom right corner of the avatar.
   This is being deprecated as we are using Badge icon as a wrapper component, see [badge.md](../badge/badge.md) for more details.
6. **onClick**: `void`  
   A callback function that is executed on an avatar click. Avatar components becomes focusable and clickable when onClick callback function is provided.
7. **ariaLabel**: `string`  
   This is going to be used as a value for the **aria-label** attribute on the avatar component.
   Recommended data should be the person's name.
8. **testId**: `string`
   Sets **data-test-id** attribute on the html element.
   We might need to add a base interface to the React repo which all of the components will need to inherit since we expect all of them to have the testId prop.
9. **id**: `undefined | string`  
   optional

#Other Design Systems
**Material UI** - https://mui.com/components/avatars/

- Support both image and letter avatars. Also support icon avatars. Letters and Icons are accepted as component children;
- Allow setting **width** and **height** of an avatar by passing the styles object into the **sx** prop;
- Support multiple variants: circular | rounded | square;
- Support avatar groups;
- Support avatar badges (colored and with images);
- Accept **classes** prop. This is something we should consider for all of our components.

**CoreUI** - https://coreui.io/react/docs/4.1/components/avatar/

- Supports both image and letter avatars;
- Letters are accepted as a component child;
- Accepts different letter and background colors;
- Support different avatar shapes and sizes;
- Support different badge colors using the **status** prop. Most common options are: primary | secondary | success | danger | warning | info.

**Shopify** - https://polaris.shopify.com/components/images-and-icons/avatar

- Also supports both image and letter avatars;
- Accepts **accessibilityLabel** as a prop;
- Accepts the **onError** callback which is fired when image is failed to load.

**Vuetify** - https://vuetifyjs.com/en/components/avatars/#usage

- Supports the **size** prop which accepts the numeric value in px. This prop will set both width and height to the specified value.
- Accepts images, icons, and letters as a child element which is rendered inside a component.

**Chakra** - https://chakra-ui.com/docs/media-and-icons/avatar

- Uses the name **prop** to generate the initials - takes the first letter of the first two words;
- Supports icons - accepts an icon component as a prop;
- Also support badges and avatar groups;
- Accepts the **getInitials** function which will generate the initials based on the name prop;
- Support the **srcSet** prop, which is a list of images to use for different screen dimensions.

#PBIs

- Develop the Badge component (React)
- Develop the Avatar component (React + a11y)
- Component testing (unit + integration + visual + manual)
- Documentation preparation
- Bug fixes and handoff

#Acceptance Criteria

1. The name of a component is &lt;Avatar&gt;
2. 4 states available:
   a. Picture
   b. Initials
   c. Gender Neutral Icon,
   lg Icon (Avatar xl),
   md Icon (Avatar lg),
   sm Icon (Avatar md and sm)
   d. Avatar + Badge (for notificateions)
3. 4 sizes available:
   a. xl (80x80px | 5x5rem)
   b. lg (48x48px | 3x3rem)
   c. md (32x32px | 2x2rem)
   d. sm (24x24px | 1.5x1.5rem)
4. In the picture status, component is using the &lt;img&gt; element for rendering a person's picture
5. Component is not focusable/clickable unless the onClick event handler is provided
6. APIs:
   a. size
   b. src
   c. srcSet
   d. initials
   e. badge
   f. onClick
   g. ariaLabel
   h. name
   i. id
7. Badge is a separate component
8. Component is fully accessible
   a. Screen reader announces the content correctly
   b. When an onClick event is provided - Avatar components needs to be focusable and users should be able to trigger the action with the space or enter key press

## Changelogs

7/28/2023 - Marking `badge` prop as deprecated.
12/13/2024: EDS-16962 - Update sizes and storybook documentation
1/28/2025: [[Avatar] Mock and replace urls in tests](https://dayforce.atlassian.net/browse/PWEB-14510)
