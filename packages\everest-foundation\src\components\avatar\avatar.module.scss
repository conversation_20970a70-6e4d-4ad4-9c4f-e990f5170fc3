@use '../../index.scss' as helper;
@use '@ceridianhcm/theme/dist/scss/' as typography;

.evrAvatar {
  position: relative;
  display: block;

  &.size-xs {
    // 24x24
    @include typography.captionBold;
    height: var(--evr-size-md);
    width: var(--evr-size-md);
    min-width: var(--evr-size-md);
  }
  &.size-sm {
    // 32x32
    @include typography.body2Bold;
    height: var(--evr-size-lg);
    width: var(--evr-size-lg);
    min-width: var(--evr-size-lg);
  }
  &.size-md {
    // 40x40
    @include typography.body2Bold;
    height: var(--evr-size-xl);
    width: var(--evr-size-xl);
    min-width: var(--evr-size-xl);
  }
  &.size-lg {
    // 48x48
    @include typography.body2Bold;
    height: var(--evr-size-2xl);
    width: var(--evr-size-2xl);
    min-width: var(--evr-size-2xl);
  }
  &.size-xl {
    // 64x64
    @include typography.body2Bold;
    font-size: var(--evr-font-size-3xl);
    height: var(--evr-size-4xl);
    width: var(--evr-size-4xl);
    min-width: var(--evr-size-4xl);
  }
  &.size-2xl {
    //80x80
    @include typography.body2Bold;
    font-size: var(--evr-font-size-3xl);
    height: var(--evr-size-6xl);
    width: var(--evr-size-6xl);
    min-width: var(--evr-size-6xl);
  }
  &.container {
    height: 100%;
    width: 100%;
    border-radius: var(--evr-radius-circle);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0;
    background-color: var(--evr-surfaces-secondary-default);
    color: var(--evr-content-primary-default);
    outline: none;
    overflow: hidden;
    user-select: none;
    &.border {
      border: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-default);
    }
    & img {
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
      inset-block-start: 0;
      inset-inline-start: 0;
    }
    .hidden {
      @include helper.visuallyHidden();
    }
  }
}
