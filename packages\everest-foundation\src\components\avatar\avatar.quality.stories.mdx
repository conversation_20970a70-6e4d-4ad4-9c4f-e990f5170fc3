import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Avatar } from './avatar';
import dog from '../../../assets/images/dog.jpg';
import mountain from '../../../assets/images/mountain.jpg';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Avatar"
  component={Avatar}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'avatar-id',
    testId: 'test-id',
    size: '',
    initials: 'ab',
    src: '',
    srcSet: '',
    ariaLabel: 'avatarLabel',
    title: 'AB',
  }}
/>

## Live Demo

<Canvas>
  <Story
    name="Picture with srcSet"
    parameters={{ chromatic: { modes: { breakpointLg: defaultModes['breakpointLg'] } } }}
  >
    {(args) => (
      <div style={{ display: 'flex', gap: '20px' }}>
        <Avatar
          {...args}
          id={`${args.id}-picture-xs`}
          size="xs"
          src={mountain}
          srcSet={`${mountain} 600w, ${dog} 1000w`}
        />
        <Avatar
          {...args}
          id={`${args.id}-picture-sm`}
          size="sm"
          src={mountain}
          srcSet={`${mountain} 600w, ${dog} 1000w`}
        />
        <Avatar
          {...args}
          id={`${args.id}-picture-md`}
          size="md"
          src={mountain}
          srcSet={`${mountain} 600w, ${dog} 1000w`}
        />
        <Avatar
          {...args}
          id={`${args.id}-picture-lg`}
          size="lg"
          src={mountain}
          srcSet={`${mountain} 600w, ${dog} 1000w`}
        />
        <Avatar
          {...args}
          id={`${args.id}-picture-xl`}
          size="xl"
          src={mountain}
          srcSet={`${mountain} 600w, ${dog} 1000w`}
        />
        <Avatar
          {...args}
          id={`${args.id}-picture-2xl`}
          size="2xl"
          src={mountain}
          srcSet={`${mountain} 600w, ${dog} 1000w`}
        />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Initials">
    {(args) => (
      <div style={{ display: 'flex', gap: '20px' }}>
        <Avatar {...args} id={`${args.id}-initials-xs`} src="" size="xs" />
        <Avatar {...args} id={`${args.id}-initials-sm`} src="" size="sm" />
        <Avatar {...args} id={`${args.id}-initials-md`} src="" size="md" />
        <Avatar {...args} id={`${args.id}-initials-lg`} src="" size="lg" />
        <Avatar {...args} id={`${args.id}-initials-xl`} src="" size="xl" />
        <Avatar {...args} id={`${args.id}-initials-2xl`} src="" size="2xl" />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Icon">
    {(args) => (
      <div style={{ display: 'flex', gap: '20px' }}>
        <Avatar {...args} id={`${args.id}-icon-xs`} src="" initials="" size="xs" />
        <Avatar {...args} id={`${args.id}-icon-sm`} src="" initials="" size="sm" />
        <Avatar {...args} id={`${args.id}-icon-md`} src="" initials="" size="md" />
        <Avatar {...args} id={`${args.id}-icon-lg`} src="" initials="" size="lg" />
        <Avatar {...args} id={`${args.id}-icon-xl`} src="" initials="" size="xl" />
        <Avatar {...args} id={`${args.id}-icon-2xl`} src="" initials="" size="2xl" />
      </div>
    )}
  </Story>
</Canvas>
<Canvas>
  <Story name="Custom Colors">
    {(args) => (
      <div style={{ display: 'flex', gap: '20px' }}>
        <Avatar
          {...args}
          id={`${args.id}-custom-color-xs`}
          size="xs"
          initials="AB" //swatch04
          color="#144C16"
          backgroundColor="#D7E9D8"
        />
        <Avatar
          {...args}
          id={`${args.id}-custom-color-sm`}
          size="sm"
          initials="CD" //swatch08
          color="#492B1B"
          backgroundColor="#E9E0DA"
        />
        <Avatar
          {...args}
          id={`${args.id}-custom-color-md`}
          size="md"
          initials="XY" //swatch02
          color="#A82D0B"
          backgroundColor="#F9E3D4"
        />
        <Avatar
          {...args}
          id={`${args.id}-custom-color-lg`}
          size="lg"
          initials="IY" //swatch03
          color="#83650B"
          backgroundColor="#F9F3D6"
        />
        <Avatar
          {...args}
          id={`${args.id}-custom-color-xl`}
          size="xl"
          initials="IJ" //swatch04
          color="#144C16"
          backgroundColor="#D7E9D8"
        />
        <Avatar
          {...args}
          id={`${args.id}-custom-color-2xl`}
          size="2xl"
          initials="MO" //swatch05
          color="#0F2C8A"
          backgroundColor="#D6E2F4"
        />
      </div>
    )}
  </Story>
</Canvas>
