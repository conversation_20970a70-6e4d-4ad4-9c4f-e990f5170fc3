import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Avatar } from './avatar';
import Examples from './avatar.examples.mdx';

<Meta
  title="Components/Avatar"
  component={Avatar}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A10008&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  argTypes={{
    size: {
      description: 'Changes the size of the Avatar.',
      type: 'enum',
      control: 'select',
      table: {
        defaultValue: { summary: 'md' },
      },
      options: ['xs', 'sm', 'md', 'lg', 'xl', '2xl'],
    },
    initials: {
      description: 'Sets the initial of the Avatar.',
      type: 'string',
      control: 'text',
    },
    ariaLabel: {
      description: 'Required for accessibility. Communicates the purpose of Avatar',
      control: 'text',
      type: { required: true, summary: 'string' },
    },
    src: {
      description: 'Sets the Picture.',
      type: 'string',
      control: 'text',
    },
    srcSet: {
      description: 'Set of images that allow the browser to choose the image to load.',
      type: 'string',
      control: 'text',
    },
    testId: {
      description: 'An id used for automation testing.',
      type: 'string',
      control: 'text',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'Optional unique identifier.',
    },
    color: {
      description: 'Specific RGB/Hex color used for the letters (initials) when there is no image present.',
      type: 'string',
    },
    backgroundColor: {
      description: 'Specific RGB/Hex background color used for the initials when there is no image present.',
      type: 'string',
    },
  }}
  args={{
    id: 'avatar-id',
    size: 'md',
    initials: 'ml',
  }}
/>

# Avatar

<Examples />

## Live Demo

<Canvas>
  <Story name="Avatar">{(args) => <Avatar {...args}></Avatar>}</Story>
</Canvas>

<ArgsTable story="Avatar" />
