import * as React from 'react';
import { getRuntimeEnvironmentInfo } from '@platform/core';
import classnames from 'classnames';

import { useCreateTestId } from '../../utils';
import { Icon } from '../icon';
import { Image } from '../image';

import styles from './avatar.module.scss';

export type TAvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface IAvatarProps {
  id: string;
  testId?: string;
  size?: TAvatarSize;
  initials?: string;
  src?: string;
  srcSet?: string;
  title?: string;
  ariaLabel?: string;
  color?: string;
  backgroundColor?: string;
  ref?: React.ForwardedRef<HTMLDivElement>;
}

const iconSizeMap = {
  xs: 'sm',
  sm: 'sm',
  md: 'md',
  lg: 'md',
  xl: 'lg',
  '2xl': 'lg',
} as const;

const { os } = getRuntimeEnvironmentInfo();

export const Avatar = React.forwardRef<HTMLDivElement, IAvatarProps>((props: IAvatarProps, ref) => {
  const { id, testId, title, ariaLabel, initials, size = 'md', src, srcSet, color, backgroundColor } = props;

  const dataRef = useCreateTestId(testId);
  const [srcError, setsrcError] = React.useState(false);
  const isNoImageAvatar = (!src && !srcSet) || srcError;
  const isAvatarImage = (src || srcSet) && !srcError;
  //Talkback announces only title if title and ariaLabel are given. Also, title is not required for mobile, so we are not passing it.
  const getTitle = () => {
    if (os === 'ios' || os === 'android') return '';
    return title ?? '';
  };
  const ariaLabelTitle = `${getTitle()} ${ariaLabel || ''}`;

  React.useEffect(() => {
    setsrcError(false);
  }, [src, srcSet]);

  const colorStyles = {
    color: color || 'var(--evr-content-primary-default)',
    backgroundColor: backgroundColor || 'var(--evr-interactive-status-neutral-default)',
  };

  const avatar = () => {
    if (isAvatarImage) {
      return (
        <Image
          id={`${id}-avatar-img`}
          src={src}
          srcSet={srcSet}
          onError={() => setsrcError(true)}
          ariaLabel={ariaLabelTitle}
        />
      );
    }
    if (initials) {
      return (
        <span aria-hidden={true} style={colorStyles}>
          {initials.toUpperCase().substring(0, 2)}
        </span>
      );
    }
    return <Icon name="person" size={iconSizeMap[size] || 'md'} fill="--evr-content-primary-default"></Icon>;
  };

  //aria-label cannot be used if child element has aria-hidden={true}. As Avatar icon and Avatar initials uses aria-hidden,
  //we are passing the aria-label to the visually hiden span so that screen reader announces it.
  return (
    <div className={classnames(styles.evrAvatar, styles[`size-${size}`])} ref={ref}>
      <div
        id={id}
        ref={dataRef}
        title={getTitle()}
        tabIndex={-1} //This helps talkback and voiceover to focus on the Avatar so that ariaLabel is announced.
        className={classnames(styles.evrAvatar, styles.container, {
          [styles.border]: isNoImageAvatar,
        })}
        style={colorStyles}
      >
        {!isAvatarImage ? <span className={styles.hidden}>{ariaLabelTitle}</span> : null}
        {avatar()}
      </div>
    </div>
  );
});

Avatar.displayName = 'Avatar';
