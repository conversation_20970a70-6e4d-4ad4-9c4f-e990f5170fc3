import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Badge } from './index';
import { Icon } from '../icon';

export const scope = { Badge, Icon };

`Badge` is a visual indicator used to display notification to an element.

## Variations

The `Badge` component contains a `status` property, with 3 states: `warning`, `error`, and `success`.

By default, it will set it to `warning` status.

export const defaultCode = `
    () => {
        const styles = {
            row: {
                display: 'flex',
                justifyContent: 'space-around',
                alignItems: 'center',
                flexWrap: 'wrap',
                columnGap: '10px'
            }
        };
        const Row = ({ children }) => (
            <div style={styles.row}>{children}</div>
        );
        return (
                <Row>
                    <Badge>
                        <Icon name='envelope' fill='--evr-interactive-primary-default'/>
                    </Badge>
                    <Badge status='error'>
                        <Icon name='envelope' fill='--evr-interactive-primary-default'/>
                    </Badge>
                    <Badge status='success'>
                        <Icon name='envelope' fill='--evr-interactive-primary-default'/>
                    </Badge>
                </Row>
        );
    }
`;

<CodeExample scope={scope} code={defaultCode} />

## How to Use

`Badge` is a wrapper component; it will add the badge element to the bottom-right corner of the wrapped component.

The `Badge` component is dependent on the wrapped element's width and height, so setting `align-items` may be necessary when used as an item in a Flexbox row to maintain its correct position.

## Accessibility

Additional contextual information given by the badge should be considered, as the status color for screen readers does not provide enough context. Screen reader labels should be provided by the consumer if the component is interactive (for example, a navigation list item containing a badge icon may want to use `aria-describedby` attribute on the list item to provide additional context given by the badge).
