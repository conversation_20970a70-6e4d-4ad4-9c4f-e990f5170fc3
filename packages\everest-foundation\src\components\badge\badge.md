# Badge

## Summary

Research and document implementations for Badge as well as support extensions to other Everest components.

- Last updated: 2023-07-19
- Figma link: https://www.figma.com/file/QBYnlfek5irAVD5cPXVhDI/%F0%9F%A7%AA-Avatar?type=design&node-id=728-24049&mode=design&t=VdncU3H4CRgkPeSA-0 (First implemented with Avatar component)

## Detailed Design

- Badge is a separate consumer-facing component, no longer an internal component
- Consumers can wrap the component they would like to apply the badge against with the Badge component
- Badge size is 8px by 8px for both Icon and Avatar at this time
- Supported badge status colors at this time will be `success` (`--evr-content-status-success-default`), `warning` (`--evr-content-status-warning-default`), and `error` (`--evr-content-status-error-default`)
- No need to implement the badge with digits in it at this time; by default the badge shows a beacon
- Badge is not a clickable element

## API

1.  **id**: `string`
    Optional. Sets a string identifier for the badge.
1.  **testId**: `string`
    Optional. Sets the **data-test-id** attribute on the HTML element.
1.  **status**: `TBadgeStatus`
    Optional. Sets the badge status, one of 'warning', 'success', or 'error'.
1.  **ref**: `undefined | ReactRef`
    Optional. Makes a reference to a DOM node.

**Future Considerations**: `variant` has been removed as we currently only have the beacon styling and no support for numbers or large number variants. Future supported variants can look like this:

```typescript
<Badge status="warning"> // normal, beacon badge
  <Icon/>
</Badge>

<Badge status="warning" label="5"> // badge with a number
  <Icon/>
</Badge>

<Badge status="warning" label="5" large> // large badge with a number
  <Icon/>
</Badge>
```

## Usage

Badge will be a wrapper component that wraps against the component you are adding the badge to.

```typescript
<Badge status="warning">
  <Icon name="envelope" fill="--evr-interactive-primary-default" />
</Badge>
```

In scenarios where there are multiple children inside Badge, only the first child will have the badge applied to it.

Designers guideline says Icon and Avatar components should only have the badge component for its `md` sizes - other sizes should not be used against the badge.

## A11y

Additional contextual information given by the badge should be considered, as the status color for screen readers does not provide enough context. SR-only labels should be provided by the consumer if the component is interactive (for example, a navigation list item containing a badge icon may want to use `aria-describedby` attribute on the list item to provide additional context given by the badge).

## Q&A

**What status colors should the badge component support?**

As of 7/24/2023 and confirmed by design, Badge will support content status colors (success, warning, error)

**Should the badge styling apply to other Icon sizes, or should it only display in `md` size just like Avatar component?**

`md` size only (24px x 24px) for Icons (confirmed by designers).

**In our current usages of Icon components with form focus (Icon Button, and Button with start/end Icon), we are only exposing `aria-label`. Would providing relevant contextual information of the badge in this `aria-label` prop be sufficient to meet a11y criteria?**

Since we are making Badge to be a wrapper component, this is not applicable anymore. For providing additional contextual information, see [A11y](#a11y) section.

**Avatar badge size in design specs is 8px x 8px, while System Icons badge is 10x10px, which size should be used for both cases?**

This has been updated and Design are sticking with 8px x 8px as the badge size for both cases.

Note that for icons, the badge placement is shifted 2px above the bottom edge, while for Avatar, the badge placement is edge to edge on the bottom-right corner.

## Other Design Systems

**Atlassian Design System** - https://atlassian.design/components/badge/

- wraps around the component it applies the badge against (necessary to utilize children prop, as well as relativity and styling against the target element)

**Aurora (GoC)** - https://design.gccollab.ca/component/badges/

- contains sr-only label to read out badge value contents
- accessibility considerations on providing relevant context to the badge for screen readers

**Bootstrap** - https://getbootstrap.com/docs/4.3/components/badge/

- Like Aurora, takes consideration of a11y context for the badge

## Required PBIs

EDS-3327 - Badge support for icons - update component - https://ceridian.atlassian.net/browse/EDS-3327

## Acceptance Criteria

- Expose existing `<Badge>` component to be consumer-facing
- Re-factor component to take in children components, update and/or add automation if applicable
- Add Badge stories
- Styles are on par with Design specs in Figma
- Verify the following:
  - API works as intended
    - testId
    - id
    - children
    - status
    - ref
- Guidances on accessibility and usage of badge
- Unit and integration test implemented and/or updated

## Changelog

08/22/2023 - Added notes on badge placement differences between Icon and Avatar, confirmed with Design
08/17/2023 - Updated Badge size to 8px x 8px, confirmed with Design
