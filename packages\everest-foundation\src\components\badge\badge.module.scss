.evrBadgeContainer {
  display: inline-flex;
  position: relative;
  --edge-smoothing: 0.4px; // necessary to smooth out jagged edges
  --mask-default: radial-gradient(
    circle at calc(100% - var(--evr-spacing-3xs)) calc(100% - var(--evr-spacing-3xs)),
    black,
    black calc(var(--evr-spacing-3xs) - var(--edge-smoothing)),
    transparent var(--evr-spacing-3xs),
    transparent calc(var(--evr-spacing-3xs) + var(--evr-spacing-4xs)),
    black calc(var(--evr-spacing-3xs) + var(--evr-spacing-4xs) + var(--edge-smoothing)),
    black 100%
  ); // "bite-shaped" image mask to allow transparency with background color
  --mask-icon: radial-gradient(
    circle at calc(100% - var(--evr-spacing-3xs)) calc(100% - (var(--evr-spacing-3xs) + var(--evr-spacing-4xs))),
    black,
    black calc(var(--evr-spacing-3xs) - var(--edge-smoothing)),
    transparent var(--evr-spacing-3xs),
    transparent calc(var(--evr-spacing-3xs) + var(--evr-spacing-4xs)),
    black calc(var(--evr-spacing-3xs) + var(--evr-spacing-4xs) + var(--edge-smoothing)),
    black 100%
  );
  mask-image: var(--mask-default);
  -webkit-mask-image: var(--mask-default);

  &.icon {
    mask-image: var(--mask-icon);
    -webkit-mask-image: var(--mask-icon);
  }

  & .badgeBorder {
    $badgeBorder-size: calc(var(--evr-size-xs) + var(--evr-spacing-4xs) + var(--evr-border-width-thin-px));
    clip-path: circle(
      calc(var(--evr-size-xs) + var(--evr-spacing-4xs) * 2) at calc(var(--evr-spacing-4xs) * -1)
        calc(var(--evr-spacing-4xs) * -1)
    );
    position: absolute;
    bottom: calc(var(--evr-spacing-4xs) * -1);
    left: calc(var(--evr-size-lg) - $badgeBorder-size);
    height: $badgeBorder-size;
    width: $badgeBorder-size;
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default);
    border-radius: var(--evr-radius-circle);
  }

  & .evrBadge {
    position: absolute;
    right: 0;
    bottom: 0;
    width: var(--evr-size-xs);
    height: var(--evr-size-xs);
    border-radius: var(--evr-radius-circle);
    background-color: var(--evr-content-status-warning-default);

    &.icon {
      bottom: calc(var(--evr-spacing-4xs));
    }

    &.error {
      background-color: var(--evr-content-status-error-default);
    }

    &.success {
      background-color: var(--evr-content-status-success-default);
    }
  }
}
