import { <PERSON>a, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { Badge } from './badge';
import { Icon } from '../icon';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Badge"
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  component={Badge}
/>

# Badge

## Live Demo

<Canvas>
  <Story name="Status - Warning">
    {() => {
      return (
        <Badge status="warning">
          <Icon name="envelope" fill="--evr-interactive-primary-default" />
        </Badge>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Status - Success">
    {() => {
      return (
        <Badge status="success">
          <Icon name="envelope" fill="--evr-interactive-primary-default" />
        </Badge>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Status - Error">
    {() => {
      return (
        <Badge status="error">
          <Icon name="envelope" fill="--evr-interactive-primary-default" />
        </Badge>
      );
    }}
  </Story>
</Canvas>
