import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import { Badge } from './badge';
import { Icon } from '../icon';

describe('[Badge]', () => {
  const testId = 'test-id';
  const id = 'badge-id';
  const getBadge = () => screen.getByTestId(testId);

  it('should have an id when given', () => {
    render(
      <Badge id={id} testId={testId}>
        <Icon name="envelope" fill="--evr-interactive-primary-default" />
      </Badge>
    );
    expect(getBadge()).toHaveAttribute('id', id);
  });

  it(`should not have accessibility violations`, async () => {
    const { container } = render(
      <Badge testId={testId}>
        <Icon name="envelope" fill="--evr-interactive-primary-default" />
      </Badge>
    );
    await waitFor(() => {
      expect(getBadge()).toBeInTheDocument();
    });
    expect(await axe(container)).toHaveNoViolations();
  });
});
