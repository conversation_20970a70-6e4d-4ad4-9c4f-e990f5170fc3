import { Meta, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { Badge } from './badge';
import { Icon } from '../icon';
import Examples from './badge.examples.mdx';

<Meta
  title="Components/Badge"
  component={Badge}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/QBYnlfek5irAVD5cPXVhDI/%F0%9F%A7%AA-Avatar?type=design&node-id=728-24049&mode=design&t=VdncU3H4CRgkPeSA-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Optional. Set the `id` attribute on the `div` element of `Badge`.',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Optional. Sets the `data-testid` attribute on the `div` element of `Badge`.',
    },
    status: {
      type: 'enum',
      control: 'select',
      description: 'Optional. Sets the notification status color of the badge. Default is `warning`',
      options: ['warning', 'success', 'error'],
      table: {
        defaultValue: { summary: 'warning' },
      },
    },
  }}
  args={{
    id: 'badge',
    testId: 'badge-test',
    status: 'warning',
  }}
/>

# Badge

<Examples />

## Live Demo

<Canvas>
  <Story name="Badge">
    {(args) => {
      return (
        <Badge id={args.id} testId={args.testId} status={args.status}>
          <Icon name="envelope" fill="--evr-interactive-primary-default" />
        </Badge>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Badge" />
