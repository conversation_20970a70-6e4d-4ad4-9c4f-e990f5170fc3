import * as React from 'react';
import classnames from 'classnames';

import { mergeRefs } from '../../utils';
import { useCreateTestId } from '../../utils/use-create-testid';
import { Avatar } from '../avatar';
import { Icon } from '../icon';

import styles from './badge.module.scss';

export type TBadgeStatus = 'warning' | 'success' | 'error';

export interface IBadgeProps {
  id?: string;
  testId?: string;
  status?: TBadgeStatus;
  ref?: React.ForwardedRef<HTMLDivElement>;
}

export const Badge = React.forwardRef<HTMLDivElement, React.PropsWithChildren<IBadgeProps>>((props, ref) => {
  const { id, testId, status = 'warning', children } = props;
  const dataRef = useCreateTestId(testId);
  return (
    <>
      {React.Children.map(children, (child: React.ReactNode, index) => {
        if (index > 0) return child;
        let isAvatarChild = false,
          isIconChild = false;
        if (React.isValidElement(child)) {
          isAvatarChild =
            child.type === Avatar && (!child.props.src || child.props.srcSet) && child.props.size === 'md';
          isIconChild = child.type === Icon;
        }
        return (
          <div className={classnames(styles.evrBadgeContainer, { [styles.icon]: isIconChild })}>
            {child}
            {isAvatarChild ? <div className={styles.badgeBorder} /> : null}
            <div
              id={id}
              className={classnames(styles.evrBadge, {
                [styles.error]: status === 'error',
                [styles.success]: status === 'success',
                [styles.icon]: isIconChild,
              })}
              ref={mergeRefs([ref, dataRef])}
            />
          </div>
        );
      })}
    </>
  );
});

Badge.displayName = 'Badge';
