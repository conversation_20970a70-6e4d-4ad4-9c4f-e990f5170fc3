@use '../../variables.scss';

a.evrBreadcrumbItem {
  display: flex;
  align-items: center;
  cursor: pointer;
  -webkit-outline: none;
  -moz-outline: none;
  outline: none;
  text-decoration: none;
  max-width: variables.$breadcrumbItemMaxWidth;
  width: 100%;
  color: var(--evr-content-primary-lowemp);
  &:active,
  &.focus,
  &:hover:not(.currentPage) {
    padding-block-end: 0;
    color: var(--evr-content-primary-default);
    text-decoration: underline;
    text-underline-position: under;
  }
  &:focus {
    text-decoration: none;
    border-radius: var(--evr-radius-3xs);
  }
  &.mobile {
    min-height: calc(var(--evr-size-md) - variables.$breadcrumbBorderWidth);
    height: auto;
    width: auto;
  }
  &.mobileFocus {
    color: var(--evr-content-primary-default);
    align-self: flex-start;
  }
  &.currentPage {
    cursor: default;
    color: var(--evr-content-primary-highemp);
    max-width: none;
  }
  &.trimWidth {
    width: calc(100% - variables.$breadcrumbIconWidth); // prevents overflow off the page
  }
  & .arrowLeft {
    display: flex;
    align-self: flex-start;
  }
  & .text {
    padding: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    &.wrap {
      min-width: auto;
      overflow: visible;
      white-space: break-spaces;
    }
  }
}
