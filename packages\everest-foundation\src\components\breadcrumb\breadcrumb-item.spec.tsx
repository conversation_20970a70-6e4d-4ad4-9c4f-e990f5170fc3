import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { TBreadcrumbValue } from './breadcrumb';
import { BreadcrumbItem, IBreadcrumbItem } from './breadcrumb-item';

describe('[BreadcrumbItem]', () => {
  const currentPage = false;
  const selectedId = '';
  const onSelected = jest.fn();
  const onNavigate = jest.fn();
  const isMobile = false;
  const testId = 'breadcrumb-item-test-id';
  const value: TBreadcrumbValue = {
    id: 'breadcrumb-item',
    name: 'Breadcrumb Item',
    route: 'test/route/value',
    backAriaLabel: 'back aria label',
    testId: testId,
  };

  const emptyBreadCrumbValue: TBreadcrumbValue = {
    id: '',
    name: '',
    route: '',
  };

  const propsMock = {
    currentPage,
    selectedId,
    onSelected,
    onNavigate,
    isMobile,
    value,
  };

  const getBreadcrumbItem = () => screen.getByTestId(testId);

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render desktop without issue', () => {
    render(<BreadcrumbItem {...propsMock} />);
    expect(getBreadcrumbItem()).toBeInTheDocument();
  });
  it('should render mobile without issue', () => {
    render(<BreadcrumbItem {...propsMock} isMobile />);
    expect(getBreadcrumbItem()).toBeInTheDocument();
  });
  it('should render on desktop without issue when onNavigate is undefined', () => {
    const props = {
      ...propsMock,
      onNavigate: undefined,
      isMobile: false,
    };
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toBeInTheDocument();
  });
  it('should render on mobile without issue when onNavigate is undefined', () => {
    const props = {
      ...propsMock,
      onNavigate: undefined,
      isMobile: true,
    };
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toBeInTheDocument();
  });
  it('should display the breadcrumb name', () => {
    render(<BreadcrumbItem {...propsMock} />);
    screen.getByText(value.name);
  });
  it('should have href equal to route', () => {
    render(<BreadcrumbItem {...propsMock} />);
    expect(getBreadcrumbItem()).toHaveAttribute('href', value.route);
    document.body.innerHTML = '';
    const props = {
      ...propsMock,
      value,
    };
    //refer to https://reactrouter.com/en/main/route/route for details on React Route.
    value.route = 'teams/:teamId';
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toHaveAttribute('href', value.route);
    document.body.innerHTML = '';
    value.route = 'teams\\:teamId';
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toHaveAttribute('href', value.route);
    document.body.innerHTML = '';
    value.route = ':teamId';
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toHaveAttribute('href', value.route);
  });
  it('should have empty href when value is invalid', () => {
    const props = {
      ...propsMock,
      value,
    };
    value.route = `javascript:alert('Hacked!')`;
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toHaveAttribute('href', '');
    document.body.innerHTML = '';
    value.route = `htts://www.dayforce.com/`;
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toHaveAttribute('href', '');
  });
  it('should have id equal to breadcrumb value id', () => {
    render(<BreadcrumbItem {...propsMock} />);
    expect(getBreadcrumbItem()).toHaveAttribute('id', value.id);
  });
  it('should have an aria label when mobile', () => {
    render(<BreadcrumbItem {...propsMock} isMobile />);
    expect(getBreadcrumbItem()).toHaveAttribute('aria-label', value.backAriaLabel);
  });
  it("should set aria-current as 'page' when in desktop with currentPage equal to true", () => {
    const props = {
      ...propsMock,
      currentPage: true,
    };
    render(<BreadcrumbItem {...props} />);
    expect(getBreadcrumbItem()).toHaveAttribute('aria-current', 'page');
  });
  it('should set aria-current as false when in desktop with currentPage equal to false', () => {
    render(<BreadcrumbItem {...propsMock} />);
    expect(getBreadcrumbItem()).toHaveAttribute('aria-current', 'false');
  });

  function clickActionTest(
    cb: jest.Mock<(value: TBreadcrumbValue, isBack: boolean) => void>,
    props: IBreadcrumbItem,
    cbTimesCalled = 1
  ) {
    render(<BreadcrumbItem {...props} />);
    getBreadcrumbItem().click();
    expect(cb).toHaveBeenCalledTimes(cbTimesCalled);
    if (cbTimesCalled > 0) {
      expect(cb).toHaveBeenCalledWith(props.value, props.isMobile);
    }
  }

  it('should call onNavigate when clicked in desktop', () => {
    clickActionTest(onNavigate, propsMock);
  });
  it('should call onNavigate when clicked in mobile', () => {
    const props = {
      ...propsMock,
      isMobile: true,
    };
    clickActionTest(onNavigate, props);
  });
  it('should not call onNavgiate when clicked in desktop and it is the current page', () => {
    const props = {
      ...propsMock,
      currentPage: true,
    };
    clickActionTest(onNavigate, props, 0);
  });
  it('should call onSelected when mouse enters', () => {
    render(<BreadcrumbItem {...propsMock} />);
    fireEvent.mouseEnter(getBreadcrumbItem());
    expect(onSelected).toHaveBeenCalledTimes(1);
    expect(onSelected).toHaveBeenCalledWith(propsMock.value);
  });
  it('should call onSelected when mouse leaves', () => {
    render(<BreadcrumbItem {...propsMock} />);
    fireEvent.mouseLeave(getBreadcrumbItem());
    expect(onSelected).toHaveBeenCalledTimes(1);
    expect(onSelected).toHaveBeenCalledWith(emptyBreadCrumbValue);
  });
  it('should call onSelected when blurred', () => {
    render(<BreadcrumbItem {...propsMock} />);
    fireEvent.blur(getBreadcrumbItem());
    expect(onSelected).toHaveBeenCalledTimes(1);
    expect(onSelected).toHaveBeenCalledWith(emptyBreadCrumbValue);
  });

  async function keyboardActionTest(
    key: string,
    cb: jest.Mock<(value: TBreadcrumbValue, isBack: boolean) => void>,
    props: IBreadcrumbItem
  ) {
    render(<BreadcrumbItem {...props} />);
    getBreadcrumbItem().focus();
    await userEvent.keyboard(key);
    expect(cb).toHaveBeenCalledTimes(1);
  }

  it('should call onNavigate when enter key is pressed in desktop', async () => {
    await keyboardActionTest('{enter}', onNavigate, propsMock);
  });

  it('should call onNavigate when enter key is pressed in mobile', async () => {
    await keyboardActionTest('{enter}', onNavigate, {
      ...propsMock,
      isMobile: true,
    });
  });
  it('should call onSelected when focused', () => {
    render(<BreadcrumbItem {...propsMock} />);
    fireEvent.focus(getBreadcrumbItem());
    expect(onSelected).toHaveBeenCalledTimes(1);
    expect(onSelected).toHaveBeenCalledWith(propsMock.value);
  });
});
