import React, { useLayoutEffect, useRef, useState } from 'react';
import { convertToPx, FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { TBreadcrumbValue } from './breadcrumb';
import { applyRemFactor, mergeRefs, useCreateTestId } from '../../utils';
import { isValidUrl } from '../../utils/is-valid-url';
import { Icon } from '../icon';

import styles from './breadcrumb-item.module.scss';

export interface IBreadcrumbItem {
  value: TBreadcrumbValue;
  currentPage?: boolean;
  selectedId: string;
  onSelected: (value: TBreadcrumbValue) => void;
  onNavigate?: (value: TBreadcrumbValue, isBack: boolean) => void;
  isMobile: boolean;
  testId?: string;
  ref?: React.ForwardedRef<HTMLAnchorElement>;
}
/**
 * BreadcrumbItem is internal and is used only by <PERSON><PERSON><PERSON>rumb
 * @internal
 */
export const BreadcrumbItem = React.forwardRef<HTMLAnchorElement, IBreadcrumbItem>((props: IBreadcrumbItem, ref) => {
  const { value, onNavigate, selectedId, onSelected, currentPage = false, isMobile } = props;
  const { id, name, backAriaLabel, testId, route } = value;
  const testIdRef = useCreateTestId(testId);
  const textContainerRef = useRef<HTMLDivElement>(null);
  const [isUnderlineOffset, setIsUnderlineOffset] = useState(false);
  const [isTextWidthReduced, setIsTextWidthReduced] = useState(false);
  const sizeSmWrapingHeight = useRef(convertToPx(`${applyRemFactor(2.25)}rem`)); // 2.25rem is the height of two lines of text when wrapping
  const emptyBreadCrumbValue: TBreadcrumbValue = {
    id: '',
    name: '',
    route: '',
  };

  function handleNavigation(e: React.KeyboardEvent | React.MouseEvent) {
    if (onNavigate) {
      e.preventDefault();
      onNavigate(value, isMobile);
      return;
    }
    // if onNavigate is not defined, default
    // action occurs
  }
  function handleOnKeyDown(e: React.KeyboardEvent) {
    // Future state: KeyDown Enter or Space key on the Current Page breadcrumb-item will refresh the page.
    if (currentPage && (e.key === 'Enter' || e.key === 'Space')) {
      e.preventDefault();
      return;
    }
    if (e.key === 'Enter') {
      handleNavigation(e);
    }
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleOnClick(e: React.MouseEvent) {
    if (currentPage) {
      e.preventDefault();
      return;
    }
    handleNavigation(e);
  }
  function handleUnderline(selected: TBreadcrumbValue) {
    if (currentPage) return;
    onSelected(selected);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleOnMouseEnter(e: React.MouseEvent) {
    handleUnderline(value);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleOnMouseLeave(e: React.MouseEvent) {
    handleUnderline(emptyBreadCrumbValue);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleOnFocus(e: React.FocusEvent) {
    handleUnderline(value);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleOnBlur(e: React.FocusEvent) {
    handleUnderline(emptyBreadCrumbValue);
  }

  useLayoutEffect(() => {
    // we want the underline offset in sm (when isMobile is true) to be consistent with lg presentation
    // so add a constant padding when text wrapping occurs
    if (isMobile && textContainerRef?.current && textContainerRef.current.clientHeight >= sizeSmWrapingHeight.current) {
      setIsUnderlineOffset(true);
    } else {
      setIsUnderlineOffset(false);
    }
  }, [isMobile, textContainerRef, textContainerRef?.current?.clientHeight]);

  useLayoutEffect(() => {
    // need to ensure there is no horizatonal overflow (can create a scrollbar when wrapping text)
    // this can happen when size is lg (isMobile is false) on a small viewport
    if (textContainerRef?.current && textContainerRef.current.scrollWidth > textContainerRef.current.offsetWidth) {
      setIsTextWidthReduced(true);
    } else {
      setIsTextWidthReduced(false);
    }
  }, [textContainerRef, textContainerRef?.current?.scrollWidth]);

  return (
    <FocusRing>
      <a
        href={isValidUrl(route) ? route : ''}
        id={id}
        aria-label={isMobile ? backAriaLabel : undefined}
        aria-current={currentPage ? 'page' : 'false'}
        onClick={handleOnClick}
        onMouseEnter={handleOnMouseEnter}
        onMouseLeave={handleOnMouseLeave}
        onFocus={handleOnFocus}
        onBlur={handleOnBlur}
        onKeyDown={handleOnKeyDown}
        className={classnames('evrCaptionText', styles.evrBreadcrumbItem, {
          [styles.focus]: id === selectedId,
          [styles.currentPage]: !isMobile && currentPage,
          evrBold: !isMobile && currentPage,
          [styles.mobile]: isMobile,
          [styles.mobileFocus]: isMobile && id === selectedId,
          [styles.trimWidth]: !!testIdRef.current?.parentElement?.previousElementSibling && isTextWidthReduced,
        })}
        ref={mergeRefs([ref, testIdRef])}
      >
        {isMobile && (
          <span className={styles.arrowLeft}>
            <Icon name="arrowLeft" fill="--evr-content-primary-lowemp" aria-hidden="true" />
          </span>
        )}
        <span
          className={classnames([
            styles.text,
            {
              [styles.wrap]: currentPage || isMobile,
              [styles.underlineOffset]: isUnderlineOffset,
            },
          ])}
          ref={textContainerRef}
        >
          {name}
        </span>
      </a>
    </FocusRing>
  );
});

BreadcrumbItem.displayName = 'BreadcrumbItem';
