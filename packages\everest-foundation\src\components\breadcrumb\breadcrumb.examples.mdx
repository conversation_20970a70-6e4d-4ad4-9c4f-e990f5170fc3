import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { Breadcrumb } from './index';
import { BrowserRouter, Routes, Route, Link, useNavigate } from 'react-router-dom';
import { announce, clearAnnouncer } from '../../utils';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { Button } from '../button';
import { Link as EvrLink } from '../link';

export const scope = { Breadcrumb, Button };

The `Breadcrumb` component is a collection of links that allows users to navigate between hierarchical pages.

## Variations

### Default Breadcrumb

`Breadcrumb` has two presentations, `lg` and `sm`, specified by the `size` prop. The default size is `lg`.

When the `size` is `lg`, `Breadcrumb` is an ordered list of links. The bold text is the current page.

For `size` equal to `sm` it displays one link to go back to the previous page.

Below is an example of `Breadcrumb` with default `size` equal to `lg`.

export const sizeLgCode = `
  () => {      
      const values = [
        {
      id: 'route-page-1',
      name: 'Home',
      route: 'https://www.dayforce.com/',
      backAriaLabel: 'This is a breadcrumb to go back to Home',
    },
    {
      id: 'route-page-2',
      name: 'Get Started',
      route: 'https://www.dayforce.com/get-started',
      backAriaLabel:
        'This is a breadcrumb to go back to Get Started with Dayforce',
    },
    {
      id: 'route-page-3',
      name: 'Privacy',
      route:
        'https://www.dayforce.com/privacy',
      backAriaLabel: 'This is a breadcrumb to go back to Privacy',
    },
    {
      id: 'route-page-4',
      name: 'Service',
      route:
        'https://www.dayforcehcm.com/mydayforce/Legal?src=login&locale=en&target=PrivacyPolicy',
      backAriaLabel: 'This is a breadcrumb to go back to Dayforce\\'s Service Provider Privacy Statement',
    }
      ];
      return (
        <Breadcrumb
          id="example-default-breadcrumb"
          values={values}
          ariaLabel="Default breadcrumb"
        />
      );
    }
`;

<CodeExample scope={scope} code={sizeLgCode} />

Here is an example of `Breadcrumb` with `size` equal to `sm`.

export const sizeSmCode = `
  () => {      
      const values = [
        {
          id: 'route-page-1',
          name: 'Home',
          route: 'https://www.dayforce.com/',
          backAriaLabel: 'Back to Home',
        },
        {
          id: 'route-page-2',
          name: 'Get Started',
          route: 'https://www.dayforce.com/get-started',
          backAriaLabel:
            'Back to Get Started',
        },
        {
          id: 'route-page-3',
          name: 'Privacy',
          route:
            'https://www.dayforce.com/privacy',
          backAriaLabel: 'Back to Privacy',
        },
      ];
      return (
        <Breadcrumb
          id="example-default-sm-breadcrumb"
          values={values}
          ariaLabel="Default small breadcrumb"
          size="sm"
        />
      );
    }
`;

<CodeExample scope={scope} code={sizeSmCode} />

The `Breadcrumb` component wraps when it is too long to fit within its container. Try resizing your viewport to see the behavior.

### Breadcrumb Navigation

A navigation callback can be defined by the `onNavigate` prop, allowing for integration of third party routing libraries or custom behavior. When undefined, navigation follows default `<a>` tag browser behavior.

export const onNavigateCode = `
  () => {      
      const values = [
        {
          id: 'route-page-1',
          name: 'Home',
          route: 'https://www.dayforce.com/',
          backAriaLabel: 'Back to Home',
        },
        {
          id: 'route-page-2',
          name: 'Get Started',
          route: 'https://www.dayforce.com/get-started',
          backAriaLabel:
            'Back to Get Started',
        },
        {
          id: 'route-page-3',
          name: 'Privacy',
          route:
            'https://www.dayforce.com/privacy',
          backAriaLabel: 'Back to Privacy',
        },
      ];
      function onNavigate(item, isBack) {
        if (isBack) {
          alert("You are going back to: " + item.name);
        }
        else {
          alert("You are navigating to: " + item.name);
        }        
        window.open(item.route, '_self');        
      }
      return (
        <Breadcrumb
          id="example-default-breadcrumb"
          onNavigate={onNavigate}
          values={values}
          ariaLabel="Default breadcrumb"
        />
      );
    }
`;

<CodeExample scope={scope} code={onNavigateCode} />

#### Navigation with React Router and LiveAnnouncer

Third party routing libraries like <EvrLink href="https://reactrouter.com/en/6.13.0/start/overview" referrerPolicy="no-referrer" rel="noopener">React Router</EvrLink> can be integrated using the `onNavigate` prop.

When navigating to a new page, the action should be announced by assistive technologies. Tools such as <LinkTo kind="Toolbox/LiveAnnouncer">LiveAnnouncer</LinkTo> might prove helpful.

export const reactRouterExampleScope = {
  Breadcrumb,
  BrowserRouter,
  Routes,
  Route,
  Link,
  useNavigate,
  announce,
  clearAnnouncer,
};
export const reactRouterPageOnePath = window.location.pathname;
export const reactRouterPageTwoPath = (() => {
  let pathArray = window.location.pathname.split('/');
  let path = '';
  for (let i = 0; i < pathArray.length - 1; i++) {
    path += (i === 0 ? '' : '/') + pathArray[i];
  }
  return path;
})();
export const reactRouterExample = `
  // import { BrowserRouter, Routes, Route, Link, useNavigate } from 'react-router-dom';
  // import { announce, clearAnnouncer } from '../../utils';
  () => {
      values=[
        {
          id: 'page-1',
          name: 'Page 1',
          route: '${reactRouterPageOnePath}',
          backAriaLabel: '',
        },
        {
          id: 'page-2',
          name: 'Page 2',
          route: '${reactRouterPageTwoPath}/breadcrumb-example-page-2',
          backAriaLabel:
            'Back to Page 1',
        },
      ];
      function PageOne() {
        React.useEffect(()=>{
          clearAnnouncer('assertive');
          announce('You are on Page One of the Breadcrumbs React Router example');
        },[]);
        return(
          <div style={{height: '4rem'}}>
            <p className='evrBodyText' style={{marginBottom: '1.5rem'}}>This is Page 1</p>            
            <Link to="${reactRouterPageTwoPath}/breadcrumb-example-page-2" className='evrBodyText evrHyperlink'>Link to Page 2</Link>
          </div>
        )
      }
      function PageTwo() {
        React.useEffect(()=>{
          clearAnnouncer('assertive');
          announce('You are on Page Two of the Breadcrumbs React Router example');
        },[]);
        const navigate = useNavigate();
        return(
          <div style={{height: '4rem'}}>
            <Breadcrumb
              id="example-breadcrumb"
              onNavigate={(value, isBack)=>{
                navigate(value.route);
              }}
              values={values}
            />
            <p className='evrBodyText' style={{marginTop: '1.5rem'}}>This is Page 2</p>            
          </div>
        )
      }      
      return (
        <BrowserRouter>
          <Routes>
              <Route path="${reactRouterPageOnePath}" element={<PageOne/>} />
              <Route path="${reactRouterPageTwoPath}/breadcrumb-example-page-2" element={<PageTwo/>} />
            </Routes>
        </BrowserRouter>
      );
    }
`;

<CodeExample scope={reactRouterExampleScope} code={reactRouterExample} />

### Breadcrumb Values

Individual `Breadcrumb` items are defined by a `values` array. Each item in this array is typed as below.

```typescript
type TBreadcrumbValue = {
  id: string; // id of a breadcrumb
  name: string; // display name
  route: string; // url or relative path
  backAriaLabel?: string; // accessible label for mobile presentation
  testId?: string; // testId for a breadcrumb
};
```

## Accessing Breadcrumb using ref

Click on the Button to access the Breadcrumb, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            gap: '20px, 10px'
        }
    }
    const Row = ({ children }) => (<div style={styles.row}>{children}</div>);
    const Column = ({ children }) => (<div style={styles.column}>{children}</div>);
    const values = [
        {
          id: 'route-page-1',
          name: 'Home',
          route: 'https://www.dayforce.com/',
          backAriaLabel: 'Back to Home',
        },
        {
          id: 'route-page-2',
          name: 'Get Started',
          route: 'https://www.dayforce.com/get-started',
          backAriaLabel:
            'Back to Get Started',
        },
        {
          id: 'route-page-3',
          name: 'Privacy',
          route:
            'https://www.dayforce.com/privacy',
          backAriaLabel: 'Back to Privacy',
        },
      ];
    const ref=React.useRef(null);
    return (
        <Column>
          <Row>
            <Button id='click-access-el-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
          </Row>
          <Row>
            <Breadcrumb
              ref={ref}
              id="example-default-breadcrumb"
              values={values}
              ariaLabel="Default breadcrumb"
            />
          </Row>
        </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

The `values` prop takes an array representing the page hierarchy. If there are fewer than two entries in this array, `Breadcrumb` does not render.

`Breadcrumb` item names should be short and succinct. The maximum width for a breadcrumb item/link before it is truncated is 350px.

## Accessibility

`ariaLabel` MUST be used to identify the breadcrumb `nav` element. `ariaLabelledBy` can alternatively be used, and will override any `ariaLabel` provided.

Feature teams should announce when a page has loaded. Teams can refer to the react router example above which utilizes LiveAnnouncer.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
