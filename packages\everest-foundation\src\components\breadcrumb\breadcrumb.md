# Breadcrumb

## Summary

Research and document implementations for the Everest Breadcrumb.

- Start Date: 2022-05-17
- Figma link: https://www.figma.com/file/qZSuDtEhFTh3Q61MZaKV2v/Navigation?node-id=2%3A46

## Detailed Design

- At the time of writing, design for Breadcrumb hasn't been completed, therefore requirements are subject to change.

- Breadcrumb can be developed in many different ways as shown by multiple Design Systems. Breadcrumb consists of slash icons and links with mutiple components such as Tooltip and Contextual Menu.

- If the name of a breadcrumb item is too long then item name will be replaced by ellipsis and Tooltip will be used. Other design system such as Workday, implemented following as core logic

```
  useEffect(() => {
    if (ref?.current) {
      setShouldShowTooltip(ref.current.scrollWidth > ref.current.clientWidth);
    }
  }, [ref]);
```

- If number of Breadcrumb items are more than 4 then Contextual Menu will be used. Only first and last items will be displayed and rest will be replaced by ellipsis, but when ellipsis is clicked, Contextual Menu with rest of items will be shown. Refer to this link for logic https://github.com/Workday/canvas-kit/blob/master/modules/preview-react/breadcrumbs/lib/Breadcrumbs/List/Collapsible.tsx

- From a design perspective, the use of contextual menu was chosen so that the page layout impact is minimal when switching from landscape to portrait and vice versa

- Each `BreadcrumbItem` will be focusable and tabable, except icons and last item which indicates the current page. They will correspond to `li` elements and consumers will not directly interact with them.

- The last item won't have a trailing slash icon.

- In mobile mode, only the back button will be displayed.

- An user supplied array `breadCrumbValues` will be used rather than a compositional pattern since the data might need to be transformed to accommodate different components, e.g. Tooltip vs MenuListItem in ContextualMenu.

- Navigation will be driven by user supplied callbacks since pages could be routed in different ways.

## API

### Breadcrumb API

1. **id** `string`  
   Id of the component.
1. **testId**: `undefined | string`  
   Optional. Sets **data-testid** attribute on the html element.
1. **values**: `TBreadcrumbValue[]`  
   Array of `TBreadcrumbValue`s that are mapped to `BreadcrumbItem` components.
   `TBreadcrumbValue` will represent breadcrumbs and have shape `{ id: string, name: string, route?: string }`. It should provide enough flexibility to meet routing needs.
1. **ariaLabel**: `string`  
   Label for `nav` html element.
1. **ariaLabelledBy**: `string | undefined`  
   Optional. `id` of the label element for `Breadcrumb`
1. **onNavigate**: `(item: TBreadcrumbValue, isBack: boolean) => void`  
   Callback provided by the user specifying nagivation action.

### BreadcrumItem API

1. **value**: TBreadcrumbValue  
   Link details of type TBreadcrumbValue as supplied by the user.
1. **onNavigate**: `(item: TBreadcrumbValue, isBack: boolean) => void`  
   Callback supplied by the parent specifying the navigation action on an item's route.
1. **selectedId**: `string`  
   Currently selected id.
1. **onSelected** `(value: TBreadcrumbValue) => void`  
   Sets the currently selected id.
1. **testId** `string | undefined`
   Optional. Sets `data-testid` attribute on the html element.
1. **currentPage**: `boolean | undefined`  
   Optional. Defaults to false.
1. **isMobile**: `boolean`  
   Indicates if the view is mobile.

## Basic Usage

The below illustrates the most basic usage of `Breadcrumb`:

```
<Breadcrumb
  id="my-breadcrumbs"
  values={[
    {
      id: "route-page-1",
      name: "Page 1",
      route: "https://www.example.com/page1,
    },
    {
      id: "route-page-2",
      name: "Page 2",
      route: "/home/<USER>",
    },
  ]}
  ariaLabel={"Breadcrumb navigation"}
  onNavigate={(item) => {
    window.open(item.route, _self)
  }}
/>
```

This will render similar to the below, where `BreadcrumbItem` will be an `li` that runs `onNavigate` when clicked or the enter key is pressed.

```
<nav id="my-breadcrumbs" aria-label="Breadcrumb navigation">
  <ol>
    {
      values.map((item) => {
        return (
          <BreadcrumItem
            onNavigate={() => onNavigate(item) }
          >
            {item.name}
          </BreadcrumItem>
        )
      })
    }
  </ol>
</nav>
```

## Accessibility

- As HTML does not have dedicated semantic elements for breadcrumbs, ARIA attributes are required to make it accessible for people using screen readers.
- To make the breadcrumbs appear to users navigating the page using landmarks, `nav` element will be used.
- `aria-label = "breadcrumb"` will be implemented on nav element.
- In order to make the set of links structured as a hierarchy, ordered list `<ol>` can be used. However, there are other examples where unordered list `<ul>` was used for.
- The visual separator arrows or any other icons are hidden from assistive technologies by using `aria-hidden="true"`.
- For keyboard consideration, each page link in the breadcrumb is reached by `Tab` and activated by `Enter`.
- `aria-current="page"` can be applied to the last link in the set to indicate that it represents the current page.
- The user must supply `aria-label` attribute. The user can also optionally provide `aria-labelledby` attribute as per https://www.w3.org/WAI/ARIA/apg/patterns/breadcrumb/ for the breadcrumb landmark. Note: if `aria-labelledby` value is provided, this value will be displayed.

## Alternatives/Trade-Offs

- Most design systems use prop to pass in the link: Ex. `<Breadcrumb.Item href="www.dayforce.com">`. On ther other hand, this pattern from Ant-Design keep options open by having a user to pass the entire element:

```
<Breadcrumbs.Item>
   <a href="https://www.dayforce.com">Application Center</a>
</Breadcrumbs.Item>
```

Downside of this approach is consumer can use different tag such as p tag or div tag.

## Q&A

**Will Ceridian implement 'Skip navigation' link?**

- ‘Skip navigation’ link allows the user to skip all navigation links, including breadcrumb. This is worth a discussion with accessbility team as it's considered good practice to have 'Skip navigation' link.

**`<ol>` vs `<ul>` for breadcrumb**

~~- Most Design Systems prefer to use `<ol>` as it shows hierarchy. Carbon Design System prefers `<ul>` as "screen readers provide more context." More discussion with accessibility team is needed to address the trade off.~~

- Will go with ordered lists for now since breadcrumbs follow an order.

**How will link `<a>` be handled in breadcrumb?**

~~- We could create separate Link component then implement that in breadcrumb, but for now it will be plain `<a>` tag with href attribute.~~

- Anticpate creating a component with `role='link'`. It seems likely new react apps will use a library like react router and we'll let the user define how they navigate via javascript.

**Will RTL be implemented?**

- Although design team has provided separate icon for RTL, we are not implementing RTL for breadcrumb at the moment.

**Other Miscellaneous Notes**

- Keyboard and mouse styling on breadcrumb items has yet to be determined.

- Key variables in determining whether or not breadcrumb items should be collasped are: width of individual breadcrumb items, width of the entire breadcrumb component, and total number of breadcrumb items

## Future Considerations

- Supporting globalization will be considered in the future.

- Since no `Tooltip` component exists at the time of writing, this will be addressed in a future development phase.

- The `ContextualMenu` implementation will also be considered in a future development phase due to complexity and rarity, e.g. at most 3-4 pages are ever presented according to marketing

- The existence a tooltip and contextual menu may pose a stacking/z-index conflict since it is possible both could appear simultaneously and overlap.

## Other Design Systems

For example:

**Material UI** - https://mui.com/material-ui/react-breadcrumbs/

- Supports collapsed breadcrumb and custom separator which can come in handy if there are more than one icon to be used as separator.

**Ant Design** - https://ant.design/components/breadcrumb/

- Implemented a function that make sure only accept Breadcrumb.Item and Breadcrumb.Separator as it's children. We could develop a custom function like this that can be re-used in other compound components.

**Cedar** - https://rei.github.io/rei-cedar-docs/components/breadcrumb/

- Supports truncated breadcrumb where long breadcrumb path shortened to display the last 2 items with hidden links indicated by ellipsis.

**Patternfly** - https://www.patternfly.org/v4/components/breadcrumb

- Supports dropdown within breadcrumb.

**Orbit** - https://orbit.kiwi/components/navigation/breadcrumbs/

- Supports RTL and in mobile view, breadcrumb shrinks to Back button only, which allow users to easily go back one step in the path.

**Carbon** - https://carbondesignsystem.com/components/breadcrumb/usage/

- When space becomes limited, uses an overflow menu component to truncate the breadcrumbs.
- Good explanation on expected accessibility outcome for breadcrumb.
- Unlike other design systems, uses unordered list so that screen readers provide more context.

**Workday** - https://github.com/Workday/canvas-kit/tree/master/modules/preview-react/breadcrumbs

- Supports truncated with ellipsis when texts in the breadcrumb item is too long.
- Supports truncated with ellipsis when too many breadcrumb items are displayed.

## Required PBIs

- Develop the component in phases and include testing (React + a11y + unit + integration + visual + bug fixes)

## Acceptance Criteria

- Component to be named `Breadcrumb`, with `BreadcrumbItem`s to be mapped from the `breadcrumbValues` array
- First build a basic breadcrumb component with responsive design and setup in Storybook (Components, Foundations, Automation)
- Styles are on par with Design spec in Figma

- Verify the following:

  - API works as intended
  - Last item in the breadcrumb should not have trailing slash icon.
  - Last item in the breadcrumb should not be interactiveable.

  - When the number of items in the breadcrumb is more than 4, only first and last two items should be displayed and rest items should be in ellipsis. When ellipsis is clicked, contextual menu should pop over displaying missing items on the menu.
  - Accessibility (aria attributes, tab order, screen reader callouts, mouse and keyboard interactivity)

- Unit and integration tests implemented

# Changelog

1/5/2024 [EDS-3706 [Dev][Breadcrumb] wrapping strategy and size](https://ceridian.atlassian.net/browse/EDS-3706)
10/29/2024 [PWEB-13498 Enhance A11y for [Breadcrumb] component](https://dayforce.atlassian.net/browse/PWEB-13498)
