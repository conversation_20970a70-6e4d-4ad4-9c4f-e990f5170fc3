@use '../../variables.scss' as variables;

.evrBreadcrumb {
  & ol {
    -webkit-outline: none;
    -moz-outline: none;
    outline: none;
    margin: 0;
    padding: 0;
    border: 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
  }
  & .itemContainer {
    display: flex;
    align-items: center;
    max-width: 100%;
  }
  & .seperator {
    display: flex;
    align-self: flex-start;
    margin-block-start: variables.$breadcrumbIconMarginOffset; // need to follow up with design
  }
}
