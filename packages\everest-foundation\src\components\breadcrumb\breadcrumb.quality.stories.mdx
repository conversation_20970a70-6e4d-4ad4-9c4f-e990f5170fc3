import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { Breadcrumb } from './breadcrumb';
import { userEvent, within } from '@storybook/test';
import { action } from '@storybook/addon-actions';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';

export const breadcrumbStoryValues = [
  {
    id: 'route-page-1',
    name: 'Home',
    route: 'https://www.dayforce.com/',
    backAriaLabel: 'This is a breadcrumb to go back to Home',
    testId: 'route-page-1-test',
  },
  {
    id: 'route-page-2',
    name: 'Get Started',
    route: 'https://www.dayforce.com/get-started',
    backAriaLabel: 'This is a breadcrumb to go back to Get Started with Dayforce',
    testId: 'route-page-2-test',
  },
  {
    id: 'route-page-3',
    name: 'Privacy',
    route: 'https://www.dayforce.com/privacy',
    backAriaLabel: 'This is a breadcrumb to go back to Privacy',
    testId: 'route-page-3-test',
  },
];
export const breadcrumbWrapStoryValues = [
  {
    id: 'route-page-1',
    name: 'Home',
    route: 'https://www.dayforce.com/',
    backAriaLabel: 'This is a breadcrumb to go back to Home',
    testId: 'route-page-1-test',
  },
  {
    id: 'route-page-2',
    name: 'Get Started Get Started Get Started Get Started Get Started',
    route: 'https://www.dayforce.com/get-started',
    backAriaLabel: 'This is a breadcrumb to go back to Get Started with Dayforce',
    testId: 'route-page-2-test',
  },
  {
    id: 'route-page-3',
    name: 'Privacy Privacy Privacy Privacy Privacy Privacy Privacy',
    route: 'https://www.dayforce.com/privacy',
    backAriaLabel: 'This is a breadcrumb to go back to Privacy',
    testId: 'route-page-3-test',
  },
];

export const testId = 'breadcrumb-test';

export const desktopMode = {
  modes: {
    breakpointXs: { disable: true }, // disable mobile mode for desktop size breadcrumb
    breakpointXxl: defaultModes['breakpointXxl'],
  },
};

<Meta
  title="Testing/Automation Test Cases/Breadcrumb"
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  component={Breadcrumb}
  args={{
    id: 'breadcrumbs-example',
    testId: testId,
    ariaLabel: 'Breadcrumbs example',
    onNavigate: action('onNavigate'),
  }}
/>

# BreadcrumbItem

## Live Demo

<Canvas>
  <Story
    name="Size lg default"
    parameters={{
      chromatic: desktopMode,
    }}
  >
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Size lg with tab focus"
    parameters={{
      chromatic: desktopMode,
    }}
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Size lg with hover"
    parameters={{
      chromatic: desktopMode,
    }}
    play={async ({ canvasElement }) => {
      const canvas = within(canvasElement);
      await userEvent.hover(canvas.getByTestId('route-page-2-test'));
    }}
  >
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Size lg with wrapped text"
    parameters={{
      chromatic: desktopMode,
    }}
  >
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbWrapStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Size lg with hover and wrapped text"
    parameters={{
      chromatic: desktopMode,
    }}
    play={async ({ canvasElement }) => {
      const canvas = within(canvasElement);
      await userEvent.hover(canvas.getByTestId('route-page-2-test'));
    }}
  >
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbWrapStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Size sm default">
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
        size={'sm'}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Size sm with wrapped text">
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbWrapStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
        size={'sm'}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Size sm with hover and wrapped text"
    play={async ({ canvasElement }) => {
      const canvas = within(canvasElement);
      await userEvent.hover(canvas.getByTestId('route-page-2-test'));
    }}
  >
    {(args) => (
      <Breadcrumb
        id={args.id}
        values={breadcrumbWrapStoryValues}
        onNavigate={args.onNavigate}
        ariaLabel={args.ariaLabel}
        testId={args.testId}
        size={'sm'}
      />
    )}
  </Story>
</Canvas>
