import React from 'react';
import { render, screen } from '@testing-library/react';

import { Breadcrumb, IBreadcrumb, TBreadcrumbSize, TBreadcrumbValue } from './breadcrumb';

describe('[Breadcrumb]', () => {
  const id = 'breadcrumb';
  const ariaLabel = 'breadcrumb aria label';
  const testId = 'breadcrumb-test';
  const values: TBreadcrumbValue[] = [
    {
      id: 'route-page-1',
      name: 'Home',
      route: 'test-page-1',
      backAriaLabel: 'This is a breadcrumb to go back to Home',
    },
    {
      id: 'route-page-2',
      name: 'Get Started',
      route: 'test-page-2',
      backAriaLabel: 'This is a breadcrumb to go back to Get Started with Dayforce',
    },
    {
      id: 'route-page-3',
      name: 'Privacy',
      route: 'test-page-3',
      backAriaLabel: 'This is a breadcrumb to go back to Privacy',
    },
  ];

  const propsMock = {
    id,
    values,
    ariaLabel,
    testId,
  };

  const getBreadcrumb = () => screen.getByTestId(testId);
  const queryBreadcrumb = () => screen.queryByTestId(testId);

  function renderComponent(props: IBreadcrumb, size?: TBreadcrumbSize) {
    render(<Breadcrumb {...props} size={size} />);
  }

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render size "sm" without issue', () => {
    renderComponent(propsMock, 'sm');
    expect(getBreadcrumb()).toBeInTheDocument();
    expect(screen.getAllByRole('link').length).toEqual(1);
    expect(document.querySelectorAll('svg[data-evr-name="arrowLeft"]').length).toEqual(1);
  });
  it('should render size "lg" without issue', () => {
    renderComponent(propsMock);
    expect(getBreadcrumb()).toBeInTheDocument();
    expect(screen.getAllByRole('link').length).toEqual(3);
    expect(document.querySelectorAll('svg[data-evr-name="forwardSlash"]').length).toEqual(2);
  });
  it('should have an aria-label when given', () => {
    renderComponent(propsMock);
    expect(getBreadcrumb()).toHaveAttribute('aria-label', ariaLabel);
  });
  it('should have an aria-labelledby attribute when given', () => {
    const ariaLabelId = 'test-aria-id';
    renderComponent({
      ...propsMock,
      ariaLabelledBy: ariaLabelId,
    });
    expect(getBreadcrumb()).toHaveAttribute('aria-labelledby', ariaLabelId);
  });
  it('should not render when there is no breadcrumb array of values', () => {
    renderComponent({
      ...propsMock,
      values: [],
    });
    expect(queryBreadcrumb()).toBeNull();
  });
  it('should not render when there is one breadcrumb array value', () => {
    renderComponent({
      ...propsMock,
      values: [
        {
          id: 'route-page-1',
          name: 'Home',
          route: 'test-page-1',
          backAriaLabel: 'This is a breadcrumb to go back to Home',
        },
      ],
    });
    expect(queryBreadcrumb()).toBeNull();
  });
});
