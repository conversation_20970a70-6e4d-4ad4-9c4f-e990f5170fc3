import { Meta, Story, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { Breadcrumb, BreadcrumbItem } from './breadcrumb';
import { action } from '@storybook/addon-actions';
import Examples from './breadcrumb.examples.mdx';

<Meta
  title="Components/Breadcrumb"
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9992&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  argTypes={{
    id: {
      type: { name: 'string', required: true },
      control: 'text',
      description: 'Set the `id` attribute on the `nav` element of `Breadcrumb`.',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets `data-testid` attribute on the `nav` element of `Breadcrumb`.',
    },
    ariaLabel: {
      type: { name: 'string', required: true },
      control: 'text',
      description: 'Accessible label to describe the `Breadcrumb`',
    },
    ariaLabelledBy: {
      type: 'string',
      control: '-',
      description: 'Id of an optional label element. ',
    },
    onNavigate: {
      control: '-',
      description: 'Callback defining custom navigation behavior. Overrides default `<a>` tag behavior.',
    },
    values: {
      type: { name: 'TBreadcrumbValue[]', required: true },
      description: 'Array of breadcrumb values of type `TBreadcrumbValue`',
    },
    size: {
      description: 'Specifies the size of `Breadcrumb`. Default is `lg`.',
      type: 'enum',
      control: 'select',
      options: ['sm', 'lg'],
      table: { defaultValue: { summary: 'lg' } },
    },
  }}
  args={{
    id: 'breadcrumb',
    testId: 'breadcrumb-test',
    ariaLabel: 'Breadcrumbs example',
    onNavigate: action('onNavigate'),
    size: 'lg',
    values: [
      {
        id: 'route-page-1',
        name: 'Home',
        route: 'https://www.dayforce.com/',
        backAriaLabel: 'Back to Home',
      },
      {
        id: 'route-page-2',
        name: 'Get Started',
        route: 'https://www.dayforce.com/get-started',
        backAriaLabel: 'Back to Get Started',
      },
      {
        id: 'route-page-3',
        name: 'Privacy',
        route: 'https://www.dayforce.com/privacy',
        backAriaLabel: 'Back to Privacy',
      },
    ],
  }}
/>

# Breadcrumb

<Examples />

## Live Demo

<Canvas>
  <Story name="Breadcrumb">
    {(args) => {
      return (
        <Breadcrumb
          id={args.id}
          values={args.values}
          onNavigate={args.onNavigate}
          ariaLabel={args.ariaLabel}
          testId={args.testId}
          size={args.size}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Breadcrumb" />
