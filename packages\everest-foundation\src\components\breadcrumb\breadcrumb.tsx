import React, { useState } from 'react';

import { BreadcrumbItem } from './breadcrumb-item';
import { Icon } from '../icon';

import styles from './breadcrumb.module.scss';

export type TBreadcrumbSize = 'sm' | 'lg';

export type TBreadcrumbValue = {
  id: string;
  name: string;
  route: string;
  backAriaLabel?: string;
  testId?: string;
};

export interface IBreadcrumb {
  id: string;
  values: TBreadcrumbValue[];
  ariaLabel: string;
  ariaLabelledBy?: string;
  onNavigate?: (item: TBreadcrumbValue, isBack: boolean) => void;
  testId?: string;
  ref?: React.ForwardedRef<HTMLElement>;
  size?: TBreadcrumbSize;
}

export const Breadcrumb = React.forwardRef<HTMLElement, IBreadcrumb>((props: IBreadcrumb, ref) => {
  const { id, values, ariaLabel, ariaLabelledBy, onNavigate, testId, size = 'lg' } = props;
  const [selectedId, setSelectedId] = useState('');

  function onSelected(selected: TBreadcrumbValue) {
    setSelectedId(selected.id);
  }

  function renderSeparator(index: number) {
    if (index === 0) return <></>;
    return (
      <span className={styles.seperator}>
        <Icon name="forwardSlash" fill="--evr-content-primary-lowemp" aria-hidden="true" />
      </span>
    );
  }
  function renderMobileBreadcrumbItem() {
    return (
      <div className={styles.itemContainer}>
        <BreadcrumbItem
          value={values[values.length - 2]}
          onNavigate={onNavigate}
          selectedId={selectedId}
          onSelected={onSelected}
          isMobile={true}
        />
      </div>
    );
  }
  function renderDesktopBreadcumbItems() {
    return (
      <ol>
        {values.map((value, index, array) => (
          <li key={value.id} className={styles.itemContainer}>
            {renderSeparator(index)}
            <BreadcrumbItem
              value={value}
              onNavigate={onNavigate}
              currentPage={index === array.length - 1}
              selectedId={selectedId}
              onSelected={onSelected}
              isMobile={false}
            />
          </li>
        ))}
      </ol>
    );
  }
  function renderBreadcrumbItems() {
    if (size === 'sm') {
      return renderMobileBreadcrumbItem();
    }
    if (size === 'lg') {
      return renderDesktopBreadcumbItems();
    }
    return <></>;
  }

  if (values.length < 2) return <></>;

  return (
    <nav
      id={id}
      aria-label={ariaLabelledBy ? undefined : ariaLabel}
      aria-labelledby={ariaLabelledBy}
      className={styles.evrBreadcrumb}
      ref={ref}
      data-testid={testId}
    >
      {renderBreadcrumbItems()}
    </nav>
  );
});

Breadcrumb.displayName = 'Breadcrumb';
