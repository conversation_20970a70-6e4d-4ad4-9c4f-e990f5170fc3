import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { ButtonBase } from './button-base';
import { Warning } from '../../../.storybook/docs/shared/status-banner.tsx';
import { Icon } from '../icon';

export const scope = { ButtonBase, Icon };

`ButtonBase` offers flexibility to build custom button components matching various designs.

## Variants

Define the button's appearance using style variants like Primary, Secondary, and Tertiary.

export const variantsExamples = `() => {
    const styles = {
        table: {
            width: '100%',
            borderCollapse: 'collapse'
        },
        td: {
            border: '1px solid #ddd',
            textAlign: 'center',
            padding: '10px'
        },
    };
    const Td = (props) => {
        let cellStyles = props.inverseBackground ? {...styles.td, background: "rgb(51, 51, 51)"} : styles.td;
        cellStyles = props.headerColumn ? {...cellStyles, fontWeight: "bold"} : cellStyles;
        return <td style={cellStyles}>{props.children}</td>
    }
    return (
        <table style={styles.table}>
            <tr>
                <Td headerColumn>Variants</Td>
                <Td><ButtonBase id='primary-btn' variant='primary'>Primary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn' variant='secondary'>Secondary</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn' variant='tertiary'>Tertiary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn' variant='secondaryNeutral'>Secondary Neutral</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn' variant='tertiaryNeutral'>Tertiary Neutral</ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>Disabled</Td>
                <Td><ButtonBase id='primary-btn-disabled' variant='primary' disabled>Primary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-disabled' variant='secondary' disabled>Secondary</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-disabled' variant='tertiary' disabled>Tertiary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn-disabled' variant='secondaryNeutral' disabled>Secondary Neutral</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn-disabled' variant='tertiaryNeutral' disabled>Tertiary Neutral</ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>Inverse</Td>
                <Td>Not supported</Td>
                <Td inverseBackground><ButtonBase id='secondary-btn-inverse' variant='secondary' inverse>Secondary</ButtonBase></Td>
                <Td inverseBackground><ButtonBase id='tertiary-btn-inverse' variant='tertiary' inverse>Tertiary</ButtonBase></Td>
                <Td>Not supported</Td>
                <Td>Not supported</Td>
            </tr>
        </table>
    );
}`;

<CodeExample scope={scope} code={variantsExamples} />

## Sizes

Use the `size` property to change the visual size of the buttons by adjusting their padding and font.

export const sizeExamples = `() => {
    const styles = {
        table: {
            width: '100%',
            borderCollapse: 'collapse'
        },
        td: {
            border: '1px solid #ddd',
            textAlign: 'center',
            padding: '10px'
        },
    };
    const Td = (props) => {
        let cellStyles = props.inverseBackground ? {...styles.td, background: "rgb(51, 51, 51)"} : styles.td;
        cellStyles = props.headerColumn ? {...cellStyles, fontWeight: "bold"} : cellStyles;
        return <td style={cellStyles}>{props.children}</td>
    }
    return (
        <table style={styles.table}>
            <tr>
                <Td headerColumn>Small</Td>
                <Td><ButtonBase id='primary-btn-small' variant='primary' size='small'>Primary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-small' variant='secondary' size='small'>Secondary</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-small' variant='tertiary' size='small'>Tertiary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn-small' variant='secondaryNeutral' size='small'>Secondary Neutral</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn-small' variant='tertiaryNeutral' size='small'>Tertiary Neutral</ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>Medium</Td>
                <Td><ButtonBase id='primary-btn-medium' variant='primary' size='medium'>Primary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-medium' variant='secondary' size='medium'>Secondary</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-medium' variant='tertiary' size='medium'>Tertiary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn-medium' variant='secondaryNeutral' size='medium'>Secondary Neutral</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn-medium' variant='tertiaryNeutral' size='medium'>Tertiary Neutral</ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>Large</Td>
                <Td><ButtonBase id='primary-btn-large' variant='primary' size='large'>Primary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-large' variant='secondary' size='large'>Secondary</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-large' variant='tertiary' size='large'>Tertiary</ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn-large' variant='secondaryNeutral' size='large'>Secondary Neutral</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn-large' variant='tertiaryNeutral' size='large'>Tertiary Neutral</ButtonBase></Td>
            </tr>
        </table>
    );
}`;

<CodeExample scope={scope} code={sizeExamples} />

## Uniform Size

Uniform Size allows forcing the `BaseButton` to have the same width and height, while respecting the value passed using the
`size` property. This allows creation of square and round buttons.

export const uniformSizeExamples = `() => {
    const styles = {
        table: {
            width: '100%',
            borderCollapse: 'collapse'
        },
        td: {
            border: '1px solid #ddd',
            textAlign: 'center',
            padding: '10px'
        },
    };
    const Td = (props) => {
        let cellStyles = props.inverseBackground ? {...styles.td, background: "rgb(51, 51, 51)"} : styles.td;
        cellStyles = props.headerColumn ? {...cellStyles, fontWeight: "bold"} : cellStyles;
        return <td style={cellStyles}>{props.children}</td>
    }
    return (
        <table style={styles.table}>
            <tr>
                <Td headerColumn>Small Button with Small Icon</Td>
                <Td><ButtonBase id='primary-btn-small-uniform' variant='primary' size='small' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-small-uniform' variant='secondary' size='small' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-small-uniform' variant='tertiary' size='small' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn-small-uniform' variant='secondaryNeutral' size='small' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn-small-uniform' variant='tertiaryNeutral' size='small' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>Medium Button with Small Icon</Td>
                <Td><ButtonBase id='primary-btn-medium-uniform' variant='primary' size='medium' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-medium-uniform' variant='secondary' size='medium' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-medium-uniform' variant='tertiary' size='medium' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn-medium-uniform' variant='secondaryNeutral' size='medium' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn-medium-uniform' variant='tertiaryNeutral' size='medium' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>Large Button with Medium Icon</Td>
                <Td><ButtonBase id='primary-btn-large-uniform' variant='primary' size='large' uniformSize><Icon name='person' size='md'/></ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-large-uniform' variant='secondary' size='large' uniformSize><Icon name='person' size='md'/></ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-large-uniform' variant='tertiary' size='large' uniformSize><Icon name='person' size='md'/></ButtonBase></Td>
                <Td><ButtonBase id='secondary-neutral-btn-large-uniform' variant='secondaryNeutral' size='large' uniformSize><Icon name='person' size='md'/></ButtonBase></Td>
                <Td><ButtonBase id='tertiary-neutral-btn-large-uniform' variant='tertiaryNeutral' size='large' uniformSize><Icon name='person' size='md'/></ButtonBase></Td>
            </tr>
        </table>
    );
}`;

<CodeExample scope={scope} code={uniformSizeExamples} />

## Border Radius

Use the `borderRadius` size to customize the look of the button corners.

export const radiusExamples = `() => {
    const styles = {
        table: {
            width: '100%',
            borderCollapse: 'collapse'
        },
        td: {
            border: '1px solid #ddd',
            textAlign: 'center',
            padding: '10px'
        },
    };
    const Td = (props) => {
        let cellStyles = props.inverseBackground ? {...styles.td, background: "rgb(51, 51, 51)"} : styles.td;
        cellStyles = props.headerColumn ? {...cellStyles, fontWeight: "bold"} : cellStyles;
        return <td style={cellStyles}>{props.children}</td>
    }
    return (
        <table>
            <tr>
                <Td headerColumn>--evr-radius-3xs</Td>
                <Td><ButtonBase id='primary-btn-3xs' size='small' radius='--evr-radius-3xs'>Everest</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-3xs' size='medium' radius='--evr-radius-3xs'>Everest</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-3xs' size='large' radius='--evr-radius-3xs'>Everest</ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>--evr-radius-2xs</Td>
                <Td><ButtonBase id='primary-btn-2xs' size='small' radius='--evr-radius-2xs'>Everest</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-2xs' size='medium' radius='--evr-radius-2xs'>Everest</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-2xs' size='large' radius='--evr-radius-2xs'>Everest</ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>--evr-radius-md</Td>
                <Td><ButtonBase id='primary-btn-md' size='small' radius='--evr-radius-md'>Everest</ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-md' size='medium' radius='--evr-radius-md'>Everest</ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-md' size='large' radius='--evr-radius-md'>Everest</ButtonBase></Td>
            </tr>
            <tr>
                <Td headerColumn>--evr-radius-circle</Td>
                <Td><ButtonBase id='primary-btn-circle' size='small' radius='--evr-radius-circle' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='secondary-btn-circle' size='medium' radius='--evr-radius-circle' uniformSize><Icon name='person' size='sm'/></ButtonBase></Td>
                <Td><ButtonBase id='tertiary-btn-circle' size='large' radius='--evr-radius-circle' uniformSize><Icon name='person' size='md'/></ButtonBase></Td>
            </tr>
        </table>
    );
}`;

<CodeExample scope={scope} code={radiusExamples} />

## Further Customization

You can further customize styling of the `ButtonBase` beyond the standard options by passing a CSS class name using the `className` property.
It is important to remember that depending on your configuration, you may need to increase specificity to override default properties.

<Warning>This feature should only be used as a last resort and for one-off customization only.</Warning>

export const customStyle = `() => {
    const css = \`
        .demo-class.evrFocusRing {
            font-family: 'Courier New', monospace;
        }\`;

    return (<>
        {/* Using \`style\` tag in order to encapsulate the CSS within this Storybook example.
            Should not actually be used when developing a component */}
        <style>{css}</style>
        <ButtonBase id='primary-btn-class' className='demo-class'>Custom Font</ButtonBase>
    </>);
}`;

<CodeExample scope={scope} code={customStyle} />

## Accessing ButtonBase using ref

You can access element by passing a ref:

export const refCode = `() => {
    const ref=React.useRef(null);
    return (<ButtonBase ref={ref} id='click-access-el-btn' onClick={()=>{console.log(ref.current)}}>Click to access element</ButtonBase>)
}`;

<CodeExample scope={scope} code={refCode} />

## How to Use

For usage guidance check the documentation of components that were created using `ButtonBase`.
