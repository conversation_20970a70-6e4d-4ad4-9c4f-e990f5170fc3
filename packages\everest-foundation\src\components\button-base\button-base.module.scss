@use '../../mixins.scss' as mixins;
@use '@ceridianhcm/theme/dist/scss/' as typography;

.evrButtonBase {
  // font
  @include typography.body2Bold; // this needs to be first because it includes "reset" mixin that clears margin and padding
  line-height: var(--evr-line-height-md);

  // color
  color: var(--evr-content-primary-default);
  background: none;

  // border box and sizing
  box-sizing: border-box;
  margin: 0;
  max-width: 100%;
  height: min-content;
  border: 0;
  outline: none;
  padding-inline: var(--evr-size-sm);

  // line breaking
  white-space: normal;
  overflow-wrap: break-word;

  // flex
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--evr-spacing-2xs);

  // interaction
  cursor: pointer;
  user-select: none;
}

/* SIZING */
.small {
  min-height: var(--evr-size-md); // 24px vertically even with icon
  padding-block: var(--evr-spacing-4xs); // 2px

  svg {
    // reducing vertical margin of the icon to offset the padding.
    // Icon is 24px just like the small button's height should so keeping it as is would make the button 28px.
    margin-block: calc(var(--evr-spacing-4xs) * -1);
  }

  &.secondary,
  &.secondaryNeutral {
    padding-block: calc(var(--evr-spacing-4xs) - var(--evr-border-width-thin-px)); // 1px
  }

  &.uniformSize {
    height: var(--evr-size-md);
    width: var(--evr-size-md);
    padding: var(--evr-spacing-4xs); // Ensures a small icon will fit without expanding the button
  }
}
.medium {
  $mediumPaddingBlock: calc(var(--evr-spacing-2xs) - var(--evr-spacing-4xs)); // 6px

  min-height: var(--evr-size-lg); // 32px
  padding-block: $mediumPaddingBlock;

  svg {
    margin-block: calc(var(--evr-spacing-3xs) * -1);
  }

  &.secondary,
  &.secondaryNeutral {
    padding-block: calc($mediumPaddingBlock - var(--evr-border-width-thin-px)); // 5px
  }

  &.uniformSize {
    height: var(--evr-size-lg);
    width: var(--evr-size-lg);
  }
}
.large {
  $largePaddingBlock: calc(var(--evr-spacing-sm) - var(--evr-spacing-3xs)); // 12px;

  font-size: var(--evr-font-size-lg);
  min-height: var(--evr-size-2xl); // 48px
  padding-block: $largePaddingBlock;

  &.secondary,
  &.secondaryNeutral {
    padding-block: calc($largePaddingBlock - var(--evr-border-width-thin-px)); // 11px
  }

  &.uniformSize {
    height: var(--evr-size-2xl);
    width: var(--evr-size-2xl);
  }
}

/* COLORING */
// BaseButton styles change the "fill" value in addition to text color to keep the passed icon's color consistent with the text accross variants and pseudo classes of the button.
.primary {
  color: var(--evr-content-primary-inverse);
  fill: var(--evr-content-primary-inverse);
  background-color: var(--evr-interactive-primary-default);

  &:hover {
    background-color: var(--evr-interactive-primary-hovered);
  }

  &:active {
    background-color: var(--evr-interactive-primary-pressed);
  }
}

.secondary {
  color: var(--evr-interactive-primary-default);
  fill: var(--evr-interactive-primary-default);
  border: var(--evr-border-width-thin-px) solid;
  border-color: var(--evr-interactive-primary-default);

  &:not(.disabled) {
    &:hover {
      color: var(--evr-interactive-primary-hovered);
      fill: var(--evr-interactive-primary-hovered);
      border-color: var(--evr-interactive-primary-hovered);
    }

    &:active {
      color: var(--evr-interactive-primary-pressed);
      fill: var(--evr-interactive-primary-pressed);
      border-color: var(--evr-interactive-primary-pressed);
    }

    &.inverse {
      color: var(--evr-content-primary-inverse);
      fill: var(--evr-content-primary-inverse);
      border-color: var(--evr-borders-primary-default);

      &:hover {
        border-color: var(--evr-borders-primary-hovered);
      }

      &:active {
        border-color: var(--evr-borders-primary-pressed);
      }
    }
  }
}

.secondaryNeutral {
  color: var(--evr-content-primary-highemp);
  fill: var(--evr-content-primary-highemp);
  border: var(--evr-border-width-thin-px) solid;
  border-color: var(--evr-borders-primary-default);

  &:not(.disabled) {
    &:hover {
      border-color: var(--evr-borders-primary-hovered);
    }

    &:active {
      border-color: var(--evr-borders-primary-pressed);
    }
  }
}

.tertiary {
  color: var(--evr-interactive-primary-default);
  fill: var(--evr-interactive-primary-default);

  &:not(.disabled) {
    &:not(.inverse) {
      &:hover {
        color: var(--evr-interactive-primary-hovered);
        fill: var(--evr-interactive-primary-hovered);
        background-color: var(--evr-surfaces-secondary-hovered);
      }

      &:active {
        color: var(--evr-interactive-primary-pressed);
        fill: var(--evr-interactive-primary-pressed);
        background-color: var(--evr-surfaces-tertiary-hovered);
      }
    }

    &.inverse {
      color: var(--evr-content-primary-inverse);
      fill: var(--evr-content-primary-inverse);

      &:active {
        background-color: var(--evr-surfaces-secondary-inverse-pressed);
      }

      &:hover {
        background-color: var(--evr-surfaces-secondary-inverse-hovered);
      }
    }
  }
}

.tertiaryNeutral {
  color: var(--evr-content-primary-highemp);
  fill: var(--evr-content-primary-highemp);

  &:not(.disabled) {
    &:hover {
      background-color: var(--evr-surfaces-secondary-hovered);
    }

    &:active {
      background-color: var(--evr-surfaces-tertiary-hovered);
    }
  }
}

.disabled {
  // in all variants expect primary, the background and border (if present) changes to transparent
  color: var(--evr-inactive-content);
  fill: var(--evr-inactive-content);
  background-color: transparent;
  border-color: transparent;
  cursor: not-allowed;

  &.primary {
    background-color: var(--evr-inactive-surfaces);
  }
}

.callToAction {
  @include mixins.clarikageo;
  padding-inline: var(--evr-size-sm);

  &.tertiary,
  &.tertiaryNeutral {
    text-decoration: underline;
    text-decoration-skip-ink: none;
    text-underline-offset: var(--evr-border-width-thick-px);
    padding-inline: var(--evr-spacing-2xs);
  }

  /* Spacing when icon(s) present */
  &.evrButtonBase:not(.tertiary, .tertiaryNeutral):has(svg:first-child:only-of-type) {
    // apply when start icon present if NOT tertiary or tertiaryNeutral
    padding-inline: var(--evr-spacing-2xs) var(--evr-spacing-sm);
  }
  &.evrButtonBase:not(.tertiary, .tertiaryNeutral):has(svg:last-child:only-of-type) {
    // apply when end icon present if NOT tertiary or tertiaryNeutral
    padding-inline: var(--evr-spacing-sm) var(--evr-spacing-2xs);
  }
  &.evrButtonBase:has(svg:first-child, svg:last-child) {
    // apply when start & end icon are present
    padding-inline: var(--evr-spacing-2xs);
  }
}
