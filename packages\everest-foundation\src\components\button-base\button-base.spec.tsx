import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { ButtonBase } from './button-base';

describe('[ButtonBase]', () => {
  const id = 'button-id';
  const label = 'click me';
  const ariaLabelledBy = 'aria-labelledby content';
  const ariaControls = 'aria-controls content';
  const ariaDescribedBy = 'aria-describedby content';
  const onClick = jest.fn();
  const onKeyDown = jest.fn();
  const onFocus = jest.fn();
  const onBlur = jest.fn();

  const props = { id, onClick };

  const getButton = () => screen.getByRole('button');

  beforeEach(jest.clearAllMocks);

  it('should have disabled attribute, when disabled prop is set to true', async () => {
    render(
      <ButtonBase {...props} disabled={true}>
        {label}
      </ButtonBase>
    );
    expect(getButton()).toHaveAttribute('disabled', '');
    expect(getButton()).not.toHaveAttribute('aria-disabled');
  });

  it('should render aria-labelledby when value is set', async () => {
    render(
      <ButtonBase {...props} ariaLabelledBy={ariaLabelledBy}>
        {label}
      </ButtonBase>
    );
    expect(getButton()).toHaveAttribute('aria-labelledby', ariaLabelledBy);
  });

  it('should render aria-describedby when value is set', async () => {
    render(
      <ButtonBase {...props} ariaDescribedBy={ariaDescribedBy}>
        {label}
      </ButtonBase>
    );
    expect(getButton()).toHaveAttribute('aria-describedby', ariaDescribedBy);
  });

  it('should render aria-controls when value is set', async () => {
    render(
      <ButtonBase {...props} ariaControls={ariaControls}>
        {label}
      </ButtonBase>
    );
    expect(getButton()).toHaveAttribute('aria-controls', ariaControls);
  });

  it('dispatch a click event by mouse click', async () => {
    render(<ButtonBase {...props}>{label}</ButtonBase>);

    await userEvent.click(getButton());
    expect(onClick).toHaveBeenCalledTimes(1);
  });
  // todo: click event should not dispatch when using key 'a', it triggered on unittest, but not storybook.
  it('dispatch a click event by Keyboard Enter key', async () => {
    render(<ButtonBase {...props}>{label}</ButtonBase>);

    await userEvent.tab();
    await userEvent.keyboard('[Enter]');
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('dispatch a click event by Keyboard Space key', async () => {
    render(<ButtonBase {...props}>{label}</ButtonBase>);

    await userEvent.tab();
    await userEvent.keyboard('[Space]');
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('dispatch a onKeyDown event by Keyboard Space key', async () => {
    render(
      <ButtonBase {...props} onKeyDown={onKeyDown}>
        {label}
      </ButtonBase>
    );

    await userEvent.tab();
    await userEvent.keyboard('[Space]');
    expect(onKeyDown).toHaveBeenCalledTimes(1);
  });

  it('dispatch onFocus event when button gets focus', async () => {
    render(
      <ButtonBase {...props} onFocus={onFocus}>
        {label}
      </ButtonBase>
    );

    await userEvent.tab();
    expect(onFocus).toHaveBeenCalledTimes(1);
  });

  it('dispatch onBlur event when button gets focus', async () => {
    render(
      <ButtonBase {...props} onBlur={onBlur}>
        {label}
      </ButtonBase>
    );

    await userEvent.tab();
    await userEvent.tab();
    expect(onBlur).toHaveBeenCalledTimes(1);
  });
});
