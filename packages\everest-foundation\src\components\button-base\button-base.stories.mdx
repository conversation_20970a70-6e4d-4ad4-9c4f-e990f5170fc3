import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { ButtonBase } from './button-base';
import { action } from '@storybook/addon-actions';
import Examples from './button-base.examples.mdx';
import { ICON_NAMES } from '../icon';

<Meta
  title="Toolbox/ButtonBase"
  component={ButtonBase}
  parameters={{
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/Everest-Web?type=design&node-id=3157-10020&mode=design&t=ksQE5zczOQp9deBj-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    disabled: {
      type: 'boolean',
      description: 'Disables the Button to prevent any user action.',
      table: {
        defaultValue: { summary: false },
      },
    },
    inverse: {
      type: 'boolean',
      description:
        'Changes the appearance of the button to be used on dark surfaces. Affects the `Secondary Button` and `Tertiary Button` only.',
      table: {
        defaultValue: { summary: false },
      },
    },
    uniformSize: {
      type: 'boolean',
      description:
        'Forces width and height to be the same value, respecting the `size` property. Should only be used with icon buttons.',
      table: {
        defaultValue: { summary: false },
      },
    },
    variant: {
      type: 'enum',
      control: 'select',
      description: 'Changes the appearance of the button. Each variant is associated with a different priority.',
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    callToAction: {
      type: 'boolean',
      description: 'Sets call to action styling on button.',
      table: {
        defaultValue: { summary: false },
      },
    },
    size: {
      type: 'enum',
      control: 'select',
      description: 'Changes the size of the button.',
      table: {
        defaultValue: { summary: 'medium' },
      },
    },
    radius: {
      control: 'select',
      description: 'Adjusts the corner radius of the button.',
      table: {
        defaultValue: { summary: '--evr-radius-md' },
      },
    },
    className: {
      control: '-',
      description:
        'Sets additional classes on the button element. Only use this in cases where non-standard appearance is required.',
    },
    type: {
      type: 'enum',
      control: 'select',
      description: 'Sets the type of the button.',
    },
    ariaLabel: {
      type: 'string',
      description:
        "Required for accessibility if the button has no visible label. A clear description of the Button's purpose.",
    },
    ariaHasPopup: {
      type: 'boolean',
      description: 'Indicates that the element has a popup context menu or sub-level menu.',
    },
    ariaExpanded: {
      type: 'boolean',
      description: 'Indicates whether the controlled element is expanded or collapsed.',
    },
    ariaControls: {
      type: 'string',
      control: 'text',
      description: 'Identifies the element (or elements) that are controlled by the button.',
    },
    ariaOwns: {
      type: 'string',
      control: 'text',
      description:
        'Identifies the element (or elements) in order to define a relationship between parent and a child when the DOM hirerachy cannot be used to represent the relationship.',
    },
    ariaLabelledBy: {
      type: 'string',
      control: 'text',
      description: 'Identifies the element (or elements) that labels the element it is applied to.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: 'text',
      description: 'Identifies the element (or elements) that describes the element on which the attribute is set.',
    },
    tabIndex: {
      control: 'select',
      description: 'Sets the `tabIndex` on the button.',
    },
    onClick: {
      control: '-',
      description: 'Callback fired when user clicks on the button.',
    },
    onKeyDown: {
      control: '-',
      description: 'Callback fired when user presses a key when button is focused.',
    },
    onFocus: {
      control: '-',
      description: 'Callback fired when button receives focus.',
    },
    onBlur: {
      control: '-',
      description: 'Callback fired when button loses focus.',
    },
    focusRingStyleTransform: {
      control: '-',
      description: 'Function to transform the focus ring style.',
    },
  }}
  args={{
    id: 'button-id',
    testId: 'test-id',
    variant: 'primary',
    callToAction: false,
    size: 'medium',
    uniformSize: false,
    radius: '--evr-radius-md',
    inverse: false,
    disabled: false,
    type: 'button',
    ariaLabel: undefined,
    ariaLabelledBy: undefined,
    ariaExpanded: undefined,
    ariaControls: undefined,
    ariaOwns: undefined,
    ariaDescribedBy: undefined,
    ariaHasPopup: undefined,
    tabIndex: 0,
    onFocus: action('onFocus'),
    onClick: action('onClick'),
    onKeyDown: action('onKeyDown'),
    onBlur: action('onBlur'),
  }}
/>

# Button

<Examples />

## Live Demo

<Canvas>
  <Story name="ButtonBase">
    {(args) => {
      const backgroundStyle = {
        backgroundColor: '#333',
        height: 'auto',
        width: 'auto',
        padding: '2rem',
      };
      return (
        <div
          style={args.inverse && (args.variant === 'secondary' || args.variant === 'tertiary') ? backgroundStyle : null}
        >
          <ButtonBase {...args}>Click Me</ButtonBase>
        </div>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="ButtonBase" />
