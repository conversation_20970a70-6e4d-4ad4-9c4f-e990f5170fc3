import * as React from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { TButtonRadius, TButtonSize, TButtonType, TButtonVariant } from './types';
import { TAriaExpanded, TAriaHasPopup } from '../../types';

import styles from './button-base.module.scss';

// If you add a new property here, make sure that you also Omit it in all the public components that extend the IButtonBaseProps type.
// Otherwise your new prop will be available in all those components.
export interface IButtonBaseProps {
  id: string;
  testId?: string;
  /** @deprecated `data-testid` is no longer used. `testId` should be used instead.  */
  'data-testid'?: string;
  disabled?: boolean;
  inverse?: boolean;
  /** Makes height and width use the same value while respecting the 'size' property. Use this to make round/square buttons */
  uniformSize?: boolean;
  variant?: TButtonVariant;
  /** @internal `callToAction` is for internal use only */
  callToAction?: boolean;
  radius?: TButtonRadius;
  size?: TButtonSize;
  className?: string;
  type?: TButtonType;
  ariaLabel?: string;
  ariaHasPopup?: TAriaHasPopup;
  ariaExpanded?: TAriaExpanded;
  ariaControls?: string;
  ariaOwns?: string;
  ariaDescribedBy?: string;
  ariaLabelledBy?: string;
  ariaPressed?: boolean;
  tabIndex?: 0 | -1;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLButtonElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLButtonElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLButtonElement>) => void;
  focusRingStyleTransform?: (focusRingStyles: React.CSSProperties) => React.CSSProperties;
}

export const ButtonBase = React.forwardRef<HTMLButtonElement, React.PropsWithChildren<IButtonBaseProps>>(
  (props, ref) => {
    const {
      id,
      'data-testid': dataTestId,
      testId,
      disabled,
      inverse,
      variant = 'primary',
      callToAction,
      radius = '--evr-radius-md',
      uniformSize,
      className,
      children,
      type,
      size = 'medium',
      ariaLabel,
      ariaHasPopup,
      ariaExpanded,
      ariaControls,
      ariaOwns,
      ariaDescribedBy,
      ariaLabelledBy,
      ariaPressed,
      tabIndex = 0,
      onClick,
      onKeyDown,
      onFocus,
      onBlur,
      focusRingStyleTransform,
    } = props;

    const isInverse = inverse && (variant === 'tertiary' || variant === 'secondary');

    return (
      <FocusRing canFocusFromMouse inverse={isInverse} disabled={disabled} styleTransform={focusRingStyleTransform}>
        <button
          id={id}
          style={{ borderRadius: `var(${radius})` }}
          className={classnames(
            styles.evrButtonBase,
            styles[size],
            styles[variant],
            {
              [styles.disabled]: disabled,
              [styles.inverse]: isInverse,
              [styles.uniformSize]: uniformSize,
              [styles.callToAction]: callToAction,
            },
            className
          )}
          type={type}
          disabled={disabled || undefined}
          onClick={onClick}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
          onBlur={onBlur}
          data-testid={testId ? testId : dataTestId}
          ref={ref}
          tabIndex={tabIndex} //tabIndex needed for safari to focus icon buttons in form fields i.e. DateField
          aria-label={ariaLabel}
          aria-expanded={ariaExpanded}
          aria-haspopup={ariaHasPopup}
          aria-controls={ariaControls}
          aria-owns={ariaOwns}
          aria-describedby={ariaDescribedBy}
          aria-labelledby={ariaLabelledBy}
          aria-pressed={ariaPressed}
        >
          {children}
        </button>
      </FocusRing>
    );
  }
);

ButtonBase.displayName = 'ButtonBase';
