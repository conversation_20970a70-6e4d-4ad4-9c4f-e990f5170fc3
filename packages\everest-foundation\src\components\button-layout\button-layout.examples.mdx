import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Button } from '../button';
import { IconButton } from '../icon-button';
import { useState } from 'react';
import { useEverestContext } from '../everest-provider';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { Info } from '../../../.storybook/docs/shared/status-banner';

export const scope = { Button, IconButton, useState, useEverestContext };

<Info>
  There is no dev component for Button layout. There are a number of different variations, with some examples being
  implemented below for convenience.
</Info>

## Guidelines

The **Button layout** guidelines are detailed in [Figma](https://www.figma.com/design/DAqvdjqCEet9gA7n6cBMcy/Button-Group?node-id=117-9187&t=TFWJEI1X2Hb5vw14-0). Button Layout is intended to have between 2 to 4 buttons.

Details on how to use the `Button` and `IconButton` components can be found in their respective docs (<LinkTo kind="Components/Buttons/Button">Button</LinkTo> and <LinkTo kind="Components/Buttons/Icon Button">IconButton</LinkTo>).

## Examples

### Split Button Layout (Responsive)

Switch between horizontal and vertical arrangements based on screen breakpoints from the `useEverestContext` hook provided by <LinkTo kind="Foundations/Everest Provider">EverestProvider</LinkTo>. For screen sizes categorized as extra small (xs) and small (sm), switch to a vertical layout. This ensures that the button layout remains functional and accessible on smaller devices.

export const primaryCode = `() => {
    const { breakpoint } = useEverestContext();
    const isFullscreen = breakpoint === 'xs' || breakpoint === 'sm';

    const styles = {
        container: {
            padding: 'var(--evr-spacing-2xs)',
            width: '70%',
            justifyContent: 'center',
            display: 'flex',
            borderStyle: 'dashed',
            borderWidth: '1px',
            borderColor: 'grey',
        },
        btnGroup: {
            display: 'flex',
            flexDirection: isFullscreen ? 'column' : 'row',
            alignItems: 'stretch',
            gap: 'var(--evr-spacing-2xs)',
            width: '100%',
        },
        buttonLayoutSplitFiller: {
            flexGrow: 1,
            margin: 0,
        },
    };
    return (
        <div style={styles.container}>
            <div style={styles.btnGroup}>
                <Button id='responsive-tertiary-btn' variant='tertiaryNeutral' label='Tertiary Button' />
                {!isFullscreen && (
                    <div style={styles.buttonLayoutSplitFiller}></div>
                )}
                <Button id='responsive-secondary-btn' variant='secondaryNeutral' label='Secondary Button' />
                <Button id='responsive-primary-btn' label='Primary Button' />
            </div>
        </div>
    );

}`;

<CodeExample scope={scope} code={primaryCode} />

### Button Layout with Role (Accessibility)

In some cases, additional context to users with assistive technologies must be provided using the `role` attribute with a value of `group` and the `aria-label` attribute to provide a descriptive label for the entire button group:

- `role` : set to `group`
- `aria-label`: provides a descriptive label for the entire button group.

However, this is not required for all button layouts. For example, OK/Cancel buttons in a `Modal` do not require this `role`.

export const secondaryCode = `() => {
    const styles = {
        btnGroup: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: 'var(--evr-spacing-2xs)'
        }
    };
    return (
        <div 
          id='btn-layout-id'
          style={styles.btnGroup} 
          aria-label='Descriptive label for the group of buttons' 
          role='group'>
            <Button id='example-primary-btn' label='Submit' />
            <Button id='example-secondary-btn' variant='secondaryNeutral' label='Save Draft' />
            <Button id='example-tertiary-btn' variant='tertiaryNeutral' label='Clear' /> 
            <IconButton id="example-tertiary-neutral-square-btn" iconName="questionMark" variant="tertiaryNeutral" ariaLabel="This is an aria-label of the Disabled Tertiary Neutral Square Icon Button" />
        </div>
    );
}`;

<CodeExample scope={scope} code={secondaryCode} />
