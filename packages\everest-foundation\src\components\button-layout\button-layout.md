# Button Layout ("Button Group")

## Summary

Research and document implementations for the Everest Button Layout as guidelines.

- Start Date: 2024-07-12
- Figma link: https://www.figma.com/design/DAqvdjqCEet9gA7n6cBMcy/Button-Group?node-id=0-1&t=27JtKMprfAoMeiIv-0

## Detailed Design

The Button Layout guideline is for the grouping related Everest buttons and actions. The group is designed to accommodate between 2 to 4 buttons.There are multiple configurations that are possible for Button Layout, which are to be exemplified in the Storybook Documentation. Button Layout is wrapped by a `div` with styling.

## Accessibility

Refer to [button.md](../button/button.md) for a complete list of accessibility considerations. For ButtonLayout, the primary considerations are as follows:  
  - Navigation is done with tab key.
  - With every focus on a new button, its label will be read out.
  - When the screen size is xs and sm, switch to mobile layout.
  - Depending on the scenario, the feature team may choose to assign a `role` and `aria-label`. For Button Layout it is advised to use the "group" role, so that upon focus, the screen reader will announce there is a group along with the `aria-label`.

## Alternatives/Trade-Offs

### Component or Guideline Discussion

Initially, we documented the Everest Button Layout as a standalone component. However, given many permutations possible for the Button Layout, we opted for having guidelines for the consumer. In addition, creating a component for Button Layout would not provide much value at this time. Code for users to copy and paste would provide a more practical solution.


### Wrapper Element
For the wrapper element, `nav`, `ul` and `div` were considered. Other design systems make use of one of these three wrapping elements. Ultimately, `div` was chosen for this case. 

`nav` is expected to contain links or navigation elements, which might not always be the use case. 
While `ul` could provide a structured list format, the Button Layout doesn't necessarily represent a list of related items, but rather a group of interactive elements. 

Using a `div` wrapper is the more generic and suitable choice, since use cases will vary for Button Layout.


## Q&A

#### What is the max number of buttons? 
- Four.

#### What happens when someone puts more than 4 buttons? 
- Any unpredicted behaviors are unsupported. It is up to the consumer to use the component within guidelines.

#### How does the split example with 3 buttons work? I.e. how do we know where the other button will be on the page? 
- The split buttons will be on opposite ends of the given container. Split is an optional configuration for 3 buttons. It consists of one button on one end of the container, with the other two buttons on the opposite end. Please see the Figma for a visual.

#### In the case of a split, should the group still be announced as if they are in the same group? 
- They should.

#### Is setting role=group required for Button Layout?
- The group role is not required for Button Layout. In some instances feature teams should avoid doing so. For example, in a modal, where setting the role to group would be redundant.

#### Is right/left arrow key navigation required?
- No. Right and left arrow key navigation is for the Toolbar component. The tab key is to be used for Button Layout.

#### What is the navigation behavior for a button layout with a disabled button? 
- Same behavior as the current Button component, which is to not focus on a disabled button.

#### In what situations can split be applied? In terms of width? Number of buttons? 
- Split should only be applied when there are three buttons and the width is not filled.

#### What is the recommended navigation method through the button layout? 
- The tab key will navigate between buttons.

#### What is the advice for the placement of buttons for consistency across apps? 
- For consistency across apps, align the buttons to the right or left, whichever is most widely used or standard.

#### What is the recommended order of appearance for reading out the buttons? 
- The order of appearance is better for reading out, from top to bottom, left to right. A11y team doesn’t recommend reading it out according to priority.

#### Is it better to announce how many buttons? 
- It’s sufficient to simply label it as a group, without declaring how many buttons are apart of said group.

## Future Considerations

- The support for the `split` feature may be extended to more than just 3 buttons.
- The tertiary button may undergo design changes to make it more obvious that it is clickable. Currently it displays similar to plaintext.
- Documentation for more than 4 buttons may be required
- In the future the need for a React component may arise for a button group. The design of this future button group may be more conventional. Buttons  may be attached to eachother and have a different look than the regular Everest Buttons.

## Example Usage

```
const buttonLayoutStyle = {
    // define styling here
};

<div 
  style={buttonLayoutStyle}
  id="button-layout-id"
>
    <Button id="primary-btn" label="Send" /> 
    <Button id="secondary-btn" variant="secondary" label="Cancel" /> 
    <Button id="tertiary-btn" variant="tertiary" label="Save as Draft" />
</div>
```

## Other Design Systems

### Material UI - https://mui.com/material-ui/api/button-group/
This design system has several props such as width, size, orientation.

### Bootstrap - https://getbootstrap.com/docs/5.0/components/button-group/
This design system has a similar implmentation (div wrapper) to what is layed out for this component.

### Gestalt https://gestalt.pinterest.systems/web/buttongroup
This design system has buttons that are not attached next to eachother like MUI or Bootstrap. They resemble the most like the Everest Buttons.

## Required PBIs

1. Architecture - https://dayforce.atlassian.net/browse/EDS-4454
1. Storybook documentation - https://dayforce.atlassian.net/browse/EDS-4602
1. Testing - https://dayforce.atlassian.net/browse/EDS-4604
1. Pushing to beta - https://dayforce.atlassian.net/browse/EDS-4613
1. Pushing to prod - https://dayforce.atlassian.net/browse/EDS-4610
1. Adding to ref app - https://dayforce.atlassian.net/browse/EDS-4611


## Acceptance Criteria

- Document and setup guidelines in Storybook
- Ensure styles align with design specifications from Figma.
- Ensure enough (diverse) examples are featured in Storybook
- Verify the following:
  - Accessibility (aria attributes, navigation order, screen reader callouts, mouse, and keyboard interactivity)
- Visual tests implemented

## Change Log
