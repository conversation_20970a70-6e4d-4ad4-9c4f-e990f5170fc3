import { Meta, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { Button } from '../button';
import { action } from '@storybook/addon-actions';
import Examples from './button-layout.examples.mdx';
import { ICON_NAMES } from '../icon';

<Meta
  title="Layout/Button Layout"
  component={Button}
  parameters={{
    status: {
      type: 'ready',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/DAqvdjqCEet9gA7n6cBMcy/Button-Group?node-id=175-11279&t=yU8QFjf0XYEfmIqT-0',
    },
  }}

/>

# Button Layout

<Examples />
