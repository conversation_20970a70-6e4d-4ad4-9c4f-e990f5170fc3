import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Button } from './button';

export const scope = { Button };

`Button` components are used when a user needs to take an action. The `Button` component consists of multiple variations to convey different levels of priority for its action.

## Variations

### Primary Button

The primary variant communicates the highest priority level. It is used to direct the user’s attention to the primary action of a user flow, such as completing a task or moving forward in a process.

There should only be one primary button within a user flow. Primary buttons should be used when the action is more important than all other actions and you want to draw attention to it, such as submit or next.

export const primaryCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
            <Button id='primary-btn' label='Primary Button' /> 
            <Button id='primary-btn-disabled' disabled label='Disabled Primary Button' />
        </Row>
    );
}`;

<CodeExample scope={scope} code={primaryCode} />

### Secondary Button

The secondary variant communicates a lower priority level. It is usually used for actions which provide an alternative to the primary action, such as cancel or redo.

There should only be one secondary button within a user flow.

export const secondaryCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            gap: '10px'
        },
        column: {
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            gap: '10px'
        },
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Row>
            <Column>
                <Button id='secondary-btn' variant='secondary' label='Secondary Button' /> 
                <Button id='secondary-neutral-btn' variant='secondaryNeutral' label='Secondary Neutral Button' /> 
            </Column>
            <Column>
                <Button id='secondary-btn-disabled' variant='secondary' disabled label='Disabled Secondary Button' />
                <Button id='secondary-neutral-btn-disabled' variant='secondaryNeutral' disabled label='Disabled Secondary Neutral Button' />
            </Column>
        </Row>
    );
}`;

<CodeExample scope={scope} code={secondaryCode} />

### Tertiary Button

The tertiary variant communicates the lowest priority level. It is used to deprioritize actions which are not directly associated with the primary or secondary buttons in a user flow, such as previous page or back.

export const tertiaryCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            gap: '10px'
        },
        column: {
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            gap: '10px'
        },
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Row>
            <Column>
                <Button id='tertiary-btn' variant='tertiary' label='Tertiary Button' />
                <Button id='tertiary-neutral-btn' variant='tertiaryNeutral' label='Tertiary Neutral Button' />
            </Column>
            <Column>
                <Button id='tertiary-btn-disabled' variant='tertiary' disabled label='Disabled Tertiary Button' />
                <Button id='tertiary-neutral-btn-disabled' variant='tertiaryNeutral' disabled label='Disabled Tertiary Neutral Button' />
            </Column>
        </Row>
    );
}`;

<CodeExample scope={scope} code={tertiaryCode} />

## Buttons with Icons

The `Button` component can have an icon placed at the start, end, or on both sides of its label. These are used to imply the type of action it is performing.

To render an icon at the start of a label, use `startIcon`. To render an icon at the end, use `endIcon`. To render on both sides, use both `startIcon` and `endIcon`.

Buttons should not contain two icons `startIcon` and `endIcon`, without also including a label.

export const buttonWithIconCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            gap: '10px'
        },
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Column>
            <Row>
                <Button id='start-icon-btn' startIcon='chevronLeft' label='Start Icon' />
                <Button id='end-icon-btn' endIcon='chevronRight' label='End Icon' />
                <Button id='start-end-icon-btn' startIcon='chevronLeft' endIcon='chevronRight' label='Icon both sides' />
            </Row>
            <Row>
                <Button variant="secondary" id='start-icon-btn' startIcon='chevronLeft' label='Start Icon' />
                <Button variant="secondary" id='end-icon-btn' endIcon='chevronRight' label='End Icon' />
                <Button variant="secondary" id='start-end-icon-btn' startIcon='chevronLeft' endIcon='chevronRight' label='Icon both sides' />
            </Row>
            <Row>
                <Button variant="tertiary" id='start-icon-btn' startIcon='chevronLeft' label='Start Icon' />
                <Button variant="tertiary" id='end-icon-btn' endIcon='chevronRight' label='End Icon' />
                <Button variant="tertiary" id='start-end-icon-btn' startIcon='chevronLeft' endIcon='chevronRight' label='Icon both sides' />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={buttonWithIconCode} />

## Inverse

The `inverse` prop is used to change the appearance of the Button to be suitable for dark surfaces and backgrounds. This prop only affects the secondary and tertiary buttons.

export const inverseButton = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            gap: '10px'
        },
        column: {
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            gap: '10px'
        },
    };
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
            <Column>
                <Button id="secondary-inverse-btn" variant="secondary" inverse label='Secondary Inverse Button' />
                <Button id="tertiary-inverse-btn" variant="tertiary" inverse label='Tertiary Inverse Button' />
            </Column>
            <Column>
                <Button id="secondary-inverse-btn-disabled" variant="secondary" inverse disabled label='Disabled Secondary Inverse Button' />
                <Button id="tertiary-inverse-btn-disabled" variant="tertiary" inverse disabled label='Disabled Tertiary Inverse Button' />
            </Column>
        </Row>
    );
}`;

<CodeExample scope={scope} code={inverseButton} background={'dark'} />

## Size

The `Button` component comes in three different sizes: `small`, `medium`, `large`, with the default size set to `medium`. Icon buttons are always `medium` size.

export const sizeCode = `() => {
    const styles = {
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            gap: '20px, 10px'
        },
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px',
            marginBottom: '10px',
            alignItems: 'center',
            justifyContent: 'center'
        }
    };
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
    <Column>
        <Row>
            <Button id='small-btn' size='small' label='Button' />
            <Button id='medium-btn' size='medium' label='Button' />
            <Button id='large-btn' size='large' label='Button' />
        </Row>
        <Row>
            <Button id='start-icon-small-btn' startIcon="add" size='small' label='Button' />
            <Button id='start-icon-medium-btn' startIcon="add" size='medium' label='Button' />
            <Button id='start-icon-large-btn' startIcon="add" size='large' label='Button' />
        </Row>
    </Column>
    );
}`;

<CodeExample scope={scope} code={sizeCode} />

## Full Width Button

A Button can be set to take up the full width of its container. This should be used when grouping multiple related Buttons together, as it keeps their widths equal.

export const fullWidthCode = `() => {
    const styles = {
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            rowGap: '30px',
            width: '100%'
        },
        containerVertical: {
            display: 'flex',
            flexDirection: 'column',
            rowGap: '10px',
            width: '50%',
            border: '1px solid grey',
            padding: '4px',
        },
        containerHorizontal: {
            display: 'flex',
            columnGap: '10px',
            border: '1px solid grey',
            padding: '4px',
            width: '50%'
        }
    };
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ContainerVertical = ({ children }) => (
        <div style={styles.containerVertical}>{children}</div>
    );
    const ContainerHorizontal = ({ children }) => (
        <div style={styles.containerHorizontal}>{children}</div>
    );
    return (
        <Column>
            <ContainerVertical>
                <Button id='primary-full-width-btn' label='Primary Action' fullWidth/>
                <Button id='secondary-full-width-btn' variant='secondary' label='Secondary Action' fullWidth/>
            </ContainerVertical>
            <ContainerHorizontal>
                <Button id='primary-full-width-btn-2' label='Primary Action' fullWidth/>
                <Button id='secondary-full-width-btn-2' variant='secondary' label='Secondary Action' fullWidth/>
            </ContainerHorizontal>
        </Column>
    );
}`;

<CodeExample scope={scope} code={fullWidthCode} />

## Accessing Button using ref

Click on the Primary Button to access the Secondary Button, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            columnGap: '0.625rem'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
        <Row>
            <Button id='click-access-el-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
            <Button id='seconary-btn-with-ref' ref={ref} variant='secondary' label='Secondary Button' />
        </Row>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

When using `Button`, avoid long text labels and line breaks. The button content should also be in sentence case; the first letter of the first word is capitalized, and all other letters and words that follow are in lowercase, except for proper nouns. The button should also be concise with a max word count of three.
When grouping multiple `Button` components, there should only be at most one primary button and one secondary button.

## Accessibility

There should always be a clear `aria-label` that communicates the button's purpose.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
