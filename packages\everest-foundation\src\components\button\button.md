﻿2021-11-04 <PERSON>ton Component

**Summary**

Research and document the finding of Everest Button Component regarding its design and architecture overview. It is based on the Figma file: https://www.figma.com/file/JyOoJ0NaQWElyNPpsNE5hP/%F0%9F%A7%AAButtons?node-id=16%3A538

**Designer Q&A**

Question 1: What the mouse hover icon should it be?

Answer:

1. Disable - cursor.not-allow

2. Other state - cursor.pointer

Question 2: Toggle Button

Answer: Future implementation - beta1

Question 3: Max number of icon

Answer: 1

Question 4: Min width, max width, button width or height

Answer:

1. Height – always the same, 32px
1. If no label/icon button, it is not a button.
1. Icon only button – always 32px x 32px
1. Only one line – any double line content (future implementation)
1. Overflow should be hidden and using ellipse for hidden content
1. Default
   1. Width of the button is based on the content – auto width
1. Full Width
   1. Width always based on the parent container
   1. Handle group of buttons (same size)
   1. Parent container can use flexbox to adjust the button alignment and location
   1. Use container to control the button group.
   1. **Consider adding size attribute to limit option**

Question 5: states (combination states, when button is disabled and hover)

Answer: For now, disabled button is not focusable (We should change this soon, this is against the accessibility)

**APIs**

We are using restricted approach only on the React Side. Only exposed APIs which is inline with Design. The button we are creating is not intended for Form purpose, any Form related APIs will be removed till we decided to support it in the future (button type always = button)

autofocus

label

variant

ariaLable

disabled

fullWidth

iconName

iconPlacement

all other attributes (forms and aria)

<https://github.com/microsoft/fast/blob/master/packages/web-components/fast-foundation/src/button/button.template.ts>

all related events

**Detail Design**

Using attribute for Label, icon and iconPlacement, design related attribute must be fully controlled by our component. (Remove the flexibility to achieve design system consistency)

**Other Design System**

Material design - all the different classes

Fast component - very flexible, using slot for start, end and even the slot itself

Base, Atlassian - iconAfter, iconBefore, startEnhancer, endEnhancer,

Carbon - use variant to define icon.

**Unresolved questions**

Dir: **LTR or RTL - need more research on this.** - need a spike PBI for this

- Start using “start” “end” instead of “left” and “right”

**TODO:**

- Research required for LTR or RTL
- WIKI to record fast feedback
- Create ticket to revisit restricting fast component to (Design System) standard OR continue on the fast-element which has already restricted. (depend on the efforts)

## Changelog

- 08/27/2024: Updated button docs ([**EDS-4512**](https://dayforce.atlassian.net/browse/EDS-4512))
- 01/12/2024: [WC Conversion] Replace Button with ButtonV2 ([**EDS-3231**](https://ceridian.atlassian.net/browse/EDS-3231))
- 10/03/2024: Button redesign ([**EDS-4723**](https://dayforce.atlassian.net/browse/EDS-4723))
