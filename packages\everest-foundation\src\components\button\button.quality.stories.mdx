import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Button } from './button';
import { screen } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Button"
  component={Button}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  argTypes={{
    variant: {
      type: 'enum',
      control: 'select',
      options: ['primary', 'secondary', 'tertiary', 'secondaryNeutral', 'tertiaryNeutral'],
      description: 'Changes the appearance of the button. Each variant is associated with a different priority.',
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
  }}
  args={{
    id: 'button-id',
    testId: 'test-id',
    disabled: false,
    fullWidth: false,
    label: 'Button',
    variant: 'primary',
    ariaLabel: '',
  }}
/>

# Button

## Live Demo

<Canvas>
  <Story name="Primary">{(args) => <Button {...args} label="Primary Button" />}</Story>
</Canvas>

<Canvas>
  <Story
    name="Primary with focus ring"
    decorators={[ChromaticDecorators.padStory]}
    play={() => {
      screen.getByRole('button').focus();
    }}
  >
    {(args) => <Button {...args} label="Primary Button" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Secondary">{(args) => <Button {...args} variant="secondary" label="Secondary Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Tertiary">{(args) => <Button {...args} variant="tertiary" label="Tertiary Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Secondary Neutral">
    {(args) => <Button {...args} variant="secondaryNeutral" label="Secondary Neutral Button" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Tertiary Neutral">
    {(args) => <Button {...args} variant="tertiaryNeutral" label="Tertiary Neutral Button" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Primary Disabled">{(args) => <Button {...args} disabled label="Disabled Primary Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Secondary Disabled">
    {(args) => <Button {...args} variant="secondary" disabled label="Disabled Secondary Button" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Tertiary Disabled">
    {(args) => <Button {...args} variant="tertiary" disabled label="Disabled Tertiary Button" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Secondary Neutral Disabled">
    {(args) => <Button {...args} variant="secondaryNeutral" disabled label="Disabled Secondary Neutral Button" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Tertiary Neutral Disabled">
    {(args) => <Button {...args} variant="tertiaryNeutral" disabled label="Disabled Tertiary Neutral Button" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="StartIcon">{(args) => <Button {...args} startIcon="chevronLeft" label="Starting Icon" />}</Story>
</Canvas>

<Canvas>
  <Story name="EndIcon">{(args) => <Button {...args} endIcon="chevronRight" label="Ending Icon" />}</Story>
</Canvas>

<Canvas>
  <Story name="Start & EndIcon">
    {(args) => <Button {...args} startIcon="chevronLeft" endIcon="chevronRight" label="Icon both sides" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Small">{(args) => <Button {...args} size="small" label="Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Medium">{(args) => <Button {...args} size="medium" label="Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Large">{(args) => <Button {...args} size="large" label="Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Small startIcon">{(args) => <Button {...args} startIcon="add" size="small" label="Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Medium startIcon">{(args) => <Button {...args} startIcon="add" size="medium" label="Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Large startIcon">{(args) => <Button {...args} startIcon="add" size="large" label="Button" />}</Story>
</Canvas>

<Canvas>
  <Story name="Primary FullWidth">{(args) => <Button {...args} label="Primary Action" fullWidth />}</Story>
</Canvas>

<Canvas>
  <Story name="Secondary FullWidth">
    {(args) => <Button {...args} variant="secondary" label="Secondary Action" fullWidth />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Secondary Inverse">
    {(args) => (
      <div style={{ backgroundColor: 'black', height: '5rem', alignItems: 'center', display: 'flex' }}>
        <Button
          {...args}
          variant="secondary"
          startIcon="chevronLeft"
          endIcon="chevronRight"
          label="Secondary Inverse"
          inverse
        />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Secondary Inverse Disabled">
    {(args) => (
      <div style={{ backgroundColor: 'black', height: '5rem', alignItems: 'center', display: 'flex' }}>
        <Button
          {...args}
          variant="secondary"
          startIcon="chevronLeft"
          endIcon="chevronRight"
          label="Secondary Inverse Disabled"
          inverse
          disabled
        />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Tertiary Inverse">
    {(args) => (
      <div style={{ backgroundColor: 'black', height: '5rem', alignItems: 'center', display: 'flex' }}>
        <Button
          {...args}
          variant="tertiary"
          startIcon="chevronLeft"
          endIcon="chevronRight"
          label="Tertiary Inverse"
          inverse
        />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Tertiary Inverse Disabled">
    {(args) => (
      <div style={{ backgroundColor: 'black', height: '5rem', alignItems: 'center', display: 'flex' }}>
        <Button
          {...args}
          variant="tertiary"
          startIcon="chevronLeft"
          endIcon="chevronRight"
          label="Tertiary Inverse Disabled"
          inverse
          disabled
        />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Button With Tall Sibling Element">
    {(args) => (
      <div style={{ width: '18.75rem', justifyContent: 'space-between', display: 'flex' }}>
        <h4 className="evrHeading4">
          Mount Everest Mount Everest Mount Everest Mount Everest Mount Everest Mount Everest
        </h4>
        <Button {...args} variant="primary" label="Button" />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Small Button With Long Text And Icons">
    {(args) => (
      <div style={{ display: 'flex' }}>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="primary"
            size="small"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="secondary"
            size="small"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="tertiary"
            size="small"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Medium Button With Long Text And Icons">
    {(args) => (
      <div style={{ display: 'flex' }}>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="primary"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="secondary"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="tertiary"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Large Button With Long Text And Icons">
    {(args) => (
      <div style={{ display: 'flex' }}>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="primary"
            size="large"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="secondary"
            size="large"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
        <div style={{ width: '10rem' }}>
          <Button
            {...args}
            variant="tertiary"
            size="large"
            label="Long Button Text Here"
            startIcon="chevronLeft"
            endIcon="chevronRight"
          />
        </div>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => (
      <>
        <Button {...args} id="enabled-id" testId="enabled-test-id" label="enabled" />
        <Button {...args} id="disabled-id" testId="disabled-test-id" label="disabled" disabled={true} />
        <Button {...args} id="secondary-id" testId="secondary-test-id" label="secondary" variant="secondary" />
      </>
    )}
  </Story>
</Canvas>
