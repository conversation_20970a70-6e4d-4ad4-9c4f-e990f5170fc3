import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Button } from './button';

describe('[Button]', () => {
  const id = 'button-id';
  const label = 'label content';
  const ariaLabelledBy = 'aria-labelledby content';
  const ariaDescribedBy = 'aria-describedby content';
  const onClick = jest.fn();
  const onKeyDown = jest.fn();
  const onFocus = jest.fn();
  const onBlur = jest.fn();

  const props = {
    label,
    id,
    onClick,
  };

  const getButton = () => screen.getByRole('button');

  beforeEach(jest.clearAllMocks);

  it('should have disabled attribute, when disabled prop is set to true', async () => {
    render(<Button {...props} disabled={true} />);
    expect(getButton()).toHaveAttribute('disabled', '');
    expect(getButton()).not.toHaveAttribute('aria-disabled');
  });

  it('should render aria-labelledby when value is set', async () => {
    render(<Button {...props} ariaLabelledBy={ariaLabelledBy} />);
    expect(getButton()).toHaveAttribute('aria-labelledby', ariaLabelledBy);
  });

  it('should render aria-describedby when value is set', async () => {
    render(<Button {...props} ariaDescribedBy={ariaDescribedBy} />);
    expect(getButton()).toHaveAttribute('aria-describedby', ariaDescribedBy);
  });

  it('should render fullWidth', async () => {
    render(<Button {...props} fullWidth={true} />);
    expect(getButton()).toHaveClass('fullWidth');
  });

  it('dispatch a click event by mouse click', async () => {
    render(<Button {...props} />);

    await userEvent.click(getButton());
    expect(onClick).toHaveBeenCalledTimes(1);
  });
  // todo: click event should not dispatch when using key 'a', it triggered on unittest, but not storybook.
  it('dispatch a click event by Keyboard Enter key', async () => {
    render(<Button {...props} />);

    await userEvent.tab();
    await userEvent.keyboard('[Enter]');
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('dispatch a click event by Keyboard Space key', async () => {
    render(<Button {...props} />);

    await userEvent.tab();
    await userEvent.keyboard('[Space]');
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('dispatch a onKeyDown event by Keyboard Space key', async () => {
    render(<Button {...props} onKeyDown={onKeyDown} />);

    await userEvent.tab();
    await userEvent.keyboard('[Space]');
    expect(onKeyDown).toHaveBeenCalledTimes(1);
  });

  it('dispatch onFocus event when button gets focus', async () => {
    render(<Button {...props} onFocus={onFocus} />);

    await userEvent.tab();
    expect(onFocus).toHaveBeenCalledTimes(1);
  });

  it('dispatch onBlur event when button gets focus', async () => {
    render(<Button {...props} onBlur={onBlur} />);

    await userEvent.tab();
    await userEvent.tab();
    expect(onBlur).toHaveBeenCalledTimes(1);
  });
});
