import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Button } from './button';
import Examples from './button.examples.mdx';
import { action } from '@storybook/addon-actions';
import { ICON_NAMES } from '../icon';

<Meta
  title="Components/Buttons/Button"
  component={Button}
  parameters={{
    status: {
      type: 'ready',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/Everest-Web?type=design&node-id=3157-10020&mode=design&t=ksQE5zczOQp9deBj-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    label: {
      type: 'string',
      description: "A concise label that communicates the Button's purpose.",
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    variant: {
      type: 'enum',
      control: 'select',
      options: ['primary', 'secondary', 'tertiary', 'secondaryNeutral', 'tertiaryNeutral'],
      description: 'Changes the appearance of the button. Each variant is associated with a different priority.',
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    size: {
      type: 'enum',
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'Changes size of button.',
      table: {
        defaultValue: { summary: 'medium' },
      },
    },
    startIcon: {
      type: 'enum',
      control: 'select',
      options: [undefined, ...ICON_NAMES.sort()],
      description: 'Changes the start icon used.',
    },
    endIcon: {
      type: 'enum',
      control: 'select',
      options: [undefined, ...ICON_NAMES.sort()],
      description: 'Changes the end icon used.',
    },
    type: {
      type: 'enum',
      control: 'select',
      options: ['submit', 'button', 'reset'],
      description: 'Sets the type of the button.',
    },
    inverse: {
      type: 'boolean',
      description:
        'Changes the appearance of the button to be used on dark surfaces. Affects the `Secondary Button` and `Tertiary Button` only.',
      table: {
        defaultValue: { summary: false },
      },
    },
    fullWidth: {
      type: 'boolean',
      description: 'Expands the Button to 100% width.',
      table: {
        defaultValue: { summary: false },
      },
    },
    disabled: {
      type: 'boolean',
      description: 'Disables the Button to prevent any user action.',
      table: {
        defaultValue: { summary: false },
      },
    },
    ariaLabel: {
      type: 'string',
      control: 'text',
      description: "Required for accessibility. A clear description of the Button's purpose.",
    },
    ariaLabelledBy: {
      type: 'string',
      control: 'text',
      description: 'Identifies the element (or elements) that labels the element it is applied to.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: 'text',
      description: ' Identifies the element (or elements) that describes the element on which the attribute is set.',
    },
    ariaExpanded: {
      type: 'boolean',
      description: 'Indicates whether the controlled element is expanded or collapsed.',
    },
    ariaControls: {
      type: 'string',
      description: 'Identifies the element(s) whose contents or presence are controlled by the button.',
    },
    ariaOwns: {
      type: 'string',
      description: 'Identifies the element(s) whose contents or presence are owned by the button.',
    },
    onClick: {
      control: '-',
      description: 'Optional callback fired when user clicks on the button.',
    },
    onKeyDown: {
      control: '-',
      description: 'Optional callback fired when user presses a key when button is focused.',
    },
    onFocus: {
      control: '-',
      description: 'Optional callback fired when button receives focus.',
    },
    onBlur: {
      control: '-',
      description: 'Optional callback fired when button loses focus.',
    },
    callToAction: {
      table: { disable: true }, // hide in Storybook
    },
  }}
  args={{
    id: 'button-id',
    label: 'Button',
    testId: 'test-id',
    variant: 'primary',
    size: 'medium',
    startIcon: undefined,
    endIcon: undefined,
    inverse: false,
    disabled: false,
    type: 'button',
    fullWidth: false,
    ariaLabel: undefined,
    ariaLabelledBy: undefined,
    ariaExpanded: undefined,
    ariaControls: undefined,
    ariaDescribedBy: undefined,
    ariaHasPopup: undefined,
    onFocus: action('onFocus'),
    onClick: action('onClick'),
    onKeyDown: action('onKeyDown'),
    onBlur: action('onBlur'),
  }}
/>

# Button

<Examples />

## Live Demo

<Canvas>
  <Story name="Button">
    {(args) => {
      const backgroundStyle = {
        backgroundColor: '#333',
        height: 'auto',
        width: 'auto',
        padding: '2rem',
      };
      return (
        <div
          style={args.inverse && (args.variant === 'secondary' || args.variant === 'tertiary') ? backgroundStyle : null}
        >
          <Button {...args} onClick={action('onClick')} />
        </div>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Button" />
