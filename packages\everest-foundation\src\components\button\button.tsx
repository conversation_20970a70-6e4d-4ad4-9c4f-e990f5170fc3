import React from 'react';

import { ButtonBase, IButtonBaseProps } from '../button-base';
import { Icon, TIconName } from '../icon';

import styles from './button.module.scss';

export interface IButtonProps
  extends Omit<
    IButtonBaseProps,
    'className' | 'children' | 'tabIndex' | 'radius' | 'uniformSize' | 'ariaPressed' | 'focusRingStyleTransform'
  > {
  label: string;
  fullWidth?: boolean;
  startIcon?: TIconName;
  endIcon?: TIconName;
}

export const Button = React.forwardRef<HTMLButtonElement, IButtonProps>((props: IButtonProps, ref) => {
  const { fullWidth, startIcon, endIcon, label, ...rest } = props;

  return (
    <ButtonBase ref={ref} className={fullWidth ? styles.fullWidth : ''} callToAction {...rest}>
      {startIcon && <Icon name={startIcon} size="md" />}
      <span>{label}</span>
      {endIcon && endIcon !== startIcon && <Icon name={endIcon} size="md" />}
    </ButtonBase>
  );
});

Button.displayName = 'Button';
