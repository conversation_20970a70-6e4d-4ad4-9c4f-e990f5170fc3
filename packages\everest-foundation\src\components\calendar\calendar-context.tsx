import React from 'react';

import { TCalendarView, TDate } from '../../types';
import { addYears, startOfDay, subYears } from '../../utils/date-utils';

export interface ICalendarContext {
  previousViewButtonRef: React.RefObject<HTMLButtonElement>;
  previousMonthButtonRef: React.RefObject<HTMLButtonElement>;
  nextMonthButtonRef: React.RefObject<HTMLButtonElement>;
  disableDefaultToday?: boolean;
  displayDate: TDate;
  setDisplayDate: (date: TDate) => void;
  minDate: TDate;
  maxDate: TDate;
  focusedDate?: TDate | null;
  setFocusedDate: (date: TDate) => void;
  view: TCalendarView;
  isFocusWithinComponent?: boolean;
  formatDateForSR: (view: TCalendarView, value?: TDate) => string;
}

const defaultContext = {
  previousViewButtonRef: React.createRef<HTMLButtonElement>(),
  previousMonthButtonRef: React.createRef<HTMLButtonElement>(),
  nextMonthButtonRef: React.createRef<HTMLButtonElement>(),
  disableDefaultToday: false,
  displayDate: startOfDay(new Date()),
  setDisplayDate: () => undefined,
  minDate: subYears(startOfDay(new Date()), 100),
  maxDate: addYears(startOfDay(new Date()), 100),
  focusedDate: undefined,
  setFocusedDate: () => undefined,
  view: 'day' as TCalendarView,
  isFocusWithinComponent: true,
  formatDateForSR: () => '',
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const CalendarContext = React.createContext<ICalendarContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  CalendarContext.displayName = 'CalendarContext';
}
