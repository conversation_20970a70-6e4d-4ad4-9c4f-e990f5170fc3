import { IFormattedWeekday, IWeekday, TCalendarView, TDate, TStartDayOfWeekRange } from '../../types';
import {
  addDays,
  addMonths,
  endOfMonth,
  getDay,
  getMonth,
  getYear,
  isAfter,
  isBefore,
  isSameDay,
  isSameYear,
  isValid,
  startOfDay,
  startOfMonth,
  startOfWeek,
  subMonths,
} from '../../utils/date-utils';

export interface FindClosestDateParams {
  date: TDate;
  maxDate: TDate;
  minDate: TDate;
  isDateDisabled: (date: TDate) => boolean;
}

/** Returns the day of the week for the given date. Sunday is 0 by default. */
export const getDayOfWeek = (date: TDate, weekdays: IFormattedWeekday[]): number => {
  const position = getDay(date);
  return weekdays.findIndex((day) => day.dayOfWeek === position);
};

/** Returns next month */
export const getNextMonth = (date: TDate): TDate => {
  return addMonths(date, 1);
};

/** Returns previous month */
export const getPreviousMonth = (date: TDate): TDate => {
  return subMonths(date, 1);
};

/** Returns month & year for header in day view */
export const getMonthYear = (months: string[], date: TDate): string => {
  const month = months[getMonth(date)];
  const year = getYear(date);
  return `${month} ${year}`;
};

/** Returns a list of years */
export const getYearRange = (minDate: TDate, maxDate: TDate): number[] => {
  const years = [];
  const minYear = getYear(minDate);
  const maxYear = getYear(maxDate);

  for (let i = minYear; i < maxYear + 1; i++) {
    years.push(i);
  }
  return years;
};

/** Returns whether the given year is a leap year or not. */
export const isLeapYear = (year: number): boolean => {
  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
};

/** Returns min and max date constraints */
export const getDateConstraints = (value: TDate, minDate: TDate, maxDate: TDate): TDate => {
  if (isBefore(value, minDate)) {
    return minDate;
  }
  if (isAfter(value, maxDate)) {
    return maxDate;
  }
  return startOfDay(value);
};

/** Returns weekdays in formatted order based on startDayOfWeek. */
export const getFormattedWeekdays = (startDayOfWeek: number, weekdays: IWeekday[]): IFormattedWeekday[] => {
  const formattedWeekdays = [];
  for (let i = 0; i < weekdays.length; i++) {
    const newIndex = (i + weekdays.length - startDayOfWeek) % weekdays.length;
    const item = { dayOfWeek: i, short: weekdays[i].short, long: weekdays[i].long };
    formattedWeekdays[newIndex] = item;
  }
  return formattedWeekdays;
};

/**
 * Returns an array of dates in the week index counted from the provided start date, or the first visible date if not given.
 * The returned array always has 7 elements, but may include null if the date does not exist according to the calendar system.
 */
export const getDatesInWeek = (
  weekIndex: number,
  weekdays: IFormattedWeekday[],
  startDayOfWeek: TStartDayOfWeekRange,
  displayDate: TDate
): (TDate | null)[] => {
  const datesInWeek = [];
  const startDateOfWeek = addDays(startOfMonth(displayDate), 7 * weekIndex);
  let date = startOfWeek(startDateOfWeek, { weekStartsOn: startDayOfWeek });
  const dayOfWeek = getDayOfWeek(date, weekdays);

  for (let i = 0; i < dayOfWeek; i++) {
    datesInWeek.push(null);
  }
  while (datesInWeek.length < weekdays.length) {
    datesInWeek.push(date);
    const nextDate = addDays(date, 1);
    if (isSameDay(date, nextDate)) {
      break;
    }
    date = nextDate;
  }
  // Null check
  while (datesInWeek.length < weekdays.length) {
    datesInWeek.push(null);
  }
  return datesInWeek;
};

/** Returns rows of dates in month */
export const getSplitRows = (input: (number | string)[], perRow: number): (string | number)[][] => {
  return Array.from({ length: Math.ceil(input.length / perRow) }, (v, i) =>
    input.slice(i * perRow, i * perRow + perRow)
  );
};

/** Returns title displayed in header */
export const formatHeaderTitle = (
  view: TCalendarView,
  displayDate: TDate,
  months: string[],
  minDate: TDate,
  maxDate: TDate
): string => {
  switch (view) {
    case 'day':
      return `${getMonthYear(months, displayDate)}`;
    case 'month':
      return `${getYear(displayDate)}`;
    case 'year':
      if (isSameYear(minDate, maxDate)) return `${getYear(minDate)}`;
      return `${getYear(minDate)} - ${getYear(maxDate)}`; // TODO: Globalize this
  }
};

/** Returns closest enabled date */
export const findClosestEnabledDate = ({
  date,
  maxDate,
  minDate,
  isDateDisabled,
}: FindClosestDateParams): TDate | null => {
  let forward: TDate | null = date;
  let backward: TDate | null = date;
  const newFocusedDay = startOfDay(date);

  // Set min/max bounds
  if (isBefore(newFocusedDay, minDate)) {
    if (backward) {
      backward = new Date(minDate);
    }
    forward = null;
  }
  if (isAfter(newFocusedDay, maxDate)) {
    if (forward) {
      forward = new Date(maxDate);
    }
    backward = null;
  }

  while (forward || backward) {
    if (backward && isAfter(backward, maxDate)) {
      backward = null;
    }
    if (forward && isBefore(forward, minDate)) {
      forward = null;
    }
    if (backward) {
      if (!isDateDisabled(backward)) {
        return backward;
      }
      backward = addDays(backward, -1);
    }
    if (forward) {
      if (!isDateDisabled(forward)) {
        return forward;
      }
      forward = addDays(forward, 1);
    }
  }
  return null;
};

/** Disabled Day Validation */
export const validateDay = (value: TDate, minDate: TDate, maxDate: TDate): string | null => {
  switch (true) {
    case !isValid(value):
      return 'invalidDate';

    case Boolean(minDate && isBefore(value, minDate)):
      return 'minDate';

    case Boolean(maxDate && isAfter(value, maxDate)):
      return 'maxDate';

    default:
      return null;
  }
};

/** Disabled Month Validation */
export const validateMonth = (value: TDate, minDate: TDate, maxDate: TDate): string | null => {
  switch (true) {
    case !isValid(value):
      return 'invalidDate';

    case Boolean(minDate && isBefore(endOfMonth(value), minDate)):
      return 'minDate';

    case Boolean(maxDate && isAfter(startOfMonth(value), maxDate)):
      return 'maxDate';

    default:
      return null;
  }
};

export const checkIsDateDisabled = (
  view: TCalendarView,
  minDate: TDate,
  maxDate: TDate
): ((day: TDate | null) => boolean) => {
  return (day: TDate | null) => {
    if (day && view === 'day') {
      return validateDay(day, minDate, maxDate) !== null;
    }
    if (day && view === 'month') {
      return validateMonth(day, minDate, maxDate) !== null;
    }
    return false;
  };
};
