import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Calendar } from '../calendar';
import { addMonths, getDate, getDay, getMonth, setDate } from '../../utils/date-utils';

export const scope = { Calendar, addMonths, getDate, getDay, getMonth, setDate };

## Basic Usage

Calendar lets users select a date. It is used with DateField to create the DatePicker component.

export const defaultCode = `
() => {
  const styles = {
    container: {
      width: '320px'
    }
  };
  const locale = 'en-US';
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: 'choose date',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const weekdays = 
    [{ short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' }];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const [value, setValue] = React.useState();
  const handleChange = (value, index) => {
    setValue(value);
  };
  return (
    <div style={styles.container}>
      <Calendar
        id="calendar-id"
        value={value}
        minDate={minDate}
        maxDate={maxDate}
        onValueChange={handleChange}
        weekdays={weekdays}
        months={months}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
      />
    </div>
   )
}`;

<CodeExample scope={scope} code={defaultCode} />

## Views

Calendar is composed of sub-components (views): year, month, and day.

export const viewsCode = `
() => {
  const styles = {
    row: { 
      display: 'flex',
    },
    container: {
      width: '320px',
    }
  };
  const locale = 'en-US';
 const weekdays = 
    [{ short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' }];  
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: 'choose date',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const defaultProps = { 
    minDate,
    maxDate, 
    value,
    handleChange,
    weekdays,
    months,
    formatDateForSR,
    textMap
  };
  const [value, setValue] = React.useState();
  const handleChange = (value, index) => {
    setValue(value);
  };
  return (
    <div style={styles.row}>
        <div style={styles.container}>
            <Calendar
            {...defaultProps}
            id="year-calendar-id"
            defaultView={'year'}
            />
        </div>
        <div style={styles.container}>
            <Calendar
            {...defaultProps}
            id="month-calendar-id"
            defaultView={'month'}
        />
        </div>
        <div style={styles.container}>
            <Calendar
            {...defaultProps}
            id="day-calendar-id"
            defaultView={'day'}
        />
        </div>
      </div>
   )
}`;

<CodeExample scope={scope} code={viewsCode} />

## Customization

### Start Day of Week

To customize the start day of the week, set `startDayOfWeek` to any value between 0 to 6 where 0 corresponds to Sunday.

- 0 --> Sunday
- 1 --> Monday
- 2 --> Tuesday
- 3 --> Wednesday
- 4 --> Thursday
- 5 --> Friday
- 6 --> Saturday

Here's an example of a calendar where the week begins with Tuesday:

export const startDayOfWeekCode = `
() => {
  const styles = {
    container: {
      width: '320px'
    }
  };
  const locale = 'en-US';
 const weekdays = 
    [{ short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' }];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: 'choose date',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const [value, setValue] = React.useState();
  const handleChange = (value, index) => {
    setValue(value);
  };
  return (
    <div style={styles.container}>
      <Calendar
        id="start-day-week-calendar-id"
        value={value}
        minDate={minDate}
        maxDate={maxDate}
        onValueChange={handleChange}
        startDayOfWeek={2} // Tuesday
        weekdays={weekdays}
        months={months}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
      />
    </div>
   )
}`;

<CodeExample scope={scope} code={startDayOfWeekCode} />

### Default View

To change what view calendar opens on by default, set `defaultView` to: day, month, or year.

Here's an example of a calendar with default view of month:

export const defaultViewCode = `
() => {
  const styles = {
    container: {
      width: '320px'
    }
  };
  const locale = 'en-US';
 const weekdays = 
    [{ short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' }];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: 'choose date',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const [value, setValue] = React.useState();
  const handleChange = (value, index) => {
    setValue(value);
  };
  return (
    <div style={styles.container}>
      <Calendar
        id="default-view-calendar-id"
        defaultView={'month'}
        value={value}
        minDate={minDate}
        maxDate={maxDate}
        onValueChange={handleChange}
        weekdays={weekdays}
        months={months}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
      />
    </div>
   )
}`;

<CodeExample scope={scope} code={defaultViewCode} />

## Calendar Border Variants

To change the border variant of the calendar, set `borderVariant` to one of the following values:

- `none`
- `inner-elevated`
- `inner-flat`
- `outer-elevated`
- `outer-flat`

These values are enumerated in the `TCalendarBorderVariant` type.

Here's an example of the various border variants:

export const calendarBorderVariantsCode = `() => {
  const styles = {
    container: {
      width: '320px',
    },
    headerColumn: {
      fontWeight: 'bold',
      textAlign: 'center',
    },
    table: {
      width: '100%',
      borderCollapse: 'collapse',
    },
    tr: {
      display: 'flex',
      flex: '1',
      gap: '10px',
      padding: '5px',
    },
    td: {
      border: 'none',
      textAlign: 'center',
    },
  };
  const locale = 'en-US';
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: 'choose date',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const today = new Date();
  const weekdays = [
    { short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' },
  ];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const [value, setValue] = React.useState();
  const onValueChange = (value, index) => {
    setValue(value);
  };
  const defaultProps = {
    minDate,
    maxDate,
    value,
    onValueChange,
    weekdays,
    months,
    formatDateForSR,
    textMap,
  };
  return (
    <table style={styles.table}>
      <tr style={styles.tr}>
        <td style={styles.td}>
          <div style={styles.headerColumn}>No Border</div>
          <div style={styles.container}>
            <Calendar {...defaultProps} id="none-calendar-id" borderVariant={'none'} />
          </div>
        </td>
        <td style={styles.td}>
          <div style={styles.headerColumn}>Inner Border (Elevated)</div>
          <div style={styles.container}>
            <Calendar {...defaultProps} id="inner-elevated-calendar-id" borderVariant={'inner-elevated'} />
          </div>
        </td>
        <td style={styles.td}>
          <div style={styles.headerColumn}>Inner Border (Flat)</div>
          <div style={styles.container}>
            <Calendar {...defaultProps} id="inner-flat-calendar-id" borderVariant={'inner-flat'} />
          </div>
        </td>
      </tr>
      <tr style={styles.tr}>
        <td style={styles.td}>
          <div style={styles.headerColumn}>Outer Border (Elevated) [Default]</div>
          <div style={styles.container}>
            <Calendar {...defaultProps} id="outer-elevated-calendar-id" borderVariant={'outer-elevated'} />
          </div>
        </td>
        <td style={styles.td}>
          <div style={styles.headerColumn}>Outer Border (Flat)</div>
          <div style={styles.container}>
            <Calendar {...defaultProps} id="outer-flat-calendar-id" borderVariant={'outer-flat'} />
          </div>
        </td>
        <td style={styles.td}></td>
      </tr>
    </table>
  );
}`;

<CodeExample scope={scope} code={calendarBorderVariantsCode} />

## Specific Dates Icons

To add icons to specific dates, pass an array of objects to the `calendarEvents` prop. Each object should contain a date and an array of icons.

`calendarEvents: ICalendarEvent[]`

The date should be in the format of a Date object, and the icon should have the Everest `Icon` props.

When a date has more than 2 icons, only the first icon will be displayed and the sum of the remaining icons will be shown next to it with a plus (+) prefix.

Here's an example of a calendar with icons on specific dates:

export const calendarEventsCode = `
() => {
  const styles = {
    container: {
      width: '320px'
    }
  };
  const locale = 'en-US';
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: 'choose date',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const today = new Date();
  const weekdays = 
    [{ short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' }];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const calendarEvents= [
    { 
     eventDate: new Date().setDate(2), 
     eventIcons: [
      { id: 'shift-icon', name: 'timeFilled', fill: '--evr-interactive-primary-default' }, 
      {name: 'globe' }]
    },
    { 
     eventDate: new Date().setDate(3), 
     eventIcons: [
      {name: 'arrowRight' }]
    },
    { 
     eventDate: new Date().setDate(4), 
     eventIcons: [
      {name: 'arrowRight' }, { name: 'timeFilled', fill: '--evr-interactive-primary-default' }]
    },
    {
      eventDate: new Date(),
      eventIcons: [
        { id: 'shift-icon', name: 'alert', fill: '--evr-interactive-status-warning-default' },
        { name: 'globe' },
        { name: 'arrowDown', fill: '--evr-interactive-primary-default' }
      ]
    }
  ];
  const [value, setValue] = React.useState();
  const handleChange = (value, index) => {
    setValue(value);
  };
  return (
    <div style={styles.container}>
      <Calendar
        id="calendar-event-id"
        value={value}
        minDate={minDate}
        maxDate={maxDate}
        onValueChange={handleChange}
        weekdays={weekdays}
        months={months}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
        calendarEvents={calendarEvents}
      />
    </div>
   )
}`;

<CodeExample scope={scope} code={calendarEventsCode} />

## Globalization

Calendar does not handle globalization internally within the component.

Globalized values for the following props should be passed in:

- `startDayOfWeek`
- `weekdays`
- `months`
- `textMap`

### Spain | 'es-ES'

Here's an example of a calendar using localized values from the Javascript native object Intl for Spain's locale.

The week begins on a Monday.

export const spainCalendarCode = `
() => {
  const styles = {
    container: {
      width: '320px'
    }
  };
  const locale = 'es-ES';
  const shortLabelLength = 'short';
  const longLabelLength = 'long';
  const getWeekdays = (locale) => {
    let weekdays = [];
    let newDate = new Date();
    for (let i = 0; i < 7; i++) {
      const date = setDate(newDate, getDate(newDate) + i - getDay(newDate));
      const shortLabel = new Intl.DateTimeFormat(locale, { weekday: shortLabelLength }).format(date);
      const longLabel = new Intl.DateTimeFormat(locale, { weekday: longLabelLength }).format(date);
      const day = { short: shortLabel, long: longLabel };
      weekdays.push(day);
    }
    return weekdays;
  };
  const getMonths = (locale) => {
    let months = [];
    let newDate = new Date();
    const formatter = new Intl.DateTimeFormat(locale, {
      month: shortLabelLength,
    }).format;
    for (let i = 0; i < 12; i++) {
      const month = formatter(addMonths(newDate, i - getMonth(newDate)));
      months.push(month);
    }
    return months;
  };
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: 'Elegir fecha',
    nextMonthButtonLabel: 'Próximo mes',
    previousMonthButtonLabel: 'Mes anterior',
    previousViewIsYearLabel: 'Ir a vista de año',
    previousViewIsMonthLabel: 'Ir a vista de mes',
  };
  const [value, setValue] = React.useState();
  const handleChange = (value, index) => {
    setValue(value);
  };
  return (
    <div style={styles.container}>
      <Calendar
        id="spain-calendar-id"
        value={value}
        minDate={minDate}
        maxDate={maxDate}
        onValueChange={handleChange}
        startDayOfWeek={1} // Monday
        weekdays={getWeekdays(locale)}
        months={getMonths(locale)}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
      />
    </div>
   )
}`;

<CodeExample scope={scope} code={spainCalendarCode} />

### Korea | 'ko-KR'

Here's an example of a calendar using localized values from the Javascript native object Intl for Korea's locale.

The week begins on a Sunday.

export const koreaCalendarCode = `
() => {
  const styles = {
    container: {
      width: '320px'
    }
  };
  const locale = 'ko-KR';
  const shortLabelLength = 'short';
  const longLabelLength = 'long';    
  const getWeekdays = (locale) => {
    let weekdays = [];
    let newDate = new Date();
    for (let i = 0; i < 7; i++) {
      const date = setDate(newDate, getDate(newDate) + i - getDay(newDate));
      const shortLabel = new Intl.DateTimeFormat(locale, { weekday: shortLabelLength }).format(date);
      const longLabel = new Intl.DateTimeFormat(locale, { weekday: longLabelLength }).format(date);
      const day = { short: shortLabel, long: longLabel };
      weekdays.push(day);
    }
    return weekdays;
  };
  const getMonths = (locale) => {
    let months = [];
    let newDate = new Date();
    const formatter = new Intl.DateTimeFormat(locale, {
      month: shortLabelLength,
    }).format;
    for (let i = 0; i < 12; i++) {
      const month = formatter(addMonths(newDate, i - getMonth(newDate)));
      months.push(month);
    }
    return months;
  };
  const minDate = new Date('01/01/2010');
  const maxDate = new Date('12/31/2030');
  const [value, setValue] = React.useState();
  const handleChange = (value, index) => {
    setValue(value);
  };
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    ariaLabel: '날짜를 선택',
    nextMonthButtonLabel: '다음 달',
    previousMonthButtonLabel: '지난달',
    previousViewIsYearLabel: '연도 보기로 이동',
    previousViewIsMonthLabel: '월별 보기로 이동',
  };
  return (
    <div style={styles.container}>
      <Calendar
        id="korea-calendar-id"
        value={value}
        minDate={minDate}
        maxDate={maxDate}
        onValueChange={handleChange}
        weekdays={getWeekdays(locale)}
        months={getMonths(locale)}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
      />
    </div>
   )
}`;

<CodeExample scope={scope} code={koreaCalendarCode} />

## Accessibility

The following aria-labels should be provided as part of `textMap` prop:

- `ariaLabel` -> `'Choose date'`
- `nextMonthButtonLabel` ->`'Next month'`
- `previousMonthButtonLabel` -> `'Previous month'`
- `previousViewIsYearLabel` -> `'Go to year view'`
- `previousViewIsMonthLabel` -> `'Go to month view'`

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

### Supported Keys

- <kbd>Down Arrow</kbd> / <kbd>Up Arrow</kbd> navigates up and down selectable items in each calendar view
- <kbd>Enter</kbd> selects an item
- <kbd>Space</kbd> selects an item
- <kbd>Tab</kbd> moves tab focus
