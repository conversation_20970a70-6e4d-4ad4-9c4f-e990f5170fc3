# Calendar

## Summary

- Start Date: 2023-02-28
- Figma: https://www.figma.com/file/0ZDD0G8lcpcEVaCbjjmxvD/branch/WQKAlaU2yr1OAL0Zs22reC/%F0%9F%A7%AADatePicker?node-id=3361%3A2837&t=gouE0gFBPXwOihCy-0

## Detailed Design

Calendar is used with DateField to create the DatePicker component. It should be a fully globalizable component, able to accomodate different locales. Calendar will have a base component and sub-components for day, month, and year view.

Requirements:

- [ ] Accessibility. Library date pickers use aria-react (not flexible, doesn't let us pass in our own screen readers)
- [ ] Has to be able to be localized (where we provide locale code)
- [ ] Able to specify start day of the week
- [ ] Choose a day, month, and year with shortcuts (right, left toggle)
- [ ] Mobile (functionality same, experience/look is different)
- [ ] Able to be styled
- [ ] Able to exclude weekends or other certain days (Support disable) [OUT OF SCOPE for now]
- [ ] Able to select date range aka set min/max date [OUT OF SCOPE for now]

## API

1. **id**: `undefined | string`  
   Sets **id** attribute on the html element.
1. **testId**: `undefined | string`  
   Sets **data-test-id** attribute on the html element.
1. **value**: `undefined | Date`  
   Sets _calendar_ value. Default value is today's date.
1. **weekdays**: `undefined | string[]`
   Sets header labels. Begins with Sunday as 0.
1. **startDayOfWeek**: `undefined | number`
   Sets which day the week will begin with. Sunday is 0.
1. **disableDefaultToday**: `undefined | boolean`
   Determines whether today's date renders in a highlighted circle. Default is false.
1. **fullWidth**: `undefined | boolean`
   Sets the Calendar width to fill parent container. Default is false.
1. **borderVariant**: `undefined | TCalendarBorderVariant`
   Sets border variant of the calendar. Default is `outer-elevated`.
1. **onChange**: `undefined | (value: Date) => void`
   Callback when value is changed.
1. **formatDateForSR**: `undefined | (value: Date) => string`
   Converts date into formatted screen reader message.
1. **screenReaderTextMap**: `undefined | ICalendarScreenReaderText`
   `aria-label` for _calendar_.

   ```typescript
   export interface ICalendarScreenReaderText {
     nextMonthButtonLabel: string;
     previousMonthButtonLabel: string;
   }
   ```

1. **calendarEvents**: `undefined | ICalendarEvent[]`
   Array of Calender Event Dates and associated icons

   ```typescript
   export interface ICalendarEvent {
     eventDate: TDate;
     eventIcons: IIconProps[];
   }
   ```

## Usage

A function using **startDayOfWeek** and **weekdays** props to format weekdays in order:

```typescript
interface IWeekday {
  dayOfWeek: number;
  label: string;
}
const startDayOfWeek = 2; //Tuesday
const weekdays = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

const weekdaysFormatter = (startDayOfWeek: number, weekdays: string[]): IWeekday[] => {
  let formattedWeekdays = [];
  for (let i = 0; i < weekdays.length; i++) {
    const newIndex = (i + weekdays.length - startDayOfWeek) % weekdays.length;
    const item = { dayOfWeek: i, label: weekdays[i] };
    formattedWeekdays[newIndex] = item;
  }
  return formattedWeekdays;
};

weekdaysFormatter(startDayOfWeek, weekdays); //Expected output: [{dayOfWeek: 2, label: "Tu"}, {dayOfWeek: 3, label: "We"}, {dayOfWeek: 4, label: "Th"}...]
```

A function using the date value and **weekdays** to return the date's day of the week:

```typescript
   const newDate = Sun Mar 12 2023 14:51:52 GMT-0400 (Eastern Daylight Time);
   const weekdays = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];
   const startDayOfWeek = 2;
   const formattedWeekdays = weekdaysFormatter(startDayOfWeek, weekdays);  //[{dayOfWeek: 2, label: "Tu"}, {dayOfWeek: 3, label: "We"}, {dayOfWeek: 4, label: "Th"}...]

   const getDayOfWeek = (date: Date, weekdays: string[]) : number => {
      const position = date.getDay();
      return weekdays.findIndex(day => day.dayOfWeek === position);
   };

   getDayOfWeek(newDate, formattedWeekdays); //Expected output: 5
```

Functions returning first/last date of week, month, and year:

```typescript
const getStartOfWeek = (date: Date, weekdays: string[]): Date => {};
const getEndOfWeek = (weekdays: string[]): Date => {};

const getStartOfMonth = (date: Date, weekdays: string[]): Date => {};
const getEndOfMonth = (weekdays: string[]): Date => {};

const getStartOfYear = (date: Date, weekdays: string[]): Date => {};
const getEndOfYear = (weekdays: string[]): Date => {};
```

Basic usage of `Calendar`:

```typescript
() => {
  const locale = 'en-US';
  const labelLength = 'narrow';

  const [value, setValue] = useState(Date.now());
  const screenReaderTextMap = {
    nextMonthButton: 'Previous month',
    previousMonthButton: 'Next month',
  };

  const getWeekdays = (locale, labelLength) => {
    let weekdays = [];
    let newDate = new Date();
    const formatter = new Intl.DateTimeFormat(locale, { weekday: labelLength }).format;
    for (let i = 0; i < 7; i++) {
      const day = formatter(new Date().getTime() - (newDate.getDay() - i) * 86400000);
      weekdays.push({ dayOfWeek: i, label: day });
    }
    return weekdays;
  };
  const handleChange = (value, index) => {
    setValue(value);
  };

  return (
    <Calendar
      weekdays={getWeekdays(locale, labelLength)}
      value={value}
      onChange={handleChange}
      screenReaderTextMap={screenReaderTextMap}
    />
  );
};
```

## Prerequisite/Other Components

1. Sub-components: to be rendered based on view of day, month, or year

   - Header: left/right month toggle buttons and date are displayed here at the top
   - HeaderCell: weekday labels
   - CalendarCell: individual cell
   - DayCalendar: days in a month displayed in this view
   - MonthCalendar: all months displayed in this view
   - YearCalendar: all years displayed in this view

   ```typescript
   const Calendar = () => {
     const [view, setView] = useState('day');
     const [value, setValue] = useState(Date.now());

     return (
       <>
         <CalendarHeader value={value} />
         <div>
           {view === 'day' && <DayCalendar {...props} />}
           {view === 'month' && <MonthCalendar {...props} />}
           {view === 'year' && <YearCalendar {...props} />}
         </div>
       </>
     );
   };
   ```

1. Re-use existing components

   - Button
   - Icon button
   - FocusRing

## Accessibility

1. Accessibility behavior mirrors that of the Dojo datepicker calendar.

   Mouse focus behavior:

   - If same year, focus on focusedDate month
   - If different year, focus on focusedDate month of new year
   - If same month, focus on focusedDate day
   - If different month, focus on focusedDate day of new month

   Keyboard focus behavior:

   - If same year, focus on focusedDate month
   - If different year, focus on Jan 1st of new year
   - If same month, focus on focusedDate day
   - If different month, focus on 1st of new month

   In day view, using the header next/previous month buttons won't affect focus which will stay on last focused date.

   Example: Focus is on May 30 -> Next month button is pressed/clicked -> Focus will land on June 30

2. aria-live (using liveAnnouncer component) when value or view has changed

3. When NVDA is enabled, to navigate dates in Calendar table, users need to use: Alt + arrow key (https://kb.iu.edu/d/atfo)

   This does not apply to VoiceOver.

## 3rd Party Alternatives

[React Spectrum](https://react-spectrum.adobe.com/react-spectrum/Calendar.html)

- Pros

  - Good accessibility (focus ring on focused value)
  - Supports many other types of calendars (ex. Persian, Islamic, Hebrew)
  - Mobile friendly

- Cons
  - Built in globalization using locale codes, not flexible
  - No way to customize start day of week
  - Only view is of days in a month

[Material UI](https://mui.com/x/react-date-pickers/date-calendar/#api)

- Pros

  - Has days in month and year view
  - Has prop to disable today's date from being highlighted
  - Mobile friendly

- Cons
  - Lack of built-in accessibility
  - Relies on deprecated [Moment.js](https://momentjs.com/docs/#/-project-status/)
  - Relies heavily on locale, locale is coupled into most helper functions
  - Purchase Pro Plan for advanced features

[Ant Design](https://ant.design/components/date-picker)

- Pros

  - Has all of the views we want and more

- Cons
  - Purchase Enterprise license for commercial use

[Mobiscroll](https://demo.mobiscroll.com/react/calendar/#)

- Pros

  - Has month and year view

- Cons
  - Purchase commercial license
  - Built-in localization, not flexible

[Syncfusion](https://ej2.syncfusion.com/react/documentation/calendar/getting-started)

- Pros

  - Has all of the views we want
  - Styles can be customized

- Cons
  - Monthly subscription can be costly

[date-fns](https://www.npmjs.com/package/date-fns)

- Pros
  - Has many useful helper functions that don't require locale and simplifies calculating dates
  - Size of 300 bytes isn't too large

## Q&A

1. Will we handle Lunar calendar as well?

   - No, only Gregorian for now.

1. Can I change which day of the week Calendar starts with?

   - Yes, this can be done using the **startDayOfWeek** prop. By default, different locales will have different start days. For example, the week starts on a Sunday in the U.S. and on a Monday in Spain.

1. Can I change the weekday labels?

   - Yes. For example, the weekday label for Monday can be modified to be "M" or "Mo". Custom labels can be provided and will be mapped to their respective weekday.

1. Do cons of using a 3rd party library outweigh the pros, in terms of localization and accessibility?

   - No. For the main reason of the examined 3rd party alternatives not being flexible enough to suit our needs, we will build Calendar in-house. However, we will be utilizing date-fns to help us with our helper functions to grab/calculate dates.

## Required PBIs

1. Research 3rd party library for Calendar: https://ceridian.atlassian.net/browse/EDS-2861
1. Create Calendar component: https://ceridian.atlassian.net/browse/EDS-2862
1. Screen reader accessibility: https://ceridian.atlassian.net/browse/EDS-2863
1. Tests: https://ceridian.atlassian.net/browse/EDS-2864

   - Axe-core
   - Manual (screen reader)
   - Visual
   - Unit
   - Playwright

1. Move to Production: https://ceridian.atlassian.net/browse/EDS-2865

### Reference

- React Spectrum: https://react-spectrum.adobe.com/internationalized/date/Calendar.html

- Material UI Calendar: https://mui.com/x/react-date-pickers/date-calendar/

- Customize Header Labels: https://ej2.syncfusion.com/react/documentation/calendar/how-to/customize-the-calendar-day-header?cs-save-lang=1&cs-lang=js

- https://ej2.syncfusion.com/documentation/api/calendar/#dayheaderformat

- Localized Calendar: https://demo.mobiscroll.com/react/calendar/localization#

# Changelog

1/26/2024 [EDS-3519: Modify weekdays prop to also accept SR labels, announce weekday SR labels in Day view](https://ceridian.atlassian.net/browse/EDS-3519)
4/18/2025 [WFM-83556: Add Date symbol(s) and fullWidth styling](https://dayforce.atlassian.net/browse/WFM-83556)
4/30/2025 [WFM-85442: Update Everest Calendar component - Add style variant](https://dayforce.atlassian.net/browse/WFM-85442)
