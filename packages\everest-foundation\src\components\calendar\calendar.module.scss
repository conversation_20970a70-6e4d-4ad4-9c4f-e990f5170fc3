@use '../../variables.scss';
@use '../../index.scss' as helper;

.evrCalendar {
  display: block;
  isolation: isolate;
  width: var(--evr-form-field-min-width);
  background-color: var(--evr-surfaces-primary-default);
  border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-pressed);
  border-radius: var(--evr-radius-2xs);
  box-shadow: var(--evr-depth-06);
  &.evrCalendarFullWidth {
    width: 100%;
  }
  &.evrCalendarNoBorder {
    border: none;
    box-shadow: none;
  }
  &.evrCalendarOuterFlatBorder {
    box-shadow: none;
  }

  .table {
    outline: none;
    table-layout: fixed; // fixed or auto?
    width: 100%;
    padding-inline-start: var(--evr-spacing-2xs);
    padding-inline-end: var(--evr-spacing-2xs);
    border-collapse: separate; // separate or collapse? separate fixes issue of isSelected border-radius not applying
    border-spacing: 0 var(--evr-spacing-3xs); // 0 4px
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /** Month & Year views */
  .yearTable {
    max-height: 15.25rem; // 244px
    overflow-y: scroll;
    display: flex;
    flex-direction: column;

    /* Force scrolllbar to always show in Chrome, Safari
    *  Not possible in Firefox
    *  Comment out for now but to be investigated deeper in: https://ceridian.atlassian.net/browse/EDS-2174
    */
    // &::-webkit-scrollbar {
    //   width: var(--evr-spacing-2xs);
    // }
    // &::-webkit-scrollbar-track {
    //   background: var(--evr-surfaces-primary-default);
    // }
    // &::-webkit-scrollbar-thumb {
    //   background-color: var(--evr-borders-decorative-default);
    //   border-radius: var(--evr-radius-3xs);
    // }
  }

  .monthYearRow {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: var(--evr-spacing-3xs);
    justify-content: space-between;
    padding-bottom: var(--evr-spacing-3xs);
  }

  .monthYearCell {
    height: calc(var(--evr-spacing-xl) + var(--evr-spacing-3xs)); // 44px
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    // Fixes issue with focus ring being cutoff
    &:focus-within {
      overflow: visible;
    }
  }

  .monthYearCellSpan {
    color: var(--evr-content-primary-default);
    border: 0px;
    border-radius: var(--evr-radius-lg);
    outline: 0px;
    text-align: center;
    vertical-align: baseline;
    height: var(--evr-spacing-lg); // 32px
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &:focus {
      outline: none;
    }

    &:hover:not(.isToday):not(.isSelected):not(.isDisabled) {
      background-color: var(--evr-surfaces-secondary-hovered);
    }

    // .pressed &:hover {
    //   background-color: var(--evr-surfaces-secondary-hovered);
    // }
  }

  .monthYearCellSpanText {
    max-width: var(--evr-spacing-2xl);
    margin: var(--evr-spacing-3xs) var(--evr-spacing-2xs);
    overflow: hidden;
  }

  /** Day view */
  .dayOfWeekHeader {
    height: var(--evr-size-lg);
    align-items: center;
    cursor: default;
  }
  .calendarInnerBorder {
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-pressed);
    border-radius: var(--evr-radius-2xs);
    box-shadow: var(--evr-depth-06);
    &.calendarInnerFlatBorder {
      box-shadow: none;
    }
  }

  .calendarDayRow {
    vertical-align: top;
  }

  .dayOfWeekCell {
    color: var(--evr-content-primary-default);
    max-width: calc(var(--evr-spacing-xl) + var(--evr-spacing-3xs)); // 44px
    overflow: hidden;
    text-align: center;
  }

  .isOutsideMonth {
    color: var(--evr-content-primary-lowemp);
  }

  .dayCell {
    color: var(--evr-content-primary-default);
    text-align: center;
    width: calc(var(--evr-spacing-xl) + var(--evr-spacing-3xs)); // 44px
    height: calc(var(--evr-spacing-xl) + var(--evr-spacing-3xs)); // 44px
    padding: 0;
    padding: var(--evr-spacing-3xs) 0;
    position: relative;
    box-sizing: border-box; // include padding in 44px width/height
  }

  .dayCellAndIconContainer {
    display: flex;
    flex-direction: column;
    gap: var(--evr-spacing-2xs);
  }

  .dayCellSpan {
    height: var(--evr-spacing-lg);
    width: var(--evr-spacing-lg);
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    line-height: var(--evr-line-height-md);
    border-radius: var(--evr-radius-circle);
    box-sizing: border-box;
    cursor: pointer;

    &:focus {
      outline: none;
    }

    &:hover:not(.isToday):not(.isSelected):not(.isDisabled) {
      background-color: var(--evr-surfaces-secondary-hovered);
    }
  }

  .eventIcons {
    display: flex;
    justify-content: center;
    gap: var(--evr-spacing-4xs);

    .moreThanTwoEvents {
      display: flex;
      align-items: center;
      gap: var(--evr-spacing-4xs);
    }
  }

  /** All views (day, month, year) */
  .isToday {
    color: var(--evr-interactive-primary-default);
    font-weight: var(--evr-bold-weight);
    border: var(--evr-border-width-thin-px) solid var(--evr-interactive-primary-default);
  }

  .isSelected {
    color: var(--evr-content-primary-inverse);
    font-weight: var(--evr-bold-weight);
    background: var(--evr-interactive-primary-default);
  }

  .isDisabled {
    color: var(--evr-inactive-content);
    text-decoration: line-through;

    &:hover {
      cursor: not-allowed;
    }

    @media (forced-colors: active) {
      color: GrayText;
    }
  }

  .visuallyHidden {
    @include helper.visuallyHidden();
  }
}
