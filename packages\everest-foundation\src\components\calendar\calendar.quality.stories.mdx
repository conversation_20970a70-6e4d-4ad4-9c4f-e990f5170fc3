import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { Calendar } from './calendar';
import { action } from '@storybook/addon-actions';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

{/* Disable Chromatic for dynamic tests - default date is always current day */}
export const disableDynamicSnapshot = {chromatic: Chromatic.DISABLE};

export const minDate = new Date('02/15/2010 00:00:00');
export const maxDate = new Date('11/15/2100 00:00:00');
export const testDate = new Date('03/15/2023 00:00:00');
export const testEvents = [
  {
    eventDate: new Date('03/15/2023 00:00:00'),
    eventIcons: [
      { id: 'shift-icon', name: 'timeFilled', fill: '--evr-interactive-primary-default' },
      { name: 'globe' },
    ],
  },
  {
    eventDate: new Date('04/01/2023 00:00:00'),
    eventIcons: [{ name: 'check' }, { name: 'globe' }, { name: 'timeFilled' }],
  },
];

<Meta
  title="Testing/Automation Test Cases/Calendar"
  component={Calendar}
  decorators={[ChromaticDecorators.padStory, ChromaticDecorators.setWidthTo100vw]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'calendar-id',
    testId: 'calendar-test-id',
    textMap: {
      ariaLabel: 'Choose date',
      nextMonthButtonLabel: 'Next month',
      previousMonthButtonLabel: 'Previous month',
      previousViewIsYearLabel: 'Go to year view',
      previousViewIsMonthLabel: 'Go to month view',
    },
    formatDateForSR: () => {},
    startDayOfWeek: 0,
    weekdays: [
      { short: 'Su', long: 'Sunday' },
      { short: 'Mo', long: 'Monday' },
      { short: 'Tu', long: 'Tuesday' },
      { short: 'We', long: 'Wednesday' },
      { short: 'Th', long: 'Thursday' },
      { short: 'Fr', long: 'Friday' },
      { short: 'Sa', long: 'Saturday' },
    ],
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    disableDefaultToday: false,
    defaultView: 'day',
    minDate: minDate,
    maxDate: maxDate,
  }}
/>

# Calendar

## Live Demo

<Canvas>
  <Story name="Default Year" parameters={disableDynamicSnapshot}>
    {(args) => <Calendar {...args} defaultView="year" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Month" parameters={disableDynamicSnapshot}>
    {(args) => <Calendar {...args} defaultView="month" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Day" parameters={disableDynamicSnapshot} v>
    {(args) => <Calendar {...args} defaultView="day" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Year disableDefaultToday" parameters={disableDynamicSnapshot}>
    {(args) => <Calendar {...args} defaultView="year" disableDefaultToday />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Month disableDefaultToday" parameters={disableDynamicSnapshot}>
    {(args) => <Calendar {...args} defaultView="month" disableDefaultToday />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Day disableDefaultToday" parameters={disableDynamicSnapshot}>
    {(args) => <Calendar {...args} defaultView="day" disableDefaultToday />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Year with selected value">{(args) => <Calendar {...args} defaultView="year" value={testDate} />}</Story>
</Canvas>

<Canvas>
  <Story name="Month with selected value">
    {(args) => <Calendar {...args} defaultView="month" value={testDate} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Day with selected value">{(args) => <Calendar {...args} defaultView="day" value={testDate} />}</Story>
</Canvas>

<Canvas>
  <Story name="Month in current year of minDate">
    {(args) => <Calendar {...args} defaultView="month" value={minDate} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Month in current year of maxDate">
    {(args) => <Calendar {...args} defaultView="month" value={maxDate} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Day in current month of minDate">
    {(args) => <Calendar {...args} defaultView="day" value={minDate} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Day in current month of maxDate">
    {(args) => <Calendar {...args} defaultView="day" value={maxDate} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Day with startDayOfWeek">
    {(args) => <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={1} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Full Width Calendar">
    {(args) => <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={1} fullWidth />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Day with Events">
    {(args) => <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={0} calendarEvents={testEvents} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Calendar with No Border">
    {(args) => <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={0} borderVariant="none" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Calendar with Inner Border (Elevated)">
    {(args) => (
      <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={0} borderVariant="inner-elevated" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Calendar with Inner Border (Flat)">
    {(args) => <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={0} borderVariant="inner-flat" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Calendar with Outer Border (Elevated) - Default">
    {(args) => (
      <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={0} borderVariant="outer-elevated" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Calendar with Outer Border (Flat)">
    {(args) => <Calendar {...args} defaultView="day" value={testDate} startDayOfWeek={0} borderVariant="outer-flat" />}
  </Story>
</Canvas>
