import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { act } from '@testing-library/react-hooks';
import userEvent from '@testing-library/user-event';

import { Calendar } from './calendar';
import { formatHeaderTitle } from './calendar-helpers';
import { ICalendarEvent, TDate } from '../../types';
import {
  addMonths,
  addYears,
  getYear,
  startOfDay,
  startOfMonth,
  startOfYear,
  subMonths,
  subYears,
} from '../../utils/date-utils';

import styles from './calendar.module.scss';

const onChange = jest.fn();
const onMonthChange = jest.fn();
const formatDateForSR = jest.fn();

const props = {
  id: 'calendar-id',
  testId: 'calendar-test-id',
  minDate: new Date('02/01/2010'),
  maxDate: new Date('11/01/2030'),
  weekdays: [
    { short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' },
  ],
  months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  onValueChange: onChange,
  onMonthChange: onMonthChange,
  formatDateForSR,
  textMap: {
    ariaLabel: 'Choose date',
    nextMonthButtonLabel: 'Next month',
    previousMonthButtonLabel: 'Previous month',
    previousViewIsYearLabel: 'Go to year view',
    previousViewIsMonthLabel: 'Go to month view',
  },
};

const getByTestId = () => screen.getByTestId(props.testId);
const getHeaderTitle = () => document.getElementById(`${props.id}-header-title`);
const getPreviousViewButton = (buttonName: string) => screen.getByRole('button', { name: buttonName });
const getPreviousMonthButton = () => screen.getByTestId(`${props.testId}-previous-month-button`);
const getNextMonthButton = () => screen.getByTestId(`${props.testId}-next-month-button`);
const getDayView = () => screen.getByTestId(`${props.id}-day`);
const getMonthView = () => screen.getByTestId(`${props.id}-month`);
const getYearView = () => screen.getByTestId(`${props.id}-year`);

const today = startOfDay(new Date());
const initialValue = new Date('7/30/23 00:00:00');
const nextMonth = (date: TDate) => startOfMonth(addMonths(date, 1));
const nextYear = (date: TDate) => startOfYear(addYears(date, 1));
const prevMonth = (date: TDate) => startOfMonth(subMonths(date, 1));
const prevYear = (date: TDate) => startOfYear(subYears(date, 1));
const getSelectedValue = (date: TDate, view: string) =>
  document.getElementById(`${props.id}-${view}-${startOfDay(date).valueOf()}`);

describe('[Calendar]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('[Day View]', () => {
    it('renders correct header for day view', () => {
      render(<Calendar {...props} defaultView={'day'} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument(); // day view calendar
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument(); // prev view button
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveAttribute(
        'aria-label',
        props.textMap.previousViewIsMonthLabel
      );
      expect(getPreviousMonthButton()).toBeInTheDocument(); // prev month button (left arrow)
      expect(getNextMonthButton()).toBeInTheDocument(); // next month button (right arrow)
    });

    it('switches to previous month of current year', async () => {
      const currentHeaderTitle = formatHeaderTitle('day', initialValue, props.months, props.minDate, props.maxDate);
      const prevHeaderTitle = formatHeaderTitle(
        'day',
        prevMonth(initialValue),
        props.months,
        props.minDate,
        props.maxDate
      );
      render(<Calendar {...props} defaultView={'day'} value={initialValue} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument(); // day view calendar
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument(); // prev view button
      expect(getPreviousMonthButton()).toBeInTheDocument(); // prev month button (left arrow)
      expect(getNextMonthButton()).toBeInTheDocument(); // next month button (right arrow)
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(currentHeaderTitle);

      await act(async () => {
        await userEvent.click(getPreviousMonthButton());
      });

      await waitFor(() => {
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(prevHeaderTitle);
      });
    });

    it('switches to next month of current year', async () => {
      const currentHeaderTitle = formatHeaderTitle('day', initialValue, props.months, props.minDate, props.maxDate);
      const nextHeaderTitle = formatHeaderTitle(
        'day',
        nextMonth(initialValue),
        props.months,
        props.minDate,
        props.maxDate
      );
      render(<Calendar {...props} defaultView={'day'} value={initialValue} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument(); // day view calendar
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument(); // prev view button
      expect(getPreviousMonthButton()).toBeInTheDocument(); // prev month button (left arrow)
      expect(getNextMonthButton()).toBeInTheDocument(); // next month button (right arrow)
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(currentHeaderTitle);

      await act(async () => {
        await userEvent.click(getNextMonthButton());
      });

      await waitFor(() => {
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(nextHeaderTitle);
        expect(onMonthChange).toHaveBeenCalledTimes(1);
      });
    });

    it('switches to previous month of previous year if current month is at beginning of current year', async () => {
      const startOfYearDate = new Date('1/1/2023');
      const currentYear = getYear(startOfYearDate);
      const prevYear = getYear(prevMonth(startOfYearDate));
      const prevHeaderTitle = formatHeaderTitle(
        'day',
        prevMonth(startOfYearDate),
        props.months,
        props.minDate,
        props.maxDate
      );
      render(<Calendar {...props} defaultView={'day'} value={startOfYearDate} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument(); // day view calendar
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument(); // prev view button
      expect(getPreviousMonthButton()).toBeInTheDocument(); // prev month button (left arrow)
      expect(getNextMonthButton()).toBeInTheDocument(); // next month button (right arrow)

      await act(async () => {
        await userEvent.click(getPreviousMonthButton());
      });

      await waitFor(() => {
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(prevHeaderTitle);
        expect(prevYear).not.toEqual(currentYear);
        expect(onMonthChange).toHaveBeenCalledTimes(1);
      });
    });

    it('switches to next month of next year if current month is at end of current year', async () => {
      const endOfYearDate = new Date('12/1/2023');
      const currentYear = getYear(endOfYearDate);
      const nextYear = getYear(nextMonth(endOfYearDate));
      const nextHeaderTitle = formatHeaderTitle(
        'day',
        nextMonth(endOfYearDate),
        props.months,
        props.minDate,
        props.maxDate
      );
      render(<Calendar {...props} defaultView={'day'} value={endOfYearDate} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument(); // day view calendar
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument(); // prev view button
      expect(getPreviousMonthButton()).toBeInTheDocument(); // prev month button (left arrow)
      expect(getNextMonthButton()).toBeInTheDocument(); // next month button (right arrow)

      await act(async () => {
        await userEvent.click(getNextMonthButton());
      });

      await waitFor(() => {
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(nextHeaderTitle);
        expect(nextYear).not.toEqual(currentYear);
        expect(onMonthChange).toHaveBeenCalledTimes(1);
      });
    });

    it('does not switch to previous month if minDate reached', () => {
      // minDate: new Date('01/15/2010')
      const minDate = new Date('1/15/2010');
      render(<Calendar {...props} defaultView={'day'} value={minDate} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument(); // day view calendar
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument(); // expect prevMonth button to be disabled
      expect(getPreviousMonthButton()).toBeInTheDocument(); // prev month button (left arrow)
      expect(getPreviousMonthButton()).toBeDisabled();
      expect(getNextMonthButton()).toBeInTheDocument(); // next month button (right arrow)
    });

    it('does not switch to next month if maxDate reached', () => {
      const maxDate = new Date('12/15/2030');
      render(<Calendar {...props} defaultView={'day'} value={maxDate} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument(); // day view calendar
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument(); // expect prevMonth button to be disabled
      expect(getPreviousMonthButton()).toBeInTheDocument(); // prev month button (left arrow)
      expect(getNextMonthButton()).toBeInTheDocument(); // next month button (right arrow)
      expect(getNextMonthButton()).toBeDisabled();
    });

    it('changes day view to month view onViewChange', async () => {
      render(<Calendar {...props} defaultView={'day'} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getDayView()).toBeInTheDocument();
      expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(getPreviousViewButton(props.textMap.previousViewIsMonthLabel));
      });

      await waitFor(() => {
        expect(getMonthView()).toBeInTheDocument();
      });
    });

    it('sets selected date in current month', async () => {
      render(<Calendar {...props} defaultView={'day'} />);
      const selectedValue = getSelectedValue(today, 'day');
      const headerTitle = formatHeaderTitle('day', today, props.months, props.minDate, props.maxDate);

      expect(selectedValue).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(selectedValue as HTMLElement);
      });

      await waitFor(() => {
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenCalledWith(today);
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(headerTitle);
      });
    });

    it('sets selected date in next month', async () => {
      const startOfMonthDate = new Date('08/01/2023');
      const prevMonth = startOfDay(initialValue);
      const headerTitle = formatHeaderTitle('day', prevMonth, props.months, props.minDate, props.maxDate);

      render(<Calendar {...props} defaultView={'day'} value={startOfMonthDate} />);

      const selectCell = getSelectedValue(prevMonth, 'day');

      await act(async () => {
        await userEvent.click(selectCell as HTMLElement);
      });

      await waitFor(() => {
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenCalledWith(initialValue);
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(headerTitle);
      });
    });

    it('highlights todays date if disableDefaultToday not enabled', async () => {
      render(<Calendar {...props} defaultView={'day'} />);

      const todayCell = getSelectedValue(today, 'day');

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getDayView()).toBeInTheDocument();
        expect(todayCell).toHaveClass('isToday');
      });
    });

    it('does not highlight todays date if disableDefaultToday enabled', async () => {
      render(<Calendar {...props} defaultView={'day'} disableDefaultToday />);

      const todayCell = getSelectedValue(today, 'day');

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getDayView()).toBeInTheDocument();
        expect(todayCell).not.toHaveClass('isToday');
      });
    });

    describe('focus behavior', () => {
      it.skip('value should not lose focus if day changes', async () => {
        render(<Calendar {...props} defaultView={'day'} value={initialValue} />);

        const selectedValue = getSelectedValue(initialValue, 'day');
        expect(selectedValue).toHaveFocus();

        // TODO test just before midnight

        // TODO test just after midnight
        await act(async () => {
          jest.advanceTimersByTime(8.646e7); // set time forwards by 24 hrs 1 min
          jest.runAllTimers();
        });

        expect(selectedValue).toBeInTheDocument();
        expect(selectedValue).toHaveFocus();
      });
    });
  });

  describe('[Month View]', () => {
    it('renders correct header for month view', () => {
      const monthHeaderTitle = formatHeaderTitle('month', today, props.months, props.minDate, props.maxDate);
      render(<Calendar {...props} defaultView={'month'} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getMonthView()).toBeInTheDocument();
      expect(getPreviousViewButton(props.textMap.previousViewIsYearLabel)).toBeInTheDocument();
      expect(getPreviousViewButton(props.textMap.previousViewIsYearLabel)).toHaveTextContent(monthHeaderTitle);
      expect(getPreviousViewButton(props.textMap.previousViewIsYearLabel)).toHaveAttribute(
        'aria-label',
        props.textMap.previousViewIsYearLabel
      );
    });

    it('renders correct month range if minDate month starts in same year', () => {
      render(<Calendar {...props} defaultView={'month'} value={props.minDate} />);

      const minDateCell = getSelectedValue(prevMonth(props.minDate), 'month');

      expect(getByTestId()).toBeInTheDocument();
      expect(getMonthView()).toBeInTheDocument();
      expect(minDateCell).toHaveClass('isDisabled');
    });

    it('renders correct month range if maxDate month starts in same year', () => {
      render(<Calendar {...props} defaultView={'month'} value={props.maxDate} />);

      const maxDateCell = getSelectedValue(nextMonth(props.maxDate), 'month');

      expect(getByTestId()).toBeInTheDocument();
      expect(getMonthView()).toBeInTheDocument();
      expect(maxDateCell).toHaveClass('isDisabled');
    });

    it('changes month view to year view onViewChange', async () => {
      render(<Calendar {...props} defaultView={'month'} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getMonthView()).toBeInTheDocument();
      expect(getPreviousViewButton(props.textMap.previousViewIsYearLabel)).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(getPreviousViewButton(props.textMap.previousViewIsYearLabel));
      });

      await waitFor(() => {
        expect(getYearView()).toBeInTheDocument();
      });
    });

    it('sets selected date', async () => {
      // Use initialValue instead of the default value of today, otherwise the test will fail during December
      // since the next month will be Jan and won't exist in the current display/year
      render(<Calendar {...props} defaultView={'month'} value={initialValue} />);
      const selectCell = getSelectedValue(nextMonth(initialValue), 'month');
      const headerTitle = formatHeaderTitle('day', nextMonth(initialValue), props.months, props.minDate, props.maxDate);

      expect(selectCell).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(selectCell as HTMLElement);
      });

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getDayView()).toBeInTheDocument();
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toBeInTheDocument();
        expect(getPreviousViewButton(props.textMap.previousViewIsMonthLabel)).toHaveTextContent(headerTitle);
      });
    });

    it('highlights todays month if disableDefaultToday not enabled', async () => {
      render(<Calendar {...props} defaultView={'month'} />);

      const currentMonth = getSelectedValue(startOfMonth(today), 'month');

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getMonthView()).toBeInTheDocument();
        expect(currentMonth).toHaveClass('isToday');
      });
    });

    it('does not highlight todays month if disableDefaultToday enabled', async () => {
      render(<Calendar {...props} defaultView={'month'} disableDefaultToday />);

      const currentMonth = getSelectedValue(startOfMonth(today), 'month');

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getMonthView()).toBeInTheDocument();
        expect(currentMonth).not.toHaveClass('isToday');
      });
    });

    describe('focus behavior', () => {
      it.skip('should not lose focus if month changes', async () => {
        // TODO: Add these tests in https://ceridian.atlassian.net/browse/EDS-3521
        // test just before midnight end of month
        // test just after midnight end of month
        //
        // render(<Calendar {...props} defaultView={'month'} value={endOfMonthDate} />);
        // const endOfMonthValue = getSelectedValue(endOfMonthDate, 'month');
        // expect(endOfMonthValue).toHaveFocus();
        // await act(async () => {
        //   jest.advanceTimersByTime(8.646e7); // set time forwards by 24 hrs 1 min
        //   jest.runAllTimers();
        // });
        // expect(endOfMonthValue).toBeInTheDocument();
        // expect(endOfMonthValue).toHaveFocus();
      });
    });
  });

  describe('[Year View]', () => {
    it('renders correct header for year view', () => {
      const yearHeader = formatHeaderTitle('year', today, props.months, props.minDate, props.maxDate);
      render(<Calendar {...props} defaultView={'year'} />);

      expect(getByTestId()).toBeInTheDocument();
      expect(getYearView()).toBeInTheDocument();
      expect(getHeaderTitle()).toBeInTheDocument();
      expect(getHeaderTitle()).toHaveTextContent(yearHeader);
    });

    it('renders correct year range starting with minDate and maxDate', () => {
      render(<Calendar {...props} defaultView={'year'} />);

      const minDateCell = getSelectedValue(startOfYear(props.minDate), 'year');
      const maxDateCell = getSelectedValue(startOfYear(props.maxDate), 'year');
      const minDatePrevCell = getSelectedValue(startOfYear(prevYear(props.minDate)), 'year');
      const maxDatePrevCell = getSelectedValue(startOfYear(nextYear(props.maxDate)), 'year');

      expect(getByTestId()).toBeInTheDocument();
      expect(getYearView()).toBeInTheDocument();
      expect(minDateCell).toBeInTheDocument();
      expect(minDatePrevCell).not.toBeInTheDocument();
      expect(maxDateCell).toBeInTheDocument();
      expect(maxDatePrevCell).not.toBeInTheDocument();
    });

    it('sets selected date', async () => {
      render(<Calendar {...props} defaultView={'year'} />);
      const headerTitle = formatHeaderTitle('month', startOfMonth(today), props.months, props.minDate, props.maxDate);
      const selectedValue = getSelectedValue(startOfYear(today), 'year');

      expect(selectedValue).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(selectedValue as HTMLElement);
      });

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getMonthView()).toBeInTheDocument();
        expect(getPreviousViewButton(props.textMap.previousViewIsYearLabel)).toBeInTheDocument();
        expect(getPreviousViewButton(props.textMap.previousViewIsYearLabel)).toHaveTextContent(headerTitle);
        expect(onMonthChange).not.toHaveBeenCalled();
      });
    });

    it('highlights todays year if disableDefaultToday not enabled', async () => {
      render(<Calendar {...props} defaultView={'year'} />);

      const currentYear = getSelectedValue(startOfYear(today), 'year');

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getYearView()).toBeInTheDocument();
        expect(getHeaderTitle()).toBeInTheDocument();
        expect(currentYear).toHaveClass('isToday');
      });
    });

    it('does not highlight todays year if disableDefaultToday enabled', async () => {
      render(<Calendar {...props} defaultView={'year'} disableDefaultToday />);

      const currentYear = getSelectedValue(startOfYear(today), 'year');

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getYearView()).toBeInTheDocument();
        expect(getHeaderTitle()).toBeInTheDocument();
        expect(currentYear).not.toHaveClass('isToday');
      });
    });

    it('should show calendar in full width', async () => {
      render(<Calendar {...props} fullWidth />);

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(getByTestId()).toHaveClass(styles.evrCalendarFullWidth);
        expect(getByTestId()).not.toHaveClass(styles.evrCalendarNormalWidth);
      });
    });

    it('should show calendar event Icons', async () => {
      const calendarEvents: ICalendarEvent[] = [
        {
          eventDate: new Date('2025-04-01T00:00'),
          eventIcons: [{ id: 'april-first-day-holiday', testId: 'april-first-day-holiday', name: 'globe' }],
        },
      ];
      render(<Calendar {...props} value={new Date('2025-04-01T00:00')} calendarEvents={calendarEvents} />);

      const aprilFirstIconContainer = screen.getByTestId(
        `calendar-id-day-${new Date('2025-04-01T00:00').valueOf()}-icon-container`
      );

      await waitFor(() => {
        expect(getByTestId()).toBeInTheDocument();
        expect(aprilFirstIconContainer.querySelectorAll('svg').length).toBe(1);
        expect(aprilFirstIconContainer.querySelector('svg[data-evr-name="globe"]')).toBeInTheDocument();
      });
    });

    describe('focus behavior', () => {
      it.skip('should not lose focus if year changes', async () => {
        // TODO: Add these tests in https://ceridian.atlassian.net/browse/EDS-3521
        // test just before midnight end of year
        // test just after midnight end of year
      });
    });
  });
});
