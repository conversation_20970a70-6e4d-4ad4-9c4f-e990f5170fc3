import { useState } from '@storybook/addons';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { Calendar } from './calendar';
import { action } from '@storybook/addon-actions';
import Examples from './calendar.examples.mdx';

<Meta
  title="Toolbox/Calendar"
  component={Calendar}
  parameters={{
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/0ZDD0G8lcpcEVaCbjjmxvD/branch/WQKAlaU2yr1OAL0Zs22reC/%F0%9F%A7%AADatePicker?node-id=3361-4568&t=3YRSlPCPmWT5w1XT-0',
    },
    controls: {
      exclude: ['ref'],
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Sets id attribute on the html element',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets data-test-id attribute on the html element',
    },
    defaultView: {
      control: 'radio',
      options: ['year', 'month', 'day'],
      description: 'Sets default view. Default value is day.',
      table: {
        defaultValue: {
          summary: 'day',
        },
      },
    },
    value: {
      control: 'date',
      description: 'Sets calendar value. Default value is todays date.',
    },
    minDate: {
      control: 'date',
      description: 'Sets min date.',
    },
    maxDate: { control: 'date', description: 'Sets max date.' },
    weekdays: {
      type: 'array',
      control: 'object',
      description: 'Array of type `IWeekday` items used to set weekday header labels. Begins with Sunday as 0.',
    },
    months: {
      type: 'array',
      description: 'Sets calendar months.',
    },
    calendarEvents: {
      type: 'array',
      description: 'Displays specified icon(s) for specific calendar event date(s).',
    },
    borderVariant: {
      control: 'select',
      options: ['none', 'inner-elevated', 'inner-flat', 'outer-elevated', 'outer-flat'],
      description: 'Sets border variant of the calendar. Default is outer-elevated.',
      table: {
        defaultValue: {
          summary: 'outer-elevated',
        },
      },
    },
    startDayOfWeek: {
      type: 'enum',
      control: 'select',
      description: 'Sets which day the week will begin with. Sunday is 0.',
      table: {
        defaultValue: {
          summary: 0,
        },
      },
    },
    disableDefaultToday: {
      type: 'boolean',
      description: "Determines whether today's date renders in a highlighted circle. Default is false.",
      table: {
        defaultValue: {
          summary: false,
        },
      },
    },
    fullWidth: {
      type: 'boolean',
      control: 'boolean',
      description: 'Sets calendar to full width.',
      table: {
        defaultValue: {
          summary: false,
        },
      },
    },
    onFocus: {
      control: '-',
      description: 'Callback when user focuses on calendar.',
    },
    onBlur: {
      control: '-',
      description: 'Callback when user leaves calendar.',
    },
    onValueChange: {
      control: '-',
      description: 'Callback when calendar selected value changed.',
    },
    onMonthChange: {
      control: '-',
      description: 'Callback when month changes.',
    },
    formatDateForSR: {
      control: '-',
      description: 'Converts date into formatted screen reader message.',
    },
    textMap: {
      type: 'array',
      control: 'object',
      description: 'Sets aria-labels for calendar.',
    },
  }}
  args={{
    id: 'calendar-1',
    testId: 'calendar-test-id',
    textMap: {
      ariaLabel: 'Choose date',
      nextMonthButtonLabel: 'Next month',
      previousMonthButtonLabel: 'Previous month',
      previousViewIsYearLabel: 'Go to year view',
      previousViewIsMonthLabel: 'Go to month view',
    },
    startDayOfWeek: 0,
    weekdays: [
      { short: 'Su', long: 'Sunday' },
      { short: 'Mo', long: 'Monday' },
      { short: 'Tu', long: 'Tuesday' },
      { short: 'We', long: 'Wednesday' },
      { short: 'Th', long: 'Thursday' },
      { short: 'Fr', long: 'Friday' },
      { short: 'Sa', long: 'Saturday' },
    ],
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    calendarEvents: [],
    fullWidth: false,
    disableDefaultToday: false,
    defaultView: 'day',
    borderVariant: 'outer-elevated',
    minDate: new Date('01/01/2010'),
    maxDate: new Date('12/31/2030'),
    onBlur: action('onBlur'),
    onFocus: action('onFocus'),
  }}
/>

# Calendar

<Examples />

## Live Demo & API

export const formatDateForSR = (view, value) => {
  switch (view) {
    case 'year':
      return value.toLocaleDateString('en-US', { year: 'numeric' });
    case 'month':
      return value.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
    case 'day':
      return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
  }
};

<Canvas>
  <Story name="Calendar">
    {(args) => {
      const [{ value }, updateArgs] = useArgs();
      return (
        <>
          <Calendar
            {...args}
            value={value}
            minDate={args.minDate}
            maxDate={args.maxDate}
            onValueChange={(newValue) => {
              updateArgs({
                value: newValue,
              });
              action('onChange')(newValue);
            }}
            weekdays={args.weekdays}
            months={args.months}
            startDayOfWeek={args.startDayOfWeek}
            textMap={args.textMap}
            formatDateForSR={formatDateForSR}
          />
        </>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Calendar" />
