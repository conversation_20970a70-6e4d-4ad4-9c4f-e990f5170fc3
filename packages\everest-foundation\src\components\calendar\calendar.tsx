import React, { forwardRef, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';

import { CalendarContext } from './calendar-context';
import { getDateConstraints, getFormattedWeekdays, getYearRange } from './calendar-helpers';
import { CalendarHeader } from './header/calendar-header';
import { useCalendarViews } from './hooks/use-calendar-views';
import { CalendarDay } from './views/calendar-day';
import { CalendarMonth } from './views/calendar-month';
import { CalendarYear } from './views/calendar-year';
import {
  ICalendarEvent,
  IWeekday,
  TCalendarBorderVariant,
  TCalendarView,
  TDate,
  TStartDayOfWeekRange,
} from '../../types';
import { announce, clearAnnouncer, mergeRefs, useCreateTestId } from '../../utils';
import {
  addYears,
  endOfMonth,
  formatISO,
  getMonth,
  getYear,
  isAfter,
  isBefore,
  isSameMonth,
  setMonth,
  setYear,
  startOfDay,
  startOfMonth,
  startOfYear,
  subYears,
} from '../../utils/date-utils';
import { useComponentFocused } from '../../utils/use-component-focused';

import styles from './calendar.module.scss';

export interface ICalendarTextMap {
  ariaLabel: string;
  nextMonthButtonLabel: string;
  previousMonthButtonLabel: string;
  previousViewIsYearLabel: string;
  previousViewIsMonthLabel: string;
}

export interface ICalendarProps {
  id: string;
  testId?: string;
  defaultView?: TCalendarView;
  value?: TDate;
  minDate?: TDate;
  maxDate?: TDate;
  weekdays: IWeekday[];
  months: string[];
  calendarEvents?: ICalendarEvent[];
  startDayOfWeek?: TStartDayOfWeekRange;
  disableDefaultToday?: boolean;
  fullWidth?: boolean;
  borderVariant?: TCalendarBorderVariant;
  onValueChange?: (value: TDate) => void;
  onMonthChange?: (value: TDate) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  formatDateForSR: (view: TCalendarView, value?: TDate) => string;
  textMap: ICalendarTextMap;
  ref?: React.ForwardedRef<HTMLTableElement>;
}

export const Calendar = forwardRef<HTMLElement, ICalendarProps>((props: ICalendarProps, ref) => {
  const {
    id,
    testId,
    defaultView = 'day',
    value,
    minDate: minDateIn,
    maxDate: maxDateIn,
    weekdays,
    months,
    startDayOfWeek = 0,
    disableDefaultToday = false,
    onValueChange,
    onFocus,
    onBlur,
    formatDateForSR,
    textMap,
    fullWidth = false,
    calendarEvents,
    onMonthChange,
    borderVariant = 'outer-elevated',
  } = props;

  const calendarRef = useCreateTestId(testId);
  const previousViewButtonRef = useCreateTestId(`${testId}-previous-view-button`);
  const previousMonthButtonRef = useCreateTestId(`${testId}-previous-month-button`);
  const nextMonthButtonRef = useCreateTestId(`${testId}-next-month-button`);
  const focusExceptionsRef = useRef<Map<string, boolean>>(new Map([[`${id}-header-previous-view-button`, true]]));

  // use ref to prevent unnecessary renders
  const today = useRef<Date>(startOfDay(new Date()));
  // Initialize min/maxDate value after today value is declared
  // Use today.current ref instead of new Date() since that changes every second
  const minDate = useMemo(() => {
    const minDateValue = minDateIn instanceof Date ? minDateIn : new Date(minDateIn as number);
    return minDateValue?.getTime() || subYears(today.current, 100).getTime();
  }, [minDateIn]);
  const maxDate = useMemo(() => {
    const maxDateValue = maxDateIn instanceof Date ? maxDateIn : new Date(maxDateIn as number);
    return maxDateValue?.getTime() || addYears(today.current, 100).getTime();
  }, [maxDateIn]);
  const years = useMemo(() => getYearRange(minDate, maxDate), [minDate, maxDate]);

  const [displayDate, setDisplayDate] = useState<TDate>(
    startOfMonth(value ?? getDateConstraints(today.current, minDate, maxDate))
  );
  const [focusedDate, setFocusedDate] = useState(value);
  const [isFocusWithinComponent, setIsFocusWithinComponent] = useState(true);

  const handleChangeMonth = useCallback(
    (newDate: TDate) => {
      if (isSameMonth(newDate, displayDate)) {
        return;
      }
      setDisplayDate(startOfMonth(newDate));
      onMonthChange?.(newDate);
    },
    [displayDate, setDisplayDate, onMonthChange]
  );

  const { view, goToView } = useCalendarViews({
    openTo: defaultView,
  });

  const selectedDays = useMemo(() => {
    if (view === 'day') {
      if (value && (isBefore(endOfMonth(value), minDate) || isAfter(startOfMonth(value), maxDate))) return;
    }
    return [value];
  }, [value, minDate, maxDate, view]);

  const orderedWeekdays = useMemo(() => {
    return getFormattedWeekdays(startDayOfWeek, weekdays);
  }, [startDayOfWeek, weekdays]);

  const handleViewChange = (currentView: TCalendarView) => {
    switch (currentView) {
      case 'day':
        return goToView('month');
      case 'month':
        return goToView('year');
    }
  };

  /*
   * Mouse behavior:
   * - Same year -> Focus on focused month
   * - Different year -> Focus on focused month in previous year
   *
   * Keyboard behavior:
   * - Same year -> Focus on focused month
   * - Different year -> Focus on Jan 1 of new year
   */
  const handleDateYearChange = (newDate: TDate) => {
    const newYear = getYear(newDate);
    const monthStart = startOfMonth(focusedDate || newDate);
    const validYear = setYear(monthStart, newYear);

    goToView('month');
    setDisplayDate(startOfYear(newDate));
    setFocusedDate(validYear);
  };

  /*
   * Mouse behavior:
   * - Same month -> Focus current focused day value in same month
   * - Different month -> Focus current focused day value in new month
   *
   * Keyboard behavior:
   * - Same month -> Focus current focused day value in same month
   * - Different month -> Focus on 1st of new month
   */
  const handleDateMonthChange = (newDate: TDate) => {
    const validDate = setMonth(focusedDate || newDate, getMonth(newDate));
    const constrainedDate = getDateConstraints(validDate, minDate, maxDate);

    goToView('day');
    setDisplayDate(startOfMonth(newDate));
    setFocusedDate(constrainedDate);
    onMonthChange?.(newDate);
  };

  const handleSelectedDayChange = (newDate: TDate) => {
    onValueChange?.(newDate);
    setDisplayDate(newDate);
    setFocusedDate?.(newDate);
  };

  useEffect(() => {
    clearAnnouncer('assertive');
    announce(`${view} view`, 'assertive');
  }, [view]);

  useEffect(() => {
    setDisplayDate(startOfMonth(getDateConstraints(value || today.current, minDate, maxDate)));
    // Include today.current to update when todays date changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, minDate, maxDate, today.current]);

  useComponentFocused(
    [id],
    // eventListener types
    ['mousedown', 'keyup'],
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      onFocus?.();
      setIsFocusWithinComponent(true);
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      setIsFocusWithinComponent(false);
      onBlur?.();
    },
    focusExceptionsRef.current
  );

  const calendarContext = useMemo(
    () => ({
      previousViewButtonRef,
      previousMonthButtonRef,
      nextMonthButtonRef,
      disableDefaultToday,
      displayDate,
      setDisplayDate,
      minDate,
      maxDate,
      focusedDate,
      setFocusedDate,
      view,
      isFocusWithinComponent,
      formatDateForSR,
    }),
    [
      previousViewButtonRef,
      previousMonthButtonRef,
      nextMonthButtonRef,
      disableDefaultToday,
      displayDate,
      setDisplayDate,
      minDate,
      maxDate,
      focusedDate,
      setFocusedDate,
      view,
      isFocusWithinComponent,
      formatDateForSR,
    ]
  );

  /*
   * Create a map of events for the month view (keyed by date)
   * This is to fix any worst-case performance issues with the calendar
   * since Map lookups are O(1) vs O(n) for array lookups
   * This is only used in the day view
   */
  const monthEventMap = useMemo(() => {
    const map = new Map<string, ICalendarEvent>();
    calendarEvents?.forEach((event) => {
      const key = formatISO(new Date(event.eventDate), { representation: 'date' });
      map.set(key, event);
    });
    return map;
  }, [calendarEvents]);

  /*
   * Memoized the root calendar class names
   * Applies the global calendar styles and adjusts layout based on
   * Border Variant and Full Width flag
   */
  const calendarRootClassName = useMemo(() => {
    const classList = [styles.evrCalendar];

    if (fullWidth) classList.push(styles.evrCalendarFullWidth);

    if (borderVariant === 'none' || borderVariant === 'inner-elevated' || borderVariant === 'inner-flat') {
      classList.push(styles.evrCalendarNoBorder);
    } else if (borderVariant === 'outer-flat') {
      classList.push(styles.evrCalendarOuterFlatBorder);
    }

    return classnames(...classList);
  }, [borderVariant, fullWidth]);

  const isInnerBorder = borderVariant === 'inner-flat' || borderVariant === 'inner-elevated';

  /*
   * Conditionally applies inner border styles when either 'inner-flat' or 'inner-elevated' border variant.
   * Defaults to elevated shadow unless explicitly set to 'inner-flat'.
   */
  const innerBorderClassName = isInnerBorder
    ? classnames(styles.calendarInnerBorder, {
        [styles.calendarInnerFlatBorder]: borderVariant === 'inner-flat',
      })
    : undefined;

  // Declarative mapping of views to components for cleaner conditional rendering.
  const viewComponentMap: Record<TCalendarView, ReactNode> = {
    year: (
      <CalendarYear
        id={`${id}-year`}
        years={years}
        date={value}
        onChange={handleDateYearChange}
        onViewChange={goToView}
      />
    ),
    month: <CalendarMonth id={`${id}-month`} months={months} date={value} onChange={handleDateMonthChange} />,
    day: (
      <CalendarDay
        id={`${id}-day`}
        selectedDays={selectedDays}
        startDayOfWeek={startDayOfWeek}
        weekdays={orderedWeekdays}
        onChange={handleSelectedDayChange}
        onMonthChange={handleChangeMonth}
        monthEventMap={monthEventMap}
      />
    ),
  };

  // Conditionally wrap the view only if an inner border is needed
  const renderCalendarView = (children: ReactNode) =>
    isInnerBorder ? <div className={innerBorderClassName}>{children}</div> : children;

  return (
    <CalendarContext.Provider value={calendarContext}>
      <div
        ref={mergeRefs([ref, calendarRef])}
        id={id}
        className={calendarRootClassName}
        tabIndex={-1}
        role="dialog"
        aria-label={textMap.ariaLabel}
        onFocus={() => setIsFocusWithinComponent(true)}
      >
        <CalendarHeader
          id={`${id}-header`}
          months={months}
          onViewChange={handleViewChange}
          onMonthChange={(newMonth) => handleChangeMonth(newMonth)}
          textMap={textMap}
        />
        {renderCalendarView(viewComponentMap[view])}
      </div>
    </CalendarContext.Provider>
  );
});

Calendar.displayName = 'Calendar';
