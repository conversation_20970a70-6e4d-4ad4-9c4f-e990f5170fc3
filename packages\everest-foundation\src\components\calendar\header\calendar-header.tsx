import React, { forwardRef, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import classnames from 'classnames';

import { TCalendarView, TDate } from '../../../types';
import { mergeRefs, useCreateTestId } from '../../../utils';
import { endOfMonth, isAfter, isBefore, startOfMonth } from '../../../utils/date-utils';
import { announce, clearAnnouncer } from '../../../utils/live-announcer';
import { Button } from '../../button';
import { IconButton } from '../../icon-button';
import { ICalendarTextMap } from '../calendar';
import { CalendarContext } from '../calendar-context';
import { formatHeaderTitle, getDateConstraints, getNextMonth, getPreviousMonth } from '../calendar-helpers';

import styles from './calendar-header.module.scss';

export interface ICalendarHeaderProps {
  id: string;
  months: string[];
  onViewChange: (view: TCalendarView) => void;
  onMonthChange: (date: TDate) => void;
  textMap: ICalendarTextMap;
}

export const CalendarHeader = forwardRef<HTMLDivElement, ICalendarHeaderProps>((props: ICalendarHeaderProps, ref) => {
  const { id, months, onViewChange, onMonthChange, textMap } = props;
  const headerCalendarRef = useCreateTestId(id);

  const context = useContext(CalendarContext);
  const {
    previousViewButtonRef,
    previousMonthButtonRef,
    nextMonthButtonRef,
    displayDate,
    minDate,
    maxDate,
    focusedDate,
    setFocusedDate,
    view,
    formatDateForSR,
  } = context;

  const [disablePreviousMonth, setDisablePreviousMonth] = useState(false);
  const [disableNextMonth, setDisableNextMonth] = useState(false);

  const previousViewAriaLabel = useMemo(() => {
    const stringArray = [];
    let previousView;
    let srLabel;

    switch (view) {
      case 'day':
        srLabel = displayDate && formatDateForSR('month', displayDate);
        previousView = textMap.previousViewIsMonthLabel;
        break;
      case 'month':
        srLabel = displayDate && formatDateForSR('year', displayDate);
        previousView = textMap.previousViewIsYearLabel;
        break;
    }
    if (srLabel) stringArray.push(srLabel);
    stringArray.push(previousView);
    return stringArray.join(', ');
  }, [view, displayDate, textMap, formatDateForSR]);

  const headerTitle = useMemo(() => {
    if (!displayDate) return '';
    return formatHeaderTitle(view, displayDate, months, minDate, maxDate);
  }, [view, months, displayDate, minDate, maxDate]);

  const hasReachedMinDate = isBefore(endOfMonth(getPreviousMonth(displayDate)), minDate);
  const hasReachedMaxDate = isAfter(startOfMonth(getNextMonth(displayDate)), maxDate);

  // Will RTL affect this 'left' direction?
  const onSelectPreviousMonth = useCallback(() => {
    const previousMonth = getPreviousMonth(displayDate);
    onMonthChange(previousMonth);

    if (focusedDate) {
      const newFocusedDate = getPreviousMonth(focusedDate);
      const constrainedDate = getDateConstraints(newFocusedDate, minDate, maxDate);
      setFocusedDate?.(constrainedDate);
    }
  }, [displayDate, minDate, maxDate, focusedDate, setFocusedDate, onMonthChange]);

  // Will RTL affect this 'right' direction?
  const onSelectNextMonth = useCallback(() => {
    const nextMonth = getNextMonth(displayDate);
    onMonthChange(nextMonth);

    if (focusedDate) {
      const newFocusedDate = getNextMonth(focusedDate);
      const constrainedDate = getDateConstraints(newFocusedDate, minDate, maxDate);
      setFocusedDate?.(constrainedDate);
    }
  }, [displayDate, minDate, maxDate, focusedDate, setFocusedDate, onMonthChange]);

  /** Fix to avoid focus disappearing on header month button once min/max date month reached */
  useEffect(() => {
    // Once disabled is set to true, focus from header month button is lost (after recent iconButtonV2 updates)
    // Workaround to avoid setting hasReachedMinDate/hasReachedMaxDate on headerMonth buttons disabled prop
    setDisablePreviousMonth(hasReachedMinDate);
    setDisableNextMonth(hasReachedMaxDate);
    if (hasReachedMinDate && previousMonthButtonRef.current === document.activeElement) {
      nextMonthButtonRef.current?.focus();
    } else if (hasReachedMaxDate && nextMonthButtonRef.current === document.activeElement) {
      previousMonthButtonRef.current?.focus();
    }
  }, [hasReachedMinDate, hasReachedMaxDate, previousMonthButtonRef, nextMonthButtonRef]);

  useEffect(() => {
    setTimeout(() => {
      clearAnnouncer('assertive');
      announce(headerTitle, 'assertive');
    });
  }, [headerTitle]);

  return (
    <div id={id} ref={mergeRefs([headerCalendarRef, ref])} className={styles.evrCalendarHeader}>
      <div className={classnames(styles.headerCell)}>
        {view === 'year' ? (
          <div id={`${id}-title`} className={classnames('evrBodyText2', 'evrBold', styles.headerYearView)}>
            {headerTitle}
          </div>
        ) : (
          <Button
            ref={previousViewButtonRef}
            id={`${id}-previous-view-button`}
            label={headerTitle}
            startIcon="arrowLeftSmall"
            variant="tertiary"
            onClick={() => onViewChange(view)}
            ariaLabel={previousViewAriaLabel}
          />
        )}
      </div>
      {view === 'day' && (
        <div className={styles.headerCell}>
          <IconButton
            ref={previousMonthButtonRef}
            id={`${id}-previous-month-button`}
            iconName="chevronLeftSmall"
            variant="tertiary"
            disabled={disablePreviousMonth}
            onClick={() => onSelectPreviousMonth()}
            ariaLabel={textMap.previousMonthButtonLabel}
          />
          <IconButton
            ref={nextMonthButtonRef}
            id={`${id}-next-month-button`}
            iconName="chevronRightSmall"
            variant="tertiary"
            disabled={disableNextMonth}
            onClick={() => onSelectNextMonth()}
            ariaLabel={textMap.nextMonthButtonLabel}
          />
        </div>
      )}
    </div>
  );
});

CalendarHeader.displayName = 'CalendarHeader';
