import { useContext } from 'react';

import { TCalendarView, TDate } from '../../../types';
import {
  addDays,
  addMonths,
  addYears,
  endOfMonth,
  endOfYear,
  isAfter,
  isBefore,
  isSameDay,
  isSameYear,
  startOfDay,
  startOfMonth,
  startOfYear,
  subMonths,
} from '../../../utils/date-utils';
import { CalendarContext, ICalendarContext } from '../calendar-context';
import {
  checkIsDateDisabled,
  findClosestEnabledDate,
  getDateConstraints,
  getNextMonth,
  getPreviousMonth,
} from '../calendar-helpers';

export interface UseFocusedStateInput {
  isDateDisabled: boolean;
  focusedDate?: TDate | null;
  minDate: TDate;
  maxDate: TDate;
  view: TCalendarView;
}

export interface CalendarState {
  focusEnter: () => void;
  focusArrowUp: () => TDate;
  focusArrowDown: () => TDate;
  focusArrowLeft: () => TDate;
  focusArrowRight: () => TDate;
  focusHome: () => TDate;
  focusEnd: () => TDate;
  focusPageUp: () => TDate;
  focusPageDown: () => TDate;
}

export const useCalendarFocus = (): CalendarState | any => {
  const context = useContext(CalendarContext);
  const { focusedDate, minDate, maxDate, view } = context;
  const isDateDisabled = checkIsDateDisabled(view, minDate, maxDate);

  if (!focusedDate) return;
  return {
    focusArrowUp() {
      switch (view) {
        case 'year':
          return addYears(focusedDate, -4);
        case 'month':
          return addMonths(focusedDate, -4);
        case 'day': {
          const newFocusedDay = addDays(focusedDate, -7);
          const closestDayToFocus = findClosestEnabledDate({
            date: newFocusedDay,
            minDate: minDate,
            maxDate: focusedDate,
            isDateDisabled,
          });
          return closestDayToFocus || newFocusedDay;
        }
      }
    },
    focusArrowDown() {
      switch (view) {
        case 'year':
          return addYears(focusedDate, 4);
        case 'month':
          return addMonths(focusedDate, 4);
        case 'day': {
          const newFocusedDay = addDays(focusedDate, 7);
          const closestDayToFocus = findClosestEnabledDate({
            date: newFocusedDay,
            minDate: focusedDate,
            maxDate: maxDate,
            isDateDisabled,
          });
          return closestDayToFocus || newFocusedDay;
        }
      }
    },
    focusArrowLeft() {
      switch (view) {
        case 'year':
          return addYears(focusedDate, -1);
        case 'month':
          return addMonths(focusedDate, -1);
        case 'day': {
          const newFocusedDay = addDays(focusedDate, -1);
          const previousAvailableMonth = getPreviousMonth(focusedDate);
          const closestDayToFocus = findClosestEnabledDate({
            date: newFocusedDay,
            minDate: startOfMonth(previousAvailableMonth),
            maxDate: newFocusedDay,
            isDateDisabled,
          });
          return closestDayToFocus || newFocusedDay;
        }
      }
    },
    focusArrowRight() {
      switch (view) {
        case 'year':
          return addYears(focusedDate, 1);
        case 'month':
          return addMonths(focusedDate, 1);
        case 'day': {
          const newFocusedDay = addDays(focusedDate, 1);
          const nextAvailableMonth = startOfMonth(getNextMonth(focusedDate));
          const closestDayToFocus = findClosestEnabledDate({
            date: newFocusedDay,
            minDate: newFocusedDay,
            maxDate: endOfMonth(nextAvailableMonth),
            isDateDisabled,
          });
          return closestDayToFocus || newFocusedDay;
        }
      }
    },
    focusPageUp() {
      switch (view) {
        case 'day': {
          const previousMonth = getPreviousMonth(focusedDate);
          const pageUpFocusedDay = isBefore(previousMonth, minDate) ? minDate : previousMonth;
          return pageUpFocusedDay;
        }
      }
    },
    focusPageDown() {
      switch (view) {
        case 'day': {
          const nextMonth = getNextMonth(focusedDate);
          const pageDownFocusedDay = isAfter(nextMonth, maxDate) ? maxDate : nextMonth;
          return pageDownFocusedDay;
        }
      }
    },
    focusHome() {
      switch (view) {
        case 'year':
          return startOfYear(minDate);
        case 'month':
          if (isSameYear(minDate, focusedDate)) return startOfMonth(minDate);
          return startOfYear(focusedDate);
        case 'day':
          return getDateConstraints(startOfMonth(focusedDate), minDate, maxDate);
      }
    },
    focusEnd() {
      switch (view) {
        case 'year':
          return startOfYear(maxDate);
        case 'month':
          if (isSameYear(maxDate, focusedDate)) return startOfMonth(maxDate);
          return startOfMonth(endOfYear(focusedDate));
        case 'day':
          return getDateConstraints(endOfMonth(focusedDate), minDate, maxDate);
      }
    },
  };
};

export const validateFocus = (currentDate: TDate, context: ICalendarContext): boolean => {
  const { focusedDate, minDate, maxDate, view } = context;

  const today = startOfDay(new Date());
  let initialFocusedCell;

  switch (view) {
    case 'year':
      if (focusedDate) {
        initialFocusedCell = startOfYear(focusedDate);
      } else {
        initialFocusedCell = startOfYear(today);
      }
      return isSameDay(initialFocusedCell, currentDate);

    case 'month':
      if (
        focusedDate &&
        Boolean(!isBefore(endOfMonth(focusedDate), minDate) && !isAfter(startOfMonth(focusedDate), maxDate))
      ) {
        initialFocusedCell = startOfMonth(focusedDate);
      } else if (focusedDate && Boolean(isBefore(startOfMonth(focusedDate), minDate))) {
        initialFocusedCell = addMonths(focusedDate, 1);
      } else if (focusedDate && Boolean(isAfter(endOfMonth(focusedDate), maxDate))) {
        initialFocusedCell = subMonths(focusedDate, 1);
      } else {
        initialFocusedCell = startOfMonth(today);
      }
      return isSameDay(initialFocusedCell, currentDate);

    case 'day':
      if (focusedDate) {
        const dayFocus = getDateConstraints(focusedDate, minDate, maxDate);
        initialFocusedCell = dayFocus;
      } else {
        initialFocusedCell = today;
      }
      return isSameDay(initialFocusedCell, currentDate);
  }
};
