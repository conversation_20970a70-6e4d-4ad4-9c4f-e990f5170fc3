import { useCallback, useEffect, useState } from 'react';

import { TCalendarView } from '../../../types';

export interface UseViewsInput {
  openTo?: TCalendarView;
}
export interface UseViewsResponse {
  view: TCalendarView;
  setView: (view: TCalendarView) => void;
  goToView: (view: TCalendarView) => void;
}

/** Calendar Views */
export const useCalendarViews = (props: UseViewsInput): UseViewsResponse => {
  const { openTo } = props;
  const [calendarView, setCalendarView] = useState<TCalendarView>(openTo || 'day');

  useEffect(() => {
    if (openTo) setCalendarView(openTo);
  }, [openTo]);

  const handleChangeView = useCallback((newView: TCalendarView) => {
    setCalendarView(newView);
  }, []);

  const goToView = useCallback(
    (view: TCalendarView) => {
      handleChangeView(view);
    },
    [handleChangeView]
  );

  return {
    view: calendarView,
    setView: handleChangeView,
    goToView,
  };
};
