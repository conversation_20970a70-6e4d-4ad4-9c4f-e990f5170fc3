import React from 'react';
import { render, screen } from '@testing-library/react';

import { CalendarCell } from './calendar-cell';
import { IIconProps } from '../../icon';

describe('[CalendarCell]', () => {
  const labelText = 'CalendarCell';
  const date = new Date();
  const getCellLabel = () => screen.getByText(labelText);
  const getIconContainer = () => screen.getByTestId(`calendar-${date.valueOf()}-icon-container`);

  it('should render cell content', () => {
    render(
      <table>
        <tbody>
          <tr>
            <CalendarCell id={''} value={date} displayValue={labelText} />
          </tr>
        </tbody>
      </table>
    );
    expect(getCellLabel()).toBeTruthy();
  });

  it('should render cell content with one icon', () => {
    const icons: IIconProps[] = [
      {
        name: 'globe',
        id: 'calendar-cell-globe-icon',
        testId: 'calendar-cell-globe-icon',
      },
    ];

    render(
      <table>
        <tbody>
          <tr>
            <CalendarCell id={'calendar'} value={date} icons={icons} displayValue={labelText} />
          </tr>
        </tbody>
      </table>
    );

    const iconContainer = screen.getByTestId(`calendar-${date.valueOf()}-icon-container`);
    expect(getCellLabel()).toBeTruthy();
    expect(iconContainer.querySelectorAll('svg')?.length).toBe(1);
    expect(iconContainer.querySelector('svg[data-evr-name="globe"]')).toBeInTheDocument();
  });

  it('should render cell content with two icons', () => {
    const icons = [
      {
        name: 'time',
        id: 'calendar-cell-time-icon',
        testId: 'calendar-cell-time-icon',
      },
      {
        name: 'check',
        fill: '--evr-content-primary-default',
      },
    ] as IIconProps[];
    render(
      <table>
        <tbody>
          <tr>
            <CalendarCell id={'calendar'} value={date} icons={icons} displayValue={labelText} />
          </tr>
        </tbody>
      </table>
    );

    const iconContainer = getIconContainer();
    expect(getCellLabel()).toBeTruthy();
    expect(iconContainer.querySelectorAll('svg')?.length).toBe(2);
    expect(iconContainer.querySelector('svg[data-evr-name="time"]')).toBeInTheDocument();
    expect(iconContainer.querySelector('svg[data-evr-name="check"]')).toBeInTheDocument();
  });

  it('should render cell content with one icon with number when more than 2 icons', () => {
    const icons = [
      {
        name: 'time',
        id: 'calendar-cell-time-icon',
        testId: 'calendar-cell-time-icon',
      },
      {
        name: 'check',
        fill: '--evr-content-primary-default',
      },
      {
        name: 'timeAdd',
      },
      {
        name: 'accessibility',
        fill: '--evr-content-primary-default',
      },
    ] as IIconProps[];
    render(
      <table>
        <tbody>
          <tr>
            <CalendarCell id={'calendar'} value={date} icons={icons} displayValue={labelText} />
          </tr>
        </tbody>
      </table>
    );

    const iconContainer = getIconContainer();
    expect(getCellLabel()).toBeTruthy();
    expect(iconContainer.querySelectorAll('svg')?.length).toBe(1);
    expect(iconContainer.querySelector('svg[data-evr-name="time"]')).toBeInTheDocument();
    expect(iconContainer).toHaveTextContent('+3');
  });
});
