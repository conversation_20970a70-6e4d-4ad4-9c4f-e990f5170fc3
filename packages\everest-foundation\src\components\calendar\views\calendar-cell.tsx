import React, { forwardRef, useContext, useMemo } from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { TDate } from '../../../types';
import { generateId } from '../../../utils';
import { Icon, IIconProps } from '../../icon';
import { CalendarContext } from '../calendar-context';

import styles from '../calendar.module.scss';

export interface ICalendarCellProps {
  id: string;
  value: TDate;
  displayValue: number | string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  onChange?: (date: TDate) => void;
  onMonthChange?: (date: TDate) => void;
  isToday?: boolean;
  isSelected?: boolean;
  isDisabled?: boolean;
  isOutsideCurrentMonth?: boolean;
  isFocusable?: boolean;
  icons?: IIconProps[];
}

export const CalendarCell = forwardRef<HTMLInputElement, ICalendarCellProps>((props: ICalendarCellProps, ref) => {
  const {
    id,
    value,
    displayValue,
    onKeyDown,
    onChange,
    isToday = false,
    isSelected = false,
    isDisabled = false,
    isOutsideCurrentMonth = false,
    isFocusable = false,
    icons,
  } = props;

  const context = useContext(CalendarContext);
  const { view, disableDefaultToday, formatDateForSR } = context;

  const isMonthOrYear = view === 'month' || view === 'year';

  const handleClick = () => {
    if (isDisabled) return;
    onChange?.(value);
  };

  const [ariaLabel, valueKey] = useMemo(() => {
    const stringArray = [];
    if (value) stringArray.push(formatDateForSR(view, value));
    return [stringArray.join(', '), value.valueOf()];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const renderIcons = () => {
    if (!icons || icons.length === 0) return null;
    if (icons?.length > 2) {
      const icon = icons[0];
      const iconId = icon.id || `${id}-${value.valueOf()}-icon-${generateId()}`;
      const iconTestId = icon.testId || `calendar-${value.valueOf()}-icon-container`;
      return (
        <div className={styles.moreThanTwoEvents} key={`${id}-${valueKey}-icon`}>
          <Icon id={iconId} testId={iconTestId} name={icon.name} size="sm" fill={icon.fill} />
          <span>+{icons.length - 1}</span>
        </div>
      );
    }
    return icons.map((icon) => {
      const iconId = icon.id || `${id}-${valueKey}-icon-${generateId()}`;
      const iconTestId = icon.testId || `${iconId}-test-id`;
      return <Icon id={iconId} testId={iconTestId} key={iconId} name={icon.name} size="sm" fill={icon.fill} />;
    });
  };

  return (
    <td
      role="gridcell"
      aria-selected={isSelected}
      aria-disabled={isDisabled || isOutsideCurrentMonth}
      className={classnames({
        [styles.dayCell]: view === 'day',
        [styles.monthYearCell]: isMonthOrYear,
      })}
    >
      <div className={styles.dayCellAndIconContainer}>
        <FocusRing>
          <span
            ref={ref}
            id={`${id}-${valueKey}`}
            role="button"
            tabIndex={isFocusable ? 0 : -1}
            aria-label={ariaLabel}
            className={classnames('evrBodyText2', {
              [styles.dayCellSpan]: view === 'day',
              [styles.monthYearCellSpan]: isMonthOrYear,
              [styles.isOutsideMonth]: isOutsideCurrentMonth,
              [styles.isToday]: isToday && !disableDefaultToday && !isDisabled,
              [styles.isSelected]: isSelected,
              [styles.isDisabled]: isDisabled,
            })}
            onClick={handleClick}
            onKeyDown={onKeyDown}
          >
            <span
              className={classnames({
                [styles.monthYearCellSpanText]: isMonthOrYear,
              })}
            >
              {displayValue}
            </span>
          </span>
        </FocusRing>
        {icons && icons.length > 0 && (
          <div
            data-testid={`${id}-${valueKey}-icon-container`}
            className={classnames('evrBodyText2', styles.eventIcons)}
          >
            {renderIcons()}
          </div>
        )}
      </div>
    </td>
  );
});

CalendarCell.displayName = 'CalendarCell';
