import React, { forwardRef, useCallback, useContext, useLayoutEffect, useMemo, useRef } from 'react';
import classnames from 'classnames';

import { CalendarCell } from './calendar-cell';
import { ICalendarEvent, IFormattedWeekday, TDate, TStartDayOfWeekRange } from '../../../types';
import { generateId, mergeRefs, useCreateTestId } from '../../../utils';
import {
  formatISO,
  getDate,
  getMonth,
  getWeeksInMonth,
  getYear,
  isAfter,
  isBefore,
  isSameDay,
  isSameMonth,
  isToday,
  startOfDay,
} from '../../../utils/date-utils';
import { CalendarContext } from '../calendar-context';
import { checkIsDateDisabled, getDatesInWeek, getNextMonth, getPreviousMonth } from '../calendar-helpers';
import { useCalendarFocus, validateFocus } from '../hooks/use-calendar-focus';

import styles from '../calendar.module.scss';

export interface ICalendarDayProps {
  id: string;
  selectedDays?: (TDate | null | undefined)[];
  weekdays: IFormattedWeekday[];
  startDayOfWeek?: TStartDayOfWeekRange;
  onChange?: (date: TDate) => void;
  onMonthChange: (date: TDate) => void;
  monthEventMap?: Map<string, ICalendarEvent>;
}

export const CalendarDay = forwardRef<HTMLTableElement, ICalendarDayProps>((props: ICalendarDayProps, ref) => {
  const { id, selectedDays, weekdays, startDayOfWeek = 0, monthEventMap, onChange, onMonthChange } = props;

  const dayCalendarRef = useCreateTestId(id);

  const context = useContext(CalendarContext);
  const {
    previousMonthButtonRef,
    nextMonthButtonRef,
    displayDate,
    minDate,
    maxDate,
    disableDefaultToday,
    focusedDate,
    setFocusedDate,
    isFocusWithinComponent,
  } = context;

  const isDateDisabled = checkIsDateDisabled('day', minDate, maxDate);

  // use ref to prevent unnecessary renders
  const today = useRef<Date>(startOfDay(new Date()));

  const weeksInMonth = useMemo(
    () =>
      getWeeksInMonth(displayDate, {
        weekStartsOn: startDayOfWeek,
      }),
    [displayDate, startDayOfWeek]
  );

  const validSelectedDays = useMemo(
    () => selectedDays?.filter((day) => !!day).map((day) => startOfDay(day as Date)),
    [selectedDays]
  );

  const displayedWeekdays = useMemo(() => {
    return weekdays.map((day) => (
      <th
        key={day.dayOfWeek}
        className={classnames('evrBodyText2', styles.dayOfWeekCell)}
        scope="col"
        role="columnheader"
      >
        <span aria-hidden="true">{day.short}</span>
        <span className={styles.visuallyHidden}>{day.long}</span>
      </th>
    ));
  }, [weekdays]);

  const navigateToCell = (newDate: TDate) => {
    if (focusedDate && !isDateDisabled(newDate)) {
      if (!isSameMonth(displayDate, newDate)) {
        if (isBefore(newDate, displayDate)) {
          onMonthChange?.(getPreviousMonth(displayDate));
        } else {
          onMonthChange?.(getNextMonth(displayDate));
        }
      }
      setFocusedDate?.(newDate);
    }
  };

  const focusState = useCalendarFocus();

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!focusedDate) return;
    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (isDateDisabled(focusedDate)) return;
        navigateToCell(focusedDate);
        onChange?.(focusedDate);
        break;
      case 'ArrowUp':
        e.preventDefault();
        navigateToCell(focusState.focusArrowUp());
        break;
      case 'ArrowDown':
        e.preventDefault();
        navigateToCell(focusState.focusArrowDown());
        break;
      case 'ArrowLeft': {
        e.preventDefault();
        navigateToCell(focusState.focusArrowLeft());
        break;
      }
      case 'ArrowRight': {
        e.preventDefault();
        navigateToCell(focusState.focusArrowRight());
        break;
      }
      case 'Home':
        e.preventDefault();
        navigateToCell(focusState.focusHome());
        break;
      case 'End':
        e.preventDefault();
        navigateToCell(focusState.focusEnd());
        break;
      case 'PageUp':
        e.preventDefault();
        navigateToCell(focusState.focusPageUp());
        break;
      case 'PageDown':
        e.preventDefault();
        navigateToCell(focusState.focusPageDown());
        break;
      default:
        break;
    }
  };

  const handleFocus = useCallback(() => {
    let initialFocusedCell;
    switch (true) {
      case focusedDate && !isBefore(focusedDate, minDate) && !isAfter(focusedDate, maxDate):
        initialFocusedCell = document.getElementById(`${id}-${startOfDay(focusedDate as TDate).valueOf()}`);
        break;

      case focusedDate && Boolean(isBefore(focusedDate, minDate)):
        setFocusedDate?.(minDate);
        break;

      case focusedDate && Boolean(isAfter(focusedDate, maxDate)):
        setFocusedDate?.(maxDate);
        break;

      default:
        initialFocusedCell = document.getElementById(`${id}-${today.current.valueOf()}`);
        setFocusedDate?.(today.current);
        break;
    }
    initialFocusedCell && initialFocusedCell.focus();
    // Include today.current to update when todays date changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focusedDate, setFocusedDate, minDate, maxDate, today.current, id]);

  useLayoutEffect(() => {
    // TODO test just before midnight, and then just after midnight
    // renders different days?
    if (!isSameDay(today.current, new Date())) {
      today.current = startOfDay(new Date());
    }
  });

  /** Set focus on cell on initial load */
  useLayoutEffect(() => {
    const isHeaderFocused =
      previousMonthButtonRef.current === document.activeElement ||
      nextMonthButtonRef.current === document.activeElement;
    if (isFocusWithinComponent && !isHeaderFocused) handleFocus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focusedDate, displayDate, disableDefaultToday, isFocusWithinComponent, handleFocus]);

  return (
    <table id={id} ref={mergeRefs([dayCalendarRef, ref])} onFocus={handleFocus} className={classnames(styles.table)}>
      <thead>
        <tr className={styles.dayOfWeekHeader}>{displayedWeekdays}</tr>
      </thead>
      <tbody>
        {[...new Array(weeksInMonth).keys()].map((weekIndex) => (
          <tr
            key={`${id}-${getMonth(displayDate)}-${getYear(displayDate)}-week-${weekIndex}`}
            className={classnames({ [styles.calendarDayRow]: !!monthEventMap?.size })}
          >
            {getDatesInWeek(weekIndex, weekdays, startDayOfWeek, displayDate).map((date) => {
              if (date) {
                const isSelected = validSelectedDays?.some((selectedDay) => isSameDay(selectedDay, date));
                const isOutsideCurrentMonth = getMonth(date) !== getMonth(displayDate);
                const isFocusable = validateFocus(date, context);
                const calendarEvent = monthEventMap?.get(formatISO(date, { representation: 'date' }));

                return (
                  <CalendarCell
                    id={id}
                    key={date?.toString()}
                    value={date}
                    displayValue={getDate(date)}
                    onChange={onChange}
                    onMonthChange={onMonthChange}
                    onKeyDown={handleKeyDown}
                    isOutsideCurrentMonth={isOutsideCurrentMonth}
                    isToday={isToday(date)}
                    isSelected={isSelected}
                    isDisabled={isDateDisabled(date)}
                    isFocusable={isFocusable}
                    icons={calendarEvent?.eventIcons}
                  />
                );
              }
              return (
                <td key={`${id}-${getMonth(displayDate)}-${getYear(displayDate)}-week-${weekIndex}-${generateId()}`} />
              );
            })}
          </tr>
        ))}
      </tbody>
    </table>
  );
});

CalendarDay.displayName = 'CalendarDay';
