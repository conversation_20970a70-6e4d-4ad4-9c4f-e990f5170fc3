import React, { forwardRef, useCallback, useContext, useLayoutEffect, useMemo, useRef } from 'react';
import classnames from 'classnames';

import { CalendarCell } from './calendar-cell';
import { TDate } from '../../../types';
import { mergeRefs, useCreateTestId } from '../../../utils';
import {
  addMonths,
  endOfMonth,
  getMonth,
  getYear,
  isAfter,
  isBefore,
  isSameDay,
  isSameMonth,
  isSameYear,
  setMonth,
  startOfDay,
  startOfMonth,
  subMonths,
} from '../../../utils/date-utils';
import { CalendarContext } from '../calendar-context';
import { checkIsDateDisabled, getSplitRows } from '../calendar-helpers';
import { useCalendarFocus, validateFocus } from '../hooks/use-calendar-focus';

import styles from '../calendar.module.scss';

export interface ICalendarMonthProps {
  id: string;
  date?: TDate;
  months: string[];
  onChange?: (date: TDate) => void;
}

export const CalendarMonth = forwardRef<HTMLTableElement, ICalendarMonthProps>((props: ICalendarMonthProps, ref) => {
  const { id, date, months, onChange } = props;

  const monthCalendarRef = useCreateTestId(id);

  const context = useContext(CalendarContext);
  const { displayDate, minDate, maxDate, focusedDate, setFocusedDate, isFocusWithinComponent } = context;

  const isDateDisabled = checkIsDateDisabled('month', minDate, maxDate);

  // use ref to prevent unnecessary renders
  const today = useRef<Date>(startOfDay(new Date()));
  const currentYear = useMemo(() => getYear(displayDate), [displayDate]);

  // Only set focus if next month is not disabled, not outside of min/max range, and in same year
  const navigateToCell = (newDate: TDate) => {
    if (focusedDate && isSameYear(newDate, focusedDate) && !isDateDisabled(newDate)) {
      const validDate = setMonth(displayDate, getMonth(newDate));
      setFocusedDate?.(validDate);
    }
  };

  const focusState = useCalendarFocus();

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!focusedDate) return;
    if (e.key !== 'Escape' && e.key !== 'Tab') e.stopPropagation();
    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (isDateDisabled(focusedDate)) return;
        navigateToCell(focusedDate);
        onChange?.(focusedDate);
        break;
      case 'ArrowUp':
        e.preventDefault();
        navigateToCell(focusState.focusArrowUp());
        break;
      case 'ArrowDown':
        e.preventDefault();
        navigateToCell(focusState.focusArrowDown());
        break;
      case 'ArrowLeft': {
        e.preventDefault();
        navigateToCell(focusState.focusArrowLeft());
        break;
      }
      case 'ArrowRight': {
        e.preventDefault();
        navigateToCell(focusState.focusArrowRight());
        break;
      }
      case 'Home': {
        e.preventDefault();
        navigateToCell(focusState.focusHome());
        break;
      }
      case 'End': {
        e.preventDefault();
        navigateToCell(focusState.focusEnd());
        break;
      }
      default:
        break;
    }
  };

  /** Tracks focusedDate even if user clicks out of calendar */
  const handleFocus = useCallback(() => {
    let initialFocusedCell;
    let currentFocusedDate;
    switch (true) {
      // if focusedDate exists and is within valid range
      case focusedDate &&
        Boolean(!isBefore(endOfMonth(focusedDate), minDate) && !isAfter(startOfMonth(focusedDate), maxDate)):
        initialFocusedCell = document.getElementById(`${id}-${startOfMonth(focusedDate as TDate).valueOf()}`);
        break;
      // if focusedDate exists but comes before minDate, grab closest enabled month
      case focusedDate && Boolean(isBefore(startOfMonth(focusedDate), minDate)):
        currentFocusedDate = addMonths(focusedDate as TDate, 1);
        setFocusedDate?.(currentFocusedDate);
        break;
      // if focusedDate exists but after maxDate, grab closest enabled month
      case focusedDate && Boolean(isAfter(endOfMonth(focusedDate), maxDate)):
        currentFocusedDate = subMonths(focusedDate as TDate, 1);
        setFocusedDate?.(currentFocusedDate);
        break;
      // else focus on current month
      default:
        initialFocusedCell = document.getElementById(`${id}-${startOfMonth(today.current).valueOf()}`);
        setFocusedDate?.(today.current);
        break;
    }
    initialFocusedCell && initialFocusedCell.focus();
    // Include today.current to update when todays date changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focusedDate, setFocusedDate, minDate, maxDate, today.current, id]);

  useLayoutEffect(() => {
    // TODO test just before midnight end of month, and then just after midnight end of month
    // renders different months?
    if (!isSameDay(today.current, new Date())) {
      today.current = startOfDay(new Date());
    }
  });

  /** Set focus on cell on initial load */
  useLayoutEffect(() => {
    isFocusWithinComponent && handleFocus();
    // When value selected and user tries key pressing calendar icon button, calendar overlay will quickly open then close
    // To fix this issue -> add displayDate to dependencies
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focusedDate, displayDate, isFocusWithinComponent]);

  return (
    <table id={id} ref={mergeRefs([monthCalendarRef, ref])} onFocus={handleFocus} className={classnames(styles.table)}>
      <tbody>
        {getSplitRows(months, 4).map((row) => (
          <tr key={`${id}-${row[0]}-to-${row[row.length - 1]}-${currentYear}`} className={styles.monthYearRow}>
            {row.map((month) => {
              const monthIndex = months.indexOf(month as string);
              const newDate = new Date(currentYear, monthIndex, 1);

              return (
                <CalendarCell
                  id={id}
                  key={`${monthIndex}-${currentYear}`}
                  value={newDate}
                  displayValue={month}
                  onChange={onChange}
                  onKeyDown={handleKeyDown}
                  isToday={isSameMonth(today.current, newDate)}
                  isSelected={date ? isSameMonth(date, newDate) : false}
                  isDisabled={isDateDisabled(newDate)}
                  isFocusable={validateFocus(newDate, context)}
                />
              );
            })}
          </tr>
        ))}
      </tbody>
    </table>
  );
});

CalendarMonth.displayName = 'CalendarMonth';
