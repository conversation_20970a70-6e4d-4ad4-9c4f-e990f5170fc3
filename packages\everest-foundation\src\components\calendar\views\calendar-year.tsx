import React, { forwardRef, useContext, useLayoutEffect, useRef } from 'react';
import classnames from 'classnames';

import { CalendarCell } from './calendar-cell';
import { TCalendarView, TDate } from '../../../types';
import { mergeRefs, useCreateTestId } from '../../../utils';
import {
  getYear,
  isSameDay,
  isSameYear,
  setYear,
  startOfDay,
  startOfMonth,
  startOfYear,
} from '../../../utils/date-utils';
import { CalendarContext } from '../calendar-context';
import { getSplitRows } from '../calendar-helpers';
import { useCalendarFocus, validateFocus } from '../hooks/use-calendar-focus';

import styles from '../calendar.module.scss';

export interface ICalendarYearProps {
  id: string;
  date?: TDate;
  years: number[];
  onChange?: (date: TDate) => void;
  onViewChange: (view: TCalendarView) => void;
}

export const CalendarYear = forwardRef<HTMLTableElement, ICalendarYearProps>((props: ICalendarYearProps, ref) => {
  const { id, date, years, onChange, onViewChange } = props;

  const yearCalendarRef = useCreateTestId(id);

  const context = useContext(CalendarContext);
  const { focusedDate, setFocusedDate, displayDate, setDisplayDate } = context;

  // use ref to prevent unnecessary renders
  const today = useRef<Date>(startOfDay(new Date()));

  const navigateToCell = (newDate: TDate) => {
    const newYear = getYear(newDate);

    if (years.includes(newYear)) {
      const validDate = setYear(focusedDate || newDate, newYear);
      setFocusedDate(validDate);
    }
  };

  const focusState = useCalendarFocus();

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!focusedDate) return;
    if (e.key !== 'Escape' && e.key !== 'Tab') e.stopPropagation();

    // Separate onChange behavior for keyHandler
    let yearStart = startOfYear(focusedDate);
    if (focusedDate && isSameYear(focusedDate, displayDate)) {
      yearStart = startOfMonth(focusedDate);
    }

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        navigateToCell(focusedDate);

        onViewChange('month');
        setDisplayDate(startOfYear(focusedDate));
        setFocusedDate(yearStart);
        break;
      case 'ArrowUp':
        e.preventDefault();
        navigateToCell(focusState.focusArrowUp());
        break;
      case 'ArrowDown':
        e.preventDefault();
        navigateToCell(focusState.focusArrowDown());
        break;
      case 'ArrowLeft': {
        e.preventDefault();
        navigateToCell(focusState.focusArrowLeft());
        break;
      }
      case 'ArrowRight': {
        e.preventDefault();
        navigateToCell(focusState.focusArrowRight());
        break;
      }
      case 'Home': {
        e.preventDefault();
        navigateToCell(focusState.focusHome());
        break;
      }
      case 'End': {
        e.preventDefault();
        navigateToCell(focusState.focusEnd());
        break;
      }
      default:
        break;
    }
  };

  useLayoutEffect(() => {
    // TODO test just before midnight end of year, and then just after midnight end of year
    // renders different years?
    if (!isSameDay(today.current, new Date())) {
      today.current = startOfDay(new Date());
    }
  });

  useLayoutEffect(() => {
    if (!focusedDate) {
      setFocusedDate?.(today.current);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useLayoutEffect(() => {
    if (focusedDate) {
      const focusedCell = document.getElementById(`${id}-${startOfYear(focusedDate).valueOf()}`);
      focusedCell && focusedCell.focus();
    }
    // When value selected and user tries key pressing calendar icon button, calendar overlay will quickly open then close
    // To fix this issue -> add displayDate to dependencies
  }, [focusedDate, displayDate, id]);

  return (
    <table id={id} ref={mergeRefs([yearCalendarRef, ref])} className={classnames(styles.table, styles.yearTable)}>
      <tbody>
        {getSplitRows(years, 4).map((row) => (
          <tr key={`${id}-${row[0]}-to-${row[row.length - 1]}`} className={styles.monthYearRow}>
            {row.map((year) => {
              // set each id to Jan 1st for each year
              const newDate = setYear(startOfYear(today.current), year as number);

              return (
                <CalendarCell
                  id={id}
                  key={year}
                  value={newDate}
                  displayValue={year}
                  onChange={onChange}
                  onKeyDown={handleKeyDown}
                  isToday={isSameYear(today.current, newDate)}
                  isSelected={date ? isSameYear(date, newDate) : false}
                  isFocusable={validateFocus(newDate, context)}
                />
              );
            })}
          </tr>
        ))}
      </tbody>
    </table>
  );
});

CalendarYear.displayName = 'CalendarYear';
