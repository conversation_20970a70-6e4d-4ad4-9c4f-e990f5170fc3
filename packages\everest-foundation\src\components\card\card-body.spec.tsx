import * as React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

import { CardBody } from './card-body';
import { Button, Avatar } from '../..';

describe('[CardFooterAction]', () => {
  const testId = 'footer-test-id';

  it('should not have accessibility violations', async () => {
    const { container } = render(
      <CardBody testId={testId} id="card-body-id">
        <Button id="card-body-button-enter" label="Enter" />
        <Button id="card-body-button-cancel" label="Cancel" />
        <Avatar
          id="card-body-image-avatar"
          size="md"
          src="https://www.linkpicture.com/q/Avatar_2.jpg"
          ariaLabel="avatar"
        />
        <Avatar id="card-body-initials-avatar" initials="ML" size="lg" />
      </CardBody>
    );
    expect(await axe(container)).toHaveNoViolations();
  });
});
