import React, { PropsWithChildren } from 'react';

import { ContainerBodyBase, IContainerBodyBaseProps } from '../container-base/container-body-base';

export type ICardBodyProps = Omit<IContainerBodyBaseProps, 'className' | 'roleType'>;

export const CardBody = ({ children, id, testId }: PropsWithChildren<ICardBodyProps>): JSX.Element => {
  return (
    <ContainerBodyBase id={id} testId={testId} roleType="section">
      {children}
    </ContainerBodyBase>
  );
};
CardBody.displayName = 'CardBody';
