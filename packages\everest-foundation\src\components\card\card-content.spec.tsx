import * as React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

import { CardContent } from './card-content';

describe('[CardContent]', () => {
  const testId = 'body-test-id';
  const detailedText = 'This is a card with so many details on the topic above';

  it('does not have accessibility violations', async () => {
    const { container } = render(
      <CardContent testId={testId} id="card-content-id">
        <p className="evrBodyText">{detailedText}</p>
        <p className="evrBodyText">{detailedText}</p>
        <p className="evrBodyText">{detailedText}</p>
      </CardContent>
    );
    expect(await axe(container)).toHaveNoViolations();
  });
});
