import React, { PropsWithChildren } from 'react';

import { useCreateTestId } from '../../utils';

import styles from './card.module.scss';

export interface ICardContentProps {
  /** Sets `id` attribute. */
  id: string;
  /** Sets `data-testid` for testing purposes. */
  testId?: string;
}

export const CardContent = ({ children, testId, id }: PropsWithChildren<ICardContentProps>): JSX.Element => {
  const dataRef = useCreateTestId(testId);
  return (
    <section className={`${styles.cardContent}`} ref={dataRef} id={id}>
      {children}
    </section>
  );
};
CardContent.displayName = 'CardContent';
