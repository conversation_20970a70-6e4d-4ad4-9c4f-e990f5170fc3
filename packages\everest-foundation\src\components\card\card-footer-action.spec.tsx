import * as React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

import { CardFooterAction } from './card-footer-action';
import { Button } from '../..';

describe('[CardFooterAction]', () => {
  const testId = 'footer-test-id';

  it('should not have accessibility violations', async () => {
    const { container } = render(
      <CardFooterAction testId={testId} id="card-footer-id">
        <Button id="card-footer-button-enter" label="Enter" />
        <Button id="card-footer-button-cancel" label="Cancel" />
      </CardFooterAction>
    );
    expect(await axe(container)).toHaveNoViolations();
  });
});
