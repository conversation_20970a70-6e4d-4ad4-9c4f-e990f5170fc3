import React, { PropsWithChildren } from 'react';

import { ContainerFooterBase, IContainerFooterBaseProps } from '../container-base/container-footer-base';

export type ICardFooterActionProps = Omit<IContainerFooterBaseProps, 'className' | 'roleType'>;

export const CardFooterAction = ({ children, testId, id }: PropsWithChildren<ICardFooterActionProps>): JSX.Element => {
  return (
    <ContainerFooterBase id={id} testId={testId} roleType="section">
      {children}
    </ContainerFooterBase>
  );
};
CardFooterAction.displayName = 'CardFooterAction';
