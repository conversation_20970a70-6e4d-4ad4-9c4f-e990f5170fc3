import * as React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import { CardHeader } from './card-header';

describe('[CardHeader]', () => {
  const id = 'card-header-id';
  const testId = 'card-header-test-id';
  const headingText = 'My Card Header';
  const detailedText = 'This is a card with so many details on the topic above';

  it('does not have accessibility violations', async () => {
    const { container } = render(<CardHeader id={id} testId={testId} title={headingText} description={detailedText} />);
    expect(await axe(container)).toHaveNoViolations();
  });

  it('should render without issues', () => {
    render(<CardHeader id={id} testId={testId} title={headingText} description={detailedText} />);
    expect(screen.getByText(detailedText)).toBeInTheDocument();
    expect(screen.getByText(headingText)).toBeInTheDocument();
  });

  it('should render with a subtitle', () => {
    const subtitle = 'This is a subtitle';
    render(<CardHeader id={id} testId={testId} title={headingText} description={detailedText} subtitle={subtitle} />);
    expect(screen.getByText(subtitle)).toBeInTheDocument();
  });

  it('should render with an icon', () => {
    const iconName = 'add';
    render(<CardHeader id={id} testId={testId} title={headingText} description={detailedText} iconName={iconName} />);
    expect(screen.getByTestId(`${testId}-icon`)).toBeInTheDocument();
  });
});
