import React from 'react';

import { ContainerHeaderBase, IContainerHeaderBaseProps } from '../container-base/container-header-base';
import { Icon, TIconName } from '../icon';

import styles from './card.module.scss';

export interface ICardHeaderProps extends Omit<IContainerHeaderBaseProps, 'className' | 'roleType'> {
  /** Sets title on card header. */
  title?: string;
  /** Sets description on card header. */
  description?: string;
  /** Sets action item on card header. */
  action?: React.ReactNode;
  iconName?: TIconName;
  subtitle?: string;
}

export const CardHeader = ({
  id,
  testId,
  minHeight,
  title,
  description,
  action,
  iconName,
  subtitle,
}: ICardHeaderProps): JSX.Element => {
  return (
    <ContainerHeaderBase id={id} testId={testId} minHeight={minHeight} className={styles.cardHeader} roleType="section">
      {title && (
        <section className={styles.cardHeaderTitle}>
          <section className={styles.cardHeaderSection}>
            {iconName && (
              <div className={styles.cardHeaderIcon}>
                {/* TODO: Eventually replace Icon with HighlightedIcon.  HighlightedIcon has a wrapper to manage the background color */}
                <Icon name={iconName || 'menu'} testId={testId ? `${testId}-icon` : undefined} />
              </div>
            )}
            <div>
              <h4 className="evrHeading4">{title}</h4>
              {subtitle && <p className="evrBodyText2">{subtitle}</p>}
            </div>
          </section>
          {action}
        </section>
      )}
      {description && (
        <section className={styles.cardHeaderDescription}>
          <p className="evrBodyText1">{description}</p>
        </section>
      )}
    </ContainerHeaderBase>
  );
};

CardHeader.displayName = 'CardHeader';
