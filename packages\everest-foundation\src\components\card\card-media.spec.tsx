import * as React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import { CardMedia } from './card-media';
import { Image } from '../..';

describe('[CardMedia]', () => {
  const givenSrc = 'images/image-sample-1.jpg';
  const givenAlt = 'Mountain during sunset';
  const givenTitle = 'A  mountain picture';
  const id = 'media-id';
  const testId = 'test-id';

  it('should render image with given height', () => {
    render(
      <CardMedia height="200px" testId={testId} id={id}>
        <Image id={`${id}-card-img`} src={givenSrc} alt={givenAlt} title={givenTitle} />
      </CardMedia>
    );
    const mediaBlock = screen.getByTestId(testId);

    expect(getComputedStyle(mediaBlock).height).toBe('200px');
  });

  it(`$CardMedia does not have accessibility violations`, async () => {
    const { container } = render(
      <CardMedia height="200px" testId={testId} id={id}>
        <Image id={`${id}-card-img`} src={givenSrc} alt={givenAlt} title={givenTitle} />
      </CardMedia>
    );
    expect(await axe(container)).toHaveNoViolations();
  });
});
