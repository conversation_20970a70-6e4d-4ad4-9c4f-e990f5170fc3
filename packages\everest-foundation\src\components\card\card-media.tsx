import React, { PropsWithChildren } from 'react';
import classNames from 'classnames';

import { useCreateTestId } from '../../utils';

import styles from './card.module.scss';

export interface ICardMediaProps {
  /** Sets `id` attribute. */
  id: string;
  /** Sets `data-testid` for testing purposes. */
  testId?: string;
  /** Sets height on card media. */
  height?: string;
}

export const CardMedia = ({ children, height, testId, id }: PropsWithChildren<ICardMediaProps>): JSX.Element => {
  const dataRef = useCreateTestId(testId);

  return (
    <section
      ref={dataRef}
      id={id}
      style={{ height }}
      className={classNames(styles.cardMedia, {
        [styles.cardMediaAspectRatio]: !height,
      })}
    >
      {children}
    </section>
  );
};

CardMedia.displayName = 'CardMedia';
