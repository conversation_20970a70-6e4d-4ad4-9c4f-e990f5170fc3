import * as React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import { CardMetadata } from './card-metadata';
import { Tag } from '../tag';
import { TagGroup } from '../tag-group';

describe('[CardMetadata]', () => {
  const id = 'metadata-id';
  const testId = 'metadata-test-id';

  it('should render with no issues', async () => {
    render(
      <CardMetadata id={id} testId={testId}>
        <TagGroup label="Category">
          <Tag label="Admin" />
          <Tag label="Finance" />
        </TagGroup>
      </CardMetadata>
    );
    expect(screen.getByText('Category')).toBeInTheDocument();
  });

  it('should not have accessibility violations', async () => {
    const { container } = render(
      <CardMetadata id={id} testId={testId}>
        <TagGroup label="Category">
          <Tag label="Admin" />
          <Tag label="Finance" />
        </TagGroup>
      </CardMetadata>
    );
    expect(await axe(container)).toHaveNoViolations();
  });
});
