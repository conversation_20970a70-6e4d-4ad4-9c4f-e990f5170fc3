import React, { PropsWithChildren } from 'react';

import { useCreateTestId } from '../../utils';

export interface ICardMetadataProps {
  /** Sets `id` attribute. */
  id: string;
  /** Sets `data-testid` for testing purposes. */
  testId?: string;
  /** Sets height on card metadata. */
  minHeight?: string;
}

export const CardMetadata: React.FC<ICardMetadataProps> = ({
  children,
  id,
  testId,
  minHeight,
}: PropsWithChildren<ICardMetadataProps>) => {
  const dataRef = useCreateTestId(testId);

  return (
    <section ref={dataRef} id={id} style={{ minHeight }}>
      {children}
    </section>
  );
};

CardMetadata.displayName = 'CardMetadata';
