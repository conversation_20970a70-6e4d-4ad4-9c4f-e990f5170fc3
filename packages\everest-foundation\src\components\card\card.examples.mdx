import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { Card } from './card';
import { Button } from '../button';
import { IconButton } from '../icon-button';
import { Image } from '../image';
import { PopoverMenu, PopoverMenuItem } from '../popover-menu';
import { TagGroup } from '../tag-group';
import { Tag } from '../tag';
import { Avatar } from '../avatar';

export const scope = {
  Card,
  Button,
  IconButton,
  Image,
  PopoverMenu,
  PopoverMenuItem,
  TagGroup,
  Tag,
  Avatar,
};

## Usage

Cards are used as a container to host a group of components together to summarize a topic.

### Basic Card with all Nested Child Sections

A card can receive child components. The possible child components are media, heading with/without a context menu button or description text, category with tags, body container for more content and footer for an action button. <br />

export const CardWithSectionsCode = `() => {
    const styles = {
        centerText: {
            display: 'flex',
            flexDirection:'column',
            rowGap: 'var(--evr-spacing-3xs)',
        },
        cardBody: {
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            columnGap: 'var(--evr-spacing-3xs)'
        },
        cardFooter: {
            display: 'flex',
            justifyContent: 'flex-end',
            columnGap: 'var(--evr-spacing-3xs)'
        }
    };    
    const initialState = '';
    const [selectedMenuItemId, setSelectedMenuItemId] = React.useState(initialState);      
    function reducer(action) {
        switch(action) {
            case initialState:
                return;
            case "sunset-new":
                alert("You selected New!");
                break;              
            case "sunset-edit":
                alert("You selected Edit!");
                break;
            case "sunset-delete":
                alert("You selected Delete!");
                break;
            default:
                break;            
        }
        setSelectedMenuItemId(initialState);
    }      
    React.useEffect(()=>{        
        reducer(selectedMenuItemId)
    },[selectedMenuItemId]);
    return (
        <div style={{ width: '18.75rem' }}>
            <Card id='card-with-sections-1' ariaLabel='Mount Everest'>
                <Card.Media id='card-with-sections-media-id'>
                    <Image
                        id='card-with-sections-1-img'
                        alt={'Mount Everest during sunset'}
                        title={'A mountain'}
                        src={'images/image-sample-1.jpg'}
                    />
                </Card.Media>
                <Card.Content id='card-with-sections-content-id'>
                    <Card.Header
                    id='card-with-sections-header-id'
                        title="Mount Everest"
                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        action={
                            <PopoverMenu
                                id="sunset-menu-1"
                                buttonAriaLabel="Popover Action Menu"
                                onChange={({id}) => setSelectedMenuItemId(id)}
                                selectedMenuId={selectedMenuItemId}
                            >
                                <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                            </PopoverMenu>   
                        }
                    />
                    <Card.Metadata id='card-with-sections-category-id'>
                        <TagGroup label="Category">
                            <Tag label="Mountains" />
                            <Tag label="Mount" />
                            <Tag label="Everest" />
                            <Tag label="Beautiful" />
                            <Tag label="Scenic" />
                        </TagGroup>
                    </Card.Metadata>                    
                    <Card.Body id='card-with-sections-body-id'>
                        <section style={styles.cardBody}>
                            <Avatar id='card-with-sections-image-avatar' size='lg' src='https://www.linkpicture.com/q/Avatar_2.jpg' ariaLabel="avatar" />
                            <Avatar id='card-with-sections-initials-avatar' initials="ml" size="lg" ariaLabel="avatar" />
                            <section style={styles.centerText}>
                                <p className='evrCaptionText'>Last Run</p>
                                <p className='evrBodyText1'>05/12/2022 15:45</p>
                            </section>
                        </section>
                    </Card.Body> 
                    <Card.FooterAction id='card-with-sections-footer-id'>
                        <section styles={styles.cardFooter}>
                            <Button id='card-with-sections-run-btn' label="Run" />
                        </section>
                    </Card.FooterAction>
                </Card.Content>                
            </Card>
        </div>
    );
}`;

<CodeExample scope={scope} code={CardWithSectionsCode} />

### Card with Variants section

Card variant is added to change the design of the card. Either an outline or elevated (box-shadow). Styles should not be mixed within a section

export const CardWithVariantCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: '100%'
        },
    };
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card id='card-with-variant-elevated' variant='elevated'>
                    <Card.Media id='card-with-elevated-media-id'>
                        <Image
                            id='card-with-variant-elevated-img'
                            alt={'Mount Everest during sunset'}
                            title={'A mountain'}
                            src={'images/image-sample-1.jpg'}
                        />
                    </Card.Media>
                    <Card.Content id='card-with-elevated-content-id'>
                        <Card.Header
                            title="Elevated Card"
                            id="card-header-id"
                        />
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id='card-with-outlined-1' variant='outlined'>
                    <Card.Media id='card-with-outlined-media-id'>
                        <Image
                            id='card-with-outlined-1-img'
                            alt={'Mount Everest during sunset'}
                            title={'A mountain'}
                            src={'images/image-sample-1.jpg'}
                        />
                    </Card.Media>
                    <Card.Content id='card-with-outlined-content-id'>
                        <Card.Header
                            title="Outlined Card"
                            id="card-header-id"
                        />
                    </Card.Content>              
                </Card>
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={CardWithVariantCode} />

### Card with Media section

Card media section is currently designed to be populated with an image. The fill of this section is adjusted with the height prop. If no height is provided for Media then 16:9 aspect ratio will be enforced.

export const CardWithMediaCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: '100%'
        },
        centerText: {
            textAlign: 'center',
            paddingBottom: 'var(--evr-spacing-sm)',
        }
    };
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card id='card-with-media-1'>
                    <Card.Media id='card-with-media-media-id' height={"200px"}>
                        <Image
                            id='card-with-media-1-img'
                            alt={'Mount Everest during sunset'}
                            title={'A mountain'}
                            src={'images/image-sample-1.jpg'}
                        />
                    </Card.Media>             
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id='card-with-media-2'>
                    <Card.Media id='card-with-media-media-id-2'>
                        <Image
                            id='card-with-media-2-img'
                            alt={'Mount Everest during sunset'}
                            title={'A mountain'}
                            src={'images/image-sample-1.jpg'}
                        />
                    </Card.Media>             
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id='card-with-media-3'>
                    <Card.Media id='card-with-media-media-id-3'>
                        <Image
                            id='card-with-media-3-img'
                            alt={'Mount Everest during sunset'}
                            title={'A mountain'}
                            src={'images/image-sample-1.jpg'}
                        />
                    </Card.Media>
                    <Card.Content id='card-with-media-content-id'>
                        <Card.Header
                            title="Mount Everest"
                            id="card-header-id"
                        />
                    </Card.Content>              
                </Card>
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={CardWithMediaCode} />

### Card with varied Header section

A card header comprises five optional props:

- `title` is the primary card label
- `subtitle` is a secondary label, displayed below 'title'
- `iconName` is the name of the icon to display to the left of `title`
- `action` is a custom React node allowing the user to perform certain actions
- `description` is additional textual detail

export const CardWithHeaderCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: '100%'
        },
        centerText: {
            textAlign: 'center',
            paddingBottom: 'var(--evr-spacing-sm)',
        }
    };
    const initialState = '';
    const [selectedMenuItemId, setSelectedMenuItemId] = React.useState(initialState);      
    function reducer(action) {
        switch(action) {
            case initialState:
                return;
            case "sunset-new":
                alert("You selected New!");
                break;              
            case "sunset-edit":
                alert("You selected Edit!");
                break;
            case "sunset-delete":
                alert("You selected Delete!");
                break;
            default:
                break;            
        }
        setSelectedMenuItemId(initialState);
    }      
    React.useEffect(()=>{        
        reducer(selectedMenuItemId)
    },[selectedMenuItemId]);
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card id='card-with-header-1'>
                    <Card.Content id='card-content-id'>
                        <Card.Header
                            title="Mount Everest"
                            id="card-with-header-header-id"
                        />
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id='card-with-header-2'>
                    <Card.Content id='card-with-header-content-id-2'>
                        <Card.Header
                            title="Mount Everest"
                            id='card-with-header-header-id-2'
                            action={
                                <PopoverMenu
                                    id="sunset-menu-2"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu>   
                            }
                        />
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id="card-with-header-3">
                    <Card.Content id='card-with-header-content-id-3'>
                        <Card.Header
                            title="Mount Everest"
                            id='card-with-header-header-id-3'
                            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                            action={
                                <PopoverMenu
                                    id="sunset-menu-3"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu>   
                            }
                        />
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id="card-with-header-4">
                    <Card.Content id='card-with-header-content-id-4'>
                        <Card.Header
                            title="Mount Everest"
                            subtitle="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque euismod."
                            id='card-with-header-header-id-4'
                            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                            action={
                                <PopoverMenu
                                    id="sunset-menu-4"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu>   
                            }
                        />
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id="card-with-header-5">
                    <Card.Content id='card-with-header-content-id-5'>
                        <Card.Header
                            title="Mount Everest"
                            subtitle="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque euismod."
                            id='card-with-header-header-id-5'
                            iconName="globe"
                            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                            action={
                                <PopoverMenu
                                    id="sunset-menu-5"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu>   
                            }
                        />
                    </Card.Content>              
                </Card>
            </div>            
        </div>
    );
}`;

<CodeExample scope={scope} code={CardWithHeaderCode} />

### Card with Metadata section

The metadata section contains tags to show classification or attributes.

export const CardWithCategoryCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: '100%'
        },
        centerText: {
            textAlign: 'center',
            paddingBottom: 'var(--evr-spacing-sm)',
        }
    };
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card id="card-with-category-1">
                    <Card.Content id='card-with-category-content-id'>
                        <Card.Metadata id='card-category-1'>
                            <TagGroup label="Category">
                            </TagGroup>
                        </Card.Metadata>
                    </Card.Content>              
                </Card>
            </div>            
            <div style={styles.cardContainer}>
                <Card id="card-with-category-2">
                    <Card.Content id='card-with-category-content-id-2'>
                        <Card.Metadata id='card-category-2'>
                            <TagGroup label="Category">
                                <Tag label="Mountains" />
                            </TagGroup>
                        </Card.Metadata>
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id="card-with-category-3">
                    <Card.Content id='card-with-category-content-id-3'>
                        <Card.Metadata id='card-category-3'>
                            <TagGroup label="Category">
                                <Tag label="Mountains" />
                                <Tag label="Mount" />
                                <Tag label="Everest" />
                                <Tag label="Beautiful" />
                                <Tag label="Scenic" />
                            </TagGroup>
                        </Card.Metadata>
                    </Card.Content>              
                </Card>
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={CardWithCategoryCode} />

### Card with Body section

Card body section is designed as a placeholder area for more content.

export const CardWithBodyCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: '100%'
        },
        centerText: {
            display: 'flex',
            flexDirection:'column',
            rowGap: 'var(--evr-spacing-3xs)',
        },
        cardBody: {
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            columnGap: 'var(--evr-spacing-3xs)'
        },
    };
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card id="card-with-body-1">
                    <Card.Content id='card-with-body-content-id-1'>
                        <Card.Body id='card-with-body-body-id-1'>
                            <section style={styles.cardBody}>
                                <Avatar id='card-with-body-image-avatar' size='md' src='https://www.linkpicture.com/q/Avatar_2.jpg' />
                                <Avatar id='card-with-body-initials-avatar' initials="ml" size="md" />
                                <section style={styles.centerText}>
                                    <p className='evrCaptionText'>Last Run</p>
                                    <p className='evrBodyText1'>05/12/2022 15:45</p>
                                </section>
                            </section>
                        </Card.Body>
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id="card-with-body-2">
                    <Card.Content id='card-with-body-content-id-2'>
                        <Card.Body id='card-with-body-body-id-2'>
                            <section style={styles.cardBody}>
                                <Avatar id='card-with-body-picture-avatar-1' onClick={()=>alert('Avatar with picture was clicked')} size='md' src='https://www.linkpicture.com/q/Avatar_2.jpg' ariaLabel="avatar" />
                                <Avatar id='card-with-body-initials-avatar-1' onClick={()=>alert('Avatar with initials was clicked')} initials="ml" size="md" ariaLabel="avatar" />
                                <Avatar id='card-with-body-picture-avatar-2' onClick={()=>alert('Avatar with picture was clicked')} size='md' src='https://www.linkpicture.com/q/Avatar_2.jpg' ariaLabel="avatar" />
                                <Avatar id='card-with-body-initials-avatar-2' onClick={()=>alert('Avatar with initials was clicked')} initials="sk" size="md" ariaLabel="avatar" />
                            </section>
                        </Card.Body>
                    </Card.Content>              
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id="card-with-body-3">
                    <Card.Content id='card-with-body-content-id-3'>
                        <Card.Body id='card-with-body-body-id-3'>
                            <section style={styles.centerText}>
                                <p className='evrCaptionText'>Last Run</p>
                                <p className='evrBodyText1'>05/12/2022 15:45</p>
                            </section>
                        </Card.Body>
                    </Card.Content>              
                </Card>
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={CardWithBodyCode} />

### Card with Footer Action section

The footer action section can be used to include a call to action button for the card. There should only be one button.

export const CardWithFooterCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: '100%'
        },
        cardFooterEnd: {
            display: 'flex',
            justifyContent: 'flex-end'
        },
        cardFooterCenter: {
            display: 'flex',
            justifyContent: 'center'
        }
    };
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card id="card-with-footer-1">
                    <Card.Content id='card-with-footer-content-id-1'>
                        <Card.FooterAction id='card-with-footer-footer-id-1'>
                            <section style={styles.cardFooter}>
                                <Button id='card-with-footer-view-btn-1' label="View" />
                            </section>
                        </Card.FooterAction>
                    </Card.Content>              
                </Card>
            </div>           
            <div style={styles.cardContainer}>
                <Card id='card-with-footer-card-id-2'>
                    <Card.Content id='card-with-footer-content-id-2'>
                        <Card.FooterAction id='card-with-footer-footer-id-2'>
                            <section style={styles.cardFooter}>
                                <Button id='card-with-footer-view-btn-2' label="View" />
                            </section>
                        </Card.FooterAction>
                    </Card.Content>              
                </Card>
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={CardWithFooterCode} />

### Aligned Sections

When multiple Cards are laid out next to each other, it may be best to align the individual sections. This can be achieved using the `minHeight` property on `Card.Header` and `Card.Metadata`.

export const alignedSectionsCardCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: 'auto'
        },
        extraText: {
            display: 'flex',
            flexDirection:'column',
            rowGap: 'var(--evr-spacing-3xs)',
        },
        cardBody: {
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            columnGap: 'var(--evr-spacing-3xs)'
        },
        cardFooter: {
            display: 'flex',
            justifyContent: 'flex-end',
            columnGap: 'var(--evr-spacing-3xs)'
        },
        cardHeaderMinHeight: '9rem',
        cardMetadataMinHeight: '4.875rem'
    };    
    const initialState = '';
    const [selectedMenuItemId, setSelectedMenuItemId] = React.useState(initialState);      
    function reducer(action) {
        switch(action) {
            case initialState:
                return;
            case "sunset-new":
                alert("You selected New!");
                break;              
            case "sunset-edit":
                alert("You selected Edit!");
                break;
            case "sunset-delete":
                alert("You selected Delete!");
                break;
            default:
                break;            
        }
        setSelectedMenuItemId(initialState);
    }      
    React.useEffect(()=>{        
        reducer(selectedMenuItemId)
    },[selectedMenuItemId]);
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card id='card-with-aligned-sections-card-id'>
                    <Card.Media id='card-with-aligned-sections-media-id'>
                        <Image
                            id='card-with-aligned-sections-card-id-img'
                            alt={'Mount Everest during sunset'}
                            title={'A mountain'}
                            src={'images/image-sample-1.jpg'}
                        />
                    </Card.Media>
                    <Card.Content id='card-with-aligned-sections-content-id'>
                        <Card.Header
                            minHeight={styles.cardHeaderMinHeight}
                            title="Mount Everest"
                            description="Lorem ipsum."
                            id='card-with-aligned-sections-header-id'
                            action={
                                <PopoverMenu
                                    id="sunset-menu-4"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu> 
                            }
                        />
                        <Card.Metadata minHeight={styles.cardMetadataMinHeight} id='card-with-aligned-sections-media-id'>
                            <TagGroup label="Category">
                                <Tag label="Mountains" />
                                <Tag label="Mount" />
                                <Tag label="Everest" />
                                <Tag label="Beautiful" />
                                <Tag label="Scenic" />
                            </TagGroup>
                        </Card.Metadata>
                        <Card.FooterAction id='card-with-aligned-sections-footer-id'>
                            <section style={styles.cardFooter}>
                                <Button id='card-with-aligned-sections-run-btn' label="Run" />
                            </section>
                        </Card.FooterAction>
                    </Card.Content>
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card id='card-with-aligned-sections-card-id-2'>
                    <Card.Media id='card-with-aligned-sections-media-id-2'>
                        <Image
                            id='card-with-aligned-sections-card-id-2-img'
                            alt={'Lake Moraine'}
                            title={'Lake Moraine'}
                            src={'images/image-sample-2.jpeg'}
                        />
                    </Card.Media>
                    <Card.Content id='card-with-aligned-sections-content-id-2'>
                        <Card.Header
                            minHeight={styles.cardHeaderMinHeight}
                            title="Mount Everest"
                            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                            id='card-with-aligned-sections-header-id-2'
                            action={
                                <PopoverMenu
                                    id="sunset-menu-2"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu> 
                            }
                        />
                        <Card.Metadata minHeight={styles.cardMetadataMinHeight} id='card-with-aligned-sections-meta-id-2'>
                            <TagGroup label="Category">
                                <Tag label="Mountains" />
                                <Tag label="Scenic" />
                            </TagGroup>
                        </Card.Metadata>
                        <Card.Body id='card-with-aligned-sections-body-id-2'>
                            <section style={styles.cardBody}>
                                <Avatar id='card-with-aligned-sections-picture-avatar' size='md' src='https://www.linkpicture.com/q/Avatar_2.jpg' ariaLabel="avatar" />
                                <Avatar id='card-with-aligned-sections-initials-avatar' initials="ml" size="md" ariaLabel="avatar" />
                                <section style={styles.extraText}>
                                    <p className='evrCaptionText'>Last Run</p>
                                    <p className='evrBodyText1'>05/12/2022 15:45</p>
                                </section>
                            </section>
                        </Card.Body>
                        <Card.FooterAction id='card-with-aligned-sections-footer-id-2'>
                            <section style={styles.cardFooter}>
                                <Button id='card-with-aligned-sections-run-btn-2' label="Run" />
                            </section>
                        </Card.FooterAction>
                    </Card.Content>
                </Card>
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={alignedSectionsCardCode} />

### Clickable Card

Cards can be an interactive (clickable and focusable) element by passing an `onClick` function. <br />
Other callback functions such as `onFocus` and `onBlur` are available once an `onClick` function is provided. <br />
Note that only clickable child components will take precedence over the clickable card area.

export const clickableCardCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: 'auto'
        },
        extraText: {
            display: 'flex',
            flexDirection:'column',
            rowGap: 'var(--evr-spacing-3xs)',
        },
        cardBody: {
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            columnGap: 'var(--evr-spacing-3xs)'
        },
        cardFooter: {
            display: 'flex',
            justifyContent: 'flex-end',
            columnGap: 'var(--evr-spacing-3xs)'
        }
    };    
    const initialState = '';
    const [selectedMenuItemId, setSelectedMenuItemId] = React.useState(initialState);      
    function reducer(action) {
        switch(action) {
            case initialState:
                return;
            case "sunset-new":
                alert("You selected New!");
                break;              
            case "sunset-edit":
                alert("You selected Edit!");
                break;
            case "sunset-delete":
                alert("You selected Delete!");
                break;
            default:
                break;            
        }
        setSelectedMenuItemId(initialState);
    }      
    React.useEffect(()=>{        
        reducer(selectedMenuItemId)
    },[selectedMenuItemId]);
    return (
        <div style={styles.row}>
            <div style={styles.cardContainer}>
                <Card onClick={() => alert('Mount Everest clicked!')} id='clickable-card-1' ariaLabel='Mount Everest'>
                    <Card.Media id='clickable-card-media-id'>
                        <Image
                            id='clickable-card-1-img'
                            alt={'Mount Everest during sunset'}
                            title={'A mountain'}
                            src={'images/image-sample-1.jpg'}
                        />
                    </Card.Media>
                    <Card.Content id='clickable-card-content-id'>
                        <Card.Header
                            title="Mount Everest"
                            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                            id='clickable-card-header-id'
                            action={
                                <PopoverMenu
                                    id="sunset-menu-5"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu> 
                            }
                        />
                    </Card.Content>
                </Card>
            </div>
            <div style={styles.cardContainer}>
                <Card onClick={() => alert('Lake Moraine clicked!')} id='clickable-card-2' ariaLabel='Lake Moraine'>
                    <Card.Media id='clickable-card-media-id-2'>
                        <Image
                            id='clickable-card-2-img'
                            alt={'Lake Moraine'}
                            title={'Lake Moraine'}
                            src={'images/image-sample-2.jpeg'}
                        />
                    </Card.Media>
                    <Card.Content id='clickable-card-content-id-2'>
                        <Card.Header
                            title="Mount Everest"
                            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                            id='clickable-card-header-id-2'
                            action={
                                <PopoverMenu
                                    id="sunset-menu-2"
                                    buttonAriaLabel="Popover Action Menu"
                                    onChange={({id}) => setSelectedMenuItemId(id)}
                                    selectedMenuId={selectedMenuItemId}
                                >
                                    <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                                    <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                                </PopoverMenu> 
                            }
                        />
                        <Card.Metadata id='clickable-card-category'>
                            <TagGroup label="Category">
                                <Tag label="Mountains" />
                                <Tag label="Mount" />
                                <Tag label="Everest" />
                                <Tag label="Beautiful" />
                                <Tag label="Scenic" />
                            </TagGroup>
                        </Card.Metadata>
                        <Card.Body id='clickable-card-body-id-2'>
                            <section style={styles.cardBody}>
                                <Avatar id='clickable-card-picture-avatar' size='md' src='https://www.linkpicture.com/q/Avatar_2.jpg' ariaLabel="avatar" />
                                <Avatar id='clickable-card-initials-avatar' initials="ml" size="md" ariaLabel="avatar" />
                                <section style={styles.extraText}>
                                    <p className='evrCaptionText'>Last Run</p>
                                    <p className='evrBodyText1'>05/12/2022 15:45</p>
                                </section>
                            </section>
                        </Card.Body>
                        <Card.FooterAction id='clickable-card-footer-id-2'>
                            <section style={styles.cardFooter}>
                                <Button id='clickable-card-footer-run-btn' label="Run" />
                            </section>
                        </Card.FooterAction>
                    </Card.Content>
                </Card>
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={clickableCardCode} />

## Accessibility

When using the clickable card, the `id` and `ariaLabel` props are <strong>required</strong> for an accessible screen reader experience. <br />

Keyboard interaction for the clickable area is similar to that of a button - Pressing `Enter` or `Space` key will trigger the `onClick` callback.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
