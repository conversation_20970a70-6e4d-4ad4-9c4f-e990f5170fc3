# Card

## Summary

Research and document implementations for the Everest Card.

- Start Date: 2022-02-17
- Figma links: https://www.figma.com/file/fex0p65556eFidJqRIbGAT/branch/ON22pwds4QL8hKbXQohqLT/%F0%9F%A7%AACard?node-id=0%3A1

## Detailed Design

Card anatomy consists of the following and are devised in sections:

- **Container (mandatory):** - The parent div, controls the elevation and listens to click and focus events if the container is opted in to listen
- **Media (optional):** - Image / media content
- **Title / Headline Text (optional):** - Title / heading of a card and optional button to show context menu
- **Body Content Text (optional):** - Body text of the card
- **Footer section with Custom content (optional):** - Any custom content, particularly call to action controls that is located below all other content

## API

1.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the html element.
2.  **children** `ReactNode | undefined`
    Optional. Sets the section contents of the card
3.  **mediaSrc**: `string | undefined`
    Optional. Sets the import of the media or image content into the media section of the card.
4.  **mediaAlt**: `string | undefined`
    Optional. Sets the alt text of the media or image content of the media source provided.
5.  **mediaTitle**: `string | undefined`
    Optional. Sets the title text of the media or image content of the media source provided.
6.  **mediaHeight**: `string | undefined`
    Optional. Sets the media or image height.
7.  **onClick**: `() => void | undefined`
    Optional. Sets a callback function that is executed on card container click. The card container becomes interactive (focusable and clickable) when an `onClick` callback function is provided.
8.  **onFocus**: `() => void | undefined`
    Optional. Sets a callback function that is executed on card container focus. The card container becomes interactive (focusable and clickable) when an `onClick` callback function is provided.
9.  **onBlur**: `() => void | undefined`
    Optional. Sets a callback function that is executed on card container blur. The card container becomes interactive (focusable and clickable) when an `onClick` callback function is provided.
10. **ariaLabelledBy**: `string | undefined`
    Optional. Sets the value for the **aria-labelledby** attribute of the clickable card component. Recommended data should be the title or heading of the card.
11. **ariaDescribedBy**: `string | undefined`
    Optional. Sets the value for the **aria-describedby** attribute of the clickable card component.

To facilitate custom content, consumers can utilize the **children** of the component to render such content, which will follow all other content.

## Accessibility

- When card is focusable via passing the onClick prop, the card container will be a focusable element (clickable, tabbable). When there are interactive elements inside the card, tab order should start with the card container, followed by natural tab order of elements within the card
- Focus ring utility can be utilzed via `FocusRing` util from `@ceridianhcm/everest-cdk`
- Using **aria-labelledby** attribute will provide the teaser content of the card when the card is a focusable element, usually taking what is the title or heading of the card

### Clickable Area

- The ClickableArea component within the card faciliates the interactivity of the card, specifically only when an `onClick` prop is provided
- The component handles all interactive card callbacks, including `onClick`, `onFocus`, and `onBlur`
- Under the hood, this is actually a button at its markup level, which spans all of the card width and height - this way we also call `onClick` when pressing the Enter or Space key without additional logic
- This button is styled in such a way so it is still visible to a screen reader
- Since this button lives as a sibling of the custom content section, it will never violate nested button DOM structure should the custom content section contain call-to-action elements
- `ariaLabelledBy` and `ariaDescribedBy` props adds on to this button's `aria-labelled-by` and `aria-described-by` attributes respectively so the screen reader can appropriately call out the card contents when focused
- `headingText` and `bodyText` props innately add to this button's `aria-labelled-by` and `aria-described-by` attributes respectively

## Other Design Systems

**Material UI** - https://mui.com/components/cards/

- Supports card action sections, and a CardActionArea API for focus / click target of the whole card, separate clickable area sections from call-to-action elements

**Polaris (Shopify) - https://polaris.shopify.com/components/structure/card#navigation**

- Supports breaking into card sections

## Other Useful Links

**Inclusive Components** - https://inclusive-components.design/cards/

- Details and tricks on building an accessible clickable card

## Q&A

**Will there always be body content whenever there is title / headline? Does the body content only consists of text?**

- Both are independent of each other and consumers can opt-in one, or the other, or both.

- **Clarification of use cases for an entire card to trigger an event? What indicates the card to be a trigger? Is it applied to all card types regardless of the presence of actionable items?**

- This is an opt-in feature that a consumer can control.

**Clarification on media content? Is it Image only?**

- For this scope, only consider image.

**Regarding custom content section, are all actionable items placed at the very bottom of the card? Is there a limitation or restriction of such actionable items?**

- No limitation or restriction should be in place as there are many use cases that does not place actionable items at the footer of the card.

- **Is the container-only variant of the card non-focusable? Will it have any interactable elements? Screenreader callout for the card?**

- Container-only variant design can be focusable and can have interactive elements, it is up to the consumer to control the screenreader callout for the card.

**Is there any default height? Or it should be auto-sized based on content?**

- No default size, height and width should be handled by Layout component.

**Is there an actual case for the container/border alias? The Figma specs does not show an example.**

- There will be a border style and no box-shadow styles when the card is not clickable. If it is clickable, there won't be a border style and will have box-shadow styles.

**How do we handle text truncation or limiting card text to x amount of lines?**

- This is out of scope, but should be considered in later iterations.

## Future Considerations

- Media content section to support more than just image (video)
- As the guidelines of using the different structure of card in design fleshes out, consider further accessibility situations in this article: https://inclusive-components.design/cards/, in particular when the card is a focusable element
- RTL support for bidirectionality - decision to place control on container-like components such at card, or at the typographical / form components level so container-like components get RTL support for free
- Card component is driven by props, the only exception of utilizing child component is for custom content. Should the Design System becomes more freeform, many controls driven by props can be placed as child sections, such as what Material UI uses with their CardContent and CardMedia components

## Required PBIs

**715** - Dev/Design Clarifications
**640** - Card Architecture
**711** - Create Card Component in React

## Acceptance Criteria

- Component to be named `<Card>`
- Build component and setup in Storybook (Components, Foundations, Automation)
- Styles are on par with Design spec in Figma
- Verify the following:
  - API works as intended
    - testId
    - mediaSrc
    - mediaAlt
    - mediaTitle
    - mediaHeight
    - headingText
    - bodyText
    - onClick
    - onFocus
    - ariaLabelledBy
    - ariaDescribedBy
  - Verify card can take in custom child components
  - Accessibility (aria attributes, tab order, screen reader callouts, mouse and keyboard interactivity)
    - Card component is focusable when onClick is provided, and be triggered with keypress (enter/space)
- Unit and integration tests implemented

## Change Log

[EDS-3286: [Card] Set box shadow as default styling](https://ceridian.atlassian.net/browse/EDS-3286)
[EDS-3569: [Card] Adjust visual styles for card](https://ceridian.atlassian.net/browse/EDS-3569)
[EDS-1645: [Dev] [Card] Assess if any changes are required for latest Card design](https://ceridian.atlassian.net/browse/EDS-1645)
[PWEB-18701: [JSON Forms] Allow selection of icons for the title in MetaGroup](https://dayforce.atlassian.net/browse/PWEB-18701)