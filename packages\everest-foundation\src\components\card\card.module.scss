@use '../../variables.scss';
@layer evrCardClickableElements;

article.evrCard {
  position: relative; // Required for clickable area
  width: 100%;
  min-width: variables.$cardMinWidth;
  height: 100%;
  min-height: variables.$cardMinHeight;
  border: none;
  border-radius: var(--evr-radius-sm);

  &.clickable {
    & .cardContent {
      padding-block-start: calc(var(--evr-spacing-md) - 1px); // take consideration into visuallyHidden button's height
    }
  }

  &.outlined {
    outline: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
  }

  //For future use
  // &.filled {
  //   background: var(--evr-surfaces-secondary-default);
  // }

  &.elevated {
    box-shadow: var(--evr-depth-02);
  }

  & .clickableArea {
    // cannot use visuallyHidden here because absolute positioning is on the ::after pseudo element
    width: 1px;
    height: 1px;
    border: 0;
    clip: rect(0 0 0 0);
    margin: -1px;
    overflow: hidden;
    padding: 0;
    cursor: pointer;

    &:focus {
      outline: none;
    }

    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
    }
  }

  // Spacing styles
  & .cardContent {
    flex-grow: 1;

    display: flex;
    flex-direction: column;
    padding: var(--evr-spacing-md);
    gap: var(--evr-spacing-sm);
    @layer evrCardClickableElements {
      > * {
        a,
        button,
        input,
        label {
          position: relative;
        }
      }
    }
  }

  .cardHeader {
    .cardHeaderTitle {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--evr-spacing-sm);
    }

    .cardHeaderDescription:nth-child(2) {
      // Add the gap, but only if it's after the title. No need to add gap if there's no title.
      margin-block-start: var(--evr-spacing-3xs);
    }

    .cardHeaderSection {
      display: flex;
      column-gap: var(--evr-spacing-sm-px);
      align-items: flex-start;
    }

    .cardHeaderIcon {
      display: flex;
      min-width: var(--evr-size-xl);
      height: var(--evr-size-xl);
      justify-content: center;
      align-items: center;
      border-radius: var(--evr-radius-circle);
      background: var(--evr-surfaces-secondary-default);
    }
  }

  @media (forced-colors: active) {
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default);
  }

  .cardMediaAspectRatio {
    aspect-ratio: 16/9;
  }

  .cardMedia {
    border-radius: var(--evr-radius-sm) var(--evr-radius-sm) 0px 0px;
    overflow: hidden;
  }
}
