import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';

import { useState } from 'react';
import {
  Avatar,
  Button,
  Card,
  Checkbox,
  IconButton,
  Image,
  Link,
  PopoverMenu,
  PopoverMenuItem,
  RadioButton,
  RadioButtonGroup,
  Switch,
  Tag,
  TagGroup,
  TextField,
} from '../..';
import { CardBody } from '../card/card-body';
import { CardFooterAction } from '../card/card-footer-action';
import { CardHeader } from '../card/card-header';
import { CardMetadata } from '../card/card-metadata';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';
import mountain from '../../../assets/images/mountain.jpg';

<Meta
  title="Testing/Automation Test Cases/Card"
  component={Card}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    docs: {
      source: {
        excludeDecorators: true,
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/fex0p65556eFidJqRIbGAT/%F0%9F%A7%AACard?node-id=16%3A538',
    },
    controls: {
      sort: 'requiredFirst',
      exclude: ['onClick', 'onFocus', 'onBlur'],
    },
    chromatic: Chromatic.ENABLE_CI,
  }}
  argTypes={{
    testId: {
      type: 'string',
      control: 'text',
      description: 'Optional. Sets data-testid',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'A unique id is required for accessibility purpose. ',
    },
    variant: {
      type: 'enum',
      control: 'inline-radio',
      options: ['outlined', 'elevated'],
      description: 'Optional. Adds either an outline or box-shadow around component. Defaults to outlined.',
      table: {
        defaultValue: { summary: 'outlined' },
      },
    },
    ariaDescribedBy: {
      type: 'string',
      control: 'text',
      description: "Required for accessibility. Details on the Card's purpose",
    },
    ariaLabelledBy: {
      type: 'string',
      control: 'text',
      description: 'Required for accessibility. A concise label of the Card',
    },
    onClick: {
      control: '-',
      description: 'Optional callback on click. Required to make the card an interactive element',
    },
    onFocus: {
      control: '-',
      description: 'Optional callback on focus. Must have onClick provided in order to work.',
    },
    onBlur: {
      control: '-',
      description: 'Optional callback on blur. Must have onClick provided in order to work.',
    },
  }}
  args={{
    testId: 'card-test-id',
    id: 'card-id',
    ariaLabel: 'Mount Everest',
  }}
/>

# Card

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => (
      <div style={{ width: '18.75rem' }}>
        <Card {...args}>
          <Card.Media id="card-media-id">
            <Image
              id={`${args.id}-img`}
              alt={'Mount Everest during sunset'}
              title={'A mountain'}
              src={'images/image-sample-1.jpg'}
            />
          </Card.Media>
          <Card.Content id="card-content-id">
            <CardHeader
              id="card-header-id"
              title="Mount Everest"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
            />
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="With Children">
    {(args) => (
      <div style={{ width: '18.75rem' }}>
        <Card {...args}>
          <Card.Media id="card-with-children-media">
            <Image
              id="card-with-children-img"
              alt={'Mount Everest during sunset'}
              title={'A mountain'}
              src={'images/image-sample-1.jpg'}
            />
          </Card.Media>
          <Card.Content id="card-with-children-content">
            <CardHeader
              id="card-with-children-header"
              title="Mount Everest"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
              action={
                <PopoverMenu id="sunset-menu" buttonAriaLabel="sunset">
                  <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                </PopoverMenu>
              }
            />
            <CardMetadata id="card-with-children-meta">
              <TagGroup label="Category">
                <Tag label="Mountains" />
                <Tag label="Mount" />
                <Tag label="Everest" />
                <Tag label="Beautiful" />
                <Tag label="Scenic" />
              </TagGroup>
            </CardMetadata>
            <CardBody id="card-with-children-body">
              <Avatar id="card-with-children-avatar" size="md" src={mountain} title="avatar" />
            </CardBody>
            <CardFooterAction id="card-with-children-footer">
              <Button id="card-with-children-view-btn" label="View" />
            </CardFooterAction>
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="MinHeight Sections">
    {(args) => (
      <div style={{ width: '18.75rem', height: '35rem' }}>
        <Card {...args}>
          <Card.Media id="card-with-sections-media">
            <Image
              id="card-with-sections-img"
              alt={'Mount Everest during sunset'}
              title={'A mountain'}
              src={'images/image-sample-1.jpg'}
            />
          </Card.Media>
          <Card.Content id="card-with-sections-content">
            <CardHeader
              id="card-with-sections-header"
              minHeight="8rem"
              title="Mount Everest"
              description="Lorem ipsum."
              action={
                <PopoverMenu id="sunset-menu-2" buttonAriaLabel="Action Menu">
                  <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                </PopoverMenu>
              }
            />
            <CardMetadata minHeight="4rem" id="card-with-sections-meta">
              <TagGroup label="Category">
                <Tag label="Mountains" />
                <Tag label="Mount" />
              </TagGroup>
            </CardMetadata>
            <CardBody id="card-with-sections-body">
              <Avatar id="card-with-sections-avatar" size="md" src={mountain} title="avatar" />
            </CardBody>
            <CardFooterAction id="card-with-sections-footer">
              <Button id="card-with-sections-view-btn" label="View" />
            </CardFooterAction>
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
  <Story name="MinWidth and MinHeight" decorators={[ChromaticDecorators.setHeightTo100vh]}>
    {(args) => (
      <div style={{ width: '1rem', height: '1rem' }}>
        <Card {...args}>
          <Card.Content id="card-with-sections-content-2">
            <CardBody id="card-with-sections-body-2">
              <p className="evrBodyText1">Not much content</p>
            </CardBody>
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Elevated">
    {(args) => (
      <div style={{ width: '18.75rem' }}>
        <Card {...args} variant="elevated">
          <Card.Media id="card-elevated-media-id">
            <Image
              id="card-elevated-img"
              alt={'Mount Everest during sunset'}
              title={'A mountain'}
              src={'images/image-sample-1.jpg'}
            />
          </Card.Media>
          <Card.Content id="card-elevated-content-id">
            <CardHeader
              id="card-elevated-header-id"
              title="Mount Everest"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
            />
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="MediaWithHeight">
    {(args) => (
      <div style={{ width: '18.75rem' }}>
        <Card {...args} variant="elevated">
          <Card.Media id="card-media-with-height-id" height={'200px'}>
            <Image
              id="card-media-with-height-img"
              alt={'Mount Everest during sunset'}
              title={'A mountain'}
              src={'images/image-sample-1.jpg'}
            />
          </Card.Media>
          <Card.Content id="card-media-with-height-content-id">
            <CardHeader
              id="card-media-with-height-header-id"
              title="Mount Everest"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
            />
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name='Clickable'>
    {(args) => {
      const [outsideCardText, setOutsideCardText] = useState('Outside Card');
      const [textFieldValue, setTextFieldValue] = useState('Original Value');
      const [switchState, setSwitchState] = useState(false);
      const [radioButtonGroupValue, setRadioButtonGroupValue] = useState('one');
      const [checkboxState, setCheckboxState] = useState('unchecked');

      const handleTextFieldValueChange = (value) => setTextFieldValue(value);
      const handleSwitchChange = (e) => {
        setSwitchState(e);
      };
      const handleCheckboxChange = () => {
        setCheckboxState(checkboxState === 'checked' ? 'unchecked' : 'checked');
      };

      return (
        <div style={{ width: '18.75rem' }}>
          <div data-testid='clickable-card-test-id-outside'>{outsideCardText}</div>
          <Card {...args} id='clickable-card' onClick={() => {setOutsideCardText('Outside Card - Card Clicked'); console.log('Mount Everest clicked!')}}>
            <Card.Media testId='clickable-card-test-id-media' id='clickable-card-media'>
              <Image
                id='clickable-card-img'
                alt={'Mount Everest during sunset'}
                title={'A mountain'}
                src={'images/image-sample-1.jpg'}
              />
            </Card.Media>
            <Card.Content testId='clickable-card-test-id-content' id='clickable-card-content'>
              <CardHeader
                id='clickable-card-header'
                title='Mount Everest'
                description='Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua.'
                action={
                  <PopoverMenu
                    id='sunset-menu'
                    buttonAriaLabel='Popover Action Menu'
                    onChange={({id}) => alert(`You selected ${id}!`)}
                  >
                    <PopoverMenuItem id='sunset-new'>New</PopoverMenuItem>
                    <PopoverMenuItem id='sunset-edit'>Edit</PopoverMenuItem>
                    <PopoverMenuItem id='sunset-delete'>Delete</PopoverMenuItem>
                  </PopoverMenu>
                }
              />
              <CardBody id='clickable-card-body'>
                <TextField
                  testId='clickable-card-text-field'
                  id='clickable-card-text-field'
                  label='Clickable Card TextField'
                  value={textFieldValue}
                  onChange={handleTextFieldValueChange}
                />
                <p className='evrBodyText'>
                  <Link testId='clickable-card-link' id='clickable-card-link' onClick={() => { console.log('Card link has been clicked.')}} >Console Log Click</Link>
                </p>
                <Switch testId='clickable-card-switch' id='clickable-card-switch' label='Clickable Card Switch' checked={switchState} onChange={handleSwitchChange} />
                <RadioButtonGroup
                  testId='clickable-card-radio-button-group'
                  id='clickable-card-radio-button-group'
                  groupName='clickable-card-radio-button-group'
                  onChange={(value) => {
                    setRadioButtonGroupValue(value);
                  }}
                >
                  <RadioButton testId='clickable-card-radio-button-option-1' id='clickable-card-radio-button-option-1' value='one' checked={'one' === radioButtonGroupValue} label='Option One' />
                  <RadioButton testId='clickable-card-radio-button-option-2' id='clickable-card-radio-button-option-2' value='two' checked={'two' === radioButtonGroupValue} label='Option Two' />
                  <RadioButton testId='clickable-card-radio-button-option-3' id='clickable-card-radio-button-option-3' value='three' checked={'three' === radioButtonGroupValue} label='Option Three' />
                </RadioButtonGroup>
                <Checkbox testId='clickable-card-checkbox' id='clickable-card-checkbox' label='Clickable Card Checkbox' checkedState={checkboxState} onChange={handleCheckboxChange} />
              </CardBody>
              <CardFooterAction id='clickable-card-footer'>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    columnGap: 'var(--evr-spacing-3xs)',
                  }}
                >
                  <Button
                    testId='clickable-card-button'
                    id='clickable-card-button'
                    label='View'
                    onClick={() => console.log('Card button has been clicked.')}
                  />
                </div>
              </CardFooterAction>
            </Card.Content>
          </Card>
        </div>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story
    name="[Playwright] - Interactions"
    parameters={{ chromatic: Chromatic.DISABLE }}
  >
    {() => {
      const [outsideCardText, setOutsideCardText] = useState('Outside Card');
      const imgProps = {
        alt: "Mount Everest during sunset",
        title: "A mountain",
        src: "images/image-sample-1.jpg",
      };

      return (
        <div style={{ width: '18.75rem', display: 'flex'}}>
          <div data-testid="outside-card-test-id">{outsideCardText}</div>
          <Card id="default-id" testId="default-test-id" ariaLabel="default">
            <Card.Media id="default-media-id">
              <Image {...imgProps} id="default-img-id" testId="default-img-test-id" />
            </Card.Media>
            <Card.Content id="default-content-id">
              <CardHeader
                id="default-header-id"
                testId="default-header-test-id"
                title="Mount Everest"
                description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
              />
            </Card.Content>
          </Card>
          <Card id="clickable-id" testId="clickable-test-id" ariaLabel="clickable" onClick={() => setOutsideCardText('Card Clicked')}>
            <Card.Media id="clickable-media-id">
              <Image {...imgProps} />
            </Card.Media>
            <Card.Content id="clickable-content-id">
              <CardHeader
                id="clickable-header-id"
                testId="clickable-header-test-id"
                title="Mount Everest"
                description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
              />
              <CardBody id="clickable-body-id" testId="clickable-body-test-id">
                <p className="evrBodyText">
                  <Link id="clickable-link-id" testId="clickable-link-test-id">Link</Link>
                </p>
              </CardBody>
              <CardFooterAction id="clickable-footer-id" testId="clickable-footer-test-id">
                <Button id="clickable-footer-button-id" label="View" />
              </CardFooterAction>
            </Card.Content>
          </Card>
        </div>
      )
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Subtitle">
    {(args) => (
      <div style={{ width: '18.75rem' }}>
        <Card {...args}>
          <Card.Media id="card-with-children-media">
            <Image
              id="card-with-children-img"
              alt={'Mount Everest during sunset'}
              title={'A mountain'}
              src={'images/image-sample-1.jpg'}
            />
          </Card.Media>
          <Card.Content id="card-with-children-content">
            <CardHeader
              id="card-with-children-header"
              title="Mount Everest"
              subtitle="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque euismod."
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
              action={
                <PopoverMenu id="sunset-menu" buttonAriaLabel="sunset">
                  <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                </PopoverMenu>
              }
            />
            <CardMetadata id="card-with-children-meta">
              <TagGroup label="Category">
                <Tag label="Mountains" />
                <Tag label="Mount" />
                <Tag label="Everest" />
                <Tag label="Beautiful" />
                <Tag label="Scenic" />
              </TagGroup>
            </CardMetadata>
            <CardBody id="card-with-children-body">
              <Avatar id="card-with-children-avatar" size="md" src={mountain} title="avatar" />
            </CardBody>
            <CardFooterAction id="card-with-children-footer">
              <Button id="card-with-children-view-btn" label="View" />
            </CardFooterAction>
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="IconName">
    {(args) => (
      <div style={{ width: '18.75rem' }}>
        <Card {...args}>
          <Card.Media id="card-with-children-media">
            <Image
              id="card-with-children-img"
              alt={'Mount Everest during sunset'}
              title={'A mountain'}
              src={'images/image-sample-1.jpg'}
            />
          </Card.Media>
          <Card.Content id="card-with-children-content">
            <CardHeader
              id="card-with-children-header"
              title="Mount Everest"
              iconName="globe"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
              action={
                <PopoverMenu id="sunset-menu" buttonAriaLabel="sunset">
                  <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                  <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                </PopoverMenu>
              }
            />
            <CardMetadata id="card-with-children-meta">
              <TagGroup label="Category">
                <Tag label="Mountains" />
                <Tag label="Mount" />
                <Tag label="Everest" />
                <Tag label="Beautiful" />
                <Tag label="Scenic" />
              </TagGroup>
            </CardMetadata>
            <CardBody id="card-with-children-body">
              <Avatar id="card-with-children-avatar" size="md" src={mountain} title="avatar" />
            </CardBody>
            <CardFooterAction id="card-with-children-footer">
              <Button id="card-with-children-view-btn" label="View" />
            </CardFooterAction>
          </Card.Content>
        </Card>
      </div>
    )}
  </Story>
</Canvas>
