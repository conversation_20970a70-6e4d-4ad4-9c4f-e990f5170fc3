import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Card } from './card';

describe('[Card]', () => {
  const id = 'card-id';
  const ariaLabelledByText = 'Data card';
  const ariaDescribedByText = 'Empty card';
  const onBlur = jest.fn();
  const onClick = jest.fn();
  const onFocus = jest.fn();

  const getChildContent = () => <Card.Content id="card-content-id">Random Content</Card.Content>;

  const getClickableArea = () => screen.queryByRole('button');

  beforeEach(onBlur.mockReset);
  beforeEach(onClick.mockReset);
  beforeEach(onFocus.mockReset);

  it('should render children when provided', () => {
    render(<Card id={id}>{getChildContent()}</Card>);
    expect(screen.getByText('Random Content')).toBeInTheDocument();
  });

  it('should not be interactive when onClick is not provided', () => {
    render(<Card id={id} />);
    expect(getClickableArea()).toEqual(null);
  });

  it('should not be interactive when onBlur is provided but onClick is not', () => {
    render(<Card id={id} onBlur={onBlur} />);
    expect(getClickableArea()).toEqual(null);
  });

  it('should not be interactive when onFocus is provided but onClick is not', () => {
    render(<Card id={id} onFocus={onFocus} />);
    expect(getClickableArea()).toEqual(null);
  });

  it('should call onFocus when provided alongside with onClick and focused', async () => {
    render(<Card ariaLabel={ariaDescribedByText} id={id} onClick={onClick} onFocus={onFocus} />);
    expect(getClickableArea()).toBeInTheDocument();

    await userEvent.tab();
    expect(onFocus).toHaveBeenCalledTimes(1);
  });

  it('should call onBlur when provided alongside with onClick and blurred', async () => {
    render(<Card ariaLabel={ariaLabelledByText} id={id} onClick={onClick} onBlur={onBlur} />);
    expect(getClickableArea()).toBeInTheDocument();

    await userEvent.tab(); // focus
    await userEvent.tab(); // blur
    expect(onBlur).toHaveBeenCalledTimes(1);
  });

  it('should call onClick when provided and clicked', async () => {
    render(<Card id={id} onClick={onClick} />);
    expect(getClickableArea()).toBeInTheDocument();

    await userEvent.click(screen.getByRole('button'));
    expect(onClick).toHaveBeenCalledTimes(1);
  });
});
