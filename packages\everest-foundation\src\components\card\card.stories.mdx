import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';

import { Card } from './card';
import { IconButton } from '../icon-button';
import { Button } from '../button';
import { Image } from '../image';
import { PopoverMenu, PopoverMenuItem } from '../popover-menu';
import Examples from './card.examples.mdx';

<Meta
  title="Components/Card"
  component={Card}
  decorators={[
    (Story) => (
      <div style={{ width: '18.75rem' }}>
        <Story />
      </div>
    ),
  ]}
  parameters={{
    docs: {
      source: {
        excludeDecorators: true,
      },
    },
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9972&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  argTypes={{
    onClick: {
      control: '-',
    },
    onFocus: {
      control: '-',
    },
    onBlur: {
      control: '-',
    },
  }}
  args={{
    id: 'card-id',
    testId: 'card-test-id',
    variant: 'outlined',
    ariaLabel: 'Clickable Card Component',
  }}
/>

# Card

<Examples />

## Card Default

<Canvas>
  <Story name="Card">
    {(args) => (
      <Card {...args}>
        <Card.Media id="card-media-id">
          <Image
            id={`${args.id}-img`}
            alt={'Mount Everest during sunset'}
            title={'A mountain'}
            src={'images/image-sample-1.jpg'}
          />
        </Card.Media>
        <Card.Content id="card-content-id">
          <Card.Header
            id="card-header-id"
            title="Mount Everest"
            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
            action={
              <PopoverMenu id="sunset-menu" buttonAriaLabel="Action Menu" onChange={() => {}}>
                <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
              </PopoverMenu>
            }
          />
          <Card.FooterAction>
            <Button id="card-run-btn-id" label="Run" />
          </Card.FooterAction>
        </Card.Content>
      </Card>
    )}
  </Story>
</Canvas>

## Props

### Card Props

<ArgsTable story="Card" />

### Card.Media Props

<ArgsTable of={Card.Media} />

### Card.Header Props

<ArgsTable of={Card.Header} />

### Card.Metadata Props

<ArgsTable of={Card.Metadata} />

### Card.Body Props

<ArgsTable of={Card.Body} />

### Card.FooterAction Props

<ArgsTable of={Card.FooterAction} />
