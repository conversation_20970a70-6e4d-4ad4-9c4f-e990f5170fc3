import React, { Children, ReactElement, useRef } from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';

import { CardBody } from './card-body';
import { CardContent } from './card-content';
import { CardFooterAction } from './card-footer-action';
import { CardHeader } from './card-header';
import { CardMedia } from './card-media';
import { CardMetadata } from './card-metadata';
import { ContainerBase, IContainerBaseProps } from '../container-base';

import styles from './card.module.scss';

export type TCardVariant = 'outlined' | 'elevated';

export interface ICardMainProps extends Omit<IContainerBaseProps, 'className' | 'roleType'> {
  /** Callback on click. Required to make the card an interactive element. */
  onClick?: React.MouseEventHandler<HTMLElement>;
  /** Callback on focus. Must have onClick provided in order to work. */
  onFocus?: React.FocusEventHandler<HTMLElement>;
  /** Callback on blur. Must have onClick provided in order to work. */
  onBlur?: React.FocusEventHandler<HTMLElement>;
  /**
   * Adds either an outline or box-shadow around component.
   * @default outlined
   */
  variant?: TCardVariant;
}

interface IClickableAreaProps extends ICardMainProps {
  ariaDescribedBy: string;
}

const ClickableArea = ({ ariaLabel, ariaDescribedBy, onBlur, onClick, onFocus, testId }: IClickableAreaProps) => {
  const clickableAreaRef = useRef<HTMLButtonElement>(null);
  const handleClick = (e: React.MouseEvent<HTMLElement>) => onClick && onClick(e);
  const handleFocus = (e: React.FocusEvent<HTMLElement>) => onFocus && onFocus(e);
  const handleBlur = (e: React.FocusEvent<HTMLElement>) => onBlur && onBlur(e);

  if (onClick) {
    return (
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      <FocusRing getCustomTarget={(focusRingRef) => focusRingRef.parentElement!}>
        <button
          data-testid={testId ? `${testId}-card` : undefined}
          ref={clickableAreaRef}
          aria-label={`${ariaLabel ?? ''}`}
          aria-describedby={ariaDescribedBy}
          className={styles.clickableArea}
          onBlur={handleBlur}
          onClick={handleClick}
          onFocus={handleFocus}
        />
      </FocusRing>
    );
  }
  return null;
};

export const Card = ({
  id,
  ariaLabel,
  onBlur,
  onClick,
  onFocus,
  children,
  testId,
  variant = 'outlined',
}: React.PropsWithChildren<ICardMainProps>): JSX.Element => {
  let mediaChild: ReactElement | undefined;
  let contentChild: ReactElement | undefined;

  Children.forEach(children, (child) => {
    if (React.isValidElement(child)) {
      switch (child.type) {
        case Card.Media:
          mediaChild = child;
          break;
        case Card.Content:
          contentChild = child;
          break;
      }
    }
  });

  const ariaDescribedBy = `${mediaChild ? mediaChild.props.id : ''} ${contentChild ? contentChild.props.id : ''}`;

  return (
    <ContainerBase
      id={id}
      testId={testId}
      roleType="article"
      className={`${styles.evrCard} ${onClick ? styles.clickable : ''} ${styles[variant]}`}
    >
      {mediaChild}

      <ClickableArea
        id={id}
        testId={testId}
        ariaDescribedBy={ariaDescribedBy}
        ariaLabel={ariaLabel}
        onBlur={onBlur}
        onClick={onClick}
        onFocus={onFocus}
      />

      {contentChild}
    </ContainerBase>
  );
};

Card.Body = CardBody;
Card.Content = CardContent;
Card.FooterAction = CardFooterAction;
Card.Header = CardHeader;
Card.Media = CardMedia;
Card.Metadata = CardMetadata;
