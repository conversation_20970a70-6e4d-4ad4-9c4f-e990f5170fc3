import { IElementDimension, TPlacement } from '@ceridianhcm/everest-cdk';

import { getOverHang } from '../toggle-tip-base';

export type CaretStyle = Omit<IElementDimension, 'width' | 'height'>;

export const defaultCaretStyle: CaretStyle = {
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
};
export const defaultDimensions: IElementDimension = {
  ...defaultCaretStyle,
  width: 0,
  height: 0,
};

export function calcCaretPosition(
  placement: TPlacement,
  toolTipDimensions: IElementDimension,
  svgRef: React.MutableRefObject<SVGSVGElement | null>,
  isToolTip: boolean
): CaretStyle {
  const style = { top: 0, left: 0, right: 0, bottom: 0 };
  const caretDimensions = svgRef?.current?.getBoundingClientRect();
  const caretWidth = caretDimensions?.width || 0;
  const toolTipHeight = toolTipDimensions?.height || 0;
  const toolTipWidth = toolTipDimensions?.width || 0;
  switch (placement) {
    case 'topLeft': {
      style.left = toolTipWidth - caretWidth - getOverHang(isToolTip);
      style.top = toolTipHeight;
      break;
    }
    case 'topCenter': {
      style.left = (toolTipWidth - caretWidth) / 2;
      style.top = toolTipHeight;
      break;
    }
    case 'topRight': {
      style.left = getOverHang(isToolTip);
      style.top = toolTipHeight;
      break;
    }
    case 'rightCenter': {
      style.top = (toolTipHeight - caretWidth) / 2;
      style.left = -1 * (caretWidth / 2);
      break;
    }
    case 'bottomCenter': {
      style.left = (toolTipWidth - caretWidth) / 2;
      break;
    }
    case 'bottomRight': {
      style.left = getOverHang(isToolTip);
      break;
    }
    case 'bottomLeft': {
      style.left = toolTipWidth - caretWidth - getOverHang(isToolTip);
      break;
    }
    case 'leftCenter': {
      style.top = (toolTipHeight - caretWidth) / 2;
      style.left = toolTipWidth - caretWidth / 2;
      break;
    }
  }
  return style;
}
