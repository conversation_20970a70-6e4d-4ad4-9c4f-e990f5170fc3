import React, { MutableRefObject, useLayoutEffect, useRef, useState } from 'react';
import { IElementDimension, TPlacement } from '@ceridianhcm/everest-cdk';
import classNames from 'classnames';

import { calcCaretPosition, defaultCaretStyle, defaultDimensions, CaretStyle } from './caret-helper';
import { useResize } from '../../utils';

import styles from './caret.module.scss';

export interface ICaret {
  id: string;
  placement: TPlacement;
  tooltipRef: MutableRefObject<HTMLDivElement | null>;
  testId?: string;
  isToolTip: boolean;
}

export const Caret = (props: ICaret): JSX.Element => {
  const { placement, tooltipRef, id, testId, isToolTip } = props;
  const [toolTipDimensions, setToolTipDimensions] = useState<IElementDimension>(defaultDimensions);
  const [caretPosition, setCaretPosition] = useState<CaretStyle>(defaultCaretStyle);
  const [windowInnerWidth, setWindowInnerWidth] = useState(0);
  const svgRef = useRef<SVGSVGElement | null>(null);

  function getPlacementStyles() {
    if (placement.startsWith('bottom')) return styles.bottom;
    if (placement.startsWith('right')) return styles.right;
    if (placement.startsWith('left')) return styles.left;
  }
  useResize(() => {
    // detect screen zoom/resize and force a re-render
    setWindowInnerWidth(window.innerWidth);
  });

  useLayoutEffect(() => {
    if (tooltipRef?.current) {
      const tooltipRefDimensions = tooltipRef?.current?.getBoundingClientRect();
      setToolTipDimensions(tooltipRefDimensions || defaultDimensions);
    }
  }, [placement, tooltipRef, windowInnerWidth]);

  useLayoutEffect(() => {
    const caretPosiiton = calcCaretPosition(placement, toolTipDimensions, svgRef, isToolTip);
    setCaretPosition(caretPosiiton);
  }, [isToolTip, placement, toolTipDimensions]);

  return (
    <div
      className={classNames(styles.evrCaret, [getPlacementStyles()])}
      style={caretPosition}
      id={id}
      data-testid={testId ? `${testId}-caret` : undefined}
      aria-hidden
    >
      <svg width="100%" viewBox="0 0 16 8" display="block" ref={svgRef}>
        <polygon
          points="0,0,16,0,8,8"
          fill="var(--evr-surfaces-secondary-inverse-default)"
          style={{ margin: '0', border: '0' }}
        />
      </svg>
    </div>
  );
};
