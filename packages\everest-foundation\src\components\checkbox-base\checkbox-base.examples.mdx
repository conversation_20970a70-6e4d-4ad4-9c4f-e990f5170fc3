import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { CheckboxBase } from './checkbox-base';
export const scope = { CheckboxBase };

### CheckboxBase

CheckboxBase is the fundamental version of the Checkbox component that only includes the visual indicator and the styling.
It is designed to be used within another components where additional elements such as label or error message are not needed.

export const defaultCodeariaLabel = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-between',
            width: 'auto',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Column>
            <Row>
                <CheckboxBase id="checkbox-unchecked" ariaLabel="Checkbox-ariaLabel" checkedState="unchecked" />
                <CheckboxBase id="checkbox-checked" ariaLabel="Checkbox-ariaLabel" checkedState="checked" />
                <CheckboxBase id="checkbox-partial" ariaLabel="Checkbox-ariaLabel" checkedState="partial" />
            </Row>
            <Row>
                <CheckboxBase id="checkbox-unchecked" ariaLabel="Checkbox-ariaLabel" checkedState="unchecked" status="error"/>
                <CheckboxBase id="checkbox-checked" ariaLabel="Checkbox-ariaLabel" checkedState="checked" status="error"/>
                <CheckboxBase id="checkbox-partial" ariaLabel="Checkbox-ariaLabel" checkedState="partial" status="error"/>
            </Row>
            <Row>
                <CheckboxBase id="checkbox-disabled-unchecked" ariaLabel="Checkbox-ariaLabel" disabled checkedState="unchecked" />
                <CheckboxBase id="checkbox-disabled-checked" ariaLabel="Checkbox-ariaLabel" disabled checkedState="checked" />
                <CheckboxBase id="checkbox-disabled-partial" ariaLabel="Checkbox-ariaLabel" disabled checkedState="partial" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={defaultCodeariaLabel} />

### CheckboxBase with tabIndex

The `tabIndex` prop allows you to control the tab order of the `CheckboxBase` component. This can be used for managing keyboard navigation.

export const tabIndexExample = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-between',
            width: 'auto',
            columnGap: '10px'
        },
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
            <CheckboxBase id="checkbox-tabindex-1" ariaLabel="Checkbox with tabIndex 1" tabIndex={1} checkedState="checked" />
            <CheckboxBase id="checkbox-tabindex-2" ariaLabel="Checkbox with tabIndex 2" tabIndex={-1} checkedState="checked" />
            <CheckboxBase id="checkbox-tabindex-3" ariaLabel="Checkbox with tabIndex 3" tabIndex={1} checkedState="checked" />
        </Row>
    );
}`;

<CodeExample scope={scope} code={tabIndexExample} />

### Presentational CheckboxBase

Some complex components such as MultiSelect will utilize a presentational Checkbox - visually looks like a checkbox but does not have any input
checkbox features as it is handled as the list item level. This can be used with the prop `presentation`. Note that ARIA attributes and onChange
event emitters are not supported in a presentational checkbox as it has the `presentation` role. It is also not focusable and `checked` state are
to be handled via other controls or components.

export const presentationCheckbox = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-between',
            width: 'auto',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Column>
            <Row>
                <CheckboxBase id="checkbox-unchecked" checkedState="unchecked" presentation />
                <CheckboxBase id="checkbox-checked" checkedState="checked" presentation />
                <CheckboxBase id="checkbox-partial" checkedState="partial" presentation />
            </Row>
            <Row>
                <CheckboxBase id="checkbox-unchecked" checkedState="unchecked" status="error" presentation />
                <CheckboxBase id="checkbox-checked" checkedState="checked" status="error" presentation />
                <CheckboxBase id="checkbox-partial" checkedState="partial" status="error" presentation />
            </Row>
            <Row>
                <CheckboxBase id="checkbox-disabled-unchecked" disabled checkedState="unchecked" presentation />
                <CheckboxBase id="checkbox-disabled-checked" disabled checkedState="checked" presentation />
                <CheckboxBase id="checkbox-disabled-partial" disabled checkedState="partial" presentation />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={presentationCheckbox} />

## When To Use

Use the CheckboxBase when you do not need any labels, for example, in a table's selection column.
