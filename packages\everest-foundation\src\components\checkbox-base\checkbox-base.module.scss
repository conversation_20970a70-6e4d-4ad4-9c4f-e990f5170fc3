.evrCheckbox {
  position: relative;
  border-radius: var(--evr-radius-4xs);
  border: var(--evr-border-width-thin-rem) solid var(--evr-borders-primary-default);
  box-sizing: border-box;
  background: var(--evr-surfaces-primary-default);
  cursor: pointer;
  display: inline-block;

  & svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
  }

  & .evrPresentationCheckbox {
    -webkit-appearance: none;
    height: calc(var(--evr-size-sm) - var(--evr-border-width-thick-rem));
    width: calc(var(--evr-size-sm) - var(--evr-border-width-thick-rem));
    display: grid;
    place-content: center;
    border-radius: var(--evr-border-width-thin-rem);
    cursor: pointer;
    margin: 0;
  }

  & .evrPresentationCheckbox:focus-visible {
    outline: none;
  }

  & input {
    appearance: none;
    -webkit-appearance: none;
    height: calc(var(--evr-size-sm) - var(--evr-border-width-thick-rem));
    width: calc(var(--evr-size-sm) - var(--evr-border-width-thick-rem));
    display: grid;
    place-content: center;
    border-radius: var(--evr-border-width-thin-rem);
    cursor: pointer;
    margin: 0;
  }

  & input:focus-visible {
    outline: none;
  }

  /* Unchecked, Default Status */
  &.default.unchecked:hover:not(.disabled) {
    border-color: var(--evr-borders-primary-hovered);
  }

  /* Checked, Default Status */
  &.default.checked:not(.disabled),
  &.default.checked:not(.disabled) [class='checked'],
  &.default.checked:not(.disabled) [class='partial'] {
    background: var(--evr-interactive-primary-default);
    border-color: var(--evr-interactive-primary-default);
  }
  &.default.checked:not(.disabled):hover,
  &.default.checked:not(.disabled):hover [class='checked'],
  &.default.checked:not(.disabled):hover [class='partial'] {
    background: var(--evr-interactive-primary-hovered);
    border-color: var(--evr-interactive-primary-hovered);
  }

  /* Unchecked, Error Status */
  &.error.unchecked:not(.disabled) {
    border: var(--evr-border-width-thick-rem) solid var(--evr-interactive-status-error-default);
    background: var(--evr-interactive-status-error-default);
    &.presentation {
      background: var(--evr-surfaces-primary-default);
    }
  }
  &.error.unchecked:not(.disabled) [class='unchecked'] {
    background: var(--evr-surfaces-primary-default);
  }

  /* Checked, Error Status */
  &.error.checked:not(.disabled) {
    border: var(--evr-border-width-thick-rem) solid var(--evr-interactive-status-error-pressed);
    background: var(--evr-interactive-status-error-pressed);
    &.presentation {
      background: var(--evr-interactive-status-error-default);
    }
  }
  &.error.checked:not(.disabled) [class='checked'],
  &.error.checked:not(.disabled) [class='partial'] {
    background: var(--evr-interactive-status-error-default);
    border-radius: var(--evr-border-width-thin-rem);
  }

  /* Disabled */
  &.disabled {
    cursor: not-allowed;
    border-color: var(--evr-inactive-border);
    background: var(--evr-inactive-border);
    &.presentation {
      background: var(--evr-surfaces-primary-default);
    }
    & .evrPresentationCheckbox {
      cursor: not-allowed;
    }
  }
  &.disabled :disabled {
    cursor: not-allowed;
    background: var(--evr-surfaces-primary-default);
  }
  &.disabled [class='checked'],
  &.disabled [class='partial'] {
    cursor: not-allowed;
    background: var(--evr-inactive-surfaces);
  }
  &.disabled.checked.presentation {
    background: var(--evr-inactive-surfaces);
  }

  &.margin {
    margin: var(--evr-spacing-3xs);
  }

  @media (forced-colors: active) {
    :disabled {
      color: GrayText;
      border-color: GrayText;
    }

    .disabled svg {
      fill: GrayText;
    }
  }
}
