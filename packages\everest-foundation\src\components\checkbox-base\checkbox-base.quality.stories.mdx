import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { CheckboxBase } from './checkbox-base';
import { action } from '@storybook/addon-actions';
import { userEvent } from '@storybook/test';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/CheckboxBase"
  component={CheckboxBase}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    disabled: false,
    testId: 'test-id',
    onChange: action('onChange'),
    status: 'default',
  }}
/>

# CheckboxBase

## Live Demo

<Canvas>
  <Story name="No margin">
    {(args) => (
      <div style={{ width: '30px', height: '30px', border: '1px solid lightgrey' }}>
        <CheckboxBase {...args} id="no-margin" label="checkboxBase-label" checkedState="unchecked" margin={false} />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="No focus ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => (
      <CheckboxBase {...args} id="no-focus-ring" label="checkboxBase-label" checkedState="checked" hideFocusRing />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Default Unchecked">
    {(args) => <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="unchecked" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Default Checked">
    {(args) => <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="checked" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Default Partial">
    {(args) => <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="partial" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Error Unchecked">
    {(args) => (
      <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="unchecked" status="error" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Error Checked">
    {(args) => (
      <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="checked" status="error" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Error Partial">
    {(args) => (
      <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="partial" status="error" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Disabled Unchecked">
    {(args) => (
      <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="unchecked" disabled />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Disabled Checked">
    {(args) => (
      <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="checked" disabled />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Presentational checkbox - Disabled Partial">
    {(args) => (
      <CheckboxBase {...args} id="presentation-checkbox" presentation={true} checkedState="partial" disabled />
    )}
  </Story>
</Canvas>
