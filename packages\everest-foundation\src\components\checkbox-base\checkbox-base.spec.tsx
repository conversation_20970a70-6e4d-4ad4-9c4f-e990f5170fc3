import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { CheckboxBase } from './checkbox-base';
import { TCheckboxCheckedState } from './checkbox-base';
import { AriaCheckedTypes } from '../../types';

describe('[CheckboxBase]', () => {
  const id = 'checkbox-base-id';
  const testId = 'checkbox-base-test-id';
  const labelText = 'checkbox-base-label';
  const onChange = jest.fn();

  const getInput = () => screen.getByRole('checkbox');
  const getPresentationCheckbox = () => screen.getByRole('presentation');

  [
    {
      name: 'Default Checked',
      jsx: <CheckboxBase id={id} testId={testId} checkedState="checked" onChange={onChange} ariaLabel="checkbox" />,
    },
    {
      name: 'Default Unchecked',
      jsx: <CheckboxBase id={id} testId={testId} checkedState="unchecked" onChange={onChange} ariaLabel="checkbox" />,
    },
    {
      name: 'Default Partial',
      jsx: <CheckboxBase id={id} testId={testId} checkedState="partial" onChange={onChange} ariaLabel="checkbox" />,
    },
    {
      name: 'Error Checked',
      jsx: (
        <CheckboxBase
          id={id}
          testId={testId}
          checkedState="checked"
          status="error"
          onChange={onChange}
          ariaLabel="checkbox"
        />
      ),
    },
    {
      name: 'Error Unchecked',
      jsx: (
        <CheckboxBase
          id={id}
          testId={testId}
          checkedState="unchecked"
          status="error"
          onChange={onChange}
          ariaLabel="checkbox"
        />
      ),
    },
    {
      name: 'Error Partial',
      jsx: (
        <CheckboxBase
          id={id}
          testId={testId}
          checkedState="partial"
          status="error"
          onChange={onChange}
          ariaLabel="checkbox"
        />
      ),
    },
    {
      name: 'Disabled Checked',
      jsx: (
        <CheckboxBase
          id={id}
          testId={testId}
          disabled
          checkedState="checked"
          onChange={onChange}
          ariaLabel="checkbox"
        />
      ),
    },
    {
      name: 'Disabled Unchecked',
      jsx: (
        <CheckboxBase
          id={id}
          testId={testId}
          disabled
          checkedState="unchecked"
          onChange={onChange}
          ariaLabel="checkbox"
        />
      ),
    },
    {
      name: 'Disabled Partial',
      jsx: (
        <CheckboxBase
          id={id}
          testId={testId}
          disabled
          checkedState="partial"
          onChange={onChange}
          ariaLabel="checkbox"
        />
      ),
    },
    {
      name: 'Presentational',
      jsx: (
        <CheckboxBase
          id={id}
          testId={testId}
          disabled
          checkedState="partial"
          onChange={onChange}
          ariaLabel="checkbox"
          presentation={true}
        />
      ),
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('events', () => {
    beforeEach(jest.resetAllMocks);

    it('dispatch a onChange event by mouse click', async () => {
      render(<CheckboxBase id={id} testId={testId} checkedState="checked" ariaLabel={labelText} onChange={onChange} />);

      await userEvent.click(getInput());
      expect(onChange).toHaveBeenCalledTimes(1);
    });

    it('dispatch onChange event by Keyboard Space key', async () => {
      render(<CheckboxBase id={id} testId={testId} checkedState="partial" ariaLabel={labelText} onChange={onChange} />);

      await userEvent.tab();
      await userEvent.keyboard('[Space]');
      expect(onChange).toHaveBeenCalledTimes(1);
    });

    it('does not dispatch an onChange event by mouse click when presentation prop is true', async () => {
      render(<CheckboxBase id={id} testId={testId} checkedState="checked" onChange={onChange} presentation={true} />);

      await userEvent.click(getPresentationCheckbox());
      expect(onChange).toHaveBeenCalledTimes(0);
    });
  });

  it('disabled property works and will correctly set the inner input disabled attribute', () => {
    render(<CheckboxBase id={id} testId={testId} checkedState="unchecked" disabled onChange={onChange} />);

    expect(getInput()?.parentElement).toHaveClass('disabled');
    expect(getInput()).not.toHaveAttribute('aria-disabled');
    expect(getInput()).toBeDisabled();
  });

  it('should have aria-describedby value', () => {
    render(<CheckboxBase id={id} testId={testId} checkedState="checked" ariaDescribedBy="element1 element2" />);

    expect(getInput()).toHaveAttribute('aria-describedby', 'element1 element2');
  });

  it('should correctly apply tabIndex prop to the input element', () => {
    const tabIndex = -1;

    render(<CheckboxBase id={id} testId={testId} checkedState="unchecked" onChange={onChange} tabIndex={tabIndex} />);

    const checkboxInput = getInput();
    expect(checkboxInput).toHaveAttribute('tabIndex', String(tabIndex)); // Assert that tabIndex is applied
  });
});
