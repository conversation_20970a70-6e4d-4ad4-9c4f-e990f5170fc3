import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import Examples from './checkbox-base.examples.mdx';
import { CheckboxBase } from './checkbox-base';
import { action } from '@storybook/addon-actions';

<Meta
  title="Toolbox/CheckboxBase"
  component={CheckboxBase}
  parameters={{
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9941&t=HEIyoS0N6cPMADjO-0',
    },
    controls: {
      sort: ['requiredFirst'],
    },
  }}
  argTypes={{
    onChange: {
      control: '-',
    },
  }}
  args={{
    disabled: false,
    id: '',
    testId: 'test-id',
    checkedState: 'unchecked',
    status: 'default',
    ariaLabel: 'Accessible Checkbox Example',
    hideFocusRing: false,
    presentation: false,
    margin: true,
  }}
/>

# Checkbox Base

<Examples />

## Live Demo & API

<Canvas>
  <Story name="CheckboxBase">
    {(args) => {
      const [{ checkedState }, updateArgs] = useArgs();
      const handleClick = (e) => {
        let state;
        switch (checkedState) {
          case 'unchecked':
            state = 'checked';
            break;
          case 'checked':
            state = 'partial';
            break;
          case 'partial':
            state = 'unchecked';
            break;
        }
        updateArgs({
          checkedState: state,
        });
        action('onChange')(e);
      };
      return <CheckboxBase {...args} checkedState={checkedState} onChange={handleClick} />;
    }}
  </Story>
</Canvas>

<ArgsTable story="CheckboxBase" />
