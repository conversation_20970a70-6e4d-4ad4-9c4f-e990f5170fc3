/* eslint-disable jsx-a11y/no-redundant-roles */
import * as React from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { mergeRefs } from '../../utils';
import { Icon } from '../icon';

import styles from './checkbox-base.module.scss';

export type TCheckboxStatus = 'default' | 'error';
export type TCheckboxCheckedState = 'unchecked' | 'checked' | 'partial';

export interface ICheckboxBaseProps {
  /** Sets the id attribute for the checkbox. */
  id: string;
  /** An id used for automation testing. */
  testId?: string;
  /** Disables the Checkbox to prevent any user action. */
  disabled?: boolean;
  /** Sets the state of the Checkbox. Options: 'unchecked', 'checked', 'partial'.
   * @default 'unchecked'
   */
  checkedState: TCheckboxCheckedState;
  /** Callback on change. Required to make the checkbox an interactive element. */
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  /** Sets the status of the checkbox. Options: 'default', 'error' */
  status?: TCheckboxStatus;
  /** Sets the aria-label attribute. */
  ariaLabel?: string;
  /** Specifies one or more ids of elements with the text that provides additional context for the screen reader users. */
  ariaDescribedBy?: string;
  /** Sets the name attribute on the checkbox. */
  name?: string;
  /** Whether the element should include the margin around it.
   * @default true
   */
  margin?: boolean;
  /** Whether the element should hide the focus ring.
   * @default false
   */
  hideFocusRing?: boolean;
  /** Sets the decorative checkbox with no input controls underneath. onChange is not available when this is true. */
  presentation?: boolean;
  /** ClassName for custom styling. */
  className?: string;
  /** Sets the tabindex attribute. */
  tabIndex?: number;
}

const getIconTemplate = (checkedState: TCheckboxCheckedState, disabled?: boolean) => {
  return (
    <Icon
      name={checkedState === 'checked' ? 'checkSmall' : 'minusSmall'}
      fill={disabled ? '--evr-inactive-content' : '--evr-content-primary-inverse'}
      size="md"
    />
  );
};

export const CheckboxBase = React.forwardRef<HTMLInputElement, ICheckboxBaseProps>((props: ICheckboxBaseProps, ref) => {
  const {
    disabled,
    testId,
    id,
    ariaLabel,
    ariaDescribedBy,
    checkedState,
    name,
    onChange,
    presentation = 'false',
    status = 'default',
    margin = true,
    hideFocusRing = false,
    className,
    tabIndex,
  } = props;

  const checkboxRef = React.useCallback(
    (checkbox) => {
      if (checkbox) {
        checkbox.indeterminate = checkedState === 'partial';
      }
    },
    [checkedState]
  );

  // Presentational checkboxes are not focusable and does not have innate event emitters; checked state is handled manually
  const renderPresentationCheckbox = () => (
    <span
      role="presentation"
      className={classnames(
        styles['evrCheckbox'],
        styles['presentation'],
        {
          [styles.default]: status === 'default',
          [styles.error]: status === 'error',
          [styles.checked]: checkedState === 'checked' || checkedState === 'partial',
          [styles.unchecked]: checkedState === 'unchecked',
          [styles.disabled]: disabled,
          [styles.margin]: margin,
        },
        className
      )}
    >
      <div
        className={styles.evrPresentationCheckbox}
        id={`${id}-presentation-checkbox`}
        data-testid={testId ? `${testId}-presentation-checkbox` : undefined}
      >
        {checkedState && checkedState !== 'unchecked' && getIconTemplate(checkedState, disabled)}
      </div>
    </span>
  );

  const renderInputCheckbox = () => (
    <FocusRing canFocusFromMouse variant="focusring" manual={hideFocusRing} disabled={disabled}>
      <span
        id={`${id}-checkbox`}
        data-testid={testId ? `${testId}-checkbox` : undefined}
        className={classnames(
          styles['evrCheckbox'],
          {
            [styles.default]: status === 'default',
            [styles.error]: status === 'error',
            [styles.checked]: checkedState === 'checked' || checkedState === 'partial',
            [styles.unchecked]: checkedState === 'unchecked',
            [styles.disabled]: disabled,
            [styles.margin]: margin,
          },
          className
        )}
      >
        <input
          type="checkbox"
          ref={mergeRefs([ref, checkboxRef])}
          className={checkedState.toString()}
          name={name}
          id={`${id}-input`}
          data-testid={testId ? `${testId}-input` : undefined}
          checked={checkedState === 'checked'}
          disabled={disabled}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedBy}
          aria-invalid={status === 'error' ? true : undefined}
          onChange={onChange}
          tabIndex={tabIndex}
        />
        {checkedState && checkedState !== 'unchecked' && getIconTemplate(checkedState, disabled)}
      </span>
    </FocusRing>
  );

  return <>{presentation === true ? renderPresentationCheckbox() : renderInputCheckbox()}</>;
});

CheckboxBase.displayName = 'CheckboxBase';
