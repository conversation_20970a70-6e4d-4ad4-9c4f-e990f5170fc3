import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { Checkbox } from './checkbox';
import { Button } from '../button';
export const scope = { Checkbox, Button };

Checkboxes are used when a user needs to select one or more items from a set.

## Variations

### Checkbox

A Checkbox can have 3 states: `unchecked`, `checked`, and `partial`.

And one of two statuses: `default` or `error`.

A partial checkbox should only be used within a Checkbox Group, and is a purely visual representation of a "mixed" checked state,
rather than a value to be saved in data. It should appear when a parent checkbox has children checkboxes, and only some of
them are selected rather than all of them.

A partial checkbox should never be set by a user clicking on the Checkbox. However, clicking on a partial checkbox will uncheck all
of its children, and set itself back to `unchecked`.

export const defaultCodeLabel = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-between',
            width: 'auto',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Column>
            <Row>
                <Checkbox id="checkbox-unchecked" label="Checkbox-label" checkedState="unchecked" />
                <Checkbox id="checkbox-checked" label="Checkbox-label" checkedState="checked" />
                <Checkbox id="checkbox-partial" label="Checkbox-label" checkedState="partial" />
            </Row>
            <Row>
            <Column>
                <Checkbox id="checkbox-unchecked" label="Checkbox-label" checkedState="unchecked" status="error" errorMessagePrefix="Error:" errorMessage="Invalid information text" />
            </Column>   
            <Column> 
                <Checkbox id="checkbox-checked" label="Checkbox-label" checkedState="checked" status="error" errorMessagePrefix="Error:" errorMessage="Invalid information text"  />
            </Column> 
            <Column> 
                <Checkbox id="checkbox-partial" label="Checkbox-label" checkedState="partial" status="error" errorMessagePrefix="Error:" errorMessage="Invalid information text" />
            </Column>
            </Row>
            <Row>
                <Checkbox id="checkbox-disabled-unchecked" label="Checkbox-label" disabled checkedState="unchecked" />
                <Checkbox id="checkbox-disabled-checked" label="Checkbox-label" disabled checkedState="checked" />
                <Checkbox id="checkbox-disabled-partial" label="Checkbox-label" disabled checkedState="partial" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={defaultCodeLabel} />

### Default Status Checkbox

export const defaultCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-between',
            width: 'auto',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Column>
            <Row>
                <Checkbox id="checkbox-unchecked" checkedState="unchecked" />
                <Checkbox id="checkbox-checked" checkedState="checked" />
                <Checkbox id="checkbox-partial" checkedState="partial" />
            </Row>
            <Row>
                <Checkbox id="checkbox-disabled-unchecked" disabled checkedState="unchecked" />
                <Checkbox id="checkbox-disabled-checked" disabled checkedState="checked" />
                <Checkbox id="checkbox-disabled-partial" disabled checkedState="partial" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={defaultCode} />

### Error Status Checkbox

export const errorCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-between',
            width: 'auto',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    return (
        <Column>
            <Row>
                <Checkbox id="checkbox-unchecked" checkedState="unchecked" status="error" />
                <Checkbox id="checkbox-checked" checkedState="checked" status="error" />
                <Checkbox id="checkbox-partial" checkedState="partial" status="error" />
            </Row>
            <Row>
                <Checkbox id="checkbox-disabled-unchecked" disabled checkedState="unchecked" status="error" />
                <Checkbox id="checkbox-disabled-checked" disabled checkedState="checked" status="error" />
                <Checkbox id="checkbox-disabled-partial" disabled checkedState="partial" status="error" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={errorCode} />

### Handling State and Clicks

The Checkbox triggers an `onChange` event whenever it's toggled using the mouse or keyboard.
Consumers should provide a callback function to respond to these interactions and adjust the `checkedState` attribute as needed.

The Label is clickable to trigger onChange on the Checkbox.

export const controlledCode = `class ControlledCheckbox extends React.Component {
  constructor(props) {
    super(props);
    this.state = {checkedState: "unchecked", checkedStateLabel:"unchecked"};
    this.handleChange = this.handleChange.bind(this);
    this.handleChangeLabel = this.handleChangeLabel.bind(this);
  }
  handleChange() {
    // Place validation checks here
    this.setState(oldState => ({ checkedState: oldState.checkedState === "checked" ? "unchecked" : "checked" }));
  }
  handleChangeLabel() {
    // Place validation checks here
    this.setState(oldState => ({ checkedStateLabel: oldState.checkedStateLabel === "checked" ? "unchecked" : "checked" }));
  }
  render() {
    return (
      <div>
        <Checkbox id="checkbox-controlled-one" checkedState={this.state.checkedState} onChange={this.handleChange} />
        <Checkbox id="checkbox-controlled-two" label="checkbox-label" checkedState={this.state.checkedStateLabel} onChange={this.handleChangeLabel} />
      </div>
    ); 
  }
}`;

<CodeExample scope={scope} code={controlledCode} />

## Accessing Checkbox using ref

Click on the Button to access the Checkbox, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
         column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '0.625rem'
        }
    }
     const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
        <Column>
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
        </Row>
        <Row>
            <Checkbox ref={ref} id="checkbox-checked" label="Checkbox-label" checkedState="checked" />
        </Row>
      </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Use the Checkbox when there is a set of options and the user may select any number of choices, including selecting none.

If a user can only select one option from the set, use a <LinkTo kind="Components/Radio Buttons/Radio Button">Radio Button</LinkTo> instead.

If the option is only to turn something on/off, use a <LinkTo kind="Components/Switch">Switch</LinkTo> instead.

## Accessibility

A unique id should always be used to allow for correct functionality with assistive technologies.<br />

A Checkbox should also always be associated with a meaningful label.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
