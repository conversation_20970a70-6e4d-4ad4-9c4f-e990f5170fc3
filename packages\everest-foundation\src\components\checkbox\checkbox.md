Date: 2021-11-23

# Summary

Research and document implementations for the Everest Checkbox. Based on the Figma file https://www.figma.com/file/6MRT3O96cACFwhFpRH6T1r/%F0%9F%A7%AASelection-Controls?node-id=2%3A5

# Detailed design

- **Controlled input**:
  - We should have our components be as presentational and reusable as possible. Having a controlled input means it does not determine any state internally/with the DOM. Instead it just reacts to a state being passed in by the user.
  - The consuming application will determine the logic & validation checks for handling state changes, particularly because this could differ between applications/business domains.
    - This is a large benefit of controlled inputs -- validation checks and are easier to handle now that the input's value is always stored in local state
    - State changes are also highly predictable
  - The consumer should pass a click handler to determine behaviours/state changes on click
  - Currently the Storybook documentation doesn't pass in a click handler so it will not react to clicks for the above reason. This also keeps the state in sync with the Storybook Controls.
  - Articles to get an overview of controlled vs uncontrolled inputs (they're in React but the same concepts apply)
    - https://levelup.gitconnected.com/the-difference-between-controlled-and-uncontrolled-components-in-react-b76ac333db4d
    - https://blog.logrocket.com/controlled-vs-uncontrolled-components-in-react/
- **Partial state**:

  - As discussed from a design/dev critique meeting, we anticipate the need for a partial-state checkbox. It is also currently being used in the existing Dayforce application.
  - This does not denote any "real" state to be saved but acts more as a visual indicator for when it has some children checkbox items that are checked, but not all of them.
  - When this partial state appears, it can be clicked to de-select all its children.
  - An example of the desired behaviour is the Gmail parent checkbox.

- **Will not include Typography label at this phase**:

  - The current checkbox design does not include a label. This may be added later as a Label Typography component gets created
  - Label should be clickable

- **Accessibility**:

  - W3 guideline will be the basis of requirements: https://www.w3.org/TR/wai-aria-practices-1.2/#checkbox. Special callouts below:
    - A partial state checkbox will convey its state using aria-checked="mixed".
    - TBD: aria-controls announces the ID of the element whose contents or presence are controlled by the checkbox to screen reader users. Will we have any of these kinds of use cases for checkbox?
  - TBD: May need to borrow from Polaris to implement a "labelHidden" attribute for Accessibility when the label is implemented. See below.
    - Investigate: visually hidden CSS class selector for hiding label

# Other Design Systems

Polaris (Shopify) - https://polaris.shopify.com/components/forms/checkbox#navigation

- They hide their input elements visually but keep it accessible for accessibility tools. May not need to do this since new CSS specs let you style a checkbox directly now.
- Has an indeterminate checkbox
- API includes an onFocus and onBlur handler. May be worth considering for ourselves in the future
- API includes a labelHidden boolean. Worth considering for ourselves when we add the label in the future. Their reasoning: "Use the labelHidden prop to visually hide the label but make it available to assistive technologies"

Carbon - https://carbondesignsystem.com/components/checkbox/usage/

- Similar to Polaris, they also visually hide their input
- Has an indeterminate checkbox

Material design - https://material.io/components/checkboxes

- Has an indeterminate checkbox
- Vanilla Web implementation. They lean heavily on the user to instantiate components onto a querySelector, then use their own markup, Sass mixins and classes to control what is rendered/accessibility (not relevant for us)

FAST Foundation - https://www.fast.design/docs/api/fast-foundation.checkbox

- Has an indeterminate checkbox
- Gives you an ability to customize your indicator when you use the class. Seems like you can pass in an svg definition and it will land in its "control" slot.
- Default template given to you is not Form Associated (does not use the native input element). Need to explicity use the Form Associated Checkbox, or you make your own input element.
  - Generally W3C recommends to use the native HTML elements whenever possible, instead of assigning Aria roles on another element: https://www.w3.org/TR/using-aria/#firstrule
- Styles their input directly (likely because it's a newer design system)

# Tradeoffs

- It can take some effort to get accustomed to controlled inputs if the team/consuming teams are not used to them (<span style="color: green">**Update**:</span> Search UI team is already using controlled inputs. Nicky also confirmed in the initial phases of the project that this is the way forward for the Everest team in general). However the benefits are:
  - More predictable states
    - Handled entirely by the consumer
    - An uncontrolled input can lose its reference or be affected by other sources during its lifecycle
  - Easier validation, since the consumer will always have access to the input's value in local state
  - Higher reusability and less error-prone/forcing custom behaviour

# API

- disabled: string
- checkedState: CHECKED | UNCHECKED | PARTIAL
- testId: string
- id: string
- onClick: void

Out of Scope (implemented in the future):

- readonly: boolean
- error: boolean
- label: string

# Questions

- Svg vs CSS
  - Since we don't need to support IE11, we can style a checkbox directly. But we should follow up on the discussion of using an entire SVG for the checkbox itself or styling the square with CSS
  - At this point the icon sets are not ready, perhaps we start with CSS and evaluate to change to SVG later
  - <span style="color: green">**DECISION**</span>: use interim SVG exported from Figma for the checkmark. Square remains CSS. Align with design, and create Icon follow up task in a Beta Follow Up doc
- No read-only checkbox anymore -- Should be able to just handle by not giving a click handler and setting checked=true. But are there stylistic differences and do we want to do this explicitly with the API?
  - <span style="color: green">**DECISION**</span>: Design confirmed the readonly checkbox is out of scope for Alpha
- Error state checkbox is also not included in the Figma. Do we need it?
  - <span style="color: green">**DECISION**</span>: Design confirmed the error checkbox is out of scope for Alpha. It will be delivered with other form elements (partially because Help Text needs to be used for errors in conjunction with any style changes)
- Do we use aria-disabled instead of disabled to allow for screen readers to announce its disabled state?
  - <span style="color: green">**DECISION**</span>: A11y team confirmed we use aria-disabled asthe standard
- Since checkboxes need a unique id to associate with labels and to work with assistive technologie, should we generate a fallback ID? Or is the a11y team ok with having the consuming team provide it
  - <span style="color: orange">**TBD**</span>: Confirm with A11y team in larger catch-all meeting if they prefer not to auto-generate
- Does the touch spec meet accessibility? Does it need to be 44x44? The square is currently 16 x 16
  - <span style="color: green">**DECISION**</span>: Design confirmed that when the label is implemented, the height will meet this requirement.
- Do we need the following? <span style="color: orange">**TBD**</span>: Clarify with a11y team in catch-all meeting
  - MouseUp and MouseDown
  - onKeyDown, onKeyUp (clarify this usage)
  - onFocus
  - onBlur
  - id auto-generation when user does not provide one
- Why does the focus ring appear to be "flickering"? Disappeaing when I hold down the mouse click and then reappear when I let go
  - <span style="color: green">**DECISION**</span>: This behaviour is expected behaviour, on native web browser, when a basic implement of the checkbox with label component. This behaviour has been comfirmed to be working as intended with Web Platform Accessibility team with Jira ticket https://dayforce.atlassian.net/issues/EDS-4460.

## Changelog

09/17/2024 - Added details about intended "flickering" behaviour for Checkbox Component [(**EDS-4460**)](https://dayforce.atlassian.net/browse/EDS-4460)
01/04/2024 - [WC Conversion] Replace Checkbox with CheckboxV2 [(**EDS-3439**)](https://ceridian.atlassian.net/browse/EDS-3439)
