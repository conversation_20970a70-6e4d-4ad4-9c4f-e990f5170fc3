import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { Checkbox } from './checkbox';
import { action } from '@storybook/addon-actions';
import { Chromatic } from '../../../chromatic';

export const loremIpsum =
  'At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus. Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae. Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repella';

<Meta
  title="Testing/Automation Test Cases/Checkbox"
  component={Checkbox}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'evr-checkbox',
    disabled: false,
    testId: 'test-id',
    ariaLabel: 'this is an aria-label',
    onClick: action('onClick'),
  }}
/>

# Checkbox

## Live Demo

<Canvas>
  <Story name="Default Unchecked">{(args) => <Checkbox {...args} checkedState="unchecked" />}</Story>
</Canvas>

<Canvas>
  <Story name="Default Checked">{(args) => <Checkbox {...args} checkedState="checked" />}</Story>
</Canvas>

<Canvas>
  <Story name="Default Partial">{(args) => <Checkbox {...args} checkedState="partial" />}</Story>
</Canvas>

<Canvas>
  <Story name="Error Unchecked">{(args) => <Checkbox {...args} status="error" checkedState="unchecked" />}</Story>
</Canvas>

<Canvas>
  <Story name="Error Checked">{(args) => <Checkbox {...args} status="error" checkedState="checked" />}</Story>
</Canvas>

<Canvas>
  <Story name="Error Partial">{(args) => <Checkbox {...args} status="error" checkedState="partial" />}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled Unchecked">{(args) => <Checkbox {...args} checkedState="unchecked" disabled />}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled Checked">{(args) => <Checkbox {...args} checkedState="checked" disabled />}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled Partial">{(args) => <Checkbox {...args} checkedState="partial" disabled />}</Story>
</Canvas>

<Canvas>
  <Story name="Default Unchecked Label">
    {(args) => <Checkbox {...args} label="checkbox-label" checkedState="unchecked" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Checked Label">
    {(args) => <Checkbox {...args} label="checkbox-label" checkedState="checked" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Partial Label">
    {(args) => <Checkbox {...args} label="checkbox-label" checkedState="partial" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Unchecked Label with Error Message">
    {(args) => (
      <Checkbox
        {...args}
        label="checkbox-label"
        status="error"
        checkedState="unchecked"
        errorMessagePrefix="Error:"
        errorMessage="Invalid information text"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Checked Label with Error Message">
    {(args) => (
      <Checkbox
        {...args}
        label="checkbox-label"
        status="error"
        checkedState="checked"
        errorMessagePrefix="Error:"
        errorMessage="Invalid information text"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Partial Label with Error Message">
    {(args) => (
      <Checkbox
        {...args}
        label="checkbox-label"
        status="error"
        checkedState="partial"
        errorMessagePrefix="Error:"
        errorMessage="Invalid information text"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Unchecked Label">
    {(args) => <Checkbox {...args} label="checkbox-label" checkedState="unchecked" disabled />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Checked Label">
    {(args) => <Checkbox {...args} label="checkbox-label" checkedState="checked" disabled />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Partial Label">
    {(args) => <Checkbox {...args} label="checkbox-label" checkedState="partial" disabled />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Checkbox with long label">
    {(args) => <Checkbox {...args} label={loremIpsum} checkedState="checked" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => {
      const activeStates = ['checked', 'partial'];
      const [enabledCheckedState, setEnabledCheckedState] = React.useState('partial');
      const [disabledCheckedState, setDisabledCheckedState] = React.useState('partial');
      const handleChange = (checkedState, setCheckedState) =>
        setCheckedState(activeStates.includes(checkedState) ? 'unchecked' : 'checked');
      return (
        <>
          <Checkbox
            id="enabled-id"
            testId="enabled-test-id"
            label="enabled"
            checkedState={enabledCheckedState}
            onChange={() => handleChange(enabledCheckedState, setEnabledCheckedState)}
          />
          <Checkbox
            id="disabled-id"
            testId="disabled-test-id"
            label="disabled"
            checkedState={disabledCheckedState}
            onChange={() => handleChange(disabledCheckedState, setDisabledCheckedState)}
            disabled
          />
          <Checkbox
            id="error-id"
            testId="error-test-id"
            label="error"
            checkedState={'checked'}
            status={'error'}
            errorMessage={'this is an error'}
          />
        </>
      );
    }}
  </Story>
</Canvas>
