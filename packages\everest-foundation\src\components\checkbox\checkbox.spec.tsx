import React, { useEffect, useRef } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { Checkbox } from './checkbox';
import { AriaCheckedTypes } from '../../types';
import { TCheckboxCheckedState } from '../checkbox-base';

describe('[Checkbox]', () => {
  const id = 'checkbox-id';
  const testId = 'checkbox-test-id';
  const labelText = 'checkbox-label';
  const onChange = jest.fn();

  const getLabel = () => screen.getByLabelText(labelText);
  const getCheckbox = () => screen.getByRole('checkbox');

  const expectToHaveFocusRingStyle = (element: HTMLElement) => {
    expect(element).toHaveClass('evrFocusRingVisible');
  };

  const expectNotToHaveFocusRingStyle = (element: HTMLElement) => {
    expect(element).not.toHaveClass('evrFocusRingVisible');
  };

  beforeEach(onChange.mockReset);

  [
    {
      name: 'Default Checked',
      jsx: <Checkbox id={id} testId={testId} ariaLabel={labelText} checkedState="checked" />,
    },
    {
      name: 'Default Checked with label',
      jsx: <Checkbox id={id} testId={testId} label={labelText} checkedState="checked" />,
    },
    {
      name: 'Default Unchecked',
      jsx: <Checkbox id={id} testId={testId} ariaLabel={labelText} checkedState="unchecked" />,
    },
    {
      name: 'Default Unchecked with label',
      jsx: <Checkbox id={id} testId={testId} label={labelText} checkedState="unchecked" />,
    },
    {
      name: 'Default Partial',
      jsx: <Checkbox id={id} testId={testId} ariaLabel={labelText} checkedState="partial" />,
    },
    {
      name: 'Default Partial with label',
      jsx: <Checkbox id={id} testId={testId} label={labelText} checkedState="partial" />,
    },
    {
      name: 'Error Checked',
      jsx: <Checkbox id={id} testId={testId} label={labelText} checkedState="checked" status="error" />,
    },
    {
      name: 'Error Checked with label and error message',
      jsx: (
        <Checkbox
          id={id}
          testId={testId}
          label={labelText}
          checkedState="checked"
          status="error"
          errorMessagePrefix="Error:"
          errorMessage="Invalid information text"
        />
      ),
    },
    {
      name: 'Error Unchecked',
      jsx: <Checkbox id={id} testId={testId} ariaLabel={labelText} checkedState="unchecked" status="error" />,
    },
    {
      name: 'Error Unchecked with label and error message',
      jsx: (
        <Checkbox
          id={id}
          testId={testId}
          label={labelText}
          checkedState="unchecked"
          status="error"
          errorMessagePrefix="Error:"
          errorMessage="Invalid information text"
        />
      ),
    },
    {
      name: 'Error Partial',
      jsx: <Checkbox id={id} testId={testId} ariaLabel={labelText} checkedState="partial" status="error" />,
    },
    {
      name: 'Error Partial with label and error message',
      jsx: (
        <Checkbox
          id={id}
          testId={testId}
          label={labelText}
          checkedState="partial"
          errorMessagePrefix="Error:"
          errorMessage="Invalid information text"
          status="error"
        />
      ),
    },
    {
      name: 'Disabled Checked',
      jsx: <Checkbox id={id} testId={testId} disabled ariaLabel={labelText} checkedState="checked" />,
    },
    {
      name: 'Disabled Checked with label',
      jsx: <Checkbox id={id} testId={testId} disabled label={labelText} checkedState="checked" />,
    },
    {
      name: 'Disabled Unchecked',
      jsx: <Checkbox id={id} testId={testId} disabled ariaLabel={labelText} checkedState="unchecked" />,
    },
    {
      name: 'Disabled Unchecked with label',
      jsx: <Checkbox id={id} testId={testId} disabled label={labelText} checkedState="unchecked" />,
    },
    {
      name: 'Disabled Partial',
      jsx: <Checkbox id={id} testId={testId} disabled ariaLabel={labelText} checkedState="partial" />,
    },
    {
      name: 'Disabled Partial with label',
      jsx: <Checkbox id={id} testId={testId} disabled label={labelText} checkedState="partial" />,
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getLabel()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  it('should not render focus ring directly around checkbox, only around checkbox and label', () => {
    //Test related to EDS-4321
    const CheckboxFocusRingTest = () => {
      const checkboxCount = 3;
      const checkboxRefs = useRef<Array<HTMLInputElement | null>>([]);
      useEffect(() => {
        checkboxRefs.current[checkboxCount - 1]?.focus();
      }, [checkboxCount]);
      return (
        <>
          {Array.from({ length: checkboxCount }, (element, index) => (
            <Checkbox
              key={index}
              ref={(el) => (checkboxRefs.current[index] = el)}
              id={`checkbox-focus-ring-test-index-${index}`}
              label={`Checkbox Focus Ring Test ${index + 1}`}
              checkedState={'unchecked'}
              testId={`checkbox-focus-ring-test-${index + 1}`}
            />
          ))}
        </>
      );
    };

    render(<CheckboxFocusRingTest />);

    expectNotToHaveFocusRingStyle(screen.getByTestId('checkbox-focus-ring-test-1-label'));
    expectNotToHaveFocusRingStyle(screen.getByTestId('checkbox-focus-ring-test-2-label'));
    expectToHaveFocusRingStyle(screen.getByTestId('checkbox-focus-ring-test-3-label'));

    expectNotToHaveFocusRingStyle(screen.getByTestId('checkbox-focus-ring-test-1-checkbox'));
    expectNotToHaveFocusRingStyle(screen.getByTestId('checkbox-focus-ring-test-2-checkbox'));
    expectNotToHaveFocusRingStyle(screen.getByTestId('checkbox-focus-ring-test-3-checkbox'));
  });

  describe('onChange event', () => {
    it('dispatch if mouse clicked', async () => {
      render(<Checkbox id={id} testId={testId} checkedState="checked" onChange={onChange} />);

      await userEvent.click(getCheckbox());
      expect(onChange).toHaveBeenCalledTimes(1);
    });

    it('dispatch if Keyboard Space pressed', async () => {
      render(<Checkbox id={id} testId={testId} checkedState="partial" onChange={onChange} />);

      await userEvent.tab();
      await userEvent.keyboard('[Space]');
      expect(onChange).toHaveBeenCalledTimes(1);
    });
  });

  describe('onChange event with label', () => {
    it('dispatch if mouse clicked', async () => {
      render(<Checkbox id={id} testId={testId} checkedState="checked" label={labelText} onChange={onChange} />);

      await userEvent.click(getLabel());
      expect(onChange).toHaveBeenCalledTimes(1);
    });

    it('dispatch if Keyboard Space pressed', async () => {
      render(<Checkbox id={id} testId={testId} checkedState="partial" label={labelText} onChange={onChange} />);

      await userEvent.tab();
      await userEvent.keyboard('[Space]');
      expect(onChange).toHaveBeenCalledTimes(1);
    });
  });

  describe('checkbox', () => {
    it('disabled property works and will correctly set the inner Checkbox disabled attribute', () => {
      render(<Checkbox id={id} testId={testId} checkedState="unchecked" disabled onChange={onChange} />);

      expect(screen.getByTestId(`${testId}-checkbox`)).toHaveClass('disabled');
      expect(screen.getByTestId(`${testId}-label`)).toHaveClass('disabled');

      expect(getCheckbox()).not.toHaveAttribute('aria-disabled');
      expect(getCheckbox()).toBeDisabled();
    });

    it('the input should have an attribute of indeterminate when the checkedState is Partial', async () => {
      render(<Checkbox id={id} testId={testId} checkedState="partial" onChange={onChange} />);
      expect(getCheckbox()).toHaveProperty('indeterminate');
    });
  });
});
