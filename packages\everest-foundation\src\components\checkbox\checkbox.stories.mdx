import { Meta, Story, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { Checkbox } from './checkbox';
import Examples from './checkbox.examples.mdx';
import { action } from '@storybook/addon-actions';

<Meta
  title="Components/Checkbox"
  component={Checkbox}
  parameters={{
    status: {
      type: 'ready',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9941&t=HEIyoS0N6cPMADjO-0',
    },
    controls: {
      sort: ['requiredFirst'],
    },
  }}
  argTypes={{
    ariaLabel: {
      description: "A clear description of the Checkbox's purpose.",
    },
    ariaDescribedBy: {
      description: "Specifies one or more ids of elements with the text that provides additional context for the screen reader users.",
    },
    checkedState: {
      type: 'enum',
      control: 'radio',
      options: ['unchecked', 'checked', 'partial'],
      description: 'Sets the state of the Checkbox.',
      table: {
        defaultValue: { summary: 'unchecked' },
      },
    },
    status: {
      control: 'radio',
      options: ['default', 'error'],
      description: 'Sets the status of the checkbox.',
    },
    errorMessage: {
      type: 'string',
      description: 'Sets the error status message on the checkbox.',
    },
    errorMessagePrefix: {
      type: 'string',
      description: 'Sets the error status message prefix on the checkbox.',
    },
    disabled: {
      description: 'Disables the Checkbox to prevent any user action.',
      table: {
        defaultValue: { summary: false },
      },
    },
    id: {
      description: 'Required.',
      type: { summary: 'string', required: true },
    },
    testId: {
      description: 'An id used for automation testing.',
    },
    onChange: {
      control: '-',
      description: 'Optional callback on change. Required to make the checkbox an interactive element.',
    },
    name: {
      type: 'string',
      description: 'Sets the name attribute on the checkbox.',
    },
    label: {
      description: 'Sets the text of the label.',
    },
  }}
  args={{
    disabled: false,
    id: 'test-checkbox',
    testId: 'test-id',
    checkedState: 'unchecked',
    status: 'default',
    errorMessage: 'Invalid information text',
    errorMessagePrefix: 'Error:',
    label: 'Label',
  }}
/>

# Checkbox

<Examples />

## Live Demo & API

<Canvas>
  <Story name="Checkbox">
    {(args) => {
      const [{ checkedState }, updateArgs] = useArgs();
      const handleClick = (e) => {
        let state;
        switch (checkedState) {
          case 'unchecked':
            state = 'checked';
            break;
          case 'checked':
            state = 'partial';
            break;
          case 'partial':
            state = 'unchecked';
            break;
        }
        updateArgs({
          checkedState: state,
        });
        action('onChange')(e);
      };
      return <Checkbox {...args} checkedState={checkedState} onChange={handleClick} />;
    }}
  </Story>
</Canvas>

<ArgsTable story="Checkbox" />
