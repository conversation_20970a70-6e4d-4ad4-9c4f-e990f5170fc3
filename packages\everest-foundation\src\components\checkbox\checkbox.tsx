/* eslint-disable jsx-a11y/no-redundant-roles */
import * as React from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';

import { CheckboxBase, ICheckboxBaseProps } from '../checkbox-base';
import { Label } from '../label';
import { StatusMessage } from '../status-message';

import styles from './checkbox.module.scss';

export interface ICheckboxProps
  extends Omit<ICheckboxBaseProps, 'className' | 'margin' | 'hideFocusRing' | 'tabIndex'> {
  errorMessage?: string;
  errorMessagePrefix?: string;
  label?: string;
  ref?: React.ForwardedRef<HTMLInputElement>;
}

export const Checkbox = React.forwardRef<HTMLInputElement, ICheckboxProps>((props: ICheckboxProps, ref) => {
  const {
    disabled,
    testId,
    onChange,
    id,
    label,
    checkedState,
    status = 'default',
    errorMessage,
    errorMessagePrefix,
    name,
    ariaLabel,
  } = props;

  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!disabled) {
      onChange?.(e);
    }
  };

  return (
    <>
      <FocusRing canFocusFromMouse variant="focusring" willPersist disabled={disabled}>
        <Label
          disabled={disabled || undefined}
          id={`${id}-label`}
          testId={testId ? `${testId}-label` : undefined}
          textPadding
          alignment="start"
        >
          <CheckboxBase
            id={id}
            testId={testId}
            disabled={disabled}
            checkedState={checkedState}
            status={status}
            onChange={handleChange}
            ariaLabel={ariaLabel}
            ariaDescribedBy={status === 'error' && id ? `${id}-status-message` : undefined}
            name={name}
            ref={ref}
            hideFocusRing
          />
          {label}
        </Label>
      </FocusRing>
      <div className={styles.evrStatusMessage}>
        <StatusMessage
          id={`${id}-status-message`}
          testId={testId && `${testId}-status-message`}
          visible={!disabled}
          variant={status}
          statusMessagePrefix={errorMessagePrefix}
          statusMessage={errorMessage}
        />
      </div>
    </>
  );
});

Checkbox.displayName = 'Checkbox';
