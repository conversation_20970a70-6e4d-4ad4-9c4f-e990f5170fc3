import { useEffect, useRef, useState } from 'react';
import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Combobox } from './combobox';
import { ComboboxOverlayFooter } from './combobox-overlay-footer';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { Button } from '../button';
import { Icon } from '../icon';
import { FunctionUtil } from '@platform/core';
import { sampleData } from './combobox-large-sample-data';
import { Warning } from '../../../.storybook/docs/shared/status-banner.tsx';
import { Markdown } from '@storybook/blocks';
import IDataItem from '../list-item/i-data-item.md?raw';

export const scope = {
  Combobox,
  Button,
  FunctionUtil,
  Icon,
  ComboboxOverlayFooter,
  sampleData,
  useEffect,
  useRef,
  useState,
};

## Usage

Combobox component lets the user choose a single predefined option from a list of options and supports filtering based on user input. Combobox is a controlled component so it doesn't store or update its states.

There are props required to control the state of the component to create a functional combobox.

- `inputValue`: Controls the search input value
- `onInputValueChange`: Callback when input value has updated

The option is of type `IDataItem` which is defined as the following

<Markdown>{IDataItem}</Markdown>

`title` is required and used as a display value for each option in Combobox.

Let's start with creating a basic Combobox

export const basicComboboxCode = `() => {
  const options = [
    { id:'id-0', title: 'Sales'},
    { id:'id-1', title: 'Marketing'},
    { id:'id-2', title: 'Support'},
    { id:'id-3', title: 'Human Resources'},
    { id:'id-4', title: 'Product & Technology'},
  ];
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState(options);
  const handleInputValueChange = (value) => {
    setInputValue(value);
    const filtered = options.filter((option) =>
      option.title.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredOptions(filtered);
  };
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
    setFilteredOptions(options);
  };
  const handleBlur = () => {
    if (value) {
      setInputValue(value.title);
    } else {
      setInputValue('');
    }
    setFilteredOptions(options);
  };
  const handleClear = () => {
    setValue(undefined);
    setInputValue('');
  };
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: '1rem',
        width: '50%',
      }}
    >
      <Combobox
        id={'combobox1'} 
        label="Departments" 
        inputValue={inputValue}
        options={filteredOptions}
        onInputValueChange={handleInputValueChange}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        onClear={handleClear}
        noResultsText="No matches found"
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={basicComboboxCode} />

- `inputValue` - sets the value of the Combobox input (`string`)
- `onInputValueChange` - callback for when the input value changes (`string`)
- `value` - sets the selected option (`IDataItem`)
- `onChange` - callback for when selected option changes (`IDataItem`)

When user types into the input box the `onInputValueChange` callback is used to:

- update the `inputValue` state
- filter options to only those that contain the string from the `onInputValueChange` callback in their `title`

When user selects an option from the dropdown the `onChange` callback is used to:

- update the `value` state (`IDataItem`)
- update the `inputValue` to the `title` value of the selected option

In addition Combobox in this demo updates the `inputValue` in the following cases:

- when combobox loses focus - the `inputValue` gets updated to either the `title` of the selected option or an empty string
- when Clear button is activated - the `inputValue` gets updated to an empty string

For users seeking to create custom list items for the combobox, the `itemRenderer` prop is available.
This allows users to pass in JSX elements for rendering custom list items.
However, it's important to note that with this flexibility comes responsibility.
Users are expected to handle any potential issues, particularly regarding accessibility.

Customized list items should adhere to the following guidelines:

- They should be read-only.
- Avoid heavy integration with other components.

## Variations

Combobox has different variations based on the props being used.

The following examples showcase different variations with the combination of props being used, these examples only show the visual aspect, but these are not functional.

export const variationsCode = `() => {
    const styles = {
        row: {
          width: '55%',
          height: '120px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
            gap: '24px'
        },
        headerText: {
          marginBlockEnd: '8px',
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const HeaderText = ({ children }) => (
        <div style={styles.headerText} className='evrBodyText evrBold'>{children}</div>
    );
    const options = [
      { id:'id-0', title: 'Sales'},
      { id:'id-1', title: 'Marketing'},
      { id:'id-2', title: 'Support'},
      { id:'id-3', title: 'Human Resources'},
      { id:'id-4', title: 'Product & Technology'},
      { id:'id-5', title: 'Services'},
      { id:'id-6', title: 'Operations'},
      { id:'id-7', title: 'Customer Experience'},
      { id:'id-8', title: 'Finance'},
      { id:'id-9', title: 'Legal'},
    ];
    const optionsWithIcons = [
      { title: 'First Option', iconName: 'heartOutline', id: 'item7' },
      { title: 'Second Option', iconName: 'home', id: 'item8' },
      { title: 'Third Option', iconName: 'help', id: 'item9' }
    ];
    return (
      <div style={styles.column}>
        <Row>
          <HeaderText>Combobox options with icons</HeaderText>
          <Combobox
            id={'combobox2'}
            label="Departments" 
            options={optionsWithIcons}
            noResultsText='No results'
          />
        </Row>
        <Row>
          <HeaderText>Combobox with scrollbar</HeaderText>
          <Combobox
            id={'combobox3'}
            label="Departments"
            ariaLabel={'This is an aria-label'}
            options={options}
            noResultsText='No results'
          />
        </Row>
        <Row>
          <HeaderText>Combobox with selected value</HeaderText>
          <Combobox
            id={'combobox4'}
            label="Departments"
            ariaLabel={'This is an aria-label'}
            options={options}
            value={options[2]}
            inputValue={options[2].title}
            noResultsText='No results'
          />
        </Row>
        <Row>
          <HeaderText>Combobox with hidden clear button</HeaderText>
          <Combobox
            id={'combobox5'}
            label="Departments"
            ariaLabel={'This is an aria-label'}
            hideClearButton
            options={options}
            value={options[2]}
            inputValue={options[2].title}
            noResultsText='No results'
          />
        </Row>
        <Row>
          <HeaderText>Disabled Combobox</HeaderText>
          <Combobox
            id={'combobox6'}
            label="Departments"
            disabled
            ariaLabel={'This is an aria-label'}
            options={options}
            noResultsText='No results'
          />
        </Row>
        <Row>
          <HeaderText>ReadOnly Combobox</HeaderText>
          <Combobox
            id={'combobox7'}
            label="Departments"
            readOnly
            ariaLabel={'This is an aria-label'}
            options={options}
            value={options[2]}
            inputValue={options[2].title}
            noResultsText='No results'
          />
        </Row>
        <Row>
          <HeaderText>Combobox with helperText</HeaderText>
          <Combobox
            id={'combobox8'}
            label="Departments"
            ariaLabel={'This is an aria-label'}
            options={options}
            helperText="This is a helper text"
            noResultsText='No results'
          />
        </Row>
        <Row>
          <HeaderText>Combobox with an error message</HeaderText>
          <Combobox
            id={'combobox9'}
            label="Departments"
            ariaLabel={'This is an aria-label'}
            options={options}
            status="error"
            statusMessage="This is an error message"
            noResultsText='No results'
          />
        </Row>
      </div>
    );
}`;

<CodeExample scope={scope} code={variationsCode} />

### Combobox with Data Loading

Use the `loading` prop in combination with `loadingText` and `noResultsText` to indicate to the user when you are fetching data. The below example shows how data may be loaded when typing.

Additionally, the `overlayFooter` prop accepts a component to be used as a footer within the overlay. The example below contains our recommended component for this case, however, a custom component may be provided. The footer is only meant to be used in conjunction with data loading.

For accessibility requirements to be met the footer component must be provided with an `id`. This will allow the footer to be announced by a screen reader upon reaching the last option within the overlay.

<Warning>Implementations of the footer outside of data loading are **not supported** by Everest.</Warning>

export const comboboxWithDataLoadTypingCode = `() => {
  const maxOptionsDisplayed = 10;
  const exampleProps = {
    id: 'combobox-data-loading-typing-example',
    testId: 'combobox-data-loading-typing-example-test-id',
    status: 'default',
    disabled: false,
    altTextMap: {
      clearButton: 'Clear Input',
      selectedItem: '{0} selected',
      unselectedItem: '{0} not selected',
      spinnerAriaLabel: 'Loading',
    },
    maxItems: 5,
    hideClearButton: false,
    ariaLabel: 'Type and Data Load Combobox',
    label: 'Type and Data Load Combobox',
    helperText: 'Data load is delayed by one second',
    helperTextPrefix: 'Note:',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',
    clearButtonAriaLabel: 'Clear Combobox value',
    loadingText: 'Loading',
    noResultsText: 'No results found.',
  };
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [value, setValue] = useState();
  const [overlayFooter, setOverlayFooter] = useState(undefined);
  const buildOverlayFooter = () => {
    const undisplayedOptionsCount = options.length - maxOptionsDisplayed;
    const overlayFooterLabel = undisplayedOptionsCount + ' more options. Continue typing to refine further.';
    if (options.length > maxOptionsDisplayed) {
      return {
        id: 'combobox-overlay-footer-id',
        testId: 'combobox-overlay-footer-id',
        label: overlayFooterLabel,
        ariaLabel: overlayFooterLabel,
        iconName: 'information',
      };
    } else {
      return undefined;
    }
  };
  useEffect(() => {
    setOverlayFooter(buildOverlayFooter());
    setLoading(false);
  }, [options]);
  const simulatedAPIDelay = 1000;
  const typingDebounce = 500;
  const handleFocus = () => {
    if (value === undefined) {
      setLoading(true);
      fetchAndFilter('');
    }
  }
  const handleInputValueChange = (value) => {
    setInputValue(value);
    setLoading(true);
    setOverlayFooter(undefined);
    fetchAndFilter(value);
  };
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
  };
  const handleBlur = (e) => {
    if (value) {
      setInputValue(value.title);
      setOptions([value]);
    } else {
      setInputValue('');
      setOptions([]);
      setOverlayFooter(undefined);
    }
  };
  const handleClear = () => {
    setValue(undefined);
    setInputValue('');
    setOptions([]);
    setLoading(true);
    setOverlayFooter(undefined);
  };
  const fetchAndFilter = React.useCallback(
    // FunctionUtil is imported from @platform/core
    FunctionUtil.debounce((title) => {
      // simulated API call
      setTimeout(() => {
        const fetchedOptions = sampleData;
        if (title.trim().length > 0) {
          const filteredOptions = fetchedOptions.filter((option) =>
            option.title.toUpperCase().includes(title.trim().toUpperCase())
          );
          setOptions(filteredOptions);
        } else {
          setOptions(fetchedOptions);
        }
      }, simulatedAPIDelay);
    }, typingDebounce),
    [options, value]
  );
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: '1rem',
        width: '50%',
      }}
    >
      <Combobox
        {...exampleProps}
        inputValue={inputValue}
        options={options.slice(0, maxOptionsDisplayed)}
        onInputValueChange={handleInputValueChange}
        value={value}
        onFocus={handleFocus}
        onChange={handleChange}
        onBlur={handleBlur}
        onClear={handleClear}
        loading={loading}
        overlayFooter={
          overlayFooter !== undefined && (
            <ComboboxOverlayFooter
              id={overlayFooter.id} // ID is required for a11y, see storybook notes for details
              testId={overlayFooter.testId}
              label={overlayFooter.label}
              ariaLabel={overlayFooter.ariaLabel}
              iconName={overlayFooter.iconName}
            />
          )
        }
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={comboboxWithDataLoadTypingCode} />

### Combobox with Custom List Items

Here's an example demonstrating the utilization of `itemRenderer` to craft custom list items for the Combobox.<br /><br />
Customized list items should adhere to the following guidelines:

- Users are expected to handle any potential issues, particularly regarding accessibility.
- They should be read-only.
- Avoid heavy integration with other components.

export const comboboxWithCustomListItemsCode = `() => {
  const exampleProps = {
    id: 'combobox-with-custom-list-example',
    testId: 'combobox-with-custom-list-example-test-id',
    status: 'default',
    disabled: false,
    altTextMap: {
      clearButton: 'Clear Input',
      selectedItem: '{0} selected',
      unselectedItem: '{0} not selected',
    },
    maxItems: 5,
    hideClearButton: false,
    ariaLabel: 'Custom List Combobox',
    label: 'Custom List Combobox',
    clearButtonAriaLabel: 'Clear Combobox value',
    loadingText: 'Loading',
  };
  const options = [
      { id: 'id-0', title: 'Damon Stoudamire', iconName: 'heartOutline', additionalData: { description: '1st Rookie of The Year' } },
      { id: 'id-1', title: 'Vince Carter', iconName: 'rocketship', additionalData: { description: 'Most explosive' } },
      { id: 'id-2', title: 'Kyle Lowry', iconName: 'wrench', additionalData: { description: 'Pitbull' } },
      { id: 'id-3', title: 'DeMar DeRozan', iconName: 'trashCan', additionalData: { description: 'Disappears in playoffs' } },
      { id: 'id-4', title: 'Kawhi Leonard', iconName: 'performance', additionalData: { description: 'Brought 1st championship' } },
    ];
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState();
  const [filteredOptions, setFilteredOptions] = React.useState(options);
  const handleInputValueChange = (value) => {
    setInputValue(value);
    const filtered = options.filter((option) => option.title.toLowerCase().includes(value.toLowerCase()));
    setFilteredOptions(filtered);
  };
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
    setFilteredOptions(options);
  };
  const handleBlur = (e) => {
    if (value) {
      setInputValue(value.title);
    } else {
      setInputValue('');
    }
    setFilteredOptions(options);
  };
  const handleClear = () => {
    setInputValue('');
    setValue(undefined);
  };
  React.useEffect(() => {
    setInputValue('');
    setValue(undefined);
    setFilteredOptions(options);
  }, []);
  const itemRenderer = (dataItem, selected) => {
    const containerStyle = {
        display: 'flex',
        overflow: 'hidden',
    };
    const leftSideStyle = {
        display: 'flex',
        alignItems: 'center',
        marginLeft: '-0.1rem',
        marginRight: '0.5rem',
    };
    const rightSideStyle = {
        overflow: 'hidden',
        marginRight: '0.75rem',
    };
    const textResultStyle = {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
    };
    return <div style={containerStyle}>
        <div style={leftSideStyle}>
            <Icon name={dataItem.iconName} />
        </div>
        <div style={rightSideStyle}>
            <p style={textResultStyle} className={selected ? '_evrBodyText1 evrBold' : '_evrBodyText1'}>{dataItem.title}</p>
            <p style={textResultStyle} className='_evrBodyText1'>{dataItem.additionalData['description']}</p>
        </div>
    </div>
  };
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: '1rem',
        width: '50%',
      }}
    >
      <Combobox
        {...exampleProps}
        inputValue={inputValue}
        options={filteredOptions}
        onInputValueChange={handleInputValueChange}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        onClear={handleClear}
        itemRenderer={itemRenderer}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={comboboxWithCustomListItemsCode} />

### Combobox with Grouped Options

Combobox supports grouping of options. The following example demonstrates the usage of grouped options.

export const groupedOptionsCode = `() => {
  const options = [
    { id: 'group-1', title: 'Group 1', items: [
        { id:'id-1', title: 'Human Resources'},
        { id:'id-2', title: 'Product & Technology'},
        { id:'id-3', title: 'Finance'},
      ]
    },
    { id: 'group-2', title: 'Group 2', items: [
        { id:'id-4', title: 'Services'},
        { id:'id-5', title: 'Operations'},
        { id:'id-6', title: 'Delivery'},
      ]
    },
  ];
  const filterByTitle = (options, input) => {
    return options.reduce((acc, option) => {
      if (option.items?.length) {
        const filteredItems = filterByTitle(option.items || [], input);
        if (filteredItems.length > 0) {
          acc.push({ ...option, items: filteredItems });
        }
      } else if (option.title.toLowerCase().includes(input.toLowerCase())) {
        acc.push(option);
      }
      return acc;
    }, []);
  };
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState(options);
  const handleInputValueChange = (newInputValue) => {
    if (newInputValue.length === 0) {
          setFilteredOptions(options);
          setValue(undefined);
    }
    setInputValue(newInputValue);
    const filtered = filterByTitle(options, newInputValue);
    setFilteredOptions(filtered);
  };
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
    setFilteredOptions(options);
  };
  const handleBlur = () => {
    if (value) {
      setInputValue(value.title);
    } else {
      setInputValue('');
    }
    setFilteredOptions(options);
  };
  const handleClear = () => {
    setValue(undefined);
    setInputValue('');
  };
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: '1rem',
        width: '50%',
      }}
    >
      <Combobox
        id={'combobox1'} 
        label="Departments" 
        inputValue={inputValue}
        options={filteredOptions}
        onInputValueChange={handleInputValueChange}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        onClear={handleClear}
        noResultsText="No matches found"
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={groupedOptionsCode} />

### Accessing Combobox using ref

Click on the Button to access the Combobox, refer to the console for the element details.

export const refCode = `()=>{
    const ref=React.useRef(null);
    const firstOption = { title: 'First Option', id: 'item1' };
    const secondOption = { title: 'Second Option', id: 'item2' };
    const thirdOption = { title: 'Third Option', id: 'item3' };
    const fourthOption = { title: 'Fourth Option', id: 'item4' };
    const fifthOption = { title: 'Fifth Option', id: 'item5' };
    const sixthOption = { title: 'Sixth Option', id: 'item6' };
    const options = [
      firstOption,
      secondOption,
      thirdOption,
      fourthOption,
      fifthOption,
      sixthOption,
    ];    
    const [inputValue, setInputValue] = React.useState('');
    const [value, setValue] = React.useState(null);
    const [filteredOptions, setFilterOptions] = React.useState(options);
    const handleInputValueChange = (value) => {
      setInputValue(value);
    };
    const handleChange = (value, index) => { 
      setValue(value); 
    };
    const handleClear = () => {
      setValue(undefined);
      setInputValue('');
    }
    const handleBlur = () => {
      setInputValue(value? value.title : '');
    }
    React.useEffect(()=>{        
      setInputValue(value? value.title : '');
    },[value]);
    React.useEffect(()=>{       
      if (value && (!inputValue || inputValue === value.title)) {
        setFilterOptions(options);
      } else {
        setFilterOptions(
          options.filter((option) =>
            option.title.toUpperCase().includes(inputValue.toUpperCase())
          )
        );
      }
    },[inputValue]);
    return (
      <div
          style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              gap: '1rem',
              width: '45%'
          }}
      >
        <Button 
          id='access-element-btn' 
          label="Click to access element" 
          onClick={()=>{
            console.log(ref.current); 
            ref.current.focus();
          }}
        />
        <Combobox
          id={'combobox9'}
          ref={ref}
          label="Departments" 
          ariaLabel={'This is an aria-label'}
          inputValue={inputValue}
          options={filteredOptions}
          onInputValueChange={handleInputValueChange}
          value={value} 
          onChange={handleChange} 
          onBlur={handleBlur}
          onClear={handleClear}
          noResultsText='No results'
        />
      </div>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

A Combobox displays an input component combined with a ListBox with a predefined ListItem options, which lets the user choose a single option from a list of options.

Combobox is suitable for predefined selection for a list larger than 7 items, which users can search and filter to make a rapid selection.

## Accessibility

If using the `overlayFooter`, providing the component with an `id` is necessary to have the footer announced upon reaching the last option within the overlay.

### Text

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

To ensure Combobox is accessible, it is recommended the following props be provided:

- `label` OR `ariaLabel`
- `altTextMap`

| Prop       | Description                                                        |
| ---------- | ------------------------------------------------------------------ |
| label      | Visual label above the component                                   |
| ariaLabel  | Accessible input label for screen readers                          |
| altTextMap | Contains string templates used by LiveAnnouncer and Screen Readers |

As part of `altTextMap`, the following values should be provided:

| Label            | Description                                                                                                                                        | <div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div> |
| ---------------- | -------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------- |
| spinnerAriaLabel | Aria label for announcing loading spinner                                                                                                          | "Loading"                                                 |
| clearButton      | Button to clear Combobox                                                                                                                           | "Clear Input"                                             |
| selectedItem     | Selection announcement text for selected item. The component will internally replace the placeholder `{0}` with the item's title as appropriate.   | "{0} selected"                                            |
| unselectedItem   | Selection announcement text for unselected item. The component will internally replace the placeholder `{0}` with the item's title as appropriate. | "{0} not selected"                                        |

### Keyboard

The keyboard behavior should align with [Material Design's Combobox](https://mui.com/components/autocomplete/#combo-box), with the addition of a focusable clear button.

When NVDA is enabled, the `Esc` key needs to be pressed twice in order to close the overlay. The 1st key press is to switch to NVDA's browse mode and the 2nd press is to collapse the overlay.

- https://github.com/nvaccess/nvda/issues/4428
- https://github.com/nvaccess/nvda/issues/15170
- https://github.com/nvaccess/nvda/issues/10608
