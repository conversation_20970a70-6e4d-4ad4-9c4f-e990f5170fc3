# Combobox

## Summary

Research and document implementations for the Everest Combobox.

- Start Date: 2022-06-08
- Figma link: <https://www.figma.com/file/YhvqNFsWapyJnaoyAiDNMm/%F0%9F%A7%AADropdown?node-id=2%3A3>

## Detailed Design

Combobox component is a controlled component, it doesn't store or update its states.

We will be utilizing the select container and list box component.

Select container should handle Focus ring and the behavior of popup overlay and the list box will take care of the list item.

## API

1. **testId**: `undefined | string`  
   Sets **data-test-id** attribute on the html element.
1. **ariaLabel**: `undefined | string`  
   `aria-label` for _combobox_, if set, this will be used, otherwise use **label**/**placeholder**.
1. **id**: `string`
   Required id for the _combobox_.
1. **name**: `undefined | string`  
   Defines a **name** for the _combobox_.
1. **disabled**: `boolean`  
   Sets the `disabled` attribute on the _combobox_.
1. **required**: `boolean`  
   Sets the `required` attribute on the _combobox_. Adds the asterisk to the **label** / **placeholder**.
1. **label**: `undefined | string`  
   Optional user provided **label**. If none is given, no **label** is rendered. When **label** set, automatically set `htmlFor` to _combobox_ **id**.
1. ~~**placeholder**: `undefined | string`~~  
   ~~Optional user provided **placeholder**. The hint that should be displayed before a value is selected for the _combobox_.~~
1. **helperTextPrefix**: `undefined | string`
   Optional user provided prefix of helper Text.
1. **helperText**: `undefined | string`  
   Optional user provided help text. When set, _combobox_ **ariadescribedby** will set to **helperText** **id**.
1. **status**: `TSelectContainerTriggerAreaStatus`  
   Indicates the state of the _combobox_.
1. **statusMessagePrefix**: `undefined | string`
   Optional user provided prefix of status message.
1. **statusMessage**: `undefined | string`
   Optional user provided status message. When set, _combobox_ **ariadescribedby** will set to **statusMessage** **id**, this will override **helperText**. This will be used only when **status** is set to "error" or "success".
1. **value**: `undefined | IDataItem`  
   Allows to set _combobox_ value.
1. **inputValue**: `undefined | string`  
   Allows to set input value.
1. **options**: `IDataItem[]`
   _combobox_ options - `IDataItem[]` as

   ```typescript
   export interface IDataItem {
     title: string;
     value?: string;
     id: string;
     iconName?: TIconName;
     additionalData?: unknown;
     type?: TDataItemType;
     ariaDescribedBy?: string;
   }
   ```

1. ~~**itemRenderer**: `undefined | (dataItem: IDataItem, selected: boolean) => React.ReactNode`~~
   ~~Custom item style depended on `IDataItem[]`.~~
1. **maxItems**: `undefined | number`  
   Max number of items displayed on overlay.
1. ~~**overrideNoResultRender**: `undefined | (id: string) => React.ReactNode`~~
   ~~Override template for no Result found, trigger when options length is less than 1.~~
1. **onChange**: `undefined | (option: IDataItem, index: number) => void`  
   Callback when _combobox_ selected value change.
1. **onInputValueChange**: `undefined | (value: string) => void`  
   Callback when _combobox_ input value change.
1. **hideClearButton;**: `boolean`  
   Hides clear button regardless of input value.
1. **onClear**: `undefined | () => void`  
   Callback when clear button is clicked.
1. **onFocus**: `undefined | Function`  
   Optional callback on focus.
1. **onBlur**: `undefined | Function`  
   Optional callback on blur.
1. **altTextMap**: `ISelectContainerTriggerAreaAltText`  
   Localized alt text for decorative elements.
1. **noResultsText**: `string`
   Text shown when there are no results.

## Usage

Allows the user to select an option from the dropdown and filter options using input string.

## Prerequisite/Other Components

The following components will be re-used:

1. select container
1. list box
1. list item

## Accessibility

The keyboard behavior should be the same as the following link

- <https://mui.com/components/autocomplete/>

For accessiblity we will be utilizing the following Roles and Attributes:

- Role
  - `combobox`
  - `listbox`
  - `option`
- Attribute
  - `aria-controls`
  - `aria-expanded`
  - `aria-activedescendant`
  - `aria-selected`

Following reference for accessbility implementation

- <https://www.24a11y.com/2019/select-your-poison/>
- <https://w3c.github.io/aria-practices/examples/combobox/combobox-select-only.html>
- <https://w3c.github.io/aria-practices/examples/combobox/combobox-autocomplete-both.html>

When NVDA is enabled, the `Esc` key needs to be pressed twice in order to close the overlay. The 1st key press is to switch to NVDA's browse mode and the 2nd press is to collapse the overlay.

- https://github.com/nvaccess/nvda/issues/4428
- https://github.com/nvaccess/nvda/issues/15170
- https://github.com/nvaccess/nvda/issues/10608

## Alternatives/Trade-Offs

using children prop instead of options prop

- dropdown component is using the options prop, changing to children prop, we will not be able to reuse the existing list box and list item component.

- Material UI is using options for autocomplete combobox

## Q&A

1. when list item is softSelected and using tab to leave the combobox, should it trigger the selection of the softSelected list item?

- Ans: Keep it the same as dropdown - it would not autoSelect the softSelected Item. (If we are decided to change the behavior, this will be future iteration - It is possible to have both function for different use cases.)

1. on Figma file - When the combobox is in a focus state, it should expand to show all options. (This is not the behavior of the dropdown, when we using mouse click, the overlay will be open, otherwise, it required a key press (arrowUp, arrowDown, enter, etc)), please confirm the behavior.

- Ans: Keep the behavior the same as the dropdown, Figma is required a new refresh. It will be revisited again for new Figma documentation.

1. Question on Figma doc - Don'ts

- menu, navigation menu?
- 3 - 7 items then apply a browser defined scrollbar? For combobox, should we limit the number?
- Ans: Ditto

1. Should the combobox auto complete like the following example <https://w3c.github.io/aria-practices/examples/combobox/combobox-autocomplete-both.html>

- Ans: autoComplete feature is for future iteration, out of scope for now.

1. Accessibility Default behavior link is outdated, and that's the dropdown component behavior.

- Ans: Accessibility document is outdated on Figma, new documentation will be provided.

1. When searching is happening, do we need indicator to show "Loading"?

- Ans: Not for the initial comboBox, the loading area will be different Epic. TDB. - at the meantime, no visual effect while loading.

## Future Considerations

1. virtual scrolling
1. overlay appear on top of the combobox if there is not enough room in the viewport. or a design approved 'paper' method to fit the overlay in screen.
1. **dir**: `"ltr" | "rtl"`  
   Specifies the direction of the text in the _combobox_ and options.

1. For autocomplete - keyboard behavior should be the same as

- <https://www.w3.org/WAI/ARIA/apg/example-index/combobox/combobox-autocomplete-both.html>

## Other Design Systems

For example:

**Material UI** - <https://mui.com/material-ui/react-autocomplete/>

- Material UI's combo box is consider Autocomplete components
- It has many useful props that we can consider (iteration)
  - autoComplete
  - autoHighlight
  - autoSelect
  - blurOnSelect
  - etc (<https://mui.com/material-ui/api/autocomplete/>)

### Shopify Polaris - <https://polaris.shopify.com/components/forms/combobox#section-related-components>

- using children prop (ReactElement | null)
- willLoadMoreOptions and onScrolledToBottom prop are interesting for lazy load, can look more into it on virtual scrolling.

### Microsoft Fluent UI - <https://developer.microsoft.com/en-us/fluentui#/controls/web/dropdown>

- options prop (IDropdownOption) -> we are using IDataItem
- using defaultSelectedKeys and selectedKeys string[] | number[] instead of value

I prefer our implementation, using value, inputValue is better for combobox, as we are not handling multiSelect yet

### Carbon Design System - <https://carbondesignsystem.com/components/dropdown/usage/#combo-box>

Carbon Design System is using downshift

- direction prop (We are using selectContainer to handle that)

## Required PBIs

1. [[ComboBox] - Create Component](https://ceridianpt.atlassian.net/browse/EVR-407)

## Acceptance Criteria
