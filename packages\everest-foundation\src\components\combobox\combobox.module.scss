@use '../../mixins.scss' as mixins;
@use '../../index.scss' as helper;

.evrCombobox {
  width: 100%;
  border: 0;
  outline: 0;
  padding: 0;
  background: var(--evr-surfaces-primary-default);
  color: var(--evr-content-primary-default);
  cursor: default;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-inline: var(--evr-spacing-xs);
  box-sizing: border-box;
  height: 100%;
  border-radius: var(--evr-radius-2xs);
  cursor: text;

  &.disabled {
    cursor: not-allowed;
    color: var(--evr-inactive-content);
    background: var(--evr-inactive-surfaces);
    @media (forced-colors: active) {
      color: GrayText;
    }
  }

  &.readOnly {
    background-color: var(--evr-inactive-surfaces);
  }

  &.hasValue {
    padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    padding-inline-end: calc(
      var(--evr-spacing-md) + var(--evr-spacing-2xs) + var(--evr-spacing-xs) - var(--evr-border-width-thin-px)
    ); // icons width (24px) + icon padding (8px) + end padding (12px) - border
  }

  &.error {
    padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thick-px));
  }

  &.clearButton {
    padding-inline-end: calc(
      (2 * var(--evr-spacing-md)) + (3 * var(--evr-spacing-2xs)) + var(--evr-spacing-xs)
    ); // 2 icons width (24px) + 3 icon padding (8px) + end padding (12px) + divider - border
  }
}

.evrComboboxNoResult {
  @include mixins.listItemNoResults;
}

.evrOverlayContainer {
  margin-block-start: calc(var(--evr-border-width-thin-px) * -1); // prevents text shifting 1px due to borders
  border-radius: var(--evr-radius-2xs);

  &.error {
    margin-block-start: calc(var(--evr-border-width-thick-px) * -1); // prevents text shifting 2px due to borders
  }
}

.evrComboboxVisuallyHidden {
  @include helper.visuallyHidden;
}
