import { useState, useEffect } from 'react';
import { <PERSON>a, Story, Canvas } from '@storybook/addon-docs';
import { Combobox, ComboboxOverlayFooter } from './';
import { Button } from '../button';
import { screen, userEvent, within } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

export const options = [
  { id: '0', title: 'First Option' },
  { id: '1', title: 'Second Option' },
  { id: '2', title: 'Third Option' },
  { id: '3', title: 'Fourth Option' },
  { id: '4', title: 'Fifth Option' },
  { id: '5', title: 'Sixth Option' },
  {
    id: 'group-1',
    title: 'Group 1',
    items: [
      { id: '6', title: 'Seventh Option' },
      { id: '7', title: 'Eight Option' },
    ],
  },
  {
    id: 'group-2',
    title: 'Group 2',
    items: [
      { id: '8', title: 'Ninth Option' },
      { id: '9', title: 'Tenth Option' },
    ],
  },
];

export const dummyOption = { id: 'dummy', title: 'dummy' };
export const icons = ['heartOutline', 'home', 'help', 'phone', 'pin', 'search'];
export const optionsWithIcons = [
  ...options.map((item, ind) => {
    return { iconName: icons[ind], ...item };
  }),
];
export const longOptionContent = {
  id: '10',
  title:
    'This is a very long content option, it is so long that the content width is bigger than List Item width, it should be truncated and using tooltip. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.',
};
export const longLabel =
  'This is a very long label. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.';
export const helperText = 'This is some helper text';
export const helperTextPrefix = 'Hint:';
export const statusMessage = 'This is a status message';
export const statusMessagePrefix = 'Prefix:';
export const testId = 'combobox-test-id';
export const id = 'combobox-id';
export const externalButtonTestId = 'external-button-test-id';

<Meta
  title="Testing/Automation Test Cases/Combobox"
  component={Combobox}
  decorators={[ChromaticDecorators.padStory, ChromaticDecorators.setHeightTo100vh]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    options: [...options, longOptionContent],
    id: 'combobox',
    label: 'This is a label',
    ariaLabel: 'This is an aria-label',
    testId: testId,
    noResultsText: 'No results',
    altTextMap: {
      clearButton: 'Clear button',
    },
    loadingText: 'Loading',
  }}
/>

# Combobox

## Live Demo

export const getCombobox = (canvasElement) => within(canvasElement).getByRole('combobox');

<Canvas>
  <Story name="Combobox with no label">{(args) => <Combobox {...args} label={''}></Combobox>}</Story>
</Canvas>

<Canvas>
  <Story name="Combobox with label">{(args) => <Combobox {...args}></Combobox>}</Story>
</Canvas>

<Canvas>
  <Story name="Combobox with long label">{(args) => <Combobox {...args} label={longLabel}></Combobox>}</Story>
</Canvas>

<Canvas>
  <Story name="Combobox with value">
    {(args) => <Combobox {...args} value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with value and hidden clear button">
    {(args) => <Combobox {...args} value={options[1]} inputValue={options[1].title} hideClearButton></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with long value">
    {(args) => <Combobox {...args} value={longOptionContent} inputValue={longOptionContent.title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with long value and no label">
    {(args) => (
      <Combobox {...args} label={''} value={longOptionContent} inputValue={longOptionContent.title}></Combobox>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with invalid value">{(args) => <Combobox {...args} value={dummyOption}></Combobox>}</Story>
</Canvas>

<Canvas>
  <Story name="Combobox as Required field with value">
    {(args) => <Combobox {...args} required value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Combobox">{(args) => <Combobox {...args} disabled></Combobox>}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled Combobox with value">
    {(args) => <Combobox {...args} disabled value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Combobox with value and helper text">
    {(args) => (
      <Combobox
        {...args}
        disabled={true}
        value={options[1]}
        inputValue={options[1].title}
        helperText={helperText}
      ></Combobox>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly Combobox">{(args) => <Combobox {...args} readOnly></Combobox>}</Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly Combobox with value">
    {(args) => <Combobox {...args} readOnly value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Pressing Enter on a ReadOnly Combobox with value"
    play={async ({ canvasElement }) => {
      getCombobox(canvasElement).focus();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <Combobox {...args} readOnly value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with helper text">{(args) => <Combobox {...args} helperText={helperText}></Combobox>}</Story>
</Canvas>

<Canvas>
  <Story name="Combobox with helper text and helper text prefix">
    {(args) => <Combobox {...args} helperText={helperText} helperTextPrefix={helperTextPrefix}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with error message">
    {(args) => (
      <Combobox
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></Combobox>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with success message">
    {(args) => (
      <Combobox
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></Combobox>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Overlay and scroll bar"
    play={async ({ canvasElement }) => {
      await userEvent.click(getCombobox(canvasElement));
    }}
  >
    {(args) => <Combobox {...args} options={options}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Overlay and Value"
    play={async ({ canvasElement }) => {
      await userEvent.click(getCombobox(canvasElement));
    }}
  >
    {(args) => <Combobox {...args} value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Focus Ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <Combobox {...args}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Focus Ring and Value"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <Combobox {...args} value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Focus on clear button"
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
    }}
  >
    {(args) => <Combobox {...args} value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Focus Ring, Overlay and scroll bar"
    play={async ({ canvasElement }) => {
      getCombobox(canvasElement).focus();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <Combobox {...args} options={options}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Focus Ring and softSelectedIndex equal to selectedIndex"
    play={async ({ canvasElement }) => {
      getCombobox(canvasElement).focus();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <Combobox {...args} value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Focus Ring, Overlay, Value and scroll bar"
    play={async ({ canvasElement }) => {
      getCombobox(canvasElement).focus();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <Combobox {...args} value={options[1]} inputValue={options[1].title}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Item with Icon and Overlay"
    play={async ({ canvasElement }) => {
      await userEvent.click(getCombobox(canvasElement));
    }}
  >
    {(args) => <Combobox {...args} options={optionsWithIcons}></Combobox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Handling State and Events Combobox">
    {(args) => {
      const options = [
        { id: 'id-0', title: 'First Option 1' },
        { id: 'id-1', title: 'Second Option 1' },
        { id: 'id-2', title: 'Third Option 1' },
        { id: 'id-3', title: 'First Option 2' },
        { id: 'id-4', title: 'Second Option 2' },
        { id: 'id-5', title: 'Third Option 2' },
        { id: 'id-6', title: 'First Option 3' },
        { id: 'id-7', title: 'Second Option 3' },
      ];
      const [inputValue, setInputValue] = React.useState('');
      const [value, setValue] = React.useState(null);
      const [filteredOptions, setFilterOptions] = React.useState(options);
      const handleInputValueChange = (value) => {
        setInputValue(value);
      };
      const handleChange = (value, index) => {
        setValue(value);
      };
      const handleClear = () => {
        setValue(null);
      };
      const handleBlur = () => {
        setInputValue(value ? value.title : '');
      };
      React.useEffect(() => {
        setInputValue(value ? value.title : '');
      }, [value]);
      React.useEffect(() => {
        if (value && (!inputValue || inputValue === value.title)) {
          setFilterOptions(options);
        } else {
          setFilterOptions(options.filter((option) => option.title.toUpperCase().includes(inputValue.toUpperCase())));
        }
      }, [inputValue]);
      return (
        <Combobox
          {...args}
          inputValue={inputValue}
          options={filteredOptions}
          onInputValueChange={handleInputValueChange}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          onClear={handleClear}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox No Results"
    play={async ({ canvasElement }) => {
      await userEvent.type(getCombobox(canvasElement), 'asd');
    }}
  >
    {(args) => {
      const options = [
        { id: 'id-0', title: 'Sales' },
        { id: 'id-1', title: 'Marketing' },
        { id: 'id-2', title: 'Support' },
        { id: 'id-3', title: 'Human Resources' },
        { id: 'id-4', title: 'Product & Technology' },
        { id: 'id-5', title: 'Services' },
        { id: 'id-6', title: 'Operations' },
        { id: 'id-7', title: 'Customer Experience' },
        { id: 'id-8', title: 'Finance' },
        { id: 'id-9', title: 'Legal' },
      ];
      const [inputValue, setInputValue] = React.useState('');
      const [value, setValue] = React.useState(args.inputValue);
      const [filteredOptions, setFilteredOptions] = React.useState(options);
      const handleInputValueChange = (value) => {
        setInputValue(value);
        const filtered = options.filter((option) => option.title.toLowerCase().includes(value.toLowerCase()));
        setFilteredOptions(filtered);
      };
      const handleChange = (item) => {
        setValue(item);
        item ? setInputValue(item.title) : setInputValue('');
        setFilteredOptions(options);
      };
      const handleBlur = () => {
        if (value) {
          setInputValue(value.title);
        } else {
          setInputValue('');
        }
        setFilteredOptions(options);
      };
      const handleClear = () => {
        setInputValue('');
      };
      return (
        <Combobox
          {...args}
          inputValue={inputValue}
          options={filteredOptions}
          onInputValueChange={handleInputValueChange}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          onClear={handleClear}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with active descendant"
    play={async ({ canvasElement }) => {
      await userEvent.click(getCombobox(canvasElement));
      await userEvent.click(await screen.findByText(options[0].title));
    }}
  >
    {(args) => {
      const [inputValue, setInputValue] = React.useState('');
      const [value, setValue] = React.useState(null);
      const [filteredOptions, setFilterOptions] = React.useState(options);
      const handleInputValueChange = (value) => {
        setInputValue(value);
      };
      const handleChange = (value, index) => {
        setValue(value);
      };
      const handleClear = () => {
        setValue(null);
      };
      const handleBlur = () => {
        setInputValue(value ? value.title : '');
      };
      React.useEffect(() => {
        setInputValue(value ? value.title : '');
      }, [value]);
      React.useEffect(() => {
        if (value && (!inputValue || inputValue === value.title)) {
          setFilterOptions(options);
        } else {
          setFilterOptions(options.filter((option) => option.title.toUpperCase().includes(inputValue.toUpperCase())));
        }
      }, [inputValue]);
      return (
        <Combobox
          {...args}
          inputValue={inputValue}
          options={filteredOptions}
          onInputValueChange={handleInputValueChange}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          onClear={handleClear}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox loading"
    play={async ({ canvasElement }) => {
      await userEvent.click(getCombobox(canvasElement));
    }}
  >
    {(args) => <Combobox {...args} options={options} loading={true} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Combobox with external button" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => (
      <>
        <Combobox {...args} value={options[1]} inputValue={options[1].title}></Combobox>
        <Button testId={externalButtonTestId} label="Raptors" />
      </>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with Footer"
    play={async ({ canvasElement }) => {
      await userEvent.click(getCombobox(canvasElement));
    }}
  >
    {(args) => {
      const options = [
        { id: 'id-0', title: 'Sales' },
        { id: 'id-1', title: 'Marketing' },
        { id: 'id-2', title: 'Support' },
        { id: 'id-3', title: 'Human Resources' },
        { id: 'id-4', title: 'Product & Technology' },
        { id: 'id-5', title: 'Services' },
        { id: 'id-6', title: 'Operations' },
        { id: 'id-7', title: 'Customer Experience' },
        { id: 'id-8', title: 'Finance' },
        { id: 'id-9', title: 'Legal' },
      ];
      return (
        <Combobox
          {...args}
          inputValue={''}
          options={options}
          value={undefined}
          overlayFooter={
            <ComboboxOverlayFooter
              id={'combobox-overlay-footer-id'}
              label={'Continue typing to refine further.'}
              ariaLabel={'Continue typing to refine further.'}
              iconName={'information'}
            />
          }
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Combobox with grouped options"
    play={async ({ canvasElement }) => {
      await userEvent.click(getCombobox(canvasElement));
    }}
  >
    {(args) => <Combobox {...args} options={options.slice(6)} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="[Playwright] - Interactions"
    parameters={{ chromatic: Chromatic.DISABLE }}
  >
    {(args) => {
      const [inputValue, setInputValue] = useState('');
      const [value, setValue] = useState();
      const [filteredOptions, setFilteredOptions] = useState(options);
      const handleInputValueChange = (value) => {
        setInputValue(value);
        const filtered = options.filter((option) => option.title.toLowerCase().includes(value.toLowerCase()));
        setFilteredOptions(filtered);
      };
      const handleChange = (item) => {
        setValue(item);
        item ? setInputValue(item.title) : setInputValue('');
        setFilteredOptions(options);
      };
      const handleBlur = (e) => {
        setInputValue(value ? value.title : '');
        setFilteredOptions(options);
      };
      const handleClear = () => {
        setInputValue('');
        setValue(undefined);
      };
      useEffect(() => {
        setInputValue('');
        setValue(undefined);
        setFilteredOptions(options);
      }, [options]);
      return (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            gap: '1rem',
          }}
        >
          <Combobox
            {...args}
            id="enabled-id"
            testId="enabled-test-id"
            label="enabled"
            ariaLabel="enabled"
            inputValue={inputValue}
            options={filteredOptions}
            onInputValueChange={handleInputValueChange}
            value={value}
            onChange={handleChange}
            onBlur={handleBlur}
            onClear={handleClear}
          />
          <Combobox {...args} id="readonly-id" testId="readonly-test-id" label="readonly" ariaLabel="readonly" readOnly />
          <Combobox {...args} id="disabled-id" testId="disabled-test-id" label="disabled" ariaLabel="disabled" disabled />
          <Button id="button-id" testId="button-test-id" label="button" />
        </div>
      );
    }}

  </Story>
</Canvas>
