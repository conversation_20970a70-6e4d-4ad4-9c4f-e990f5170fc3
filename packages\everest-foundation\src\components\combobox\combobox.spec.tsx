import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Combobox } from './combobox';
import { ComboboxOverlayFooter } from './combobox-overlay-footer';
import { mockResizeObserver } from '../../test-utils';
import { SidePanel } from '../side-panel';

const testId = 'combobox-test-id';
const ariaLabel = 'This is an aria-label';
const options = [
  { title: 'First Option', id: 'item1' },
  { title: 'Second Option', id: 'item2' },
  { title: 'Third Option', id: 'item3' },
];
const id = 'combobox-1';
const label = 'This is a label';
const helperText = 'This is some helper text';
const helperTextPrefix = 'Hint:';
const statusMessage = 'This is a status message';
const statusMessagePrefix = 'Prefix:';
const firstOption = options[1];
const invalidValue = { title: 'dummy', id: 'item0' };
const clearButtonAriaLabel = 'Clear combobox value';
const selectedItemAriaLabel = '{0} selected';
const noResultsText = 'No results';
const loadingText = 'Loading';

const defaultProps = {
  testId: testId,
  ariaLabel: ariaLabel,
  id: id,
  options: options,
  label: label,
  noResultsText,
  altTextMap: {
    clearButton: clearButtonAriaLabel,
    selectedItem: selectedItemAriaLabel,
  },
  loading: false,
  loadingText,
};

describe('[Combobox]', () => {
  /**
   * Fix TypeError: window.ResizeObserver is not a constructor issue
   * mock out the basic API (observe, unobserve, disconnect) and
   * use jest.fn() to return particular mock entries in the test.
   * https://github.com/maslianok/react-resize-detector/issues/145
   * https://github.com/que-etc/resize-observer-polyfill/issues/50
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { ResizeObserver } = window;

  beforeEach(() => {
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();

    mockResizeObserver();
  });

  afterEach(() => {
    window.ResizeObserver = ResizeObserver;
    jest.restoreAllMocks();
  });

  const user = userEvent.setup();
  const getCombobox = () => screen.getByRole('combobox');
  const getListItems = () => screen.getAllByRole('option');
  const getLastListItem = () => screen.getByRole('option', { name: 'Third Option' });
  const getGroupHeaderItems = () => screen.getAllByRole('group');
  const queryListItems = () => screen.queryAllByRole('option');
  const getClearButton = () => screen.getByRole('button');
  const queryClearButton = () => screen.queryByRole('button');
  const getListItem2CheckIcon = () => {
    return screen.getByTestId('combobox-test-id-list-box-list-item-item2-checked-icon');
  };
  const getOverlayFooterText = () => screen.queryByText('There are more options. Continue typing to refine further.');
  const getOverlayFooterIcon = () => screen.queryByTestId('combobox-overlay-footer-id-icon');

  it('should render without label or placeholder', () => {
    render(
      <Combobox
        id={'combobox'}
        testId={testId}
        ariaLabel={ariaLabel}
        options={options}
        noResultsText={noResultsText}
      ></Combobox>
    );
    expect(getCombobox()).toBeInTheDocument();
  });

  it('should render all list items', async () => {
    render(<Combobox {...defaultProps}></Combobox>);

    await user.click(getCombobox());
    expect(getListItems()[0]).toBeInTheDocument();
    expect(getListItems().length).toBe(3);
  });

  it('should render list items when Enter is pressed', async () => {
    render(<Combobox {...defaultProps}></Combobox>);

    await user.tab(); // puts focus on input
    await user.keyboard('{enter}');
    expect(getListItems().length).toBe(3);
  });

  it('should not render list items when Ctrl is pressed', async () => {
    render(<Combobox {...defaultProps}></Combobox>);

    await user.tab();
    await user.keyboard('{ctrl}');
    expect(queryListItems().length).toBe(0);
  });

  it('should show label', () => {
    render(<Combobox {...defaultProps}></Combobox>);
    expect(getCombobox()).toBeInTheDocument();
    expect(screen.getByText(label)).toBeInTheDocument();
  });

  it('should show initial value with inputValue and clear button', () => {
    render(<Combobox {...defaultProps} value={firstOption} inputValue={firstOption.title}></Combobox>);
    expect(getCombobox()).toHaveValue(firstOption.title);
    expect(screen.getByText(label)).toBeInTheDocument();
    expect(getClearButton()).toBeInTheDocument();
  });

  it('should show initial value with inputValue and hide clear button', () => {
    render(<Combobox {...defaultProps} value={firstOption} inputValue={firstOption.title} hideClearButton></Combobox>);
    expect(getCombobox()).toHaveValue(firstOption.title);
    expect(screen.getByText(label)).toBeInTheDocument();
    expect(queryClearButton()).not.toBeInTheDocument();
  });

  it('should set disabled attribute on input field when disabled', () => {
    render(<Combobox {...defaultProps} disabled />);
    expect(getCombobox()).toHaveAttribute('disabled', '');
    expect(getCombobox()).not.toHaveAttribute('aria-disabled');
  });

  it('should set readOnly attribute on input field when readOnly', () => {
    render(<Combobox {...defaultProps} readOnly />);
    expect(getCombobox()).toHaveAttribute('readonly', '');
    expect(getCombobox()).toHaveAttribute('aria-readonly');
  });

  it('should have custom aria-label when there is no label', () => {
    render(
      <Combobox
        id={'combobox'}
        testId={testId}
        options={options}
        noResultsText={noResultsText}
        ariaLabel="custom aria label"
      />
    );

    expect(getCombobox()).toHaveAttribute('aria-label', 'custom aria label');
  });

  it('clear button should have custom aria-label', () => {
    render(<Combobox {...defaultProps} value={firstOption} inputValue={firstOption.title} />);
    expect(getClearButton()).toHaveAttribute('aria-label', clearButtonAriaLabel);
  });

  it('should show initial value List Item is selected', async () => {
    render(<Combobox {...defaultProps} value={firstOption} inputValue={firstOption.title}></Combobox>);

    await user.click(getCombobox());
    expect(getListItem2CheckIcon()).toBeInTheDocument();
  });

  it('should not show value when value is invalid', () => {
    render(<Combobox {...defaultProps} value={invalidValue}></Combobox>);
    expect(screen.queryByText(invalidValue.title)).not.toBeInTheDocument();
    expect(screen.getByText(label)).toBeInTheDocument();
    expect(queryClearButton()).not.toBeInTheDocument();
  });

  it('should show required on label', () => {
    render(<Combobox {...defaultProps} required={true}></Combobox>);
    expect(screen.getByText(label)).toBeInTheDocument();
    expect(screen.getByText(/\*/i)).toBeInTheDocument();
  });

  it('should not open overlay when disabled and have disabled class', async () => {
    render(<Combobox {...defaultProps} disabled={true}></Combobox>);

    await user.click(getCombobox());
    expect(queryListItems()[0]).toBeUndefined();
    expect(queryListItems().length).toBe(0);
    expect(getCombobox()).toHaveClass('disabled');
  });

  it('should not open overlay when readOnly and have readOnly class', async () => {
    render(<Combobox {...defaultProps} readOnly={true}></Combobox>);

    await user.click(getCombobox());
    expect(queryListItems()[0]).toBeUndefined();
    expect(queryListItems().length).toBe(0);
    expect(getCombobox()).toHaveClass('readOnly');
  });

  it('should show helper text', () => {
    render(<Combobox {...defaultProps} helperText={helperText}></Combobox>);
    expect(screen.getByText(helperText)).toBeInTheDocument();
    expect(screen.queryByText(helperTextPrefix)).not.toBeInTheDocument();
    expect(screen.queryByText(statusMessage)).not.toBeInTheDocument();
    expect(screen.queryByText(statusMessagePrefix)).not.toBeInTheDocument();
  });

  it('should show helper text and helper text prefix', () => {
    render(<Combobox {...defaultProps} helperText={helperText} helperTextPrefix={helperTextPrefix}></Combobox>);
    expect(screen.getByText(helperText)).toBeInTheDocument();
    expect(screen.getByText(helperTextPrefix)).toBeInTheDocument();
    expect(screen.queryByText(statusMessage)).not.toBeInTheDocument();
    expect(screen.queryByText(statusMessagePrefix)).not.toBeInTheDocument();
  });

  it('should show error message', () => {
    render(
      <Combobox
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></Combobox>
    );

    expect(screen.getByText(statusMessage)).toBeInTheDocument();
    expect(screen.getByText(statusMessagePrefix)).toBeInTheDocument();
    expect(screen.queryByText(helperText)).not.toBeInTheDocument();
    expect(screen.queryByText(helperTextPrefix)).not.toBeInTheDocument();
  });

  it('should show success message', () => {
    render(
      <Combobox
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></Combobox>
    );

    expect(screen.getByText(statusMessage)).toBeInTheDocument();
    expect(screen.getByText(statusMessagePrefix)).toBeInTheDocument();
    expect(screen.queryByText(helperText)).not.toBeInTheDocument();
    expect(screen.queryByText(helperTextPrefix)).not.toBeInTheDocument();
  });

  it('should render no result', async () => {
    render(<Combobox {...defaultProps} options={[]} noResultsText={noResultsText}></Combobox>);

    await user.click(getCombobox());
    expect(queryListItems().length).toBe(0);
    expect(screen.getByText(noResultsText)).toBeInTheDocument();
  });

  it('Escape keydown should stopPropagation when overlay is visible', async () => {
    const onKeyDownParent = jest.fn();
    render(
      <div onKeyDown={onKeyDownParent}>
        <Combobox
          id={'combobox'}
          testId={testId}
          ariaLabel={ariaLabel}
          options={options}
          noResultsText={noResultsText}
        ></Combobox>
      </div>
    );

    await user.click(getCombobox());
    expect(screen.getByText(options[0].title)).toBeInTheDocument();

    await user.keyboard('{Escape}');
    expect(screen.queryByText(options[0].title)).not.toBeInTheDocument();
    expect(onKeyDownParent).toHaveBeenCalledTimes(0);

    await user.keyboard('{Escape}');
    expect(onKeyDownParent).toHaveBeenCalledTimes(1);
  });

  it('should show loading stub when loading is true with an announcement', async () => {
    render(<Combobox {...defaultProps} loading={true} />);

    await user.click(getCombobox());

    const el = screen.getByTestId(`${testId}-list-box-stub`);
    expect(within(el).getByText(loadingText)).toBeInTheDocument();
    expect(screen.getByRole('alert').textContent).toBe(loadingText);
  });

  it('should not show loading stub when loading is false', async () => {
    render(<Combobox {...defaultProps} loading={false} />);

    await user.click(getCombobox());
    expect(screen.queryByText(loadingText)).not.toBeInTheDocument();
  });

  it('should show no results when options are empty with an announcement', async () => {
    render(<Combobox {...defaultProps} options={[]} />);

    await user.click(getCombobox());
    expect(screen.getByText(noResultsText)).toBeInTheDocument();
    expect(screen.getByRole('alert').textContent).toBe(noResultsText);
  });

  it('should not show no results when options are not empty', async () => {
    render(<Combobox {...defaultProps} />);

    await user.click(getCombobox());
    expect(screen.queryByText(noResultsText)).not.toBeInTheDocument();
  });

  it('should not show overlayFooter when overlayFooter prop is not passed', async () => {
    render(<Combobox {...defaultProps} />);

    await user.click(getCombobox());
    expect(getOverlayFooterText()).not.toBeInTheDocument();
    expect(getOverlayFooterIcon()).not.toBeInTheDocument();
  });

  it('should not render last option in overlay with aria-describedby when overlayFooter prop is not passed', async () => {
    render(<Combobox {...defaultProps} />);

    await user.click(getCombobox());
    expect(getLastListItem()).not.toHaveAttribute('aria-describedby', 'combobox-overlay-footer-id');
  });

  it('should show overlayFooter when overlayFooter prop is passed', async () => {
    render(
      <Combobox
        {...defaultProps}
        overlayFooter={
          <ComboboxOverlayFooter
            id={'combobox-overlay-footer-id'}
            testId={'combobox-overlay-footer-id'}
            label={'There are more options. Continue typing to refine further.'}
            ariaLabel={'There are more options. Continue typing to refine further.'}
          />
        }
      />
    );

    await user.click(getCombobox());
    expect(getOverlayFooterText()).toBeInTheDocument();
    expect(getOverlayFooterIcon()).not.toBeInTheDocument();
  });

  it('should render grouped list items when options contain nested items', async () => {
    const options = [
      {
        id: 'group-1',
        title: 'Group 1',
        items: [
          { title: 'Group 1 Item 1', id: 'item1' },
          { title: 'Group 1 Item 2', id: 'item2' },
        ],
      },
      {
        id: 'group-2',
        title: 'Group 2',
        items: [
          { title: 'Group 2 Item 1', id: 'item3' },
          { title: 'Group 2 Item 2', id: 'item4' },
        ],
      },
    ];

    render(
      <Combobox
        id="combobox-with-grouping"
        label="Departments"
        ariaLabel="This is an aria-label"
        noResultsText="No results"
        options={options}
      />
    );

    await user.click(getCombobox());

    expect(getGroupHeaderItems().length).toBe(2);
    expect(getGroupHeaderItems()[0]).toHaveTextContent(options[0].title);
  });

  it('should render last option in overlay with aria-describedby when overlayFooter is passed', async () => {
    render(
      <Combobox
        {...defaultProps}
        overlayFooter={
          <ComboboxOverlayFooter
            id={'combobox-overlay-footer-id'}
            testId={'combobox-overlay-footer-id'}
            label={'There are more options. Continue typing to refine further.'}
            ariaLabel={'There are more options. Continue typing to refine further.'}
          />
        }
      />
    );

    await user.click(getCombobox());
    expect(getLastListItem()).toHaveAttribute('aria-describedby', 'combobox-overlay-footer-id');
  });

  it('should show overlayFooter with overlayFooter icon when overlayFooter prop containing iconName is passed', async () => {
    render(
      <Combobox
        {...defaultProps}
        overlayFooter={
          <ComboboxOverlayFooter
            id={'combobox-overlay-footer-id'}
            testId={'combobox-overlay-footer-id'}
            label={'There are more options. Continue typing to refine further.'}
            ariaLabel={'There are more options. Continue typing to refine further.'}
            iconName={'information'}
          />
        }
      />
    );

    await user.click(getCombobox());
    expect(getOverlayFooterText()).toBeInTheDocument();
    expect(getOverlayFooterIcon()).toBeInTheDocument();
  });

  describe('onInputValueChange event', () => {
    const onInputValueChange = jest.fn();

    beforeEach(onInputValueChange.mockReset);

    it('dispatch onInputValueChange event when type "f" on input box', async () => {
      render(<Combobox {...defaultProps} onInputValueChange={onInputValueChange}></Combobox>);

      await user.type(getCombobox(), 'f');
      expect(onInputValueChange).toHaveBeenCalledTimes(1);
      expect(onInputValueChange).toHaveBeenLastCalledWith('f');
    });

    it('dispatch onInputValueChange event when type "First Option" on input box', async () => {
      render(<Combobox {...defaultProps} onInputValueChange={onInputValueChange}></Combobox>);

      await user.type(getCombobox(), 'First Option');
      expect(onInputValueChange).toHaveBeenCalledTimes(12);
      expect(onInputValueChange).toHaveBeenLastCalledWith('First Option');
    });
  });

  describe('onChange event', () => {
    const onChange = jest.fn();

    beforeEach(onChange.mockReset);

    it('dispatch onChange event when enter key is used to make selection', async () => {
      render(<Combobox {...defaultProps} onChange={onChange}></Combobox>);

      await user.click(getCombobox());
      await user.type(getCombobox(), '{arrowdown}');
      await user.type(getListItems()[1], '{enter}');

      expect(onChange).toHaveBeenCalledTimes(1);
      expect(onChange).toHaveBeenCalledWith(options[1]);
    });

    it('dispatch onChange event when use mouse click to make select', async () => {
      render(<Combobox {...defaultProps} onChange={onChange}></Combobox>);

      await user.click(getCombobox());
      await user.click(getListItems()[1]);

      expect(onChange).toHaveBeenCalledTimes(1);
      expect(onChange).toHaveBeenCalledWith(options[1]);
    });
  });

  describe('onClear event', () => {
    const onClear = jest.fn();

    beforeEach(onClear.mockReset);

    it('dispatch onClear event when use enter key to clear selection', async () => {
      render(
        <Combobox {...defaultProps} onClear={onClear} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.type(getClearButton(), '{enter}');
      expect(onClear).toHaveBeenCalledTimes(1);
      expect(onClear).toHaveBeenLastCalledWith();
    });

    it('dispatch onClear event when use space key to clear selection', async () => {
      render(
        <Combobox {...defaultProps} onClear={onClear} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.type(getClearButton(), '{space}');
      expect(onClear).toHaveBeenCalledTimes(1);
      expect(onClear).toHaveBeenLastCalledWith();
    });

    it('dispatch onClear event when click on clear button', async () => {
      render(
        <Combobox {...defaultProps} onClear={onClear} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.click(getClearButton());
      expect(onClear).toHaveBeenCalledTimes(1);
      expect(onClear).toHaveBeenLastCalledWith();
    });
  });

  describe('onFocus event', () => {
    const onFocus = jest.fn();

    beforeEach(onFocus.mockReset);

    it('dispatch onFocus when combobox has focus', async () => {
      render(<Combobox {...defaultProps} onFocus={onFocus}></Combobox>);

      await user.click(getCombobox());
      expect(onFocus).toHaveBeenCalledTimes(1);
    });

    it('dispatch only 1 onFocus when combobox has focus and have series of keyboard events', async () => {
      render(
        <Combobox {...defaultProps} onFocus={onFocus} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.click(getCombobox());
      await user.type(getCombobox(), '{arrowdown}{arrowdown}{arrowdown}{arrowup}');
      expect(onFocus).toHaveBeenCalledTimes(1);
    });

    it('dispatch only 1 onFocus when combobox has focus and tab to clear button and shift tab back to combobox', async () => {
      render(
        <Combobox {...defaultProps} onFocus={onFocus} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.tab();
      await user.tab(); // clear button
      await user.tab({ shift: true });
      expect(onFocus).toHaveBeenCalledTimes(1);
    });

    it('on side panel, onFocus should be called when combobox is focused, clicking back clear button should also call onFocus', async () => {
      render(
        <SidePanel testId="side-panel-combobox-test" id="side-panel-combobox" open={true} size="md">
          <Combobox {...defaultProps} onFocus={onFocus} value={firstOption} inputValue={firstOption.title}></Combobox>
        </SidePanel>
      );

      await user.tab();
      expect(onFocus).toHaveBeenCalledTimes(1);

      await user.keyboard('{enter}');
      await user.keyboard('{enter}'); // Select an item

      await user.click(screen.getByTestId('side-panel-combobox-test')); // Click outside
      await user.click(getClearButton()); // Clear button
      expect(onFocus).toHaveBeenCalledTimes(2);
    });

    it('Shift + Tab from other component, onFocus should have been called', async () => {
      render(
        <Combobox {...defaultProps} onFocus={onFocus} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.tab();
      expect(onFocus).toHaveBeenCalledTimes(1);
      await user.tab(); // clear button
      await user.tab(); // focus out
      await user.tab({ shift: true }); // focus back
      expect(onFocus).toHaveBeenCalledTimes(2);
    });
  });

  describe('onBlur event', () => {
    const onBlur = jest.fn();

    beforeEach(onBlur.mockReset);

    it.skip('should not dispatch onBlur when combobox has focus with series of tab action within the combobox', async () => {
      render(
        <Combobox {...defaultProps} onBlur={onBlur} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.tab(); // puts focus on input
      await user.tab(); // puts focus on Clear button
      await user.tab({ shift: true }); // back to input
      await user.tab(); // clear button
      await user.tab({ shift: true }); // back to input
      await user.keyboard('{arrowdown}{arrowdown}{arrowdown}{arrowup}'); // focus remains in input

      // Tabbing from the overlay should return focus to the clear button, but focus is instead landing on the document <body>, resulting onBlur being called
      await user.tab();
      await user.tab({ shift: true }); // input
      await user.keyboard('{arrowdown}{enter}'); // still in input

      expect(onBlur).toHaveBeenCalledTimes(0);
    });

    it('should dispatch onBlur when combobox has lost focus', async () => {
      render(<Combobox {...defaultProps} onBlur={onBlur}></Combobox>);

      await user.tab();
      await user.tab();
      expect(onBlur).toHaveBeenCalledTimes(1);
    });

    it('should dispatch onBlur when combobox has lost focus using mouseClick and tab', async () => {
      render(<Combobox {...defaultProps} onBlur={onBlur}></Combobox>);

      await user.click(getCombobox());
      await waitFor(async () => {
        await user.tab();
        expect(onBlur).toHaveBeenCalledTimes(1);
      });
    });

    it('should dispatch onBlur when combobox with value has lost focus using mouseClick and tab', async () => {
      render(
        <Combobox {...defaultProps} onBlur={onBlur} value={firstOption} inputValue={firstOption.title}></Combobox>
      );

      await user.click(getCombobox());
      await user.tab();
      await user.tab();
      expect(onBlur).toHaveBeenCalledTimes(1);
    });
  });
});
