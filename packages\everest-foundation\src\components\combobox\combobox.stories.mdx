import { useState, useEffect } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Combobox } from './combobox';
import { action } from '@storybook/addon-actions';
import Examples from './combobox.examples.mdx';

<Meta
  title="Components/Combobox"
  component={Combobox}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/Everest-Web?node-id=3157%3A9890&t=wUJMEMCKGHAySOZC-0',
    },
  }}
  argTypes={{
    value: {
      control: '-',
    },
    inputValue: {
      control: '-',
    },
    options: {
      type: 'array',
      control: 'object',
    },
    altTextMap: {
      type: 'object',
      control: 'object',
    },
    onChange: {
      control: '-',
    },
    onInputValueChange: {
      control: '-',
    },
    onClear: {
      control: '-',
    },
    onFocus: {
      control: '-',
    },
    onBlur: {
      control: '-',
    },
    itemRenderer: {
      control: '-',
    },
    overlayFooter: {
      control: '-',
    },
  }}
  args={{
    id: 'combo',
    testId: 'combobox-test-id',
    label: 'This is a label',
    ariaLabel: 'This is an aria label',
    status: 'default',
    disabled: false,
    readOnly: false,
    required: true,
    altTextMap: {
      clearButton: 'Clear Input',
      selectedItem: '{0} selected',
      unselectedItem: '{0} not selected',
    },
    onFocus: action('onFocus'),
    maxItems: 5,
    noResultsText: 'No matches found',
    helperText: 'This is helper text',
    helperTextPrefix: 'Hint:',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Status Message:',
    hideClearButton: false,
    loading: false,
    loadingText: 'Loading',
    options: [
      { id: 'id-0', title: 'Sales' },
      { id: 'id-1', title: 'Marketing' },
      { id: 'id-2', title: 'Support' },
      { id: 'id-3', title: 'Human Resources' },
      { id: 'id-4', title: 'Product & Technology' },
      { id: 'id-5', title: 'Services' },
      { id: 'id-6', title: 'Operations' },
      { id: 'id-7', title: 'Customer Experience' },
      { id: 'id-8', title: 'Finance' },
      { id: 'id-9', title: 'Legal' },
    ],
  }}
/>

# Combobox

<Examples />

## Live Demo

<Canvas>
  <Story name="Combobox">
    {({ options, ...args }) => {
      const [inputValue, setInputValue] = useState('');
      const [value, setValue] = useState();
      const [filteredOptions, setFilteredOptions] = useState(options);
      const handleInputValueChange = (value) => {
        action('onInputValueChange')(value);
        setInputValue(value);
        const filtered = options.filter((option) => option.title.toLowerCase().includes(value.toLowerCase()));
        setFilteredOptions(filtered);
      };
      const handleChange = (item) => {
        action('onChange')(item);
        setValue(item);
        item ? setInputValue(item.title) : setInputValue('');
        setFilteredOptions(options);
      };
      const handleBlur = (e) => {
        action('onBlur')(e);
        if (value) {
          setInputValue(value.title);
        } else {
          setInputValue('');
        }
        setFilteredOptions(options);
      };
      const handleClear = () => {
        action('onClear')();
        setInputValue('');
        setValue(undefined);
      };
      useEffect(() => {
        setInputValue('');
        setValue(undefined);
        setFilteredOptions(options);
      }, [options]);
      return (
        <Combobox
          {...args}
          inputValue={inputValue}
          options={filteredOptions}
          onInputValueChange={handleInputValueChange}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          onClear={handleClear}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Combobox" />
