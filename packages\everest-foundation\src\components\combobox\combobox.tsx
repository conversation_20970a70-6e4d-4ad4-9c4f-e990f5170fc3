import React, {
  FocusEvent,
  ReactElement,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { getRuntimeEnvironmentInfo } from '@platform/core';
import classnames from 'classnames';

import { mergeRefs, templateReplacer, useCreateTestId, getBrowser } from '../../utils';
import { getAriaAttributes } from '../../utils/get-aria-attributes';
import { announce, clearAnnouncer } from '../../utils/live-announcer';
import { FormFieldContainer, FormFieldContainerContext } from '../form-field-container';
import { IListBoxMethods, ListBox } from '../list-box';
import { IDataItem } from '../list-item';
import { IListItemContext, ListItemContext } from '../list-item/list-item-context';
import { getSelectableOptions } from '../list-item/list-item-helper';
import {
  SelectContainer,
  ISelectContainerTriggerAreaAltText,
  TSelectContainerTriggerAreaStatus,
} from '../select-container';
import { TriggerAreaStyledOverlay } from '../trigger-area-styled-overlay';

import styles from './combobox.module.scss';

export interface ICombobox {
  /**
   * Sets the `id` attribute on the combobox. A unique ID is required for accessibility purposes.
   */
  id: string;
  /**
   * An array of `IDataItem` objects that represent the items in the combobox.
   */
  options: IDataItem[];
  /**
   * Custom renderer for the combobox list items.
   */
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  /**
   * Text to display when there are no results.
   */
  noResultsText: string;
  /**
   * An ID used for automation testing. Sets the `data-testid` attribute on the combobox.
   */
  testId?: string;
  /**
   * The aria-label for the input. If not set, the value of `label` property is used.
   */
  ariaLabel?: string;
  /**
   * Sets the `name` attribute on the combobox.
   */
  name?: string;
  /**
   * Sets the `disabled` attribute on the combobox.
   * @default false
   */
  disabled?: boolean;
  /**
   * Sets the `readonly` attribute on the combobox.
   * @default false
   */
  readOnly?: boolean;
  /**
   * Sets the `required` attribute on the combobox.
   * @default false
   */
  required?: boolean;
  /**
   * Specifies the label rendered above the input.
   * @default ''
   */
  label?: string;
  /**
   * Sets the status of the combobox.
   * @default default
   */
  status?: TSelectContainerTriggerAreaStatus;
  /**
   * Sets the helper text rendered below the combobox.
   */
  helperText?: string;
  /**
   * Sets the prefix to the helper text rendered below the combobox.
   */
  helperTextPrefix?: string;
  /**
   * Sets the status message rendered under the combobox.
   */
  statusMessage?: string;
  /**
   * Sets the prefix to the status message rendered below the combobox.
   */
  statusMessagePrefix?: string;
  /**
   * The current value of the combobox.
   */
  value?: IDataItem;
  /**
   * The input value of the combobox.
   */
  inputValue?: string;
  /**
   * Max number of items displayed in overlay.
   * @default 5
   */
  maxItems?: number;
  /**
   * A callback that is fired when the selected item changes.
   */
  onChange?: (dataItem?: IDataItem, index?: number) => void;
  /**
   * A callback that is fired when the input value changes.
   */
  onInputValueChange?: (value: string) => void;
  /**
   * A callback that is fired when the clear button is clicked.
   */
  onClear?: () => void;
  /**
   * A callback that is fired when the combobox receives focus.
   */
  onFocus?: (e: React.FocusEvent) => void;
  /**
   * A callback that is fired when the combobox loses focus.
   */
  onBlur?: (e: React.FocusEvent) => void;
  /**
   * Toggles the visibility of the clear button.
   * @default false
   */
  hideClearButton?: boolean;
  /**
   * Toggles the loading presentation.
   */
  loading?: boolean;
  /**
   * Object containing localized text for various elements. `selectedItem` should be a template with placeholder,
   * e.g., `selectedItem` should have value `{0} selected`, so that the Everest component can replace the
   * placeholder `{0}` with the list item's title as appropriate.
   */
  altTextMap?: ISelectContainerTriggerAreaAltText;
  /**
   * Text to be presented when the component is loading.
   */
  loadingText?: string;
  /**
   * A component to be used as the footer of the overlay. The footer is only intended for use cases implementing data
   * loading where not all options are always displayed within the overlay. See the `Combobox with Data Loading`
   * example for more information. An `id` is required to meet accessibility requirements.
   */
  overlayFooter?: ReactElement;
}

export const Combobox = React.forwardRef<HTMLInputElement, ICombobox>((props, ref) => {
  const {
    testId,
    id,
    name: inputName,
    disabled = false,
    readOnly = false,
    required = false,
    label,
    status = 'default',
    helperText,
    helperTextPrefix,
    statusMessage,
    statusMessagePrefix,
    value: consumerProvidedValue,
    inputValue,
    options,
    itemRenderer,
    maxItems = 5,
    onChange: consumerProvidedOnChange,
    onInputValueChange: consumerProvidedOnInputValueChange,
    onClear: consumerProvidedOnClear,
    onFocus: consumerProvidedOnFocus,
    onBlur: consumerProvidedOnBlur,
    hideClearButton = false,
    altTextMap,
    noResultsText,
    loading,
    loadingText,
    overlayFooter,
  } = props;

  const runtimeInfo = getRuntimeEnvironmentInfo();
  const listboxId = `${id}-list-box`;
  const noResultsMessageId = `${id}-no-results-message`;
  const statusMessageId = `${id}-status-message`;

  const inputRef = useCreateTestId(testId);
  const inputRefOverlay = useCreateTestId(testId);
  const listBoxForwardRef = useRef<IListBoxMethods>(null);
  const overlayTriggerRef = useRef<HTMLElement>(null);
  const overlayWrapperRef = useRef<HTMLDivElement>(null);
  const clearButtonRef = useRef<HTMLDivElement>(null);
  const clearButtonRefOverlay = useRef<HTMLDivElement>(null);
  const listBoxRef = useRef<HTMLUListElement>(null);
  const totalOptionsCount = useRef<number>(0);

  // the option "highlighted" with keyboard but not yet activated
  const [softSelectedOption, setSoftSelectedOption] = useState<IDataItem | undefined>(undefined);

  // the actual item that was activated by the user by click or Enter
  const [selectedOption, setSelectedOption] = useState<IDataItem | undefined>(undefined);

  const [overlayVisible, setOverlayVisible] = useState(false);
  const [overlayContentHeight, setOverlayContentHeight] = useState(0);
  const [listBoxHeight, setListBoxHeight] = useState(0);
  const [selectableOptions, setSelectableOptions] = useState<IDataItem[]>([]);
  const [isFocusWithinComponent, setIsFocusWithinComponent] = useState(false);
  const [isListItemFocusVisible, setIsListItemFocusVisible] = useState(false);
  const showListBox = useMemo(() => options.length || loading, [options.length, loading]);
  const textMap = {
    spinnerAriaLabel: altTextMap?.spinnerAriaLabel,
  };

  const value = useMemo(
    () => selectableOptions.find((option) => option.id === consumerProvidedValue?.id),
    [selectableOptions, consumerProvidedValue]
  );

  const ariaAttributes = getAriaAttributes(Combobox.displayName || 'Combobox', {
    ...props,
    listboxId,
    noResultsMessageId,
    statusMessageId,
    overlayVisible,
    isError: props.status === 'error',
    activeDescendantId: !showListBox || loading ? undefined : softSelectedOption?.id,
  });

  const loadingStub = useMemo(
    () => (loading ? { title: loadingText, id: `${id}-combobox-stub`, type: 'loading' } : undefined),
    [loading, loadingText, id]
  );

  useEffect(() => {
    // get flattened 1-dimentional array of all items and nested groups
    const resp = getSelectableOptions(options);

    setSelectableOptions(resp.result);
    totalOptionsCount.current = resp.totalOptionsCount;
  }, [options]);

  const listItemContext: IListItemContext = {
    // combobox fully controls setting soft selection coming from mouse moves instead or relying on Listbox
    stopMouseOverSoftSelection: true,
    isFocusVisible: isListItemFocusVisible,
    itemRenderer,
    onMouseMove: (e: React.MouseEvent) => {
      // when itemRenderer used, the target id could be its child, locate the li element and set the softSelected onMouseMove
      const liElement = (e.target as HTMLElement).closest('li');
      if (liElement?.id !== softSelectedOption?.id) {
        setSoftSelectedOption(selectableOptions.find((item) => item.id == liElement?.id));
      }
    },
  };

  const formFieldContainerContext = useMemo(
    () => ({
      label,
      disabled,
      readOnly,
      required,
      status,
      id,
      helperText,
      helperTextPrefix,
      statusMessage,
      statusMessagePrefix,
    }),
    [label, disabled, readOnly, required, status, id, helperText, helperTextPrefix, statusMessage, statusMessagePrefix]
  );

  const onInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    setIsFocusWithinComponent(true);
    keyboardHandler(e);
  };

  const optionToSetAsSoftSelected = useCallback(() => {
    if (!selectableOptions.length) return undefined;

    return selectedOption && selectableOptions.findIndex((item) => item.id === selectedOption.id) > -1
      ? selectedOption
      : selectableOptions[0];
  }, [selectableOptions, selectedOption]);

  const announceListItemTitleSafari = (announceTitle: string, softSelectedOptionId: string) => {
    if (getBrowser() === 'Safari') {
      const selectionTemplate =
        softSelectedOptionId === selectedOption?.id ? altTextMap?.selectedItem || '' : altTextMap?.unselectedItem || '';
      clearAnnouncer('assertive');
      announce(templateReplacer(selectionTemplate, [announceTitle]), 'assertive');
    }
  };

  const keyboardHandler = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (readOnly) return;
    let closeOverlay = false;
    setIsListItemFocusVisible(true);

    switch (e.key) {
      case 'ArrowDown': {
        // prevent moving text input cursor. We want to use up/down to manage soft selection. To move input cursor user can use left and right arrow
        e.preventDefault();
        if (loading) return;
        if (!overlayVisible) {
          setSoftSelectedOption(optionToSetAsSoftSelected());
          setOverlayVisible(true);
          break;
        }
        const nextInd = selectableOptions.findIndex((el) => el.id === softSelectedOption?.id) + 1;
        if (nextInd < selectableOptions.length) {
          announceListItemTitleSafari(selectableOptions[nextInd].title, selectableOptions[nextInd].id);
          setSoftSelectedOption(selectableOptions[nextInd]);
        }
        setOverlayVisible(true);
        break;
      }

      case 'ArrowUp': {
        e.preventDefault(); // prevent input cursor from moving
        if (e.altKey) {
          closeOverlay = true;
          break;
        }

        if (loading) return;
        if (!overlayVisible) {
          setSoftSelectedOption(optionToSetAsSoftSelected());
          setOverlayVisible(true);
          break;
        }
        const prevInd = selectableOptions.findIndex((el) => el.id === softSelectedOption?.id) - 1;

        if (prevInd >= 0) {
          announceListItemTitleSafari(selectableOptions[prevInd].title, selectableOptions[prevInd].id);
          setSoftSelectedOption(selectableOptions[prevInd]);
        }
        setOverlayVisible(true);
        break;
      }

      case 'Home':
        e.preventDefault(); // prevent input cursor from moving
        setOverlayVisible(true);
        if (loading) return;
        setSoftSelectedOption(selectableOptions[0]);
        break;

      case 'End':
        e.preventDefault(); // prevent input cursor from moving
        setOverlayVisible(true);
        if (loading) return;
        setSoftSelectedOption(selectableOptions[selectableOptions.length - 1]);
        break;

      case 'Tab':
        if (overlayVisible && !inputValue) {
          // prevent focusing next element when dropdown is visible and just hide the overlay first.
          // This is what material UI does and it also helps us
          e.preventDefault();
        }
        closeOverlay = true;
        break;

      case 'Escape':
        closeOverlay = true;
        if (overlayVisible) e.stopPropagation();
        break;
      case 'Enter': {
        if (loading) {
          closeOverlay = true;
          break;
        }
        if (overlayVisible) {
          if (softSelectedOption && selectableOptions.includes(softSelectedOption)) {
            itemSelectionHandler(softSelectedOption);
          }
          closeOverlay = true;
        } else {
          setSoftSelectedOption(optionToSetAsSoftSelected());
          setOverlayVisible(true);
        }
        break;
      }

      case ' ':
        if (!overlayVisible && !inputValue) {
          // with first Spacebar hit on an empty input and hidden overlay, don't actually set input value to ' ' and just pop the overlay first
          e.preventDefault();
          if (loading) return;
          setSoftSelectedOption(selectableOptions[0]);
          setOverlayVisible(true);
        }
        break;

      default:
        break;
    }

    if (closeOverlay) {
      setSoftSelectedOption(undefined);
      setOverlayVisible(false);
    }
  };

  useEffect(() => {
    setSelectedOption(value);
  }, [value]);

  // find the last option and set the options.ariaDescribedBy to overlayFooter id
  useEffect(() => {
    if (overlayFooter && selectableOptions.length > 0) {
      const lastOption = selectableOptions[selectableOptions.length - 1];

      lastOption.ariaDescribedBy = overlayFooter?.props?.id;
    }
  }, [selectableOptions, overlayFooter]);

  useEffect(() => {
    setSoftSelectedOption(optionToSetAsSoftSelected());
    setSelectedOption(consumerProvidedValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inputValue, selectableOptions, consumerProvidedValue, selectedOption]);

  // reset the soft selected option when overlay is closed
  useEffect(() => {
    if (!overlayVisible) setSoftSelectedOption(undefined);
  }, [overlayVisible]);

  //set the cursor in the input text to the click position
  useEffect(() => {
    if (overlayVisible && inputRefOverlay.current) {
      inputRefOverlay.current.selectionStart = inputRef.current?.selectionStart;
      inputRefOverlay.current.selectionEnd = inputRef.current?.selectionEnd;
    }
  }, [inputRef, inputRefOverlay, overlayVisible]);

  // called when Listbox fires a list item selection event or when Entered is hit on a soft selected item
  const itemSelectionHandler = (item: IDataItem) => {
    if (loading) return;
    const selectionTemplate = altTextMap?.selectedItem || '';
    const listItemTitle = item.title || '';
    clearAnnouncer('assertive');
    announce(templateReplacer(selectionTemplate, [listItemTitle]), 'assertive');

    if (inputValue !== item?.title) {
      consumerProvidedOnInputValueChange?.(item?.title ?? '');
    }

    if (!selectedOption || item.id !== selectedOption?.id) {
      consumerProvidedOnChange?.(item);
    }
  };

  // called on clear button click. Resets selection
  const inputClearHandler = () => {
    // fire events
    consumerProvidedOnClear?.();
  };

  // called on mousedown on any visible part of the components
  const mouseDownHandler = (e: React.MouseEvent) => {
    /* 
      When the user clicks on the background input (e.target === inputRef.current):  
       1. We want to open the overlay  
       2. We want to copy exactly where they clicked (`inputRef.current?.selectionStart` and `inputRef.current?.selectionEnd`) and copy that cursor placement to the overlay input  

      The `selectionStart` and `selectionEnd` values are wrong during mouseDown, only reliable in onClick.  

      So we want to skip the mouse down logic for the background input and let the input's onClick handle it.  
    */
    if (e.target === inputRef.current) return;
    clickInputHandler(e);
  };

  // called on click on the input
  const clickInputHandler = (e: React.MouseEvent) => {
    if (!disabled && !readOnly) {
      setIsFocusWithinComponent(true);
      e.preventDefault(); // prevent default input focus. We manage it when overlay visibility changes

      setOverlayVisible(() => {
        if (!overlayVisible) {
          // we're about to open overlay so set soft selected option
          announceListItemTitleSafari(softSelectedOption?.title || '', softSelectedOption?.id || '');
          setSoftSelectedOption(optionToSetAsSoftSelected());
        }
        return !overlayVisible;
      });
    }
  };

  // manages focus in inputs and soft selection when overlay visibility changes
  useLayoutEffect(() => {
    if (overlayVisible) {
      // this will set soft selection in case the keydown button didn't set it (for example if overlay was triggered by Spacebar or Enter)

      const listItem = (listBoxRef?.current as HTMLElement)?.querySelector('[role="option"]');
      const listItemHeight = Math.ceil(listItem?.getBoundingClientRect().height ?? 0); // ceil is used here to get correct height when zoomed in
      const overlayFooterHeight = overlayFooter ? listItemHeight : 0;
      // Use maxItems provided by consumer unless the options contain even fewer items.
      // If this evaluates to 0 there will no height restriction
      const maxListBoxHeight = loadingStub
        ? listItemHeight
        : Math.min(maxItems, totalOptionsCount.current) * listItemHeight;

      setListBoxHeight(maxListBoxHeight);
      setOverlayContentHeight(
        inputRefOverlay.current?.clientHeight && maxListBoxHeight
          ? inputRefOverlay.current?.clientHeight + maxListBoxHeight - 1 + overlayFooterHeight //-1 is to remove the gap between last item and the container
          : 0
      );

      setTimeout(() => {
        (inputRefOverlay?.current as HTMLElement)?.focus();
        listBoxForwardRef.current?.scrollListItemIntoView(softSelectedOption);
      }, 0);
    } else if (isFocusWithinComponent) {
      setSoftSelectedOption(undefined);
      (inputRef?.current as HTMLElement).focus();
    }
  }, [
    selectableOptions,
    softSelectedOption,
    setSoftSelectedOption,
    inputRefOverlay,
    inputRef,
    overlayVisible,
    maxItems,
    isFocusWithinComponent,
    loadingStub,
    overlayFooter,
  ]);

  // this handler receives all the events happening inside combobox
  const focusEventHandler = (e: React.FocusEvent<HTMLElement>) => {
    if (!isInternalFocusChange(e)) {
      if (e.type === 'focus') {
        // Shift + tab to clear button
        if (clearButtonRef.current === e.target) (inputRef?.current as HTMLElement).focus(); // Focus on the input
        consumerProvidedOnFocus?.(e);
      } else if (e.type === 'blur') {
        setIsFocusWithinComponent(false);
        setIsListItemFocusVisible(false);
        consumerProvidedOnBlur?.(e);
      }
    } else {
      setIsFocusWithinComponent(true);
    }
  };

  function handleOnChange(e: React.ChangeEvent<HTMLInputElement>) {
    setOverlayVisible(true);
    consumerProvidedOnInputValueChange?.(e.target.value);
  }

  // analyzes the focus event and deteremines if focus was brought in from another element
  // or if it "goes to" another element. It will check if the relevant element exists inside the combobox
  const isInternalFocusChange = (e: FocusEvent<HTMLElement>): boolean => {
    if (e.type === 'focus') {
      const isTargetInside =
        inputRef.current === e.target ||
        clearButtonRef.current === e.target ||
        overlayWrapperRef.current?.contains(e.target) ||
        false;

      const isRelatedTargetOutside =
        e.relatedTarget !== null
          ? inputRef.current !== e.relatedTarget &&
            clearButtonRef.current !== e.relatedTarget &&
            !overlayWrapperRef.current?.contains(e.relatedTarget as Node)
          : true;

      return !(isTargetInside && isRelatedTargetOutside);
    }

    return (
      (e.relatedTarget &&
        (overlayTriggerRef.current?.contains(e.relatedTarget as Node) ||
          overlayWrapperRef.current?.contains(e.relatedTarget as Node))) ||
      false
    );
  };

  const renderInput = (isOverlay: boolean) => {
    return (
      <input
        className={classnames('evrBodyText1', styles.evrCombobox, {
          [styles.disabled]: disabled,
          [styles.readOnly]: readOnly,
          [styles.hasValue]: !!inputValue,
          [styles.clearButton]: !!inputValue && !hideClearButton,
          [styles.error]: status === 'error',
        })}
        ref={isOverlay ? mergeRefs([ref, inputRefOverlay]) : mergeRefs([ref, inputRef])}
        id={isOverlay ? `${id}-input-overlay` : `${id}-input`}
        tabIndex={!isOverlay && overlayVisible ? -1 : undefined}
        aria-hidden={!isOverlay && overlayVisible ? true : undefined}
        disabled={disabled}
        readOnly={readOnly}
        {...ariaAttributes.input}
        aria-activedescendant={
          isOverlay && ariaAttributes.input ? ariaAttributes.input?.['aria-activedescendant'] : undefined
        }
        value={inputValue}
        onChange={handleOnChange}
        onKeyDown={onInputKeyDown}
        onFocus={(e) => {
          if (isFocusWithinComponent && !isOverlay) e.stopPropagation();
        }}
        name={inputName}
        autoComplete={'off'}
        autoCorrect={'off'}
        spellCheck={false}
        onBlur={(e) => {
          if (!isInternalFocusChange(e)) {
            setOverlayVisible(false); // collapses the overlay if input loses focus
          }
        }}
        onClick={(e) => {
          e.stopPropagation();
          if (!overlayVisible) clickInputHandler(e); // call the handler on input click to ensure that the overlay opens
        }}
      ></input>
    );
  };

  const renderListResults = () => {
    if (showListBox) {
      const scopedOptions = loading ? ([loadingStub] as IDataItem[]) : options;
      return (
        <ListItemContext.Provider value={listItemContext}>
          <ListBox
            height={listBoxHeight ? `${listBoxHeight}px` : ''}
            ref={listBoxForwardRef}
            options={scopedOptions}
            selectableOptions={loading ? ([loadingStub] as IDataItem[]) : selectableOptions}
            listBoxRef={listBoxRef}
            selectedOptions={selectedOption ? [selectedOption] : undefined}
            softSelectedId={softSelectedOption?.id}
            id={listboxId}
            focusOnSoftSelected={false}
            onSelection={itemSelectionHandler}
            onListItemBlur={(e) => {
              // technically list items are not supposed to ever be focused but voiceover on iOS actually fires focus events when
              // list items get the accessibility focus. We need to prevent those events propagating, because they break stuff
              e.stopPropagation();
              if (!isInternalFocusChange(e as FocusEvent<HTMLElement>)) setOverlayVisible(false); // this ensures the combobox collapses on ios when focus leaves list item when using vioceover
            }}
            onListItemFocus={(e) => {
              e.stopPropagation();
            }}
            testId={testId ? `${testId}-list-box` : undefined}
            textMap={textMap}
            footer={overlayFooter}
          />
          <div
            className={styles.evrComboboxVisuallyHidden}
            role="alert"
            tabIndex={runtimeInfo.os === 'android' || runtimeInfo.os === 'ios' ? undefined : -1}
          >
            {loading && loadingText}
          </div>
        </ListItemContext.Provider>
      );
    }
    return (
      <div className={styles.evrComboboxNoResult} id={noResultsMessageId} role="alert">
        <p className="evrBodyText2">{noResultsText}</p>
      </div>
    );
  };

  const getOverlayHeight = useCallback(() => {
    if (!overlayContentHeight) return '';
    const borderSize = status === 'error' ? 'var(--evr-border-width-thick-px)' : 'var(--evr-border-width-thin-px)';
    return `calc(${overlayContentHeight}px + (${borderSize} * 3))`; // total 3 => 2 for the borderContainer borders and 1 for the FormFieldContainer bottomBorder
  }, [overlayContentHeight, status]);

  return (
    <FormFieldContainerContext.Provider value={formFieldContainerContext}>
      <div onBlur={focusEventHandler} onFocus={focusEventHandler}>
        <TriggerAreaStyledOverlay
          id={`${id}-trigger-area-overlay`}
          triggerRef={overlayTriggerRef}
          overlayVisible={overlayVisible}
          overlayHeight={getOverlayHeight()}
          error={status === 'error'}
          overrideFocusRingDefaultBehavior={!isFocusWithinComponent || isListItemFocusVisible}
          renderTriggerAreaContent={() => (
            <FormFieldContainer
              htmlFor={`${id}-input`}
              borderContainerRef={overlayTriggerRef}
              renderContent={() => (
                <SelectContainer
                  id={`${id}-select-container`}
                  testId={testId ? `${testId}-select-container` : undefined}
                  chevronIconOpen={false}
                  clearButtonFocusable={overlayVisible ? false : true}
                  disabled={disabled}
                  readOnly={readOnly}
                  renderContent={() => renderInput(false)}
                  clearButtonRef={clearButtonRef}
                  showClearButton={!!inputValue && !hideClearButton}
                  onClear={inputClearHandler}
                  clearButtonAriaLabel={altTextMap?.clearButton}
                />
              )}
              onMouseDown={mouseDownHandler}
              statusMessageId={statusMessageId}
              onClickLabel={(e: React.MouseEvent) => {
                e.preventDefault();
                overlayVisible ? inputRefOverlay?.current?.focus() : inputRef?.current?.focus();
              }}
              testId={testId && `${testId}-ff-container`}
            />
          )}
          renderOverlayContent={() => (
            <div
              className={classnames(styles.evrOverlayContainer, {
                [styles.error]: status === 'error',
              })}
              ref={overlayWrapperRef}
              onClick={mouseDownHandler}
            >
              <FormFieldContainer
                hideLabel={true}
                hideStatusMessage={true}
                hideFocusRing={true}
                bottomBorder={true}
                renderContent={() => (
                  <SelectContainer
                    id={`${id}-select-container-overlay`}
                    testId={testId ? `${testId}-select-container-overlay` : undefined}
                    chevronIconOpen={true}
                    clearButtonFocusable={true}
                    renderContent={() => renderInput(true)}
                    clearButtonRef={clearButtonRefOverlay}
                    showClearButton={!!inputValue && !hideClearButton}
                    onClear={inputClearHandler}
                    clearButtonAriaLabel={altTextMap?.clearButton}
                  />
                )}
              />
              {renderListResults()}
            </div>
          )}
        ></TriggerAreaStyledOverlay>
      </div>
    </FormFieldContainerContext.Provider>
  );
});

Combobox.displayName = 'Combobox';
