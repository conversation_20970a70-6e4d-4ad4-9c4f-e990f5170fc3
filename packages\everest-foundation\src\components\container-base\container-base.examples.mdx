import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { ContainerBase, ContainerHeaderBase, ContainerBodyBase, ContainerFooterBase } from '.';
import { Button } from '../button';
import { ContributedBanner } from '../../../.storybook/docs/shared/component-status-banner';

<ContributedBanner dev="Yogiraj <PERSON>" team="UI Modernization" />

export const scope = {
  ContainerBase,
  ContainerHeaderBase,
  ContainerBodyBase,
  ContainerFooterBase,
  Button,
};

## Basic Usage

A basic container consists of optional header, body, and footer section:

export const defaultCode = `() => {
  const css = \`
        .demo-container-class {
            padding: var(--evr-spacing-md);
            gap: var(--evr-spacing-sm);
        }\`;
  return (
    <>
      {/* Using \`style\` tag in order to encapsulate the CSS within this Storybook example.
          Should not actually be used when developing a component */}
      <style>{css}</style>
      <ContainerBase
        id="basic-container-id"
        status="info"
        ariaLabel="Basic container example"
        className="demo-container-class"
      >
        <ContainerHeaderBase id="basic-header-id">
          <h4 className="evrHeading4">Basic ContainerBase Example</h4>
        </ContainerHeaderBase>
        <ContainerBodyBase id="basic-body-id">
          <p className="evrBodyText1">This container shows basic usage with all sections.</p>
        </ContainerBodyBase>
        <ContainerFooterBase id="basic-footer-id">
          <Button 
            id="basic-action-id"
            label="Learn More"
            onClick={() => {}}
          />
        </ContainerFooterBase>
      </ContainerBase>
    </>
  )
}`;

<CodeExample scope={scope} code={defaultCode} />
