# ContainerBase

- Start Date: 2025-03-12
- Figma link: https://www.figma.com/file/oFWh4EveGvP6DH72YNEDQZ/Employee-Experience-2025?node-id=13207-151909&m=dev
- Epic: https://dayforce.atlassian.net/browse/PWEB-16707

## Summary

ContainerBase is a versatile container designed to standardize page content. It wraps its provided children in a basic container with customizable styles. ContainerBase will be used in a variety of components, including Card and NotificationBanner.

## Detailed Design

ContainerBase consists of an optional header, body, and footer section, each of which accept custom children.

- **ContainerHeaderBase**: Can include an accent icon, avatar, status indicators, sub titles, etc.
- **ContainerBodyBase**: The main content area of the card. This body section is flexible and can contain custom content.
- **ContainerFooterBase**: Pinned to the bottom of ContainerBase. It can display CTA buttons.

## API

### IContainerBaseProps

1. **id**: `string`
   Sets `id` attribute.
2. **testId**: `string`
   Optional. Sets `data-testid` attribute.
3. **ariaLabel**: `string`
   Optional. Sets `aria-label` attribute.
4. **roleType**: `string`
   Optional. Used to set the proper semantic tag or role for accessibility purposes.
5. **className**: `string`
   Optional. Sets custom class name to allow for additional styling.

### IContainerHeaderBaseProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId**: `string`
    Optional. Sets `data-testid` attribute.
3.  **roleType**: `string`
    Optional. Used to set the proper semantic tag or role for accessibility purposes.
4.  **minHeight**: `string`
    Optional. Sets min height.
5.  **className**: `string`
    Optional. Sets custom class name to allow for additional styling.

### IContainerBodyBaseProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId**: `string`
    Optional. Sets `data-testid` attribute.
3.  **roleType**: `string`
    Optional. Used to set the proper semantic tag or role for accessibility purposes.
4.  **className**: `string`
    Optional. Sets custom class name to allow for additional styling.

### IContainerFooterBaseProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId**: `string`
    Optional. Sets `data-testid` attribute.
3.  **roleType**: `string`
    Optional. Used to set the proper semantic tag or role for accessibility purposes.
4.  **className**: `string`
    Optional. Sets custom class name to allow for additional styling.

### Implementation

ContainerBase

```jsx
export interface IContainerBaseProps {
  id: string;
  testId?: string;
  className?: string;
  roleType?: string;
  ariaLabel?: string;
}
export const ContainerBase = forwardRef<HTMLDivElement, PropsWithChildren<IContainerBaseProps>>((props, ref) => {
  const { id, testId, className, roleType, ariaLabel, children } = props;
  const newProps = {
    ref,
    id,
    className: classnames(styles.evrContainerBase, className),
    'aria-label': ariaLabel,
    'data-testid': testId,
  };
  if (roleType === 'article') {
    return <article {...newProps}>{children}</article>;
  }
  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
});
```

ContainerHeaderBase

```jsx
export interface IContainerHeaderBaseProps {
  id: string;
  testId?: string;
  className?: string;
  minHeight?: string;
  roleType?: string;
}
export const ContainerHeaderBase = (props: React.PropsWithChildren<IContainerHeaderBaseProps>): JSX.Element => {
  const { id, testId, className, minHeight, roleType, children } = props;
  const newProps = {
    id,
    className,
    'data-testid': testId,
    style: { minHeight },
  };
  if (roleType === 'section') {
    return <section {...newProps}>{children}</section>;
  }
  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
};
```

ContainerBodyBase

```jsx
export interface IContainerBodyBaseProps {
  id: string;
  testId?: string;
  className?: string;
  roleType?: string;
}
export const ContainerBodyBase = (props: React.PropsWithChildren<IContainerBodyBaseProps>): JSX.Element => {
  const { id, testId, className, roleType, children } = props;
  const newProps = {
    id,
    className,
    'data-testid': testId,
  };
  if (roleType === 'section') {
    return <section {...newProps}>{children}</section>;
  }
  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
};
```

ContainerFooterBase

```jsx
export interface IContainerFooterBaseProps {
  id: string;
  testId?: string;
  className?: string;
  roleType?: string;
}
export const ContainerFooterBase = (props: React.PropsWithChildren<IContainerFooterBaseProps>): JSX.Element => {
  const { id, testId, className, roleType, children } = props;
  const newProps = {
    id,
    className,
    'data-testid': testId,
  };
  if (roleType === 'section') {
    return <section {...newProps}>{children}</section>;
  }
  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
};
```

### Usage

Basic Usage in Card

```jsx
export interface ICardHeaderProps extends Omit<IContainerHeaderBaseProps, 'className' | 'roleType'> {
  title?: string;
  description?: string;
  action?: React.ReactNode;
}
return (
  <ContainerBase
    id={id}
    testId={testId}
    className={`${styles.evrCard} ${onClick ? styles.clickable : ''} ${styles[variant]}`}
    roleType="article"
  >
    {...children}
  </ContainerBase>
);
```

Basic Usage in NotificationBanner

```jsx
return (
  <ContainerBase
    id={id}
    ref={ref}
    testId={testId}
    className={classnames(styles.evrNotificationBanner, styles[status])}
    ariaLabel={textMap?.ariaLabel}
    roleType="region"
  >
    {...children}
  </ContainerBase>
);
```

## Accessibility

- `roleType` will set [ARIA role](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Reference/Roles) on ContainerBase.

- `ariaLabel` should be set for screen reader purposes.

## Future Considerations

- Currently only ContainerHeaderBase has a `minHeight` prop. This prop is used by CardHeader to set its height. If other Card templates need to change minHeight, then the prop can easily be added in later.

- More templates in addition to ContainerHeaderBase, ContainerBodyBase, ContainerFooterBase

## Q&A

Q: What is the purpose of the `roleType` prop?
A: ContainerBase at its core is a div with a `role` attribute. Some roles can be set on the div instead of requiring a specific html element. However, other roles such as `<section>` require the html element tag. Therefore, ContainerBase will check what the role is before assigning it to either a div or `<section>` tag. More html tags may be added later if required.

Q: Why isn't `roleType` simply called `role`?
A: The prop name `role` presents linting and SonarQube issues because it is a standard HTML attribute used for accessibility purposes. To prevent this, the prop is named `roleType`. For example, `<ContainerBase role="article">` would generate a SQ warning `Use <article> instead of the "article" role to ensure accessibility across all devices.sonarqube(typescript:S6819)`.

Q: Why are there 2 style props (`minHeight` vs. `className`) on ContainerBase?
A: `minHeight` is an existing prop on Card and we do not want to alter or remove it. Since Card will wrap ContainerBase, it must be added to ContainerBase as well. Since there is no way to extrapolate the minHeight style from `className`.

## Required PBIs

1. [Architecture Doc](https://dayforce.atlassian.net/browse/PWEB-19068)
1. [Create React Component](https://dayforce.atlassian.net/browse/PWEB-19343)
