import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';
import { ContainerBase, ContainerHeaderBase, ContainerBodyBase, ContainerFooterBase } from '.';
import { Button } from '../button';

<Meta
  title="Testing/Automation Test Cases/ContainerBase"
  component={ContainerBase}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: {
        breakpointXs: { disable: true }, // disable default to use smaller size
        mobileMinimum: defaultModes['mobileMinimum'],
      },
    },
  }}
  args={{
    id: 'container-base-id',
    testId: 'container-base-test-id',
  }}
/>

# ContainerBase

## Live Demo

<Canvas>
  <Story name="Default">
    {({ ...args }) => {
      const css = `
        .container {
            padding: var(--evr-spacing-md);
            gap: var(--evr-spacing-sm);
        }`;
      return (
        <>
          <style>{css}</style>
          <ContainerBase {...args} className="container">
            <ContainerHeaderBase>
              <h4 className="evrHeading4">Heading</h4>
            </ContainerHeaderBase>
            <ContainerBodyBase>
              <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
            </ContainerBodyBase>
            <ContainerFooterBase>
              <Button id="container-action-btn" label="Take Action" />
            </ContainerFooterBase>
          </ContainerBase>
        </>
      );
    }}
  </Story>
</Canvas>
