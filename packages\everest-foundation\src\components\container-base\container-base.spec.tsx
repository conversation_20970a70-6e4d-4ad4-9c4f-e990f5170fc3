import React from 'react';
import { render, screen } from '@testing-library/react';

import { ContainerBodyBase } from './container-body-base';
import { ContainerFooterBase } from './container-footer-base';
import { ContainerHeaderBase } from './container-header-base';

import { ContainerBase } from '.';

const ariaLabel = 'Error message';
const defaultProps = {
  id: 'test-container',
  testId: 'container-test-id',
};

const getContainerBase = () => screen.getByTestId(defaultProps.testId);

describe('[ContainerBase]', () => {
  it('renders with minimal props', () => {
    render(<ContainerBase {...defaultProps} />);
    expect(getContainerBase()).toBeInTheDocument();
  });

  it('renders with children', () => {
    render(
      <ContainerBase {...defaultProps}>
        <div>Test Content</div>
      </ContainerBase>
    );
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should have custom aria-label when provided', () => {
    render(<ContainerBase {...defaultProps} ariaLabel={ariaLabel} />);
    expect(getContainerBase()).toHaveAttribute('aria-label', ariaLabel);
  });

  it('applies custom className when provided', () => {
    render(
      <ContainerBase {...defaultProps} className="custom-class">
        <div>Content</div>
      </ContainerBase>
    );
    expect(getContainerBase()).toHaveClass('custom-class');
  });

  it('renders header, body, and footer sections when provided', () => {
    render(
      <ContainerBase {...defaultProps}>
        <ContainerHeaderBase id="header-id" testId="header-test-id">
          Header
        </ContainerHeaderBase>
        <ContainerBodyBase id="body-id" testId="body-test-id">
          Body
        </ContainerBodyBase>
        <ContainerFooterBase id="footer-id" testId="footer-test-id">
          Footer
        </ContainerFooterBase>
      </ContainerBase>
    );
    expect(screen.getByTestId('header-test-id')).toBeInTheDocument();
    expect(screen.getByTestId('body-test-id')).toBeInTheDocument();
    expect(screen.getByTestId('footer-test-id')).toBeInTheDocument();
  });
});
