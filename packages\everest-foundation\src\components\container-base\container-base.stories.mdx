import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { ContainerBase, ContainerHeaderBase, ContainerBodyBase, ContainerFooterBase } from '.';
import Examples from './container-base.examples.mdx';
import { Button } from '../button';
import { action } from '@storybook/addon-actions';

<Meta
  title="Toolbox/ContainerBase"
  component={ContainerBase}
  parameters={{
    status: {
      contributed: true,
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/gMr3JaW6FsOZGDb1Up9taD/%F0%9F%A7%AA-Cards-and-widgets?node-id=1-4&p=f&m=devices',
    },
  }}
  args={{
    id: 'container-base-id',
    testId: 'container-base-test-id',
  }}
/>

# ContainerBase

<Examples />

## Live Demo

<Canvas>
  <Story name="ContainerBase">
    {(args) => {
      return (
        <>
          <style>
            {`
              .demo-container-class {
                padding: var(--evr-spacing-md);
                gap: var(--evr-spacing-sm);
              }
            `}
          </style>
          <ContainerBase {...args} className="demo-container-class">
            <ContainerHeaderBase id="container-header-base-id">
              <h4 className="evrHeading4">Example Container</h4>
            </ContainerHeaderBase>
            <ContainerBodyBase id="container-body-base-id">
              <p className="evrBodyText1">Body Text</p>
            </ContainerBodyBase>
            <ContainerFooterBase id="container-footer-base-id">
              <Button
                id="container-action-btn"
                label="Take Action"
              />
            </ContainerFooterBase>
          </ContainerBase>
        </>
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="ContainerBase" />
