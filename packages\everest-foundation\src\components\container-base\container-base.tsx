import React, { PropsWithChildren, forwardRef } from 'react';
import classnames from 'classnames';

import styles from './container-base.module.scss';

export interface IContainerBaseProps {
  /** Sets `id` attribute. */
  id: string;
  /** Sets `data-testid` for testing purposes. */
  testId?: string;
  /** Sets custom class name(s) to allow for additional styling. */
  className?: string;
  /** Accessibility role, used to set the proper semantic tag or role. */
  roleType?: string;
  /** Sets `aria-label` attribute. */
  ariaLabel?: string;
}

export const ContainerBase = forwardRef<HTMLDivElement, PropsWithChildren<IContainerBaseProps>>((props, ref) => {
  const { id, testId, className, roleType, ariaLabel, children } = props;

  const newProps = {
    ref,
    id,
    className: classnames(styles.evrContainerBase, className),
    'aria-label': ariaLabel,
    'data-testid': testId,
  };

  if (roleType === 'article') {
    return <article {...newProps}>{children}</article>;
  }

  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
});

ContainerBase.displayName = 'ContainerBase';
