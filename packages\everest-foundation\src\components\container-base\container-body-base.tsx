import React from 'react';

export interface IContainerBodyBaseProps {
  /** Sets `id` attribute. */
  id: string;
  /** Sets `data-testid` for testing purposes. */
  testId?: string;
  /** Sets custom class name(s) to allow for additional styling. */
  className?: string;
  /** Accessibility role, used to set the proper semantic tag or role. */
  roleType?: string;
}

export const ContainerBodyBase = (props: React.PropsWithChildren<IContainerBodyBaseProps>): JSX.Element => {
  const { id, testId, className, roleType, children } = props;

  const newProps = {
    id,
    className,
    'data-testid': testId,
  };

  if (roleType === 'section') {
    return <section {...newProps}>{children}</section>;
  }

  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
};

ContainerBodyBase.displayName = 'ContainerBodyBase';
