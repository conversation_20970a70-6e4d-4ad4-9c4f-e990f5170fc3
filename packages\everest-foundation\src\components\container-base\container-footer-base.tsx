import React from 'react';
import classnames from 'classnames';

import styles from './container-base.module.scss';

export interface IContainerFooterBaseProps {
  /** Sets `id` attribute. */
  id: string;
  /** Sets `data-testid` for testing purposes. */
  testId?: string;
  /** Sets custom class name(s) to allow for additional styling. */
  className?: string;
  /** Accessibility role, used to set the proper semantic tag or role. */
  roleType?: string;
}

export const ContainerFooterBase = (props: React.PropsWithChildren<IContainerFooterBaseProps>): JSX.Element => {
  const { id, testId, className, roleType, children } = props;

  const newProps = {
    id,
    className: classnames(styles.evrContainerFooterBase, className),
    'data-testid': testId,
  };

  if (roleType === 'section') {
    return <section {...newProps}>{children}</section>;
  }

  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
};

ContainerFooterBase.displayName = 'ContainerFooterBase';
