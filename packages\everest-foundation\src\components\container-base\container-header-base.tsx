import React from 'react';

export interface IContainerHeaderBaseProps {
  /** Sets `id` attribute. */
  id: string;
  /** Sets `data-testid` for testing purposes. */
  testId?: string;
  /** Sets custom class name(s) to allow for additional styling. */
  className?: string;
  /** Sets min height. */
  minHeight?: string;
  /** Accessibility role, used to set the proper semantic tag or role. */
  roleType?: string;
}

export const ContainerHeaderBase = (props: React.PropsWithChildren<IContainerHeaderBaseProps>): JSX.Element => {
  const { id, testId, className, minHeight, roleType, children } = props;

  const newProps = {
    id,
    className,
    'data-testid': testId,
    style: { minHeight },
  };

  if (roleType === 'section') {
    return <section {...newProps}>{children}</section>;
  }

  return (
    <div role={roleType} {...newProps}>
      {children}
    </div>
  );
};

ContainerHeaderBase.displayName = 'ContainerHeaderBase';
