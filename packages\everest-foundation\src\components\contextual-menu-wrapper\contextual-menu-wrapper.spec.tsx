import React from 'react';
import { render, screen } from '@testing-library/react';

import { ContextualMenuWrapper } from './contextual-menu-wrapper';
import { IContextualMenu } from '../contextual-menu/contextual-menu';
import { MenuListItem } from '../menu-list-item';
import { defaultContext } from '../popover-menu/popover-menu-context';

describe('[ContextualMenuWrapper]', () => {
  beforeEach(() => {
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  afterEach(() => {
    jest.resetAllMocks();
  });

  const testId = 'contextual-wrap-test';
  const id = 'contextual-wrap';

  // only testing that props are passed to the underlying contextual menu
  const propsMock = {
    ...defaultContext,
    visible: true,
    id,
    testId,
  };

  const TestComponent = (props: IContextualMenu) => (
    <ContextualMenuWrapper {...props}>
      <MenuListItem id="menu-item">Menu Item</MenuListItem>
    </ContextualMenuWrapper>
  );

  it('should pass props to contextual menu', () => {
    render(<TestComponent {...propsMock} />);
    const contextMenu = screen.queryByTestId(testId);
    expect(contextMenu).toBeInTheDocument();
    expect(contextMenu).toHaveAttribute('id', id);
  });
});
