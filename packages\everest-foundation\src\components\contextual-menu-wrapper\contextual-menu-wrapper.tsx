import React, { PropsWithChildren, useContext } from 'react';

import { ContextualMenu, IContextualMenu } from '../contextual-menu/contextual-menu';
import { PopoverMenuContext } from '../popover-menu/popover-menu-context';

export const ContextualMenuWrapper = (props: PropsWithChildren<IContextualMenu>): JSX.Element => {
  const popoverMenuContext = useContext(PopoverMenuContext);
  const propsContext = {
    ...popoverMenuContext,
    ...props,
  };
  const { children } = propsContext;
  return <ContextualMenu {...propsContext}>{children}</ContextualMenu>;
};
