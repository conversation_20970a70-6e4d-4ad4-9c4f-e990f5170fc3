import React, { useRef, useState, PropsWithChildren, useLayoutEffect } from 'react';

import { ContextualMenu } from './contextual-menu';
import { TMenuListItemValue } from '../menu-list';

interface IContextualMenuTestComponent {
  testId: string;
  id: string;
  onChange: (value: TMenuListItemValue) => undefined;
  buttonTestId: string;
  visible?: boolean;
  maxItems?: number;
  onClose?: () => void;
  onOpen?: () => void;
}

export const ContextualMenuTestComponent = (props: PropsWithChildren<IContextualMenuTestComponent>): JSX.Element => {
  const triggerRef = useRef(null);
  const menuRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const { buttonTestId, onChange, onClose, onOpen, children } = props;
  useLayoutEffect(() => {
    if (props.visible) setVisible(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <button
        id={buttonTestId}
        data-testid={buttonTestId}
        ref={triggerRef}
        onClick={() => {
          setVisible((visible) => !visible);
        }}
        style={{
          height: 'var(--evr-size-xl)',
          border: '1px solid grey',
          borderRadius: 'var(--evr-radius-2xs)',
          backgroundColor: 'white',
          color: 'black',
          margin: 0,
          marginBottom: 'var(--evr-size-lg)',
        }}
      >
        Test button
      </button>
      <ContextualMenu
        {...props}
        visible={visible}
        setVisible={setVisible}
        triggerRef={triggerRef}
        menuRef={menuRef}
        ariaLabelledBy={buttonTestId}
        onChange={onChange}
        onClose={onClose}
        onOpen={onOpen}
      >
        {children}
      </ContextualMenu>
    </>
  );
};
