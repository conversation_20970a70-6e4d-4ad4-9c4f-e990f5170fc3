import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { ContextualMenu } from './contextual-menu';
import { But<PERSON> } from '../button';
import { PopoverMenuButton } from '../popover-menu/popover-menu-button';
import { MenuListItem } from '../menu-list-item';
import { mergeRefs } from '../../utils';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';

export const scope = {
  ContextualMenu,
  Button,
  PopoverMenuButton,
  MenuListItem,
  mergeRefs,
};

A `ContextualMenu` is used to show actionable items related to the
current context.

## Usage

### ContextualMenu with a Button

`ContextualMenu` can be used with any clickable component.

`visible` prop indicates if the menu is visible. `setVisible` prop sets the visibility of the menu.

To trigger the menu, set the reference to the trigger element using the `triggerRef` prop and the visibility is controlled by `onClick` prop of the trigger element.

`onChange` is a callback function that runs when a menu selection is made, and it updates the component state with the menu item id and the menu item role.

Click on the trigger element to display the menu items. If no selection must be made, click on the trigger element to hide the menu items.

`MenuListItem` items that are checkable should have a role of either `menuitemcheckbox` or `menuitemradio` and can be specified using the `checkedIds` prop. See <LinkTo kind="Toolbox/Menu List">MenuList</LinkTo>.

export const defaultCode = `
  ()=>{
      const styles = {
          row: {
              display: 'flex',
              justifyContent: 'space-around',
              flexWrap: 'wrap',
          },
          column: {
              display: 'flex',
              justifyContent: 'space-around',
              alignItems: 'center',
              flexDirection: 'column',
              width: '100%',
              rowGap: '0.625rem'
          }
      }
      const Row = ({ children }) => (
          <div style={styles.row}>{children}</div>
      );
      const Column = ({ children }) => (
          <div style={styles.column}>{children}</div>
      );
      const [selectedMenuItem, setSelectedMenuItem] = React.useState('');
      const [visible, setVisible] = React.useState(false);
      const triggerRef = React.useRef(null);
      const menuRef = React.useRef(null);
      return (
        <Column>
          <Row>
            <Button id='trigger-ref-btn' label="Trigger Element" ref={triggerRef} onClick={() => {
            setVisible((visible) => !visible);
          }}/>
            <ContextualMenu
              testId="contextual-menu-test-id"
              id="contextual-menu-id"
              triggerRef={triggerRef}
              setVisible={setVisible}
              visible={visible}
              onChange={({id}) => setSelectedMenuItem(id)}
            >
              <MenuListItem id="list-item-1">Planes</MenuListItem>
              <MenuListItem id="list-item-2">Trains</MenuListItem>
            </ContextualMenu>
          </Row>
          <Row style={{ marginTop: '2rem' }}>
            <p className="evrBodyText">{selectedMenuItem === ''
              ? 'Choose from the menu.'
              : 'You chose option with id ' + selectedMenuItem}
            </p>
          </Row>
        </Column>
      );
}
`;

<CodeExample scope={scope} code={defaultCode} />

### Menu Placement and Disable

The menu items can be disabled with the `disabled` attribute. To disable the menu, disable the trigger element.

The position of the menu on mount can be set with the `placement` prop. The default value is `bottom`. If there isn't enough space on the screen for the menu's placement on mount, it will attempt the opposite placement. For example, if there is not enough space at the `bottom`, the component will attempt to place the menu at the `top`. To learn more about positioning see the <LinkTo kind="Toolbox/AnchoredOverlay">AnchoredOverlay</LinkTo> component.

export const menuPlacementDisable = `
  ()=>{
      const styles = {
      row: {
        display: 'flex',          
        justifyContent: 'space-evenly',
        flexWrap: 'wrap',
        columnGap: '20px'
      },
    };   
      const [visible_bottom, setVisible_bottom] = React.useState(false);
      const triggerRef_bottom = React.useRef(null);
      const [visible_left, setVisible_left] = React.useState(false);
      const triggerRef_left = React.useRef(null);
      const [visible_disabled, setVisible_disabled] = React.useState(false);
      const triggerRef_disabled = React.useRef(null);
      const [visible_MenuItemDisabled, setVisible_MenuItemDisabled] = React.useState(false);
      const triggerRef_MenuItemDisabled = React.useRef(null);
      return (
        <div style={styles.row}>
          <Button id='placement-bottom-btn' label="Placement Bottom" ref={triggerRef_bottom} onClick={() => {
            setVisible_bottom((visible_bottom) => !visible_bottom);
          }}/>
          <ContextualMenu
            testId="contextual-menu-test-id"
            id="contextual-menu-id"
            triggerRef={triggerRef_bottom}
            setVisible={setVisible_bottom}
            visible={visible_bottom}
          >
            <MenuListItem id="list-item-1">Planes</MenuListItem>
            <MenuListItem id="list-item-2">Trains</MenuListItem>
          </ContextualMenu>
          <Button id='placement-left-btn' label="Placement Left" ref={triggerRef_left} onClick={() => {
            setVisible_left((visible_left) => !visible_left);
          }}/>
          <ContextualMenu
            testId="contextual-menu-test-id"
            id="contextual-menu-id"
            triggerRef={triggerRef_left}
            setVisible={setVisible_left}
            visible={visible_left}
            placement="left"
          >
            <MenuListItem id="list-item-1">Add New</MenuListItem>
            <MenuListItem id="list-item-2" divider>Delete</MenuListItem>
            <MenuListItem id="list-item-4">Download</MenuListItem>
          </ContextualMenu>
          <Button id='trigger-element-disabled-btn' label="Trigger Element Disabled" disabled ref={triggerRef_disabled} onClick={() => {
            setVisible_disabled((visible_disabled) => !visible_disabled);
          }}/>
          <ContextualMenu
            testId="contextual-menu-test-id"
            id="contextual-menu-id"
            triggerRef={triggerRef_disabled}
            setVisible={setVisible_disabled}
            visible={visible_disabled}
          >
            <MenuListItem id="list-item-1">Add New</MenuListItem>
            <MenuListItem id="list-item-2">Delete</MenuListItem>
          </ContextualMenu>
          <Button id='menu-list-item-disabled-btn' label="MenuListItem Disabled" ref={triggerRef_MenuItemDisabled} onClick={() => {
            setVisible_MenuItemDisabled((visible_MenuItemDisabled) => !visible_MenuItemDisabled);
          }}/>
          <ContextualMenu
            testId="contextual-menu-test-id"
            id="contextual-menu-id"
            triggerRef={triggerRef_MenuItemDisabled}
            setVisible={setVisible_MenuItemDisabled}
            visible={visible_MenuItemDisabled}
          >
            <MenuListItem id="list-item-1">Add New</MenuListItem>
            <MenuListItem id="list-item-2" disabled>Delete</MenuListItem>
          </ContextualMenu>
        </div>
      );
}
`;

<CodeExample scope={scope} code={menuPlacementDisable} />

## Accessing ContextualMenu using ref

Click on the Button to access the ContextualMenu, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '0.625rem'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    const [visible, setVisible] = React.useState(false);
    const triggerRef = React.useRef(null);
    return (
      <Column>
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
        </Row>
        <Row>
            <Button id='trigger-element-btn' label="Trigger Element" ref={mergeRefs([ref, triggerRef])} onClick={() => {
            setVisible((visible) => !visible);
          }}/>
          <ContextualMenu
            testId="contextual-menu-test-id"
            id="contextual-menu-id"
            triggerRef={triggerRef}
            setVisible={setVisible}
            visible={visible}
            ref={ref}
          >
            <MenuListItem id="list-item-1">Planes</MenuListItem>
            <MenuListItem id="list-item-2">Trains</MenuListItem>
          </ContextualMenu>
        </Row>
      </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Click on the trigger element to display the menu items. If no selection has to be made, click on the trigger element to hide the menu items.

The default and suggested maximum number of menu items is `7`. This can be adjusted with the `maxItems` prop on `ContextualMenu`.

Related menu items can be visually grouped using the `divider` prop on the `MenuItem` component.

Menu items should have succinct descriptions.

## Accessibility

The menu can be navigated with a keyboard using the `Arrow`, `Home` and `End` keys. Selections are made with the `Enter` and `Space` keys. Jump to a menu item by pressing the first letter of a menu item's text.
