# Contextual Menu

## Summary

Research and document implementations for the Everest Contextual Menu.

- Start Date: 2022-03-25
- Figma link: https://www.figma.com/file/C5r0pQ9nTQiump044rnI8s/%F0%9F%A7%AAMenus?node-id=2%3A349

## API

### ContextualMenuContext

1. **activeDescendantId**: `undefined | string`  
   The current value of the `aria-activedescendant` attribute.
1. **setActiveDescendantId**: `undefined | Dispatch<SetStateAction<string>>`  
   React set state callback for `activeDescendantId`.
1. **onChange**: `undefined | Dispatch<SetStateAction<string>>`  
   Callback that runs when a menu selection is made and should update React state with the menu item id.
1. **closeContextualMenu**: `undefined | () => void`  
   Callback to close the contextual menu.
1. **hideFocusRing**: `undefined | () => void`  
   Callback to manually hide the focus ring.

### ContextualMenu

1. **id**: `string`  
   `id` of the menu element.
1. **triggerRef**: `RefObject<HTMLElement>`  
   React ref to the trigger element
1. **visible**: `boolean`  
   Indicates if the menu is visible.
1. **setVisible**: `React.Dispatch<SetStateAction<boolean>>`  
   Set menu visibility.
1. **maxItems**: `number`  
   Optional. Set the maximum number of menu item elements allowed.
1. **testId**: `string | undefined`  
   Optional. Set the `data-testid` attribute of the menu.
1. **onChange**: `(value: string) => void`  
   Callback that runs when a menu selection is made and should update React state with the menu item id.
1. **selectedId**: `string`  
   State value holding the id of the selected menu item.
1. **activeDescendantId**: `string`  
   The current value of the `aria-activedescendant` attribute.
1. **setActiveDescendantId**: `Dispatch<SetStateAction<string>>`  
   React set state callback for `activeDescendantId`.
1. **placement**: `TPlacement | undefined`  
   Optional. Specifies the placement of the menu. See AnchoredOverlay.

### ContextualMenuItem (extends ContextualMenuContext)

1. **id**: `string`  
   `id` of the menu item.
1. **testId**: `string | undefined`  
   Optional. Value of the `data-testid` attribute.
1. **disabled**: `boolean | undefined`  
   Optional. Indicates if a menu item is disabled.
1. **divider**: `boolean | undefined`  
   Optional. Indicates if a menu item has a horizontal divider at bottom.

## Detailed Design

A contextual menu is used to show actionable items related to the current context. For example, users can right-click on a data row to display a set of shortcut actions related to data rows. A contextual menu can also be triggered by a menu button.

A contextual menu is usually consists of two parts:

- The menu trigger
- The menu (a menu container wrapped with menu items)

A selected menu item can initiate an action, or display a submenu of additional menu items. A menu item can be disabled. A disabled menu item appears gray and doesn’t highlight when the pointer moves over it.

Menus can also include separators, and menu items can contain icons and symbols. A menu may use separator lines or subheadings to create visually distinct groups of related menu items.

## Accessibility

This section describes accessibility requirements follow the [Actions Menu Button Pattern](https://www.w3.org/TR/wai-aria-practices/examples/menu-button/menu-button-actions-active-descendant.html).

### Aria Attributes

#### Menu Trigger

- Use `aria-haspopup="true"` to indicate the menu trigger opens a menu.
- Set `aria-expanded="true"` when the menu is open. Remove the `aria-expanded` attribute when the menu is closed.

#### Menu

- The menu is rendered as a styled `<ul role="menu">` element with an aria-label attribute whose value is the accessible name of the menu trigger.
- The menu items are rendered as styled `<li role="menuitem">` elements.
- Use `aria-activedescendant` to indicate which menu item is visually indicated as focused, while DOM focus remains on the menu.

### Keyboard Interaction

- Use either the space key or enter key to trigger/close the menu.
- Use Esc key to close the menu and moves the focus back to the menu trigger.
- Use Arrow keys to cycle focus through menu items (including disabled menu items).
- Use character keys to move focus to the next menu item with a label that starts with the typed character if such an menu item exists – otherwise, focus does not move.
- If a menu item opens a submenu, pressing Enter or the right arrow key opens the submenu and focus goes to the first item in the submenu.
- If a submenu is open, pressing the left arrow key closes the submenu and puts the focus back on the menu item that opened the submenu.

## Alternatives/Trade-Offs

## Q&A

**Sample question**  
Sample answer

## Future Considerations

## Other Design Systems

**Fluent UI** - https://developer.microsoft.com/en-us/fluentui#/controls/web/contextualmenu

- Support icons and secondary text
- Can change direction of the popup menu, support using arrow
- Support customized sub menu that renders a layout

**Material UI** - https://mui.com/components/menus/#context-menu

- Provide the "dense menu" option that allows long list and long text by reducing padding and text size
- The menu content can be mixed with other component such as Avatar by pointing the `anchorEl` to the trigger component ref
- Support max height - scroll the menu internally when the max height is met

**Chakra UI** - https://chakra-ui.com/docs/components/overlay/menu

- Support render menu in a portal
- Support letter navigation
- Chakra exports 8 components for rendering menus. This approach allows flexible configuration of a menu that meets varies business needs.

**Atlassian** - https://atlassian.design/components/menu/examples

- The Atlassian menu is meant to be composed using multiple smaller components, including:

1. Menu group: useful to render menus within navigation elements.
2. Popup menu group: render menus within popup containers. It's useful to create customized menu.
3. Section: used to group related items in a menu, with an optional heading component.

## Required PBIs

For more complex components, consider creating smaller PBIs that can fit into a sprint, but include appropriate testing in each PBI.

## Acceptance Criteria
