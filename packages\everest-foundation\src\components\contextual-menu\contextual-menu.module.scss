@use '../../index.scss' as helper;

.evrContextualMenu {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  // list-style-type: none;
  margin: 0;
  padding-inline: 0;
  padding-block: var(--evr-spacing-2xs);
  border-radius: var(--evr-radius-2xs);
  box-shadow: var(--evr-depth-06);
  background-color: var(--evr-surfaces-primary-default);
  outline: none;
  min-width: helper.applyRemFactor(10rem); // 160px
  max-width: helper.applyRemFactor(25rem); // 400px
  max-height: 90vh; // fallback in case dvh not supported
  max-height: 90dvh; 
  overflow-y: auto;
}
