import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { ContextualMenuTestComponent } from './contextual-menu-test-component';
import { MenuListItem } from '../menu-list-item';

const testId = 'menu-test-id';
const id = 'contextual-menu-id';
const onChange = jest.fn();
const onClose = jest.fn();
const onOpen = jest.fn();
const buttonTestId = 'button-test-id';

const propsMock = {
  testId,
  id,
  onChange,
  onClose,
  onOpen,
  buttonTestId,
};

const getContextualMenu = () => screen.getByRole('menu');
const queryContextualMenu = () => screen.queryByRole('menu');
const getTriggerButton = () => screen.getByRole('button');

describe('[ContextualMenu]', () => {
  beforeEach(() => {
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should not render when trigger button has not been clicked', () => {
    render(
      <ContextualMenuTestComponent {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </ContextualMenuTestComponent>
    );

    expect(queryContextualMenu()).toBeNull();
    expect(screen.queryByText('Planes')).toBeNull();
    expect(screen.queryByText('Trains')).toBeNull();
  });

  it('should render menu and children without issue when trigger button is clicked and call onOpen', async () => {
    render(
      <ContextualMenuTestComponent {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </ContextualMenuTestComponent>
    );

    await userEvent.click(getTriggerButton());
    expect(getContextualMenu()).toBeInTheDocument();

    getContextualMenu().focus();
    expect(document.activeElement).toEqual(getContextualMenu());

    expect(screen.getByText('Planes')).toBeInTheDocument();
    expect(screen.getByText('Trains')).toBeInTheDocument();
    expect(onOpen).toHaveBeenCalledTimes(1);
  });

  it('should call onChange and onClose when menu item is clicked, and close menu', async () => {
    render(
      <ContextualMenuTestComponent {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </ContextualMenuTestComponent>
    );

    await userEvent.click(getTriggerButton());
    expect(getContextualMenu()).toBeInTheDocument();

    await userEvent.click(screen.getByRole('menuitem', { name: 'Trains' }));
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id: 'trains-item', role: 'menuitem' });

    expect(queryContextualMenu()).toBeNull();
    expect(screen.queryByText('Planes')).toBeNull();
    expect(screen.queryByText('Trains')).toBeNull();
    expect(onClose).toHaveBeenCalledTimes(1);
  });
});
