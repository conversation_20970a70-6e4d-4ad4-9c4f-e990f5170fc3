import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { ContextualMenu } from './contextual-menu';
import { MenuListItem } from '../menu-list-item';
import { PopoverMenuButton } from '../popover-menu/popover-menu-button';
import Examples from './contextual-menu.examples.mdx';
import { Button } from '../button';
import { useRef, useState } from 'react';

<Meta
  title="Toolbox/Contextual Menu"
  component={ContextualMenu}
  parameters={{
    controls: {
      exclude: ['fitToScreen', 'onChange', 'onClose', 'onOpen'],
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9870&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  args={{
    testId: 'contextual-menu-test-id',
    id: 'test-contextual-menu-button-id',
    maxItems: 7,
    placement: 'bottom',
    disabled: '',
    initialPosition: 'start',
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Set the `id` attribute on the trigger element.',
    },
    placement: {
      type: 'enum',
      control: 'select',
      options: ['top', 'bottom', 'right', 'left'],
      description: 'Placement of the menu.',
      table: {
        defaultValue: { summary: 'bottom' },
      },
    },
    maxItems: {
      type: 'number',
      control: 'number',
      description: 'Max number of items displayed in overlay.',
      table: {
        defaultValue: { summary: 7 },
      },
    },
    disabled: {
      description: 'Disable the menu item.',
      control: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    initialPosition: {
      control: 'select',
      options: ['start', 'end'],
      description: 'Specifies the default `MenuListItem` when opened. It is either `start` or `end`.',
      table: {
        defaultValue: { summary: 'start' },
      },
    },
    testId: {
      control: 'text',
      description: 'Sets `data-testid` attribute on the html elements.',
    },
    ariaLabelledBy: {
      control: 'text',
      description: 'Id of the label element when not standalone.',
    },
    menuRef: {
      control: 'disable',
      description: 'Set the reference to the `MenuList`.',
    },
    visible: {
      control: 'disable',
      description: 'Indicates if the menu is visible.',
    },
    setVisible: {
      control: 'disable',
      description: 'Set menu visibility.',
    },
    triggerRef: {
      control: 'disable',
      description: 'Set reference to the trigger element.',
    },
    checkedIds: {
      control: '-',
      description: 'Specifies the `MenuListItem` ids which remain checked.',
    },
  }}
/>

# Contextual Menu

<Examples />

## Live Demo

<Canvas>
  <Story name="Contextual Menu">
    {({ ariaLabelledBy, initialPosition, placement, maxItems, testId, id, ...args }) => {
      const [lastSelectedMenuItem, setLastSelectedMenuItem] = useState('');
      const [checkedIds, setCheckedIds] = useState(['list-item-2']);
      const [visible, setVisible] = useState(false);
      const triggerRef = useRef(null);
      const menuRef = useRef(null);
      return (
        <>
          <Button
            label="Trigger Element"
            onClick={() => {
              setVisible((visible) => !visible);
            }}
            ref={triggerRef}
            testId={testId}
            id={id}
          />
          <ContextualMenu
            id="user-menu-id"
            triggerRef={triggerRef}
            menuRef={menuRef}
            setVisible={setVisible}
            visible={visible}
            onChange={({ id, role }) => {
              // setLastSelectedMenuItem is not required
              setLastSelectedMenuItem(id);
              if (role === 'menuitemcheckbox') {
                if (checkedIds.includes(id)) {
                  const index = checkedIds.indexOf(id);
                  const checkedIdsCopy = [...checkedIds];
                  checkedIdsCopy.splice(index, 1);
                  setCheckedIds(checkedIdsCopy);
                  return;
                }
                setCheckedIds((prev) => [...prev, id]);
              }
            }}
            checkedIds={checkedIds}
            maxItems={maxItems}
            placement={placement}
            initialPosition={initialPosition}
            ariaLabelledBy={ariaLabelledBy}
          >
            <MenuListItem id="list-item-1">Profile</MenuListItem>
            <MenuListItem id="list-item-2" role="menuitemcheckbox">
              Remember Preferences
            </MenuListItem>
          </ContextualMenu>
          <br />
          <p className="evrBodyText">
            {lastSelectedMenuItem === ''
              ? 'Choose from the menu. In this example, the first list item is not checkable, however the second is.'
              : 'You chose option with id ' + lastSelectedMenuItem}
          </p>
        </>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Contextual Menu" />
