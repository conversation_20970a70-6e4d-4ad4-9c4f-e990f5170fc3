import React, { PropsWithChildren, RefObject, useRef, useEffect, useState, useLayoutEffect } from 'react';
import { AnchoredOverlay, IAnchorOrigin, ITransformOrigin, TPlacement } from '@ceridianhcm/everest-cdk';

import { MenuList, TMenuListInitialPosition, TMenuListItemValue } from '../menu-list';
import { getPlacementOrigins } from '../popover/popover-helper';

import styles from './contextual-menu.module.scss';
import variables from '../../variables.scss';

export interface IContextualMenu {
  id: string;
  onChange?: (value: TMenuListItemValue) => void;
  triggerRef?: RefObject<HTMLElement>;
  menuRef?: RefObject<HTMLUListElement>;
  setVisible?: (vale: boolean) => void;
  visible?: boolean;
  testId?: string;
  maxItems?: number;
  placement?: TPlacement;
  anchorOrigin?: IAnchorOrigin;
  transformOrigin?: ITransformOrigin;
  ariaLabelledBy?: string;
  onClose?: () => void;
  onOpen?: () => void;
  initialPosition?: TMenuListInitialPosition;
  fitToScreen?: boolean; //fitToScreen doesnt effect the output as this is disabled in anchoredOverlay.
  ref?: React.ForwardedRef<HTMLDivElement>;
  checkedIds?: string[];
}

export const ContextualMenu = React.forwardRef<HTMLDivElement, PropsWithChildren<IContextualMenu>>(
  (props: PropsWithChildren<IContextualMenu>, ref): JSX.Element => {
    const [isAnchoredOverlayPositioned, setIsAnchoredOverlayPositioned] = useState(false);
    // triggerRef needs to be optional in the interface so we can take advantage of the convenience of
    // context and not pass it as a prop explicitly, however AnchoredOverlay won't accept an optional
    // triggerRef. To get around this create a defaultTriggerRef that should only be created once
    const defaultTriggerRef = useRef<HTMLElement>(null);
    const {
      id,
      testId,
      maxItems = 7,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      onChange = (value: TMenuListItemValue) => undefined,
      visible = false,
      setVisible,
      children,
      placement = 'bottom',
      anchorOrigin,
      transformOrigin,
      ariaLabelledBy,
      onClose,
      onOpen,
      menuRef,
      triggerRef = defaultTriggerRef,
      initialPosition,
      fitToScreen = true,
      checkedIds,
    } = props;

    const [overlayContentMaxHeight, setOverlayContentMaxHeight] = useState<string | undefined>(undefined);

    const { anchorOrigin: getAnchorOriginFromPlacement, transformOrigin: getTransformOriginFromPlacement } =
      getPlacementOrigins(placement);

    const getAnchorOrigin = () =>
      anchorOrigin || (transformOrigin ? { vertical: 'bottom', horizontal: 'left' } : getAnchorOriginFromPlacement);

    const getTransformOrigin = () =>
      transformOrigin || (anchorOrigin ? { vertical: 'top', horizontal: 'left' } : getTransformOriginFromPlacement);

    useLayoutEffect(() => {
      if (visible) {
        const menuParentElement = menuRef?.current?.parentElement;
        const { paddingTop: menuPaddingTop, paddingBottom: menuPaddingBottom } = menuParentElement
          ? window.getComputedStyle(menuParentElement)
          : { paddingTop: '0px', paddingBottom: '0px' };
        const listItemHeight = menuRef?.current?.firstElementChild?.clientHeight ?? 0;
        const maxListBoxItemHeight = maxItems * listItemHeight;
        const cumulativeDividerHeight = ` + ${variables.menuListHorizontalDividerHeight}`.repeat(maxItems);

        setOverlayContentMaxHeight(
          `calc(${menuPaddingTop} + ${menuPaddingBottom} + ${maxListBoxItemHeight}px${cumulativeDividerHeight})`
        );
      }
    }, [maxItems, menuRef, visible]);

    const anchoredOverlayProps = {
      id: `${id}-anchored-overlay`,
      triggerRef,
      visible,
      anchorOrigin: getAnchorOrigin(),
      transformOrigin: getTransformOrigin(),
      fitToScreen,
      height: 'auto',
      maxHeight: overlayContentMaxHeight,
      style: { display: overlayContentMaxHeight ? 'flex' : undefined },
      onInitPosition: () => setIsAnchoredOverlayPositioned(true),
    };
    const menuListProps = {
      id,
      initialPosition,
      ref: menuRef,
      onChange,
      closeMenu: closeContextualMenu,
      ariaLabelledBy,
      testId,
      checkedIds,
    };

    function closeContextualMenu() {
      setVisible?.(false);
      setIsAnchoredOverlayPositioned(false);
      onClose?.();
    }

    useEffect(() => {
      if (visible && isAnchoredOverlayPositioned) {
        onOpen?.();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible, isAnchoredOverlayPositioned]);

    return (
      <AnchoredOverlay {...anchoredOverlayProps}>
        <div ref={ref} className={styles.evrContextualMenu}>
          <MenuList {...menuListProps}>{children}</MenuList>
        </div>
      </AnchoredOverlay>
    );
  }
);

ContextualMenu.displayName = 'ContextualMenu';
