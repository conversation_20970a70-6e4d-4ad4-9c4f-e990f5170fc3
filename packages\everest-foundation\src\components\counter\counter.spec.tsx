import React from 'react';
import { render, screen } from '@testing-library/react';

import { Counter } from './counter';

describe('[Counter]', () => {
  const altText = 'This is an alt text';
  const testId = 'counter-test-id';
  const limit = 100;
  const value = '50';
  const mockProps = {
    altText,
    testId,
    value,
  };

  it('should render counter', () => {
    render(<Counter {...mockProps} />);
    expect(screen.getByText(value));
  });

  it('should render counter with limit properly when provided', () => {
    render(<Counter {...mockProps} limit={limit} />);
    expect(screen.getByText(`${value}/${limit}`));
  });

  it('should render altText when provided', () => {
    render(<Counter {...mockProps} altText={altText} />);
    expect(screen.getByText(altText));
  });
});
