import React from 'react';
import classNames from 'classnames';

import { useCreateTestId } from '../../utils';

import styles from './counter.module.scss';

export type TCounterStatus = 'default' | 'error' | 'success';

export interface ICounterProps {
  altText?: string;
  id?: string;
  limit?: number;
  value?: string;
  status?: TCounterStatus;
  testId?: string;
}

/**
 * Counter is currently an internal component only used by TextArea and TextField
 * @internal
 */
export const Counter = ({ altText, id, limit, status = 'default', testId, value }: ICounterProps): JSX.Element => {
  const dataRef = useCreateTestId(testId);
  return (
    <span className={styles.evrCounter} ref={dataRef}>
      <span
        aria-hidden={true}
        className={classNames('evrCaptionText', {
          [styles.error]: status === 'error',
        })}
      >
        {limit ? `${value}/${limit.toString(10)}` : value}
      </span>
      <span className={styles.evrCounterSRText} id={id}>
        {altText}
      </span>
    </span>
  );
};
