import React from 'react';

type TDateFieldBaseContext = {
  type: string;
};

export type TDateFieldDatePickerContext = {
  getDatefieldCanPositionCursor?: () => boolean;
  setDatefieldCanPositionCursor?: (val: boolean) => void;
} & TDateFieldBaseContext;

// create different interfaces as required and union
export type TDateFieldContext = TDateFieldDatePickerContext;

export const defaultDateFieldContext = {
  type: '',
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const DateFieldContext = React.createContext<TDateFieldContext>(defaultDateFieldContext);

if (process.env.NODE_ENV !== 'production') {
  DateFieldContext.displayName = 'DateFieldContext';
}
