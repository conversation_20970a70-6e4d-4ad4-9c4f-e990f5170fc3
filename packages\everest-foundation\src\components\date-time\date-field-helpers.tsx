// globalization can provide the placeholder value for different locales
export interface IDateSegmentPlaceholder {
  day: string;
  month: string;
  year: string;
}

export interface IDateTimeFormatPart {
  id: string;
  type: Intl.DateTimeFormatPartTypes;
  value: string;
}

export interface IDateFieldTextMapBase {
  ariaLabel?: string;
  clearButtonAriaLabel?: string;
  iconAriaLabel?: string;
  dayLabel: string;
  monthLabel: string;
  yearLabel: string;
  formatLabel: string;
  expectedDateFormatLabel: string;
  blank: string;
  required?: string;
  invalidEntry?: string;
  invalidDay?: string;
  invalidMonth?: string;
  invalidYear?: string;
}

export const getSegmentMaxValueLength = (type: Intl.DateTimeFormatPartTypes): number => {
  switch (type) {
    case 'year':
      return 4;
    case 'literal':
      return 1;
    default:
      return 2;
  }
};

export const getDisplayText = (
  dateTimeParts: IDateTimeFormatPart[],
  partialDayValue: string | undefined,
  partialMonthValue: string | undefined,
  partialYearValue: string | undefined,
  dateSegmentPlaceholder: IDateSegmentPlaceholder
): string => {
  let displayText = '';
  if (partialDayValue || partialMonthValue || partialYearValue) {
    for (const dateTimePart of dateTimeParts) {
      switch (dateTimePart.type) {
        case 'literal':
          displayText += dateTimePart.value;
          break;
        case 'day':
          displayText += partialDayValue || dateSegmentPlaceholder[dateTimePart.type];
          break;
        case 'month':
          displayText += partialMonthValue || dateSegmentPlaceholder[dateTimePart.type];
          break;
        case 'year':
          displayText += partialYearValue || dateSegmentPlaceholder[dateTimePart.type];
          break;
      }
    }
  }
  return displayText;
};

export const getMaxDayValue = (year: string | undefined, month: string | undefined): number => {
  let curMaxDayValue = 31;
  if (!month) return curMaxDayValue;
  const curMonth = parseInt(month);
  if (curMonth === 2) {
    curMaxDayValue = 28;
    if (!year || parseInt(year) % 4 === 0) curMaxDayValue = 29;
  }
  if (month && [4, 6, 9, 11].includes(curMonth)) curMaxDayValue = 30;
  return curMaxDayValue;
};

export const getNextSegmentType = (
  dateTimeParts: IDateTimeFormatPart[],
  focusableTypes: Intl.DateTimeFormatPartTypes[],
  type: Intl.DateTimeFormatPartTypes,
  isPrevious: boolean
): string => {
  let nextSegmentType = '';
  let previousSegmentType = '';
  let currentTypeFound = false;

  for (const dateTimePart of dateTimeParts.filter((x) => focusableTypes.includes(x.type))) {
    if (currentTypeFound) {
      nextSegmentType = dateTimePart.type;
      break;
    }
    if (dateTimePart.type === type) {
      currentTypeFound = true;
      if (isPrevious && previousSegmentType) {
        nextSegmentType = previousSegmentType;
        break;
      }
    }
    previousSegmentType = dateTimePart.type;
  }

  return nextSegmentType;
};

export const getNextSeparator = (dateTimeParts: IDateTimeFormatPart[], i: number): string | undefined => {
  let nextSeparator;
  if (dateTimeParts[i + 1]?.type === 'literal') {
    nextSeparator = dateTimeParts[i + 1].value.trim();
  }
  return nextSeparator;
};
