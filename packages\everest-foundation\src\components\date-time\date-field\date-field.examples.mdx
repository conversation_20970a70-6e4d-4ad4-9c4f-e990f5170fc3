import { CodeExample } from '../../../../.storybook/doc-blocks/example/example';
import { useRef, useState } from 'react';
import { DateField } from '../date-field';
import { Button } from '../../button';

export const scope = { DateField, Button, useRef, useState };

## Basic Usage

DateField lets users enter and edit a date using a keyboard, where each date part value is displayed as an individually editable segment. It's a controlled component so it doesn't store or update its states.

Let's start with creating a basic functional DateField using props to control the state:

export const defaultCode = `() => {
  const styles = {
    container: {
      width: '260px',
    },
  };
  const locale = 'en-US';
  /**
   *  using BCP 47 language code
   *      https://www.ietf.org/rfc/bcp/bcp47.txt
   *      https://www.techonthenet.com/js/language_tags.php
   * can override our own culture format if it is differ from Intl.DateTimeFormat and formatToParts
   */
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const formatDateTimeForSR = (value) => {
    return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
  };
  const textMap = {
    ariaLabel: 'This is an aria label',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    blank: 'blank',
    required: 'required',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
  };
  const [dateValue, setDateValue] = useState(null);
  const [status, setStatus] = useState('default');
  const [errorMessage, setErrorMessage] = useState('');
  const [errorMessagePrefix, setErrorMessagePrefix] = useState('');
  const [hasDisplayValue, setHasDisplayValue] = useState(false);
  const handleDateValueChange = (displayValue, value) => {
    setHasDisplayValue(!!displayValue);
    if (value) {
      const newDate = new Date(value);
      // if Year is less than 100, it will map it to 1901 or 1999
      // using setFullYear for any year less than 100 to work correctly
      newDate.setFullYear(value.getFullYear());
      setDateValue(newDate);
    } else {
      setDateValue(null);
    }
  };
  const handleBlur = () => {
    if (!dateValue && hasDisplayValue) {
      setStatus('error');
      setErrorMessagePrefix('Error:');
      setErrorMessage('Please enter a valid date');
    } else {
      setStatus('default');
    }
  };
  return (
    <div style={styles.container}>
      <DateField
        id="datefield-default"
        label="Appointment date"
        dateSegmentPlaceholder={dateSegmentPlaceholder}
        dateTimeParts={getDateTimeParts()}
        value={dateValue}
        onChange={handleDateValueChange}
        onBlur={handleBlur}
        helperTextPrefix="Recommended:"
        helperText="within 60 days from today"
        status={status}
        statusMessage={errorMessage}
        statusMessagePrefix={errorMessagePrefix}
        formatDateTimeForSR={formatDateTimeForSR}
        textMap={textMap}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={defaultCode} />

- `value`: Date value
- `onChange`: Callback when the display date value or date value has changed
  - Return displayValue as a text value containing any valid part of the date segment and date value
  - If date value is invalid, return _undefined_

The basic DateField example uses the `onChange` callback trigger to update the date value.

- Individual year values from 0 to 99 map to the years 1900 to 1999. All other values are the actual year. See the [example](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date#interpretation_of_two-digit_years).

```typescript
const handleDateValueChange = (value) => {
  if (value) {
    const newDate = new Date(value);
    // if Year is less than 100, Javascript will map it to 2001 or 1901
    // using setFullYear for any year less than 100 to work correctly
    newDate.setFullYear(value.getFullYear());
    setDateValue(newDate);
  } else {
    setDateValue(null);
  }
};
```

- Using the combination of `onBlur` and `onChange`'s displayValue to validate and update the `status` and `statusMessage` props.

```typescript
const handleDisplayTextChange = (value) => {
  setHasDisplayValue(!!value);
};
const handleBlur = () => {
  if (!dateValue && hasDisplayValue) {
    setStatus('error');
    setErrorMessage('Invalid Date');
  } else {
    setStatus('default');
  }
};
```

## Variations

DateField has different variations based on the combination of props being used.

export const variationsCode = `() => {
  const locale = 'en-US';
  const label = 'This is a label';
  const clearButtonAriaLabel = 'clear button';
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const formatDateTimeForSR = (value) => {
    return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
  };
  const textMap = {
    ariaLabel: 'This is an aria label',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    blank: 'blank',
    required: 'required',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
  };
  const defaultProps = {
    dateSegmentPlaceholder,
    dateTimeParts: getDateTimeParts(),
    formatDateTimeForSR,
    textMap,
  };
  const propsWithLabel = {
    ...defaultProps,
    label,
  };
  const dateValue = new Date();
  const styles = {
    row: {
      width: '55%',
      height: '120px',
      marginBottom: '1rem',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
    },
    headerText: {
      marginBlockEnd: '10px',
      width: '70%',
    },
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const HeaderText = ({ children }) => (
    <div style={styles.headerText}>
      <p className="evrBodyText">{children}</p>
    </div>
  );
  return (
    <div style={styles.column}>
      <Row>
        <HeaderText>DateField without label</HeaderText>
        <DateField {...defaultProps} id="datefield-1" />
      </Row>
      <Row>
        <HeaderText>DateField with label value</HeaderText>
        <DateField {...propsWithLabel} id="datefield-2" />
      </Row>
      <Row>
        <HeaderText>DateField with initial value</HeaderText>
        <DateField
          {...propsWithLabel}
          id="datefield-3"
          value={dateValue}
          textMap={{ ...textMap, clearButtonAriaLabel }}
        />
      </Row>
      <Row>
        <HeaderText>DateField as required field</HeaderText>
        <DateField {...propsWithLabel} id="datefield-4" required />
      </Row>
      <Row>
        <HeaderText>Disabled DateField</HeaderText>
        <DateField {...propsWithLabel} id="datefield-5" disabled />
      </Row>
      <Row>
        <HeaderText>Disabled DateField with initial value</HeaderText>
        <DateField {...propsWithLabel} id="datefield-6" value={dateValue} disabled iconName='calendar'/>
      </Row>
      <Row>
        <HeaderText>ReadOnly DateField</HeaderText>
        <DateField {...propsWithLabel} id="datefield-7" readOnly />
      </Row>
      <Row>
        <HeaderText>ReadOnly DateField with initial value</HeaderText>
        <DateField {...propsWithLabel} id="datefield-8" value={dateValue} readOnly />
      </Row>
      <Row>
        <HeaderText>ReadOnly DateField with initial value and icon</HeaderText>
        <DateField {...propsWithLabel} id="datefield-9" value={dateValue} readOnly iconName='calendar' />
      </Row>
      <Row>
        <HeaderText>DateField with helperText</HeaderText>
        <DateField {...propsWithLabel} id="datefield-10" helperText="This is a helper text" helperTextPrefix="Notice:" />
      </Row>
      <br />
      <Row>
        <HeaderText>DateField with an error message</HeaderText>
        <DateField
          {...propsWithLabel}
          id="datefield-11"
          status="error"
          statusMessage="This is an error message"
          statusMessagePrefix="Error:"
        />
      </Row>
    </div>
  );
}`;

<CodeExample scope={scope} code={variationsCode} />

## Clear DateField value

User can clear DateField value.

export const clearDateFieldCode = `() => {
  const locale = 'en-US';
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const formatDateTimeForSR = (value) => {
    return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
  };
  const textMap = {
    ariaLabel: 'Clear date field example',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    blank: 'blank',
    required: 'required',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
  };
  const dateFieldRef = React.useRef(null);
  const [dateValue, setDateValue] = React.useState(null);
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const handleDateValueChange = (displayValue, value) => {
    setHasDisplayValue(!!displayValue);
    if (value) {
      const newDate = new Date(value);
      // if Year is less than 100, it will map it to 1901 or 1999
      // using setFullYear for any year less than 100 to work correctly
      newDate.setFullYear(value.getFullYear());
      setDateValue(newDate);
    } else {
      setDateValue(null);
    }
  };
  const handleClearValue = () => {
    // useImperativeHandle to trigger clear()
    // with typescript: dateFieldRef.current && (dateFieldRef.current as IDateFieldMethods).clear();
    dateFieldRef.current && dateFieldRef.current.clear();
    setDateValue(null);
  };
  return (
    <div>
      <div style={{ marginBottom: '10px' }}>
        <DateField
          id="datefield-1"
          ref={dateFieldRef}
          dateSegmentPlaceholder={dateSegmentPlaceholder}
          dateTimeParts={getDateTimeParts()}
          formatDateTimeForSR={formatDateTimeForSR}
          value={dateValue}
          status={status}
          onChange={handleDateValueChange}
          textMap={textMap}
        />
      </div>
      <Button id="click-clear-date-field-btn" label="Click to clear DateField value" onClick={handleClearValue} />
    </div>
  );
}`;

<CodeExample scope={scope} code={clearDateFieldCode} />

## Focus DateField input

Click on the Button to set focus to DateField input.

export const focusDateFieldCode = `() => {
  const locale = 'en-US';
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const formatDateTimeForSR = (value) => {
    return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
  };
  const textMap = {
    ariaLabel: 'Focus date field example',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    blank: 'blank',
    required: 'required',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
  };
  const dateFieldRef = React.useRef(null);
  const [dateValue, setDateValue] = React.useState(null);
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const handleDateValueChange = (displayValue, value) => {
    setHasDisplayValue(!!displayValue);
    if (value) {
      const newDate = new Date(value);
      // if Year is less than 100, it will map it to 1901 or 1999
      // using setFullYear for any year less than 100 to work correctly
      newDate.setFullYear(value.getFullYear());
      setDateValue(newDate);
    } else {
      setDateValue(null);
    }
  };
  const handleFocusInput = () => {
    // useImperativeHandle to trigger focus()
    // with typescript: dateFieldRef.current && (dateFieldRef.current as IDateFieldMethods).focus();
    dateFieldRef.current && dateFieldRef.current.focus();
  };
  return (
    <div>
      <div style={{ marginBottom: '10px' }}>
        <DateField
          id="datefield-1"
          ref={dateFieldRef}
          dateSegmentPlaceholder={dateSegmentPlaceholder}
          dateTimeParts={getDateTimeParts()}
          formatDateTimeForSR={formatDateTimeForSR}
          value={dateValue}
          status={status}
          onChange={handleDateValueChange}
          textMap={textMap}
        />
      </div>
      <Button id="click-focus-date-field-btn" label="Click to focus DateField input" onClick={handleFocusInput} />
    </div>
  );
}`;

<CodeExample scope={scope} code={focusDateFieldCode} />

## Globalization

DateField is a controlled component and does not handle Globalization internally.

Globalization is handled by the following props:

- `dateTimeParts`: DateField segment parts object
- `dateSegmentPlaceholder`: Placeholder for the date input format
- `textMap`: Object containing localized text for various elements
- `formatDateTimeForSR`: Function to format how screen reader should announce the date

### Australia | "en-AU"

The date format in Australia is `dd/mm/yyyy`. The `dateSegmentPlaceholder` for day is `dd`, `mm` for month, and `yyyy` for year, which is the default and does not need to be overridden.

However, `dateTimeParts` needs to be customized for this locale to display the day first, followed by the month, then year.

```typescript
dateTimeParts: [
  { id: 'segment-day-1', type: 'day', value: '' },
  { id: 'segment-literal-1', type: 'literal', value: '/' },
  { id: 'segment-month-1', type: 'month', value: '' },
  { id: 'segment-literal-2', type: 'literal', value: '/' },
  { id: 'segment-year-1', type: 'year', value: '' },
];
```

export const australiaDateFieldCode = `() => {
  const styles = {
    container: {
      width: '260px',
    },
  };
  const locale = 'en-AU';
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const dateTimeParts = [
    { id: 'segment-day', type: 'day', value: '' },
    { id: 'segment-literal-1', type: 'literal', value: '/' },
    { id: 'segment-month-1', type: 'month', value: '' },
    { id: 'segment-literal-2', type: 'literal', value: '/' },
    { id: 'segment-year', type: 'year', value: '' },
  ];
  const formatDateTimeForSR = (value) => {
    return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
  };
  const textMap = {
    ariaLabel: 'This is an aria label',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    blank: 'blank',
    required: 'required',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
  };
  const [dateValue, setDateValue] = React.useState(null);
  const [status, setStatus] = React.useState('default');
  const [errorMessage, setErrorMessage] = React.useState('');
  const [errorMessagePrefix, setErrorMessagePrefix] = React.useState('');
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const handleDateValueChange = (displayValue, value) => {
    setHasDisplayValue(!!displayValue);
    if (value) {
      const newDate = new Date(value);
      // if Year is less than 100, it will map it to 1901 or 1999
      // using setFullYear for any year less than 100 to work correctly
      newDate.setFullYear(value.getFullYear());
      setDateValue(newDate);
    } else {
      setDateValue(null);
    }
  };
  const handleBlur = () => {
    if (!dateValue && hasDisplayValue) {
      setStatus('error');
      setErrorMessagePrefix('Error:');
      setErrorMessage('Please enter a valid date');
    } else {
      setStatus('default');
    }
  };
  return (
    <div style={styles.container}>
      <DateField
        id="datefield-au"
        label="Appointment date"
        required
        dateSegmentPlaceholder={dateSegmentPlaceholder}
        dateTimeParts={dateTimeParts}
        value={dateValue}
        onChange={handleDateValueChange}
        onBlur={handleBlur}
        helperTextPrefix="Recommended:"
        helperText="within 60 days from today"
        status={status}
        statusMessage={errorMessage}
        statusMessagePrefix={errorMessagePrefix}
        formatDateTimeForSR={formatDateTimeForSR}
        textMap={textMap}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={australiaDateFieldCode} />

### Chinese (Hong Kong) | "zh-HK"

Here's another example of a customized DateField locale.

DateField is designed to be flexible to use any globalization module. In this case, Javascript native `Intl` and `Intl.DateTimeFormat.formatToParts` objects are used to create the localized date segments for this locale.

Another option could be to utilize the Dayforce Globalization module (`@ceridianhcm/globalization`).

| Variable                     | en-US                     | zh-HK         |
| ---------------------------- | ------------------------- | ------------- |
| dateSegmentPlaceholder.day   | dd                        | 日            |
| dateSegmentPlaceholder.month | mm                        | 月            |
| dateSegmentPlaceholder.year  | yyyy                      | 年            |
| label                        | Appointment date          | 預約日期      |
| helperTextPrefix             | Recommended:              | 推薦:         |
| helperText                   | within 60 days from today | 60 天內       |
| statusMessage (error)        | Invalid Date              | 錯誤日期      |
| dayLabel                     | day                       | 日            |
| monthLabel                   | month                     | 月            |
| yearLabel                    | year                      | 年            |
| formatLabel                  | format                    | 格式          |
| expectedDateFormatLabel      | Expected Date Format:     | 預期日期格式: |
| blank                        | blank                     | 沒有數據      |
| required                     | required                  | 必需的        |
| invalidEntry                 | invalid entry             | 錯誤輸入      |
| invalidDay                   | invalid day               | 錯誤日        |
| invalidMonth                 | invalid month             | 錯誤月        |
| invalidYear                  | invalid year              | 錯誤年        |

export const chineseHongKongCode = `() => {
  const styles = {
    container: {
      width: '260px',
    },
  };
  const locale = 'zh-HK';
  /**
   *  using BCP 47 language code
   *      https://www.ietf.org/rfc/bcp/bcp47.txt
   *      https://www.techonthenet.com/js/language_tags.php
   * can override our own culture format if it is differ from Intl.DateTimeFormat and formatToParts
   */
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const formatDateTimeForSR = (value) => {
    return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
  };
  const [dateValue, setDateValue] = React.useState(null);
  const [status, setStatus] = React.useState('default');
  const [errorMessage, setErrorMessage] = React.useState('');
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const handleDateValueChange = (displayValue, value) => {
    setHasDisplayValue(!!displayValue);
    if (value) {
      const newDate = new Date(value);
      // if Year is less than 100, it will map it to 1901 or 1999
      // using setFullYear for any year less than 100 to work correctly
      newDate.setFullYear(value.getFullYear());
      setDateValue(newDate);
    } else {
      setDateValue(null);
    }
  };
  const handleBlur = () => {
    if (!dateValue && hasDisplayValue) {
      setStatus('error');
      setErrorMessage('錯誤日期');
    } else {
      setStatus('default');
    }
  };
  const dateSegmentPlaceholder = {
    day: '日',
    month: '月',
    year: '年',
  };
  const textMap = {
    ariaLabel: '这是一个ariaLabel',
    dayLabel: '日',
    monthLabel: '月',
    yearLabel: '年',
    formatLabel: '格式',
    expectedDateFormatLabel: '預期日期格式:',
    blank: '沒有數據',
    required: '必需的',
    invalidEntry: '錯誤輸入',
    invalidDay: '錯誤日',
    invalidMonth: '錯誤月',
    invalidYear: '錯誤年',
  };
  return (
    <div style={styles.container}>
      <DateField
        id="datefield-hk"
        label="預約日期"
        required
        dateSegmentPlaceholder={dateSegmentPlaceholder}
        dateTimeParts={getDateTimeParts()}
        value={dateValue}
        onChange={handleDateValueChange}
        onBlur={handleBlur}
        helperTextPrefix="推薦:"
        helperText="60 天內"
        status={status}
        statusMessage={errorMessage}
        formatDateTimeForSR={formatDateTimeForSR}
        textMap={textMap}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={chineseHongKongCode} />

### Hindi (India) | "hi-IN"

Here's another example of a customized DateField locale.

| Variable                     | en-US                     | hi-IN                    |
| ---------------------------- | ------------------------- | ------------------------ |
| dateSegmentPlaceholder.day   | dd                        | दिन                      |
| dateSegmentPlaceholder.month | mm                        | महीना                    |
| dateSegmentPlaceholder.year  | yyyy                      | वर्ष                     |
| label                        | Appointment date          | नियुक्ति तिथि            |
| helperTextPrefix             | Recommended:              | अनुशंसा:                 |
| helperText                   | within 60 days from today | 60 दिनों के भीतर         |
| statusMessage (error)        | Invalid Date              | गलत तारीख                |
| dayLabel                     | day                       | दिन                      |
| monthLabel                   | month                     | महीना                    |
| yearLabel                    | year                      | वर्ष                     |
| formatLabel                  | format                    | प्रारूप                  |
| expectedDateFormatLabel      | Expected Date Format:     | अपेक्षित दिनांक प्रारूप: |
| blank                        | blank                     | खाली                     |
| required                     | required                  | आवश्यक                   |
| invalidEntry                 | invalid entry             | अमान्य प्रविष्टि         |
| invalidDay                   | invalid day               | अमान्य दिन               |
| invalidMonth                 | invalid month             | अमान्य महीना             |
| invalidYear                  | invalid year              | अमान्य वर्ष              |

export const hindiCode = `() => {
  const styles = {
    container: {
      width: '260px',
    },
  };
  const locale = 'hi-IN';
  /**
   *  using BCP 47 language code
   *      https://www.ietf.org/rfc/bcp/bcp47.txt
   *      https://www.techonthenet.com/js/language_tags.php
   * can override our own culture format if it is differ from Intl.DateTimeFormat and formatToParts
   */
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const formatDateTimeForSR = (value) => {
    return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
  };
  const [dateValue, setDateValue] = React.useState(null);
  const [status, setStatus] = React.useState('default');
  const [errorMessage, setErrorMessage] = React.useState('');
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const handleDateValueChange = (displayValue, value) => {
    setHasDisplayValue(!!displayValue);
    if (value) {
      const newDate = new Date(value);
      // if Year is less than 100, it will map it to 1901 or 1999
      // using setFullYear for any year less than 100 to work correctly
      newDate.setFullYear(value.getFullYear());
      setDateValue(newDate);
    } else {
      setDateValue(null);
    }
  };
  const handleBlur = () => {
    if (!dateValue && hasDisplayValue) {
      setStatus('error');
      setErrorMessage('गलत तिथि');
    } else {
      setStatus('default');
    }
  };
  const dateSegmentPlaceholder = {
    day: 'दिन',
    month: 'महीना',
    year: 'वर्ष',
  };
  const textMap = {
    ariaLabel: 'यह एक एरिया लेबल है',
    dayLabel: 'दिन',
    monthLabel: 'महीना',
    yearLabel: 'वर्ष',
    formatLabel: 'प्रारूप',
    expectedDateFormatLabel: 'अपेक्षित दिनांक प्रारूप:',
    blank: 'खाली',
    required: 'आवश्यक',
    invalidEntry: 'अमान्य प्रविष्टि',
    invalidDay: 'अमान्य दिन',
    invalidMonth: 'अमान्य महीना',
    invalidYear: 'अमान्य वर्ष',
  };
  return (
    <div style={styles.container}>
      <DateField
        id="datefield-in"
        label="नियुक्ति तिथि"
        required
        dateSegmentPlaceholder={dateSegmentPlaceholder}
        dateTimeParts={getDateTimeParts()}
        value={dateValue}
        onChange={handleDateValueChange}
        onBlur={handleBlur}
        helperTextPrefix="अनुशंसा:"
        helperText="60 दिनों के भीतर"
        status={status}
        statusMessage={errorMessage}
        formatDateTimeForSR={formatDateTimeForSR}
        textMap={textMap}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={hindiCode} />

## How to Use

DateField is an input field and does not provide a calendar picker for selecting a date. Consider using DatePicker if you need a date input field and a calendar picker.

## Accessibility

DateField consists of different date part segments: day, month, and year.

The keyboard behavior for DateField should function the same as TextInput but with the following modifications:

- The `separator` (ex. "\") will be visible and auto-filled.
- Segment value is limited to the max value length of the specified date segment type:
  - For example, if the max length for the year segment is 4, you can't type in more than 4 numbers.
  - The cursor will jump to the next segment when it reaches the end of the current segment.
- If the segment value is not valid, an error message will be shown.

Keyboard key behavior:

- <kbd>Tab</kbd>, <kbd>Shift Tab</kbd>: Tabs to the next/previous focusable segment, all segments are tab stoppable.
- <kbd>Arrow Left</kbd>, <kbd>Arrow Right</kbd>: Moves the cursor within the DateField like a text input.
- <kbd>Date Separator</kbd>: Moves the cursor to the next segment. This key is variable and will depend on the provided
  locale. Common separator characters are `/`, `.`, and `-`.
- <kbd>Backspace</kbd>, <kbd>Delete</kbd>: Removes the character before/after the cursor, will move to the previous segment
  if the previous character is a separator.

### Screen Reader Accessibility

DateField consists of three parts: day, month, and year. Using these parts, the component will utilize the Javascript native `Intl` object to construct a screen reader message.

When DateField first receives focus, it should always announce the following:

- `ariaLabel` OR `label` (if no `ariaLabel` provided)
- `required`
- `expectedDateFormatLabel` and current localized date format (ex. "mm/dd/yyyy")
- Current localized `value` format when `value` is not null OR current `displayValue`
- If `value` and `displayValue` are both null -> Announce _blank_
- If `status` is error -> Announce "invalid entry"
- `helperTextPrefix` and `helperText`
- `statusMessagePrefix` and `statusMessage`

Using LiveAnnouncer's `aria-live` and DateSegment's `ariaLabel`
