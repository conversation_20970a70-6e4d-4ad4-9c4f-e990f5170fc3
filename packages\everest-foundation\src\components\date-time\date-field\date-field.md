# Date Field

## Summary

Research and document implementations for the Everest Date Field.

- Start Date: 2022-07-18
- Figma link: https://www.figma.com/file/0ZDD0G8lcpcEVaCbjjmxvD/%F0%9F%A7%AADatePicker?node-id=2%3A349

## Detailed Design

To maintain the customization of DateField/DatePicker, we decided to build it from scratch.

- Three-part date segments, but not smart data segment (not spinner)
  - It doesn't auto move the focus to the other segment, the only way to move it is either using tab/arrow/separator
  - It doesn't track the max or min value
  - It wouldn't force to only accept the correct date, it allows invalid dates, but prompts an error message under the datefield (with 2-5 seconds and onblur combinations)
- On the Input/focus stage, we only accept numeric as our input.
- Date format only applies for display purposes.
- It is a controlled component
- Only support day, month, and year, for now, any other date segment will be a future improvement
- placeholder to show the DateSegment type
- build our accessibility (keyboard and screen reader)
- incorporate Ceridian globalization (could be future implementation), reference [bcp47 language codes](https://www.ietf.org/rfc/bcp/bcp47.txt)
- for international date, using [JavaScript Internationalization API](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)

Updated behavior

- use the cursor and make the datefield behavior like a textField
- Can't highlight the whole text field
- Jump to the next field when the maxlenght has met on the current field

## API

1. **testId**: `undefined | string`  
   Sets **data-test-id** attribute on the html element.
1. **id**: `string`  
   Required. Sets **id** attribute on the html element.
1. **disabled**: `boolean`  
   Sets the `disabled` attribute on the _dateField_.
1. **required**: `boolean`  
   Sets the `required` attribute on the _dateField_. Adds the asterisk to the **label** / **placeholder**.
1. **label**: `undefined | string`  
   Optional user provided **label**. If not provided, no **label** is rendered. When **label** is set, automatically set `htmlFor` to _dateField_ **id**
1. **helperTextPrefix**: `undefined | string`
   Optional user provided prefix of helper Text.
1. **helperText**: `undefined | string`  
   Optional user provided help text. When set, _dateField_ **ariadescribedby** will be set to **helperText** **id**
1. **status**: `TDateFieldStatus`  
   Indicates the state of the _dateField_.
1. **statusMessagePrefix**: `undefined | string`
   Optional user provided prefix of status message.
1. **statusMessage**: `undefined | string`
   Optional user provided status message. When set, _dateField_ **ariadescribedby** will be set to **statusMessage** **id**, this will override **helperText**. This will be used only when **status** is set to "error" or "success".
1. **value**: `undefined | Date`  
   _dateField_ value
1. **ariaLabel**: `undefined | string`  
   `aria-label` for _dateField_, if set, this will be used, otherwise use **label**/**placeholder**
1. **onChange**: `undefined | (value: Date) => void`  
   Callback when _dateField_ value change
1. **onFocus**: `undefined | Function`  
   Optional callback on focus.
1. **onBlur**: `undefined | Function`  
   Optional callback on blur.

## Usage

DateField doesn't have a datepicker, it is a Form Field without the Calendar overlay. With Calendar Overlay, it is the DatePicker component

<DateField label="this is a label" value={Date.now()} />

## Prerequisite/Other Components

1. sub-components
   - DateSegment component would handle different date part
1. re-use the following existing components
   - StatusMessage
   - FocusRing
   - wouldn't be able to use SelectContainerTriggerArea because not all date field has a datepicker (overlay)
   - wouldn't be able to use TextField, dateField has 3 editable spans.

## Accessibility

- DateSegment (ContentEditable)

  - role='textbox'
  - tabIndex
  - aria-multiline=false
  - aria-label
  - utilizing date-segment aria-label to handle the screen reader announcer, the dateFieldAriaLabelValue would be generated on the dateField component and pass it down to the date-segment, and control to be announced for the first focus.

- aria-live (using liveAnnouncer component)

  - When the value has changed, using aria-live to announce the updated value

- using javascript native Intl object to construct date format and date parts based on different cultures/localization for the Screen reader.

Reference:

[React Aria useDateField](https://codesandbox.io/s/small-water-y5dgj4?file=/src/DateField.js) as reference.

Combine with aria-live and [Intl.DateTimeFormat](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat)

[W3C Date Picker Dialog Example](https://www.w3.org/WAI/ARIA/apg/example-index/dialog-modal/datepicker-dialog)

## Alternatives/Trade-Offs

[React Aria useDateField](https://react-spectrum.adobe.com/react-aria/useDateField.html)

- Benefits
  - Customizable styles
  - Build-in Accessibility (keyboard and screen reader)
  - Battle-tested, with a lot of real-life use cases
  - Different Date Segments are a plus
  - Open source and we can access all source code for any diagnosis or bug fix
  - Create a working DateField/DatePicker with minimum effort and it has fully supported a11y and localization
- Trade-off
  - Keyboard accessibility is not customizable, and it doesn't align with Everest Design System.
    - For Design, it is a bad practice to automatically override the user's input. React Aria only allow valid input which required automatically updating the user's input. This introduces a bad user experience. for example. try to input 2/29/2020 on [React Aria Example](https://codesandbox.io/s/small-water-y5dgj4?file=/src/DateField.js). We would get a 2/9/2020 as the return value which is not intended by the user.
  - To update React Aria to the desired keyboard a11y, we have to fork the useDateFieldState and possibly some other hooks.
  - Globalization is baked in and there is no easy way to replace it with our modules. We could wrap it to match the Everest Localization module, but we are at the mercy of React Aria's implementation. There are hardcoded culture placeholder, if we wanted to replace those, we would have to fork the useDateFieldState and some other hooks.
  - Any changes or updates which not supported by React Aria can take a lot longer to implement.
  - React Aria has label or error messages which are not required for Everest and we have to override

[Airbnb React Date](https://airbnb.io/projects/react-dates/)

- Benefit
  - One of the older React libraries for datepicker.
  - Localizable and mobile friendly
- Trade-off
  - It relies on Moment.js which is deprecated, [Moment.js Project Status](https://momentjs.com/docs/#/-project-status/)
  - It doesn't have good documentation

[MUI X Date/Time pickers](https://mui.com/x/react-date-pickers/getting-started/)

- Benefit
  - Localizable and mobile friendly
  - a11y should be supported (not yet tested on safari and voiceOver)
  - Ability to switch different date-library to manage the date manipulation
- Trade-off
  - Commercial pro version for advanced features (Date Range)

## Q&A

1. Do we support different types of Calendars?

   - No, only Gregorian

1. Are three parts the way to go?

   - Yes, and allow invalid data, prompts with an invalid error message
   - Disabled autofocus from date segment, only trigger by arrow/tab/separator

1. Do we support Prefix and Suffix?

   - No

1. Do we support Prefix and Suffix icons?

   - Not Prefix, Suffix icons including (eye, calendar, etc)

1. Do we support x button?

   - no, use the backspace or delete button to remove the date

1. This is for future implementation, is clicking on the calendar button the only way to open the calendar?

   - yes, any other behavior is considered future improvement, We don't consider Calendar or DatePicker for DateField component

1. Why is only the last date segment text-aligned at start?

   - This is to ensure users can enter what they want in the previous segments, which are text-align centered, and the cursor does not jump around when entering input in previous segments.

## Future Considerations

1. Month-only DateField
1. Year only DateField
1. Quarter-only DateField
1. Week number only DateField
1. Calendar-related styles
   - start day of the week
   - Calendar style
   - Month-only Calendar
   - Year-only Calendar
1. date format when not in focus? React Aria has useDateFormatter to update the display format.
1. different Calendars
1. Smart date segment focus

## Other Design Systems

**Material UI** - https://mui.com/x/react-date-pickers/getting-started/

- support one text field and the placeholder would disappear when started to type.
- support mask and input format
- props - we can reference mui props for our datefield. https://mui.com/x/api/date-pickers/date-picker/

**Carbon Design System** - https://carbondesignsystem.com/components/date-picker/code/

- using a single text field
- Calendar open with mouse focus

**React Aria** - https://react-spectrum.adobe.com/react-aria/useDateField.html

- use different date segment
- customizable
- fully support accessibility

**Ant Design** - https://ant.design/components/date-picker/

- support date/week/month/quarter/year only picker
- allowClear API (x button)
- mouse focused will open the datepicker

**Shopify Polaris** - https://polaris.shopify.com/components/date-picker

- Calendar only picker, it doesn't have datefield associated with it
- Their recommendation for displaying datepicker associated with datefield is using a button to trigger the popover(contain calendar), and not when the text input gets focused.

**Microsoft Fluent UI** - https://developer.microsoft.com/en-us/fluentui#/controls/web/datepicker

- support the first day of the week
- the datepicker itself has the function to go to different years/months more in line with what Everest Design.
- Mouse click would open the datepicker
- Have Fri as the prefix, it is editable, but edited wouldn't update the value, only changing the Month/Day/Year update the date.

## Required PBIs

1. Create DateField https://ceridianpt.atlassian.net/browse/EVR-1342
   - styles
   - focus ring
   - support for dateFormat (dd/MMM/yyyy)
1. DateField Screen reader accessibility https://ceridianpt.atlassian.net/browse/EVR-1344
1. Automation Test for DateField https://ceridianpt.atlassian.net/browse/EVR-1343
   - Keyboard a11y (Playwright)
   - Screen Reader test (manual)
   - Unit Test
   - styles test (Appitool)
1. DateField style https://ceridianpt.atlassian.net/browse/EVR-1352
1. DateField cursor instead of highlighted the whole segment https://ceridianpt.atlassian.net/browse/EVR-1353
1. DateFormat implementation https://ceridianpt.atlassian.net/browse/EVR-1345
   - [Intl.DateTimeFormat.prototype.resolvedOptions()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/resolvedOptions)
1. DateField Globalization/Localization https://ceridianpt.atlassian.net/browse/EVR-1346
1. DatePicker arch https://ceridianpt.atlassian.net/browse/EVR-1347

## Acceptance Criteria

## Changelog

03/11/25 - Moving from alpha to ready status
