import { <PERSON><PERSON>, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { <PERSON><PERSON> } from '../../button';
import { DateField } from '../../date-time';
import { screen, userEvent, waitFor } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../../chromatic';

export const testId = 'datefield-test-id';
export const longLabel =
  'This is a very long label. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.';
export const helperText = 'This is some helper text';
export const helperTextPrefix = 'Hint:';
export const statusMessage = 'This is a status message';
export const statusMessagePrefix = 'Prefix:';
export const defaultDateValue = new Date(2022, 11, 31); // Dec 31, 2022
export const dateTimeParts = [
  { id: 'segment-month-1', type: 'month', value: '' },
  { id: 'segment-literal-1', type: 'literal', value: '/' },
  { id: 'segment-day-1', type: 'day', value: '' },
  { id: 'segment-literal-2', type: 'literal', value: '/' },
  { id: 'segment-year-1', type: 'year', value: '' },
];
export const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};
export const textMap = {
  ariaLabel: 'This is an aria label',
  clearButtonAriaLabel: 'clear button aria label',
  iconAriaLabel: 'icon aria label',
  dayLabel: 'day',
  monthLabel: 'month',
  yearLabel: 'year',
  formatLabel: 'format',
  expectedDateFormatLabel: 'Expected Date Format:',
  blank: 'blank',
  required: 'required',
  invalidEntry: 'invalid entry',
  invalidDay: 'invalid day',
  invalidMonth: 'invalid month',
  invalidYear: 'invalid year',
};
export const formatDateTimeForSR = () => {};

export const getDateFieldByTestId = () => screen.getByTestId(testId);

<Meta
  title="Testing/Automation Test Cases/Date Field"
  component={DateField}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'datefield-1',
    testId,
    label: 'This is a label',
    dateTimeParts,
    dateSegmentPlaceholder,
    textMap,
    formatDateTimeForSR,
  }}
/>

# Date Field

## Live Demo

<Canvas>
  <Story name="Default">{(args) => <DateField {...args} label={''}></DateField>}</Story>
</Canvas>

<Canvas>
  <Story name="DateField with label">{(args) => <DateField {...args}></DateField>}</Story>
</Canvas>

<Canvas>
  <Story name="DateField with long label">{(args) => <DateField {...args} label={longLabel}></DateField>}</Story>
</Canvas>

<Canvas>
  <Story name="DateField with value">{(args) => <DateField {...args} value={defaultDateValue}></DateField>}</Story>
</Canvas>

<Canvas>
  <Story name="DateField as Required field">{(args) => <DateField {...args} required></DateField>}</Story>
</Canvas>

<Canvas>
  <Story name="DateField as Required field with value">
    {(args) => <DateField {...args} required value={defaultDateValue}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateField">{(args) => <DateField {...args} disabled></DateField>}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateField with value">
    {(args) => <DateField {...args} disabled value={defaultDateValue}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateField with value and helper text">
    {(args) => <DateField {...args} disabled value={defaultDateValue} helperText={helperText}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateField with value and required field">
    {(args) => <DateField {...args} disabled required value={defaultDateValue}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateField">{(args) => <DateField {...args} readOnly></DateField>}</Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateField with value">
    {(args) => <DateField {...args} readOnly value={defaultDateValue}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateField with value and helper text">
    {(args) => <DateField {...args} readOnly value={defaultDateValue} helperText={helperText}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateField with value and required field">
    {(args) => <DateField {...args} readOnly required value={defaultDateValue}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateField with helper text">
    {(args) => <DateField {...args} helperText={helperText} helperTextPrefix={helperTextPrefix}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateField with error message">
    {(args) => (
      <DateField
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></DateField>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateField with success message">
    {(args) => (
      <DateField
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></DateField>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateField with invalid month and day field"
    play={async () => {
      await waitFor(() => {
        userEvent.click(getDateFieldByTestId());
        userEvent.type(getDateFieldByTestId(), '1588');
      });
    }}
  >
    {(args) => <DateField {...args}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateField with icon">{(args) => <DateField {...args} iconName={'calendar'}></DateField>}</Story>
</Canvas>

<Canvas>
  <Story
    name="DateField with clear button"
    play={async () => {
      await waitFor(() => {
        userEvent.click(getDateFieldByTestId());
        userEvent.type(getDateFieldByTestId(), '1122');
      });
    }}
  >
    {(args) => <DateField {...args} onClear={() => {}}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateField with icon and clear button"
    play={async () => {
      await waitFor(() => {
        userEvent.click(getDateFieldByTestId());
        userEvent.type(getDateFieldByTestId(), '1122');
      });
    }}
  >
    {(args) => <DateField {...args} iconName={'calendar'} onClear={() => {}}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateField with Focus Ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <DateField {...args}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateField with Focus Ring and value"
    play={async () => {
      await waitFor(() => {
        getDateFieldByTestId();
        userEvent.tab();
      });
    }}
  >
    {(args) => <DateField {...args} value={defaultDateValue}></DateField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Handling State and Events DateField">
    {(args) => {
      const dateFieldRef = React.useRef(null);
      const [dateValue, setDateValue] = React.useState(null);
      const [status, setStatus] = React.useState('default');
      const [errorMessage, setErrorMessage] = React.useState('');
      const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
      const handleDateValueChange = (displayValue, value) => {
        setHasDisplayValue(!!displayValue);
        if (value) {
          const newDate = new Date(value);
          // if Year is less than 100, it will map it to 1901 or 1999
          // using setFullYear for any year less than 100 to work correctly
          newDate.setFullYear(value.getFullYear());
          setDateValue(newDate);
        } else {
          setDateValue(null);
        }
      };
      const handleBlur = (e) => {
        if (!dateValue && hasDisplayValue) {
          setStatus('error');
          setErrorMessage('This is a status message');
        } else {
          setStatus('default');
        }
      };
      const handleClear = () => {
        dateFieldRef.current && dateFieldRef.current.clear();
        setDateValue(null);
      };
      const handleUpdateClick = () => {
        setDateValue(new Date());
      };
      return (
        <>
          <DateField
            {...args}
            ref={dateFieldRef}
            value={dateValue}
            status={status}
            statusMessage={errorMessage}
            onBlur={handleBlur}
            onChange={handleDateValueChange}
            onClear={handleClear}
          />
          <Button label="Update" onClick={handleUpdateClick} />
        </>
      );
    }}
  </Story>
</Canvas>
