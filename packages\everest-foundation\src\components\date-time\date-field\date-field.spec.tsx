import React, { createRef, forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { act } from '@testing-library/react-hooks';
import userEvent from '@testing-library/user-event';

import { DateField, IDateFieldMethods } from './date-field';
import { generateId } from '../../../utils';
import { Button } from '../../button';
import { Combobox } from '../../combobox';
import { TIconName } from '../../icon';
import { IDateTimeFormatPart } from '../date-field-helpers';

const locale = 'en-US';
const testId = 'datefield-test-id';
const label = 'This is a label';
const id = 'datefield-1';
const iconAriaLabel = 'icon label';
const clearButtonAriaLabel = 'Clear input';
const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};
const initialDay = '31';
const initialMonth = '12';
const initialYear = '2022';
const nextDay = '1';
const nextMonth = '1';
const nextYear = '2023';
const dayPlaceholder = 'dd';
const monthPlaceholder = 'mm';
const yearPlaceholder = 'yyyy';
const initialDate = new Date(parseInt(initialYear), parseInt(initialMonth) - 1, parseInt(initialDay));
const nextDate = new Date(parseInt(nextYear), parseInt(nextMonth) - 1, parseInt(nextDay));
const helperText = 'This is a helper';
const statusMessage = 'This is a status message';
const statusMessagePrefix = 'Prefix:';
const onFocus = jest.fn();
const onBlur = jest.fn();
const onChange = jest.fn();
const onClear = jest.fn();
const onIconKeyDown = jest.fn();
const onIconClick = jest.fn();
const iconName = 'calendar';
const iconId = `${id}-calendar-icon-button`;
const textMap = {
  ariaLabel: 'This is an aria label',
  dayLabel: 'dayLabel',
  monthLabel: 'monthLabel',
  yearLabel: 'yearLabel',
  formatLabel: 'format',
  expectedDateFormatLabel: 'Expected Date Format:',
  blank: 'blank',
  required: 'required',
  invalidEntry: 'invalid entry',
  invalidDay: 'invalid day',
  invalidMonth: 'invalid month',
  invalidYear: 'invalid year',
  clearButtonAriaLabel: clearButtonAriaLabel,
};
const formatDateTimeForSR = jest.fn();

const getLabel = () => screen.getByText(label);
const getDateField = () => screen.getByTestId(`${testId}`);
const getYearSegment = () => screen.getByRole('textbox', { name: /yearLabel/i });
const getDaySegment = () => screen.getByRole('textbox', { name: /dayLabel/i });
const getMonthSegment = () => screen.getByRole('textbox', { name: /monthLabel/i });
const getClearButton = () => screen.getByRole('button', { name: clearButtonAriaLabel });
const getIconButton = () => screen.getByRole('button', { name: iconAriaLabel });
const queryIconButton = () => screen.queryByRole('button', { name: iconAriaLabel });
const queryClearButton = () => screen.queryByRole('button', { name: clearButtonAriaLabel });
const getCombobox = () => screen.getByRole('combobox');

//This is to fix "TypeError: window.ResizeObserver is not a constructor" for dropdown
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

const dateTimeParts = () => {
  const dateTimeParts: IDateTimeFormatPart[] = [];
  const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
  for (const part of parts) {
    dateTimeParts.push({ ...part, id: generateId() });
  }
  return dateTimeParts;
};

const propsWithoutLabel = {
  dateSegmentPlaceholder,
  dateTimeParts: dateTimeParts(),
  testId,
  id,
  textMap,
  formatDateTimeForSR,
};

const defaultProps = {
  ...propsWithoutLabel,
  label,
};

const variants = {
  dateFieldDefault: {
    name: 'Default DateField',
    jsx: <DateField {...defaultProps}></DateField>,
  },
  dateFieldWithoutLabel: {
    name: 'DateField without label',
    jsx: <DateField {...propsWithoutLabel}></DateField>,
  },
  dateFieldWithIntialDateValue: {
    name: 'DateField with initial date value',
    jsx: <DateField {...defaultProps} value={initialDate}></DateField>,
  },
  dateFieldWithHelperText: {
    name: 'DateField with helper text',
    jsx: <DateField {...defaultProps} helperText={helperText}></DateField>,
  },
  dateFieldWithSuccessMessage: {
    name: 'DateField with success message',
    jsx: (
      <DateField
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></DateField>
    ),
  },
  dateFieldWithErrorMessage: {
    name: 'DateField with error message',
    jsx: (
      <DateField
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></DateField>
    ),
  },
  dateFieldDisabled: {
    name: 'Disabled DateField',
    jsx: <DateField {...defaultProps} disabled></DateField>,
  },
  dateFieldReadOnly: {
    name: 'ReadOnly DateField',
    jsx: <DateField {...defaultProps} readOnly></DateField>,
  },
  dateFieldWithOnBlurAndFocus: {
    name: 'DateField with onBlur and onFocus',
    jsx: <DateField {...defaultProps} onBlur={onBlur} onFocus={onFocus}></DateField>,
  },
  dateFieldWithOnFocus: {
    name: 'DateField with onFocus',
    jsx: <DateField {...defaultProps} onFocus={onFocus}></DateField>,
  },
  dateFieldWithOnBlur: {
    name: 'DateField with onBlur',
    jsx: <DateField {...defaultProps} onBlur={onBlur}></DateField>,
  },
  dateFieldWithOnChange: {
    name: 'DateField with onChange',
    jsx: <DateField {...defaultProps} onChange={onChange}></DateField>,
  },
};

interface IDateFieldWrapperRef {
  rerender: (newInitialValue?: Date) => void;
}

interface IDateFieldWrapperProps {
  initialValue?: Date;
}

const DateFieldWrapper = forwardRef<IDateFieldWrapperRef, IDateFieldWrapperProps>(({ initialValue }, ref) => {
  const dateFieldRef = useRef<IDateFieldMethods>(null);
  const [value, setValue] = useState<Date | undefined>(initialValue);

  const onValueChange = (displayValue?: string, newValue?: Date) => {
    onChange();
    if (newValue) {
      const newDate = new Date(newValue);
      // if Year is less than 100, it will map it to 1901 or 1999
      // using setFullYear for any year less than 100 to work correctly
      newDate.setFullYear(newValue.getFullYear());
      setValue(newDate);
    } else {
      setValue(undefined);
    }
  };

  const onValueClear = () => {
    onClear();
    dateFieldRef.current?.clear();
    setValue(undefined);
  };

  //Related to 'setting state directly' describe block
  //Used to manually trigger a rerender to verify that direct changes to the value state, made outside of user interaction, are reflected in the date field ui
  useImperativeHandle(ref, () => ({
    rerender(newInitialValue?: Date) {
      setValue(newInitialValue);
    },
  }));

  return (
    <>
      <Button id="clear-value-button" label="Clear value" onClick={onValueClear} />
      <DateField {...defaultProps} ref={dateFieldRef} value={value} onChange={onValueChange} onClear={onValueClear} />
    </>
  );
});
DateFieldWrapper.displayName = 'DateFieldWrapper';

const renderControlledDateField = (initialValue?: Date) => {
  const dateFieldWrapperRef = createRef<IDateFieldWrapperRef>();
  render(<DateFieldWrapper ref={dateFieldWrapperRef} initialValue={initialValue} />);
  return dateFieldWrapperRef.current;
};

describe('[DateField]', () => {
  it('should show label', () => {
    render(variants.dateFieldDefault.jsx);
    expect(getDateField()).toBeInTheDocument();
    expect(getLabel()).toBeInTheDocument();
  });

  it('should render without label', () => {
    render(variants.dateFieldWithoutLabel.jsx);
    expect(getDateField()).toBeInTheDocument();
  });

  it('should render segments placeholders', () => {
    render(variants.dateFieldDefault.jsx);
    expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
    expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
    expect(getDaySegment()).toHaveTextContent(dayPlaceholder);
  });

  it('should render initial date value', () => {
    render(variants.dateFieldWithIntialDateValue.jsx);
    expect(getYearSegment()).toHaveTextContent(initialYear);
    expect(getMonthSegment()).toHaveTextContent(initialMonth);
    expect(getDaySegment()).toHaveTextContent(initialDay);
    expect(screen.getByText(label)).toBeInTheDocument();
  });

  it('should render helper text when provided', () => {
    render(variants.dateFieldWithHelperText.jsx);
    expect(screen.getByText(helperText)).toBeInTheDocument();
  });

  it('should render status message in the error state when provided', () => {
    render(variants.dateFieldWithErrorMessage.jsx);
    expect(screen.getByText(statusMessage)).toBeInTheDocument();
  });

  it('should render status message in the success state when provided', () => {
    render(variants.dateFieldWithSuccessMessage.jsx);
    expect(screen.getByText(statusMessage)).toBeInTheDocument();
  });

  it('should set contenteditable false on all editable segments when disabled', () => {
    render(variants.dateFieldDisabled.jsx);
    expect(getYearSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getMonthSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getDaySegment()).toHaveAttribute('contenteditable', 'false');
  });

  it('should set contenteditable false on all editable segments when readOnly', () => {
    render(variants.dateFieldReadOnly.jsx);
    expect(getYearSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getMonthSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getDaySegment()).toHaveAttribute('contenteditable', 'false');
  });

  it('should display clear button when specified', () => {
    render(<DateField {...defaultProps} value={initialDate} onClear={onClear} />);
    expect(queryClearButton()).toBeInTheDocument();
  });

  it('should have a clear button aria-label when specified', () => {
    render(
      <DateField
        {...defaultProps}
        value={initialDate}
        onClear={onClear}
        textMap={{ ...textMap, clearButtonAriaLabel }}
      />
    );
    expect(queryClearButton()).toHaveAttribute('aria-label', clearButtonAriaLabel);
  });

  it('should display an icon when specified', () => {
    render(<DateField {...defaultProps} iconName={iconName} textMap={{ ...textMap, iconAriaLabel }} />);
    expect(getIconButton()).toBeInTheDocument();
  });

  it('should have an icon aria-label when specified', () => {
    render(<DateField {...defaultProps} iconName={iconName} textMap={{ ...textMap, iconAriaLabel }} />);
    expect(getIconButton()).toHaveAttribute('aria-label', iconAriaLabel);
  });

  it('should not display an icon when not specified', () => {
    render(<DateField {...defaultProps} />);
    expect(queryIconButton()).not.toBeInTheDocument();
  });

  it('should call onIconClick when the icon is clicked', async () => {
    onIconClick.mockClear();
    render(
      <DateField
        {...defaultProps}
        iconName={iconName}
        onIconClick={onIconClick}
        textMap={{ ...textMap, iconAriaLabel }}
      />
    );

    await act(async () => await userEvent.click(getIconButton()));
    expect(onIconClick).toHaveBeenCalledTimes(1);
  });

  it('should have a tabbable icon when onIconKeyDown is defined', async () => {
    onIconKeyDown.mockClear();
    render(<DateField {...defaultProps} iconName={iconName} onIconKeyDown={onIconKeyDown} />);

    await act(async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
    });

    expect(document.activeElement?.id).toEqual(iconId);
  });

  it('should call onIconKeyDown when defined and the icon is focused and a key is pressed', async () => {
    onIconKeyDown.mockClear();
    render(<DateField {...defaultProps} iconName={iconName} onIconKeyDown={onIconKeyDown} />);

    await act(async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard(' ');
    });

    expect(onIconKeyDown).toHaveBeenCalledTimes(1);
  });

  it('should pass focus to first DateSegment (month) when label is clicked on', async () => {
    render(<DateField {...defaultProps} iconName={iconName} onIconKeyDown={onIconKeyDown} />);
    expect(getMonthSegment()).not.toHaveFocus();
    await userEvent.click(getLabel());
    expect(getMonthSegment()).toHaveFocus();
  });

  describe('onFocus and onBlur event', () => {
    beforeEach(onFocus.mockReset);
    beforeEach(onBlur.mockReset);

    it('dispatch onFocus when dateField has focus', async () => {
      render(variants.dateFieldWithOnBlurAndFocus.jsx);

      await act(async () => await userEvent.tab());
      await waitFor(() => {
        expect(onFocus).toHaveBeenCalledTimes(1);
        expect(onBlur).toHaveBeenCalledTimes(0);
      });
    });

    it('dispatch onFocus once when dateField has focus and tab to next segment', async () => {
      render(variants.dateFieldWithOnBlurAndFocus.jsx);

      await act(async () => {
        await userEvent.tab();
        await userEvent.tab();
        await userEvent.tab();
      });
      await waitFor(() => {
        expect(onFocus).toHaveBeenCalledTimes(1);
        expect(onBlur).toHaveBeenCalledTimes(0);
      });
    });

    it('dispatch onBlur once when tab out of datefield', async () => {
      render(
        <>
          {variants.dateFieldWithOnBlur.jsx}
          <button>Test</button>
        </>
      );

      await act(async () => {
        await userEvent.tab();
        await userEvent.tab();
        await userEvent.tab();
        await userEvent.tab();
      });

      await waitFor(() => {
        expect(onBlur).toHaveBeenCalledTimes(1);
      });
    });

    it('dispatch onBlur once when clicked out of datefield and clicked on combobox', async () => {
      const optionsWithIcons = [
        { title: 'First Option', iconName: 'heartOutline' as TIconName, id: 'item7' },
        { title: 'Second Option', iconName: 'home' as TIconName, id: 'item8' },
        { title: 'Third Option', iconName: 'help' as TIconName, id: 'item9' },
      ];
      render(
        <>
          {variants.dateFieldWithOnBlur.jsx}
          <Combobox id={'combobox2'} label="Departments" options={optionsWithIcons} noResultsText="No results" />
        </>
      );

      await act(async () => {
        await userEvent.click(getMonthSegment());
        await userEvent.click(getCombobox());
      });

      await waitFor(() => {
        expect(onBlur).toHaveBeenCalledTimes(1);
        expect(getCombobox()).toHaveFocus();
      });
    });
  });

  /**
   * JSDom doesn't fully support contenteditable element https://github.com/jsdom/jsdom/issues/1670
   * It is out of scope for React Testing Library https://github.com/testing-library/user-event/issues/230
   * Workaround to trigger onInput event https://github.com/testing-library/dom-testing-library/pull/235   *
   */
  describe('onChange event', () => {
    beforeEach(onChange.mockReset);

    it('dispatch onChange with no value when only month segment has updated', async () => {
      render(variants.dateFieldWithOnChange.jsx);

      getMonthSegment().innerText = initialMonth;
      getMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith('12/dd/yyyy', undefined);
      });
    });

    it('dispatch onChange with no value when only day segment has updated', async () => {
      render(variants.dateFieldWithOnChange.jsx);

      getDaySegment().innerText = initialDay;
      getDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getDaySegment()).toHaveTextContent(initialDay);
      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith('mm/31/yyyy', undefined);
      });
    });

    it('dispatch onChange with no value when only year segment has updated', async () => {
      render(variants.dateFieldWithOnChange.jsx);

      getYearSegment().innerText = initialYear;
      getYearSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getYearSegment()).toHaveTextContent(initialYear);
      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith('mm/dd/2022', undefined);
      });
    });

    it('dispatch onChange with empty value when month segment is not digits', async () => {
      render(variants.dateFieldWithOnChange.jsx);

      getMonthSegment().innerText = 'ab';
      getMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith('', undefined);
      });
    });

    it('dispatch onChange with Date value when full date has updated', async () => {
      render(variants.dateFieldWithOnChange.jsx);

      getMonthSegment().innerText = initialMonth;
      getMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getDaySegment().innerText = initialDay;
      getDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getYearSegment().innerText = initialYear;
      getYearSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith('12/31/2022', initialDate);
      });
    });
  });

  //These tests are related to this issue: https://dayforce.atlassian.net/browse/PWEB-11523
  describe('setting state directly', () => {
    it('should update Date value', async () => {
      const dateFieldWrapperRef = renderControlledDateField();

      expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getDaySegment()).toHaveTextContent(dayPlaceholder);

      // set value
      await waitFor(() => {
        dateFieldWrapperRef?.rerender(initialDate);
      });

      await waitFor(() => {
        expect(getYearSegment()).toHaveTextContent(initialYear);
        expect(getMonthSegment()).toHaveTextContent(initialMonth);
        expect(getDaySegment()).toHaveTextContent(initialDay);
      });
    });

    it('should update Date value consecutively and correctly', async () => {
      const dateFieldWrapperRef = renderControlledDateField();

      expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getDaySegment()).toHaveTextContent(dayPlaceholder);

      // set value
      await waitFor(() => {
        dateFieldWrapperRef?.rerender(initialDate);
      });
      // set value again consecutively
      await waitFor(() => {
        dateFieldWrapperRef?.rerender(nextDate);
      });

      await waitFor(() => {
        expect(getYearSegment()).toHaveTextContent(nextYear);
        expect(getMonthSegment()).toHaveTextContent(nextMonth);
        expect(getDaySegment()).toHaveTextContent(nextDay);
      });
    });

    it('should update Date value consecutively and correctly after value is typed in', async () => {
      const dateFieldWrapperRef = renderControlledDateField();
      expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getDaySegment()).toHaveTextContent(dayPlaceholder);

      // manually type in value
      await act(async () => {
        await userEvent.type(getDaySegment(), initialDay, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
        await userEvent.type(getMonthSegment(), initialMonth, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
        await userEvent.type(getYearSegment(), initialYear, {
          initialSelectionStart: 0,
          initialSelectionEnd: 4,
        });
      });

      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      // set value directly
      await waitFor(() => {
        dateFieldWrapperRef?.rerender(nextDate);
      });

      await waitFor(() => {
        expect(getYearSegment()).toHaveTextContent(nextYear);
        expect(getMonthSegment()).toHaveTextContent(nextMonth);
        expect(getDaySegment()).toHaveTextContent(nextDay);
      });
    });

    it('should update Date value when year segment digits deleted', async () => {
      renderControlledDateField(initialDate);
      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);

      // delete year only
      initialYear.split('').forEach(async () => {
        await userEvent.type(getYearSegment(), '{backspace}');
      });

      await waitFor(() => {
        // year placeholder should be shown
        expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getMonthSegment()).toHaveTextContent(initialMonth);
        expect(getDaySegment()).toHaveTextContent(initialDay);
      });
    });
  });

  describe('clear button functionality', () => {
    beforeEach(onClear.mockReset);

    it('should trigger onClear if valid Date value and display value present', async () => {
      renderControlledDateField(initialDate);
      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      // Trigger onClear button
      await act(async () => await userEvent.click(getClearButton()));

      await waitFor(() => {
        expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(onClear).toHaveBeenCalledTimes(1);
      });

      expect(queryClearButton()).not.toBeInTheDocument();
    });

    it('should trigger onClear input if display value present but Date value is invalid', async () => {
      renderControlledDateField(initialDate);
      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(getMonthSegment());
        await userEvent.keyboard('{Backspace}{Backspace}');
      });

      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      // Trigger onClear button
      await act(async () => await userEvent.click(getClearButton()));

      await waitFor(() => {
        expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(onClear).toHaveBeenCalledTimes(1);
      });

      expect(queryClearButton()).not.toBeInTheDocument();
    });

    it('should clear input if a null or undefined value is passed in using an external clear button', async () => {
      renderControlledDateField(initialDate);
      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      const externalClearButton = document.getElementById('clear-value-button');
      expect(externalClearButton).toBeInTheDocument();
      await act(async () => await userEvent.click(externalClearButton as HTMLElement));

      await waitFor(() => {
        expect(getYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(onClear).toHaveBeenCalledTimes(1);
      });

      expect(queryClearButton()).not.toBeInTheDocument();
    });
  });

  describe('keyboard behavior', () => {
    it('should accept a value change for year segment', async () => {
      render(variants.dateFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getYearSegment(), initialYear, {
          initialSelectionStart: 0,
          initialSelectionEnd: 4,
        });
      });

      expect(getYearSegment()).toHaveTextContent(initialYear);
    });

    it('should accept a value change for month segment', async () => {
      render(variants.dateFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getMonthSegment(), initialMonth, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
      });

      expect(getMonthSegment()).toHaveTextContent(initialMonth);
    });

    it('should accept a value change for day segment', async () => {
      render(variants.dateFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getDaySegment(), initialDay, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
      });

      expect(getDaySegment()).toHaveTextContent(initialDay);
    });
  });
});
