import { useState } from 'react';
import { <PERSON>a, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';
import { DateField } from './date-field';
import Examples from './date-field.examples.mdx';
import { ICON_NAMES } from '../../icon';

<Meta
  title="Components/Text Fields/DateField"
  component={DateField}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/0ZDD0G8lcpcEVaCbjjmxvD/branch/WQKAlaU2yr1OAL0Zs22reC/%F0%9F%A7%AADatePicker?node-id=2%3A349&t=LAwoK3L51vnjT35Z-0',
    },
  }}
  argTypes={{
    dateTimeParts: {
      type: 'object',
      control: 'object',
      description: 'DateField segment parts object.',
    },
    label: {
      type: 'string',
      control: 'text',
      description: 'Specifies the label rendered in the date field.',
    },
    iconName: {
      type: { name: 'enum' },
      control: 'select',
      options: ['', ...ICON_NAMES].sort(),
      description: 'Sets the name of the icon.',
    },
    value: {
      control: '-',
      description: 'Define value of the date field.',
    },
    disabled: {
      type: 'boolean',
      description: 'Sets the disabled attribute on the date field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    readOnly: {
      type: 'boolean',
      description: 'Sets the readOnly attribute on the date field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    required: {
      type: 'boolean',
      description: 'Sets the required attribute on the date field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    status: {
      type: 'enum',
      control: 'radio',
      options: ['default', 'error', 'success'],
      description: 'Sets the status of the date field.',
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    statusMessage: {
      type: 'string',
      control: 'text',
      description: 'Sets the status message rendered under the date field.',
    },
    statusMessagePrefix: {
      description: 'Sets the prefix to the status message rendered under the date field.',
    },
    helperText: {
      type: 'string',
      control: 'text',
      description: 'Sets the helper text rendered under the date field.',
    },
    helperTextPrefix: {
      type: 'string',
      control: 'text',
      description: 'Sets the prefix to the helper text rendered under the date field.',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'A unique id is required for accessibility purpose. ',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'An id used for automation testing.',
    },
    textMap: {
      type: 'object',
      control: 'object',
      description: 'Object containing localized text for various elements.',
    },
    dateSegmentPlaceholder: {
      type: 'object',
      control: 'object',
      description: 'A placeholder for the date input format',
    },
    onIconClick: {
      control: '-',
      description: 'Callback when the icon is clicked.',
    },
    onIconKeyDown: {
      control: '-',
      description: 'Callback when a key is pressed on the icon.',
    },
    onChange: {
      control: '-',
      description: 'Callback when date field value changed.',
    },
    onClear: {
      control: '-',
      description: 'Callback when user clears date field.',
    },
    onFocus: {
      control: '-',
      description: 'Callback when user focus on date field.',
    },
    onBlur: {
      control: '-',
      description: 'Callback when user leave date field.',
    },
    onClick: {
      control: '-',
      description: 'Callback when the user clicks the date field.',
    },
    formatDateTimeForSR: {
      control: '-',
      description: 'Formats how screen reader should announce the date.',
    },
  }}
  args={{
    dateTimeParts: [
      { id: 'segment-month-1', type: 'month', value: '' },
      { id: 'segment-literal-1', type: 'literal', value: '/' },
      { id: 'segment-day-1', type: 'day', value: '' },
      { id: 'segment-literal-2', type: 'literal', value: '/' },
      { id: 'segment-year-1', type: 'year', value: '' },
    ],
    dateSegmentPlaceholder: {
      day: 'dd',
      month: 'mm',
      year: 'yyyy',
    },
    textMap: {
      ariaLabel: 'Appointment date',
      clearButtonAriaLabel: 'clear button aria label',
      iconAriaLabel: 'icon aria label',
      dayLabel: 'day',
      monthLabel: 'month',
      yearLabel: 'year',
      formatLabel: 'format',
      expectedDateFormatLabel: 'Expected Date Format:',
      blank: 'blank',
      required: 'required',
      invalidEntry: 'invalid entry',
      invalidDay: 'invalid day',
      invalidMonth: 'invalid month',
      invalidYear: 'invalid year',
    },
    label: 'Appointment date',
    id: 'datefield-1',
    testId: 'datefield-test-id',
    disabled: false,
    readOnly: false,
    required: true,
    status: 'default',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Error:',
    helperText: 'within 60 days from today',
    helperTextPrefix: 'Recommended:',
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    onChange: action('onChange'),
    onClear: action('onClear'),
    onClick: action('onClick'),
    onIconClick: action('onIconClick'),
    onIconKeyDown: action('onIconKeyDown'),
  }}
/>

# DateField

<Examples />

## Live Demo

export const formatDateTimeForSR = (value) => {
  return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
};

<Canvas>
  <Story name="DateField">
    {(args) => {
      const dateFieldRef = React.useRef(null);
      const [dateValue, setDateValue] = React.useState(null);
      const [{ status }, updateArgs] = useArgs();
      const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
      const handleDateValueChange = (displayValue, value) => {
        setHasDisplayValue(!!displayValue);
        if (value) {
          const newDate = new Date(value);
          // if Year is less than 100, it will map it to 1901 or 1999
          // using setFullYear for any year less than 100 to work correctly
          newDate.setFullYear(value.getFullYear());
          setDateValue(newDate);
        } else {
          setDateValue(null);
        }
        args.onChange?.(displayValue, value);
      };
      const handleBlur = (e) => {
        if (!dateValue && hasDisplayValue) {
          updateArgs({ status: 'error' });
        } else {
          updateArgs({ status: 'default' });
        }
        args.onBlur?.(e);
      };
      const handleClear = (e) => {
        args.onClear?.(e);
        dateFieldRef.current && dateFieldRef.current.clear();
        setDateValue(null);
      };
      React.useEffect(() => {
        updateArgs({ status: args.status });
      }, [args.status]);
      return (
        <DateField
          {...args}
          ref={dateFieldRef}
          onClear={handleClear}
          value={dateValue}
          status={status}
          onChange={handleDateValueChange}
          onBlur={handleBlur}
          formatDateTimeForSR={formatDateTimeForSR}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="DateField" />
