import React, { RefObject, forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';

import { announce, clearAnnouncer } from '../../../utils';
import { useComponentFocused } from '../../../utils/use-component-focused';
import { Divider } from '../../divider';
import { FormFieldContainer, FormFieldContainerContext, TFormFieldStatus } from '../../form-field-container';
import { TIconName } from '../../icon';
import { IconButton } from '../../icon-button';
import { IDateFieldTextMapBase, IDateSegmentPlaceholder, IDateTimeFormatPart } from '../date-field-helpers';
import {
  DateSegmentContainer,
  IDateSegmentContainerMethods,
  IDateSegmentContainerTextMap,
} from '../date-segment-container';

import styles from './date-field.module.scss';

export interface IDateFieldMethods extends Omit<IDateSegmentContainerMethods, 'setCursorStart' | 'setCursorEnd'> {
  focus: IDateSegmentContainerMethods['setCursorStart'];
}

/* eslint-disable-next-line @typescript-eslint/no-empty-interface */
export interface IDateFieldTextMap extends IDateFieldTextMapBase {}

export interface IDateFieldProps {
  label?: string;
  value?: Date;
  dateTimeParts: IDateTimeFormatPart[];
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  status?: TFormFieldStatus;
  statusMessage?: string;
  statusMessagePrefix?: string;
  helperText?: string;
  helperTextPrefix?: string;
  id: string;
  testId?: string;
  textMap: IDateFieldTextMap;
  dateSegmentPlaceholder: IDateSegmentPlaceholder;
  iconName?: TIconName;
  dateFieldRef?: RefObject<HTMLElement>;
  onIconClick?: (e: React.MouseEvent) => void;
  onIconKeyDown?: (e: React.KeyboardEvent) => void;
  onChange?: (displayValue: string, value?: Date) => void;
  onClear?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onClick?: (event: React.MouseEvent) => void;
  formatDateTimeForSR: (value: Date) => string;
}

export const DateField = forwardRef<IDateFieldMethods, IDateFieldProps>((props: IDateFieldProps, ref) => {
  const {
    label,
    value,
    dateTimeParts,
    disabled,
    readOnly,
    required,
    id,
    status = 'default',
    statusMessage,
    statusMessagePrefix,
    helperText,
    helperTextPrefix,
    testId,
    textMap,
    dateSegmentPlaceholder,
    iconName,
    dateFieldRef,
    onIconClick,
    onIconKeyDown,
    onChange,
    onClear,
    onFocus,
    onBlur,
    onClick,
    formatDateTimeForSR,
  } = props;

  const [isFocused, setIsFocused] = useState<boolean | undefined>(undefined);
  const [displayValue, setDisplayValue] = useState('');
  const dateContainerRef = useRef<HTMLDivElement>(null);
  const dateSegmentContainerRef = useRef<IDateSegmentContainerMethods>(null);
  const clearBtnRef = useRef<HTMLDivElement>(null);
  const iconBtnRef = useRef<HTMLDivElement>(null);

  const dateFieldLabelId = `${id}-label`;
  const statusMessageId = `${id}-status-message`;

  const { clearButtonAriaLabel, iconAriaLabel } = textMap;

  const buildDateSegmentContainerTextMap = (): IDateSegmentContainerTextMap => {
    const isStatusMessageRendered = !!statusMessage && status !== 'default';
    const isHelperTextRendered = !!helperText && !isStatusMessageRendered;

    return {
      ariaLabel: textMap.ariaLabel,
      clearButtonAriaLabel: textMap.clearButtonAriaLabel,
      iconAriaLabel: textMap.iconAriaLabel,
      dayLabel: textMap.dayLabel,
      monthLabel: textMap.monthLabel,
      yearLabel: textMap.yearLabel,
      formatLabel: textMap.formatLabel,
      expectedDateFormatLabel: textMap.expectedDateFormatLabel,
      blank: textMap.blank,
      required: required && textMap.required ? textMap.required : undefined,
      invalidEntry: status === 'error' && textMap.invalidEntry ? textMap.invalidEntry : undefined,
      invalidDay: textMap.invalidDay,
      invalidMonth: textMap.invalidMonth,
      invalidYear: textMap.invalidYear,
      formattedSRValue: value ? formatDateTimeForSR(value) : undefined,
      dateSegmentPlaceholder: dateSegmentPlaceholder,
      label: label,
      statusMessage: isStatusMessageRendered ? statusMessage : undefined,
      statusMessagePrefix: statusMessagePrefix,
      helperText: isHelperTextRendered ? helperText : undefined,
      helperTextPrefix: helperTextPrefix,
    };
  };

  const handleMouseXPosition = (e: React.MouseEvent) => {
    if (disabled) return;
    if (
      dateContainerRef.current?.contains(e.target as HTMLElement) ||
      iconBtnRef.current?.contains(e.target as HTMLElement)
    ) {
      // On mousedown, set focus on the closest date segment near the mouse click position
      // ex. in the en-US locale, if clicking right most part of DateField, cursor should land on the year segment (yyyy)
      dateSegmentContainerRef.current?._focusOnMouseXPosition?.(e.clientX);
    }
  };

  const handleOnClick = (e: React.MouseEvent) => {
    handleMouseXPosition(e);
    onClick?.(e);
  };

  useComponentFocused(
    [dateContainerRef, iconBtnRef, clearBtnRef],
    // eventListener types
    ['mousedown', 'keyup'],
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      setIsFocused(true);
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      setIsFocused(false);
    }
  );

  useEffect(() => {
    if (disabled) return;
    if (isFocused) {
      onFocus?.();
    } else if (typeof isFocused === 'boolean' && !isFocused) {
      onBlur?.();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFocused]);

  const handleChange = (updatedDisplayValue: string, dateValue: Date | undefined) => {
    clearAnnouncer('assertive');
    if (dateValue) {
      announce(formatDateTimeForSR(dateValue));
    }
    setDisplayValue(updatedDisplayValue);
    onChange?.(updatedDisplayValue, dateValue);
  };

  const handleClearButtonOnClick = () => {
    if (disabled) return;
    onClear?.();
  };

  const handleClearButtonKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;
    if (e.key === 'Enter' || e.key === ' ') {
      onClear?.();
      e.stopPropagation();
    }
    if (e.key !== 'Tab') e.preventDefault();
  };

  const handleIconOnClick = (e: React.MouseEvent) => {
    if (disabled) return;
    onIconClick?.(e);
  };

  const handleIconOnKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;
    onIconKeyDown?.(e);
  };

  useImperativeHandle(ref, () => ({
    clear: () => {
      dateSegmentContainerRef.current?.clear();
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    _focusOnMouseXPosition: (x: number) => {
      dateSegmentContainerRef.current?._focusOnMouseXPosition?.(x);
    },
    focus: () => {
      dateSegmentContainerRef.current?.setCursorStart();
    },
  }));

  useEffect(() => {
    if (dateContainerRef.current && dateFieldRef) {
      (dateFieldRef as any).current = dateContainerRef.current;
    }
  }, [dateFieldRef, dateContainerRef]);

  const renderClearButton = () =>
    (!!displayValue || !!value) &&
    !disabled &&
    !readOnly &&
    onClear && (
      <div
        className={classnames(styles.clearButtonWrapper, {
          [styles.clearButtonWrapperWithIcon]: !!iconName,
        })}
        ref={clearBtnRef}
      >
        <div className={styles.clearButton}>
          <IconButton
            id={`${id}-clear-icon-button`}
            iconName="xSmall"
            type="button"
            ariaLabel={clearButtonAriaLabel ?? ''}
            onClick={handleClearButtonOnClick}
            onKeyDown={handleClearButtonKeyDown}
            testId={testId ? `${testId}-clear-icon-button` : undefined}
            variant="tertiaryNeutral"
            className={styles.overrideIconFill}
          />
        </div>
        {!!iconName && (
          <div className={styles.divider}>
            <Divider vertical />
          </div>
        )}
      </div>
    );

  const renderFormFieldContainerContent = () => {
    return (
      <div id={id} ref={dateContainerRef} data-testid={testId} className={styles.evrDateField} onClick={handleOnClick}>
        <div
          className={classnames(styles.dateSegmentWrapper, {
            [styles.disabled]: disabled,
          })}
        >
          <DateSegmentContainer
            ref={dateSegmentContainerRef}
            dateTimeParts={dateTimeParts}
            value={value}
            disabled={disabled}
            readOnly={readOnly}
            testId={testId} //no suffix required to separate this testId from the container as DateSegment adds the segment type as a suffix
            onChange={handleChange}
            textMap={buildDateSegmentContainerTextMap()}
            dateSegmentPlaceholder={dateSegmentPlaceholder}
            isParentFocused={isFocused}
          />
        </div>
        {/* should consider creating a generic button bar component */}
        {renderClearButton()}
        {!!iconName && (
          <div className={classnames({ [styles.iconWrapper]: iconName })} ref={iconBtnRef}>
            <IconButton
              id={`${id}-${iconName}-icon-button`}
              iconName={iconName}
              type="button"
              ariaLabel={iconAriaLabel ?? ''}
              onClick={handleIconOnClick}
              onKeyDown={handleIconOnKeyDown}
              testId={testId ? `${testId}-${iconName}-icon-button` : undefined}
              disabled={disabled || readOnly}
              variant="tertiaryNeutral"
              className={classnames({ [styles.overrideIconFill]: !disabled && !readOnly })}
            />
          </div>
        )}
      </div>
    );
  };

  const formFieldContainerContext = useMemo(
    () => ({
      label,
      disabled,
      readOnly,
      required,
      status,
      helperText,
      helperTextPrefix,
      statusMessage,
      statusMessagePrefix,
    }),
    [label, disabled, readOnly, required, status, helperText, helperTextPrefix, statusMessage, statusMessagePrefix]
  );

  return (
    <FormFieldContainerContext.Provider value={formFieldContainerContext}>
      <FormFieldContainer
        testId={testId ? `${testId}-form-field-container` : undefined}
        renderContent={renderFormFieldContainerContent}
        focused={isFocused}
        labelId={dateFieldLabelId}
        statusMessageId={statusMessageId}
        htmlFor={id}
        onClickLabel={() => {
          dateSegmentContainerRef?.current && dateSegmentContainerRef.current.setCursorStart();
        }}
      />
    </FormFieldContainerContext.Provider>
  );
});

DateField.displayName = 'DateField';
