import { <PERSON>Example } from '../../../../.storybook/doc-blocks/example/example';
import { DatePicker } from './date-picker';
import { But<PERSON> } from '../../button';
import { addMonths, getDate, getDay, getMonth, setDate } from '../../../utils/date-utils';
import { LinkTo } from '../../../../.storybook/docs/shared/link-to';
import { Warning } from '../../../../.storybook/docs/shared/status-banner.tsx';

<Warning>
  Use the <LinkTo dayforceStoryPath="docs/components-datepicker--docs">globalized DatePicker</LinkTo> from
  `@ceridianhcm/everest-dayforce`. DatePicker from `@ceridianhcm/components` should not be used.
</Warning>

export const scope = { DatePicker, Button, addMonths, getDate, getDay, getMonth, setDate };

## Basic Usage

DatePicker is used to select a date. It combines the <LinkTo kind="Components/Text Fields/DateField">DateField</LinkTo> and <LinkTo kind="Toolbox/Calendar">Calendar</LinkTo> components.

export const defaultCode = `() => {
  const styles = {
    container: {
      width: '320px',
    },
  };
  const minDate = new Date('1/1/2000');
  const maxDate = new Date('12/31/2050');
  const dateTimeParts = [
    { id: 'segment-month-1', type: 'month', value: '' },
    { id: 'segment-literal-1', type: 'literal', value: '/' },
    { id: 'segment-day-1', type: 'day', value: '' },
    { id: 'segment-literal-2', type: 'literal', value: '/' },
    { id: 'segment-year-1', type: 'year', value: '' },
  ];
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const weekdays = [
    { short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' },
  ];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString('en-US', { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    clearButtonAriaLabel: 'Clear input',
    calendarAriaLabel: 'choose date',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    requiredLabel: 'required',
    blank: 'blank',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const [value, setValue] = React.useState(null);
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const [disabled, setDisabled] = React.useState(false);
  const [status, setStatus] = React.useState('default');
  const [statusMessage, setStatusMessage] = React.useState('');
  const [statusMessagePrefix, setStatusMessagePrefix] = React.useState('');
  const handleBlur = () => {
    if (!value && hasDisplayValue) {
      setStatus('error');
      setStatusMessagePrefix('Error:');
      setStatusMessage('Please enter a valid date');
    } else if (value && (value > maxDate || value < minDate)) {
      setStatus('error');
      setStatusMessagePrefix('Error: ');
      setStatusMessage('Please enter a valid date within the range');
    } else {
      setStatus('default');
    }
  };
  const handleChange = (displayValue, newValue) => {
    setHasDisplayValue(!!displayValue);
    if (newValue) {
      const newDate = new Date(newValue);
      newDate.setFullYear(newValue.getFullYear());
      setValue(newDate);
    } else {
      setValue(undefined);
    }
  };
  React.useEffect(() => {
    if (minDate > maxDate) {
      setDisabled(true);
      console.error('Error: Invalid min or max date');
    }
  }, [minDate, maxDate]);
  return (
    <div style={styles.container}>
      <DatePicker
        id="date-picker-id"
        testId="date-picker-test-id"
        label={'Starting date'}
        disabled={disabled}
        value={value}
        minDate={minDate}
        maxDate={maxDate}
        dateTimeParts={dateTimeParts}
        dateSegmentPlaceholder={dateSegmentPlaceholder}
        weekdays={weekdays}
        months={months}
        helperTextPrefix="Recommended:"
        helperText="within 60 days from today"
        status={status}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        onChange={handleChange}
        onBlur={handleBlur}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={defaultCode} />

Clicking on the calendar icon will open Calendar overlay and cause Calendar to become focus trapped.

The `onChange` will trigger to update the date value on date field change and calendar change:

```typescript
const handleChange = (displayValue: string, newValue?: Date) => {
  setHasDisplayValue(!!displayValue);
  if (newValue) {
    const newDate = new Date(newValue);
    newDate.setFullYear(newValue.getFullYear());
    setValue(newDate);
  } else {
    setValue(undefined);
  }
};

<DatePicker {...rest} onChange={handleChange} />;
```

## Variations

DatePicker has different variations based on the combination of props being used.

export const variationsCode = `() => {
  const locale = 'en-US';
  const label = 'Starting Date';
  const ariaLabel = 'This is an aria label';
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const weekdays = [
    { short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' },
  ];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString('en-US', { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    clearButtonAriaLabel: 'Clear input',
    calendarAriaLabel: 'choose date',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    requiredLabel: 'required',
    blank: 'blank',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const dateValue = new Date();
  const propsWithoutLabel = {
    dateSegmentPlaceholder,
    dateTimeParts: getDateTimeParts(),
    weekdays,
    months,
    formatDateForSR,
    textMap,
  };
  const defaultProps = { ...propsWithoutLabel, label };
  const styles = {
    row: {
      width: '55%',
      height: '120px',
      marginBottom: '1rem',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
    },
    headerText: {
      marginBlockEnd: '10px',
      width: '70%',
    },
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const HeaderText = ({ children }) => (
    <div style={styles.headerText}>
      <p className="evrBodyText">{children}</p>
    </div>
  );
  return (
    <div style={styles.column}>
      <Row>
        <HeaderText>DatePicker with label value</HeaderText>
        <DatePicker {...defaultProps} id="label-date-picker-id" />
      </Row>
      <Row>
        <HeaderText>DatePicker without label</HeaderText>
        <DatePicker {...propsWithoutLabel} textMap={{ ...textMap, ariaLabel }} id="no-label-date-picker-id" />
      </Row>
      <Row>
        <HeaderText>DatePicker with initial value</HeaderText>
        <DatePicker {...defaultProps} id="initial-value-date-picker-id" value={dateValue} />
      </Row>
      <Row>
        <HeaderText>DatePicker with default month view Calendar</HeaderText>
        <DatePicker {...defaultProps} id="default-view-month-date-picker-id" defaultView={'month'} />
      </Row>
      <Row>
        <HeaderText>DatePicker with default year view Calendar</HeaderText>
        <DatePicker {...defaultProps} id="default-view-year-date-picker-id" defaultView={'year'} />
      </Row>
      <Row>
        <HeaderText>DatePicker as required field</HeaderText>
        <DatePicker {...defaultProps} id="required-date-picker-id" required />
      </Row>
      <Row>
        <HeaderText>Disabled DatePicker</HeaderText>
        <DatePicker {...defaultProps} id="disabled-date-picker-id" disabled />
      </Row>
      <Row>
        <HeaderText>Disabled DatePicker with initial value</HeaderText>
        <DatePicker {...defaultProps} id="disabled-initial-value-date-picker-id" value={dateValue} disabled />
      </Row>
      <Row>
        <HeaderText>ReadOnly DatePicker</HeaderText>
        <DatePicker {...defaultProps} id="readOnly-date-picker-id" readOnly />
      </Row>
      <Row>
        <HeaderText>ReadOnly DatePicker with initial value</HeaderText>
        <DatePicker {...defaultProps} id="readOnly-initial-value-date-picker-id" value={dateValue} readOnly />
      </Row>
      <Row>
        <HeaderText>DatePicker with helper text</HeaderText>
        <DatePicker
          {...defaultProps}
          id="helper-text-date-picker-id"
          helperText="This is a helper text"
          helperTextPrefix="Notice:"
        />
      </Row>
      <br />
      <Row>
        <HeaderText>DatePicker with an error message</HeaderText>
        <DatePicker
          {...defaultProps}
          id="error-message-date-picker-id"
          status="error"
          statusMessage="This is an error message"
          statusMessagePrefix="Error:"
        />
      </Row>
    </div>
  );
}`;

<CodeExample scope={scope} code={variationsCode} />

## Clear DatePicker value

User can clear DatePicker value.

export const clearDatePickerCode = `() => {
  const styles = {
    container: {
      width: '320px',
      marginBottom: '10px',
    },
  };
  const dateTimeParts = [
    { id: 'segment-month-1', type: 'month', value: '' },
    { id: 'segment-literal-1', type: 'literal', value: '/' },
    { id: 'segment-day-1', type: 'day', value: '' },
    { id: 'segment-literal-2', type: 'literal', value: '/' },
    { id: 'segment-year-1', type: 'year', value: '' },
  ];
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const weekdays = [
    { short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' },
  ];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString('en-US', { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    clearButtonAriaLabel: 'Clear input',
    calendarAriaLabel: 'choose date',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    requiredLabel: 'required',
    blank: 'blank',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const datePickerRef = React.useRef(null);
  const [value, setValue] = React.useState(null);
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const [status, setStatus] = React.useState('default');
  const [statusMessage, setStatusMessage] = React.useState('');
  const [statusMessagePrefix, setStatusMessagePrefix] = React.useState('');
  const handleBlur = (e) => {
    if (!value && hasDisplayValue) {
      setStatus('error');
    } else {
      setStatus('default');
    }
  };
  const handleChange = (displayValue, newValue) => {
    setHasDisplayValue(!!displayValue);
    if (newValue) {
      const newDate = new Date(newValue);
      newDate.setFullYear(newValue.getFullYear());
      setValue(newDate);
    } else {
      setValue(undefined);
    }
  };
  const handleClearValue = () => {
    // useImperativeHandle to trigger clear()
    // with typescript: datePickerRef.current && (datePickerRef.current as IDatePickerMethods).clear();
    datePickerRef.current && datePickerRef.current.clear();
  };
  React.useEffect(() => {
    setStatus('default');
  }, []);
  return (
    <div>
      <div style={styles.container}>
        <DatePicker
          ref={datePickerRef}
          id="clear-date-picker-id"
          testId="clear-date-picker-test-id"
          label={'Starting date'}
          value={value}
          dateTimeParts={dateTimeParts}
          dateSegmentPlaceholder={dateSegmentPlaceholder}
          weekdays={weekdays}
          months={months}
          status={status}
          statusMessage={statusMessage}
          statusMessagePrefix={statusMessagePrefix}
          onChange={handleChange}
          onBlur={handleBlur}
          formatDateForSR={formatDateForSR}
          textMap={textMap}
        />
      </div>
      <Button
        id="click-clear-date-picker-value-btn"
        label="Click to clear DatePicker value"
        onClick={handleClearValue}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={clearDatePickerCode} />

## Focus DatePicker input

Click on the Button to set focus to DatePicker input.

export const focusDatePickerCode = `() => {
  const styles = {
    container: {
      width: '320px',
      marginBottom: '10px',
    },
  };
  const dateTimeParts = [
    { id: 'segment-month-1', type: 'month', value: '' },
    { id: 'segment-literal-1', type: 'literal', value: '/' },
    { id: 'segment-day-1', type: 'day', value: '' },
    { id: 'segment-literal-2', type: 'literal', value: '/' },
    { id: 'segment-year-1', type: 'year', value: '' },
  ];
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const weekdays = [
    { short: 'Su', long: 'Sunday' },
    { short: 'Mo', long: 'Monday' },
    { short: 'Tu', long: 'Tuesday' },
    { short: 'We', long: 'Wednesday' },
    { short: 'Th', long: 'Thursday' },
    { short: 'Fr', long: 'Friday' },
    { short: 'Sa', long: 'Saturday' },
  ];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString('en-US', { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    clearButtonAriaLabel: 'Clear input',
    calendarAriaLabel: 'choose date',
    dayLabel: 'day',
    monthLabel: 'month',
    yearLabel: 'year',
    formatLabel: 'format',
    expectedDateFormatLabel: 'Expected Date Format:',
    requiredLabel: 'required',
    blank: 'blank',
    invalidEntry: 'invalid entry',
    invalidDay: 'invalid day',
    invalidMonth: 'invalid month',
    invalidYear: 'invalid year',
    nextMonthButtonLabel: 'next month',
    previousMonthButtonLabel: 'previous month',
    previousViewIsYearLabel: 'go to year view',
    previousViewIsMonthLabel: 'go to month view',
  };
  const datePickerRef = React.useRef(null);
  const [value, setValue] = React.useState(null);
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const [status, setStatus] = React.useState('default');
  const [statusMessage, setStatusMessage] = React.useState('');
  const [statusMessagePrefix, setStatusMessagePrefix] = React.useState('');
  const handleBlur = (e) => {
    if (!value && hasDisplayValue) {
      setStatus('error');
    } else {
      setStatus('default');
    }
  };
  const handleChange = (displayValue, newValue) => {
    setHasDisplayValue(!!displayValue);
    if (newValue) {
      const newDate = new Date(newValue);
      newDate.setFullYear(newValue.getFullYear());
      setValue(newDate);
    } else {
      setValue(undefined);
    }
  };
  const handleFocusInput = () => {
    // useImperativeHandle to trigger focus()
    // with typescript: datePickerRef.current && (datePickerRef.current as IDatePickerMethods).focus();
    datePickerRef.current && datePickerRef.current.focus();
  };
  React.useEffect(() => {
    setStatus('default');
  }, []);
  return (
    <div>
      <div style={styles.container}>
        <DatePicker
          ref={datePickerRef}
          id="focus-date-picker-id"
          testId="focus-date-picker-test-id"
          label={'Starting date'}
          value={value}
          dateTimeParts={dateTimeParts}
          dateSegmentPlaceholder={dateSegmentPlaceholder}
          weekdays={weekdays}
          months={months}
          status={status}
          statusMessage={statusMessage}
          statusMessagePrefix={statusMessagePrefix}
          onChange={handleChange}
          onBlur={handleBlur}
          formatDateForSR={formatDateForSR}
          textMap={textMap}
        />
      </div>
      <Button
        id="click-focus-date-picker-input-btn"
        label="Click to focus DatePicker input"
        onClick={handleFocusInput}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={focusDatePickerCode} />

## Globalization

DatePicker is a controlled component and does not handle Globalization internally.

Globalization is handled by the following props:

| Prop                   | Description                                                                                                         |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------- |
| dateTimeParts          | DatePicker segment parts object                                                                                     |
| dateSegmentPlaceholder | Placeholder for the date input format                                                                               |
| startDayOfWeek         | Day the week begins with                                                                                            |
| weekdays               | Days of the week                                                                                                    |
| months                 | Months in the year                                                                                                  |
| textMap                | Object containing localized text for various elements                                                               |
| formatDateForSR        | Function to format how screen reader announces the date value. Accepts view and value parameters, returns a string. |

### Germany | "de-DE"

The date format in Germany is `dd.mm.yyyy`. The `dateSegmentPlaceholder` for day is `dd`, `mm` for month, and `yyyy` for year.

```typescript
const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};
```

The `dateTimeParts` prop needs to be customized for this specific locale to first display the day, followed by the month, then year.

```typescript
const dateTimeParts = [
  { id: 'segment-day-1', type: 'day', value: '' },
  { id: 'segment-literal-1', type: 'literal', value: '/' },
  { id: 'segment-month-1', type: 'month', value: '' },
  { id: 'segment-literal-2', type: 'literal', value: '/' },
  { id: 'segment-year-1', type: 'year', value: '' },
];
```

Here's an example of a DatePicker using localized German values from the Javascript native object Intl.

Notice: The week begins on a Monday. This means `startDayOfWeek` is set to 1.

export const germanyDatePickerCode = `() => {
  const styles = {
    container: {
      width: '260px',
    },
  };
  const locale = 'de-DE';
  const shortLabelLength = 'short';
  const longLabelLength = 'long';
  const dateSegmentPlaceholder = {
    day: 'dd',
    month: 'mm',
    year: 'yyyy',
  };
  const getDateTimeParts = () => {
    const dateTimeParts = [];
    const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
    for (let i = 0; i < parts.length; i++) {
      dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
    }
    return dateTimeParts;
  };
  const getWeekdays = (locale) => {
    let weekdays = [];
    let newDate = new Date();
    for (let i = 0; i < 7; i++) {
      const date = setDate(newDate, getDate(newDate) + i - getDay(newDate));
      const shortLabel = new Intl.DateTimeFormat(locale, { weekday: shortLabelLength }).format(date);
      const longLabel = new Intl.DateTimeFormat(locale, { weekday: longLabelLength }).format(date);
      const day = { short: shortLabel, long: longLabel };
      weekdays.push(day);
    }
    return weekdays;
  };
  const getMonths = (locale) => {
    let months = [];
    let newDate = new Date();
    const formatter = new Intl.DateTimeFormat(locale, {
      month: shortLabelLength,
    }).format;
    for (let i = 0; i < 12; i++) {
      const month = formatter(addMonths(newDate, i - getMonth(newDate)));
      months.push(month);
    }
    return months;
  };
  const formatDateForSR = (view, value) => {
    switch (view) {
      case 'year':
        return value.toLocaleDateString(locale, { year: 'numeric' });
      case 'month':
        return value.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
      case 'day':
        return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
    }
  };
  const textMap = {
    clearButtonAriaLabel: 'Klare Eingabe',
    calendarAriaLabel: 'Datum wählen',
    nextMonthButtonLabel: 'nächsten Monat',
    previousMonthButtonLabel: 'vorheriger Monat',
    previousViewIsYearLabel: 'Gehen Sie zur Jahresansicht',
    previousViewIsMonthLabel: 'Gehen Sie zur Monatsansicht',
  };
  const [dateValue, setDateValue] = React.useState(null);
  const [status, setStatus] = React.useState('default');
  const [errorMessage, setErrorMessage] = React.useState('');
  const [errorMessagePrefix, setErrorMessagePrefix] = React.useState('');
  const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
  const handleChange = (displayValue, newValue) => {
    setHasDisplayValue(!!displayValue);
    if (newValue) {
      const newDate = new Date(newValue);
      newDate.setFullYear(newValue.getFullYear());
      setDateValue(newDate);
    } else {
      setDateValue(undefined);
    }
  };
  const handleClear = () => {
    setDateValue(undefined);
  };
  const handleBlur = () => {
    if (!dateValue && hasDisplayValue) {
      setStatus('error');
      setErrorMessagePrefix('Fehler:');
      setErrorMessage('Bitte gib ein korrektes Datum an');
    } else {
      setStatus('default');
    }
  };
  return (
    <div style={styles.container}>
      <DatePicker
        label="Startdatum"
        helperTextPrefix="Empfohlen:"
        helperText="innerhalb von 60 Tagen ab heute"
        id="germany-datepicker-id"
        testId="germany-datepicker-test-id"
        required
        value={dateValue}
        dateSegmentPlaceholder={dateSegmentPlaceholder}
        dateTimeParts={getDateTimeParts()}
        startDayOfWeek={1}
        weekdays={getWeekdays(locale)}
        months={getMonths(locale)}
        onClear={handleClear}
        onBlur={handleBlur}
        onChange={handleChange}
        status={status}
        statusMessage={errorMessage}
        statusMessagePrefix={errorMessagePrefix}
        formatDateForSR={formatDateForSR}
        textMap={textMap}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={germanyDatePickerCode} />

## How to Use

Consider using DatePicker if you need a date input field and a calendar picker.

## Accessibility

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

The following values should be provided as part of the `textMap` prop:

| Label                    | Description                                            | <div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div>                                                                                                 |
| ------------------------ | ------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------- |
| ariaLabel                | Aria label for DatePicker                              | "Date picker"                                                                                                                                             |
| clearButtonAriaLabel     | Button to clear DateField input                        | "Clear input"                                                                                                                                             |
| calendarAriaLabel        | Button to open/close calendar, Aria label for Calendar | "choose date"                                                                                                                                             |
| dayLabel                 | DateField segment value for day                        | "day"                                                                                                                                                     |
| monthLabel               | DateField segment value for month                      | "month"                                                                                                                                                   |
| yearLabel                | DateField segment value for year                       | "year"                                                                                                                                                    |
| formatLabel              | DateField format label                                 | "format" <br/><br/> User focuses on the placeholder segment for month. Screen reader reads out "month edit format mm", where **"format"** is formatLabel. |
| expectedDateFormatLabel  | Expected date format description                       | "Expected Date Format:"                                                                                                                                   |
| requiredLabel            | Required input label                                   | "required"                                                                                                                                                |
| blank                    | Empty value label                                      | "blank"                                                                                                                                                   |
| invalidEntry             | Status error message                                   | "invalid entry"                                                                                                                                           |
| invalidDay               | Invalid day error                                      | "invalid day"                                                                                                                                             |
| invalidMonth             | Invalid month error                                    | "invalid month"                                                                                                                                           |
| invalidYear              | Invalid year error                                     | "invalid year"                                                                                                                                            |
| nextMonthButtonLabel     | Next month arrow button in Calendar day view           | "next month"                                                                                                                                              |
| previousMonthButtonLabel | Previous month arrow button in Calendar day view       | "previous month"                                                                                                                                          |
| previousViewIsYearLabel  | Previous view button in Calendar month view            | "go to year view"                                                                                                                                         |
| previousViewIsMonthLabel | Previous view button in Calendar day view              | "go to month view"                                                                                                                                        |

The `ariaLabel` in `textMap` object is optional, but should be provided when a `label` for DatePicker is not provided.

For help with DatePicker aria labels, please refer to this outside [ARIA example](https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-datepicker/).

### Supported Keys

- <kbd>Down Arrow</kbd> / <kbd>Up Arrow</kbd> navigates up and down selectable items in each calendar view
- <kbd>Enter</kbd> selects an item
- <kbd>Space</kbd> selects an item
- <kbd>Tab</kbd> moves tab focus
