# Date Picker

## Summary

- Start Date: 2023-08-25
- Figma link: https://www.figma.com/file/0ZDD0G8lcpcEVaCbjjmxvD/branch/WQKAlaU2yr1OAL0Zs22reC/%F0%9F%A7%AA-DatePicker?type=design&node-id=3361-2837&mode=design&t=Uzsy6rrD2k2GSogc-0

## Detailed Design

DatePicker allows users to input a desired date manually or select/unselect a particular date using calendar view. It uses the DateField and Calendar overlay. DatePicker should be fully globalizable.

Requirements:

- [ ] If date is manually typed in and calendar overlay is open, calendar value should update instantly.
- [ ] If date is selected using Calendar, new value should populate in DateField.
  - Clicking on year, month, or day date should instantaneously update DatePicker's value.
- [ ] Calendar overlay should close after a day date is selected.
  - Should not close if year or month is selected.
- [ ] Able to clear DateField input & Calendar value with clear icon button
- [ ] Able to open Calendar by hovering cursor over calendar icon button
  - Calendar overlay to appear below <PERSON>Field
- [ ] Able to set Calendar value if valid date entered in DateField
- [ ] Not able to interact with <PERSON><PERSON><PERSON> if disabled
- [ ] Mobile (functionality same, experience/look is different)
  - Fullscreen
  - Focus trap: focus lands on calendar icon button once fullscreen closed

## API

1. **id**: `string`
   Sets **id** attribute.
1. **testId**: `undefined | string`  
   Optional. Sets **data-test-id** attribute.
1. **disabled**: `undefined | boolean`
   Optional. Sets the disabled attribute.
1. **required**: `undefined | boolean`
   Optional. Sets the required attribute.
1. **ariaLabel**: `undefined | string`
   Optional. Sets a clear description of the _date field_.
1. **label**: `undefined | string`
   Sets label on _date field_.
1. **value**: `undefined | TDate`
   Optional. Defines value for _date picker_.
1. **dateTimeParts**: `IDateTimeFormatPart[]`
   DateField segment parts object.
1. **dateSegmentPlaceholder**: `undefined | IDateSegmentPlaceholder`
   Optional. Sets a placeholder for the date input format on _date field_.
1. **status**: `undefined | TFormFieldStatus`
   Optional. Sets status on _date field_.
1. **statusMessage**: `undefined | string`
   Optional. Sets status message on _date field_.
1. **statusMessagePrefix**: `undefined | string`
   Optional. Sets status message prefix on _date field_.
1. **helperText**: `undefined | string`
   Optional. Sets helper text on _date field_.
1. **helperTextPrefix**: `undefined | string`
   Optional. Sets helper text prefix on _date field_.
1. **formatDateForSR**: `undefined | (value: Date) => string`
   Optional. Formats how screen reader should announce the date.
1. **screenReaderTextMap**: `undefined | IDatePickerScreenReaderText`
   Optional. Sets object containing localized text for various elements.

   ```typescript
   export interface IDatePickerScreenReaderText {
     clearButtonAriaLabel: string;
     required?: string;
     invalidEntry?: string;
     formatLabel?: string;
     expectedDateFormatLabel?: string;
     blank?: string;
     invalidDay?: string;
     invalidMonth?: string;
     invalidYear?: string;
     dayLabel?: string;
     monthLabel?: string;
     yearLabel?: string;
     calendarHeaderLabel: string;
     calendarNextMonthButtonLabel: string;
     calendarPreviousMonthButtonLabel: string;
     calendarPreviousViewIsYearLabel: string;
     calendarPreviousViewIsMonthLabel: string;
   }
   ```

1. **minDate**: `undefined | TDate`
   Optional. Sets min date.
1. **maxDate**: `undefined | TDate`
   Optional. Sets max date.
1. **defaultView**: `undefined | TView`
   Optional. Sets default _calendar_ view. Default is day.
1. **weekdays**: `undefined | string[]`
   Optional. Sets _calendar_ header labels. Begins with Sunday as 0.
1. **startDayOfWeek**: `undefined | number`
   Optional. Sets which day the week _calendar_ will begin with. Sunday is 0.
1. **disableDefaultToday**: `undefined | boolean`
   Optional. Determines whether to highlight today's date in _calendar_. Default is `false`.
1. **onValueChange**: `undefined | (value: Date) => void`
   Optional. Callback when value has changed.
1. **onClear**: `() => void`
   Optional. Callback when clear button in _date field_ is clicked.

## Usage

DateField needs new `onClear` prop to indicate when to show clear button.

```
export interface IDateField {
   onClear?: () => void;
}
```

Add clear button and calendar icon to DateField.

```
  const handleClearButtonMouseDown = () => {
    if (disabled) return;
    onClear?.(true);
  };
  const handleClearButtonKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;
    if (e.key === 'Enter' || e.key === ' ') {
      clearSelection();
      e.stopPropagation();
    }
    if (e.key !== 'Tab') e.preventDefault();
  };

  const renderClearButton = () =>
    value && !!onClear && iconName && !disabled && (
      <div>
         <FocusRing target="firstchild">
            <div
               id={`${id}-clear-button`}
               className={styles.clearButton}
               onClick={handleClearButtonClick}
               onKeyDown={handleClearButtonKeyDown}
               tabIndex={disabled ? -1 : 0}
               role="button"
               aria-hidden={true}
               aria-label={clearButtonAriaLabel}
               data-testid={testId ? `${testId}-clear-button` : undefined}
            >
            <Icon
              name="xSmall"
              fill={!disabled ? '--evr-content-primary-default' : '--evr-inactive-content'}
              id={`${id}-clear-button-icon`}
            />
          </div>
        </FocusRing>
        <div className={styles.divider}>
            <Divider vertical />
         </div>
      </div>
    );

  const renderFormFieldContainerContent = () => {
    return (
      <div
        id={id}
        ref={dateContainerRef}
        className={classnames(styles.evrDateField)}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
      >
        <div className={classnames(styles.dateSegmentWrapper, {[styles.disabled]: disabled})} >
          <DateSegmentContainer
            ref={dateSegmentContainerRef}
            dateTimeParts={dateTimeParts}
            value={value}
            disabled={disabled}
            testId={testId}
            onChange={handleChange}
            dateFieldScreenReaderValue={dateFieldScreenReaderValue}
            screenReaderTextMap={screenReaderTextMap}
            dateSegmentPlaceholder={dateSegmentPlaceholder}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
        </div>
        {renderClearButton()}
        {!!onClear && iconName &&
         <FormFieldContainerIconWrapper
            iconName={'calendar'}
            disabled={disabled}
            position={'end'}
            onClick={}
         />}
      </div>
    );
  };
```

Calendar accepts min and maxDate props but DateField does not. To handle min/maxDates, add a check in the onChange function for DateField.

```
   const handleDateValueChange = (displayValue, value) => {
      setHasDisplayValue(!!displayValue);
      if (value) {
         const newDate = new Date(value.toString());
         if(newDate > maxDate) {
            setStatus('error');
            setErrorMessagePrefix('Error:')
            setErrorMessage('Exceeded max date');
         }
         if(newDate < minDate) {
            setStatus('error');
            setErrorMessagePrefix('Error:')
            setErrorMessage('Exceeded min date');
         }
         // if Year is less than 100, it will map it to 1901 or 1999
         // using setFullYear for any year less than 100 to work correctly
         newDate.setFullYear(value.getFullYear());
         setDateValue(newDate);
      } else {
         setDateValue(null);
      }
   };
```

Wrap Calendar in an TriggerAreaStyledOverlay to open on calendar icon button hover/press/click. Overlay accomodates fullscreen for mobile view.

Basic usage of `DatePicker`:

```
  const overlayTriggerRef = useRef<HTMLElement>(null);
  const [dateFieldValue, setDateFieldValue] = useState();
  const [calendarValue, setCalendarValue] = useState();
  const [value, setValue] = useState();
  const [calendarOpen, setCalendarOpen] = useState(false);

   const handleDateValueChange = (displayValue, value) => {
      setHasDisplayValue(!!displayValue);
      if (value) {
         const newDate = new Date(value.toString());
         if(newDate > maxDate) {
            setStatus('error');
            setErrorMessagePrefix('Error:')
            setErrorMessage('Exceeded max date');
         }
         if(newDate < minDate) {
            setStatus('error');
            setErrorMessagePrefix('Error:')
            setErrorMessage('Exceeded min date');
         }
         // if Year is less than 100, it will map it to 1901 or 1999
         // using setFullYear for any year less than 100 to work correctly
         newDate.setFullYear(value.getFullYear());
         setDateValue(newDate);
      } else {
         setDateValue(null);
      }
   };

   return (
      <TriggerAreaStyledOverlay
         id={`${id}-trigger-area-overlay`}
         testId={testId}
         triggerRef={overlayTriggerRef}
         overlayVisible={calendarOpen}
         error={status === 'error'}
         overrideFocusRingDefaultBehavior={!isFocusedByKeyboard || !isFocusWithinComponent}
         renderTriggerAreaContent={() => (
            <div
              className={classnames(styles.evrOverlayContainer, {
                [styles.error]: status === 'error',
              })}
              ref={overlayWrapperRef}
              onClick={mouseDownHandler}
            >
               <DateField label={label} disabled={disabled} onChange={handleDateValueChange} onClear={onClear} iconName="calendar"/>
            </div>
         )}
         renderOverlayContent={() => (
            <Calendar
               value={value}
               onValueChange={() => {}}
               minDate={minDate}
               maxDate={maxDate}
               weekdays={weekdays}
               months={months}
               startDayOfWeek={startDayOfWeek}
               screenReaderTextMap={screenReaderTextMap}
               formatDateForSR={formatDateForSR}
            />
          )}
      />
   )
```

## Prerequisite/Other Components

1. Re-use existing components

   - DateField
   - Calendar
   - TriggerAreaStyledOverlay
   - Icon button
   - FocusRing
   - FocusTrap

   DateField needs new props for use in DatePicker: `onClear` and `iconName`.

## Accessibility

1. Keyboard Navigation

   - Alt + DownArrow / Command + DownArrow / DownArrow: Open calendar
   - LeftArrow / RightArrow: Move cursor to the day/month/year field

1. Screen Reader Criteria

   - Calendar icon button does not need extra description. DateField input should convey context already.

   - Weekday header abbreviations should be visually hidden (ex. Mo, Tu, We). Alternative full weekday names must be provided (ex: Monday, Tuesday, Wednesday).

   - Calendar header: "[previous view button screen text], [previousViewIsYearLabel]"

## Other Design Systems

[React Spectrum](https://mui.com/x/react-date-pickers/date-calendar/#api)

Accessibility:

- Home & End keys -> Navigate to start/end of month

[Material UI](https://mui.com/x/react-date-pickers/date-calendar/#api)

- Navigating to next/previous month does not change value
- Navigating to year view then clicking different year changes value

Accessibility:

- Home & End keys -> Navigate to start/end of date's current week

[Ant Design](https://ant.design/components/date-picker)

- Changing views does not change the value, but it does show a placeholder which is hovered over date.

## Q&A

Q: Is the positioning of Calendar overlay customizable?
A: No, overlay must open up below DateField due to DateField's label placement.

Q: Are there min/maxDates for DateField as well?
A: No, there will be internal min/maxDate props within DatePicker.

Q: Will we need a prop for DatePicker to determine when its used by itself or in DatePicker (show clear button & calendar icon button)?
A: Yes but make them more generic (onClear & iconName instead of isDatePicker).

Q: Will changing views set a new date value?
A: --

## Required PBIs

1. Create DatePicker component: https://ceridian.atlassian.net/browse/EDS-3479
2. Screen reader accessibility: https://ceridian.atlassian.net/browse/EDS-3519
3. Tests: https://ceridian.atlassian.net/browse/EDS-3520, https://ceridian.atlassian.net/browse/EDS-3521

   - Visual
   - Unit
   - Playwright
   - Axe-core
   - Manual (screen reader)

4. Move to Production: https://ceridian.atlassian.net/browse/EDS-3522

# Changelog

1/26/2024 [EDS-3519: Modify weekdays prop to also accept SR labels, announce weekday SR labels in Day view](https://ceridian.atlassian.net/browse/EDS-3519)

2/8/2024 [EDS-4027: Consolidate onDateFieldChange and onCalendarChange into a single onChange](https://dayforce.atlassian.net/browse/EDS-4027)
