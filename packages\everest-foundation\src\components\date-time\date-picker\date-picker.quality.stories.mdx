import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { DatePicker } from './date-picker';
import { screen, userEvent, waitFor } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../../chromatic';

{/* Disable Chromatic for dynamic tests */}
export const disableDynamicSnapshot = {chromatic: Chromatic.DISABLE};

export const id = 'datepicker-id';
export const testId = 'datepicker-test-id';
export const label = 'Select Date';
export const ariaLabel = 'datepicker aria label';
export const initialValue = new Date(2022, 11, 31); // Dec 31, 2022
export const minDate = new Date('01/01/2010');
export const maxDate = new Date('12/31/2030');
export const invalidValue = new Date('11/41/2041');
export const invalidMinDate = new Date('01/01/2000'); // 10 years before minDate
export const invalidMaxDate = new Date('01/01/2040'); // 10 years after maxDate
export const dateTimeParts = [
  { id: 'segment-month-1', type: 'month', value: '' },
  { id: 'segment-literal-1', type: 'literal', value: '/' },
  { id: 'segment-day-1', type: 'day', value: '' },
  { id: 'segment-literal-2', type: 'literal', value: '/' },
  { id: 'segment-year-1', type: 'year', value: '' },
];
export const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};
export const weekdays = [
  { short: 'Su', long: 'Sunday' },
  { short: 'Mo', long: 'Monday' },
  { short: 'Tu', long: 'Tuesday' },
  { short: 'We', long: 'Wednesday' },
  { short: 'Th', long: 'Thursday' },
  { short: 'Fr', long: 'Friday' },
  { short: 'Sa', long: 'Saturday' },
];
export const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
export const helperText = 'This is some helper text';
export const helperTextPrefix = 'Hint:';
export const statusMessage = 'This is a status message';
export const statusMessagePrefix = 'Error:';
export const textMap = {
  clearButtonAriaLabel: 'Clear input',
  calendarAriaLabel: 'choose date',
  dayLabel: 'day',
  monthLabel: 'month',
  yearLabel: 'year',
  formatLabel: 'format',
  expectedDateFormatLabel: 'Expected date format:',
  blank: 'blank',
  nextMonthButtonLabel: 'next month',
  previousMonthButtonLabel: 'previous month',
  previousViewIsYearLabel: 'go to year view',
  previousViewIsMonthLabel: 'go to month view',
};
export const formatDateForSR = () => {};

export const getDatePicker = () => screen.getByTestId(`${testId}-date-field`);

<Meta
  title="Testing/Automation Test Cases/Date Picker"
  component={DatePicker}
  decorators={[ChromaticDecorators.padStory, ChromaticDecorators.setHeightTo100vh, ChromaticDecorators.setWidthTo100vw]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id,
    testId,
    label,
    dateTimeParts,
    dateSegmentPlaceholder,
    minDate,
    maxDate,
    weekdays,
    months,
    textMap,
    formatDateForSR,
  }}
/>

# Date Picker

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => <DatePicker {...args} label={''} textMap={{ ...textMap, ariaLabel }}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DatePicker with label">{(args) => <DatePicker {...args}></DatePicker>}</Story>
</Canvas>

<Canvas>
  <Story name="DatePicker with value and clear button">
    {(args) => <DatePicker {...args} value={initialValue}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled DatePicker">{(args) => <DatePicker {...args} disabled></DatePicker>}</Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DatePicker">{(args) => <DatePicker {...args} readOnly></DatePicker>}</Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DatePicker with value">
    {(args) => <DatePicker {...args} readOnly value={initialValue}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="ReadOnly DatePicker with FocusRing"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <DatePicker {...args} readOnly></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DatePicker with helper text">
    {(args) => <DatePicker {...args} helperText={helperText} helperTextPrefix={helperTextPrefix}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DatePicker with error message">
    {(args) => (
      <DatePicker
        {...args}
        status={'error'}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
      ></DatePicker>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with Focus Ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <DatePicker {...args}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with calendar overlay in day view"
    parameters={disableDynamicSnapshot}
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with value and calendar overlay in day view"
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args} value={initialValue}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with calendar overlay in month view"
    parameters={disableDynamicSnapshot}
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args} defaultView="month"></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with value and calendar overlay in month view"
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args} defaultView="month" value={initialValue}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with calendar overlay in year view"
    parameters={disableDynamicSnapshot}
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args} defaultView="year"></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with value and calendar overlay in year view"
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args} defaultView="year" value={initialValue}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with invalid value"
    play={async () => {
      await userEvent.type(getDatePicker(), '41412041');
    }}
  >
    {(args) => <DatePicker {...args}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with invalid value and calendar overlay"
    parameters={disableDynamicSnapshot}
    play={async () => {
      await userEvent.type(getDatePicker(), '41412041');
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with value before min date and calendar overlay"
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args} value={invalidMinDate}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DatePicker with value after max date and calendar overlay"
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <DatePicker {...args} value={invalidMaxDate}></DatePicker>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Handling State and Events DatePicker">
    {(args) => {
      const [dateValue, setDateValue] = React.useState(null);
      const [status, setStatus] = React.useState('default');
      const [errorMessage, setErrorMessage] = React.useState('');
      const [errorMessage2, setErrorMessage2] = React.useState('');
      const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
      const handleChange = (displayValue, newValue) => {
        setHasDisplayValue(!!displayValue);
        if (newValue) {
          const newDate = new Date(newValue);
          newDate.setFullYear(newValue.getFullYear());
          setDateValue(newDate);
        } else {
          setDateValue(undefined);
        }
      };
      const handleBlur = (e) => {
        if (!dateValue && hasDisplayValue) {
          setStatus('error');
          setErrorMessage('This is a status message');
        } else {
          setStatus('default');
        }
      };
      return (
        <DatePicker
          {...args}
          value={dateValue}
          status={status}
          statusMessage={errorMessage}
          onBlur={handleBlur}
          onChange={handleChange}
        />
      );
    }}
  </Story>
</Canvas>
