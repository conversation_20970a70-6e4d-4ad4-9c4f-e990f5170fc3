import React, { useRef, useState } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { act, renderHook } from '@testing-library/react-hooks';
import userEvent from '@testing-library/user-event';

import { DatePicker, IDatePickerMethods } from './date-picker';
import { mockResizeObserver } from '../../../test-utils';
import { TDate } from '../../../types';
import { generateId } from '../../../utils';
import { startOfDay } from '../../../utils/date-utils';
import { Button } from '../../button';
import { IDateTimeFormatPart } from '../date-field-helpers';

const locale = 'en-US';
const id = 'datepicker';
const testId = 'datepicker-test-id';
const label = 'Select Date';
const value = undefined;
const disabled = false;
const required = false;
const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};
const weekdays = [
  { short: 'Su', long: 'Sunday' },
  { short: 'Mo', long: 'Monday' },
  { short: 'Tu', long: 'Tuesday' },
  { short: 'We', long: 'Wednesday' },
  { short: 'Th', long: 'Thursday' },
  { short: 'Fr', long: 'Friday' },
  { short: 'Sa', long: 'Saturday' },
];
const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const disableDefaultToday = false;
const initialValue = new Date('November 15, 2022'); // Nov 30, 2022
const initialDay = '15';
const initialMonth = '11';
const initialYear = '2022';
const minDate = new Date('01/01/2010');
const maxDate = new Date('12/31/2030');
const statusMessage = 'This is a status message';
const statusMessagePrefix = 'Prefix:';
const helperText = 'within 60 days from today';
const helperTextPrefix = 'Recommended:';
const textMap = {
  ariaLabel: 'datepicker aria label',
  calendarAriaLabel: 'choose date',
  clearButtonAriaLabel: 'Clear input',
  dayLabel: 'dayLabel',
  monthLabel: 'monthLabel',
  yearLabel: 'yearLabel',
  formatLabel: 'format',
  expectedDateFormatLabel: 'Expected date format:',
  blank: 'blank',
  nextMonthButtonLabel: 'next month',
  previousMonthButtonLabel: 'previous month',
  previousViewIsYearLabel: 'go to year view',
  previousViewIsMonthLabel: 'go to month view',
};
const formatDateForSR = jest.fn();
const onChange = jest.fn();
const onClear = jest.fn();
const onFocus = jest.fn();
const onBlur = jest.fn();

const getLabel = () => screen.queryByText(label);
const getDatePicker = () => screen.getByTestId(`${testId}-date-field`);
const getYearSegment = () => screen.getByRole('textbox', { name: /yearLabel/i });
const getDaySegment = () => screen.getByRole('textbox', { name: /dayLabel/i });
const getMonthSegment = () => screen.getByRole('textbox', { name: /monthLabel/i });
const getClearButton = () => screen.getByRole('button', { name: textMap.clearButtonAriaLabel });
const queryClearButton = () => screen.queryByRole('button', { name: textMap.clearButtonAriaLabel });
const getCalendarIcon = () => screen.getByTestId(`${testId}-date-field-calendar-icon-button`);
const getSelectedValue = (date: TDate, view: string) =>
  document.getElementById(`${id}-calendar-${view}-${startOfDay(date).valueOf()}`);

const dateTimeParts = () => {
  const dateTimeParts: IDateTimeFormatPart[] = [];
  const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
  for (const part of parts) {
    dateTimeParts.push({ ...part, id: generateId() });
  }
  return dateTimeParts;
};

const handleEscape = jest.fn();

const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
  if (event.nativeEvent.key === 'Escape') {
    handleEscape();
  }
};

const propsWithoutLabel = {
  id,
  testId,
  value,
  disabled,
  required,
  dateTimeParts: dateTimeParts(),
  dateSegmentPlaceholder,
  weekdays,
  months,
  disableDefaultToday,
  minDate,
  maxDate,
  statusMessage,
  statusMessagePrefix,
  helperText,
  helperTextPrefix,
  textMap,
  formatDateForSR,
  onChange,
  onClear,
  onFocus,
  onBlur,
};

const defaultProps = {
  ...propsWithoutLabel,
  label,
};

const variants = {
  datePickerDefault: {
    name: 'Default DatePicker',
    jsx: <DatePicker {...defaultProps}></DatePicker>,
  },
  datePickerWithoutLabel: {
    name: 'DatePicker without label',
    jsx: <DatePicker {...propsWithoutLabel}></DatePicker>,
  },
  datePickerWithIntialDateValue: {
    name: 'DatePicker with initial date value',
    jsx: <DatePicker {...defaultProps} value={initialValue}></DatePicker>,
  },
  datePickerWithHelperText: {
    name: 'DatePicker with helper text',
    jsx: <DatePicker {...defaultProps} helperText={helperText}></DatePicker>,
  },
  datePickerWithErrorMessage: {
    name: 'DatePicker with error message',
    jsx: (
      <DatePicker
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></DatePicker>
    ),
  },
  datePickerDisabled: {
    name: 'Disabled DatePicker',
    jsx: <DatePicker {...defaultProps} disabled></DatePicker>,
  },
  datePickerWithinParent: {
    name: 'DatePicker within parent',
    jsx: (
      <div data-testid="datepicker-parent" tabIndex={0} onKeyDown={handleKeyDown}>
        <DatePicker {...defaultProps}></DatePicker>
      </div>
    ),
  },
};

const renderControlledDatePicker = (initialValue: TDate) => {
  const onChange = jest.fn().mockImplementation((setControlledValue, newValue?: TDate) => {
    act(() => setControlledValue(newValue));
  });
  const ControlledDatePickerWrapper = () => {
    const [controlledValue, setControlledValue] = useState<TDate | undefined>(initialValue);
    const datePickerRef = useRef<IDatePickerMethods>(null);
    const onFocusInput = jest.fn().mockImplementation(() => {
      datePickerRef?.current?.focus();
    });
    const onValueClear = jest.fn().mockImplementation(() => {
      datePickerRef?.current?.clear();
      act(() => {
        setControlledValue(undefined);
      });
    });
    return (
      <>
        <Button id="clear-value-button" label="Clear value" onClick={onValueClear} />
        <Button id="focus-input-button" label="Focus input" onClick={onFocusInput} />
        <DatePicker
          {...defaultProps}
          ref={datePickerRef}
          value={controlledValue}
          onClear={onClear}
          onChange={(newValue) => onChange(setControlledValue, newValue)}
        />
      </>
    );
  };

  render(<ControlledDatePickerWrapper />);
};

describe('[DatePicker]', () => {
  /**
   * Fix TypeError: window.ResizeObserver is not a constructor issue
   * mock out the basic API (observe, unobserve, disconnect) and
   * use jest.fn() to return particular mock entries in the test.
   * https://github.com/maslianok/react-resize-detector/issues/145
   * https://github.com/que-etc/resize-observer-polyfill/issues/50
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { ResizeObserver } = window;

  beforeEach(() => {
    mockResizeObserver();
  });

  afterEach(() => {
    window.ResizeObserver = ResizeObserver;
    jest.restoreAllMocks();
  });

  it('should show label', () => {
    render(variants.datePickerDefault.jsx);
    expect(getDatePicker()).toBeInTheDocument();
    expect(getLabel()).toBeInTheDocument();
  });

  it('should render without label', () => {
    render(variants.datePickerWithoutLabel.jsx);
    expect(getDatePicker()).toBeInTheDocument();
    expect(getLabel()).not.toBeInTheDocument();
  });

  it('should render segment placeholders', () => {
    render(variants.datePickerDefault.jsx);
    expect(getYearSegment()).toHaveTextContent(dateSegmentPlaceholder.year);
    expect(getMonthSegment()).toHaveTextContent(dateSegmentPlaceholder.month);
    expect(getDaySegment()).toHaveTextContent(dateSegmentPlaceholder.day);
  });

  it('should render initial value and clear button', () => {
    render(variants.datePickerWithIntialDateValue.jsx);
    expect(getYearSegment()).toHaveTextContent(initialYear);
    expect(getMonthSegment()).toHaveTextContent(initialMonth);
    expect(getDaySegment()).toHaveTextContent(initialDay);
    expect(getClearButton()).toBeInTheDocument();
    expect(getClearButton()).toHaveAttribute('aria-label', textMap.clearButtonAriaLabel);
    expect(getLabel()).toBeInTheDocument();
  });

  it('should render calendar icon', () => {
    render(variants.datePickerDefault.jsx);
    expect(getCalendarIcon()).toBeInTheDocument();
    expect(getCalendarIcon()).toHaveAttribute('aria-label', textMap.calendarAriaLabel);
  });

  it('should render calendar icon with correct ariaLabel when value selected', () => {
    const formatDateForSR = (value: Date) => {
      return value;
    };
    const ControlledDatePicker = (props?: any) => {
      return (
        <DatePicker {...defaultProps} value={props.value} formatDateForSR={props.formatDateForSR} onClear={onClear} />
      );
    };
    render(
      <ControlledDatePicker
        value={initialValue}
        onChange={onChange}
        formatDateForSR={() => formatDateForSR(initialValue)}
      />
    );
    expect(getCalendarIcon()).toBeInTheDocument();
    expect(getCalendarIcon()).toHaveAttribute(
      'aria-label',
      `${formatDateForSR(initialValue)}, ${textMap.calendarAriaLabel}`
    );
  });

  it('should render helper text when provided', () => {
    render(variants.datePickerWithHelperText.jsx);
    expect(screen.getByText(helperText)).toBeInTheDocument();
  });

  it('should render error status message when provided', () => {
    render(variants.datePickerWithErrorMessage.jsx);
    expect(screen.getByText(statusMessage)).toBeInTheDocument();
  });

  it('should not trigger onSubmit when clicking calendar button', async () => {
    const handleSubmit = jest.fn((e) => e.preventDefault());
    render(
      <form onSubmit={handleSubmit}>
        <DatePicker {...defaultProps} />
        <button type="submit">Submit</button>
      </form>
    );

    // Interact with DatePicker
    await act(async () => await userEvent.click(getCalendarIcon()));
    expect(getCalendarIcon()).toBeInTheDocument();
    await act(async () => await userEvent.click(getSelectedValue(initialValue, 'day') as HTMLElement));

    expect(handleSubmit).not.toHaveBeenCalled(); // onSubmit should not be called

    // Submit the form
    const submitButton = screen.getByRole('button', { name: 'Submit' });
    await act(async () => await userEvent.click(submitButton));
    expect(handleSubmit).toHaveBeenCalledTimes(1); // onSubmit should be called once
  });

  it('should not trigger onSubmit when clicking clear button', async () => {
    const handleSubmit = jest.fn((e) => e.preventDefault());
    render(
      <form onSubmit={handleSubmit}>
        <DatePicker {...defaultProps} value={initialValue} />
        <button type="submit">Submit</button>
      </form>
    );
    // Ensure the clear button appears
    expect(getClearButton()).toBeInTheDocument();

    await act(async () => await userEvent.click(getClearButton()));
    expect(handleSubmit).not.toHaveBeenCalled(); // onSubmit should not be called

    const submitButton = screen.getByRole('button', { name: 'Submit' });
    await act(async () => await userEvent.click(submitButton));
    expect(handleSubmit).toHaveBeenCalledTimes(1); // onSubmit should be called once
  });

  it('should focus date field input after calling focus from ref', async () => {
    renderControlledDatePicker(initialValue);

    expect(getYearSegment()).toHaveTextContent(initialYear);
    expect(getMonthSegment()).toHaveTextContent(initialMonth);
    expect(getDaySegment()).toHaveTextContent(initialDay);
    expect(getClearButton()).toBeInTheDocument();
    expect(getYearSegment()).not.toHaveFocus();
    expect(getMonthSegment()).not.toHaveFocus();
    expect(getDaySegment()).not.toHaveFocus();

    const externalFocusButton = screen.getByRole('button', { name: 'Focus input' });
    expect(externalFocusButton).toBeInTheDocument();
    await act(async () => await userEvent.click(externalFocusButton));

    expect(getYearSegment()).toHaveTextContent(initialYear);
    expect(getMonthSegment()).toHaveTextContent(initialMonth);
    expect(getDaySegment()).toHaveTextContent(initialDay);
    expect(getClearButton()).toBeInTheDocument();
    expect(getYearSegment()).not.toHaveFocus();
    expect(getMonthSegment()).toHaveFocus();
    expect(getDaySegment()).not.toHaveFocus();
  });

  it('should propogate ESC keyboard event to parent when date field is closed', async () => {
    render(variants.datePickerWithinParent.jsx);

    const parentDiv = screen.getByTestId('datepicker-parent');
    parentDiv.focus();

    await userEvent.keyboard('{Escape}');
    await waitFor(() => {
      expect(handleEscape).toHaveBeenCalledTimes(1);
    });
  });

  it('should not propagate ESC keyboard event to parent when date field is open', async () => {
    render(variants.datePickerWithinParent.jsx);

    const parentDiv = screen.getByTestId('datepicker-parent');
    parentDiv.focus();

    await act(async () => {
      await userEvent.click(getCalendarIcon());
    });

    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });

    await waitFor(() => {
      expect(handleEscape).toHaveBeenCalledTimes(1);
    });
  });

  describe('clear button functionality', () => {
    beforeEach(onClear.mockReset);

    it('should trigger onClear if valid Date value and display value present', async () => {
      const { result } = renderHook((value?: Date) => {
        const [controlledValue, setControlledValue] = useState<Date | undefined>(value || undefined);
        return { controlledValue, setControlledValue };
      });
      const ControlledDatePicker = (props?: any) => {
        return <DatePicker {...defaultProps} value={props.value} onClear={onClear} />;
      };
      const { rerender } = render(<ControlledDatePicker value={initialValue} onChange={onChange} />);
      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      // Trigger onClear button
      await act(async () => await userEvent.click(getClearButton()));

      rerender(<ControlledDatePicker value={result.current.controlledValue} />);
      expect(getYearSegment()).toHaveTextContent(dateSegmentPlaceholder.year);
      expect(getMonthSegment()).toHaveTextContent(dateSegmentPlaceholder.month);
      expect(getDaySegment()).toHaveTextContent(dateSegmentPlaceholder.day);
      expect(queryClearButton()).not.toBeInTheDocument();
      expect(onClear).toHaveBeenCalledTimes(1);
    });

    it('should trigger onClear input if display value present but Date value is invalid', async () => {
      const { result } = renderHook((value?: Date) => {
        const [controlledValue, setControlledValue] = useState<Date | undefined>(value || undefined);
        return { controlledValue, setControlledValue };
      });
      const ControlledDatePicker = (props?: any) => {
        return <DatePicker {...defaultProps} value={props.value} onClear={onClear} />;
      };
      const { rerender } = render(<ControlledDatePicker value={initialValue} onChange={onChange} />);
      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(getMonthSegment());
        await userEvent.keyboard('{Backspace}{Backspace}');
      });

      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(dateSegmentPlaceholder.month);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      // Trigger onClear button
      await act(async () => await userEvent.click(getClearButton()));

      rerender(<ControlledDatePicker value={result.current.controlledValue} />);
      expect(getYearSegment()).toHaveTextContent(dateSegmentPlaceholder.year);
      expect(getMonthSegment()).toHaveTextContent(dateSegmentPlaceholder.month);
      expect(getDaySegment()).toHaveTextContent(dateSegmentPlaceholder.day);
      expect(queryClearButton()).not.toBeInTheDocument();
      expect(onClear).toHaveBeenCalledTimes(1);
    });

    it('should clear input if a null or undefined value is passed in using an external clear button', async () => {
      renderControlledDatePicker(initialValue);
      expect(getYearSegment()).toHaveTextContent(initialYear);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      const externalClearButton = screen.getByRole('button', { name: 'Clear value' });
      expect(externalClearButton).toBeInTheDocument();
      await act(async () => await userEvent.click(externalClearButton));

      expect(getYearSegment()).toHaveTextContent(dateSegmentPlaceholder.year);
      expect(getMonthSegment()).toHaveTextContent(dateSegmentPlaceholder.month);
      expect(getDaySegment()).toHaveTextContent(dateSegmentPlaceholder.day);
      expect(queryClearButton()).not.toBeInTheDocument();
    });
  });

  describe('onChange event', () => {
    beforeEach(onChange.mockReset);

    it('dispatch onChange event when using calendar to choose a date', async () => {
      render(variants.datePickerWithIntialDateValue.jsx);

      expect(getCalendarIcon()).toBeInTheDocument();
      await act(async () => await userEvent.click(getCalendarIcon()));

      expect(getSelectedValue(initialValue, 'day')).toBeInTheDocument();
      await act(async () => await userEvent.click(getSelectedValue(initialValue, 'day') as HTMLElement));

      await waitFor(() => {
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenLastCalledWith(`${initialMonth}/${initialDay}/${initialYear}`, initialValue);
      });
    });

    it('dispatch onChange event when inputting invalid date into date field', async () => {
      render(variants.datePickerDefault.jsx);

      getDaySegment().innerText = initialDay;
      getDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));

      expect(getDaySegment()).toHaveTextContent(initialDay);

      await waitFor(() => {
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenLastCalledWith(`mm/${initialDay}/yyyy`, undefined);
      });
    });

    it('dispatch onChange event when inputting valid date into date field', async () => {
      render(variants.datePickerDefault.jsx);

      getDaySegment().innerText = initialDay;
      getDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getMonthSegment().innerText = initialMonth;
      getMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getYearSegment().innerText = initialYear;
      getYearSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));

      expect(getDaySegment()).toHaveTextContent(initialDay);
      expect(getMonthSegment()).toHaveTextContent(initialMonth);
      expect(getYearSegment()).toHaveTextContent(initialYear);

      await waitFor(() => {
        expect(onChange).toHaveBeenCalledTimes(3);
        expect(onChange).toHaveBeenLastCalledWith(`${initialMonth}/${initialDay}/${initialYear}`, initialValue);
      });
    });
  });
});
