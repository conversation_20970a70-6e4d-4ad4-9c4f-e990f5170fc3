import { <PERSON>a, Story, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { DatePicker } from './date-picker';
import Examples from './date-picker.examples.mdx';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';

<Meta
  title="Components/Date Picker"
  component={DatePicker}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/proto/0ZDD0G8lcpcEVaCbjjmxvD/branch/WQKAlaU2yr1OAL0Zs22reC/%F0%9F%A7%AA-DatePicker?node-id=3361-4568&scaling=min-zoom&page-id=3361%3A2837',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'A unique id is required for accessibility purpose. ',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'An id used for automation testing.',
    },
    label: {
      type: 'string',
      control: 'text',
      description: 'Specifies the label rendered in date picker.',
    },
    disabled: {
      type: 'boolean',
      description: 'Sets the disabled attribute on date picker.',
      table: {
        defaultValue: { summary: false },
      },
    },
    readOnly: {
      type: 'boolean',
      description: 'Sets the readOnly attribute on date picker.',
      table: {
        defaultValue: { summary: false },
      },
    },
    required: {
      type: 'boolean',
      description: 'Sets the required attribute on date picker.',
      table: {
        defaultValue: { summary: false },
      },
    },
    value: {
      control: 'date',
      description: 'Defines the value of date picker.',
    },
    dateTimeParts: {
      type: 'object',
      control: 'object',
      description: 'Date picker segment parts object.',
    },
    dateSegmentPlaceholder: {
      type: 'object',
      control: 'object',
      description: 'A placeholder for the date input format.',
    },
    defaultView: {
      control: 'radio',
      options: ['year', 'month', 'day'],
      description: 'Sets the default view.',
    },
    minDate: {
      control: 'date',
      description: 'Sets the min date.',
    },
    maxDate: {
      control: 'date',
      description: 'Sets the max date.',
    },
    weekdays: {
      type: 'array',
      control: 'object',
      description: 'Array of type `IWeekday` items used to set weekday header labels. Begins with Sunday as 0.',
    },
    months: {
      type: 'array',
      description: 'Sets calendar months.',
    },
    startDayOfWeek: {
      type: 'enum',
      control: 'select',
      description: 'Sets which day the week will begin with. Sunday is 0.',
    },
    disableDefaultToday: {
      type: 'boolean',
      description: "Determines whether today's date is highlighted in calendar.",
      table: {
        defaultValue: { summary: false },
      },
    },
    status: {
      type: 'enum',
      control: 'radio',
      options: ['default', 'error', 'success'],
      description: 'Sets the status of the date picker.',
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    statusMessage: {
      type: 'string',
      control: 'text',
      description: 'Sets the status message rendered under the date picker.',
    },
    statusMessagePrefix: {
      type: 'string',
      control: 'text',
      description: 'Sets the prefix to the status message rendered under the date picker.',
    },
    helperText: {
      type: 'string',
      control: 'text',
      description: 'Sets the helper text rendered under the date picker.',
    },
    helperTextPrefix: {
      type: 'string',
      control: 'text',
      description: 'Sets the prefix to the helper text rendered under the date picker.',
    },
    textMap: {
      type: 'object',
      control: 'object',
      description: 'Object containing localized text for various elements.',
    },
    formatDateForSR: {
      control: '-',
      description: 'Formats how screen reader should announce the date.',
    },
    onChange: {
      control: '-',
      description: 'Callback when date picker value is changed.',
    },
    onClear: {
      control: '-',
      description: 'Callback when date picker value is cleared.',
    },
    onFocus: {
      control: '-',
      description: 'Callback when date picker is focused.',
    },
    onBlur: {
      control: '-',
      description: 'Callback when date picker is blurred.',
    },
  }}
  args={{
    id: 'datepicker-id',
    testId: 'datepicker-test-id',
    label: 'Select Date',
    disabled: false,
    readOnly: false,
    required: false,
    dateTimeParts: [
      { id: 'segment-month-1', type: 'month', value: '' },
      { id: 'segment-literal-1', type: 'literal', value: '/' },
      { id: 'segment-day-1', type: 'day', value: '' },
      { id: 'segment-literal-2', type: 'literal', value: '/' },
      { id: 'segment-year-1', type: 'year', value: '' },
    ],
    dateSegmentPlaceholder: {
      day: 'dd',
      month: 'mm',
      year: 'yyyy',
    },
    weekdays: [
      { short: 'Su', long: 'Sunday' },
      { short: 'Mo', long: 'Monday' },
      { short: 'Tu', long: 'Tuesday' },
      { short: 'We', long: 'Wednesday' },
      { short: 'Th', long: 'Thursday' },
      { short: 'Fr', long: 'Friday' },
      { short: 'Sa', long: 'Saturday' },
    ],
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    startDayOfWeek: 0,
    disableDefaultToday: false,
    defaultView: 'day',
    minDate: new Date('01/01/2000'),
    maxDate: new Date('12/31/2050'),
    status: 'default',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',
    helperText: 'within 60 days from today',
    helperTextPrefix: 'Recommended:',
    onChange: action('onChange'),
    onClear: action('onClear'),
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    value: undefined,
    textMap: {
      ariaLabel: 'Datepicker aria label',
      calendarAriaLabel: 'choose date',
      clearButtonAriaLabel: 'clear input',
      dayLabel: 'day',
      monthLabel: 'month',
      yearLabel: 'year',
      formatLabel: 'format',
      expectedDateFormatLabel: 'Expected Date Format:',
      requiredLabel: 'required',
      blank: 'blank',
      invalidEntry: 'invalid entry',
      invalidDay: 'invalid day',
      invalidMonth: 'invalid month',
      invalidYear: 'invalid year',
      nextMonthButtonLabel: 'next month',
      previousMonthButtonLabel: 'previous month',
      previousViewIsYearLabel: 'go to year view',
      previousViewIsMonthLabel: 'go to month view',
    },
  }}
/>

# Date Picker

<Examples />

## Live Demo

export const formatDateForSR = (view, value) => {
  switch (view) {
    case 'year':
      return value.toLocaleDateString('en-US', { year: 'numeric' });
    case 'month':
      return value.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
    case 'day':
      return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
  }
};

<Canvas>
  <Story name="Date Picker">
    {(args) => {
      const [{ minDate, maxDate, required}, updateArgs] = useArgs();
      const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
      const handleChange = (displayValue, value) => {
        setHasDisplayValue(!!displayValue);
        if (value) {
          // if Year is less than 100, it will map it to 1901 or 1999
          // using setFullYear for any year less than 100 to work correctly
          const newDate = new Date(value).setFullYear(value.getFullYear());
          updateArgs({value: newDate});
        } else {
          updateArgs({value: null})
        }

        if (value && value >= minDate && value <= maxDate) {
          updateArgs({status: 'default'});
        } else if (!value && !required) {
          updateArgs({status: 'default'});
        } else {
          updateArgs({ status: 'error', statusMessagePrefix: 'Error: ', statusMessage: 'Please enter a valid date.' });
        }
        args.onChange?.(displayValue, value);
      };
      const handleBlur = (e) => {
        if (!args.value && hasDisplayValue) {
          updateArgs({ status: 'error' });
        } else if (!args.value && args.required) {
          updateArgs({ status: 'error', statusMessagePrefix: 'Error: ', statusMessage: 'This field is required.' });
        }
        args.onBlur?.(e);
      };
      const handleClear = () => {
        args.onClear?.();
        updateArgs({ value: undefined });
      };
      React.useEffect(() => {
        if (args.value && (args.value < minDate || args.value > maxDate)) {
          updateArgs({ status: 'error', statusMessagePrefix: 'Error: ', statusMessage: 'Please enter a valid date.' });
        } else {
          updateArgs({ status: 'default' });
        }
      }, [minDate, maxDate, args.value]);
      return (
        <DatePicker
          {...args}
          onChange={handleChange}
          formatDateForSR={formatDateForSR}
          onClear={handleClear}
          onBlur={handleBlur}
        />
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="Date Picker" />
