import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';

import { IWeekday, TCalendarView, TDate, TStartDayOfWeekRange } from '../../../types';
import { getDate, getMonth, getYear } from '../../../utils/date-utils';
import { FocusTrap } from '../../../utils/focus-trap';
import { useComponentFocused } from '../../../utils/use-component-focused';
import { Calendar } from '../../calendar';
import { FormFieldContainerContext, TFormFieldStatus } from '../../form-field-container';
import { TriggerAreaStyledOverlay } from '../../trigger-area-styled-overlay';
import { DateField, IDateFieldMethods } from '../date-field';
import { DateFieldContext } from '../date-field-context';
import { getDisplayText, IDateSegmentPlaceholder, IDateTimeFormatPart } from '../date-field-helpers';

import styles from './date-picker.module.scss';

export interface IDatePickerTextMap {
  ariaLabel?: string;
  clearButtonAriaLabel: string;
  calendarAriaLabel: string;
  dayLabel: string;
  monthLabel: string;
  yearLabel: string;
  formatLabel: string;
  expectedDateFormatLabel: string;
  blank: string;
  requiredLabel?: string;
  invalidEntry?: string;
  invalidDay?: string;
  invalidMonth?: string;
  invalidYear?: string;
  nextMonthButtonLabel: string;
  previousMonthButtonLabel: string;
  previousViewIsYearLabel: string;
  previousViewIsMonthLabel: string;
}

export interface IDatePickerProps {
  id: string;
  testId?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  label?: string;
  value?: TDate;
  dateTimeParts: IDateTimeFormatPart[];
  dateSegmentPlaceholder: IDateSegmentPlaceholder;
  defaultView?: TCalendarView;
  minDate?: TDate;
  maxDate?: TDate;
  weekdays: IWeekday[];
  months: string[];
  startDayOfWeek?: TStartDayOfWeekRange;
  disableDefaultToday?: boolean;
  status?: TFormFieldStatus;
  statusMessage?: string;
  statusMessagePrefix?: string;
  helperText?: string;
  helperTextPrefix?: string;
  textMap: IDatePickerTextMap;
  formatDateForSR: (view: TCalendarView, value?: TDate) => string;
  onChange?: (displayValue: string, value?: Date) => void;
  onClear?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
}
export type IDatePickerMethods = Omit<IDateFieldMethods, '_focusOnMouseXPosition'>;

export const DatePicker = forwardRef<IDatePickerMethods, IDatePickerProps>((props: IDatePickerProps, ref) => {
  const {
    id,
    testId,
    disabled,
    readOnly,
    required,
    label,
    value,
    dateTimeParts,
    dateSegmentPlaceholder,
    status = 'default',
    statusMessage,
    statusMessagePrefix,
    helperText,
    helperTextPrefix,
    textMap,
    defaultView,
    minDate,
    maxDate,
    weekdays,
    months,
    startDayOfWeek,
    disableDefaultToday,
    onChange,
    onClear,
    onFocus,
    onBlur,
    formatDateForSR,
  } = props;

  const {
    ariaLabel,
    clearButtonAriaLabel,
    calendarAriaLabel,
    requiredLabel,
    dayLabel,
    monthLabel,
    yearLabel,
    formatLabel,
    expectedDateFormatLabel,
    blank,
    invalidEntry,
    invalidDay,
    invalidMonth,
    invalidYear,
    nextMonthButtonLabel,
    previousMonthButtonLabel,
    previousViewIsYearLabel,
    previousViewIsMonthLabel,
  } = textMap;

  const dateFieldRef = useRef<HTMLElement>(null);
  const dateFieldMethodsRef = useRef<IDateFieldMethods>(null);
  const calendarRef = useRef<HTMLElement>(null);
  const componentType = useRef('date-picker');
  const mouseEventTargetRef = useRef<{ target: EventTarget; clientX: number } | null>(null);

  const [overlayContentHeight] = useState(0);
  const [datePickerValue, setDatePickerValue] = useState(value);
  const [calendarOpen, setCalendarOpen] = useState(false);
  const [isKeyboardInput, setIsKeyboardInput] = useState(false);
  const [datefieldCanPositionCursor, setDatefieldCanPositionCursor] = useState(true);
  const [isFocusWithinComponent, setIsFocusWithinComponent] = useState(false);

  const calendarIconButtonAriaLabel = useMemo(() => {
    const stringArray = [];
    if (value) stringArray.push(formatDateForSR('day', value));
    stringArray.push(calendarAriaLabel);
    return stringArray.join(', ');
  }, [value, calendarAriaLabel, formatDateForSR]);

  const handleOnKeyDownContainer = (e: React.KeyboardEvent) => {
    if (disabled || readOnly) return;
    const htmlEventTarget = e.target as HTMLDivElement;

    switch (e.key) {
      case 'Escape': {
        // let esc key bubble up to close parent component
        if (calendarOpen) {
          e.stopPropagation();
        }
        e.preventDefault();
        calendarRef.current?.contains(htmlEventTarget) && calendarOpen && setCalendarOpen(false);
        setIsKeyboardInput(true);
        break;
      }
      case 'Enter':
      case ' ': {
        const calendarHeader = document.getElementById(`${id}-calendar-header`);
        const isCalendarDay =
          calendarRef.current?.contains(htmlEventTarget) && !calendarHeader?.contains(htmlEventTarget);
        if (calendarOpen && isCalendarDay) {
          e.stopPropagation();
          e.preventDefault();
          setCalendarOpen(false);
          setIsKeyboardInput(true);
        }
        break;
      }
      case 'ArrowDown': {
        e.stopPropagation();
        e.preventDefault();
        setCalendarOpen(true);
        setIsKeyboardInput(true);
        break;
      }
      default:
        break;
    }
  };

  const handleOnClickContainer = (e: React.MouseEvent) => {
    mouseEventTargetRef.current = { target: e.target, clientX: e.clientX };
    const calendarIcon = document.getElementById(`${id}-date-field-calendar-icon-button`) as HTMLElement;
    if (disabled || readOnly) return;
    if (
      (!calendarOpen && calendarIcon?.contains(e.target as HTMLElement)) ||
      calendarRef?.current?.contains(e.target as HTMLElement)
    ) {
      setCalendarOpen(true);
    } else {
      setCalendarOpen(false);
    }
    setIsKeyboardInput(false);
  };

  const handleOnCalendarIconKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      setCalendarOpen((prev) => !prev);
    }
  };

  const handleCalendarChange = (value?: TDate) => {
    if (value) {
      const date = value instanceof Date ? value : new Date(value);
      const displayValue = getDisplayText(
        dateTimeParts,
        getDate(date).toString(),
        (getMonth(date) + 1).toString(),
        getYear(date).toString(),
        dateSegmentPlaceholder
      );
      onChange?.(displayValue, date);
    } else {
      onChange?.('', undefined);
    }
  };

  const handleClear = () => {
    onClear?.();
    dateFieldMethodsRef.current?.clear();
  };

  useImperativeHandle(ref, () => ({
    clear: () => {
      dateFieldMethodsRef.current?.clear();
    },
    focus: () => {
      dateFieldMethodsRef.current?.focus();
    },
  }));

  useEffect(() => {
    if (isKeyboardInput && !calendarOpen) {
      const calendarIcon = document.getElementById(`${id}-date-field-calendar-icon-button`);
      calendarIcon?.focus();
      setDatefieldCanPositionCursor(false);
    } else if (!isKeyboardInput && !calendarOpen && mouseEventTargetRef.current) {
      dateFieldMethodsRef.current?._focusOnMouseXPosition(mouseEventTargetRef.current.clientX);
      setDatefieldCanPositionCursor(true);
    }
  }, [isKeyboardInput, id, calendarOpen]);

  useEffect(() => {
    setDatePickerValue(value);
    setCalendarOpen(false);
  }, [value]);

  useEffect(() => {
    setCalendarOpen(false);
  }, [disabled, readOnly]);

  const getOverlayHeight = useCallback(() => {
    if (!overlayContentHeight) return '';
    const borderSize = status === 'error' ? 'var(--evr-border-width-thick-px)' : 'var(--evr-border-width-thin-px)';
    return `calc(${overlayContentHeight}px + (${borderSize} * 3))`; // total 3 => 2 for the borderContainer borders and 1 for the FormFieldContainer bottomBorder
  }, [overlayContentHeight, status]);

  useComponentFocused(
    [dateFieldRef, calendarRef],
    // eventListener types
    ['mousedown', 'keyup'],
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      onFocus?.();
      setIsFocusWithinComponent(true);
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      setIsFocusWithinComponent(false);
      setCalendarOpen(false);
      onBlur?.();
    }
  );

  const formFieldContainerContext = useMemo(
    () => ({
      label,
      disabled,
      readOnly,
      required,
      status,
      helperText,
      helperTextPrefix,
      statusMessage,
      statusMessagePrefix,
    }),
    [label, disabled, readOnly, required, status, helperText, helperTextPrefix, statusMessage, statusMessagePrefix]
  );

  // override the default overlay style
  const overlayStyle: React.CSSProperties = {
    borderRadius: 'var(--evr-radius-2xs)',
    backgroundColor: 'var(--evr-surfaces-primary-default)',
    position: 'absolute',
    boxSizing: 'border-box',
  };

  const datePickerContext = useMemo(
    () => ({
      type: componentType.current,
      getDatefieldCanPositionCursor: () => datefieldCanPositionCursor,
      setDatefieldCanPositionCursor: (val: boolean) => {
        setDatefieldCanPositionCursor(val);
      },
    }),
    [componentType, datefieldCanPositionCursor, setDatefieldCanPositionCursor]
  );

  return (
    <FormFieldContainerContext.Provider value={formFieldContainerContext}>
      <div
        id={id}
        className={styles.evrDatePicker}
        aria-label={ariaLabel}
        onKeyDown={handleOnKeyDownContainer}
        onClick={handleOnClickContainer}
      >
        <TriggerAreaStyledOverlay
          id={`${id}-trigger-area-overlay`}
          triggerRef={dateFieldRef}
          overlayVisible={calendarOpen}
          overlayHeight={getOverlayHeight()}
          overlayWidth={'auto'}
          offset={{ horizontal: '0', vertical: '4px' }} // var(--evr-spacing-2xs)
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          overlayStyle={overlayStyle}
          error={status === 'error'}
          overrideFocusRingDefaultBehavior={!isFocusWithinComponent}
          renderTriggerAreaContent={() => (
            <div
              className={classnames(styles.evrOverlayContainer, {
                [styles.error]: status === 'error',
              })}
            >
              <DateFieldContext.Provider value={datePickerContext}>
                <DateField
                  dateFieldRef={dateFieldRef}
                  id={`${id}-date-field`}
                  testId={testId ? `${testId}-date-field` : undefined}
                  label={label}
                  value={datePickerValue as Date}
                  disabled={disabled}
                  readOnly={readOnly}
                  required={required}
                  iconName={'calendar'}
                  dateTimeParts={dateTimeParts}
                  dateSegmentPlaceholder={dateSegmentPlaceholder}
                  status={status}
                  statusMessage={statusMessage}
                  statusMessagePrefix={statusMessagePrefix}
                  helperText={helperText}
                  helperTextPrefix={helperTextPrefix}
                  onChange={onChange}
                  onClear={handleClear}
                  formatDateTimeForSR={(val) => formatDateForSR('day', val)}
                  textMap={{
                    clearButtonAriaLabel,
                    iconAriaLabel: calendarIconButtonAriaLabel,
                    dayLabel,
                    monthLabel,
                    yearLabel,
                    formatLabel,
                    expectedDateFormatLabel,
                    blank,
                    required: requiredLabel,
                    invalidEntry,
                    invalidDay,
                    invalidMonth,
                    invalidYear,
                  }}
                  onIconKeyDown={handleOnCalendarIconKeyDown}
                  ref={dateFieldMethodsRef}
                />
              </DateFieldContext.Provider>
            </div>
          )}
          renderOverlayContent={() => (
            <FocusTrap
              onActivation={() => {
                mouseEventTargetRef.current = null;
              }}
              // eslint-disable-next-line jsx-a11y/no-autofocus
              autoFocus={false}
            >
              <Calendar
                ref={calendarRef}
                id={`${id}-calendar`}
                testId={testId ? `${testId}-calendar` : undefined}
                defaultView={defaultView}
                value={datePickerValue}
                minDate={minDate}
                maxDate={maxDate}
                weekdays={weekdays}
                months={months}
                startDayOfWeek={startDayOfWeek}
                disableDefaultToday={disableDefaultToday}
                onValueChange={handleCalendarChange}
                formatDateForSR={formatDateForSR}
                textMap={{
                  ariaLabel: calendarAriaLabel,
                  nextMonthButtonLabel,
                  previousMonthButtonLabel,
                  previousViewIsYearLabel,
                  previousViewIsMonthLabel,
                }}
              />
            </FocusTrap>
          )}
        />
      </div>
    </FormFieldContainerContext.Provider>
  );
});

DatePicker.displayName = 'DatePicker';
