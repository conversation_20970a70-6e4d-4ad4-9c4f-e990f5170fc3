# DateRangeField

## Summary

Research and document implementations for the **Everest DateRangeField**.

- Start Date: 2025-01-03
- Figma link: https://www.figma.com/design/EruqEEN3NPLA8MdvaJ4h8k/%F0%9F%A7%AA-Date-Range-Picker?node-id=4376-93384&t=iSAeGNx1vAlQKslQ-0

## Detailed Design

The **DateRangeField** will attempt to make use of the existing implementation of **DateField** in order to keep the two components as closely related as possible.

Changes to the implementation include:

- Updating the `value` prop to accept an object containing two optional properties

  - `start`: the beginning date of the range
  - `end`: the end date of the range

  - ```typescript
    interface IDateRangeFieldValue {
      start?: Date;
      end?: Date;
    }
    ```

- `onChange` callback parameters have been updated to reflect the need for both start and end values.

## API

1. **testId**: `undefined | string`  
   Optional. Sets **data-test-id** attribute on the html element.
1. **id**: `string`  
   Required. Sets **id** attribute on the html element.
1. **dateSegmentPlaceholder** `IDateSegmentPlaceholder`
   Required. A placeholder for the date input format.
1. **dateTimeParts** `IDateTimeFormatPart[]`
   Required. DateRangeField segment parts object.
1. **formatDateTimeForSR** `(value: Date) => string`
   Required. Formats how screen reader should announce the date range.
1. **textMap** `IDateRangeFieldTextMap`
   Required. Interface of text labels for the DateRangeField parts.
1. **disabled**: `boolean`  
   Optional. Sets the `disabled` attribute on the date range field.
1. **readOnly** `boolean`
   Optional. Sets the readOnly attribute on the date range field.
1. **required**: `boolean`  
   Optional. Sets the `required` attribute on the date range field. Adds the asterisk to the **label** / **placeholder**.
1. **label**: `undefined | string`  
   Optional. User provided **label**. If not provided, no **label** is rendered. When **label** is set, automatically set `htmlFor` to the date range field **id**.
1. **iconName** `undefined | TIconName`
   Optional. Sets the name of the icon.
1. **helperTextPrefix**: `undefined | string`
   Optional. User provided prefix of helper Text.
1. **helperText**: `undefined | string`  
   Optional. User provided help text. When set, the date range field's **ariadescribedby** will be set to **helperText** **id**
1. **status**: `TDateFieldStatus`.
   Optional. Indicates the state of the date range field.
1. **statusMessagePrefix**: `undefined | string`
   Optional. User provided prefix of status message.
1. **statusMessage**: `undefined | string`
   Optional. User provided status message. When set, the date range field's **ariadescribedby** will be set to **statusMessage** **id**, this will override **helperText**. This will be used only when **status** is set to "error" or "success".
1. **value**: `IDateRangeFieldValue`
   Optional. Object containing the start and end dates of the range.
1. **onChange**: `undefined | (displayValue: IDateRangeFieldDisplayValue, value?: IDateRangeFieldValue) => void`  
   Optional. Callback when date range field value changes.
1. **onFocus**: `undefined | () => void`  
   Optional. Callback on focus.
1. **onBlur**: `undefined | () => void`  
   Optional. Callback on blur.
1. **onIconKeyDown** `undefined | ((e: KeyboardEvent<Element>) => void)`
   Optional. Callback when a key is pressed on the icon.
1. **onIconClick** `undefined | ((e: MouseEvent<Element, MouseEvent>) => void)`
   Optional. Callback when the icon is clicked.
1. **onClick** `undefined | ((event: MouseEvent<Element, MouseEvent>) => void)`
   Optional. Callback when the user clicks the date range field.
1. **onClear** `undefined | () => void`
   Optional. Callback when user clears the date range field.
1. **dateFieldRef** `undefined | ReactRef`
   Optional. Reference to the **DateRangeField** container.

#### IDateSegmentPlaceholder

```typescript
interface IDateSegmentPlaceholder {
  day: string;
  month: string;
  year: string;
}
```

#### IDateTimeFormatPart[]

```typescript
interface IDateTimeFormatPart {
  id: string;
  type: Intl.DateTimeFormatPartTypes;
  value: string;
}
```

#### IDateRangeFieldTextMap

```typescript
interface IDateRangeFieldTextMap {
  ariaLabel?: string;
  clearButtonAriaLabel?: string;
  iconAriaLabel?: string;
  startDayLabel: string;
  startMonthLabel: string;
  startYearLabel: string;
  endDayLabel: string;
  endMonthLabel: string;
  endYearLabel: string;
  formatLabel: string;
  expectedDateFormatLabel: string;
  blank: string;
  required?: string;
  invalidEntry?: string;
  invalidDay?: string;
  invalidMonth?: string;
  invalidYear?: string;
}
```

#### IDateRangeFieldDisplayValue

Interface for providing the display value strings of the start and end dates to the onChange callback

```typescript
export interface IDateRangeFieldDisplayValue {
  start: string;
  end: string;
}
```

#### IDateRangeFieldValue

Interface for providing the date values of the start and end dates to the onChange callback

```typescript
export interface IDateRangeFieldValue {
  start?: Date;
  end?: Date;
}
```

## Usage

DateRangeField doesn't have a datepicker, it is a Form Field without the Calendar overlay. With Calendar Overlay, it is the DateRangePicker component

```typescript
const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};

const getDateTimeParts = () => {
  const dateTimeParts = [];
  const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
  for (let i = 0; i < parts.length; i++) {
    dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
  }
  return dateTimeParts;
};

const formatDateTimeForSR = (value) => {
  return new Intl.DateTimeFormat(locale, { dateStyle: 'full' }).format(value);
};

const textMap = {
  ariaLabel: 'This is an aria label',
  startDayLabel: 'start day',
  startMonthLabel: 'start month',
  startYearLabel: 'start year',
  endDayLabel: 'end day',
  endMonthLabel: 'end month',
  endYearLabel: 'end year',
  formatLabel: 'format',
  expectedDateFormatLabel: 'Expected Date Format:',
  blank: 'blank',
  required: 'required',
  invalidEntry: 'invalid entry',
  invalidDay: 'invalid day',
  invalidMonth: 'invalid month',
  invalidYear: 'invalid year',
};

const [dateValue, setDateValue] = React.useState<IDateRangeFieldValue>({
  start: new Date(2025, 1, 28),
  end: new Date(2025, 4, 17),
});

<DateRangeField
  id="date-range-field"
  label="Appointment date"
  dateSegmentPlaceholder={dateSegmentPlaceholder}
  dateTimeParts={getDateTimeParts()}
  value={dateValue}
  formatDateTimeForSR={formatDateTimeForSR}
  textMap={textMap}
  iconName={'calendar'}
/>;
```

## Accessibility

- DateSegment (ContentEditable)

  - role='textbox'
  - tabIndex
  - aria-multiline=false
  - aria-label
  - utilizing **DateSegmentContainer** aria-label to handle the screen reader announcer, the `dateFieldAriaLabelValue` would be generated on each date range field and passed down to the **DateSegmentContainer**, and control to be announced for the first focus.
  - Each date range field would handle its own `dateFieldAriaLabelValue` utilizing **DateSegmentContainer** for announcing.

- aria-live (using liveAnnouncer component)

  - When the value has changed, using aria-live to announce the updated value

- using javascript native Intl object to construct date format and date parts based on different cultures/localization for the Screen reader.

Reference:

[React Aria useDateField](https://codesandbox.io/s/small-water-y5dgj4?file=/src/DateField.js) as reference.

Combine with aria-live and [Intl.DateTimeFormat](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat)

## Other Design Systems

**Material UI** - https://mui.com/x/react-date-pickers/getting-started/

- uses an array containing two elements, one for start and another for end

**React Aria** - https://react-spectrum.adobe.com/react-aria/DateRangePicker.html

- uses compound components with two separate `<DateInput />` components supplied as children to `<DateRangePicker />`

**NextUI** - https://nextui.org/docs/components/date-range-picker

- uses an object that contains both start/end date values
  - This approach is the most similar to what we have chosen to go with.

## Required PBIs

1. Create DateRangeField - https://dayforce.atlassian.net/browse/PWEB-17810
   - Create component based on **DateField**
1. Verify and implement a11y requirements of **DateRangeField**
1. Add Playwright, Jest, Chromatic testing for DateRangeField - https://dayforce.atlassian.net/browse/PWEB-17812
   - Update tests from **DateField** to reflect **DateRangeField**
   - This should help verify they maintain the same functionality with the only differences being that there are now two date values
1. Update Storybook docs & examples - https://dayforce.atlassian.net/browse/PWEB-17813
   - Flesh out documentation and examples based on **DateField**
1. Create RefApp example of DateRangeField - https://dayforce.atlassian.net/browse/PWEB-17814
1. Move DateField & DateRangeField to prod - https://dayforce.atlassian.net/browse/PWEB-17815
   - Move **DateField** & **DateRangeField** to ready status
1. Globalize **DateRangeField**
   - Create globalized version of **DateRangeField** to be included in the `everest-dayforce` repository
1. Add globalized **DateRangeField** to ref app

## Changelog

03/11/25 - Moving from alpha to ready status
