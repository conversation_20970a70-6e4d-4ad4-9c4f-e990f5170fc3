@use '../../../variables.scss';

.evrDateRangeField {
  width: 100%;
  display: flex;
  position: relative;
  cursor: text;

  .dateSegmentWrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    padding-block-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    padding-block-end: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    padding-inline-end: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    align-items: center;

    &.disabled {
      cursor: not-allowed;
      background: var(--evr-inactive-surfaces);
      color: var(--evr-inactive-content);
      @media (forced-colors: active) {
        color: GrayText;
      }
    }

    .dateRangeSeparator {
      padding-inline: var(--evr-spacing-2xs);

      &.placeholder {
        color: var(--evr-content-primary-lowemp);
      }
      &.disabled {
        color: var(--evr-inactive-content);
      }
      &.readOnly {
        color: var(--evr-content-primary-default);
      }
    }
  }

  .segment {
    width: 100%;
  }

  .dateSegmentContainer {
    display: flex;
    background-color: var(--evr-surfaces-primary-default);
    border-width: var(--evr-border-width-thin-px);
    border-color: var(--evr-borders-primary-default);
  }

  .dateSegmentContainer:hover {
    border-color: var(--evr-borders-primary-hovered);
  }

  &.error {
    border: var(--evr-border-width-thick-px) solid var(--evr-borders-status-error);
  }

  &.hasMarginAtBottom {
    margin-bottom: var(--evr-spacing-2xs);
  }

  .clearButtonWrapper {
    position: absolute;
    display: flex;
    align-items: center;
    height: 100%;
    inset-inline-end: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    .clearButton {
      display: flex;
      margin-inline-end: var(--evr-spacing-2xs);
    }
  }

  .clearButtonWrapperWithIcon {
    inset-inline-end: calc(
      var(--evr-spacing-lg) + var(--evr-spacing-2xs) + var(--evr-spacing-xs) - var(--evr-border-width-thin-px)
    ); // icons width (32px) + icon padding (8px) + end padding (12px) - border
  }

  .divider {
    display: flex;
    padding-block-start: var(--evr-size-3xs);
    height: var(--evr-size-sm);
  }

  .iconWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    inset-inline-end: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    inset-block-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px) - var(--evr-spacing-3xs));

    &:hover {
      cursor: pointer;
    }
  }

  .overrideIconFill {
    color: var(--evr-content-primary-default);
    fill: var(--evr-content-primary-default);
  }
}
