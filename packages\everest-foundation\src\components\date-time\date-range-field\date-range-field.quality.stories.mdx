import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { <PERSON><PERSON>, DateRangeField } from '../../..';
import { screen, userEvent, waitFor } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../../chromatic';

export const testId = 'daterangefield-test-id';
export const longLabel =
  'This is a very long label. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.';
export const helperText = 'This is some helper text';
export const helperTextPrefix = 'Hint:';
export const statusMessage = 'This is a status message';
export const statusMessagePrefix = 'Prefix:';
export const defaultDateValue = { start: new Date(2022, 11, 31), end: new Date(2023, 11, 31) }; // Dec 31, 2022, Dec 31, 2023
export const dateTimeParts = [
  { id: 'segment-month-1', type: 'month', value: '' },
  { id: 'segment-literal-1', type: 'literal', value: '/' },
  { id: 'segment-day-1', type: 'day', value: '' },
  { id: 'segment-literal-2', type: 'literal', value: '/' },
  { id: 'segment-year-1', type: 'year', value: '' },
];
export const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};
export const textMap = {
  ariaLabel: 'This is an aria label',
  clearButtonAriaLabel: 'clear button aria label',
  iconAriaLabel: 'icon aria label',
  startDayLabel: 'start day',
  startMonthLabel: 'start month',
  startYearLabel: 'start year',
  endDayLabel: 'end day',
  endMonthLabel: 'end month',
  endYearLabel: 'end year',
  formatLabel: 'format',
  expectedDateFormatLabel: 'Expected Date Format:',
  blank: 'blank',
  required: 'required',
  invalidEntry: 'invalid entry',
  invalidDay: 'invalid day',
  invalidMonth: 'invalid month',
  invalidYear: 'invalid year',
};
export const formatDateTimeForSR = () => {};

export const getDateRangeFieldByTestId = () => screen.getByTestId(testId);

<Meta
  title="Testing/Automation Test Cases/Date Range Field"
  component={DateRangeField}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'daterangefield-1',
    testId,
    label: 'This is a label',
    dateTimeParts,
    dateSegmentPlaceholder,
    textMap,
    formatDateTimeForSR,
  }}
/>

# Date Range Field

## Live Demo

<Canvas>
  <Story name="Default">{(args) => <DateRangeField {...args} label={''}></DateRangeField>}</Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField with label">{(args) => <DateRangeField {...args}></DateRangeField>}</Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField with long label">
    {(args) => <DateRangeField {...args} label={longLabel}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField with value">
    {(args) => <DateRangeField {...args} value={defaultDateValue}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField as Required field">
    {(args) => <DateRangeField {...args} required></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField as Required field with value">
    {(args) => <DateRangeField {...args} required value={defaultDateValue}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateRangeField">{(args) => <DateRangeField {...args} disabled></DateRangeField>}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateRangeField with value">
    {(args) => <DateRangeField {...args} disabled value={defaultDateValue}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateRangeField with value and helper text">
    {(args) => <DateRangeField {...args} disabled value={defaultDateValue} helperText={helperText}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled DateRangeField with value and required field">
    {(args) => <DateRangeField {...args} disabled required value={defaultDateValue}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateRangeField">{(args) => <DateRangeField {...args} readOnly></DateRangeField>}</Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateRangeField with value">
    {(args) => <DateRangeField {...args} readOnly value={defaultDateValue}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateRangeField with value and helper text">
    {(args) => <DateRangeField {...args} readOnly value={defaultDateValue} helperText={helperText}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly DateRangeField with value and required field">
    {(args) => <DateRangeField {...args} readOnly required value={defaultDateValue}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField with helper text">
    {(args) => <DateRangeField {...args} helperText={helperText} helperTextPrefix={helperTextPrefix}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField with error message">
    {(args) => (
      <DateRangeField
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></DateRangeField>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField with success message">
    {(args) => (
      <DateRangeField
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></DateRangeField>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateRangeField with invalid month and day field"
    play={async () => {
      await waitFor(() => {
        userEvent.click(getDateRangeFieldByTestId());
        userEvent.type(getDateRangeFieldByTestId(), '1588');
      });
    }}
  >
    {(args) => <DateRangeField {...args}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="DateRangeField with icon">
    {(args) => <DateRangeField {...args} iconName={'calendar'}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateRangeField with clear button"
    play={async () => {
      await waitFor(() => {
        userEvent.click(getDateRangeFieldByTestId());
        userEvent.type(getDateRangeFieldByTestId(), '1122');
      });
    }}
  >
    {(args) => <DateRangeField {...args} onClear={() => {}}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateRangeField with icon and clear button"
    play={async () => {
      await waitFor(() => {
        userEvent.click(getDateRangeFieldByTestId());
        userEvent.type(getDateRangeFieldByTestId(), '1122');
      });
    }}
  >
    {(args) => <DateRangeField {...args} iconName={'calendar'} onClear={() => {}}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateRangeField with Focus Ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <DateRangeField {...args}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="DateRangeField with Focus Ring and value"
    play={async () => {
      await waitFor(() => {
        getDateRangeFieldByTestId();
        userEvent.tab();
      });
    }}
  >
    {(args) => <DateRangeField {...args} value={defaultDateValue}></DateRangeField>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Handling State and Events DateRangeField">
    {(args) => {
      const dateRangeFieldRef = React.useRef(null);
      const [dateValue, setDateValue] = React.useState(null);
      const [status, setStatus] = React.useState('default');
      const [errorMessage, setErrorMessage] = React.useState('');
      const [hasDisplayValue, setHasDisplayValue] = React.useState(false);
      const handleDateValueChange = (displayValue, value) => {
        let newStartDate;
        let newEndDate;
        setHasDisplayValue(!!displayValue?.start && !!displayValue?.end);
        if (value?.start) {
          newStartDate = new Date(value.start);
          // if Year is less than 100, it will map it to 1901 or 1999
          // using setFullYear for any year less than 100 to work correctly
          value.start && newStartDate.setFullYear(value.start.getFullYear());
        }
        if (value?.end) {
          newEndDate = new Date(value.end);
          // if Year is less than 100, it will map it to 1901 or 1999
          // using setFullYear for any year less than 100 to work correctly
          value.end && newEndDate.setFullYear(value.end.getFullYear());
        }
        if (newStartDate && newEndDate) {
          setDateValue({ start: newStartDate, end: newEndDate });
        } else if (!newStartDate && newEndDate) {
          setDateValue({ start: value?.start, end: newEndDate });
        } else if (newStartDate && !newEndDate) {
          setDateValue({ start: newStartDate, end: value?.end });
        } else {
          setDateValue(null);
        }
      };
      const handleBlur = (e) => {
        if (!dateValue && hasDisplayValue) {
          setStatus('error');
          setErrorMessage('This is a status message');
        } else {
          setStatus('default');
        }
      };
      const handleClear = () => {
        dateRangeFieldRef.current && dateRangeFieldRef.current.clear();
        setDateValue(null);
      };
      const handleUpdateClick = () => {
        setDateValue(new Date());
      };
      return (
        <>
          <DateRangeField
            {...args}
            ref={dateRangeFieldRef}
            value={dateValue}
            status={status}
            statusMessage={errorMessage}
            onBlur={handleBlur}
            onChange={handleDateValueChange}
            onClear={handleClear}
          />
          <Button label="Update" onClick={handleUpdateClick} />
        </>
      );
    }}
  </Story>
</Canvas>
