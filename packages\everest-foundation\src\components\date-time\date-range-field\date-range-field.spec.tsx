import React, { createRef, forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { act } from '@testing-library/react-hooks';
import userEvent from '@testing-library/user-event';

import {
  DateRangeField,
  IDateRangeFieldDisplayValue,
  IDateRangeFieldMethods,
  IDateRangeFieldValue,
} from './date-range-field';
import { generateId } from '../../../utils';
import { Button } from '../../button';
import { IDateTimeFormatPart } from '../date-field-helpers';

const locale = 'en-US';
const testId = 'date-range-field-test-id';
const label = 'This is a label';
const id = 'date-range-field-1';
const iconAriaLabel = 'icon label';
const clearButtonAriaLabel = 'Clear input';
const dateSegmentPlaceholder = {
  day: 'dd',
  month: 'mm',
  year: 'yyyy',
};
const initialDay = '31';
const initialMonth = '12';
const startInitialYear = '2022';
const endInitialYear = '2023';
const nextDay = '1';
const nextMonth = '1';
const startNextYear = '2023';
const endNextYear = '2023';
const dayPlaceholder = 'dd';
const monthPlaceholder = 'mm';
const yearPlaceholder = 'yyyy';
const initialDates: IDateRangeFieldValue = {
  start: new Date(parseInt(startInitialYear), parseInt(initialMonth) - 1, parseInt(initialDay)),
  end: new Date(parseInt(endInitialYear), parseInt(initialMonth) - 1, parseInt(initialDay)),
};
const nextDates: IDateRangeFieldValue = {
  start: new Date(parseInt(startNextYear), parseInt(nextMonth) - 1, parseInt(nextDay)),
  end: new Date(parseInt(endNextYear), parseInt(nextMonth) - 1, parseInt(nextDay)),
};
const helperText = 'This is a helper';
const statusMessage = 'This is a status message';
const statusMessagePrefix = 'Prefix:';
const onFocus = jest.fn();
const onBlur = jest.fn();
const onChange = jest.fn();
const onClear = jest.fn();
const onIconKeyDown = jest.fn();
const onIconClick = jest.fn();
const iconName = 'calendar';
const iconId = `${id}-calendar-icon-button`;
const textMap = {
  iconAriaLabel: iconAriaLabel,
  ariaLabel: 'This is an aria label',
  startDayLabel: 'start dayLabel',
  startMonthLabel: 'start monthLabel',
  startYearLabel: 'start yearLabel',
  endDayLabel: 'end dayLabel',
  endMonthLabel: 'end monthLabel',
  endYearLabel: 'end yearLabel',
  formatLabel: 'format',
  expectedDateFormatLabel: 'Expected Date Format:',
  blank: 'blank',
  required: 'required',
  invalidEntry: 'invalid entry',
  invalidDay: 'invalid day',
  invalidMonth: 'invalid month',
  invalidYear: 'invalid year',
  clearButtonAriaLabel: clearButtonAriaLabel,
};
const formatDateTimeForSR = jest.fn();

const getLabel = () => screen.getByText(label);
const getRangeDateRangeField = () => screen.getByTestId(`${testId}`);
const getStartYearSegment = () => screen.getByRole('textbox', { name: /start yearLabel/i });
const getStartDaySegment = () => screen.getByRole('textbox', { name: /start dayLabel/i });
const getStartMonthSegment = () => screen.getByRole('textbox', { name: /start monthLabel/i });
const getEndYearSegment = () => screen.getByRole('textbox', { name: /end yearLabel/i });
const getEndDaySegment = () => screen.getByRole('textbox', { name: /end dayLabel/i });
const getEndMonthSegment = () => screen.getByRole('textbox', { name: /end monthLabel/i });
const getClearButton = () => screen.getByRole('button', { name: clearButtonAriaLabel });
const getIconButton = () => screen.getByRole('button', { name: iconAriaLabel });
const queryIconButton = () => screen.queryByRole('button', { name: iconAriaLabel });
const queryClearButton = () => screen.queryByRole('button', { name: clearButtonAriaLabel });

const tabThroughDate = async () => {
  //tab through month
  await userEvent.tab();
  //tab through day
  await userEvent.tab();
  //tab through year
  await userEvent.tab();
};

const dateTimeParts = () => {
  const dateTimeParts: IDateTimeFormatPart[] = [];
  const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
  for (const part of parts) {
    dateTimeParts.push({ ...part, id: generateId() });
  }
  return dateTimeParts;
};

const propsWithoutLabel = {
  dateSegmentPlaceholder,
  dateTimeParts: dateTimeParts(),
  testId,
  id,
  textMap,
  formatDateTimeForSR,
};

const defaultProps = {
  ...propsWithoutLabel,
  label,
};

const variants = {
  dateRangeFieldDefault: {
    name: 'Default DateRangeField',
    jsx: <DateRangeField {...defaultProps}></DateRangeField>,
  },
  dateRangeFieldWithoutLabel: {
    name: 'DateRangeField without label',
    jsx: <DateRangeField {...propsWithoutLabel}></DateRangeField>,
  },
  dateRangeFieldWithIntialDateValue: {
    name: 'DateRangeField with initial date value',
    jsx: <DateRangeField {...defaultProps} value={initialDates}></DateRangeField>,
  },
  dateRangeFieldWithHelperText: {
    name: 'DateRangeField with helper text',
    jsx: <DateRangeField {...defaultProps} helperText={helperText}></DateRangeField>,
  },
  dateRangeFieldWithSuccessMessage: {
    name: 'DateRangeField with success message',
    jsx: (
      <DateRangeField
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></DateRangeField>
    ),
  },
  dateRangeFieldWithErrorMessage: {
    name: 'DateRangeField with error message',
    jsx: (
      <DateRangeField
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></DateRangeField>
    ),
  },
  dateRangeFieldDisabled: {
    name: 'Disabled DateRangeField',
    jsx: <DateRangeField {...defaultProps} disabled></DateRangeField>,
  },
  dateRangeFieldReadOnly: {
    name: 'ReadOnly DateRangeField',
    jsx: <DateRangeField {...defaultProps} readOnly></DateRangeField>,
  },
  dateRangeFieldWithOnBlurAndFocus: {
    name: 'DateRangeField with onBlur and onFocus',
    jsx: <DateRangeField {...defaultProps} onBlur={onBlur} onFocus={onFocus}></DateRangeField>,
  },
  dateRangeFieldWithOnFocus: {
    name: 'DateRangeField with onFocus',
    jsx: <DateRangeField {...defaultProps} onFocus={onFocus}></DateRangeField>,
  },
  dateRangeFieldWithOnBlur: {
    name: 'DateRangeField with onBlur',
    jsx: <DateRangeField {...defaultProps} onBlur={onBlur}></DateRangeField>,
  },
  dateRangeFieldWithOnChange: {
    name: 'DateRangeField with onChange',
    jsx: <DateRangeField {...defaultProps} onChange={onChange}></DateRangeField>,
  },
};

interface IDateRangeFieldWrapperRef {
  rerender: (newInitialValue?: IDateRangeFieldValue) => void;
}

interface IDateRangeFieldWrapperProps {
  initialValue?: IDateRangeFieldValue;
}

const DateRangeFieldWrapper = forwardRef<IDateRangeFieldWrapperRef, IDateRangeFieldWrapperProps>(
  ({ initialValue }, ref) => {
    const dateRangeFieldRef = useRef<IDateRangeFieldMethods>(null);
    const [value, setValue] = useState<IDateRangeFieldValue | undefined>(initialValue);

    const onValueChange = (displayValue?: IDateRangeFieldDisplayValue, newValue?: IDateRangeFieldValue) => {
      onChange();
      if (newValue) {
        const newStartDate = newValue.start ? new Date(newValue.start) : undefined;
        if (newStartDate && newValue.start) {
          newStartDate.setFullYear(newValue.start.getFullYear());
        }

        const newEndDate = newValue.end ? new Date(newValue.end) : undefined;
        if (newEndDate && newValue.end) {
          newEndDate.setFullYear(newValue.end.getFullYear());
        }

        setValue({ ...value, start: newStartDate, end: newEndDate });
      }
    };

    const onValueClear = () => {
      onClear();
      dateRangeFieldRef.current?.clear();
      setValue(undefined);
    };

    //Related to 'setting state directly' describe block
    //Used to manually trigger a rerender to verify that direct changes to the value state, made outside of user interaction, are reflected in the date range field ui
    useImperativeHandle(ref, () => ({
      rerender(newInitialValue?: IDateRangeFieldValue) {
        setValue(newInitialValue);
      },
    }));

    return (
      <>
        <Button id="clear-value-button" label="Clear value" onClick={onValueClear} />
        <DateRangeField
          {...defaultProps}
          ref={dateRangeFieldRef}
          value={value}
          onChange={onValueChange}
          onClear={onValueClear}
        />
      </>
    );
  }
);
DateRangeFieldWrapper.displayName = 'DateRangeFieldWrapper';

const renderControlledDateRangeField = (initialValue?: IDateRangeFieldValue) => {
  const dateRangeFieldWrapperRef = createRef<IDateRangeFieldWrapperRef>();
  render(<DateRangeFieldWrapper ref={dateRangeFieldWrapperRef} initialValue={initialValue} />);
  return dateRangeFieldWrapperRef.current;
};

describe('[DateRangeField]', () => {
  it('should show label', () => {
    render(variants.dateRangeFieldDefault.jsx);
    expect(getRangeDateRangeField()).toBeInTheDocument();
    expect(getLabel()).toBeInTheDocument();
  });

  it('should render without label', () => {
    render(variants.dateRangeFieldWithoutLabel.jsx);
    expect(getRangeDateRangeField()).toBeInTheDocument();
  });

  it('should render segments placeholders', () => {
    render(variants.dateRangeFieldDefault.jsx);
    expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
    expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
    expect(getStartDaySegment()).toHaveTextContent(dayPlaceholder);
    expect(getEndYearSegment()).toHaveTextContent(yearPlaceholder);
    expect(getEndMonthSegment()).toHaveTextContent(monthPlaceholder);
    expect(getEndDaySegment()).toHaveTextContent(dayPlaceholder);
  });

  it('should render initial date value', () => {
    render(variants.dateRangeFieldWithIntialDateValue.jsx);
    expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
    expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
    expect(getStartDaySegment()).toHaveTextContent(initialDay);
    expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
    expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
    expect(getEndDaySegment()).toHaveTextContent(initialDay);
    expect(screen.getByText(label)).toBeInTheDocument();
  });

  it('should render helper text when provided', () => {
    render(variants.dateRangeFieldWithHelperText.jsx);
    expect(screen.getByText(helperText)).toBeInTheDocument();
  });

  it('should render status message in the error state when provided', () => {
    render(variants.dateRangeFieldWithErrorMessage.jsx);
    expect(screen.getByText(statusMessage)).toBeInTheDocument();
  });

  it('should render status message in the success state when provided', () => {
    render(variants.dateRangeFieldWithSuccessMessage.jsx);
    expect(screen.getByText(statusMessage)).toBeInTheDocument();
  });

  it('should set contenteditable false on all editable segments when disabled', () => {
    render(variants.dateRangeFieldDisabled.jsx);
    expect(getStartYearSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getStartMonthSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getStartDaySegment()).toHaveAttribute('contenteditable', 'false');
    expect(getEndYearSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getEndMonthSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getEndDaySegment()).toHaveAttribute('contenteditable', 'false');
  });

  it('should set contenteditable false on all editable segments when readOnly', () => {
    render(variants.dateRangeFieldReadOnly.jsx);
    expect(getStartYearSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getStartMonthSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getStartDaySegment()).toHaveAttribute('contenteditable', 'false');
    expect(getEndYearSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getEndMonthSegment()).toHaveAttribute('contenteditable', 'false');
    expect(getEndDaySegment()).toHaveAttribute('contenteditable', 'false');
  });

  it('should display clear button when specified', () => {
    render(<DateRangeField {...defaultProps} value={initialDates} onClear={onClear} />);
    expect(queryClearButton()).toBeInTheDocument();
  });

  it('should have a clear button aria-label when specified', () => {
    render(
      <DateRangeField
        {...defaultProps}
        value={initialDates}
        onClear={onClear}
        textMap={{ ...textMap, clearButtonAriaLabel }}
      />
    );
    expect(queryClearButton()).toHaveAttribute('aria-label', clearButtonAriaLabel);
  });

  it('should display an icon when specified', () => {
    render(<DateRangeField {...defaultProps} iconName={iconName} />);
    expect(getIconButton()).toBeInTheDocument();
  });

  it('should have an icon aria-label when specified', () => {
    render(<DateRangeField {...defaultProps} iconName={iconName} />);
    expect(getIconButton()).toHaveAttribute('aria-label', iconAriaLabel);
  });

  it('should not display an icon when not specified', () => {
    render(<DateRangeField {...defaultProps} />);
    expect(queryIconButton()).not.toBeInTheDocument();
  });

  it('should call onIconClick when the icon is clicked', async () => {
    onIconClick.mockClear();
    render(<DateRangeField {...defaultProps} iconName={iconName} onIconClick={onIconClick} />);

    await act(async () => await userEvent.click(getIconButton()));
    expect(onIconClick).toHaveBeenCalledTimes(1);
  });

  it('should have a tabbable icon when onIconKeyDown is defined', async () => {
    onIconKeyDown.mockClear();
    render(<DateRangeField {...defaultProps} iconName={iconName} onIconKeyDown={onIconKeyDown} />);

    await act(async () => {
      //tab to date range field
      await userEvent.tab();
      //tab through start date segment
      await tabThroughDate();
      //tab through end date segment
      await tabThroughDate();
    });

    expect(document.activeElement?.id).toEqual(iconId);
  });

  it('should call onIconKeyDown when defined and the icon is focused and a key is pressed', async () => {
    onIconKeyDown.mockClear();
    render(<DateRangeField {...defaultProps} iconName={iconName} onIconKeyDown={onIconKeyDown} />);

    await act(async () => {
      //tab to date range field
      await userEvent.tab();
      //tab through start date segment
      await tabThroughDate();
      //tab through end date segment
      await tabThroughDate();
      await userEvent.keyboard(' ');
    });

    expect(onIconKeyDown).toHaveBeenCalledTimes(1);
  });

  it('should pass focus to first DateSegment (month) when label is clicked on', async () => {
    render(<DateRangeField {...defaultProps} iconName={iconName} onIconKeyDown={onIconKeyDown} />);
    expect(getStartMonthSegment()).not.toHaveFocus();
    await userEvent.click(getLabel());
    expect(getStartMonthSegment()).toHaveFocus();
  });

  describe('onFocus and onBlur event', () => {
    beforeEach(onFocus.mockReset);
    beforeEach(onBlur.mockReset);

    it('dispatch onFocus when dateRangeField has focus', async () => {
      render(variants.dateRangeFieldWithOnBlurAndFocus.jsx);

      await act(async () => await userEvent.tab());
      await waitFor(() => {
        expect(onFocus).toHaveBeenCalledTimes(1);
        expect(onBlur).toHaveBeenCalledTimes(0);
      });
    });

    it('dispatch onFocus once when dateRangeField has focus and tab to next segment', async () => {
      render(variants.dateRangeFieldWithOnBlurAndFocus.jsx);

      await act(async () => {
        //tab into day segment
        await userEvent.tab();
        //tab into month segment
        await userEvent.tab();
        //tab into year segment
        await userEvent.tab();
        //tab to end day segement
        await userEvent.tab();
        //tab to end month segment
        await userEvent.tab();
        //tab to end year segment
        await userEvent.tab();
      });
      await waitFor(() => {
        expect(onFocus).toHaveBeenCalledTimes(1);
        expect(onBlur).toHaveBeenCalledTimes(0);
      });
    });

    it('dispatch onBlur once when tab out of dateRangefield', async () => {
      render(
        <>
          {variants.dateRangeFieldWithOnBlur.jsx}
          <button>Test</button>
        </>
      );

      await act(async () => {
        //tab to date range field
        await userEvent.tab();
        //tab through start date segment
        await tabThroughDate();
        //tab through end date segment
        await tabThroughDate();
      });

      await waitFor(() => {
        expect(onBlur).toHaveBeenCalledTimes(1);
      });
    });
  });

  /**
   * JSDom doesn't fully support contenteditable element https://github.com/jsdom/jsdom/issues/1670
   * It is out of scope for React Testing Library https://github.com/testing-library/user-event/issues/230
   * Workaround to trigger onInput event https://github.com/testing-library/dom-testing-library/pull/235   *
   */
  describe('onChange event', () => {
    beforeEach(onChange.mockReset);

    it('dispatch onChange with no value when only month segment has updated', async () => {
      render(variants.dateRangeFieldWithOnChange.jsx);

      getStartMonthSegment().innerText = initialMonth;
      getStartMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getStartMonthSegment()).toHaveTextContent(initialMonth);

      getEndMonthSegment().innerText = initialMonth;
      getEndMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);

      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith({ start: '12/dd/yyyy', end: '12/dd/yyyy' }, undefined);
      });
    });

    it('dispatch onChange with no value when only day segment has updated', async () => {
      render(variants.dateRangeFieldWithOnChange.jsx);

      getStartDaySegment().innerText = initialDay;
      getStartDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getStartDaySegment()).toHaveTextContent(initialDay);

      getEndDaySegment().innerText = initialDay;
      getEndDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getEndDaySegment()).toHaveTextContent(initialDay);

      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith({ start: 'mm/31/yyyy', end: 'mm/31/yyyy' }, undefined);
      });
    });

    it('dispatch onChange with no value when only year segment has updated', async () => {
      render(variants.dateRangeFieldWithOnChange.jsx);

      getStartYearSegment().innerText = startInitialYear;
      getStartYearSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);

      getEndYearSegment().innerText = endInitialYear;
      getEndYearSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      expect(getEndYearSegment()).toHaveTextContent(endInitialYear);

      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith({ start: 'mm/dd/2022', end: 'mm/dd/2023' }, undefined);
      });
    });

    it('dispatch onChange with empty value when month segment is not digits', async () => {
      render(variants.dateRangeFieldWithOnChange.jsx);

      getStartMonthSegment().innerText = 'ab';
      getStartMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith({ start: '', end: '' }, undefined);
      });
    });

    //second argument to onChange is failing
    it.skip('dispatch onChange with Date value when full date has updated', async () => {
      render(variants.dateRangeFieldWithOnChange.jsx);

      getStartMonthSegment().innerText = initialMonth;
      getStartMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getStartDaySegment().innerText = initialDay;
      getStartDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getStartYearSegment().innerText = startInitialYear;
      getStartYearSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));

      getEndMonthSegment().innerText = initialMonth;
      getEndMonthSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getEndDaySegment().innerText = initialDay;
      getEndDaySegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      getEndYearSegment().innerText = endInitialYear;
      getEndYearSegment().dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));

      await waitFor(() => {
        expect(onChange).toHaveBeenLastCalledWith({ start: '12/31/2022', end: '12/31/2023' }, initialDates);
      });
    });
  });

  //These tests are related to this issue: https://dayforce.atlassian.net/browse/PWEB-11523
  describe('setting state directly', () => {
    it('should update Date value', async () => {
      const dateRangeFieldWrapperRef = renderControlledDateRangeField();

      expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getStartDaySegment()).toHaveTextContent(dayPlaceholder);

      expect(getEndYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getEndMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getEndDaySegment()).toHaveTextContent(dayPlaceholder);

      // set value
      await waitFor(() => {
        dateRangeFieldWrapperRef?.rerender(initialDates);
      });

      await waitFor(() => {
        expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
        expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
        expect(getStartDaySegment()).toHaveTextContent(initialDay);

        expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
        expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
        expect(getEndDaySegment()).toHaveTextContent(initialDay);
      });
    });

    it('should update Date value consecutively and correctly', async () => {
      const dateRangeFieldWrapperRef = renderControlledDateRangeField();

      expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getStartDaySegment()).toHaveTextContent(dayPlaceholder);

      expect(getEndYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getEndMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getEndDaySegment()).toHaveTextContent(dayPlaceholder);

      // set value
      await waitFor(() => {
        dateRangeFieldWrapperRef?.rerender(initialDates);
      });
      // set value again consecutively
      await waitFor(() => {
        dateRangeFieldWrapperRef?.rerender(nextDates);
      });

      await waitFor(() => {
        expect(getStartYearSegment()).toHaveTextContent(startNextYear);
        expect(getStartMonthSegment()).toHaveTextContent(nextMonth);
        expect(getStartDaySegment()).toHaveTextContent(nextDay);

        expect(getEndYearSegment()).toHaveTextContent(endNextYear);
        expect(getEndMonthSegment()).toHaveTextContent(nextMonth);
        expect(getEndDaySegment()).toHaveTextContent(nextDay);
      });
    });

    it('should update Date value consecutively and correctly after value is typed in', async () => {
      const dateRangeFieldWrapperRef = renderControlledDateRangeField();
      expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getStartDaySegment()).toHaveTextContent(dayPlaceholder);

      expect(getEndYearSegment()).toHaveTextContent(yearPlaceholder);
      expect(getEndMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getEndDaySegment()).toHaveTextContent(dayPlaceholder);

      // manually type in value
      await act(async () => {
        await userEvent.type(getStartDaySegment(), initialDay, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
        await userEvent.type(getStartMonthSegment(), initialMonth, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
        await userEvent.type(getStartYearSegment(), startInitialYear, {
          initialSelectionStart: 0,
          initialSelectionEnd: 4,
        });

        await userEvent.type(getEndDaySegment(), initialDay, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
        await userEvent.type(getEndMonthSegment(), initialMonth, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
        await userEvent.type(getEndYearSegment(), endInitialYear, {
          initialSelectionStart: 0,
          initialSelectionEnd: 4,
        });
      });

      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
      expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
      expect(getStartDaySegment()).toHaveTextContent(initialDay);

      expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
      expect(getEndDaySegment()).toHaveTextContent(initialDay);

      // set value directly
      await waitFor(() => {
        dateRangeFieldWrapperRef?.rerender(nextDates);
      });

      await waitFor(() => {
        expect(getStartYearSegment()).toHaveTextContent(startNextYear);
        expect(getStartMonthSegment()).toHaveTextContent(nextMonth);
        expect(getStartDaySegment()).toHaveTextContent(nextDay);

        expect(getEndYearSegment()).toHaveTextContent(endNextYear);
        expect(getEndMonthSegment()).toHaveTextContent(nextMonth);
        expect(getEndDaySegment()).toHaveTextContent(nextDay);
      });
    });

    it('should update Date value when year segment digits deleted', async () => {
      renderControlledDateRangeField(initialDates);
      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
      expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
      expect(getStartDaySegment()).toHaveTextContent(initialDay);

      expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
      expect(getEndDaySegment()).toHaveTextContent(initialDay);

      // delete year only
      startInitialYear.split('').forEach(async () => {
        await userEvent.type(getStartYearSegment(), '{backspace}');
      });

      await waitFor(() => {
        // year placeholder should be shown for start date year
        expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
        expect(getStartDaySegment()).toHaveTextContent(initialDay);

        expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
        expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
        expect(getEndDaySegment()).toHaveTextContent(initialDay);
      });
    });
  });

  describe('clear button functionality', () => {
    beforeEach(onClear.mockReset);

    it('should trigger onClear if valid Date value and display value present', async () => {
      renderControlledDateRangeField(initialDates);
      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
      expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
      expect(getStartDaySegment()).toHaveTextContent(initialDay);
      expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
      expect(getEndDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      // Trigger onClear button
      await act(async () => await userEvent.click(getClearButton()));

      await waitFor(() => {
        expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getStartDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(getEndYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getEndMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getEndDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(onClear).toHaveBeenCalledTimes(1);
      });

      expect(queryClearButton()).not.toBeInTheDocument();
    });

    it('should trigger onClear input if display value present but Date value is invalid', async () => {
      renderControlledDateRangeField(initialDates);
      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
      expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
      expect(getStartDaySegment()).toHaveTextContent(initialDay);
      expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
      expect(getEndDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      await act(async () => {
        await userEvent.click(getStartMonthSegment());
        await userEvent.keyboard('{Backspace}{Backspace}');
      });

      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
      expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
      expect(getStartDaySegment()).toHaveTextContent(initialDay);
      expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
      expect(getEndDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      // Trigger onClear button
      await act(async () => await userEvent.click(getClearButton()));

      await waitFor(() => {
        expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getStartDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(getEndYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getEndMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getEndDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(onClear).toHaveBeenCalledTimes(1);
      });

      expect(queryClearButton()).not.toBeInTheDocument();
    });

    it('should clear input if a null or undefined value is passed in using an external clear button', async () => {
      renderControlledDateRangeField(initialDates);
      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
      expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
      expect(getStartDaySegment()).toHaveTextContent(initialDay);
      expect(getEndYearSegment()).toHaveTextContent(endInitialYear);
      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
      expect(getEndDaySegment()).toHaveTextContent(initialDay);
      expect(getClearButton()).toBeInTheDocument();

      const externalClearButton = document.getElementById('clear-value-button');
      expect(externalClearButton).toBeInTheDocument();
      await act(async () => await userEvent.click(externalClearButton as HTMLElement));

      await waitFor(() => {
        expect(getStartYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getStartMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getStartDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(getEndYearSegment()).toHaveTextContent(yearPlaceholder);
        expect(getEndMonthSegment()).toHaveTextContent(monthPlaceholder);
        expect(getEndDaySegment()).toHaveTextContent(dayPlaceholder);
        expect(onClear).toHaveBeenCalledTimes(1);
      });

      expect(queryClearButton()).not.toBeInTheDocument();
    });
  });

  describe('keyboard behavior', () => {
    it('should accept a value change for start year segment', async () => {
      render(variants.dateRangeFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getStartYearSegment(), startInitialYear, {
          initialSelectionStart: 0,
          initialSelectionEnd: 4,
        });
      });

      expect(getStartYearSegment()).toHaveTextContent(startInitialYear);
    });

    it('should accept a value change for start month segment', async () => {
      render(variants.dateRangeFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getStartMonthSegment(), initialMonth, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
      });

      expect(getStartMonthSegment()).toHaveTextContent(initialMonth);
    });

    it('should accept a value change for start day segment', async () => {
      render(variants.dateRangeFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getStartDaySegment(), initialDay, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
      });

      expect(getStartDaySegment()).toHaveTextContent(initialDay);
    });

    it('should accept a value change for end year segment', async () => {
      render(variants.dateRangeFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getEndYearSegment(), startInitialYear, {
          initialSelectionStart: 0,
          initialSelectionEnd: 4,
        });
      });

      expect(getEndYearSegment()).toHaveTextContent(startInitialYear);
    });

    it('should accept a value change for end month segment', async () => {
      render(variants.dateRangeFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getEndMonthSegment(), initialMonth, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
      });

      expect(getEndMonthSegment()).toHaveTextContent(initialMonth);
    });

    it('should accept a value change for end day segment', async () => {
      render(variants.dateRangeFieldDefault.jsx);

      await act(async () => {
        await userEvent.type(getEndDaySegment(), initialDay, {
          initialSelectionStart: 0,
          initialSelectionEnd: 2,
        });
      });

      expect(getEndDaySegment()).toHaveTextContent(initialDay);
    });
  });
});
