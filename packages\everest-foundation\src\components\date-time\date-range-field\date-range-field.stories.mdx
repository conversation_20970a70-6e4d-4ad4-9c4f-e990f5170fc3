import { useEffect, useRef, useState } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';
import { DateRangeField } from './date-range-field';
import Examples from './date-range-field.examples.mdx';
import { ICON_NAMES } from '../../icon';

<Meta
  title="Components/Text Fields/DateRangeField"
  component={DateRangeField}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/EruqEEN3NPLA8MdvaJ4h8k/%F0%9F%A7%AA-Date-Range-Picker?node-id=4376-93384&t=8zJnMb2xD41UufBH-0',
    },
  }}
  argTypes={{
    dateTimeParts: {
      type: 'object',
      control: 'object',
      description: 'DateRangeField segment parts object.',
    },
    label: {
      type: 'string',
      control: 'text',
      description: 'Specifies the label rendered in the date range field.',
    },
    iconName: {
      type: { name: 'enum' },
      control: 'select',
      options: ['', ...ICON_NAMES].sort(),
      description: 'Sets the name of the icon.',
    },
    value: {
      control: '-',
      description: 'Define value of the date range field.',
    },
    disabled: {
      type: 'boolean',
      description: 'Sets the disabled attribute on the date range field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    readOnly: {
      type: 'boolean',
      description: 'Sets the readOnly attribute on the date range field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    required: {
      type: 'boolean',
      description: 'Sets the required attribute on the date range field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    status: {
      type: 'enum',
      control: 'radio',
      options: ['default', 'error', 'success'],
      description: 'Sets the status of the date range field.',
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    statusMessage: {
      type: 'string',
      control: 'text',
      description: 'Sets the status message rendered under the date range field.',
    },
    statusMessagePrefix: {
      description: 'Sets the prefix to the status message rendered under the date range field.',
    },
    helperText: {
      type: 'string',
      control: 'text',
      description: 'Sets the helper text rendered under the date range field.',
    },
    helperTextPrefix: {
      type: 'string',
      control: 'text',
      description: 'Sets the prefix to the helper text rendered under the date range field.',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'A unique id is required for accessibility purpose. ',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'An id used for automation testing.',
    },
    textMap: {
      type: 'object',
      control: 'object',
      description: 'Object containing localized text for various elements.',
    },
    dateSegmentPlaceholder: {
      type: 'object',
      control: 'object',
      description: 'A placeholder for the date input format',
    },
    onIconClick: {
      control: '-',
      description: 'Callback when the icon is clicked.',
    },
    onIconKeyDown: {
      control: '-',
      description: 'Callback when a key is pressed on the icon.',
    },
    onChange: {
      control: '-',
      description: 'Callback when date range field value changed.',
    },
    onClear: {
      control: '-',
      description: 'Callback when user clears date range field.',
    },
    onFocus: {
      control: '-',
      description: 'Callback when user focus on date range field.',
    },
    onBlur: {
      control: '-',
      description: 'Callback when user leave date range field.',
    },
    onClick: {
      control: '-',
      description: 'Callback when the user clicks the date range field.',
    },
    formatDateTimeForSR: {
      control: '-',
      description: 'Formats how screen reader should announce the date.',
    },
  }}
  args={{
    dateTimeParts: [
      { id: 'segment-month-1', type: 'month', value: '' },
      { id: 'segment-literal-1', type: 'literal', value: '/' },
      { id: 'segment-day-1', type: 'day', value: '' },
      { id: 'segment-literal-2', type: 'literal', value: '/' },
      { id: 'segment-year-1', type: 'year', value: '' },
    ],
    dateSegmentPlaceholder: {
      day: 'dd',
      month: 'mm',
      year: 'yyyy',
    },
    textMap: {
      ariaLabel: 'Leave of absence dates',
      clearButtonAriaLabel: 'clear button aria label',
      iconAriaLabel: 'icon aria label',
      startDayLabel: 'Day of start date',
      startMonthLabel: 'Month of start date',
      startYearLabel: 'Year of start date',
      endDayLabel: 'Day of end date',
      endMonthLabel: 'Month of end date',
      endYearLabel: 'Year of end date',
      formatLabel: 'format',
      expectedDateFormatLabel: 'Format:',
      blank: 'blank',
      required: 'required',
      invalidEntry: 'invalid entry',
      invalidDay: 'invalid day',
      invalidMonth: 'invalid month',
      invalidYear: 'invalid year',
    },
    label: 'Leave of absence dates',
    id: 'daterangefield-1',
    testId: 'daterangefield-test-id',
    disabled: false,
    readOnly: false,
    required: true,
    status: 'default',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Error:',
    helperText: 'within 60 days from today',
    helperTextPrefix: 'Recommended:',
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    onChange: action('onChange'),
    onClear: action('onClear'),
    onClick: action('onClick'),
    onIconClick: action('onIconClick'),
    onIconKeyDown: action('onIconKeyDown'),
  }}
/>

# DateRangeField

<Examples />

## Live Demo

export const formatDateTimeForSR = (value) => {
  return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
};

<Canvas>
  <Story name="DateRangeField">
    {(args) => {
      const dateRangeFieldRef = useRef(null);
      const [dateValue, setDateValue] = useState(null);
      const [{ status }, updateArgs] = useArgs();
      const [hasDisplayValue, setHasDisplayValue] = useState(false);
      const handleDateValueChange = (displayValue, value) => {
        const newStartDate = value?.start ? new Date(value.start) : null;
        const newEndDate = value?.end ? new Date(value.end) : null;
        setHasDisplayValue(!!displayValue?.start || !!displayValue?.end);
        if (newStartDate) newStartDate.setFullYear(value.start.getFullYear());
        if (newEndDate) newEndDate.setFullYear(value.end.getFullYear());
        setDateValue({
          start: newStartDate || value?.start || null,
          end: newEndDate || value?.end || null,
        });
        args.onChange?.(displayValue, value);
      };
      const handleBlur = (e) => {
        if ((!dateValue?.start || !dateValue?.end) && hasDisplayValue) {
          updateArgs({ status: 'error' });
        } else {
          updateArgs({ status: 'default' });
        }
        args.onBlur?.(e);
      };
      const handleClear = (e) => {
        dateRangeFieldRef.current && dateRangeFieldRef.current.clear();
        setDateValue(null);
        args.onClear?.(e);
      };
      useEffect(() => {
        updateArgs({ status: args.status });
      }, [args.status]);
      return (
        <DateRangeField
          {...args}
          ref={dateRangeFieldRef}
          onClear={handleClear}
          value={dateValue}
          status={status}
          onChange={handleDateValueChange}
          onBlur={handleBlur}
          formatDateTimeForSR={formatDateTimeForSR}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="DateRangeField" />
