import React, { RefObject, forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';

import { announce, clearAnnouncer } from '../../../utils';
import { useComponentFocused } from '../../../utils/use-component-focused';
import { Divider } from '../../divider';
import { FormFieldContainer, FormFieldContainerContext, TFormFieldStatus } from '../../form-field-container';
import { TIconName } from '../../icon';
import { IconButton } from '../../icon-button';
import { IDateFieldTextMapBase, IDateSegmentPlaceholder, IDateTimeFormatPart } from '../date-field-helpers';
import {
  DateSegmentContainer,
  IDateSegmentContainerMethods,
  IDateSegmentContainerTextMap,
} from '../date-segment-container';

import styles from './date-range-field.module.scss';

export interface IDateRangeFieldMethods extends Omit<IDateSegmentContainerMethods, 'setCursorStart' | 'setCursorEnd'> {
  focus: IDateSegmentContainerMethods['setCursorStart'];
}

export interface IDateRangeFieldTextMap extends Omit<IDateFieldTextMapBase, 'dayLabel' | 'monthLabel' | 'yearLabel'> {
  startDayLabel: string;
  startMonthLabel: string;
  startYearLabel: string;
  endDayLabel: string;
  endMonthLabel: string;
  endYearLabel: string;
}

export interface IDateRangeFieldDisplayValue {
  start: string;
  end: string;
}

export interface IDateRangeFieldValue {
  start?: Date;
  end?: Date;
}

export interface IDateRangeFieldProps {
  label?: string;
  value?: IDateRangeFieldValue;
  dateTimeParts: IDateTimeFormatPart[];
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  status?: TFormFieldStatus;
  statusMessage?: string;
  statusMessagePrefix?: string;
  helperText?: string;
  helperTextPrefix?: string;
  id: string;
  testId?: string;
  textMap: IDateRangeFieldTextMap;
  dateSegmentPlaceholder: IDateSegmentPlaceholder;
  iconName?: TIconName;
  dateRangeFieldRef?: RefObject<HTMLElement>;
  onIconClick?: (e: React.MouseEvent) => void;
  onIconKeyDown?: (e: React.KeyboardEvent) => void;
  onChange?: (displayValue: IDateRangeFieldDisplayValue, value?: IDateRangeFieldValue) => void;
  onClear?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onClick?: (event: React.MouseEvent) => void;
  formatDateTimeForSR: (value: Date) => string;
}

export const DateRangeField = forwardRef<IDateRangeFieldMethods, IDateRangeFieldProps>(
  (props: IDateRangeFieldProps, ref) => {
    const {
      label,
      value,
      dateTimeParts,
      disabled,
      readOnly,
      required,
      id,
      status = 'default',
      statusMessage,
      statusMessagePrefix,
      helperText,
      helperTextPrefix,
      testId,
      textMap,
      dateSegmentPlaceholder,
      iconName,
      dateRangeFieldRef,
      onIconClick,
      onIconKeyDown,
      onChange,
      onClear,
      onFocus,
      onBlur,
      onClick,
      formatDateTimeForSR,
    } = props;

    const [isFocused, setIsFocused] = useState<boolean | undefined>(undefined);
    const [startDisplayValue, setStartDisplayValue] = useState('');
    const [endDisplayValue, setEndDisplayValue] = useState('');
    const dateRangeContainerRef = useRef<HTMLDivElement>(null);
    const startDateSegmentContainerRef = useRef<IDateSegmentContainerMethods>(null);
    const dateSegmentSeparatorRef = useRef<HTMLDivElement>(null);
    const endDateSegmentContainerRef = useRef<IDateSegmentContainerMethods>(null);
    const clearBtnRef = useRef<HTMLDivElement>(null);
    const iconBtnRef = useRef<HTMLDivElement>(null);

    const dateRangeFieldLabelId = `${id}-label`;
    const statusMessageId = `${id}-status-message`;

    const { clearButtonAriaLabel, iconAriaLabel } = textMap;

    const buildDateSegmentContainerTextMap = (isEndDate = false): IDateSegmentContainerTextMap => {
      const formattedStartDate = value?.start ? formatDateTimeForSR(value.start) : undefined;
      const formattedEndDate = value?.end ? formatDateTimeForSR(value.end) : undefined;

      const isStatusMessageRendered = !!statusMessage && status !== 'default';
      const isHelperTextRendered = !!helperText && !isStatusMessageRendered;

      return {
        ariaLabel: textMap.ariaLabel,
        clearButtonAriaLabel: textMap.clearButtonAriaLabel,
        iconAriaLabel: textMap.iconAriaLabel,
        dayLabel: isEndDate ? textMap.endDayLabel : textMap.startDayLabel,
        monthLabel: isEndDate ? textMap.endMonthLabel : textMap.startMonthLabel,
        yearLabel: isEndDate ? textMap.endYearLabel : textMap.startYearLabel,
        formatLabel: textMap.formatLabel,
        expectedDateFormatLabel: textMap.expectedDateFormatLabel,
        blank: textMap.blank,
        required: required && textMap.required ? textMap.required : undefined,
        invalidEntry: status === 'error' && textMap.invalidEntry ? textMap.invalidEntry : undefined,
        invalidDay: textMap.invalidDay,
        invalidMonth: textMap.invalidMonth,
        invalidYear: textMap.invalidYear,
        formattedSRValue: isEndDate ? formattedEndDate : formattedStartDate,
        dateSegmentPlaceholder: dateSegmentPlaceholder,
        label: label,
        statusMessage: isStatusMessageRendered ? statusMessage : undefined,
        statusMessagePrefix: statusMessagePrefix,
        helperText: isHelperTextRendered ? helperText : undefined,
        helperTextPrefix: helperTextPrefix,
      };
    };

    // On mousedown, set focus on the closest date segment near the mouse click position
    // ex. in the en-US locale, if clicking right most part of DateRangeField, cursor should land on the end year segment (yyyy)
    const focusOnDateSegment = (x: number) => {
      const dateSegmentSeparatorRect = dateSegmentSeparatorRef.current?.getBoundingClientRect();

      //dateSegmentSeparatorRef ref could be undefined. that should never be the case - but if it is, focus on start date
      if (!dateSegmentSeparatorRect) {
        startDateSegmentContainerRef.current?._focusOnMouseXPosition?.(x);
        return;
      }

      const { left, width } = dateSegmentSeparatorRect;
      const dateSegmentSeparatorMiddle = left + width / 2;

      x < dateSegmentSeparatorMiddle
        ? startDateSegmentContainerRef.current?._focusOnMouseXPosition?.(x)
        : endDateSegmentContainerRef.current?._focusOnMouseXPosition?.(x);
    };

    const handleMouseXPosition = (e: React.MouseEvent) => {
      if (disabled) return;
      if (
        dateRangeContainerRef.current?.contains(e.target as HTMLElement) ||
        iconBtnRef.current?.contains(e.target as HTMLElement)
      ) {
        focusOnDateSegment(e.clientX);
      }
    };

    const handleOnClick = (e: React.MouseEvent) => {
      handleMouseXPosition(e);
      onClick?.(e);
    };

    useComponentFocused(
      [dateRangeContainerRef, iconBtnRef, clearBtnRef],
      // eventListener types
      ['mousedown', 'keyup'],
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      (targetId: string) => {
        setIsFocused(true);
      },
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      (targetId: string) => {
        setIsFocused(false);
      }
    );

    useEffect(() => {
      if (disabled) return;
      if (isFocused) {
        onFocus?.();
      } else if (typeof isFocused === 'boolean' && !isFocused) {
        onBlur?.();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isFocused]);

    const handleStartChange = (updatedDisplayValue: string, dateValue: Date | undefined) => {
      handleChange('start', updatedDisplayValue, dateValue);
    };

    const handleEndChange = (updatedDisplayValue: string, dateValue: Date | undefined) => {
      handleChange('end', updatedDisplayValue, dateValue);
    };

    const handleChange = (type: 'start' | 'end', updatedDisplayValue: string, dateValue: Date | undefined) => {
      clearAnnouncer('assertive');
      if (dateValue) {
        announce(formatDateTimeForSR(dateValue));
      }

      if (type === 'start') {
        setStartDisplayValue(updatedDisplayValue);
        onChange?.(
          { start: updatedDisplayValue, end: endDisplayValue },
          value || dateValue ? { ...value, start: dateValue } : undefined
        );
      } else {
        setEndDisplayValue(updatedDisplayValue);
        onChange?.(
          { start: startDisplayValue, end: updatedDisplayValue },
          value || dateValue ? { ...value, end: dateValue } : undefined
        );
      }
    };

    const handleClearButtonOnClick = () => {
      if (disabled) return;
      onClear?.();
    };

    const handleClearButtonKeyDown = (e: React.KeyboardEvent) => {
      if (disabled) return;
      if (e.key === 'Enter' || e.key === ' ') {
        onClear?.();
        e.stopPropagation();
      }
      if (e.key !== 'Tab') e.preventDefault();
    };

    const handleIconOnClick = (e: React.MouseEvent) => {
      if (disabled) return;
      onIconClick?.(e);
    };

    const handleIconOnKeyDown = (e: React.KeyboardEvent) => {
      if (disabled) return;
      onIconKeyDown?.(e);
    };

    useImperativeHandle(ref, () => ({
      clear: () => {
        startDateSegmentContainerRef.current?.clear();
        endDateSegmentContainerRef.current?.clear();
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _focusOnMouseXPosition: (x: number) => {
        focusOnDateSegment(x);
      },
      focus: () => {
        startDateSegmentContainerRef.current?.setCursorStart();
      },
    }));

    useEffect(() => {
      if (dateRangeContainerRef.current && dateRangeFieldRef) {
        (dateRangeFieldRef as any).current = dateRangeContainerRef.current;
      }
    }, [dateRangeFieldRef, dateRangeContainerRef]);

    const renderClearButton = () =>
      (!!startDisplayValue || !!endDisplayValue || !!value?.start || !!value?.end) &&
      !disabled &&
      !readOnly &&
      onClear && (
        <div
          className={classnames(styles.clearButtonWrapper, {
            [styles.clearButtonWrapperWithIcon]: !!iconName,
          })}
          ref={clearBtnRef}
        >
          <div className={styles.clearButton}>
            <IconButton
              id={`${id}-clear-icon-button`}
              iconName="xSmall"
              ariaLabel={clearButtonAriaLabel ?? ''}
              onClick={handleClearButtonOnClick}
              onKeyDown={handleClearButtonKeyDown}
              testId={testId ? `${testId}-clear-icon-button` : undefined}
              variant="tertiaryNeutral"
              className={styles.overrideIconFill}
            />
          </div>
          {!!iconName && (
            <div className={styles.divider}>
              <Divider vertical />
            </div>
          )}
        </div>
      );

    const renderFormFieldContainerContent = () => {
      return (
        <div
          id={id}
          ref={dateRangeContainerRef}
          data-testid={testId}
          className={styles.evrDateRangeField}
          onClick={handleOnClick}
        >
          <div
            className={classnames(styles.dateSegmentWrapper, {
              [styles.disabled]: disabled,
            })}
          >
            <DateSegmentContainer
              ref={startDateSegmentContainerRef}
              dateTimeParts={dateTimeParts}
              value={value?.start}
              disabled={disabled}
              readOnly={readOnly}
              testId={`${testId}-start`}
              onChange={handleStartChange}
              onContainerFocusChange={(isPrevious) => {
                if (!isPrevious) {
                  endDateSegmentContainerRef.current?.setCursorStart(); //Pass this in order to move focus from last segment of start date to first segment of end date
                }
              }}
              textMap={buildDateSegmentContainerTextMap()}
              dateSegmentPlaceholder={dateSegmentPlaceholder}
              isParentFocused={isFocused}
            />
            <span
              ref={dateSegmentSeparatorRef}
              className={classnames('evrBodyText1', styles.dateRangeSeparator, {
                [styles.disabled]: disabled,
                [styles.readOnly]: readOnly,
                [styles.placeholder]: !startDisplayValue && !endDisplayValue,
              })}
            >
              &ndash;
            </span>
            <DateSegmentContainer
              ref={endDateSegmentContainerRef}
              dateTimeParts={dateTimeParts}
              value={value?.end}
              disabled={disabled}
              readOnly={readOnly}
              testId={`${testId}-end`}
              onChange={handleEndChange}
              onContainerFocusChange={(isPrevious) => {
                if (isPrevious) {
                  startDateSegmentContainerRef.current?.setCursorEnd(); //Pass this in order to move focus from first segment of end date to end of last segment of start date
                }
              }}
              textMap={buildDateSegmentContainerTextMap(true)}
              dateSegmentPlaceholder={dateSegmentPlaceholder}
              isParentFocused={isFocused}
            />
          </div>
          {/* should consider creating a generic button bar component */}
          {renderClearButton()}
          {!!iconName && (
            <div className={classnames({ [styles.iconWrapper]: iconName })} ref={iconBtnRef}>
              <IconButton
                id={`${id}-${iconName}-icon-button`}
                iconName={iconName}
                ariaLabel={iconAriaLabel ?? ''}
                onClick={handleIconOnClick}
                onKeyDown={handleIconOnKeyDown}
                testId={testId ? `${testId}-${iconName}-icon-button` : undefined}
                disabled={disabled || readOnly}
                variant="tertiaryNeutral"
                className={classnames({ [styles.overrideIconFill]: !disabled && !readOnly })}
              />
            </div>
          )}
        </div>
      );
    };

    const formFieldContainerContext = useMemo(
      () => ({
        label,
        disabled,
        readOnly,
        required,
        status,
        helperText,
        helperTextPrefix,
        statusMessage,
        statusMessagePrefix,
      }),
      [label, disabled, readOnly, required, status, helperText, helperTextPrefix, statusMessage, statusMessagePrefix]
    );

    return (
      <FormFieldContainerContext.Provider value={formFieldContainerContext}>
        <FormFieldContainer
          testId={testId ? `${testId}-form-field-container` : undefined}
          renderContent={renderFormFieldContainerContent}
          focused={isFocused}
          labelId={dateRangeFieldLabelId}
          statusMessageId={statusMessageId}
          htmlFor={id}
          onClickLabel={() => {
            startDateSegmentContainerRef?.current && startDateSegmentContainerRef.current.setCursorStart();
          }}
        />
      </FormFieldContainerContext.Provider>
    );
  }
);

DateRangeField.displayName = 'DateRangeField';
