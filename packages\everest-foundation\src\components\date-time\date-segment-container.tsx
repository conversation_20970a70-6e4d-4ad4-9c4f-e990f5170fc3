import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';

import {
  IDateTimeFormatPart,
  IDateSegmentPlaceholder,
  getDisplayText,
  getMaxDayValue,
  getNextSegmentType,
  getNextSeparator,
  getSegmentMaxValueLength,
  IDateFieldTextMapBase,
} from './date-field-helpers';
import { DateSegment } from './date-segment';
import { announce } from '../../utils';
import { getDate, getMonth, getYear, isSameDay, startOfDay } from '../../utils/date-utils';

export interface IDateSegmentContainerMethods {
  /**
   * Focus on mouseclick point
   * @deprecated This property should not be used.
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  _focusOnMouseXPosition: (x: number) => void;
  /**
   * Clear input field
   */
  clear: () => void;
  /**
   * Set cursor to start of first date segment
   */
  setCursorStart: () => void;
  /**
   * Move cursor to end of last date segment
   * Needed when backspacing from end date to start date
   */
  setCursorEnd: () => void;
}
export interface IDateSegmentContainerTextMap extends IDateFieldTextMapBase {
  formattedSRValue?: string;
  dateSegmentPlaceholder: IDateSegmentPlaceholder;
  label?: string;
  statusMessage?: string;
  statusMessagePrefix?: string;
  helperText?: string;
  helperTextPrefix?: string;
}

export interface IDateSegmentContainer {
  dateTimeParts: IDateTimeFormatPart[];
  value?: Date;
  disabled?: boolean;
  readOnly?: boolean;
  testId?: string;
  onChange?: (displayValue: string, value?: Date) => void;
  onFocus?: (e: React.FocusEvent) => void;
  onBlur?: (e: React.FocusEvent) => void;
  onContainerFocusChange?: (isPrevious: boolean) => void;
  textMap: IDateSegmentContainerTextMap;
  dateSegmentPlaceholder: IDateSegmentPlaceholder;
  /** Whether the parent container has focus */
  isParentFocused?: boolean;
}

const buildDateFieldAriaLabelValue = (
  type: 'day' | 'month' | 'year',
  textMap: IDateSegmentContainerTextMap,
  displayValue: string,
  isFirstSegment: boolean
) => {
  const {
    ariaLabel,
    expectedDateFormatLabel,
    blank,
    required,
    invalidEntry,
    formattedSRValue,
    dateSegmentPlaceholder,
    label,
    statusMessage,
    statusMessagePrefix,
    helperText,
    helperTextPrefix,
  } = textMap;

  const stringArray = [];
  if (isFirstSegment) {
    if (ariaLabel || label) {
      stringArray.push(ariaLabel ?? label);
    }
    if (statusMessage) {
      stringArray.push(`${statusMessagePrefix ?? ''} ${statusMessage}`);
    }
    if (helperText) {
      stringArray.push(`${helperTextPrefix ?? ''} ${helperText}`);
    }
  }

  switch (type) {
    case 'day':
      stringArray.push(textMap?.dayLabel);
      break;
    case 'month':
      stringArray.push(textMap?.monthLabel);
      break;
    case 'year':
      stringArray.push(textMap?.yearLabel);
      break;
  }

  if (required) {
    stringArray.push(required);
  }
  if (invalidEntry) {
    stringArray.push(invalidEntry);
  }
  stringArray.push(`${expectedDateFormatLabel} ${dateSegmentPlaceholder[type]}`);
  if (formattedSRValue) {
    stringArray.push(formattedSRValue);
  } else if (displayValue) {
    stringArray.push(displayValue);
  } else {
    stringArray.push(blank);
  }
  return stringArray.join(', ');
};

export const DateSegmentContainer = React.forwardRef<IDateSegmentContainerMethods, IDateSegmentContainer>(
  (props: IDateSegmentContainer, ref) => {
    const {
      dateTimeParts,
      value,
      disabled,
      readOnly,
      testId,
      onChange,
      onFocus,
      onBlur,
      onContainerFocusChange,
      textMap,
      dateSegmentPlaceholder,
      isParentFocused,
    } = props;

    const dateSegmentRefs = useRef<HTMLDivElement[] | null[]>([]);
    const [isFocused, setIsFocused] = useState<boolean>(false);
    const focusableTypes: Intl.DateTimeFormatPartTypes[] = ['month', 'day', 'year'];

    const dayValue = value ? getDate(value) : undefined;
    const monthValue = value ? getMonth(value) + 1 : undefined;
    const yearValue = value ? getYear(value) : undefined;

    const [partialDayValue, setPartialDayValue] = useState(dayValue?.toString());
    const [partialMonthValue, setPartialMonthValue] = useState(monthValue?.toString());
    const [partialYearValue, setPartialYearValue] = useState(yearValue?.toString());

    const [isDayValueValid, setIsDayValueValid] = useState(true);
    const [isMonthValueValid, setIsMonthValueValid] = useState(true);
    const [isYearValueValid, setIsYearValueValid] = useState(true);

    const [dayCaretPosition, setDayCaretPosition] = useState(-1);
    const [monthCaretPosition, setMonthCaretPosition] = useState(-1);
    const [yearCaretPosition, setYearCaretPosition] = useState(-1);

    const firstRender = useRef(true);

    const displayValue = getDisplayText(
      dateTimeParts,
      partialDayValue,
      partialMonthValue,
      partialYearValue,
      dateSegmentPlaceholder
    );

    const getFocusableIndex = (index: number, isPrevious = false) => {
      while (index >= 0 && index <= dateSegmentRefs.current.length) {
        index = isPrevious ? index - 1 : index + 1;
        if ((dateSegmentRefs.current?.[index] as HTMLDivElement)?.contentEditable) {
          return index;
        }
      }
      return -1;
    };

    const getCaretPosition = (type: Intl.DateTimeFormatPartTypes) => {
      switch (type) {
        case 'month':
          return monthCaretPosition;
        case 'day':
          return dayCaretPosition;
        case 'year':
          return yearCaretPosition;
      }
    };

    const setCaretPosition = (type: Intl.DateTimeFormatPartTypes, isPrevious: boolean) => {
      switch (getNextSegmentType(dateTimeParts, focusableTypes, type, isPrevious)) {
        case 'month':
          if (isPrevious) {
            setMonthCaretPosition(partialMonthValue ? partialMonthValue.length : 0);
          } else {
            setMonthCaretPosition(0);
          }
          break;
        case 'day':
          if (isPrevious) {
            setDayCaretPosition(partialDayValue ? partialDayValue.length : 0);
          } else {
            setDayCaretPosition(0);
          }
          break;
        case 'year':
          if (isPrevious) {
            setYearCaretPosition(partialYearValue ? partialYearValue.length : 0);
          } else {
            setYearCaretPosition(0);
          }
          break;
      }
    };

    const focusNextSegment = (type: Intl.DateTimeFormatPartTypes, index: number, isPrevious = false) => {
      if (index === 0 && isPrevious) {
        onContainerFocusChange?.(true);
        return;
      }
      const nextFocusableIndex = getFocusableIndex(index, isPrevious);
      if (nextFocusableIndex < 0) {
        onContainerFocusChange?.(false);
        return;
      }
      setCaretPosition(type, isPrevious);
      (dateSegmentRefs.current?.[nextFocusableIndex] as HTMLDivElement)?.focus();
    };

    const getSegmentValue = (type: Intl.DateTimeFormatPartTypes): string | undefined => {
      switch (type) {
        case 'month':
          return partialMonthValue;
        case 'day':
          return partialDayValue;
        case 'year':
          return partialYearValue;
      }
    };

    /**
     * Traverse dateTimeParts backwards, grab last date segment that isn't a literal, apply text-align start styling
     * Can't just grab last index segment because in some locales date ends in a literal (ex. ko-KR ends in ".")
     */
    const getLastNonLiteralSegmentType = (segmentType: Intl.DateTimeFormatPartTypes): boolean | undefined => {
      for (let index = dateTimeParts.length - 1; index >= 0; index--) {
        if (dateTimeParts[index].type !== 'literal') {
          return dateTimeParts[index].type === segmentType;
        }
      }
    };

    const getFirstNonLiteralSegmentType = (segmentType: Intl.DateTimeFormatPartTypes): boolean | undefined => {
      for (let index = 0; index <= dateTimeParts.length - 1; index++) {
        if (dateTimeParts[index].type !== 'literal') {
          return dateTimeParts[index].type === segmentType;
        }
      }
    };

    const getIsValid = (type: Intl.DateTimeFormatPartTypes): boolean | undefined => {
      switch (type) {
        case 'month':
          return isMonthValueValid;
        case 'day':
          return isDayValueValid;
        case 'year':
          return isYearValueValid;
      }
    };

    const handleChange = (type: Intl.DateTimeFormatPartTypes, newValue: string | undefined) => {
      switch (type) {
        case 'month':
          setPartialMonthValue(newValue);
          break;
        case 'day':
          setPartialDayValue(newValue);
          break;
        case 'year':
          setPartialYearValue(newValue);
          break;
      }
    };

    const handleSegmentFocus = (e: React.FocusEvent, type: Intl.DateTimeFormatPartTypes) => {
      // reset other not focused segments caret position
      // when using tab to focus, it wouldn't trigger focusNextSegment
      // need to reset other segment's caretPosition
      switch (type) {
        case 'month':
          setDayCaretPosition(-1);
          setYearCaretPosition(-1);
          break;
        case 'day':
          setMonthCaretPosition(-1);
          setYearCaretPosition(-1);
          break;
        case 'year':
          setMonthCaretPosition(-1);
          setDayCaretPosition(-1);
          break;
      }
      if (!isFocused) {
        onFocus?.(e);
        setIsFocused(true);
      }
    };

    const handleSegmentBlur = (e: React.FocusEvent) => {
      if (
        e.relatedTarget === null ||
        !(dateSegmentRefs.current?.[0] as HTMLElement)?.parentElement?.contains(e.relatedTarget as HTMLElement)
      ) {
        onBlur?.(e);
        setIsFocused(false);
      }
    };

    const generateDateSegments = () => {
      return dateTimeParts.map((segment, i) => {
        switch (segment.type) {
          case 'literal':
            return (
              <DateSegment
                key={segment.id}
                id={segment.id}
                index={i}
                placeholder={segment.value}
                type={segment.type}
                disabled={disabled}
                testId={testId}
                isParentFocused={isParentFocused || isFocused}
              />
            );
          case 'day':
          case 'month':
          case 'year':
            // only support month, day and year for now
            return (
              <DateSegment
                key={segment.id}
                id={segment.id}
                index={i}
                ref={(el) => {
                  dateSegmentRefs.current[i] = el;
                }}
                isLastSegment={getLastNonLiteralSegmentType(segment.type)}
                type={segment.type}
                testId={testId}
                placeholder={dateSegmentPlaceholder[segment.type]}
                disabled={disabled}
                readOnly={readOnly}
                separator={getNextSeparator(dateTimeParts, i)}
                value={getSegmentValue(segment.type)}
                focusNextSegment={focusNextSegment}
                maxValueLength={getSegmentMaxValueLength(segment.type)}
                onChange={handleChange}
                valid={getIsValid(segment.type)}
                caretPosition={getCaretPosition(segment.type)}
                onFocus={handleSegmentFocus}
                onBlur={handleSegmentBlur}
                ariaLabel={buildDateFieldAriaLabelValue(
                  segment.type,
                  textMap,
                  displayValue,
                  getFirstNonLiteralSegmentType(segment.type) || false
                )}
                textMap={textMap}
                isParentFocused={isParentFocused || isFocused}
              />
            );
        }
        return null;
      });
    };

    useImperativeHandle(ref, () => ({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _focusOnMouseXPosition: (x: number) => {
        let focusOnLastSegment = true;
        for (let index = 1; index < dateSegmentRefs.current.length; index++) {
          if (
            dateSegmentRefs.current?.[index]?.contentEditable &&
            (dateSegmentRefs.current[index] as HTMLDivElement).getBoundingClientRect()?.left > x
          ) {
            dateSegmentRefs.current[getFocusableIndex(index, true)]?.focus();
            focusOnLastSegment = false;
            break;
          }
        }
        if (focusOnLastSegment) {
          dateSegmentRefs.current[getFocusableIndex(dateSegmentRefs.current.length, true)]?.focus();
        }
      },
      clear: () => {
        setPartialDayValue('');
        setPartialMonthValue('');
        setPartialYearValue('');
      },
      setCursorStart: () => {
        dateSegmentRefs.current?.length && dateSegmentRefs.current[0]?.focus();
      },
      setCursorEnd: () => {
        setYearCaretPosition(partialYearValue ? partialYearValue.length : 0);
        dateSegmentRefs.current?.length && dateSegmentRefs.current[dateSegmentRefs.current.length - 1]?.focus();
      },
    }));

    useEffect(() => {
      if (!firstRender.current) {
        const newMaxDayValue = getMaxDayValue(partialYearValue, partialMonthValue);
        const validMonth = !partialMonthValue || (parseInt(partialMonthValue) > 0 && parseInt(partialMonthValue) <= 12);
        const validYear = !partialYearValue || (parseInt(partialYearValue) > 0 && parseInt(partialYearValue) <= 9999);
        const validDay =
          !partialDayValue || (parseInt(partialDayValue) > 0 && parseInt(partialDayValue) <= newMaxDayValue);
        setIsMonthValueValid(validMonth);
        setIsYearValueValid(validYear);
        setIsDayValueValid(validDay);

        // required to have setTimeout to trigger the announce
        setTimeout(() => {
          !validDay && textMap?.invalidDay && announce(textMap?.invalidDay);
          !validMonth && textMap?.invalidMonth && announce(textMap?.invalidMonth);
          !validYear && textMap?.invalidYear && announce(textMap?.invalidYear);
        });

        if (validDay && validMonth && validYear && partialDayValue && partialMonthValue && partialYearValue) {
          const newDate = new Date(
            parseInt(partialYearValue),
            parseInt(partialMonthValue) - 1,
            parseInt(partialDayValue)
          );
          // if Year is less than 100, it will map to 1901
          // using setFullYear for any year less than 100 to work correctly
          newDate.setFullYear(parseInt(partialYearValue));

          // only trigger onChange is value has changed
          if (!isSameDay(newDate, startOfDay(value as Date))) {
            onChange?.(displayValue, newDate);
          }
        } else {
          onChange?.(displayValue);
        }
      }
      firstRender.current = false;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [partialDayValue, partialMonthValue, partialYearValue]);

    // Update display value if value changed as a controlled component
    useEffect(() => {
      // If value exists then update segment values
      if (value) {
        if (!partialDayValue || Number.parseInt(partialDayValue) !== getDate(value)) {
          setPartialDayValue(dayValue?.toString());
        }
        if (!partialMonthValue || Number.parseInt(partialMonthValue) !== getMonth(value) + 1) {
          setPartialMonthValue(monthValue?.toString());
        }
        if (!partialYearValue || Number.parseInt(partialYearValue) !== getYear(value)) {
          setPartialYearValue(yearValue?.toString());
        }
      }
    }, [value]);

    return <>{generateDateSegments()}</>;
  }
);

DateSegmentContainer.displayName = 'DateSegmentContainer';
