@use '../../../variables.scss';

.evrDateSegment {
  width: auto;
  //caret-color: transparent;
  text-align: center;
  outline: none;

  &.placeholder {
    color: var(--evr-content-primary-lowemp);
  }
  &.invalid {
    background-color: var(--evr-interactive-status-error-decorative);
    font-weight: var(--evr-bold-weight);
    border-radius: var(--evr-radius-3xs);
  }
  &.disabled {
    color: var(--evr-inactive-content);
  }
  &.readOnly {
    color: var(--evr-content-primary-default);
  }
  &.minWidth1ch {
    min-width: 1ch;
  }
  &.minWidth2ch {
    min-width: 2ch;
  }
  &.minWidth3ch {
    min-width: 3ch;
  }
  &.minWidth4ch {
    min-width: 4ch;
  }
  &.lastSegment {
    text-align: start;
  }
}
