import React, { HTMLAttributes, forwardRef, useCallback, useContext, useEffect, useLayoutEffect, useRef } from 'react';
import classnames from 'classnames';

import { mergeRefs } from '../../../utils';
import { DateFieldContext } from '../date-field-context';
import { IDateFieldTextMapBase } from '../date-field-helpers';

import styles from './date-segment.module.scss';

export interface IDateSegment {
  id: string;
  /** The index of current editable segment. */
  index: number;
  /** The type of segment. */
  type: Intl.DateTimeFormatPartTypes;
  /** The numeric value for the segment, if applicable. */
  value?: string;
  /** A placeholder string for the segment. */
  placeholder: string;
  /** A separator string for date field. */
  separator?: string;
  /** Whether the segment is disabled. */
  disabled?: boolean;
  /** Whether the segment is readOnly. */
  readOnly?: boolean;
  /** Whether the segment type is formatted last */
  isLastSegment?: boolean;
  /** Whether the segment is valid */
  valid?: boolean;
  maxValueLength?: number;
  testId?: string;
  onChange?: (type: Intl.DateTimeFormatPartTypes, inputValue: string | undefined) => void;
  focusNextSegment?: (type: Intl.DateTimeFormatPartTypes, index: number, isPrevious: boolean) => void;
  caretPosition?: number;
  onFocus?: (e: React.FocusEvent, type: Intl.DateTimeFormatPartTypes) => void;
  onBlur?: (e: React.FocusEvent, type: Intl.DateTimeFormatPartTypes) => void;
  ariaLabel?: string;
  textMap?: IDateFieldTextMapBase;
  /** Whether the parent container has focus */
  isParentFocused?: boolean;
}

export const DateSegment = forwardRef<HTMLDivElement, IDateSegment>((props, ref) => {
  const {
    id,
    type,
    value,
    placeholder,
    separator = '/',
    isLastSegment,
    disabled = false,
    readOnly = false,
    valid = true,
    index,
    maxValueLength,
    testId,
    onChange,
    focusNextSegment,
    caretPosition = -1,
    onFocus,
    onBlur,
    ariaLabel,
    textMap,
    isParentFocused,
  } = props;

  // use carePosition Ref to track the caretPosition
  // when using onChange to update the value, it caused the editableContent to refresh
  // which caused the caret position to reset to 0. Overrided that prevent caretPosition to jump to the 0
  const caretPositionRef = useRef(0);
  const deleteOrBackspaceKeyPressed = useRef(false);
  const segmentRef = useRef(null);

  const firstRender = useRef(true);
  const context = useContext(DateFieldContext);

  const setCursor = useCallback(
    (cursorPos: number) => {
      if (segmentRef.current && !firstRender.current) {
        const segmentDiv = segmentRef.current as HTMLDivElement;
        const selection = window.getSelection();
        const range = document.createRange();
        selection?.removeAllRanges();
        if (segmentDiv?.childNodes[0]?.textContent && cursorPos <= segmentDiv?.childNodes[0]?.textContent?.length) {
          range.setStart(segmentDiv.childNodes[0], cursorPos);
        }
        range.collapse(true);
        selection?.addRange(range);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [value]
  );

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const currentCaretPosition = getCaretPosition();
    const valueLength = `${value}`.length;

    if (e.key === 'Backspace') {
      deleteOrBackspaceKeyPressed.current = true;
    }
    if ((e.key === 'ArrowLeft' || e.key === 'Backspace') && (currentCaretPosition === 0 || !value)) {
      e.preventDefault();
      focusNextSegment?.(type, index, true);
    } else if ((e.key === 'ArrowRight' && (!value || currentCaretPosition === valueLength)) || e.key === separator) {
      e.preventDefault();
      focusNextSegment?.(type, index, false);
    } else if (e.key === 'Delete') {
      if (!value) e.preventDefault();
      deleteOrBackspaceKeyPressed.current = true;
    }

    // regex to take 0-9 value only
    const updatedKey = e.key.replace(/\D/g, '');
    if (
      (!updatedKey || (value && maxValueLength && maxValueLength <= valueLength)) &&
      e.key !== 'Tab' &&
      e.key !== 'ArrowLeft' &&
      e.key !== 'ArrowRight' &&
      e.key !== 'Delete' &&
      e.key !== 'Backspace'
    ) {
      e.preventDefault();
    }
  };

  const getCaretPosition = () => {
    const editableDiv = segmentRef.current ? (segmentRef.current as HTMLDivElement) : null;
    let caretPos = 0,
      sel,
      range;
    if (window.getSelection) {
      sel = window.getSelection();
      if (sel?.rangeCount) {
        range = sel.getRangeAt(0);
        if (range.commonAncestorContainer.parentNode == editableDiv) {
          caretPos = range.endOffset;
        }
      }
    }
    return caretPos;
  };

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    const updatedValue = e.currentTarget.innerText?.replace(/\D/g, '');
    caretPositionRef.current = getCaretPosition();
    onChange?.(type, updatedValue);
  };

  const handleFocus = (e: React.FocusEvent) => {
    onFocus?.(e, type);
  };

  const handleBlur = (e: React.FocusEvent) => {
    onBlur?.(e, type);
  };

  const runSetCursorWithContext = (position: number) => {
    // prevent setCursor from running when contained in the date picker
    if (context.type && context.getDatefieldCanPositionCursor?.()) {
      setCursor(position);
      return;
    }
    // allow setCursor to always run when it isn't the child of another relevant component
    if (!context.type) {
      setCursor(position);
    }
  };

  const updateContext = () => {
    // allow setCursor to run as usual on subsequent 'value' effects when all the segments have been iterated through
    if (isLastSegment && context.type && !context.getDatefieldCanPositionCursor?.()) {
      context.setDatefieldCanPositionCursor?.(true);
    }
  };

  useLayoutEffect(() => {
    // Execute the internal focus/cursor logic within the segments only if the parent component is already focused,
    // because we don't want to end up stealing focus from a completely different component.
    if (isParentFocused) {
      runSetCursorWithContext(caretPositionRef.current);
    }
    // Intentionally only run this if the `value` changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  useEffect(() => {
    firstRender.current = false;

    // Execute the internal focus/cursor logic within the segments only if the parent component is already focused,
    // because we don't want to end up stealing focus from a completely different component.
    if (isParentFocused) {
      const currentCaretPosition = getCaretPosition();
      if (value && currentCaretPosition === 0 && !deleteOrBackspaceKeyPressed.current) {
        // handle the situation when using mouse click to change the caretPosition on placeholder value (value is null)
        // make sure the cursor is on the right position after the value has updated
        runSetCursorWithContext(value?.length);
      } else if (value && currentCaretPosition === maxValueLength && currentCaretPosition === `${value}`.length) {
        // handle focus to the next segment when the current segment met the maxValueLength
        focusNextSegment?.(type, index, false);
      }

      deleteOrBackspaceKeyPressed.current = false;
      updateContext();
    }
    // Intentionally only run this if the `value` changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  useLayoutEffect(() => {
    if (caretPosition >= 0) {
      setCursor(caretPosition);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [caretPosition]);

  const segmentProps: null | HTMLAttributes<HTMLDivElement> =
    type === 'literal'
      ? {}
      : {
          role: 'textbox',
          tabIndex: !disabled ? 0 : -1,
          inputMode: 'numeric',
          'aria-multiline': false,
          'aria-label': ariaLabel,
          'aria-invalid': !valid,
          'aria-describedby': value ? '' : `${id}-format-desc`,
          'aria-readonly': readOnly,
          spellCheck: 'false',
          contentEditable: !disabled && !readOnly,
          suppressContentEditableWarning: true,
          onKeyDown: handleKeyDown,
          onInput: handleInput,
          onFocus: handleFocus,
          onBlur: handleBlur,
        };

  return (
    <>
      <div
        id={id}
        ref={mergeRefs([ref, segmentRef])}
        className={classnames('evrBodyText1', styles.evrDateSegment, {
          [styles.minWidth1ch]: maxValueLength === 1,
          [styles.minWidth2ch]: maxValueLength === 2,
          [styles.minWidth4ch]: maxValueLength === 4,
          [styles.lastSegment]: isLastSegment,
          [styles.invalid]: !valid,
          [styles.disabled]: disabled,
          [styles.readOnly]: readOnly,
          [styles.placeholder]: !!placeholder && (!value || value === ''),
        })}
        data-testid={testId ? `${testId}-${type}` : undefined}
        {...segmentProps}
      >
        {!value || value === '' ? placeholder : value}
      </div>
      <span id={`${id}-format-desc`} className={'evrBodyText1'} hidden={true}>
        {textMap?.formatLabel}
      </span>
    </>
  );
});

DateSegment.displayName = 'DateSegment';
