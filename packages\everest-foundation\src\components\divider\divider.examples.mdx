import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Divider } from './divider';
import { Button } from '../button';

export const scope = { Divider, Button };

Divider is used in a layout to separate the contents.

## Variations

### Horizontal - Default

The default horizontal divider is used to separate content horizontally.
`Variant` prop is set to `default` by default. `Vertical` prop is set to `false`, which is equivalent to horizontal orientation by default.

export const defaultHorizontalDivider = `
    <div style={{marginBottom: 20}}>
        <p className='evrBodyText'>This is a</p>
        <Divider/>
        <p className='evrBodyText'>divider component</p>
    </div>
`;

<CodeExample scope={scope} code={defaultHorizontalDivider} />

### Horizontal - Low Emphasis

The low emphasis horizontal divider is used to separate content horizontally with a divider of lower opacity, meaning the divider will stand out less against the background.

export const lowEmpHorizontalDivider = `
    <div style={{marginBottom: 20}}>
        <p className='evrBodyText'>This is a</p>
        <Divider variant={"lowEmp"}/>
        <p className='evrBodyText'>divider component</p>
    </div>
`;

<CodeExample scope={scope} code={lowEmpHorizontalDivider} />

### Vertical - Default

A default vertical divider is used to separate content vertically. `Variant` prop is set to `default` by default.

export const defaultVerticalDivider = `() => {
    const styles = {
        row: {
            display: 'flex',
            margin: 10
        },
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
            <p className='evrBodyText'>This is a</p>
            <Divider vertical={true} />
            <p className='evrBodyText'>divider component</p>
        </Row>
    );
}`;

<CodeExample scope={scope} code={defaultVerticalDivider} />

### Vertical - Low Emphasis

A low emphasis vertical divider is used to separate content vertically with a divider of lower opacity, meaning the divider will stand out less against the background.

export const lowEmpVerticalDivider = `() => {
    const styles = {
        row: {
            display: 'flex',
            margin: 10
        },
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
            <p className='evrBodyText'>This is a</p>
            <Divider vertical={true} variant={"lowEmp"}/>
            <p className='evrBodyText'>divider component</p>
        </Row>
    );
}`;

<CodeExample scope={scope} code={lowEmpVerticalDivider} />

## Accessing Divider using ref

Click on the Button to access the Divider, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
         column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '10px'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
        <Column>
            <Row>
                <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
            </Row>
            <Row>
                <div>
                    <p className='evrBodyText'>This is a</p>
                    <Divider ref={ref}/>
                    <p className='evrBodyText'>divider component</p>
                </div>
            </Row>
        </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

When using vertical divider, it is recommended to apply `display: 'flex'` on parent container and use margin-right/left to control the spacing between divider and elements. Also width/height of the divider can be controlled through setting appropriate width/height of parent container.

## Accessibility

- `aria-hidden=true` is applied to remove from accessibility tree so screen reader can skip it.
- For vertical divider, using `<div role='separator' aria-orientation='vertical'>` was under consideration, but since divider is used for purely decorative purpose, decision was made to omit them.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
