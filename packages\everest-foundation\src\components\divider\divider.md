# Divider

## Summary

Research and document implementations for the Everest Divider.

- Start Date: 2022-02-24
- Figma link: https://www.figma.com/file/k6gcErcQQNjSOKLFj9u0PH/Divider?node-id=520%3A16277

## Detailed Design

Divider consists of `<hr>` tag with two different orientations: horizontal and vertical.

## API

Remember to include testId.

1. **testId**: `string | undefined`  
   Optional. Sets `data-testid` attribute on the containing html element.
1. **id**: `undefined | string`  
   optional
1. **vertical**: `boolean | undefined`
   Sets the orientation of the divider. By default, divider will be horizontal.
1. **variant**: `default | lowEmp`
   Sets the opacity of the divider. By default, `variant` will be "default".

## Accessibility

- `<hr>` tag will be applied for horizontal divider and `<div role='separator' aria-orientation='vertical'>` will be used for vertical divider to serve semantic purpose.
- Since divider is used for presentational purpose, `aria-hidden='true'` will be applied to both horizontal and vertical divider to delete from accessibility tree so screen reader won't detect them.

## Alternatives/Trade-Offs

- For vertical divider, decided to use `<div role='separator' aria-orientation='vertical'>` instead of `<hr>` as there were style conflicts between custom style and default `<hr>` style.

- There are numerous ways to implement vertical divider. One way was to use pseudo element on border `.divider::before` that use absolute positioning, but this require parent element to have `position:relative` which could potentially create conflict with children elements. Therefore, alternative way was chosen which is to recommends consumer to apply `display:flex` on parent container then implement vertical divider which essentially consists of border-left. This will cover the height of container. Since flexbox is popular technique to align children items in the container, this will create less conflict.

## Q&A

**Should divider be created using `<hr>` tag with styles or `<div>` tag with role sepator and aria-orientation `<div role='separator' aria-orientation='vertical'>`?**  
For horizontal divider, `<hr>` tag with custom styles will be used for semantic purpose, but for vertical divider, default style on `<hr>` creates conflict with custom styling, therefore `<div role='separator' aria-orientation='vertical'>` will be applied.

**Besides default/inverse border for color, what are the other styling requirement? Specially for vertical orientation?**
1px is the default width for divider. For margin and padding, we will let consumer control it.

**Why use px for width instead of rem?**
Unlike rem, px does not scale as viewport increase/decrease. The width of the divider should be fixed regardless of dimension of viewport, therefore px will be used.

**Should `aria-hidden='true'` be applied in order to remove divider from accessibility tree so screen reader won't read them?**
`aria-hidden='true'` will be applied as divider is used for presentational purpose only.

**Can feature teams be able to control radius of divider?**
No, feature teams will need to request Design System Management to add a variant.

## Future Considerations

Since divider can have various styles and designs, there could be the need for divider with different design such as rounded corner divider in the future.

## Other Design Systems

**Material UI** - https://mui.com/components/dividers/

- Renders <`hr`> by default and will change to <`div role='separator'`> for divider with text
- Supports HTML5 specification
  For example:

```
<ListItem>
   <Divider variant="inset" component="li" />
</ListItem>
```

**Semantic UI** - https://semantic-ui.com/elements/divider.html

- Supports various styles: Fitted, Section, Hidden and Clearing
- Vertical divider becomes horizontal when view is switched to mobile.

**Ant Design** - https://ant.design/components/divider/

- Supports dashed line and title within the divider
  For example:

```
----------Text----------
```

**Anvil** - https://anvil.servicetitan.com/components/divider/

- Supports control over Divider's gutter size.

**Adobe Spectrum** - https://react-spectrum.adobe.com/react-spectrum/Divider.html

- Supports sizing of the divider with 3 different sizes: small, medium, large

## Required PBIs

- Develop the component and include testing (React + a11y + unit + integration + visual + bug fixes)

## Acceptance Criteria

1. The name of a component is &lt;Divider&gt;
2. APIs:  
   a. orientation  
   b. color
3. Height of Vertical divider stretches out up to the height of parent container.
4. Width of horizontal divider stretches out up to the width of parent container.
5. Divider is a Decorative component, it is not focusable, not screen reader enabled.
