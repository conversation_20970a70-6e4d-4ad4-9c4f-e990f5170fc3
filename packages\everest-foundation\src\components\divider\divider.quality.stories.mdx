import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Divider } from './divider';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Divider"
  component={Divider}
  parameters={{
    backgrounds: {
      default: 'light',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/k6gcErcQQNjSOKLFj9u0PH/Divider?node-id=2%3A3',
    },
    controls: {
      sort: 'requiredFirst',
    },
    chromatic: Chromatic.ENABLE_CI,
  }}
  argTypes={{
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets the `data-test-id` attribute on the html element for automation testing.',
    },
    variant: {
      type: 'string',
      control: 'select',
      options: ['default', 'lowEmp'],
      description: 'Changes the opacity of the divider.',
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    vertical: {
      type: 'boolean',
      control: 'boolean',
      description: 'Sets the orientation of the divider.',
      table: {
        defaultValue: { summary: false },
      },
    },
  }}
  args={{
    vertical: false,
    variant: 'default',
    testId: 'divider-playground',
  }}
/>

# Divider

## Live Demo

<Canvas>
  <Story name="Default Horizontal">
    {(args) => {
      return (
        <div>
          <p className="evrBodyText">This is a</p>
          <Divider {...args} vertical={false} />
          <p className="evrBodyText">divider component</p>
        </div>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Low Emphasis Horizontal">
    {(args) => {
      return (
        <div>
          <p className="evrBodyText">This is a</p>
          <Divider {...args} vertical={false} variant="lowEmp" />
          <p className="evrBodyText">divider component</p>
        </div>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Vertical">
    {(args) => {
      return (
        <div style={{ display: 'flex' }}>
          <p className="evrBodyText">This is a</p>
          <Divider {...args} vertical />
          <p className="evrBodyText">divider component</p>
        </div>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Low Emphasis Vertical">
    {(args) => {
      return (
        <div style={{ display: 'flex' }}>
          <p className="evrBodyText">This is a</p>
          <Divider {...args} vertical variant="lowEmp" />
          <p className="evrBodyText">divider component</p>
        </div>
      );
    }}
  </Story>
</Canvas>
