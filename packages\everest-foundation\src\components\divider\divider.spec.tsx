import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import { Divider } from './divider';

describe('[Divider]', () => {
  const testId = 'divider-test-id';
  const emphasisVariant = 'lowEmp';
  const getDivider = () => screen.getByTestId(testId);

  [
    {
      name: 'Default Horizontal Divider',
      jsx: <Divider testId={testId} vertical={false} />,
    },
    {
      name: 'Low Emphasis Horizontal Divider',
      jsx: <Divider testId={testId} variant={emphasisVariant} vertical={false} />,
    },
    {
      name: 'Default Vertical Divider',
      jsx: <Divider testId={testId} vertical={true} />,
    },
    {
      name: 'Low Emphasis Vertical Divider',
      jsx: <Divider testId={testId} variant={emphasisVariant} vertical={true} />,
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getDivider()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  it('should have attribute aria-hidden set to true ', () => {
    render(<Divider testId={testId} vertical={false} />);
    expect(getDivider()).toHaveAttribute('aria-hidden', 'true');
  });

  it('horizontal divider should have hr tag ', () => {
    const { container } = render(<Divider testId={testId} vertical={false} />);
    expect(container.querySelector(`hr`)).toBeInTheDocument();
  });
});
