import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Divider } from './divider';
import Examples from './divider.examples.mdx';

<Meta
  title="Components/Divider"
  component={Divider}
  parameters={{
    status: {
      type: 'ready',
    },
    backgrounds: {
      default: 'light',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/yzd3fHCowfWDL4iTBAoJNv/Component-Documentation-for-Designers?node-id=2001%3A28860&t=T8R8rIMK0cxRvwUS-0',
    },
    controls: {
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    testId: {
      type: 'string',
      control: 'text',
      description:
        'Sets the `data-test-id` attribute on the html element for automation testing.',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'Optional unique identifier.',
    },
    variant: {
      type: 'string',
      control: 'select',
      options: ['default', 'lowEmp'],
      description: 'Changes the opacity of the divider.',
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    vertical: {
      type: 'boolean',
      control: 'boolean',
      description: 'Sets the orientation of the divider.',
      table: {
        defaultValue: { summary: false },
      },
    },
  }}
  args={{
    variant: 'default',
    vertical: false,
    testId: 'divider-test-id',
  }}
/>

# Divider

<Examples />

## Live Demo

<Canvas>
  <Story name="Divider">
    {(args) => {
      return args.vertical === false ? (
        <div>
          <p className="evrBodyText">This is a</p>
          <Divider {...args} />
          <p className="evrBodyText">divider component</p>
        </div>
      ) : (
        <div>
          <div style={{ display: 'flex', height: '2rem' }}>
            <p className="evrBodyText">This is a</p>
            <Divider {...args} />
            <p className="evrBodyText">divider component</p>
          </div>
        </div>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Divider" />
