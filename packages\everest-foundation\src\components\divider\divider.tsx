import * as React from 'react';
import { createTestId } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { mergeRefs } from '../../utils';

import styles from './divider.module.scss';
export interface IDividerProps {
  id?: string;
  variant?: 'default' | 'lowEmp';
  vertical?: boolean;
  testId?: string;
  ref?: React.ForwardedRef<HTMLDivElement> | React.ForwardedRef<HTMLHRElement>;
}

export const Divider = React.forwardRef<HTMLElement, IDividerProps>(
  ({ id, testId, vertical = false, variant = 'default' }: IDividerProps, ref) => {
    const dataRef = React.useRef(null);

    React.useEffect(() => {
      if (testId && testId.length > 0 && dataRef.current) {
        createTestId(dataRef.current, testId);
      }
      /* added vertical as dependency as createTestID needs to be re-rendered everytime vertical prop value changes 
    in order for testID to be generated for both orientation.*/
    }, [dataRef, testId, vertical]);

    return !vertical ? (
      <hr
        id={id}
        ref={mergeRefs([ref, dataRef])}
        aria-hidden="true"
        className={classnames(styles.evrDivider, styles.horizontal, styles[variant])}
      />
    ) : (
      <div
        id={id}
        ref={mergeRefs([ref, dataRef])}
        aria-hidden="true"
        className={classnames(styles.evrDivider, styles.vertical, styles[variant])}
      ></div>
    );
  }
);

Divider.displayName = 'Divider';
