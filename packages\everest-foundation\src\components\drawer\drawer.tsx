import React, { forwardRef, PropsWithChildren } from 'react';
import classnames from 'classnames';

import { SidePanel, SidePanelBody } from '../side-panel';

import styles from './drawer.module.scss';

export interface IDrawer {
  /** The id of the drawer. */
  id: string;

  /** Sets **data-testid** attribute on the html element. */
  testId?: string;

  /** Indicates whether the drawer is expanded or collapsed. */
  expanded: boolean;

  /**
   * Indicates whether the side nav is a push vs side panel. Defaults to `true`.
   * @default true
   */
  persistent?: boolean;
}

export const Drawer = forwardRef<HTMLDivElement, PropsWithChildren<IDrawer>>((props, ref): JSX.Element => {
  const { expanded = false, persistent = true, id, testId, children } = props;

  if (!persistent && expanded) {
    // Render as a SidePanel when non-persistent and expanded
    return (
      <SidePanel id={id} testId={testId} open={expanded} size="sm" ref={ref} anchor="left">
        <SidePanelBody id={`${id}-side-panel-body`} testId={`${testId}-side-panel-body`}>
          {children}
        </SidePanelBody>
      </SidePanel>
    );
  }

  return (
    <div
      id={id}
      data-testid={testId}
      ref={ref}
      className={classnames(styles.evrDrawer, {
        [styles.evrDrawerCollapsed]: !expanded,
        [styles.evrDrawerExpanded]: expanded,
      })}
    >
      {children}
    </div>
  );
});

Drawer.displayName = 'Drawer';
