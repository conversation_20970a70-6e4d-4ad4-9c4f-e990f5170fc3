import { TState, TImplementation, TAction, TStateEffect } from '../../utils/use-state-machine';
import { IDataItem } from '../list-item';

//  dropdown States
const DEFAULT = 'DEFAULT';
const FOCUSED = 'FOCUSED';
const FOCUSED_ON_CLEAR = 'FOCUSED_ON_CLEAR';
const FOCUSED_WITH_OVERLAY = 'FOCUSED_WITH_OVERLAY';
const FOCUSED_ON_FIRST_LIST_ITEM = 'FOCUSED_ON_FIRST_LIST_ITEM';
const FOCUSING = 'FOCUSING';
const FOCUSING_WITH_OVERLAY = 'FOCUSING_WITH_OVERLAY';
const FOCUSING_ON_FIRST_LIST_ITEM = 'FOCUSING_ON_FIRST_LIST_ITEM';

export const dropdownStates = {
  DEFAULT, // Not focused, no overlay
  FOCUSED, // Focused, but no overlay, triggered by mouse
  FOCUSED_ON_CLEAR, // Focused on clear button
  FOCUSED_WITH_OVERLAY, // Focused, with overlay displayed
  FOCUSED_ON_FIRST_LIST_ITEM, // Focused on first item with overlay
  FOCUSING, // Focusing, transitioning to FOCUSED
  FOCUSING_WITH_OVERLAY, // Focusing, transitioning to FOCUSED_WITH_OVERLAY
  FOCUSING_ON_FIRST_LIST_ITEM, // Focusing, transitioning to FOCUSED_ON_FIRST_LIST_ITEM
};

// dropdown actions
const MOUSE_CLICK = 'MOUSE_CLICK';
const FOCUS = 'FOCUS';
const KEYBOARD_FOCUS_CLEAR = 'KEYBOARD_FOCUS_CLEAR';
const KEYBOARD_FOCUS_LIST_ITEM = 'KEYBOARD_FOCUS_LIST_ITEM';
const KEYBOARD_CLOSE_OVERLAY = 'KEYBOARD_CLOSE_OVERLAY';
const MAKE_SELECTION = 'MAKE_SELECTION';
const CLEAR_SELECTION = 'CLEAR_SELECTION';
const LIST_ITEM_TAB = 'LIST_ITEM_TAB';
const FOCUS_ON_FIRST_LIST_ITEM = 'FOCUS_ON_FIRST_LIST_ITEM';
const BLUR = 'BLUR';

export const dropdownActions = {
  MOUSE_CLICK,
  FOCUS,
  KEYBOARD_FOCUS_CLEAR,
  KEYBOARD_FOCUS_LIST_ITEM,
  KEYBOARD_CLOSE_OVERLAY,
  MAKE_SELECTION,
  CLEAR_SELECTION,
  LIST_ITEM_TAB,
  FOCUS_ON_FIRST_LIST_ITEM,
  BLUR,
};

export const dropdownActionImpls: TImplementation[] = [
  {
    action: { type: dropdownActions.MOUSE_CLICK },
    callback: (state: TState): TState => {
      if (state.value === dropdownStates.FOCUSED_WITH_OVERLAY) return { value: dropdownStates.FOCUSING };
      else if ([dropdownStates.DEFAULT, dropdownStates.FOCUSED].includes(state.value))
        return { value: dropdownStates.FOCUSING_WITH_OVERLAY };
      return { value: dropdownStates.FOCUSED };
    },
  },
  {
    action: { type: dropdownActions.FOCUS },
    callback: (state: TState): TState => {
      if (state.value === dropdownStates.FOCUSED_WITH_OVERLAY) return { value: dropdownStates.FOCUSING_WITH_OVERLAY };
      else if (state.value === dropdownStates.FOCUSING_WITH_OVERLAY)
        return { value: dropdownStates.FOCUSED_WITH_OVERLAY };
      else if (state.value === dropdownStates.FOCUSING_ON_FIRST_LIST_ITEM)
        return { value: dropdownStates.FOCUSED_ON_FIRST_LIST_ITEM };
      else if (state.value === dropdownStates.FOCUSED) return { value: dropdownStates.FOCUSING };
      return { value: dropdownStates.FOCUSED };
    },
  },
  {
    action: { type: dropdownActions.KEYBOARD_FOCUS_CLEAR },
    callback: (): TState => {
      return { value: dropdownStates.FOCUSED_ON_CLEAR };
    },
  },
  {
    action: { type: dropdownActions.KEYBOARD_FOCUS_LIST_ITEM },
    callback: (state: TState): TState => {
      if (state.value === dropdownStates.FOCUSED) return { value: dropdownStates.FOCUSED_WITH_OVERLAY };
      if (state.value === dropdownStates.FOCUSED_WITH_OVERLAY) return { value: dropdownStates.FOCUSING_WITH_OVERLAY };
      return state;
    },
  },
  {
    action: { type: dropdownActions.KEYBOARD_CLOSE_OVERLAY },
    callback: (state: TState): TState => {
      if (state.value === dropdownStates.FOCUSED_WITH_OVERLAY) return { value: dropdownStates.FOCUSED };
      return state;
    },
  },
  {
    action: { type: dropdownActions.MAKE_SELECTION },
    callback: (state: TState): TState => {
      if ([dropdownStates.FOCUSED_WITH_OVERLAY, dropdownStates.FOCUSED_ON_FIRST_LIST_ITEM].includes(state.value))
        return { value: dropdownStates.FOCUSED };
      return state;
    },
  },
  {
    action: { type: dropdownActions.CLEAR_SELECTION },
    callback: (state: TState): TState => {
      if (state.value === dropdownStates.FOCUSED) return { value: dropdownStates.FOCUSING };
      return { value: dropdownStates.FOCUSED };
    },
  },
  {
    action: { type: dropdownActions.LIST_ITEM_TAB },
    callback: (): TState => {
      return { value: dropdownStates.DEFAULT };
    },
  },
  {
    action: { type: dropdownActions.FOCUS_ON_FIRST_LIST_ITEM },
    callback: (state: TState): TState => {
      if (state.value === dropdownStates.FOCUSED_WITH_OVERLAY)
        return { value: dropdownStates.FOCUSING_ON_FIRST_LIST_ITEM };
      return state;
    },
  },
  {
    action: { type: dropdownActions.BLUR },
    callback: (): TState => {
      return { value: dropdownStates.DEFAULT };
    },
  },
];

export function getDropdownStateEffects(
  dataRef: React.MutableRefObject<null>,
  clearButtonRef: React.MutableRefObject<null>,
  focusedListItemId: string,
  setOverlayVisible: React.Dispatch<React.SetStateAction<boolean>>,
  setSoftSelectedId: React.Dispatch<React.SetStateAction<string>>,
  setIsFocusing: React.Dispatch<React.SetStateAction<boolean>>,
  setInTransition: React.Dispatch<React.SetStateAction<boolean>>
): TStateEffect[] {
  return [
    {
      state: { value: dropdownStates.DEFAULT },
      onEffect: () => {
        setOverlayVisible(false);
        setSoftSelectedId('');
      },
    },
    {
      state: { value: dropdownStates.FOCUSED },
      onEffect: () => {
        setOverlayVisible(false);
        setSoftSelectedId('');
        setTimeout(() => {
          // setTimeout to handle firefox focus back to the dropdown when clear Button clicked while focused
          dataRef.current && (dataRef.current as HTMLElement).focus();
        });
        setInTransition(false);
      },
    },
    {
      state: { value: dropdownStates.FOCUSED_ON_CLEAR },
      onEffect: () => {
        setOverlayVisible(false);
        setSoftSelectedId('');
        clearButtonRef.current && (clearButtonRef.current as HTMLElement).focus();
      },
    },
    {
      state: { value: dropdownStates.FOCUSED_WITH_OVERLAY },
      onEffect: () => {
        setOverlayVisible(true);
        setSoftSelectedId(focusedListItemId);
        setInTransition(false);
      },
    },
    {
      state: { value: dropdownStates.FOCUSED_ON_FIRST_LIST_ITEM },
      onEffect: () => {
        setSoftSelectedId(focusedListItemId);
        setInTransition(false);
      },
    },
    {
      state: { value: dropdownStates.FOCUSING },
      onEffect: () => {
        setIsFocusing(true);
      },
    },
    {
      state: { value: dropdownStates.FOCUSING_WITH_OVERLAY },
      onEffect: () => {
        setIsFocusing(true);
      },
    },
    {
      state: { value: dropdownStates.FOCUSING_ON_FIRST_LIST_ITEM },
      onEffect: () => {
        setIsFocusing(true);
      },
    },
  ];
}

export function interceptTabKeyDown(dataRef: React.MutableRefObject<null>, dispatch: React.Dispatch<TAction>): void {
  /**
   * intercepting the Tab on overlay Items and forced it to focus on the Dropdown
   * the Tab execution will happen after, which make sure to maintain the proper Tab order
   * onKeydown - as soon as the keyboard key is pressed before the character is typed
   * onKeyup - as soon as the keyboard key is released, the event is triggered.
   *          This event is triggered after the character is typed
   */
  dataRef.current && (dataRef.current as HTMLElement).focus();
  dispatch({ type: dropdownActions.LIST_ITEM_TAB });
}

/**
 * default search will cycle among the options starting with @searchValue character
 */
export function searchListItem(
  searchValue: string,
  options: IDataItem[],
  softSelectedId: string,
  overrideSearchMethod?: (currentIndex: number, options: IDataItem[], searchValue: string) => number
): string {
  const softSelectedIndex = options.findIndex((x) => x.id === softSelectedId);
  let currentIndex = softSelectedIndex;
  if (overrideSearchMethod) {
    currentIndex = overrideSearchMethod(currentIndex, options, searchValue);
  } else {
    let firstFoundIndex = -1;
    const searchText = searchValue.toUpperCase();
    for (const [index, option] of options.entries()) {
      if (option.title.toUpperCase().startsWith(searchText)) {
        if (firstFoundIndex < 0) firstFoundIndex = index;
        if (index > softSelectedIndex) {
          currentIndex = index;
          break;
        }
      }
    }
    // No options found after currentIndex, cycle back to the beginning
    if (currentIndex === softSelectedIndex && firstFoundIndex >= 0) {
      currentIndex = firstFoundIndex;
    }
  }

  return currentIndex >= 0 ? options[currentIndex].id : options[0].id;
}
