import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Dropdown } from './dropdown';
import { Icon } from '../icon';
import { Button } from '../button';
import { Markdown } from '@storybook/blocks';
import IDataItem from '../list-item/i-data-item.md?raw';

export const scope = { Dropdown, Button, Icon };

`Dropdown` component lets user choose one option from a list of options.

`Dropdown` is a controlled component with user-made selections held in external state.

There are props required to control the state of the component to create a functional `Dropdown`.

- `value` - controls the `Dropdown` value
- `onChange` - callback when `Dropdown` value has updated

The option is an `IDataItem` which defined as the following

<Markdown>{IDataItem}</Markdown>

## Basic Usage

`Dropdown` is a controlled component. The `value` prop controls the dropdown value.  
To update value, we can use `onChange` or `onClear` function which return the newly selected value and update the value.

export const basicDropdownCode = `() => {
    const options = [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
      { title: 'Third Option', id: 'item3' },
    ];
    const [value, setValue] = React.useState(null);
    const handleChange = (value, index) => { setValue(value); };
    const handleClear = () => { setValue(null); };
    return (
      <div style={{ width: '50%', height: '60px' }}>
        <Dropdown
          label="This is a label" 
          ariaLabel={'This is an aria-label'}
          options={options}
          value={value} 
          onChange={handleChange} 
          onClear={handleClear}
          clearButtonAriaLabel={'Clear Selection'}
          noResultsText="No matches found"
        />
      </div>
    );
}`;

<CodeExample scope={scope} code={basicDropdownCode} />

## Variations

`Dropdown` has different variations based on the props being used.

export const variationsCode = `() => {
    const styles = {
        row: {
            width: '55%',
            marginBottom: '2.5rem'        
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%'
        },
        headerText: {
            marginBlockEnd: '0.625rem',
            width: '70%'
        },
        code: {
            // copied from storybook
            lineHeight: '1',
            margin: '0 2px',
            padding: '3px 5px',
            whiteSpace: 'nowrap',
            borderRadius: '3px',
            fontSize: '13px',
            border: '1px solid #EEEEEE',
            color: 'rgba(51,51,51,0.9)',
            backgroundColor: '#F8F8F8',
        }
    };
    const HeaderText = ({ children }) => <div style={styles.headerText}><p className='evrBodyText'>{children}</p></div>;
    const [value, setValue] = React.useState(null);
    const handleChange = (value, index) => { setValue(value); };
    const firstOption = { title: 'First Option', id: 'item1' };
    const secondOption = { title: 'Second Option', id: 'item2' };
    const thirdOption = { title: 'Third Option', id: 'item3' };
    const fourthOption = { title: 'Fourth Option', id: 'item4' };
    const fifthOption = { title: 'Fifth Option', id: 'item5' };
    const sixthOption = { title: 'Sixth Option', id: 'item6' };
    const options = [ firstOption, secondOption, thirdOption ];
    const scrollbarOptions = [ firstOption, secondOption, thirdOption, fourthOption, fifthOption, sixthOption ];
    const iconOptions = [
      { title: 'First Option', iconName: 'heartOutline', id: 'item1' },
      { title: 'Second Option', iconName: 'home', id: 'item2' },
      { title: 'Third Option', iconName: 'help', id: 'item3' },
    ];
    return (
        <div style={styles.column}>
            <div style={styles.row}>
                <HeaderText><code style={styles.code}>Dropdown</code> with Label</HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={options}
                    ariaLabel={'This is an aria-label'}
                    noResultsText="No matches found"
                />
            </div>
            <div style={styles.row}>
                <HeaderText><code style={styles.code}>Dropdown</code> option with icon</HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={iconOptions}
                    ariaLabel={'This is an aria-label'}
                    noResultsText="No matches found"
                />
            </div>
            <div style={styles.row}>
                <HeaderText><code style={styles.code}>Dropdown</code> with scrollbar</HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={scrollbarOptions}
                    ariaLabel={'This is an aria-label'}
                    noResultsText="No matches found"
                />
            </div>            
            <div style={styles.row}>
                <HeaderText><code style={styles.code}>Dropdown</code> with selected value</HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={options}
                    value={secondOption}
                    ariaLabel={'This is an aria-label'}
                    clearButtonAriaLabel={'Clear Selection'}
                    noResultsText="No matches found"
                />
            </div>   
            <div style={styles.row}>
                <HeaderText>Disabled <code style={styles.code}>Dropdown</code></HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={options}
                    value={secondOption}
                    disabled
                    ariaLabel={'This is an aria-label'}
                    noResultsText="No matches found"
                />
            </div>
            <div style={styles.row}>
                <HeaderText>ReadOnly <code style={styles.code}>Dropdown</code></HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={options}
                    value={secondOption}
                    readOnly
                    ariaLabel={'This is an aria-label'}
                    noResultsText="No matches found"
                />
            </div>
            <div style={styles.row}>
                <HeaderText><code style={styles.code}>Dropdown</code> with helper text</HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={options}
                    helperText="This is a helper text"
                    ariaLabel={'This is an aria-label'}
                    noResultsText="No matches found"
                />
            </div>
            <div style={styles.row}>
                <HeaderText><code style={styles.code}>Dropdown</code> with an error message</HeaderText>
                <Dropdown 
                    label="This is a label" 
                    options={options}
                    status="error" 
                    statusMessage="This is an error message" 
                    statusMessagePrefix="Error:"
                    ariaLabel="This is an aria-label"
                    noResultsText="No matches found"
                />
            </div>
        </div>
    );
}`;

<CodeExample scope={scope} code={variationsCode} />

### Override Dropdown search method

Override the default search method to use includes instead of startsWith

export const overrideSearchMethodCode = `() => {
    const overrideSearchMethod = (currentIndex, options, searchValue) => {
        let nextIndex = currentIndex;
        let firstFoundIndex = -1;
        const searchText = searchValue.toUpperCase();
        for (let i = 0; i < options.length; i++) { 
            let option = options[i];
            if (option.title.toUpperCase().includes(searchText)) {
                if (firstFoundIndex < 0) firstFoundIndex = i;
                if (i > currentIndex) {
                    nextIndex = i;
                    break;
                }
            }
        }
        if (nextIndex === currentIndex && firstFoundIndex >= 0) {
            nextIndex = firstFoundIndex;
        }
        return nextIndex;
    };
    const options = [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
      { title: 'Third Option', id: 'item3' },
    ];
    return (
        <div style={{ width: '50%', height: '60px' }}>
            <Dropdown 
                label="This is a label" 
                overrideSearchMethod={overrideSearchMethod}
                options={options} 
                ariaLabel={'This is an aria-label'}
                noResultsText="No matches found"
            />
        </div>
    );
}`;

<CodeExample scope={scope} code={overrideSearchMethodCode} />

### Hidden Clear button

The `hideClearButton` prop hides the clear button when a selection is made.

export const controlledWithHiddenClearButtonCode = `() => {           
    const [value, setValue] = React.useState(null);
    const options = [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
      { title: 'Third Option', id: 'item3' },
    ];
    const handleChange = (value) => { value && setValue(value); };
    const handleClear = () => { setValue(null); };
    return (
        <div style={{ width: '50%', height: '60px' }}>
            <Dropdown 
                value={value} 
                label="This is a label" 
                options={options} 
                onChange={handleChange} 
                onClear={handleClear} 
                ariaLabel={'This is an aria-label'}
                hideClearButton
                noResultsText="No matches found"
            />
        </div>
    )  
  }
`;

<CodeExample scope={scope} code={controlledWithHiddenClearButtonCode} />

### Dropdown with Grouped Items

The `options` data can contain grouped items. (See the [How To Use](#how-to-use) section for more details)

export const groupingCode = `() => {
  const [value, setValue] = React.useState(null);
  const handleChange = (value, index) => { setValue(value); };
  const options = [
    { title: 'Group 1',  id: 'group-1', items: [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
      { title: 'Third Option', id: 'item3' },
    ] },
    { title: 'Group 2', id: 'group-2', items: [
      { title: 'Fourth Option', id: 'item4' },
      { title: 'Fifth Option', id: 'item5' },
      { title: 'Sixth Option', id: 'item6' },
    ] },
  ];
  return (
    <div style={{ width: '50%', height: '60px' }}>
      <Dropdown 
        label="This is a label" 
        options={options}
        ariaLabel="This is an aria-label"
        value={value}
        onChange={handleChange}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={groupingCode} />

### Dropdown with Data Load

Use the `loading` prop in combination with `loadingText` and `noResultsText` to indicate to the user when you are fetching data. See the below example.

export const dropdownWithDataLoadCode = `() => {
    const exampleProps = {        
        id: 'dropdown-data-loading-example',
        ariaLabel: 'Data Loading Dropdown',        
        label: 'Data Loading Dropdown',
        helperText: 'Data load is delayed by three seconds',
        helperTextPrefix: 'Note:',
        statusMessage: 'This is a status message',
        statusMessagePrefix: 'Prefix:',
        clearButtonAriaLabel: 'Clear dropdown value',        
        loadingText: 'Loading',
        noResultsText: 'No data loaded',
        textMap: {
            spinnerAriaLabel: 'Loading',
        },
    }
    const [value, setValue] = React.useState();
    const [options, setOptions] = React.useState([]);
    const [loading, setLoading] = React.useState(false);
    const handleChange = (value) => {
        value && setValue(value);
    };
    const handleClear = () => {
        setValue(null);
    };
    const handleClick = () => {
        setValue('');
        setOptions([]);
        setLoading(true);
        setTimeout(() => {
            setOptions([
                { title: 'First Option', id: 'item1' },
                { title: 'Second Option', id: 'item2' },
                { title: 'Third Option', id: 'item3' },
                { title: 'Fourth Option', id: 'item4' },
                { title: 'Fifth Option', id: 'item5' },
                { title: 'Sixth Option', id: 'item6' },
            ]);
            setLoading(false);
        }, 3000);
    };
    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',          
                gap: '1rem',
                width: '33%'
            }}
        >                 
            <Button
                id='reset-load-data-btn'
                label="Reset & Load Data"
                onClick={handleClick}
            />            
            <Dropdown
                {...exampleProps}
                onChange={handleChange}
                onClear={handleClear}
                value={value}                
                options={options}
                loading={loading}
            />
        </div>        
    );
}`;

<CodeExample scope={scope} code={dropdownWithDataLoadCode} />

### Dropdown with Custom List Items

Here's an example demonstrating the utilization of `itemRenderer` to craft custom list items for the Dropdown.<br /><br />
Customized list items should adhere to the following guidelines:

- Users are expected to handle any potential issues, particularly regarding accessibility.
- They should be read-only.
- Avoid heavy integration with other components.

export const dropdownWithCustomListItemsCode = `() => {
  const exampleProps = {
    id: 'dropdown-with-custom-list-example',
    ariaLabel: 'Custom List Dropdown',        
    label: 'Custom List Dropdown',
    clearButtonAriaLabel: 'Clear dropdown value',        
    loadingText: 'Loading',
    noResultsText: 'No data loaded',
  };
  const options = [
      { id: 'id-0', title: 'Damon Stoudamire', iconName: 'heartOutline', additionalData: { description: '1st Rookie of The Year' } },
      { id: 'id-1', title: 'Vince Carter', iconName: 'rocketship', additionalData: { description: 'Most explosive' } },
      { id: 'id-2', title: 'Kyle Lowry', iconName: 'wrench', additionalData: { description: 'Pitbull' } },
      { id: 'id-3', title: 'DeMar DeRozan', iconName: 'trashCan', additionalData: { description: 'Disappears in playoffs' } },
      { id: 'id-4', title: 'Kawhi Leonard', iconName: 'performance', additionalData: { description: 'Brought 1st championship' } },
    ];
  const [value, setValue] = React.useState();
  const handleChange = (value) => { 
    value && setValue(value); 
  };
  const handleClear = () => {
    setValue(undefined);
  };
  React.useEffect(() => {
    setValue(undefined);
  }, []);
  const itemRenderer = (dataItem, selected) => {
    const containerStyle = {
        display: 'flex',
        overflow: 'hidden',
    };
    const leftSideStyle = {
        display: 'flex',
        alignItems: 'center',
        marginLeft: '-0.1rem',
        marginRight: '0.5rem',
    };
    const rightSideStyle = {
        overflow: 'hidden',
        marginRight: '0.75rem',
    };
    const textResultStyle = {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
    };
    return <div style={containerStyle}>
        <div style={leftSideStyle}>
            <Icon name={dataItem.iconName} />
        </div>
        <div style={rightSideStyle}>
            <p style={textResultStyle} className={selected ? '_evrBodyText1 evrBold' : '_evrBodyText1'}>{dataItem.title}</p>
            <p style={textResultStyle} className='_evrBodyText1'>{dataItem.additionalData['description']}</p>
        </div>
    </div>
  };
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: '1rem',
        width: '33%',
      }}
    >
      <Dropdown
        {...exampleProps}
        options={options}
        value={value}
        onChange={handleChange}
        onClear={handleClear}
        itemRenderer={itemRenderer}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={dropdownWithCustomListItemsCode} />

## Accessing Dropdown using ref

Click on the Button to access the Dropdown, refer to the console for the element details.

export const refCode = `()=>{
    const ref = React.useRef(null);
    const [value, setValue] = React.useState(null);
    const options = [
        { title: 'First Option', id: 'item1' },
        { title: 'Second Option', id: 'item2' },
        { title: 'Third Option', id: 'item3' },
    ];
    const handleChange = (value) => { 
        value && setValue(value); 
    };
    const handleClear = () => { setValue(null); };
    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',          
                gap: '1rem',
                width: '45%'
            }}
        >
            <Button
                id='access-element-btn'
                label="Click to access element"
                onClick={()=>{
                    console.log(ref.current);
                    ref.current.focus();
                }}
            />      
            <Dropdown
                value={value}
                label="This is a label"
                options={options}
                onChange={handleChange}
                onClear={handleClear}
                ariaLabel={'This is an aria-label'}
                ref={ref}
                clearButtonAriaLabel={'Clear Selection'}
                noResultsText="No matches found"
            />
        </div>
    );

}  
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Consider selection control if only 2 options are available.

The `options` prop now supports grouped items. When a top-level option item contains an `items` array, it will be rendered as a group. Currently we only support one level of grouping but in the future we will support nested groups at multiple levels.

Below is an example of a grouped item:

```js
{
  id: 'group-0',
  title: 'Grouped items',
  items: [
    { id: 'item-1', title: 'grouped item 1' },
    { id: 'item-2', title: 'grouped item 2' },
  ],
}
```

## Accessibility

The keyboard behavior should be similar to [Select-Only Combobox Example](https://www.w3.org/WAI/ARIA/apg/example-index/combobox/combobox-select-only.html) with the following differences:

- Tabbing from focused on List Item would not make a selection and move the focus to the next focusable item
- Tabbing from the dropdown with value will focus on the clear button.

The following Roles and Attributes will be used:

| Role       | Description                                                                                                             |
| ---------- | ----------------------------------------------------------------------------------------------------------------------- |
| `combobox` | Identifies the input as a combobox                                                                                      |
| `listbox`  | Identifies the element as a listbox                                                                                     |
| `option`   | Identifies the element as a listbox option. The text content of the element provides the accessible name of the option. |

| Attribute               | Description                                                                                                                                                                                                                                                                                                           |
| ----------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `aria-labelledby`       | Identifies the element that labels the combobox                                                                                                                                                                                                                                                                       |
| `aria-controls`         | Identifies the element that serves as the popup                                                                                                                                                                                                                                                                       |
| `aria-expanded`         | Indicates if the popup element is displayed or not                                                                                                                                                                                                                                                                    |
| `aria-activedescendant` | Refers to an option in the listbox when it is visually indicated as having keyboard focus. When navigation keys like <kbd>↓</kbd>, are pressed, JavaScript changes the value. Enables assistive technologies to know which element the application regards as focused while DOM focus remains on the `input` element. |
| `aria-selected`         | Specified on an option in the listbox when it is visually highlighted as selected. Occurs only when an option in the list is referenced by `aria-activedescendant`.                                                                                                                                                   |
| `aria-haspopup`         | Indicates the availability and type of interactive popup element that can be triggered by the element on which the attribute is set.                                                                                                                                                                                  |

### Text

The following values should be provided as part of the `textMap` prop:

| Label            | Description                               | <div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div> |
| ---------------- | ----------------------------------------- | --------------------------------------------------------- |
| spinnerAriaLabel | Aria label for announcing loading spinner | "Loading"                                                 |

### Keyboard

When NVDA is enabled, the `Esc` key needs to be pressed twice in order to close the overlay. The 1st key press is to switch to NVDA's browse mode and the 2nd press is to collapse the overlay.

- https://github.com/nvaccess/nvda/issues/4428
- https://github.com/nvaccess/nvda/issues/15170
- https://github.com/nvaccess/nvda/issues/10608
