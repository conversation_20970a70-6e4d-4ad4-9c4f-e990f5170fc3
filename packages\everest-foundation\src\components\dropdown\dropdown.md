# Dropdown

## Summary

Research and document implementations for the Everest Dropdown.

- Start Date: 2022-02-15
- Figma link: https://www.figma.com/file/YhvqNFsWapyJnaoyAiDNMm/%F0%9F%A7%AADropdown?node-id=553%3A41694

## Detailed Design

Dropdown component is a controlled component, it doesn't store or update its states.

We will be utilizing the cdk overlay and list component for the list popup, this approach will be consistent throughout Everest.

We are using options prop for the list item.

Create Overlay component for Everest on cdk (reference to the existing cdk version) - need to consider z-index issue: should it be a child of the dropdown or using portal to push it to body

Create Basic List Component - Should support text with checkmark only, not consider other OptionTemplates

## API

1. **testId**: `undefined | string`  
   Sets **data-test-id** attribute on the html element.
1. **ariaLabel**: `undefined | string`  
   `aria-label` for _dropdown_, if set, this will be used, otherwise use **label**/**placeholder**
1. **ariaDescribedBy**: `undefined | string`  
   **ariaDescribedBy** for _dropdown_, sets to **helperText** or **errorMessage**
1. **id**: `undefined | string`  
   required or auto generated
1. **name**: `undefined | string`  
   Defines a **name** for the _dropdown_
1. **disabled**: `boolean`  
   Sets the `disabled` attribute on the _dropdown_.
1. **required**: `boolean`  
   Sets the `required` attribute on the _dropdown_. Adds the asterisk to the **label** / **placeholder**.
1. **label**: `undefined | string`  
   Optional user provided **label**. If none is given, no **label** is rendered. When **label** set, automatically set `htmlFor` to _dropdown_ **id**
1. **placeholder**: `undefined | string`  
   Optional user provided **placeholder**. The hint that should be displayed before a value is selected for the _dropdown_.
1. **helperTextPrefix**: `undefined | string`
   Optional user provided prefix of helper Text.
1. **helperText**: `undefined | string`  
   Optional user provided help text. When set, _dropdown_ **ariadescribedby** will set to **helperText** **id**
1. **status**: `TSelectContainerTriggerAreaStatus`  
   Indicates the state of the dropdown.
1. **statusMessagePrefix**: `undefined | string`
   Optional user provided prefix of status message.
1. **statusMessage**: `undefined | string`
   Optional user provided status message. When set, _dropdown_ **ariadescribedby** will set to **statusMessage** **id**, this will override **helperText**. This will be used only when **status** is set to "error" or "success".
1. **value**: `undefined | IDataItem`  
   Define value of input
1. **options**: `IDataItem[]`
   _dropdown_ options - `IDataItem[]` as

   ```typescript
   export interface IDataItem {
     title: string;
     value?: string;
     id: string;
     iconName?: TIconName;
     additionalData?: unknown;
     type?: TDataItemType;
     ariaDescribedBy?: string;
   }
   ```

1. **itemRenderer**: `undefined | (dataItem: IDataItem, selected: boolean) => React.ReactNode` - internal till the strictOption is ready  
   Custom item style depended on `strictOption[]`
1. **maxItems**: `undefined | number`  
   Max number of items displayed on overlay
1. **onItemFocus**: `undefined | (option: IDataItem, index: number) => void`  
   Callback when user focus on a item
1. **onItemBlur**: `undefined | (option: IDataItem, index: number) => void`  
   Callback when user blur on a item
1. **onChange**: `undefined | (option: IDataItem, index: number) => void`
   Callback when _dropdown_ selected value change
1. **onClear**: `undefined | () => void`  
   Callback when clear button is clicked
1. **onFocus**: `undefined | Function`  
   Optional callback on focus.
1. **onBlur**: `undefined | Function`  
   Optional callback on blur.
1. **altTextMap**: `ISelectContainerTriggerAreaAltText`  
   Localized alt text for decorative elements

```
   [
      { key: 'clear', altText: 'ClearIcon altText' },
   ]
```

## Accessibility

The keyboard behavior should be the same as the following link

- https://www.w3.org/WAI/ARIA/apg/example-index/combobox/combobox-select-only.html

For accessiblity we will be utilized the following Roles and Attributes

- Role
  - `combobox`
  - `listbox`
  - `option`
- Attribute
  - `aria-controls`
  - `aria-expanded`
  - `aria-activedescendant`
  - `aria-selected`

Following reference for accessbility implementation

- https://www.24a11y.com/2019/select-your-poison/
- https://w3c.github.io/aria-practices/examples/combobox/combobox-select-only.html
- https://w3c.github.io/aria-practices/examples/combobox/combobox-autocomplete-both.html

When NVDA is enabled, the `Esc` key needs to be pressed twice in order to close the overlay. The 1st key press is to switch to NVDA's browse mode and the 2nd press is to collapse the overlay.

- https://github.com/nvaccess/nvda/issues/4428
- https://github.com/nvaccess/nvda/issues/15170
- https://github.com/nvaccess/nvda/issues/10608

## Alternatives/Trade-Offs

using children prop instead of options prop

using <`select`> and <`option`>

- This is how Shopify Polaris approach for simple dropdown. It is not consistent with using <`ul`> <`li`> on their combobox.
- We should implement all the dropdown popup the same way (dropdown, combobox and multiSelect)

downshift - use Render Prop

- this is a good solution and very flexible and powerful, but the approach is required switching the development paradigm compare to the current HOC solution. For consistency, we should use our current approach and might be revisit in the future.

## Q&A

1. Default and List Expanded? What is the use case for that?

   - Answer:
     - List wouldn't expanded unless it has focus/active
     - expanded list should disappear when dropdown lost focus
     - The dropdown will never expanded the list when it is not active/focus - Figma has it due to some variants constraints (ignore Figma state for this)

1. For List Item, do we support only Text for dropdown option?
   - Answer: Yes
1. Active and No expanded list?

   - Answer: It only happened
     - When item selected (Mouse or keyboard(Enter/spacebar))
     - When use keyboard Esc

1. Label and Help text included for the component?

   - Answer:

     - Help text is included
     - Label - Label is supported
     - help text should be hidden under the expanded list, it wouldn't show when expanded.

1. Invalid state?? Error State

   - Answer:
     - Error state is included
     - Error expanded list should be showing if the dropdown is invalid.
     - Support error help text
     - Error Help text would overwrite regular help text.

1. the exclamation mark icon? Is it just an icon? what is the padding etc?
   - Answer: - Nick will update Figma for details info (no exclamation icon)
1. What happen to the dropdown button when it is invalid?
   - Answer: Nick will update Figma (Figma updated)
1. Is the dropdown searchable?

   - Answer:
     - dropdown is not textInput, but when using keyboard key, it will auto soft selected the first matching letter from the list
     - example: https://www.w3.org/TR/wai-aria-practices-1.2/examples/combobox/combobox-select-only.html
     - The selected checkmark should indicate the selected item.

1. Any soft selection indication?

   - Answer:
     - Soft Selection should be supported when using keyboard up and down key to indicate the selected item or using searching by typing.
     - example: https://www.w3.org/TR/wai-aria-practices-1.2/examples/combobox/combobox-select-only.html
     - soft selection style - update background color.

1. Accessibility

   - Answer: The keyboard behavior should be the same as the following link
     - https://www.w3.org/TR/wai-aria-practices-1.2/examples/combobox/combobox-select-only.html
     - once tab into the control - dropdown list should show
       There are couple different as the w3 link doesn't have the clear button.
     - different 1 - tabbing out when highlighting the List Item doesn't select the item
     - different 2 - it will tabbing out from the list box, will tab out of the dropdown to the next focusable item on the page
     - different 3 - when the focus is on the dropdown itself and it has selected value, tabbing will stop on the clear button first and required one more tab to get out of the dropdown.

1. Can selected item be unselect?
   - Answer: Yes, it should be able to unselect
     - Using X button to clear. X always there when there is value for dropdown.
1. Required field?
   - Answer: The \* on the label to indicate required field
1. What should it display when no value selected?
   - Answer: Show placeholder value - When placeholder hasn't set (show '--' as fallback)
1. Variant
   - Answer: only one variant
1. What is the recommended dropdown size of the dropdown list? Any size limit? scrollbar?

   - Answer:
     - limit for basic dropdown 3-7 items (Scroll bar should be implemented after that)
     - If more than 7 items (recommended to use comboBox(Searchable) instead of dropdown)
     - if less than 3 items (should be using selection control (radio button, toggle, checkbox, etc))
     - If dropdown has less than 3 items - the fallback (auto shrink to the item list size - 1, 2, or 3)
     - no scroll bar but grow the dropdown overlay till 7 items, then scroll bar should appear

1. should the dropdown size be customizable? like max item show? size?

   - Answer:
     - Yes, customizable, support up to 15 items.
     - switch to scrollable dropdown if the size is bigger than the available area.

1. What happen when I use arrow down to highlight on the item, what is the item style - focusRing? What would be trigger the selection? enter or space?

   - Answer:
     - Item List style on Item List Page (https://www.figma.com/file/YhvqNFsWapyJnaoyAiDNMm/%F0%9F%A7%AADropdown?node-id=661%3A29824) - same as hover
     - https://www.w3.org/TR/wai-aria-practices-1.2/examples/combobox/combobox-select-only.html
     - enter or spacebar to select list item
     - FocusRing (No FocusRing for softselected item) - https://www.carbondesignsystem.com/components/dropdown/code/
     - focus ring only on the full dropdown component
     - item list wouldn't have focus Ring and it only has focus style. - background etc. list item Figma.

1. Popup width?

   - Answer: should always be the same as the dropdown component.

1. Item size bigger than the popup width

   - Answer: using default behavior for now, Truncated and tooltip

1. for disabled dropdown, can the user still expand the list?

   - Answer: No

1. For dropdown focus ring - the focus ring will cover the whole dropdown + overlay pop up

   - Answer: Yes
     - on technical side, since it is 2 part component, we will be using three border approach, the top component will have top, left and right outline, and the bottom component will have bottom, left and right outline.

1. hover on the clear icon button

   - Answer: Nothing happen but only update the cursor to click cursor

1. focus on clear icon button
   - Answer: update the background color when it is focus, but the outside focus ring stayed.

## Future Considerations

Future improvement - The following items are out of scope for this PBI

1. dropdown popup on top of the dropdown control - out of scope for now
1. List item template and pop up container size - should be cover on the list item Epic. out of scope for now
1. popup size relative to windows size - out of scope for now
1. How are we dealing with text width bigger than the container size. (truncate with tooltip for now, future consideration on the following questions)

   - for Button, we wrap it.
   - for TextInput, we truncate it
   - How should dropdown behave?
   - The catalog dropdownFilterList - wrap to second line and then truncated.
   - Should we increase the pop up width to show more info on the dropdown? out of scope

List Virtualization (Idea)

1. **onScroll**: `undefined | Function`  
   Optional callback on scroll.
1. **firstVisibleIndex**: `number`  
   Index of the first visible option - `default` = 0
1. **totalItems**: `number | undefined`  
   Total results, when number is greater than maxItems scrollbar will appear

1. **dir**: `"ltr" | "rtl" `  
   Specifies the direction of the text in the _combobox_ and options.

## Other Design Systems

### Material - https://mui.com/components/selects/

- using children prop (node - MenuItem)
- props - https://mui.com/api/select/

### Shopify Polaris - https://polaris.shopify.com/components/forms/select#navigation

- using options prop (String | StrictOption | SelectGroup)[]
- using <`select`> and <`option`>
- not consistent with their comboBox, which is using <`ul`> and <`li`>

### Microsoft Fluent UI - https://developer.microsoft.com/en-us/fluentui#/controls/web/dropdown

- options prop (IDropdownOption)

### Carbon Design System - https://www.carbondesignsystem.com/components/dropdown/usage/

- items props

```
   const items = [
   {
      id: 'option-1',
      label: 'Option 1',
   },
   {
      id: 'option-2',
      label: 'Option 2',
   },
```

### Atlassian Design System - https://atlassian.design/components/select/examples

- options prop

```
   options={[
     { label: 'Adelaide', value: 'adelaide' },
     { label: 'Brisbane', value: 'brisbane' },
     { label: 'Canberra', value: 'canberra' },
     { label: 'Darwin', value: 'darwin' },
     { label: 'Hobart', value: 'hobart' },
     { label: 'Melbourne', value: 'melbourne' },
     { label: 'Perth', value: 'perth' },
     { label: 'Sydney', value: 'sydney' },
   ]}
```

### chakra - https://chakra-ui.com/docs/form/select

- children prop

```
   <Select placeholder='Select option'>
      <option value='option1'>Option 1</option>
      <option value='option2'>Option 2</option>
      <option value='option3'>Option 3</option>
   </Select>
```

## Required PBIs

1. [[List component] Architecture](https://ceridianpt.atlassian.net/browse/EVR-756) (Basic and only support text and checkmark-selection - including hover, active color)
   - this needs to support dropdownOptions (text only)
1. [[Overlay] Architecture](https://ceridianpt.atlassian.net/browse/EVR-757)
1. [Create Dropdown component](https://ceridianpt.atlassian.net/browse/EVR-715) (including error, helptext and error helptext), Utilized the Overlay and List Component - focus on the functionality
1. [Dropdown component style](https://ceridianpt.atlassian.net/browse/EVR-758) - focus on styled
1. [Dropdown Accessibility](https://ceridianpt.atlassian.net/browse/EVR-759) (Focus stay on the dropdown but the visual focus should be moved using keydown and keyup)
1. [Automation Testing](https://ceridianpt.atlassian.net/browse/EVR-852) - UnitTest and integration tests
1. [PoC - build a dropdown component using downshift - list virtualization consideration](https://ceridianpt.atlassian.net/browse/EVR-856)
1. [WIKI documentation - different between dropdown, combobox, listBox, and multiSelect](https://ceridianpt.atlassian.net/browse/EVR-857)

# Changelog

05/14/2024:

- Enable mouseClick to focus with FocusRing
- Clicking on the label would focus on the dropdown
- When list is loaded from loading, the overlay remain opened

- 12/10/2024: Add aria-disabled attribute [https://github.com/DayforceGlobal/platform-components/pull/1795]
