@use '../../variables.scss';

.evrDropdown {
  outline: none;
  color: var(--evr-content-primary-default);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  height: 100%;
  width: 100%;

  &.hasValue {
    height: auto;
    width: auto;
    padding-block-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    padding-block-end: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
    padding-inline-end: calc(
      var(--evr-spacing-md) + var(--evr-spacing-2xs) + var(--evr-spacing-xs) - var(--evr-border-width-thin-px)
    ); // icons width (24px) + icon padding (8px) + end padding (12px) - border

    &.compact {
      padding-block-start: calc(var(--evr-spacing-3xs) + var(--evr-border-width-thin-px));
      padding-block-end: calc(var(--evr-spacing-3xs) + var(--evr-border-width-thin-px));
    }
  }

  &.error {
    padding-block-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thick-px));
    padding-block-end: calc(var(--evr-spacing-xs) - var(--evr-border-width-thick-px));
    padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thick-px));

    &.compact {
      padding-block-start: var(--evr-spacing-3xs);
      padding-block-end: var(--evr-spacing-3xs);
    }
  }

  &.clearButton {
    padding-inline-end: calc(
      (2 * var(--evr-spacing-md)) + (3 * var(--evr-spacing-2xs)) + var(--evr-spacing-xs)
    ); // 2 icons width (24px) + 3 icon padding (8px) + end padding (12px) + divider - border
  }

  &.overlayContainer {
    margin-block-start: var(--evr-border-width-thin-px);

    &.error {
      margin-block-start: var(--evr-border-width-thick-px);
    }
  }

  &.disabled {
    cursor: not-allowed;
    pointer-events: none;
    color: var(--evr-inactive-content);
    background: var(--evr-inactive-surfaces);
    @media (forced-colors: active) {
      color: GrayText;
    }
  }
}

.evrOverlayContainer {
  // borderbox is implemented on both overlay and formFieldContainer, this is to offset the formfieldContainer borderbox
  margin-block-start: calc(var(--evr-border-width-thin-px) * -1);

  &.error {
    margin-block-start: calc(var(--evr-border-width-thick-px) * -1);
  }
}
