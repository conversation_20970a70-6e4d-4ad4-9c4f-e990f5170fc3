import { Met<PERSON>, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Dropdown } from './dropdown';
import { Button } from '../button';
import { screen, userEvent, waitFor } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

export const firstOption = { title: 'First Option', id: 'item1' };
export const secondOption = { title: 'Second Option', id: 'item2' };
export const thirdOption = { title: 'Third Option', id: 'item3' };
export const fourthOption = { title: 'Fourth Option', id: 'item4' };
export const fifthOption = { title: 'Fifth Option', id: 'item5' };
export const sixthOption = { title: 'Sixth Option', id: 'item6' };
export const groupedOptions = [
  {
    title: 'Group 1',
    id: 'group-1',
    items: [
      { title: 'Alpha Option', id: 'item7' },
      { title: 'Beta Option', id: 'item8' },
      { title: 'Gamma Option', id: 'item9' },
    ],
  },
  {
    title: 'Group 2',
    id: 'group-2',
    items: [
      { title: 'Delta Option', id: 'item10' },
      { title: 'Epsilon Option', id: 'item11' },
      { title: 'Zeta Option', id: 'item12' },
    ],
  },
];
export const longOptionContent = {
  title:
    'This is a very long content option, it is so long that the content width is bigger than List Item width, it should be truncated and using tooltip. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.',
};
export const dummyOption = { title: 'dummy' };
export const options = [firstOption, secondOption, thirdOption];
export const firstOptionWithIcon = {
  title: 'First Option',
  iconName: 'heartOutline',
  id: 'itemIcon1',
};
export const secondOptionWithIcon = {
  title: 'Second Option',
  iconName: 'home',
  id: 'itemIcon2',
};
export const thirdOptionWithIcon = {
  title: 'Third Option',
  iconName: 'help',
  id: 'itemIcon3',
};
export const fourthOptionWithIcon = {
  title: 'Fourth Option',
  iconName: 'phone',
  id: 'itemIcon4',
};
export const fifthOptionWithIcon = {
  title: 'Fifth Option',
  iconName: 'pin',
  id: 'itemIcon5',
};
export const sixthOptionWithIcon = {
  title: 'Sixth Option',
  iconName: 'search',
  id: 'itemIcon6',
};
export const optionsWithIcon = [firstOptionWithIcon, secondOptionWithIcon, thirdOptionWithIcon];
export const longLabel =
  'This is a very long label. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.';
export const helperText = 'This is some helper text';
export const helperTextPrefix = 'Hint:';
export const statusMessage = 'This is a status message';
export const statusMessagePrefix = 'Prefix:';
export const testId = 'dropdown-test-id';
export const externalButtonTestId = 'external-button-test-id';

<Meta
  title="Testing/Automation Test Cases/Dropdown"
  component={Dropdown}
  decorators={[ChromaticDecorators.padStory, ChromaticDecorators.setHeightTo100vh]}
  parameters={{
    controls: {
      exclude: ['dir', 'itemRenderer'],
      sort: 'requiredFirst',
    },
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    options: [...options, longOptionContent],
    label: 'This is a label',
    testId: testId,
    clearButtonAriaLabel: 'This is clear button',
    noResultsText: 'No matches found',
    loadingText: 'Loading',
  }}
/>

# Dropdown

## Live Demo

export const getDropdown = () => screen.getByTestId(testId);

<Canvas>
  <Story name="Default">
    {(args) => <Dropdown {...args} label={''} ariaLabel={'This is an ariaLabel'}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with label">{(args) => <Dropdown {...args}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with value">{(args) => <Dropdown {...args} value={secondOption}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with value and hidden clear button">
    {(args) => <Dropdown {...args} value={secondOption} hideClearButton></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with long label">{(args) => <Dropdown {...args} label={longLabel}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with long label and value">
    {(args) => <Dropdown {...args} value={secondOption} label={longLabel}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with long value">{(args) => <Dropdown {...args} value={longOptionContent}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with long value and no label">
    {(args) => <Dropdown {...args} label={''} value={longOptionContent}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with invalid value">{(args) => <Dropdown {...args} value={dummyOption}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Dropdown as Required field">{(args) => <Dropdown {...args} required={true}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Dropdown as Required field with value">
    {(args) => <Dropdown {...args} required={true} value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Dropdown">{(args) => <Dropdown {...args} disabled={true}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled Dropdown with value">
    {(args) => <Dropdown {...args} disabled={true} value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Dropdown with value and helper text">
    {(args) => <Dropdown {...args} disabled={true} value={secondOption} helperText={helperText}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Dropdown with value and required field">
    {(args) => <Dropdown {...args} disabled={true} required value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly Dropdown">{(args) => <Dropdown {...args} readOnly></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly Dropdown with value">
    {(args) => <Dropdown {...args} readOnly={true} value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly Dropdown with value and required field">
    {(args) => <Dropdown {...args} readOnly={true} required value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Pressing Enter on a ReadOnly Dropdown with value and required field"
    play={async ({}) => {
      await userEvent.click(getDropdown());
      await userEvent.keyboard('{Enter}');
    }}
  >
    {(args) => <Dropdown {...args} readOnly={true} required value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with helper text">{(args) => <Dropdown {...args} helperText={helperText}></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with helper text and helper text prefix">
    {(args) => <Dropdown {...args} helperText={helperText} helperTextPrefix={helperTextPrefix}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with error message">
    {(args) => (
      <Dropdown
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></Dropdown>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown with success message">
    {(args) => (
      <Dropdown
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></Dropdown>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Dropdown compact">{(args) => <Dropdown {...args} compact></Dropdown>}</Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown compact with Overlay"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} compact></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Overlay"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Overlay and scroll bar"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} options={[...options, fourthOption, fifthOption, sixthOption]}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Overlay and Value"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with error border and overlay"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => (
      <Dropdown
        {...args}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></Dropdown>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus Ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <Dropdown {...args}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus Ring and Value"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <Dropdown {...args} value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus on clear button"
    play={async () => {
      await userEvent.tab();
      await userEvent.tab();
    }}
  >
    {(args) => <Dropdown {...args} value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus Ring and Overlay"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} name={'FocusRingAndOverlay'}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus Ring, Overlay and scroll bar"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} options={[...options, fourthOption, fifthOption, sixthOption]}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus Ring and softSelectedIndex"
    play={async () => {
      await userEvent.tab();
      await userEvent.type(getDropdown(), 's');
    }}
  >
    {(args) => <Dropdown {...args}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus Ring and softSelectedIndex equal to selectedIndex"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} value={secondOption}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Focus Ring, Overlay, Value and scroll bar"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => (
      <Dropdown
        {...args}
        value={secondOption}
        options={[...options, fourthOption, fifthOption, sixthOption]}
      ></Dropdown>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Item with Icon and Overlay"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} options={optionsWithIcon}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with Item with Icon, Overlay and scroll bar"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => (
      <Dropdown
        {...args}
        options={[...optionsWithIcon, fourthOptionWithIcon, fifthOptionWithIcon, sixthOptionWithIcon]}
      ></Dropdown>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Handling State and Events Dropdown">
    {() => {
      const [value, setValue] = React.useState(null);
      const options = [
        { title: 'First Option 1', id: 'item1' },
        { title: 'Second Option 1', id: 'item2' },
        { title: 'Third Option 1', id: 'item3' },
        { title: 'First Option 2', id: 'item4' },
        { title: 'Second Option 2', id: 'item5' },
        { title: 'Third Option 2', id: 'item6' },
        { title: 'First Option 3', id: 'item7' },
        { title: 'Second Option 3', id: 'item8' },
      ];
      const handleChange = (value) => {
        value && setValue(value);
      };
      const handleClear = () => {
        setValue(null);
      };
      return (
        <Dropdown
          value={value}
          testId={testId}
          label="This is a label"
          options={options}
          onChange={handleChange}
          onClear={handleClear}
          ariaLabel={'This is an aria-label'}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Handling State and Events Dropdown With value">
    {() => {
      const options = [
        { title: 'First Option 1', id: 'item1' },
        { title: 'Second Option 1', id: 'item2' },
        { title: 'Third Option 1', id: 'item3' },
        { title: 'First Option 2', id: 'item4' },
        { title: 'Second Option 2', id: 'item5' },
        { title: 'Third Option 2', id: 'item6' },
        { title: 'First Option 3', id: 'item7' },
        { title: 'Second Option 3', id: 'item8' },
      ];
      const [value, setValue] = React.useState(options[1]);
      const handleChange = (value) => {
        value && setValue(value);
      };
      const handleClear = () => {
        setValue(null);
      };
      return (
        <Dropdown
          value={value}
          testId={testId}
          label="This is a label"
          options={options}
          onChange={handleChange}
          onClear={handleClear}
          ariaLabel={'This is an aria-label'}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown with active descendant"
    play={async () => {
      await userEvent.click(getDropdown());
      await userEvent.click(await screen.findByText(options[0].title));
    }}
  >
    {(args) => {
      const [value, setValue] = React.useState(null);
      const handleChange = (value) => {
        value && setValue(value);
      };
      const handleClear = () => {
        setValue(null);
      };
      return (
        <Dropdown
          {...args}
          value={value}
          options={options}
          onChange={handleChange}
          onClear={handleClear}
          ariaLabel={'This is an aria-label'}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown loading"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} options={options} loading={true} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Dropdown no results"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} options={[]} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="With grouped options"
    play={async () => {
      await userEvent.click(getDropdown());
    }}
  >
    {(args) => <Dropdown {...args} options={groupedOptions}></Dropdown>}
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => {
      const options = [firstOption, secondOption, thirdOption, fourthOption, fifthOption, ...groupedOptions];
      const [value, setValue] = React.useState();
      const handleChange = (value) => {
        value && setValue(value);
      };
      const handleClear = () => {
        setValue(null);
      };
      return (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            gap: '1rem',
          }}
        >
          <Dropdown
            {...args}
            id="enabled-id"
            testId="enabled-id"
            label="enabled"
            ariaLabel="enabled"
            value={value}
            options={options}
            onChange={handleChange}
            onClear={handleClear}
          />
          <Dropdown
            {...args}
            id="readonly-id"
            testId="readonly-id"
            label="readonly"
            ariaLabel="readonly"
            options={options}
            value={options[0]}
            readOnly
          />
          <Dropdown
            {...args}
            id="disabled-id"
            testId="disabled-id"
            label="disabled"
            ariaLabel="disabled"
            options={options}
            value={options[1]}
            disabled
          />
          <Button id="button" testId="button" label="button" />
        </div>
      );
    }}
  </Story>
</Canvas>
