import React from 'react';
import { act, render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Dropdown } from './dropdown';
import { mockResizeObserver } from '../../test-utils';
import { IDataItem } from '../list-item';

const testId = 'dropdown-test-id';
const clearButtonTestId = `${testId}-select-container-clear-button`;
const ariaLabel = 'This is an aria-label';
const options = [
  { title: 'First Option', id: 'item1' },
  { title: 'Second Option', id: 'item2' },
  { title: 'Third Option', id: 'item3' },
];
const id = 'dropdown-1';
const labelId = `${id}-dropdown-label`;
const label = 'This is a label';
const helperText = 'This is some helper text';
const helperTextPrefix = 'Hint:';
const statusMessage = 'This is a status message';
const statusMessagePrefix = 'Prefix:';
const displayValue = options[1].title;
const firstOption = options[1];
const invalidValue = { title: 'dummy', id: 'item0' };
const clearButtonAltText = 'Clear dropdown value';
const noResultsText = 'No results';
const loadingText = 'Loading';
const onChange = jest.fn();
const onClear = jest.fn();
const onFocus = jest.fn();
const onBlur = jest.fn();
const overrideSearchMethod = (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  currentIndex: number,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  options: IDataItem[],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  searchValue: string
) => {
  return 2;
};

const propsWithoutLabel = {
  id,
  testId,
  ariaLabel,
  options,
  clearButtonAriaLabel: clearButtonAltText,
  loading: false,
  loadingText,
  noResultsText,
};

const defaultProps = {
  ...propsWithoutLabel,
  label,
};

const variants = {
  defaultDropdown: {
    name: 'default dropdown',
    jsx: (
      <Dropdown
        id={id}
        testId={testId}
        ariaLabel={ariaLabel}
        options={options}
        noResultsText={noResultsText}
      ></Dropdown>
    ),
  },
  dropdownWithAriaLabel: {
    name: 'Dropdown with ariaLabel',
    jsx: <Dropdown {...propsWithoutLabel} ariaLabel={ariaLabel}></Dropdown>,
  },
  dropdownWithoutAriaLabel: {
    name: 'Dropdown without ariaLabel',
    jsx: <Dropdown {...defaultProps} ariaLabel={''}></Dropdown>,
  },
  dropdownWithLabel: {
    name: 'Dropdown with label',
    jsx: <Dropdown {...defaultProps}></Dropdown>,
  },
  dropdownWithoutLabel: {
    name: 'Dropdown without Label',
    jsx: <Dropdown {...propsWithoutLabel}></Dropdown>,
  },
  dropdownWithValue: {
    name: 'Dropdown with value',
    jsx: <Dropdown {...defaultProps} value={firstOption}></Dropdown>,
  },
  dropdownWithValueAndHiddenClearButton: {
    name: 'Dropdown with value and hidden clear button',
    jsx: <Dropdown {...defaultProps} value={firstOption} hideClearButton></Dropdown>,
  },
  dropdownWithInvalidValue: {
    name: 'Dropdown with invalid value',
    jsx: <Dropdown {...defaultProps} value={invalidValue}></Dropdown>,
  },
  requiredDropDown: {
    name: 'Dropdown as Required field',
    jsx: <Dropdown {...defaultProps} required={true}></Dropdown>,
  },
  disabledDropdown: {
    name: 'Disabled Dropdown',
    jsx: <Dropdown {...defaultProps} disabled={true}></Dropdown>,
  },
  readOnlyDropdown: {
    name: 'Read Only Dropdown',
    jsx: <Dropdown {...defaultProps} readOnly={true}></Dropdown>,
  },
  dropdownWithHelperText: {
    name: 'Dropdown with helper text',
    jsx: <Dropdown {...defaultProps} helperText={helperText}></Dropdown>,
  },
  dropdownWithHelperTextAndHelperTextPrefix: {
    name: 'Dropdown with helper text',
    jsx: <Dropdown {...defaultProps} helperText={helperText} helperTextPrefix={helperTextPrefix}></Dropdown>,
  },
  dropdownWithErrorMessage: {
    name: 'Dropdown with error message',
    jsx: (
      <Dropdown
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'error'}
      ></Dropdown>
    ),
  },
  dropdownWithSuccessMessage: {
    name: 'Dropdown with success message',
    jsx: (
      <Dropdown
        {...defaultProps}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status={'success'}
      ></Dropdown>
    ),
  },
  dropdownWithLongListItems: {
    name: 'Dropdown Long List Items',
    jsx: (
      <Dropdown
        {...defaultProps}
        options={[
          ...options,
          ...[
            { title: 'First 2', id: 'item4' },
            { title: 'first 3', id: 'item5' },
            { title: 'First 4', id: 'item6' },
          ],
        ]}
      ></Dropdown>
    ),
  },
  dropdownWithOnChange: {
    name: 'Dropdown with onChange',
    jsx: <Dropdown {...defaultProps} onChange={onChange}></Dropdown>,
  },
  dropdownWithOnChangeAndValue: {
    name: 'Dropdown with onChange and value',
    jsx: <Dropdown {...defaultProps} onChange={onChange} value={firstOption}></Dropdown>,
  },
  dropdownWithOnClearAndValue: {
    name: 'Dropdown with onClear and value',
    jsx: <Dropdown {...defaultProps} onClear={onClear} value={firstOption}></Dropdown>,
  },
  dropdownWithOnClearValueAndNoLabel: {
    name: 'Dropdown without Label',
    jsx: <Dropdown {...propsWithoutLabel} onClear={onClear} value={firstOption}></Dropdown>,
  },
  dropdownWithOnFocus: {
    name: 'Dropdown with onFocus',
    jsx: <Dropdown {...defaultProps} onFocus={onFocus} value={firstOption}></Dropdown>,
  },
  dropdownWithOnBlur: {
    name: 'Dropdown with onBlur',
    jsx: (
      <>
        <Dropdown {...defaultProps} onBlur={onBlur} value={firstOption}></Dropdown>
        <button>tabbable button</button>
      </>
    ),
  },
  dropdownWithParentOnMouseDown: {
    name: 'Dropdown with Parent onMouseDown',
    jsx: (
      <>
        <p id="dummy-element" tabIndex={0}></p>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            gap: '1rem',
            width: '33%',
          }}
          onMouseDown={() => {
            setTimeout(() => {
              document.getElementById('dummy-element')?.focus();
            }, 0);
          }}
        >
          <Dropdown
            testId={testId}
            label="This is a label"
            options={options}
            onChange={onChange}
            onClear={onClear}
            onBlur={onBlur}
            ariaLabel={'This is an aria-label'}
            clearButtonAriaLabel={'Clear Selection'}
            noResultsText="No matches found"
          />
        </div>
      </>
    ),
  },
  dropdownWithOverrideSearchMethod: {
    name: 'Dropdown with overrideSearchMethod',
    jsx: <Dropdown {...defaultProps} overrideSearchMethod={overrideSearchMethod}></Dropdown>,
  },
};

describe('[Dropdown]', () => {
  /**
   * Fix TypeError: window.ResizeObserver is not a constructor issue
   * mock out the basic API (observe, unobserve, disconnect) and
   * use jest.fn() to return particular mock entries in the test.
   * https://github.com/maslianok/react-resize-detector/issues/145
   * https://github.com/que-etc/resize-observer-polyfill/issues/50
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { ResizeObserver } = window;

  beforeEach(() => {
    mockResizeObserver();
  });

  afterEach(() => {
    window.ResizeObserver = ResizeObserver;
    jest.restoreAllMocks();
  });

  const user = userEvent.setup();
  const getDropdown = () => screen.getByRole(`combobox`);
  const getLabel = () => screen.getByText(label);
  const getListItems = () => screen.getAllByRole('option');
  const queryListItems = () => screen.queryAllByRole('option');
  const getClearButton = () => screen.getByTestId(`${clearButtonTestId}`);
  const queryClearButton = () => screen.queryByTestId(`${clearButtonTestId}`);
  const getListItem2CheckIcon = () => {
    return screen.getByTestId('dropdown-test-id-list-box-list-item-item2-checked-icon');
  };

  it('should render aria-label when label not present', () => {
    render(variants.dropdownWithAriaLabel.jsx);
    expect(getDropdown()).toHaveAttribute('aria-label', ariaLabel);
  });

  it('should render aria-labelledby when label is present', () => {
    render(variants.dropdownWithoutAriaLabel.jsx);
    expect(getDropdown()).toHaveAttribute('aria-labelledby', labelId);
  });

  it('should render clear button alt text', () => {
    render(variants.dropdownWithValue.jsx);
    expect(getClearButton()).toHaveAttribute('aria-label', clearButtonAltText);
  });

  it('should show label', () => {
    render(variants.dropdownWithLabel.jsx);
    expect(getDropdown()).toBeInTheDocument();
    expect(getLabel()).toBeInTheDocument();
  });

  it('should render without label', () => {
    render(variants.dropdownWithoutLabel.jsx);
    expect(getDropdown()).toBeInTheDocument();
  });

  it('should render aria-disabled attribute', () => {
    render(variants.disabledDropdown.jsx);
    expect(getDropdown()).toHaveAttribute('tabindex', '-1');
    expect(getDropdown()).toHaveAttribute('aria-disabled', 'true');
  });

  it('should render aria-readonly attribute when readOnly', () => {
    render(variants.readOnlyDropdown.jsx);
    expect(getDropdown()).toHaveAttribute('aria-readonly');
  });

  it('should show initial value and clear button', () => {
    render(variants.dropdownWithValue.jsx);
    expect(getDropdown()).toHaveTextContent(displayValue);
    expect(getLabel()).toBeInTheDocument();
    expect(getClearButton()).toBeInTheDocument();
  });

  it('should show initial value and hide clear button', () => {
    render(variants.dropdownWithValueAndHiddenClearButton.jsx);
    expect(getDropdown()).toHaveTextContent(displayValue);
    expect(getLabel()).toBeInTheDocument();
    expect(queryClearButton()).not.toBeInTheDocument();
  });

  it('should show initial value List Item is selected', async () => {
    render(variants.dropdownWithValue.jsx);
    await act(async () => await user.click(getDropdown()));
    expect(getListItem2CheckIcon()).toBeInTheDocument();
  });

  it('should not show value when value is invalid', () => {
    render(variants.dropdownWithInvalidValue.jsx);
    expect(screen.queryByText(invalidValue.title)).not.toBeInTheDocument();
    expect(getLabel()).toBeInTheDocument();
    expect(queryClearButton()).not.toBeInTheDocument();
  });

  it('should show required on label', () => {
    render(variants.requiredDropDown.jsx);
    expect(getLabel()).toBeInTheDocument();
    expect(screen.getAllByText(/\*/i)[0]).toBeInTheDocument();
  });

  it('should not open overlay when disabled', async () => {
    render(variants.disabledDropdown.jsx);
    await act(async () => await user.click(getDropdown()));
    expect(queryListItems()[0]).toBeUndefined();
    expect(queryListItems().length).toBe(0);
  });

  it('should not open overlay when readOnly', async () => {
    render(variants.readOnlyDropdown.jsx);
    await user.click(getDropdown());
    expect(queryListItems()[0]).toBeUndefined();
    expect(queryListItems().length).toBe(0);
  });

  it('should show helper text', () => {
    render(variants.dropdownWithHelperText.jsx);
    expect(screen.getByText(helperText)).toBeInTheDocument();
    expect(screen.queryByText(helperTextPrefix)).not.toBeInTheDocument();
    expect(screen.queryByText(statusMessage)).not.toBeInTheDocument();
    expect(screen.queryByText(statusMessagePrefix)).not.toBeInTheDocument();
  });

  it('should show helper text and helper text prefix', () => {
    render(variants.dropdownWithHelperTextAndHelperTextPrefix.jsx);
    expect(screen.getByText(helperText)).toBeInTheDocument();
    expect(screen.getByText(helperTextPrefix)).toBeInTheDocument();
    expect(screen.queryByText(statusMessage)).not.toBeInTheDocument();
    expect(screen.queryByText(statusMessagePrefix)).not.toBeInTheDocument();
  });

  it('should show error message', () => {
    render(variants.dropdownWithErrorMessage.jsx);
    expect(screen.getByText(statusMessage)).toBeInTheDocument();
    expect(screen.getByText(statusMessagePrefix)).toBeInTheDocument();
    expect(screen.queryByText(helperText)).not.toBeInTheDocument();
    expect(screen.queryByText(helperTextPrefix)).not.toBeInTheDocument();
  });

  it('should show success message', () => {
    render(variants.dropdownWithSuccessMessage.jsx);
    expect(screen.getByText(statusMessage)).toBeInTheDocument();
    expect(screen.getByText(statusMessagePrefix)).toBeInTheDocument();
    expect(screen.queryByText(helperText)).not.toBeInTheDocument();
    expect(screen.queryByText(helperTextPrefix)).not.toBeInTheDocument();
  });

  it('Escape keydown should stopPropagation when overlay is visible', async () => {
    const onKeyDownParent = jest.fn();
    render(<div onKeyDown={onKeyDownParent}>{variants.defaultDropdown.jsx}</div>);

    await act(async () => await user.click(getDropdown()));
    expect(screen.getByText(options[0].title)).toBeInTheDocument();

    await act(async () => await user.keyboard('{Escape}'));
    expect(screen.queryByText(options[0].title)).not.toBeInTheDocument();
    expect(onKeyDownParent).toHaveBeenCalledTimes(0);

    await act(async () => await user.keyboard('{Escape}'));
    expect(onKeyDownParent).toHaveBeenCalledTimes(1);
  });

  it('should show loading stub when loading is true', async () => {
    render(<Dropdown {...defaultProps} loading={true} />);

    await act(async () => await user.click(getDropdown()));
    await waitFor(() => {
      const el = screen.getByTestId(`${testId}-list-box-stub`);
      expect(within(el).getByText(loadingText)).toBeInTheDocument();
    });
  });

  it('should not show loading stub when loading is false', async () => {
    render(<Dropdown {...defaultProps} loading={false} />);

    await act(async () => await user.click(getDropdown()));
    expect(screen.queryByText(loadingText)).not.toBeInTheDocument();
  });

  it('should show no results when options are empty', async () => {
    render(<Dropdown {...defaultProps} options={[]} />);

    await act(async () => await user.click(getDropdown()));
    await waitFor(() => {
      const el = screen.getByTestId(`${testId}-list-box-stub`);
      expect(within(el).getByText(noResultsText)).toBeInTheDocument();
    });
  });

  it('should not show no results when options are not empty', async () => {
    render(<Dropdown {...defaultProps} />);

    await act(async () => await user.click(getDropdown()));
    const el = screen.queryByTestId(`${testId}-list-box-stub`);
    expect(el).not.toBeInTheDocument();
  });

  it('Can open overlay using keyboard when navigating with shift + tab from clear button', async () => {
    render(variants.dropdownWithOnClearAndValue.jsx);

    await user.keyboard('{tab}{tab}');
    await user.keyboard('{shift>}{tab}');
    await user.keyboard('{enter}');
    await waitFor(() => {
      expect(screen.getByTestId(`${testId}-list-box`)).toBeInTheDocument();
    });
  });

  // Should search based on key press on dropdown
  describe('Search functionality', () => {
    // checking if the List Item has focused
    it('should focused on "Second Option" when use keyboard "s" key on dropdown', async () => {
      render(variants.dropdownWithLongListItems.jsx);

      await act(async () => {
        await user.tab();
        await user.type(getDropdown(), 's');
      });

      await waitFor(() => {
        expect(document.activeElement).toEqual(getListItems()[1]);
      });
    });

    it('should focused on "First Option" when use keyboard "f" key on dropdown', async () => {
      render(variants.dropdownWithLongListItems.jsx);

      await act(async () => {
        await user.tab();
        await user.type(getDropdown(), 'f', { skipClick: true });
      });

      expect(document.activeElement).toEqual(getListItems()[0]);
    });

    it('should focused on "First Option" when use keyboard capital "F" key on dropdown', async () => {
      render(variants.dropdownWithLongListItems.jsx);

      await act(async () => {
        await user.tab();
        await user.type(getDropdown(), 'F', { skipClick: true });
      });

      await waitFor(() => {
        expect(document.activeElement).toEqual(getListItems()[0]);
      });
    });
  });

  describe('onChange event', () => {
    beforeEach(onChange.mockReset);

    it('dispatch onChange event when use enter key to make select', async () => {
      render(variants.dropdownWithOnChange.jsx);

      await act(async () => {
        await user.click(getDropdown());
        await user.keyboard('{arrowdown}');
        await user.keyboard('{enter}');
      });
      await waitFor(() => {
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenLastCalledWith(options[1]);
      });
    });

    it('dispatch onChange event when use space key to make select', async () => {
      render(variants.dropdownWithOnChange.jsx);

      await act(async () => {
        await user.click(getDropdown());
        await user.keyboard('{arrowdown}{arrowdown}');
        await user.keyboard(' ');
      });

      await waitFor(() => {
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenLastCalledWith(options[2]);
      });
    });

    it('dispatch onChange event when use mouse click to make select', async () => {
      render(variants.dropdownWithOnChange.jsx);

      await act(async () => {
        await user.click(getDropdown());
        await user.click(getListItems()[1]);
      });

      expect(onChange).toHaveBeenCalledTimes(1);
      expect(onChange).toHaveBeenLastCalledWith(options[1]);
    });

    it('dispatch onChange event when click on clear button', async () => {
      render(variants.dropdownWithOnChangeAndValue.jsx);

      await act(async () => {
        await user.click(getDropdown());
        await user.click(getClearButton());
      });

      expect(onChange).toHaveBeenCalledTimes(1);
      expect(onChange).toHaveBeenLastCalledWith();
    });
  });

  describe('onClear event', () => {
    beforeEach(onClear.mockReset);

    it('dispatch onClear event when use enter key to clear selection', async () => {
      render(variants.dropdownWithOnClearAndValue.jsx);

      await act(async () => await user.type(getClearButton(), '{enter}'));
      expect(onClear).toHaveBeenCalledTimes(1);
      expect(onClear).toHaveBeenLastCalledWith();
    });

    it('dispatch onClear event when use space key to clear selection', async () => {
      render(variants.dropdownWithOnClearAndValue.jsx);

      await act(async () => await user.type(getClearButton(), ' '));
      expect(onClear).toHaveBeenCalledTimes(1);
      expect(onClear).toHaveBeenLastCalledWith();
    });

    it('dispatch onClear event when click on clear button', async () => {
      render(variants.dropdownWithOnClearAndValue.jsx);

      await act(async () => await user.click(getClearButton()));
      expect(onClear).toHaveBeenCalledTimes(1);
      expect(onClear).toHaveBeenLastCalledWith();
    });

    it('should clear the value when no label', async () => {
      render(variants.dropdownWithOnClearValueAndNoLabel.jsx);

      await act(async () => await user.click(getClearButton()));
      expect(onClear).toHaveBeenCalledTimes(1);
      expect(onClear).toHaveBeenLastCalledWith();
      expect(screen.queryByText('First Option')).not.toBeInTheDocument();
    });
  });

  describe('onFocus event', () => {
    beforeEach(onFocus.mockReset);

    it('dispatch onFocus when dropdown has focus', async () => {
      render(variants.dropdownWithOnFocus.jsx);

      await act(async () => await user.click(getDropdown()));
      expect(onFocus).toHaveBeenCalledTimes(1);
    });

    it.skip('dispatch only 1 onFocus when dropdown has focus and have series of keyboard events', async () => {
      render(variants.dropdownWithOnFocus.jsx);

      await act(async () => {
        await user.click(getDropdown());
        await user.keyboard('{arrowdown}{arrowdown}{arrowdown}{arrowup}');

        // Tabbing from the overlay should return focus to the clear button, but focus is instead landing on the document <body>, resulting in extra onFocus call
        await user.tab();
        await user.tab({ shift: true });
      });

      expect(onFocus).toHaveBeenCalledTimes(1);
    });

    it.skip('dispatch only 1 onFocus when dropdown has focus and tab to clear button and shift tab back to dropdown', async () => {
      render(variants.dropdownWithOnFocus.jsx);

      await act(async () => {
        await user.click(getDropdown());

        // Tabbing from the overlay should return focus to the clear button, but focus is instead landing on the document <body>, resulting in extra onFocus call
        await user.tab();
        await user.tab({ shift: true });
      });

      expect(onFocus).toHaveBeenCalledTimes(1);
    });
  });

  describe('onBlur event', () => {
    beforeEach(onBlur.mockReset);

    it.skip('should not dispatch onBlur when dropdown has focus with series of tab action within the dropdown', async () => {
      render(variants.dropdownWithOnBlur.jsx);
      await act(async () => {
        await user.tab(); // focus on container
        await user.tab(); // focus on clear button
        await user.tab({ shift: true }); // focus on container
        await user.tab(); // focus on clear button
        await user.keyboard('{arrowdown}{arrowdown}{arrowdown}{arrowup}'); // focus on list item

        // Tabbing from the overlay should return focus to the clear button, but focus is instead landing on the document <body>, resulting in onBlur being called
        await user.tab();
        await user.tab({ shift: true }); // focus on container
        await user.keyboard('{arrowdown}{enter}'); // focus on list item
      });

      expect(onBlur).toHaveBeenCalledTimes(0);
    });

    it('should dispatch onBlur when dropdown has lost focus', async () => {
      render(variants.dropdownWithOnBlur.jsx);

      await act(async () => {
        await user.tab();
        await user.tab();
        await user.tab();
      });
      expect(onBlur).toHaveBeenCalledTimes(1);
    });

    it('should dispatch onBlur when dropdown has lost focus using mouseClick and tab', async () => {
      render(variants.dropdownWithOnBlur.jsx);

      await act(async () => {
        await user.click(getDropdown());

        // this tab should focus clear button, but it focuses the document body instead...
        await user.tab();
        await user.tab();
      });

      expect(onBlur).toHaveBeenCalledTimes(1);
    });
  });

  it('should return override search method value', async () => {
    render(variants.dropdownWithOverrideSearchMethod.jsx);

    await act(async () => {
      await user.tab();
      await user.type(getDropdown(), 'h');
    });

    await waitFor(() => {
      expect(document.activeElement).not.toEqual(getListItems()[0]);
      expect(document.activeElement).not.toEqual(getListItems()[1]);
      expect(document.activeElement).toEqual(getListItems()[2]);
    });
  });

  it('should open the listbox and not call onBlur after clicking the dropdown text input', async () => {
    onBlur.mockReset();
    render(variants.dropdownWithParentOnMouseDown.jsx);

    await act(async () => await user.click(screen.getByTestId(testId)));
    expect(screen.getByTestId(`${testId}-list-box`)).toBeInTheDocument();
    expect(onBlur).toHaveBeenCalledTimes(0);
  });

  it('should open the listbox and not call onBlur after clicking the chevron icon', async () => {
    onBlur.mockReset();
    render(variants.dropdownWithParentOnMouseDown.jsx);

    await act(async () => await user.click(screen.getByTestId(`${testId}-select-container-icon-wrapper-icon`)));
    await new Promise((r) => setTimeout(r, 100));
    expect(screen.getByTestId(`${testId}-list-box`)).toBeInTheDocument();
    expect(onBlur).toHaveBeenCalledTimes(0);
  });
});
