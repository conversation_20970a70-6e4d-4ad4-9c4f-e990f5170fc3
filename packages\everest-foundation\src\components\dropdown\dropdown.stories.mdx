import { <PERSON>a, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Dropdown } from './dropdown';
import { action } from '@storybook/addon-actions';
import Examples from './dropdown.examples.mdx';

<Meta
  title="Components/Dropdown"
  component={Dropdown}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      exclude: ['dir', 'compact'],
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    value: {
      control: '-',
    },
    options: {
      type: 'array',
      control: 'object',
    },
    itemRenderer: {
      control: '-',
    },
    status: {
      type: 'enum',
      control: 'radio',
      options: ['default', 'error', 'success'],
    },
    onBlur: {
      control: '-',
    },
    onFocus: {
      control: '-',
    },
    onChange: {
      control: '-',
    },
    onClear: {
      control: '-',
    },
    overrideSearchMethod: {
      control: '-',
    },
    textMap: {
      type: 'object',
      control: 'object',
    },
  }}
  args={{
    id: 'dropdown-1',
    testId: 'dropdown-test-id',
    label: 'This is a label',
    ariaLabel: 'This is an aria-label',
    clearButtonAriaLabel: 'Clear dropdown value',
    required: true,
    disabled: false,
    readOnly: false,
    maxItems: 5,
    options: [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
      { title: 'Third Option', id: 'item3' },
      { title: 'Fourth Option', id: 'item4' },
      { title: 'Fifth Option', id: 'item5' },
      { title: 'Sixth Option', id: 'item6' },
    ],
    status: 'default',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',
    helperText: 'This is some helper text',
    helperTextPrefix: 'Hint:',
    onClear: action('onClear'),
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    onChange: action('onChange'),
    hideClearButton: false,
    loading: false,
    loadingText: 'Loading',
    noResultsText: 'No matches found',
  }}
/>

# Dropdown

<Examples />

## Live Demo

<Canvas>
  <Story name="Dropdown">
    {(args) => {
      const [value, setValue] = React.useState();
      const handleChange = (value) => {
        value && setValue(value);
      };
      const handleClear = () => {
        setValue(null);
      };
      return <Dropdown {...args} value={value} onChange={handleChange} onClear={handleClear} />;
    }}
  </Story>
</Canvas>

<ArgsTable story="Dropdown" />
