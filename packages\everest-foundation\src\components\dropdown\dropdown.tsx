import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { getRuntimeEnvironmentInfo } from '@platform/core';
import classnames from 'classnames';

import {
  dropdownActionImpls,
  dropdownActions,
  dropdownStates,
  getDropdownStateEffects,
  interceptTabKeyDown,
  searchListItem,
} from './dropdown-helpers';
import { generateId, isPrintableCharacters, mergeRefs, useCreateTestId } from '../../utils';
import { getAriaAttributes } from '../../utils/get-aria-attributes';
import { IStateMachineDefinition, TStateEffect, useStateMachine } from '../../utils/use-state-machine';
import { FormFieldContainer, FormFieldContainerContext, TFormFieldStatus } from '../form-field-container';
import { IListBoxTextMap, IListBoxMethods, ListBox } from '../list-box';
import { IDataItem } from '../list-item';
import { IListItemContext, ListItemContext } from '../list-item/list-item-context';
import { getSelectableOptions } from '../list-item/list-item-helper';
import { SelectContainer } from '../select-container';
import { TriggerAreaStyledOverlay } from '../trigger-area-styled-overlay';

import styles from './dropdown.module.scss';

export interface IDropdownProps {
  /**
   * Sets the `data-testid` attribute on the dropdown.
   */
  testId?: string;
  /**
   * A clear description of the dropdown.
   */
  ariaLabel?: string;
  /**
   * Sets the `id` attribute on the dropdown.
   */
  id?: string;
  /**
   * Sets the disabled attribute on the dropdown.
   * @default false
   */
  disabled?: boolean;
  /**
   * Sets the `readOnly` attribute on the dropdown.
   * @default false
   */
  readOnly?: boolean;
  /**
   * Sets the `required` attribute on the dropdown.
   * @default false
   */
  required?: boolean;
  /**
   * Specifies the label rendered in the dropdown.
   */
  label?: string;
  /**
   * Sets the status of the dropdown.
   * @default default
   */
  status?: TFormFieldStatus;
  /**
   * Sets the helper text rendered under the dropdown.
   */
  helperText?: string;
  /**
   * Sets the prefix to the helper text rendered under the dropdown.
   */
  helperTextPrefix?: string;
  /**
   * Sets the status message rendered under the dropdown.
   */
  statusMessage?: string;
  /**
   * Sets the prefix to the status message rendered under the dropdown.
   */
  statusMessagePrefix?: string;
  /**
   * Defines the value of the dropdown.
   */
  value?: IDataItem;
  /**
   * String array containing all list items.
   */
  options: IDataItem[];
  /**
   * Custom renderer for the list items.
   */
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  /**
   * Custom search method for the list items.
   */
  overrideSearchMethod?: (currentIndex: number, options: IDataItem[], searchValue: string) => number;
  /**
   * Max number of items displayed in overlay.
   * @default 5
   */
  maxItems?: number;
  /**
   * Aria-label for the clear button.
   */
  clearButtonAriaLabel?: string;
  /**
   * Callback when dropdown selected value changed.
   */
  onChange?: (dataItem?: IDataItem) => void;
  /**
   * Callback when clear button is clicked.
   */
  onClear?: () => void;
  /**
   * Callback when user focus on dropdown.
   */
  onFocus?: (e: React.FocusEvent) => void;
  /**
   * Callback when user leave dropdown.
   */
  onBlur?: (e: React.FocusEvent) => void;
  /**
   * Hides the clear button.
   * @default false
   */
  hideClearButton?: boolean;
  /**
   * React ref to the input element.
   */
  ref?: React.ForwardedRef<HTMLInputElement>;
  /**
   * Toggles the loading presentation.
   * @default false
   */
  loading?: boolean;
  /**
   * Text to be presented when the component is loading.
   */
  loadingText?: string;
  /**
   * Text to be presented when there are no results.
   */
  noResultsText: string;
  /**
   * Toggles the compact presentation.
   * @default false
   */
  compact?: boolean;
  /**
   * Object containing localized text for various elements.
   */
  textMap?: IListBoxTextMap;
}

export const Dropdown = React.forwardRef<HTMLInputElement, IDropdownProps>((props: IDropdownProps, ref) => {
  const {
    testId,
    ariaLabel,
    id: initialId,
    disabled = false,
    readOnly = false,
    required = false,
    label,
    status = 'default',
    helperText,
    helperTextPrefix,
    statusMessage,
    statusMessagePrefix,
    value: initialValue,
    options,
    itemRenderer,
    overrideSearchMethod,
    maxItems = 5,
    clearButtonAriaLabel,
    onChange,
    onClear,
    onFocus,
    onBlur,
    hideClearButton = false,
    loading = false,
    loadingText,
    noResultsText,
    compact = false,
    textMap,
  } = props;

  const { os } = getRuntimeEnvironmentInfo();
  const dataRef = useCreateTestId(testId);
  const overlayDataRef = useCreateTestId(testId ? `${testId}-overlay` : undefined);
  const totalOptionsCount = useRef<number>(0);
  const clearButtonRef = useRef(null);
  const overlayClearButtonRef = useRef(null);

  const id = useMemo(() => initialId || generateId(), [initialId]);
  const prevLoadingRef = useRef<boolean>(loading);

  const dropdownLabelId = `${id}-dropdown-label`;
  const dropdownListBoxId = `${id}-dropdown-list-box`;
  const statusMessageId = `${id}-dropdown-status-message`;
  const listBoxForwardRef = useRef(null);
  const listBoxRef = useRef(null);

  const [overlayContentHeight, setOverlayContentHeight] = useState(0);
  const [overlayHeight, setOverlayHeight] = useState<string>('');
  const [listBoxHeight, setListBoxHeight] = useState<number>(0);
  const [overlayVisible, setOverlayVisible] = useState<boolean>(false);
  const [softSelectedId, setSoftSelectedId] = useState<string>('');
  // focusListItemId - intermediate state, used to setSoftSelectedId on effect
  const [focusedListItemId, setFocusedListItemId] = useState<string>('');
  const [ariaActiveDescendantId, setAriaActiveDescendantId] = useState<string>('');
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isListItemFocusVisible, setIsListItemFocusVisible] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [isClearButtonFocused, setIsClearButtonFocused] = useState<boolean>(false);
  const [isFocusing, setIsFocusing] = useState<boolean>(false);
  const [inTransition, setInTransition] = useState<boolean>(false);
  const [selectableOptions, setSelectableOptions] = useState<IDataItem[]>([]);

  const value = useMemo(
    () => selectableOptions.find((option) => option.id === initialValue?.id),
    [selectableOptions, initialValue]
  );

  const emptyOption = useMemo(
    () =>
      loading || !options.length
        ? {
            title: loading ? loadingText : noResultsText,
            id: `${id}-dropdown-stub`,
            type: loading ? 'loading' : 'noResults',
          }
        : undefined,
    [loading, options.length, loadingText, noResultsText, id]
  );

  const ariaAttributes = getAriaAttributes(Dropdown.displayName || 'Dropdown', {
    ...props,
    overlayVisible,
    ariaActiveDescendantId,
    ariaLabel,
    dropdownLabelId,
    dropdownListBoxId,
    statusMessageId,
    label,
    getStatus: () => props.status === 'error',
  });

  const dropdownStateEffects: TStateEffect[] = getDropdownStateEffects(
    dataRef,
    clearButtonRef,
    focusedListItemId,
    setOverlayVisible,
    setSoftSelectedId,
    setIsFocusing,
    setInTransition
  );

  const stateMachineDefinition: IStateMachineDefinition = {
    initial: { value: dropdownStates.DEFAULT },
    states: dropdownStateEffects,
    actions: dropdownActionImpls,
  };

  const [state, dispatch] = useStateMachine(stateMachineDefinition);

  useEffect(() => {
    // get flattened 1-dimentional array of all items and nested groups
    const resp = getSelectableOptions(options);

    setSelectableOptions(resp.result);
    totalOptionsCount.current = resp.totalOptionsCount;
  }, [options]);

  useEffect(() => {
    if (isFocusing) {
      if (loading) {
        // delay focus to prevent focus on background dropdown when loading
        setTimeout(() => {
          dispatch({ type: dropdownActions.FOCUS });
        });
      } else dispatch({ type: dropdownActions.FOCUS });
      setInTransition(true);
      setIsFocusing(false);
    }
  }, [dispatch, isFocusing, loading]);

  const getDisplayValue = useCallback(() => {
    const displayValue = value && value.title;
    return displayValue || '';
  }, [value]);

  useLayoutEffect(() => {
    if (overlayVisible && listBoxRef.current) {
      const listItem = (listBoxRef.current as HTMLElement).querySelector('[role="option"],[role="presentation"]');
      if (!listItem) return;

      const borderContainerHeight = overlayTriggerRef.current ? overlayTriggerRef.current.clientHeight : 0;
      const listItemHeight = Math.ceil(listItem.getBoundingClientRect().height); //ceil is used here to get the correct height when zoomed in
      const newListBoxHeight = emptyOption
        ? listItemHeight
        : Math.min(maxItems, totalOptionsCount.current) * listItemHeight;

      setListBoxHeight(newListBoxHeight);
      setOverlayContentHeight(newListBoxHeight + borderContainerHeight);
    }
  }, [maxItems, overlayVisible, emptyOption]);

  const handleClearSelection = () => {
    if (disabled || readOnly) return;
    dispatch({ type: dropdownActions.CLEAR_SELECTION });
    onChange?.();
    onClear?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled || readOnly) return;

    setIsListItemFocusVisible(true);
    if (e.key === 'Escape' || (e.key === 'ArrowUp' && e.altKey)) {
      dispatch({ type: dropdownActions.KEYBOARD_CLOSE_OVERLAY });
    }

    switch (e.key) {
      case ' ':
      case 'Enter':
      case 'ArrowDown':
        setFocusedListItemId(emptyOption ? emptyOption.id : value?.id ?? selectableOptions[0].id);
        dispatch({ type: dropdownActions.KEYBOARD_FOCUS_LIST_ITEM });
        break;
      case 'ArrowUp':
      case 'Home':
        setFocusedListItemId(emptyOption ? emptyOption.id : selectableOptions[0].id);
        dispatch({ type: dropdownActions.KEYBOARD_FOCUS_LIST_ITEM });
        break;
      case 'End':
        setFocusedListItemId(emptyOption ? emptyOption.id : selectableOptions[selectableOptions.length - 1].id);
        dispatch({ type: dropdownActions.KEYBOARD_FOCUS_LIST_ITEM });
        break;
      default:
        break;
    }

    if (isPrintableCharacters(e) && e.key !== ' ' && !emptyOption) {
      setFocusedListItemId(searchListItem(e.key, selectableOptions, softSelectedId, overrideSearchMethod));
      dispatch({ type: dropdownActions.KEYBOARD_FOCUS_LIST_ITEM });
    }

    if (overlayVisible && e.key !== 'Tab') {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  const handleClearButtonKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab') {
      dispatch({ type: dropdownActions.KEYBOARD_FOCUS_CLEAR });
    }
  };

  const handleClearButtonKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && e.shiftKey) {
      dispatch({ type: dropdownActions.FOCUS });
    }
  };

  // When isFocused is false and clear button is focused, that means focus is coming from outside of dropdown.
  // So we direct the focus to drodown container to meet accessibility requirement.
  useLayoutEffect(() => {
    if (isClearButtonFocused) (dataRef?.current as HTMLElement).focus();
    setIsClearButtonFocused(false);
  }, [isFocused, dataRef, clearButtonRef, isClearButtonFocused]);

  const handleFocus = (e: React.FocusEvent) => {
    if (!isFocused) {
      setIsClearButtonFocused(clearButtonRef.current === e.target);
      dispatch({ type: dropdownActions.FOCUS });
      setIsFocused(true);
      onFocus?.(e);
    }
  };

  const handleBlur = (e: React.FocusEvent) => {
    if (dataRef.current && (dataRef.current as HTMLElement).contains(e.target as HTMLElement) && overlayVisible) return;

    if (listBoxRef.current && (listBoxRef.current as HTMLElement).contains(e.relatedTarget as HTMLElement)) return;

    if (dataRef.current && (dataRef.current as HTMLElement).contains(e.relatedTarget as HTMLElement)) return;

    if (clearButtonRef.current && (clearButtonRef.current as HTMLElement).contains(e.relatedTarget as HTMLElement))
      return;

    if (inTransition) {
      // if in transition, don't blur
      setInTransition(false);
      return;
    }
    dispatch({ type: dropdownActions.BLUR });
    onBlur?.(e);
    setIsFocused(false);
    setIsListItemFocusVisible(false);
  };

  const shareDropdownDivProps = {
    className: classnames({ [`evrBodyText${compact ? '2' : '1'}`]: true }, styles.evrDropdown, {
      [styles.hasValue]: !!value,
      [styles.compact]: compact,
      [styles.clearButton]: !!value && !hideClearButton,
      [styles.overlayContainer]: overlayVisible,
      [styles.disabled]: disabled,
      [styles.error]: status === 'error',
    }),
  };

  const handleMouseOver = () => {
    setIsHovered(true);
  };

  const handleMouseOut = () => {
    setIsHovered(false);
  };

  const renderSelectContainerContent = () => {
    return (
      <div
        {...shareDropdownDivProps}
        id={id}
        {...ariaAttributes.dropdown}
        ref={mergeRefs([ref, dataRef])}
        tabIndex={disabled ? -1 : 0}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onMouseEnter={handleMouseOver}
        onMouseOut={handleMouseOut}
      >
        {getDisplayValue()}
      </div>
    );
  };

  const renderOverlaySelectContainerContent = () => {
    return (
      <div {...shareDropdownDivProps} ref={overlayDataRef} id={`${id}-overlay`} tabIndex={-1} aria-hidden={true}>
        {getDisplayValue()}
      </div>
    );
  };

  const sharedSelectContainerProps = {
    disabled: disabled,
    readOnly: readOnly,
    showClearButton: value && !hideClearButton,
    clearButtonAriaLabel,
    onClearButtonFocus: handleFocus,
    onClearButtonBlur: handleBlur,
  };

  const renderFormFieldContainerContent = () => {
    return (
      <SelectContainer
        {...sharedSelectContainerProps}
        id={`${id}-select-container`}
        testId={testId ? `${testId}-select-container` : undefined}
        chevronIconOpen={false}
        clearButtonFocusable={true}
        onClear={handleClearSelection}
        renderContent={renderSelectContainerContent}
        clearButtonRef={clearButtonRef}
        onClearButtonKeyUp={handleClearButtonKeyUp}
        onClearButtonKeyDown={handleClearButtonKeyDown}
        onMouseEnter={handleMouseOver}
        onMouseOut={handleMouseOut}
      />
    );
  };

  const renderOverlayFormFieldContainerContent = () => {
    return (
      <SelectContainer
        {...sharedSelectContainerProps}
        id={`${id}-select-container-overlay`}
        testId={testId ? `${testId}-select-container-overlay` : undefined}
        chevronIconOpen={true}
        clearButtonFocusable={false}
        onClear={handleClearSelection}
        renderContent={renderOverlaySelectContainerContent}
        clearButtonRef={overlayClearButtonRef}
      />
    );
  };

  const handleFormFieldMouseDown = (e: React.MouseEvent) => {
    if (disabled || readOnly) return;
    if (
      (clearButtonRef.current && (clearButtonRef.current as HTMLElement).contains(e.target as HTMLElement)) ||
      (overlayClearButtonRef.current &&
        (overlayClearButtonRef.current as HTMLElement).contains(e.target as HTMLElement))
    )
      return;
    e.stopPropagation();
    setFocusedListItemId(emptyOption ? emptyOption.id : value?.id || selectableOptions[0].id);
    dispatch({ type: dropdownActions.MOUSE_CLICK });
  };

  const [focusedOnSoftSelectedItem, setFocusedOnSoftSelectedItem] = useState(false);

  useEffect(() => {
    setFocusedOnSoftSelectedItem(true);
  }, [softSelectedId]);

  useEffect(() => {
    if (focusedOnSoftSelectedItem && softSelectedId) {
      listBoxForwardRef.current && (listBoxForwardRef.current as IListBoxMethods).focusOnSoftSelectedItem();
    }
    setFocusedOnSoftSelectedItem(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focusedOnSoftSelectedItem]);

  const firstLoad = useRef(true);
  const [mobileFocusedOnDropdown, setMobileFocusedOnDropdown] = useState(false);
  useEffect(() => {
    if (os === 'ios' && !firstLoad.current) {
      if (!overlayVisible) {
        /**
         * workaround for iOS device
         * iOS doesn't recognized the overlay popup list is associated to the dropdown div
         * workaround to make sure it focused on dropdown div when the overlay has closed
         * use useState and useEffect to focus on the dataRef
         */
        setMobileFocusedOnDropdown(true);
      }
    }
    if (firstLoad.current) {
      firstLoad.current = false;
    }
    // Reset activeDescendent once overlay is collapsed
    if (!overlayVisible) {
      setAriaActiveDescendantId('');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [overlayVisible]);

  useEffect(() => {
    if (mobileFocusedOnDropdown) {
      dataRef.current && (dataRef.current as HTMLElement).focus();
      setMobileFocusedOnDropdown(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mobileFocusedOnDropdown]);

  const listItemContext: IListItemContext = {
    setAriaActiveDescendantId,
    isFocusVisible: isListItemFocusVisible,
    setIsFocusVisible: setIsListItemFocusVisible,
    itemRenderer,
    compact,
  };

  const handleSelection = (dataItem: IDataItem) => {
    if (emptyOption) return;
    if (dataItem.id !== value?.id) onChange?.(dataItem);
    dispatch({ type: dropdownActions.MAKE_SELECTION });
  };

  const handleEscape = () => {
    dispatch({ type: dropdownActions.KEYBOARD_CLOSE_OVERLAY });
  };

  useEffect(() => {
    if (prevLoadingRef.current && !loading) {
      setFocusedListItemId(emptyOption ? emptyOption.id : selectableOptions[0].id);
      dispatch({ type: dropdownActions.FOCUS_ON_FIRST_LIST_ITEM });
    }
    prevLoadingRef.current = loading;
  }, [loading, dispatch, emptyOption, selectableOptions]);

  const overlayTriggerRef = useRef<HTMLElement>(null);
  const renderOverlayContent = () => {
    return (
      <div
        className={classnames(styles.evrOverlayContainer, {
          [styles.error]: status === 'error',
        })}
      >
        <FormFieldContainer
          testId={testId && `${testId}-ff-container-overlay`}
          hideLabel={true}
          hideStatusMessage={true}
          bottomBorder={true}
          renderContent={renderOverlayFormFieldContainerContent}
          onMouseDown={handleFormFieldMouseDown}
          height={compact ? 'calc(var(--evr-size-lg) + 2 * var(--evr-border-width-thin-px))' : 'var(--evr-size-2xl)'}
        />
        <ListItemContext.Provider value={listItemContext}>
          <ListBox
            ref={listBoxForwardRef}
            options={emptyOption ? [emptyOption] : options}
            selectableOptions={emptyOption ? [emptyOption] : selectableOptions}
            height={listBoxHeight ? `${listBoxHeight}px` : ''}
            {...ariaAttributes.listbox}
            selectedOptions={value ? [value] : []}
            softSelectedId={softSelectedId}
            onSelection={handleSelection}
            onEscape={handleEscape}
            onSearchListItem={(searchValue) => {
              if (emptyOption) return;
              setSoftSelectedId(searchListItem(searchValue, selectableOptions, softSelectedId, overrideSearchMethod));
            }}
            onTabKeydown={() => interceptTabKeyDown(dataRef, dispatch)}
            id={dropdownListBoxId}
            listBoxRef={listBoxRef}
            onListItemFocus={handleFocus}
            onListItemBlur={handleBlur}
            onKeyboardNavigation={() => {
              if (state.value === dropdownStates.FOCUSED_WITH_OVERLAY)
                dispatch({ type: dropdownActions.KEYBOARD_FOCUS_LIST_ITEM });
            }}
            testId={testId ? `${testId}-list-box` : undefined}
            textMap={textMap}
          />
        </ListItemContext.Provider>
      </div>
    );
  };

  /**
   * workaround for clicking label to focus on the dropdown
   * dropdown is using <div> instead of <input>
   * `for` attribute only support labelable form-related elements
   */
  const handleLabelClick = () => {
    if (disabled) return;
    dispatch({ type: dropdownActions.FOCUS });
  };

  const renderTriggerAreaContent = () => {
    return (
      <FormFieldContainer
        id={id && `${id}-ff-container`}
        testId={testId && `${testId}-ff-container`}
        borderContainerRef={overlayTriggerRef}
        renderContent={renderFormFieldContainerContent}
        onMouseDown={handleFormFieldMouseDown}
        onClickLabel={handleLabelClick}
        focused={isFocused}
        // Don't need htmlFor here since Dropdown is not an input based component
        // Clicking the label will not handle focus
        labelId={dropdownLabelId}
        statusMessageId={statusMessageId}
        hovered={isHovered}
        height={compact ? 'calc(var(--evr-size-lg) + 2 * var(--evr-border-width-thin-px))' : 'var(--evr-size-2xl)'}
      />
    );
  };

  const formFieldContainerContext = useMemo(
    () => ({
      label,
      disabled,
      readOnly,
      required,
      status,
      helperText,
      helperTextPrefix,
      statusMessage,
      statusMessagePrefix,
    }),
    [label, disabled, readOnly, required, status, helperText, helperTextPrefix, statusMessage, statusMessagePrefix]
  );

  useEffect(() => {
    if (!overlayContentHeight) setOverlayHeight('');
    const borderSize = status === 'error' ? 'var(--evr-border-width-thick-px)' : 'var(--evr-border-width-thin-px)';
    setOverlayHeight(`calc(${overlayContentHeight}px + (${borderSize} * 3))`); // total 3 => 2 for the borderContainer borders and 1 for the FormFieldContainer bottomBorder
  }, [status, overlayContentHeight]);

  return (
    <FormFieldContainerContext.Provider value={formFieldContainerContext}>
      <TriggerAreaStyledOverlay
        id={`${id}-trigger-area-overlay`}
        triggerRef={overlayTriggerRef}
        overlayVisible={overlayVisible}
        overlayHeight={overlayHeight}
        error={status === 'error'}
        renderTriggerAreaContent={renderTriggerAreaContent}
        renderOverlayContent={renderOverlayContent}
        overrideFocusRingDefaultBehavior={isListItemFocusVisible}
      ></TriggerAreaStyledOverlay>
    </FormFieldContainerContext.Provider>
  );
});

Dropdown.displayName = 'Dropdown';
