# EmptyState

- Start Date: 2025-04-02
- Figma link: <https://www.figma.com/design/yzd3fHCowfWDL4iTBAoJNv/Everest-Documentation-for-Designers?node-id=16559-127126&p=f&m=dev>
- Illustrations used in the component: <https://www.figma.com/design/ej2kr7CiVtmRmPhREFWirB/Everest-Assets?node-id=17886-6037&p=f&m=dev>
- Epic: <https://dayforce.atlassian.net/browse/PWEB-18988>

## Summary

An `EmptyState` component informs users about the absence of content, provides context for why it is missing, and often includes actions to guide them toward the next steps.

## Detailed Design

The component can include up to four optional elements stacked vertically:

- An illustration
- A title
- A subtitle
- An action button

Each element is centered horizontally within a flex container.

## API

1. **id**: `string`  
   Sets **id** attribute on the html element.
1. **testId**: `undefined | string`  
   Sets **data-testid** attribute on the html element.
1. **size**: `'sm' | 'md' | 'lg'` Default is `md`.
   This prop controls the font styling for both the `title` and `subtitle` containers. When the `title` and `subtitle` props are provided as strings, they inherit the font styling from the containers by applying the appropriate classes based on the size prop. If these props are provided as `React.ReactNode`, the same classes will be applied, but users can override them with custom styling in their templates.
1. **title**: `undefined | React.ReactNode`  
   Renders the title content. Default styling is applied to text nodes, but it can be fully overridden by providing a custom template.
1. **subtitle**: `undefined | React.ReactNode`  
   Renders the subtitle content. Default styling is applied to text nodes, but it can be fully overridden by providing a custom template.
1. **illustration**: `undefined | React.ReactNod`  
   Provides a slot for illustrations. It is the consumer’s responsibility to ensure that the illustration's size aligns with the other elements in the `EmptyState` component.
1. **actions**: `undefined | React.ReactNode`  
   Displays action buttons. Consumers must ensure that the `Button`'s size and styling is correctly applied and aligns with other elements in the `EmptyState` component.
1. **className**: `undefined | string`  
   Enables additional styling control (e.g., min-width, width, max-width, borders) and allows customization of the default gap between elements.

### Usage With Default Styling

```jsx
   <EmptyState 
      id="example" 
      testId="example" 
      illustration={<SpotIllustration name="empty" size="medium"/>}
      title="No Entries"
      subtitle="There are no candidates for the current filters set."
      actions={<Button label="Reset Filters" variant="primary" size="large"/>}
      className="some-class-that-sets-container-width"
   />
```

### Usage With Custom Styling

```jsx
   <EmptyState 
      id="example" 
      testId="example" 
      illustration={<SpotIllustration name="empty" size="medium"/>}
      title={<h3 classname="some-class">No Entries</h3>}
      subtitle={<p classname="another-class">There are no candidates for the current filters set.</p>}
      actions={<Button label="Reset Filters" variant="primary" size="large"/>}
      className="some-class-that-sets-container-width"
   />
```

## Accessibility

- The action button is the only focusable element and includes its own label, so an additional aria-label is not necessary.
- For the component’s content to be announced when it appears, it should be enclosed in an existing container with `aria-live="polite"`. This is because `aria-live` regions announce content changes only when the container is already present in the DOM and its content updates dynamically — not when the live region itself is added. The exact implementation may vary depending on the context in which the `EmptyState` is used. I suspect that it won't always be required.

## Future Considerations

- If secondary button will be needed it can be passed in using the `actions` prop.

## Other Design Systems

This is almost identical implementation to what Atlassian does with the exception of `illustration`, `actions` type and additional `className` prop.
[Polaris](https://polaris.shopify.com/components/layout-and-structure/empty-state) and [Carbon Design](https://carbondesignsystem.com/patterns/empty-states-pattern/) use very similar approaches.

## Required PBIs

1. [Create Architecture document](https://dayforce.atlassian.net/browse/PWEB-19824)
1. [Create React Component + Docs](https://dayforce.atlassian.net/browse/PWEB-20007)
1. [Tests: A11y, Manual, Visual tests, Unit tests, playwright tests](https://dayforce.atlassian.net/browse/PWEB-20006)
1. [Mark As Ready](https://dayforce.atlassian.net/browse/PWEB-20008)
1. [Add to Ref App](https://dayforce.atlassian.net/browse/PWEB-20009)

## Q&A

## Changelog

[Date]: [Jira Ticket Number] - [Title of the Jira ticket]
