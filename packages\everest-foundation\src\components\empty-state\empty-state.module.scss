@use '@ceridianhcm/theme/dist/scss/' as typography;
@use '../../variables.scss';

@mixin centerColumn($gap) {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $gap;
}

.evrEmptyState {
  @include centerColumn(var(--evr-spacing-sm));
  justify-content: center;
  box-sizing: border-box;

  &.lg {
    min-width: variables.$emptyStateMinWidthLg;
    max-width: variables.$emptyStateMaxWidthLg;
  }

  &.md {
    min-width: variables.$emptyStateMinWidthMd;
    max-width: variables.$emptyStateMaxWidthMd;
  }

  &.sm {
    min-width: variables.$emptyStateMinWidthSm;
    max-width: variables.$emptyStateMaxWidthSm;
  }

  .actionAndTextWrapper {
    @include centerColumn(var(--evr-spacing-md));

    &.sm {
      gap: var(--evr-spacing-sm);
    }

    .actions {
      display: flex;
      gap: var(--evr-spacing-xs);
    }
  }

  .textWrapper {
    @include centerColumn(var(--evr-spacing-md));
    color: var(--evr-content-primary-default);
    text-align: center;
  
    &.sm,
    &.md {
      gap: var(--evr-spacing-2xs);
  
      .subtitle {
        @include typography.body2Regular;
      }
  
      .title {
        @include typography.heading4Lg;
      }
    }
  
    &.lg {
      .subtitle {
        @include typography.body1Regular;
      }
  
      .title {
        @include typography.heading3Lg;
      }
    }
  }
}
