import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { EmptyState } from './empty-state';
import { Chromatic } from '../../../chromatic';
import { Button } from '../button/button';

<Meta
  title="Testing/Automation Test Cases/EmptyState"
  component={ EmptyState }
  args={{
    subtitle: "There are no entries found based on the current filters set. Try different filters to see some entries.",
    illustration: <div style={{width: '100px', height: '100px', border: '1px solid lightgrey'}}></div>
  }}
/>

# EmptyState

## Live Demo

<Canvas>
  <Story name="Large">
    {(args) => (
      <EmptyState
        id="sample-empty-state-1"
        size='lg'
        title="LARGE. Super long title to check the behavior"
        actions={<Button id="action-id-1" label="Action" size='large' />}
        {...args}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Medium">
    {(args) => (
      <EmptyState
        id="sample-empty-state-2"
        size='md'
        title="MEDIUM. Super long title to check the behavior"
        actions={<Button id="action-id-2" label="Action" />}
        {...args}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Small">
    {(args) => (
      <EmptyState
        id="sample-empty-state-3"
        size='sm'
        title="SMALL. Super long title to check the behavior"
        actions={<Button id="action-id-3" label="Action" />}
        {...args}
        subtitle={undefined}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="No Subtitle - Large">
    {(args) => (
      <EmptyState
        id="sample-empty-state-5"
        size='lg'
        title="No Subtitle - Large"
        actions={<Button id="action-id-5" label="Action" size='large' />}
        {...args}
        subtitle={undefined}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="No Illustration - Small">
    {(args) => (
      <EmptyState
        id="sample-empty-state-6"
        size='sm'
        title="No Illustration - Small"
        actions={<Button id="action-id-6" label="Action" />}
        {...args}
        illustration={undefined}
        subtitle={undefined}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="No Illustration - Large">
    {(args) => (
      <EmptyState
        id="sample-empty-state-7"
        size='lg'
        title="No Illustration - Large"
        actions={<Button id="action-id-7" label="Action" size='large' />}
        {...args}
        illustration={undefined}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="No Actions">
    {(args) => (
      <EmptyState
        id="sample-empty-state-8"
        title="No Actions"
        {...args}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Two Actions">
    {(args) => (
      <EmptyState
        id="sample-empty-state-9"
        size='sm'
        title="No Actions"
        actions={<>
          <Button id="action-id-9-1" label="Action 1" />
          <Button id="action-id-9-2" label="Action 2" />
        </>}
        {...args}
        subtitle={undefined}
      />
    )}
  </Story>
</Canvas>
