import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { EmptyState } from './empty-state';
import { Button } from '../button/button';

<Meta
  title="Everest Labs/Components/EmptyState"
  component={ EmptyState }
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/yzd3fHCowfWDL4iTBAoJNv/Everest-Documentation-for-Designers?node-id=16559-127126&p=f&m=dev',
    },
  }}
  args={{
    id: "empty-state-demo",
    title: "No Entries Found",
    subtitle: "There are no entries found based on the current filters set. Try different filters to see some entries.",
    illustration: 'Defined',
    actions: 'Defined',
    size: 'md'
  }}
  argTypes={{
    size: {
      control: 'select',
    },
    illustration: {
      control: 'select',
      options: ['Defined', 'Undefined'],
      mapping: {
        Defined: <div style={{ width: '200px', height: '200px', border: '1px solid lightgrey' }}></div>,
        Undefined: undefined,
      },
    },
    actions: {
      control: 'select',
      options: ['Defined', 'Undefined'],
      mapping: {
        Defined: <Button id="action-id" label="Action" />,
        Undefined: undefined,
      },
    }
  }}
/>

# EmptyState

## Live Demo

<Canvas>
  <Story name="EmptyState">
    {(args) =>
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <EmptyState {...args} subtitle={args.size === 'sm' ? undefined : args.subtitle} />
      </div>
    }
  </Story>
</Canvas>

<ArgsTable story="EmptyState" />
