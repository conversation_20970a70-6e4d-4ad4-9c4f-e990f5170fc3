import React, { PropsWithChildren } from 'react';
import classNames from 'classnames';

import styles from './empty-state.module.scss';

export interface IEmptyStateProps {
  /** Sets id attribute on the container element */
  id: string;
  /** Sets data-testid attribute on the container element */
  testId?: string;
  /**
   * Controls styling for title and subtitle as well as gaps within the component.
   * @default md
   */
  size?: 'sm' | 'md' | 'lg';
  /** Renders title content. A default styling class is applied based on the size prop, but can be overriden with custom template. */
  title?: React.ReactNode;
  /** Renders the subtitle content. A default styling class is applied based on the size prop, but can be overriden with custom template. */
  subtitle?: React.ReactNode;
  /** Renders an illustration. Consumers must ensure it aligns in size with the other elements. */
  illustration?: React.ReactNode;
  /** Renders the action node with Button component. Consumers must ensure size and styling align with the other elements. */
  actions?: React.ReactNode;
  /** Custom class name to provide additional styling such as width, gap, etc. */
  className?: string;
}

export const EmptyState = ({
  id,
  testId,
  size = 'md',
  title,
  subtitle,
  illustration,
  actions,
  className,
}: PropsWithChildren<IEmptyStateProps>): JSX.Element => {
  return (
    <div id={id} data-testid={testId} className={classNames(styles[size], styles.evrEmptyState, className)}>
      {illustration && <div className={styles.illustrationRow}>{illustration}</div>}

      <div className={classNames(styles[size], styles.actionAndTextWrapper)}>
        <div className={classNames(styles[size], styles.textWrapper)}>
          {title && <div className={styles.title}>{title}</div>}
          {subtitle && <div className={styles.subtitle}>{subtitle}</div>}
        </div>

        {actions && <div className={styles.actions}>{actions}</div>}
      </div>
    </div>
  );
};

EmptyState.displayName = 'EmptyState';
