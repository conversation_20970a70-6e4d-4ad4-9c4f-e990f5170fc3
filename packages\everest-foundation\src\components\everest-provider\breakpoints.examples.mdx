import { LinkTo } from '../../../.storybook/docs/shared/link-to';

Breakpoints are available in both SCSS and React.

It is strongly encouraged that teams use the default breakpoints in their SCSS to ensure a consistent experience across MFEs. More specifically, use the breakpoint mapping as specified below, and define styles for media with these minimum widths.

## Using Breakpoints in SCSS

Paste the below at the top of your SCSS file.

```css
@use '@ceridianhcm/components/dist/breakpoints' as brkpts;
```

Breakpoints are defined via a map as follows.

```css
$breakpoints: (
  xs: 0px,
  sm: 430px,
  md: 768px,
  lg: 1280px,
  xl: 1440px,
  xxl: 1920px
);
```

Use the mixin `mediaBreakpointMin` in your SCSS to facilitate access to the breakpoint mapping.

For example, `myClass` below has background color red for screen sizes 0 through 767px inclusive, and blue for 768px pixels and higher.

```css
.myClass {
  @include brkpts.mediaBreakpointMin('xs') {
    background-color: red;
  }
  @include brkpts.mediaBreakpointMin('md') {
    background-color: blue;
  }
}
```

To access a specific breakpoint's value in SCSS, use `map.get` with the relevant key.

```css
@use 'sass:map';

$breakpointMd: map.get(brkpts.breakpoints, 'md');
```

## Using Breakpoints in React

See <LinkTo kind="Foundations/Everest Provider">EverestProvider</LinkTo> to subscribe to the last breakpoint.
