import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { useState, useRef } from 'react';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { useEverestContext } from './everest-provider';
import { Toggletip } from '../toggle-tip';
import { IconButton } from '../icon-button';

export const scope = { useEverestContext, useState, useRef, Toggletip, IconButton };

`EverestProvider` loads <LinkTo kind="Foundations/Tokens/Tokens Guide">tokens</LinkTo> into the DOM, and gives mostly static information about a page via React context. This includes the current browser name and last breakpoint.

## Using EverestProvider

As described in the <a href="https://ceridian.atlassian.net/l/cp/FcHEw198" target="_blank" rel="noreferrer">wiki</a>, wrap the contents of your MFE with `EverestProvider`. A unique `id` prop is required.

Review the containing `div` of your MFE to see <LinkTo kind="Foundations/Tokens/Tokens Guide">tokens</LinkTo> loaded into the DOM.

To access the current `browser` or `breakpoint`, destructure these properties from the context object returned by the `useEverestContext` hook.

### Browser Names

`browser` has the following type.

```typescript
type TBrowser =
  | 'Firefox'
  | 'Chrome'
  | 'Safari'
  | 'Samsung Internet'
  | 'Opera'
  | 'Microsoft Internet Explorer'
  | 'Microsoft Edge (Legacy)'
  | 'Microsoft Edge (Chromium)'
  | 'unknown';
```

### Breakpoint values

`breakpoint` returns the key specified by the SCSS map (see <LinkTo kind="Foundations/Breakpoints">Breakpoints</LinkTo>) whose mapping is the maximum value less than or equal to the screen size.

For example, if the screen size is 400px, the `breakpoint` has value `sm`.

### Example with `useEverestContext`

Try resizing your browser to see the current iframe breakpoint change.

export const defaultEverestProviderCode = `
  () => {
    const { browser, breakpoint } = useEverestContext();
    return (
      <div 
        className="evrBodyText1" 
        style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          flex: '1', 
          flexDirection: 'column' 
      }}>
        <div>Browser: {browser}</div>
        <div>Current Breakpoint: {breakpoint}</div>
      </div>
    );
  }
`;

<CodeExample scope={scope} code={defaultEverestProviderCode} />

### Example of Distinguishing between Screen Sizes

Distinguishing between breakpoints in React is useful when there are different presentations, components, or parameterizations corresponding to different screen sizes. For example, a small screen may require a different component or parameters from desktop.

The below example uses `useEverestContext` with `Toggletip` to determine its presentation. Resize your browser to see the toggletip occupy the entire screen when the screen is narrow, and become a dialogue when the screen is wide.

export const defaultToggleTipEverestProviderCode = `
  () => {
    const [open, setOpen] = useState(false);
    const { breakpoint } = useEverestContext();
    const isFullscreen = 'xs' === breakpoint || 'sm' === breakpoint;
    const triggerRef = useRef(null);
    const onClick = (e) => setOpen(prev => !prev);
    const onKeyDown = (e) => {
      e.preventDefault();
      if (e.key === 'Enter' || e.key === ' ') {
        setOpen(true);
      }
    };
    const onClose = (e) => {
      setOpen(false);
      const btn = document.getElementById('toggletip-btn-everest-provider');
      btn && btn.focus();
    };
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        className="evrBodyText2"
      >
        <div style={{ height: 'min-content' }}>
          <IconButton
            id="toggletip-btn-everest-provider"
            iconName="information"
            onClick={onClick}
            onKeyDown={onKeyDown}
            ref={triggerRef}
            ariaExpanded={open}
            ariaControls='toggle-tip-breakpoint-example'
            ariaLabel="Information"
            variant="tertiaryNeutral"
          />
        </div>
        <Toggletip 
          id='toggle-tip-breakpoint-example'
          open={open}
          placement='topRight'
          closeButtonAriaLabel='Close'          
          onClose={onClose} 
          triggerRef={triggerRef}
          fullscreen={isFullscreen}
        >
          <div>
            {isFullscreen ? 'Current presentation is for narrow screens' : 'Current presentation is for wide screens'}
          </div>
        </Toggletip>

      </div>
    )

}
`;

<CodeExample scope={scope} code={defaultToggleTipEverestProviderCode} />
