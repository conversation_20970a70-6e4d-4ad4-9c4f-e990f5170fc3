import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';

import '@testing-library/jest-dom/extend-expect';
import { EverestProvider, useEverestContext, LIBRARY_URL } from './everest-provider';

const id = 'everest-provider-id';
const librariesUrl = 'dayforce.com/libraries/';

// Mock fetch globally
global.fetch = jest.fn(() =>
  Promise.resolve({
    text: () => Promise.resolve(JSON.stringify({ data: { paths: '<path></path>' } })),
  })
) as jest.Mock;

describe('[EverestProvider]', () => {
  const TestComponent = () => {
    const contextValue = useEverestContext();
    return (
      <div>
        <p>{contextValue.librariesUrl}</p>
      </div>
    );
  };

  it('provides the expected default context value', async () => {
    render(
      <div>
        <EverestProvider id={id}>
          <TestComponent />
        </EverestProvider>
      </div>
    );

    await waitFor(() => {
      expect(screen.getByText(LIBRARY_URL)).toBeInTheDocument();
    });
  });

  it('passes prop librariesUrl correctly', async () => {
    render(
      <div>
        <EverestProvider id={id} librariesUrl={librariesUrl}>
          <TestComponent />
        </EverestProvider>
      </div>
    );

    await waitFor(() => {
      expect(screen.getByText(librariesUrl)).toBeInTheDocument();
    });
  });
});
