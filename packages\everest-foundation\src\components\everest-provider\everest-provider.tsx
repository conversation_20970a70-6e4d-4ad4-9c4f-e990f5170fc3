import React, { useState, PropsWithChildren, useMemo, useContext } from 'react';
import { ThemeProvider as TokenProvider } from '@ceridianhcm/theme';

import { getBrowser, getGlobalContext, TBrowser, useBreakpoints } from '../../utils';
import { IconProvider } from '../icon-provider';
import { WindowSizeProvider } from '../window-size-provider';

// the URL for the static assets of the icons
export const LIBRARY_URL = 'https://static.dayforcehcm.com/libraries/';

export interface IEverestContext {
  browser: TBrowser;
  breakpoint: string;
  librariesUrl?: string;
  // include IRuntimeEnvironmentInfo in future
}

const defaultContext: IEverestContext = {
  browser: 'Chrome',
  breakpoint: 'xs',
  librariesUrl: LIBRARY_URL,
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const EverestContext = getGlobalContext<IEverestContext>('everestContext', defaultContext);

export interface IEverestProvider {
  id: string;
  librariesUrl?: string;
}

export const EverestProvider = (props: PropsWithChildren<IEverestProvider>): JSX.Element => {
  const {
    id,
    librariesUrl = (window['Dayforce' as any] as any)?.AppSettingsData?.librariesUrl ?? LIBRARY_URL,
    children,
  } = props;
  const [breakpoint, setBreakpoint] = useState(defaultContext.breakpoint);
  const [browser] = useState<TBrowser>(getBrowser());

  useBreakpoints((size) => setBreakpoint(size));

  const context = useMemo(() => ({ breakpoint, browser, librariesUrl }), [breakpoint, browser, librariesUrl]);

  return (
    <EverestContext.Provider value={context}>
      <WindowSizeProvider>
        <TokenProvider id={id}>
          <IconProvider>{children}</IconProvider>
        </TokenProvider>
      </WindowSizeProvider>
    </EverestContext.Provider>
  );
};

export const useEverestContext = (): IEverestContext => useContext(EverestContext);

EverestProvider.displayName = 'EverestProvider';
