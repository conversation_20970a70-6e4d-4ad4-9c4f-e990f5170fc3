import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { FileDropzone, FileDropzoneContext } from './file-dropzone';
import { useContext } from 'react';
import { <PERSON><PERSON> } from '../button';
import { FileUploadTemplate } from '../file-upload-template';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { AlphaBanner, ContributedBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';

<ContributedBanner dev="<PERSON><PERSON><PERSON>" team="UI Modernization" />

<AlphaBanner />

export const scope = { Button, FileDropzone, FileDropzoneContext, useContext, FileUploadTemplate };

File Dropzone is designed to allow file upload through drag-and-drop or file selection. It can be used together with <LinkTo kind="Everest Labs/Components/File Dropzone/File Upload Template">File Upload Template</LinkTo>.

## Basic Usage

export const defaultCode = `() => {
  return (
    <FileDropzone id="file-dropzone-id" onFileSelected={(files) => console.log('Files uploaded:', files)}>
      <FileUploadTemplate
        id="file-upload-template-id"
        primaryText="Drop files here to Upload, or"
        buttonLabel="Upload"
        captionText="Max file size is 100MB"
        />
    </FileDropzone>
  );
}`;

<CodeExample scope={scope} code={defaultCode} />

## Variations

### Single File Upload

By default, multiple files may be uploaded to File Dropzone. To limit this number of files to one, set `multiple` to `false`.

export const singleFileCode = `() => {
  return (
    <FileDropzone 
      id="file-dropzone-single-file-id" 
      onFileSelected={(files) => console.log('Files uploaded:', files)} 
      multiple={false}
    >
      <FileUploadTemplate
        id="file-upload-template-id"
        primaryText="Drop file here to Upload, or"
        buttonLabel="Upload"
        captionText="Max file size is 100MB"
      />
    </FileDropzone>
  );
}`;

<CodeExample scope={scope} code={singleFileCode} />

### With Label

A label may be set on File Dropzone using the `label` prop.

export const labelCode = `() => {
  return (
    <FileDropzone 
      id="file-dropzone-label-id" 
      onFileSelected={(files) => console.log('Files uploaded:', files)} 
      label="Custom Label"
    >
      <FileUploadTemplate
        id="file-upload-template-id"
        primaryText="Drop files here to Upload, or"
        buttonLabel="Upload"
        captionText="Max file size is 100MB"
      />
    </FileDropzone>
  );
}`;

<CodeExample scope={scope} code={labelCode} />

### With Error Message

An error message may be set using the `errorMessage` and `errorMessagePrefix` props.

export const errorMessageCode = `() => {
  return (
    <FileDropzone 
      id="file-dropzone-error-message-id" 
      errorMessagePrefix="Error: " 
      errorMessage="Cannot upload more than 5 files at once" 
      onFileSelected={(files) => console.log('Files uploaded:', files)}
    >
      <FileUploadTemplate
        id="file-upload-template-id"
        primaryText="Drop files here to Upload, or"
        buttonLabel="Upload"
        captionText="Max file size is 100MB"
      />
    </FileDropzone>
    );
}`;

<CodeExample scope={scope} code={errorMessageCode} />

## Accessibility

A unique id should always be used to allow for correct functionality with assistive technologies.

Keyboard interaction for the button - Pressing `Enter` or `Space` key will trigger the `onClick` callback.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale. For example:

```tsx
<FileDropzone id="file-dropzone-id" onFileSelected={(files) => console.log('Files uploaded:', files)}>
  <FileUploadTemplate
    id="file-upload-template-id"
    primaryText="Drop files here to Upload, or"
    buttonLabel="Upload"
    captionText="Max File size is 100MB, 5 files per upload"
  />
</FileDropzone>
```
