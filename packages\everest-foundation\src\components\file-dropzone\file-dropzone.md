# File Dropzone

## Summary

Research and document implementations for the Everest File Dropzone.

- Start Date: 2024-11-27
- Figma links:
  - Quick Entry Design (the origin of Uploader): https://www.figma.com/design/qiYLWZeZQemQVMTZsUoM3A/File-Uploader?node-id=402-40359&node-type=canvas&t=eoEAZa0untj37W2r-0

## Detailed Design

The `FileDropzone` component is designed to allow users to upload files through drag-and-drop or file selection. It is intended to be flexible, providing a wrapper for customizable child components such as `FileUploaderTemplate` for customizing the content and UI. The `FileDropzone` is built with the following features:

- Drag-and-drop functionality for file uploads.
- Supports multiple file uploads simultaneously.
- Provides a hidden file input for users to click and browse files.
- Allows for customizable UI and instructions passed via the `children` prop.

## API

### FileDropzoneProps

1. **onFileSelected**: `(files: File[]) => void`  
   A callback function that is triggered when files are dropped into the dropzone. The dropped files are passed as an array.

2. **children**: `React.ReactNode`  
   The customizable child component (e.g., `FileUploaderTemplate`) that defines the content and instructions inside the dropzone.

3. **errorMessage**: `string`  
   The customizable error message that gets displayed to the user.

4. **label**: `string`  
   The customizable label message for the file dropzone component.

5. **id**: `string`  
   Sets a string identifier for the component.

6. **testId**: `string | undefined`
   Optional. Sets the **data-test-id** attribute on the HTML element.

7. **errorMessagePrefix**: `string | undefined`  
   Optional. The customizable error message prefix that gets displayed to the user.

8. **multiple**: `boolean | undefined`  
   Optional. This allows the user to upload multiple files.

## Usage

Include sample usage, to better illustrate how feature teams are expected to use this component. For example,

<FileDropzone onFileSelected={handleFileUpload}>
  <FileUploaderTemplate
    instructionText="Drag and drop your files here"
    onBrowse={handleFileUpload}
  />
</FileDropzone>

## Prerequisite/Other Components

FileUploaderTemplate: A customizable component that can be passed to the FileDropzone as a child. This template can include instructions, file selection buttons, and any other UI elements needed to guide the user in the file upload process.

## Accessibility

Drag and Drop Accessibility: The drag-and-drop functionality isn't inherently accessible, but users can still browse for files using the file input button. The hidden file input (<input type="file" />) is keyboard accessible and can be triggered with the "Enter" or "Space" keys.

## Alternatives/Trade-Offs

Customizable Instructions: Since the FileDropzone doesn't include default text or instructions, it's up to the child component (e.g., FileUploaderTemplate) to provide clear instructions. This makes it highly flexible but requires careful UI design for consistency.

## Q&A

**Can the file input button be styled?**
Yes, the file input button is hidden by default, but it can be customized through the child component (FileUploaderTemplate), which can trigger the file input manually.

**Can we support more file validation?**
Yes, you can handle validation for allowed file types, file size, and number of files within the child components or the parent component where onFileSelected is handled.

**What happens if a file exceeds the size limit?**
While FileDropzone itself does not handle file size validation, you can implement this feature in the child component or onFileSelected callback. You can reject the file based on its size and display a suitable error message.

**Can we pass additional props to the child component?**
Yes, any additional props required by the child component (like FileUploaderTemplate) can be passed directly in the children prop.

**Is the drag-and-drop zone fully responsive?**
Yes, the FileDropzone is designed to be fully responsive. The child components can be styled as needed to ensure the dropzone works across various screen sizes.

**Can FileDropzone support multiple file uploads?**
Yes, the FileDropzone component supports multiple file uploads if the child component (like FileUploaderTemplate) handles the multiple file selection and validation.

**What is the onFileSelected function used for?**
The onFileSelected function is used to handle the uploaded files when they are dropped into the dropzone. It receives an array of files that can be processed or uploaded.

## Other Design Systems

**Carbon**

- https://react.carbondesignsystem.com/?path=/docs/components-fileuploader--overview

**Spectrum**

- https://opensource.adobe.com/spectrum-web-components/components/dropzone/
- there is no react flavor

**Ant Design**

- https://ant.design/components/upload

## Required PBIs

EDS-3327 - FileDropzone Uplaoder Template Component - https://ceridian.atlassian.net/browse/EDS-3327

1. FileDropzone Architecture/Development - https://dayforce.atlassian.net/browse/PWEB-18309
2. Architecture/Accessibility/Tests - https://dayforce.atlassian.net/browse/PWEB-18310

## Acceptance Criteria

- Document and setup guidelines in Storybook
- Ensure styles align with design specifications from Figma.
- Ensure enough (diverse) examples are featured in Storybook
- Verify the following:
  - Accessibility (aria attributes, navigation order, screen reader callouts, mouse, and keyboard interactivity)
- Visual tests implemented
