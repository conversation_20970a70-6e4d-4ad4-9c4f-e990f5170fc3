@use '@ceridianhcm/theme/dist/scss/' as typography;

.evrFileDropzone {
  display: flex;
  flex-direction: column;
  gap: var(--evr-spacing-3xs);

  .outerBox {
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default);
    border-radius: var(--evr-radius-2xs);
    box-sizing: border-box;
    padding: var(--evr-spacing-sm);
    display: flex;
    align-items: stretch;
    transition: all 0.3s ease;
    margin-block-end: var(--evr-spacing-3xs);

    /* Error styling for outer box */
    &.error {
      border-color: var(--evr-borders-status-error);
    }
  }

  .innerBox {
    border: var(--evr-border-width-thin-px) dashed var(--evr-borders-decorative-default);
    border-radius: var(--evr-radius-2xs);
    background-color: var(--evr-surfaces-secondary-default);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
    padding: var(--evr-spacing-xl) var(--evr-spacing-sm);

    /* Visual changes when files are dragged over the zone */
    &.dragging {
      background-color: var(--evr-interactive-primary-decorative);
    }
  }

  .fileInputField {
    display: none;
  }
}
