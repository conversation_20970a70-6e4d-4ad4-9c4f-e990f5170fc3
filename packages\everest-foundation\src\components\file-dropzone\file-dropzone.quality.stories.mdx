import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { FileDropzone } from './file-dropzone';
import { FileUploadTemplate } from '../file-upload-template';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

export const id = 'dropzone-id';
export const testId = 'dropzone-test-id';
export const labelValue = 'Supporting Documents';
export const helperText = 'Drag and drop files here or click to upload';
export const errorMessagePrefix = 'Error: ';
export const errorMessageValue = 'This is an error';
export const width = '300px';
export const height = '300px';
export const onFileSelected = (files) => {
  console.log('Files uploaded:', files); // Inline definition of handleFileUpload
};

export const FileDropzoneWithTemplate = (args) => {
  return (
    <FileDropzone {...args}>
      <FileUploadTemplate
        id="file-upload-template-id"
        primaryText="Drop files here to Upload, or"
        buttonLabel="Upload"
        captionText="Max File size is 100MB, 5 files per upload"
      />
    </FileDropzone>
  );
};

<Meta
  title="Testing/Automation Test Cases/File Dropzone"
  component={FileDropzone}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id,
    testId,
    onFileSelected,
  }}
/>

# File Dropzone

<Canvas>
  <Story name="Default">{(args) => <FileDropzoneWithTemplate {...args} />}</Story>
</Canvas>

<Canvas>
  <Story name="Custom Width and Height">
    {(args) => <FileDropzoneWithTemplate {...args} width={width} height={height} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Label">{(args) => <FileDropzoneWithTemplate {...args} label={labelValue} />}</Story>
</Canvas>

<Canvas>
  <Story name="Error Message">
    {(args) => (
      <FileDropzoneWithTemplate {...args} errorMessagePrefix={errorMessagePrefix} errorMessage={errorMessageValue} />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => (
      <FileDropzoneWithTemplate
        {...args}
        label={labelValue}
        errorMessagePrefix={errorMessagePrefix}
        errorMessage={errorMessageValue}
      />
    )}
  </Story>
</Canvas>
