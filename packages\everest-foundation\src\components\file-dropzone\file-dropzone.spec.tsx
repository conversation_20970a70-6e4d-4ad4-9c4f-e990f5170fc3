import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { FileDropzone } from './file-dropzone';
import { FileUploadTemplate } from '../file-upload-template/file-upload-template';

const id = 'dropzone-1';
const testId = 'dropzone-test-id';
const label = 'Supporting Documents';
const errorMessage = 'This is an error';
const errorMessagePrefix = 'Custom Error:';
const file = new File(['file content'], 'example.txt', { type: 'text/plain' });
const multipleFiles = [
  new File(['file1'], 'file1.txt', { type: 'text/plain' }),
  new File(['file2'], 'file2.txt', { type: 'text/plain' }),
];
const onFileSelected = jest.fn();
const buttonLabel = 'Upload';
const getFileInput = () => screen.getByTestId(`${testId}-file-input`);
const getErrorMessageElement = () => screen.getByTestId(`${testId}-error-message`);
const getLabel = () => screen.getByText(label);
const queryLabel = () => screen.queryByText(label);

describe('[FileDropzone]', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with a label if provided', () => {
    render(<FileDropzone id={id} testId={testId} onFileSelected={onFileSelected} label={label} />);
    expect(getLabel()).toBeInTheDocument();
  });

  it('does not render the label if none is provided', () => {
    render(<FileDropzone id={id} testId={testId} onFileSelected={onFileSelected} />);

    expect(queryLabel()).not.toBeInTheDocument();
  });

  it('renders the template component successfully', () => {
    render(
      <FileDropzone id={id} testId={testId} onFileSelected={onFileSelected} label={label}>
        <FileUploadTemplate
          id="file-upload-template-id"
          primaryText="Drop files here to Upload, or"
          buttonLabel="Upload"
          captionText="Max File size is 100MB, 5 files per upload"
        />
      </FileDropzone>
    );
    expect(getLabel()).toBeInTheDocument();
    expect(screen.getByText('Drop files here to Upload, or')).toBeInTheDocument();
  });

  it('calls onFileSelected when files are selected', async () => {
    render(
      <FileDropzone id={id} testId={testId} onFileSelected={onFileSelected}>
        Drop your files
      </FileDropzone>
    );

    await userEvent.upload(getFileInput(), file);

    await waitFor(() => expect(onFileSelected).toHaveBeenCalledWith([file]));
  });

  it('handles multiple file selection correctly', async () => {
    render(<FileDropzone id={id} testId={testId} onFileSelected={onFileSelected} multiple />);

    await userEvent.upload(getFileInput(), multipleFiles);

    await waitFor(() => expect(onFileSelected).toHaveBeenCalledWith(multipleFiles));
  });

  it('handles single file selection correctly', async () => {
    render(<FileDropzone id={id} testId={testId} onFileSelected={onFileSelected} multiple={false} />);

    await userEvent.upload(getFileInput(), file);

    await waitFor(() => expect(onFileSelected).toHaveBeenCalledWith([file]));
  });

  it('displays the error message when provided', () => {
    render(
      <FileDropzone id={id} testId={testId} onFileSelected={onFileSelected} errorMessage={errorMessage}>
        Drop your files
      </FileDropzone>
    );
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('displays the error message with the correct prefix', () => {
    render(
      <FileDropzone
        id={id}
        testId={testId}
        onFileSelected={onFileSelected}
        errorMessage={errorMessage}
        errorMessagePrefix={errorMessagePrefix}
      >
        Drop your files
      </FileDropzone>
    );

    expect(getErrorMessageElement()).toHaveTextContent(errorMessagePrefix);
    expect(getErrorMessageElement()).toHaveTextContent(errorMessage);
  });

  it('renders default icon when no uploadIcon is passed', () => {
    render(
      <FileDropzone id={id} testId={testId} onFileSelected={onFileSelected}>
        <FileUploadTemplate
          id="file-upload-template-id"
          primaryText="Drop files here to Upload, or"
          buttonLabel="Upload"
          captionText="Max File size is 100MB, 5 files per upload"
          testId="file-upload-template"
        />
      </FileDropzone>
    );

    expect(screen.getByTestId('file-upload-template')).toHaveClass('evrFileUploadTemplateContainer');
  });

  it('does not render caption text if not passed', () => {
    render(
      <FileDropzone id={id} testId={testId} onFileSelected={onFileSelected}>
        <FileUploadTemplate
          id="file-upload-template-id"
          primaryText="Drop files here to Upload, or"
          buttonLabel="Upload"
          testId="file-upload-template"
        />
      </FileDropzone>
    );

    expect(screen.queryByText('Max File size is 100MB, 5 files per upload')).not.toBeInTheDocument();
  });

  it('renders the button with the correct label', () => {
    render(
      <FileDropzone id={id} testId={testId} onFileSelected={onFileSelected}>
        <FileUploadTemplate
          id="file-upload-template-id"
          primaryText="Drop files here to Upload, or"
          buttonLabel={buttonLabel}
          captionText="Max File size is 100MB, 5 files per upload"
          testId="file-upload-template"
        />
      </FileDropzone>
    );

    expect(screen.getByText(buttonLabel)).toBeInTheDocument();
  });
});
