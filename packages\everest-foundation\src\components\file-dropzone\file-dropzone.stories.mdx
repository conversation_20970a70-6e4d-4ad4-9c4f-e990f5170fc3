import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { FileDropzone } from './file-dropzone';
import Examples from './file-dropzone.examples.mdx';
import { useContext } from 'react';
import { FileUploadTemplate } from '../..';

<Meta
  title="Everest Labs/Components/File Dropzone/File Dropzone"
  component={FileDropzone}
  parameters={{
    status: {
      type: 'alpha',
      contributed: true,
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/qiYLWZeZQemQVMTZsUoM3A/File-Uploader?node-id=500-27084&t=gLDR8fv5KaOqMXEE-0',
    },
  }}
  args={{
    id: 'file-dropzone-id',
    testId: 'file-dropzone-test-id',
    label: 'Supporting Documents',
    multiple: true,
    onFileSelected: (files) => {
      console.log('Files uploaded:', files);
    },
  }}
/>

# File Dropzone

<Examples />

## Live Demo

<Canvas>
  <Story name="File Dropzone">
    {(args) => (
      <FileDropzone {...args}>
        <FileUploadTemplate
          id="file-upload-template-id"
          primaryText="Please upload your documents (PDF, DOCX allowed), or"
          buttonLabel="Upload"
          captionText="Max file size is 100MB, 5 files per upload"
        />
      </FileDropzone>
    )}
  </Story>
</Canvas>

<ArgsTable story="File Dropzone" />
