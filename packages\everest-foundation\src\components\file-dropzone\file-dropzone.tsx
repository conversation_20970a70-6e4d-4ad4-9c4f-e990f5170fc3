import React, { useState, useRef, create<PERSON>ontext, PropsWithChildren, useMemo } from 'react';
import classnames from 'classnames';

import { Label } from '../label';
import { StatusMessage } from '../status-message';

import styles from './file-dropzone.module.scss';

export interface IFileDropzoneProps {
  /** Sets `id` attribute on dropzone. */
  id: string;
  /** Sets `data-testid` on dropzone. */
  testId?: string;
  /** Callback function triggered when files are dropped. */
  onFileSelected: (files: File[]) => void;
  /** Sets error message in dropzone. */
  errorMessage?: string;
  /** Sets the prefix to the error message rendered in dropzone. */
  errorMessagePrefix?: string;
  /** Sets label on dropzone. */
  label?: string;
  /**
   * Sets width of dropzone.
   * @default 100%
   */
  width?: string;
  /**
   * Sets height of dropzone.
   * @default 100%
   */
  height?: string;
  /**
   * Determines if multiple files can be uploaded.
   * @default true
   */
  multiple?: boolean;
}

interface FileDropzoneContextType {
  openFileExplorer: () => void;
}

// esLint doesn't like this context object capitalized -- ignoring rule
// eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-empty-function
export const FileDropzoneContext = createContext<FileDropzoneContextType>({ openFileExplorer: () => {} });

export const FileDropzone: React.FC<PropsWithChildren<IFileDropzoneProps>> = ({
  id,
  onFileSelected,
  children,
  errorMessage,
  errorMessagePrefix,
  label,
  testId,
  width = '100%',
  height = '100%',
  multiple = true,
}) => {
  const [dragging, setDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null); // Reference for the file input element

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragging(false);
    const files = Array.from(e.dataTransfer.files);
    onFileSelected(files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      onFileSelected(files);
    }
  };

  const contextProps = useMemo(
    () => ({
      openFileExplorer: () => {
        if (fileInputRef.current) {
          fileInputRef.current.click();
        }
      },
    }),
    []
  );

  return (
    <FileDropzoneContext.Provider value={contextProps}>
      {/* Container for label, drop zone, and error message */}
      <div id={id} data-testid={testId} className={styles.evrFileDropzone}>
        {/* Render label if passed */}
        {!!label && (
          <Label id={`${id}-label`} variant="bodyText2" testId={testId ? `${testId}-label` : undefined}>
            {label}
          </Label>
        )}
        {/* Outer Box */}
        <div
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          data-testid={testId ? `${testId}-outer-box` : undefined}
          className={classnames(styles.outerBox, {
            [styles.error]: errorMessage, // Apply error class when errorMessage is provided
          })}
          style={{ width: width, height: height } as React.CSSProperties}
        >
          {/* Inner Box */}
          <div
            data-testid={testId ? `${testId}-inner-box` : undefined}
            className={classnames(styles.innerBox, {
              [styles.dragging]: dragging,
            })}
          >
            {children}
            <input
              id={`${id}-file-input`}
              data-testid={testId ? `${testId}-file-input` : undefined}
              ref={fileInputRef}
              type="file"
              className={styles.fileInputField}
              multiple={multiple}
              onChange={handleFileInputChange}
            />
          </div>
        </div>
        {/* Show errorMessage if prop provided */}
        <StatusMessage
          visible
          id={`${id}-error-message`}
          testId={testId && `${testId}-error-message`}
          variant={'error'}
          statusMessagePrefix={errorMessagePrefix}
          statusMessage={errorMessage}
        />
      </div>
    </FileDropzoneContext.Provider>
  );
};

FileDropzone.displayName = 'FileDropzone';
