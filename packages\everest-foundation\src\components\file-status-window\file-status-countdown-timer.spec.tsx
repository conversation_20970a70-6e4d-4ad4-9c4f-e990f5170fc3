import React from 'react';
import { render, screen } from '@testing-library/react';

import '@testing-library/jest-dom';
import { FileStatusCountdownTimer } from './file-status-countdown-timer';
import { Button } from '../button';
import { IconButton } from '../icon-button';

const props = {
  id: 'countdown-timer-id',
  testId: 'countdown-timer',
  label: '5 min. left...',
};

describe('[FileStatusCountdownTimer]', () => {
  it('renders with required props', () => {
    render(<FileStatusCountdownTimer {...props} />);
    expect(screen.getByTestId(props.testId)).toBeInTheDocument();
    expect(screen.getByText(props.label)).toBeInTheDocument();
  });

  it('applies the correct id and testId', () => {
    render(<FileStatusCountdownTimer {...props} />);
    const section = screen.getByTestId(props.testId);
    expect(section).toHaveAttribute('id', props.id);
    expect(section).toHaveAttribute('data-testid', props.testId);
  });

  it('renders actions when provided', () => {
    const actions = (
      <>
        <Button id="btnClear" label="Clear" />
        <IconButton iconName={'view'} ariaLabel="View" id="btnView" />
      </>
    );
    render(<FileStatusCountdownTimer {...props} actions={actions} />);
    expect(screen.getByRole('button', { name: /Clear/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /View/i })).toBeInTheDocument();
  });

  it('does not render actions when actions are not provided', () => {
    render(<FileStatusCountdownTimer {...props} />);
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });
});
