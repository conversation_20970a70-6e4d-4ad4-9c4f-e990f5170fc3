import React, { ReactNode } from 'react';

import styles from './file-status-window.module.scss';

export interface IFileStatusCountdownTimerProps {
  /**
   * A unique identifier.
   */
  id: string;
  /**
   * Sets **data-testid** attribute on the html element; used for automation testing.
   */
  testId?: string;
  /**
   * Optional. A message displayed about the current file transfer item.
   */
  label: string;
  /**
   * Optional. An array of action buttons to display.
   */
  actions?: ReactNode;
}

export const FileStatusCountdownTimer = ({
  id,
  testId,
  label,
  actions,
}: IFileStatusCountdownTimerProps): JSX.Element => {
  return (
    <section id={id} data-testid={testId} className={styles.evrFileStatusCountdownTimer}>
      <span className="evrCaptionText">{label}</span>
      {actions && <div>{actions}</div>}
    </section>
  );
};

FileStatusCountdownTimer.displayName = 'FileStatusCountdownTimer';
