import React from 'react';
import { render, screen } from '@testing-library/react';

import '@testing-library/jest-dom';
import { FileStatusList, IFileStatusDataItem } from './file-status-list';

jest.mock('./file-status', () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  __esModule: true,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  FileStatus: () => <div>Mocked FileStatus</div>,
}));

describe('[FileStatusList]', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockFiles: IFileStatusDataItem[] = [
    {
      id: 'file-1',
      name: 'Resume.docx',
      progressPercentage: 50,
      status: 'uploading',
      statusMessage: 'Uploading...',
      actions: [],
    },
    {
      id: 'file-2',
      name: 'Sales-CA.csv',
      progressPercentage: 100,
      status: 'success',
      statusMessage: 'Upload complete',
      actions: [
        {
          id: 'file-2-action-cancel',
          label: 'Cancel',
          variant: 'secondary',
          size: 'small',
        },
        {
          id: 'file-4-action-view',
          ariaLabel: 'View',
          iconName: 'view',
          variant: 'tertiary',
        },
      ],
    },
    {
      id: 'file-3',
      name: 'profile-avatar.png',
      progressPercentage: 0,
      status: 'error',
      statusMessage: 'Upload failed',
      actions: [
        {
          id: 'file-3-action-clear',
          label: 'Clear',
          variant: 'secondary',
          size: 'small',
        },
      ],
    },
  ];

  it('renders a list of files', () => {
    render(<FileStatusList id="file-status-list" files={mockFiles} />);
    const listItems = screen.getByRole('list').querySelectorAll('li');
    expect(listItems).toHaveLength(3);
  });

  it('renders an empty list when no files are provided', () => {
    render(<FileStatusList id="file-status-list" files={[]} />);
    const list = screen.getByRole('list');
    expect(list).toBeEmptyDOMElement();
  });
});
