import React from 'react';

import { <PERSON>ton, IButtonProps } from '../button';
import { IconButton, IIconButtonProps } from '../icon-button';
import { FileStatus, TFileStatusDataItemStatus } from './file-status';

import styles from './file-status-window.module.scss';

export interface IFileStatusDataItem {
  /**
   * A unique identifier.
   */
  id: string;
  /**
   * The name of the file.
   */
  name: string;
  /**
   * The progress percentage of the file.
   */
  progressPercentage: number;
  /**
   * The current status of the file.
   */
  status: TFileStatusDataItemStatus;
  /**
   * A message to display about the current status file.
   */
  statusMessage: string;
  /**
   * An array of action buttons to display.
   */
  actions: (IButtonProps | IIconButtonProps)[];
}

export interface IFileStatusListProps {
  /**
   * Sets the ID of the file status list.
   */
  id: string;
  /**
   * Sets `data-testid` for testing.
   */
  testId?: string;
  /**
   * A list of file status data items.
   */
  files: IFileStatusDataItem[];
}

const getActionButtons = (actions: (IButtonProps | IIconButtonProps)[] = []) => {
  return actions.map((action) => {
    if ('iconName' in action) {
      return <IconButton {...action} key={action.id} size="small" />;
    }
    return <Button {...action} key={action.id} size="small" />;
  });
};

export const FileStatusList = (props: IFileStatusListProps): JSX.Element => {
  const { files = [] } = props;

  return (
    <ul className={styles.evrFileStatusWindowFiles}>
      {files.map((file) => (
        <li key={file.id}>
          <FileStatus {...file} actions={getActionButtons(file.actions)} />
        </li>
      ))}
    </ul>
  );
};

FileStatusList.displayName = 'FileStatusList';
