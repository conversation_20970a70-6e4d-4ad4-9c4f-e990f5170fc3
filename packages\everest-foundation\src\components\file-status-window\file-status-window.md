# FileStatusWindow

- Start Date: 2025-03-10
- Figma links:
  - [Component](https://www.figma.com/design/Bkay9m2xXxUIc6BDMi4tOr/Everest-Web?m=auto&node-id=73712-32412&t=GSeTPDIkaFmPmUAt-1)
  - [Documentation](https://www.figma.com/design/yzd3fHCowfWDL4iTBAoJNv/Everest-Documentation-for-Designers?m=auto&node-id=16559-137650&t=RyMUEITn90Ii3P3H-1)
- Epic: [PWEB-10616](https://dayforce.atlassian.net/browse/PWEB-10616)

## Summary

The `FileStatusWindow` is a component that renders a user-friendly interface for tracking the progress of file uploads. It is an extension of the [FloatingWindow](../floating-window/floating-window.md) component. The functionality includes the following:

- Displays the status of multiple files being uploaded.
- Displays the remaining time for ongoing uploads.
- Allows users to cancel individual or all uploads.
- Allows users to add more files to the queue.

## Detailed Design

The `FileStatusWindow` extends the `FloatingWindow` component. It does not actually handle the uploading of files, nor does it derive the upload status information. That is entirely the responsibility of the consumer.

### Structure:

The `FileStatusWindow` extends/implements the following `FloatingWindow` components:

- **FloatingWindow**  
  Main container component that handles the opening/closing of the window, and manages the window state changes (normal, minimized).
- **FloatingWindowHeader**  
  Renders the title, the close button, and the window state controls for minimizing/restoring the window.
- **FloatingWindowBody**  
  Displays consumer-provided file uploads as individual `FileStatus` components.
- **FloatingWindowFooter**  
  Contains a customizable action button for adding additional files to the queue.

Other components:

- **FileStatusList**: A component that accepts and iterates over an array of file objects (`IFileStatusDataItem[]`) which describe the files being uploaded, along with the actions that can be performed on the files. The file items are individually rendered as `FileStatus` components.
- **FileStatus**: A component to display information about an individual upload. The rendered information includes:
  - The name of the file
  - An icon to visually convey the status of the upload
  - Messaging appropriate for the active status of the upload
  - A progress bar for visually displaying the upload completion as a percentage (uses the existing `Progress` component)
  - Optionally provides one or more action buttons; a typical use case might be a 'Cancel` button for a queued file and a 'Clear' button for a finished file.
- **FileStatusCountdownTimer**: A component to render messaging about the remaining upload time of the active uploads, typically shown below the header. It can also show a markup node for actions next to the messaging; a typical use case might be for rendering a 'Cancel All' button.

### Context and Behavior:

- **Actions**: Users can interact with individual file uploads (e.g., cancel, clear, view, etc). The consumer provides information such as labels, icons, and callbacks to invoke for the actions. The information that describes the actions must be in the form of `IButtonProps` or `IIconButtonProps`.

## API

### FileStatusWindow, FileStatusWindowHeader, FileStatusWindowBody, FileStatusWindowFooter

These components are exported aliases of `FloatingWindow` components. See [FloatingWindow](../floating-window/floating-window.md) for the full API.

### FileStatusList

1. **id**: `string`
   A unique identifier.
1. **testId?**: `string`
   Optional. Sets the value for the `data-testid` attribute. Used for testing.
1. **files**: `IFileStatusDataItem[]`
   An array of `IFileStatusDataItem` objects, to be rendered as a list of `FileStatus` components.

```typescript
export type TFileStatusDataItemStatus = 'queued' | 'uploading' | 'success' | 'error' | 'cancelled';

export interface IFileStatusDataItem {
  id: string;
  testId?: string;
  name: string;
  progressPercentage: number;
  status: TFileStatusDataItemStatus;
  statusMessage: string;
  actions?: (IButtonProps | IIconButtonProps)[];
}
```

### FileStatus

1. **id**: `string`  
   A unique identifier for each file upload.
1. **testId?**: `string`  
   Optional. Sets the value for the `data-testid` attribute. Used for testing.
1. **className**: `string`  
   A CSS class that applies rounded corners, for when the component is rendered outside of the context of a `FileStatusWindow`. By default there will be no `border-radius` applied.
1. **name**: `string`  
   The name of the file, including the extension (e.g., "file1.txt").
1. **progressPercentage**: `number`  
   The current progress of the file upload, expressed as percentage (0-100).
1. **status:**: `TFileStatusDataItemStatus`  
   The current status of the file upload.
1. **statusMessage**: `string`  
   The status message of the file upload.
1. **actions?**: `undefined | ReactNode`  
   Optional. A `ReactNode` generated by the callee which typically includes one or more action buttons.

### FileStatusCountdownTimer

1. **id**: `string`  
   A unique identifier for the component.
1. **testId?**: `string`  
   Optional. Sets the value for the `data-testid` attribute. Used for testing.
1. **label**: `string`  
   The message shown for the estimated time to complete all of the in-progress uploads.
1. **action?**: `undefined | ReactNode`  
   Optional. A `ReactNode` rendered next to the remaining time; typical usage would be a `Cancel All` button.

## Usage

Here's an example of using the `FileStatusWindow`:

```jsx
// This is the recommended structure of the data; the consumer is responsible for any real-time updates.
const files = [
  {
    id: 'file-1',
    name: 'file1.txt',
    progressPercentage: 0,
    status: 'queued',
    statusMessage: 'Queued for upload • 500 bytes',
    actions: [
      {
        id: 'file-1-action-cancel',
        label: 'Cancel',
        onClick: () => console.log('Cancel'),
        variant: 'secondary',
        size: 'small',
      },
    ],
  },
  {
    id: 'file-2',
    name: 'file2.jpg',
    progressPercentage: 68,
    status: 'uploading',
    statusMessage: 'Uploading • 130 bytes / 1.5 KB',
    actions: [
      {
        id: 'file-2-action-cancel',
        label: 'Cancel',
        variant: 'secondary',
        size: 'small',
        onClick: () => console.log('Cancel'),
      },
    ],
  },
  {
    id: 'file-3',
    name: 'file3.doc',
    progressPercentage: 100,
    status: 'success',
    statusMessage: 'Success • 1523 bytes',
    actions: [
      {
        id: 'file-3-action-view-entries',
        label: 'View Entries',
        variant: 'secondary',
        size: 'small',
        onClick: () => console.log('View Entries'),
      },
    ],
  },
  {
    id: 'file-4',
    name: 'file4.csv',
    progressPercentage: 100,
    status: 'success',
    statusMessage: 'Importing',
    actions: [
      {
        id: 'file-4-action-view',
        ariaLabel: 'View',
        iconName: 'view',
        size: 'small',
        variant: 'tertiary',
        onClick: () => console.log('View'),
      },
      {
        id: 'file-4-action-download',
        ariaLabel: 'Download',
        iconName: 'import',
        size: 'small',
        variant: 'tertiary',
        onClick: () => console.log('Download'),
      },
    ],
  },
  {
    id: 'file-5',
    name: 'file5.pdf',
    progressPercentage: 0,
    status: 'error',
    statusMessage: 'Upload failed',
    actions: [
      {
        id: 'file-5-action-clear',
        label: 'Clear',
        variant: 'secondary',
        size: 'small',
        onClick: () => console.log('Clear'),
      },
    ],
  },
];
  const textMap: {
    closeButtonAriaLabel: 'Close',
    minimizeButtonAriaLabel: 'Minimize',
    restoreButtonAriaLabel: 'Restore',
    countdownTimerText: '5 min left...',
    addFilesButtonText: 'Add Files',
    headerText: `Uploading 2 files`,
  },
  const [open, setOpen] = useState(false);
  const [windowState, setWindowState] = useState('normal');
  const handleOpen = useCallback(() => {
    setOpen(true);
  }, []);
  const handleClose = useCallback((e) => {
    setOpen(false);
  }, []);
  const handleWindowStateChange = (windowState) => {
    setWindowState(windowState);
  };

  <FileStatusWindow
    id="file-status-windows-1"
    open={open}
    windowState={windowState}
    onOpen={handleOpen}
    onClose={handleClose}
    onWindowStateChange={handleWindowStateChange}
    width="375px"
    ariaLabelledBy="file-status-window-header-id"
    ariaDescribedBy="file-status-window-body-id"
  >
  <FileStatusWindowHeader
    id="file-status-window-header-id"
    textMap={{
      closeButtonAriaLabel: textMap.closeButtonAriaLabel,
      minimizeButtonAriaLabel: textMap.minimizeButtonAriaLabel,
      restoreButtonAriaLabel: textMap.restoreButtonAriaLabel,
    }}
    onCloseButtonClick={() => setOpen(false)}
  >
    {textMap.headerText}
  </FileStatusWindowHeader>
  <FileStatusCountdownTimer
    id="file-status-countdown-timer"
    label={textMap.countdownTimerText }
    actions={<Button id="cancel-all-button" variant="tertiary" label="Cancel All" size="small" onClick={() => console.log('Cancel all uploads.')} />}
  />
  {windowState === 'normal' && (
    <>
      <FileStatusWindowBody>
        <FileStatusList files={files} id="file-status-window-files" />
      </FileStatusWindowBody>
      <FileStatusWindowFooter id="file-status-window-body-id">
        <Button id="add-files-button" label="Add files" size="small" onClick={() => console.log('Add files')} />
      </FileStatusWindowFooter>
    </>
  )}
</FileStatusWindow>;
```

## Accessibility

- All interactive elements such as the Cancel and Cancel All action buttons are focusable and navigable with the keyboard.
- `FileStatusWindow` is completely keyboard navigable, leveraging existing a11y-aware components.
- The progress bars are implemented with the [`Progress`](../progress/progress.md) component, inheriting already-established accessible behaviours.
- Uses clear and concise status messages, contrasting colors, and sufficient font sizes for readability.
- Uses ARIA attributes (e.g., aria-live, aria-describedby) to convey information to screen readers.

## Future Considerations

- Drag-and-Drop Support: Consider adding drag-and-drop functionality for file uploads to improve the user experience.

## Other Design Systems

- Ant Design - https://ant.design/components/upload
- Carbon - https://react.carbondesignsystem.com/?path=/docs/components-fileuploader--overview
- Spectrum- https://opensource.adobe.com/spectrum-web-components/components/dropzone/

## Required PBIs

- [PWEB-19043](https://dayforce.atlassian.net/browse/PWEB-19043) [FileStatusWindow] - Architecture
- [PWEB-20252](https://dayforce.atlassian.net/browse/PWEB-20252): [FileStatusWindow] [Development] Create component
- [PWEB-20262](https://dayforce.atlassian.net/browse/PWEB-20262): [Progress] [Development] Add customization to allow value to be displayed to the right of the bar
- [PWEB-20253](https://dayforce.atlassian.net/browse/PWEB-20253): [FileStatusWindow] [Development] A11y
- [PWEB-20254](https://dayforce.atlassian.net/browse/PWEB-20254): [FileStatusWindow] [Development] Storybook Documentation
- [PWEB-20255](https://dayforce.atlassian.net/browse/PWEB-20255): [FileStatusWindow] [Development] Tests: P11y, Manual, Visual tests, Unit tests, playwright tests
- [PWEB-20261](https://dayforce.atlassian.net/browse/PWEB-20261): [FileStatusWindow] [Development] Add to Ref App
- [PWEB-20338](https://dayforce.atlassian.net/browse/PWEB-20338): [FileStatusWindow] [Development] Mark as ready for production

## Q&A

**Can the File Status Window handle multiple file uploads?**  
Yes, it is designed to support the display of statuses for multiple files simultaneously.

**What happens if the file upload fails?**  
It is up to the consumer to report that the upload failed by updating the upload state data. The FileStatus component blindly displays whatever data is passed to it.

**Can I customize the file information displayed?**  
Yes. While a `FileStatus` component exists to render uploaded files, the consumer can opt to use their own markup.
The `FileStatusWindow` component (or more specifically, the `FloatingWindow` component) was designed in such a way to allow the consumers to
render their own markup inside the `children` node of `FileStatusWindowBody`.

**Is it possible to cancel a file upload?**  
Yes, since the `FileStatus` component supports dynamic actions, one of those actions can be cancel operation; also, the `FileStatusWindow` can optionally render a _Cancel All_ button to cancel all active uploads. The consumer must provide the handlers for these operations.

**How is file progress updated?**  
It is entirely the responsibility of the consumer to initiate and monitor all file uploads, and subsequently pass relevant updated information to the `FileStatusWindow`.
One way to approach it might be if the consumer was using [axios-http](https://axios-http.com/) for the uploads, then a handler can be attached to the axios `onUploadProgress` event. The handler would perform some state updates on the progress and status of the uploads, and `FileStatusWindow` would subsequently reflect those updates.

**Is the File Status Window accessible?**  
Yes, the component is designed with accessibility in mind, including support for screen readers, keyboard navigation, and focus management.
This component is primarily built with existing Everest components that have already gone through the rigors of accessibility testing.

**When would the countdown timer be displayed?**  
The countdown timer would typically be shown when there are active uploads. It offers the consumer an ideal way to provide a means of cancelling active downloads via a 'Cancel All' action, among other possible actions. Ideally it would be visible when the window is both open and minimized but ultimately it is up to the consumer.

**When would a `FileStatus` status component be rendered outside of the context of a `FileStatusWindow`?**  
The stand-alone variant is suitable for upload/import activities that are decentralized (can be initiated from multiple local contexts), one-off, and/or are low-volume. For example, clicking "Add resume" button on a form to upload a resume.

## Changelog

2025-03-20: PWEB-19043 - Creation of the Architecture Document for FileStatusWindow component.
