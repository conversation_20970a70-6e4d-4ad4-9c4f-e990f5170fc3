.evrFileStatusCountdownTimer {
  display: flex;
  padding: var(--evr-spacing-2xs) var(--evr-spacing-sm);
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  background-color: var(--evr-surfaces-tertiary-default);
}

.evrFileStatusWindowFiles {
  list-style: none;
  padding: 0;
  margin: 0;
  overscroll-behavior: contain;
}

.evrFileStatus {
  display: flex;
  align-items: flex-start;
  align-self: stretch;
  padding: var(--evr-spacing-xs) var(--evr-spacing-sm);
  border-block: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
  border-inline-start: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
  background-color: var(--evr-surfaces-primary-default);

  &:hover {
    background: var(--evr-surfaces-primary-hovered);
  }

  &.evrRoundedCorners {
    border-radius: var(--evr-radius-2xs);
  }

  & .evrFileStatusContentContainer {
    display: flex;
    align-items: flex-start;
    gap: var(--evr-spacing-2xs);
    flex: 1 0 0;
  }

  & .evrFileStatusInfoContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: var(--evr-spacing-3xs);
    flex: 1 0 0;
  }

  & .evrContentRow1 {
    display: flex;
    align-items: center;
    gap: var(--evr-spacing-2xs);
    align-self: stretch;
  }

  & .evrContentRow2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: var(--evr-spacing-2xs);
    align-self: stretch;
  }

  & .evrProgressBar {
    display: flex;
    align-items: center;
    gap: var(--evr-spacing-2xs);
    align-self: stretch;
  }

  & .evrActionButtonsContainer {
    display: flex;
    align-items: flex-start;
    gap: var(--evr-spacing-2xs);
  }

  & .evrFileName {
    flex: 1 0 0;
  }
}
