import { useCallback, useRef, useState } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Button } from '../button';
import { Icon } from '../icon';
import {
  FileStatusWindow,
  FileStatusWindowBody,
  FileStatusList,
  FileStatusWindowFooter,
  FileStatusWindowHeader,
  FileStatusCountdownTimer,
} from '.';
import { action } from '@storybook/addon-actions';

<Meta
  title="Everest Labs/Components/FileStatusWindow"
  component={FileStatusWindow}
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/e8eVNiZs3CtcHJfwQIlKk7/Patterns-Documentation-for-Designers?node-id=2001-19567&node-type=canvas&t=zh9kmJXLjxPkc5ue-0',
    },
  }}
  args={{
    id: 'file-status-window-id',
    testId: 'file-status-window-testid',
    open: false,
    windowState: 'normal',
    width: '375px',
    ariaLabelledBy: 'file-status-window-header-id',
    ariaDescribedBy: 'file-status-window-body-id',
  }}
/>

# FileStatusWindow

## Live Demo

<Canvas>
  <Story name="FileStatusWindow">
    {(args) => {
      const files = [
        {
          id: 'file-1',
          name: 'readme.txt',
          progressPercentage: 0,
          status: 'queued',
          statusMessage: 'Queued for upload, 5 MB',
          actions: [
            {
              id: 'file-1-action-cancel',
              label: 'Cancel',
              onClick: () => console.log('Cancel'),
              variant: 'secondary',
              size: 'small',
            },
          ],
        },
        {
          id: 'file-2',
          name: 'profile-avatar.png',
          progressPercentage: 68,
          status: 'uploading',
          statusMessage: 'Uploading, 130 bytes of 5 MB',
          actions: [
            {
              id: 'file-2-action-cancel',
              label: 'Cancel',
              variant: 'secondary',
              size: 'small',
              onClick: () => console.log('Cancel'),
            },
          ],
        },
        {
          id: 'file-3',
          name: 'Resume-12.docx',
          progressPercentage: 100,
          status: 'success',
          statusMessage: 'Success, uploaded 5 MB',
          actions: [
            {
              id: 'file-3-action-view-entries',
              label: 'View Entries',
              variant: 'secondary',
              onClick: () => console.log('View Entries'),
            },
          ],
        },
        {
          id: 'file-4',
          name: 'Sales-CA.csv',
          progressPercentage: 100,
          status: 'success',
          statusMessage: '200 entries imported',
          actions: [
            {
              id: 'file-4-action-view',
              ariaLabel: 'View',
              iconName: 'view',
              variant: 'tertiary',
              onClick: () => console.log('View'),
            },
            {
              id: 'file-4-action-download',
              ariaLabel: 'Download',
              iconName: 'import',
              variant: 'tertiary',
              onClick: () => console.log('Download'),
            },
          ],
        },
        {
          id: 'file-5',
          name: 'first-draft.pdf',
          progressPercentage: 0,
          status: 'error',
          statusMessage: 'Upload failed',
          actions: [
            {
              id: 'file-5-action-clear',
              label: 'Clear',
              variant: 'secondary',
              onClick: () => console.log('Clear'),
            },
          ],
        },
        {
          id: 'file-6',
          name: 'file6.docx',
          progressPercentage: 0,
          status: 'cancelled',
          statusMessage: 'Cancelled',
        },
      ];
      const [open, setOpen] = useState(false);
      const [windowState, setWindowState] = useState('normal');
      const triggerButtonRef = useRef(null);
      const handleOpen = useCallback(() => {
        action('onOpen')();
      }, []);
      const handleClose = useCallback((e) => {
        setOpen(false);
        triggerButtonRef?.current?.focus();
        action('onClose')();
      }, []);
      const handleWindowStateChange = (windowState) => {
        setWindowState(windowState);
        action('onWindowStateChange')(windowState);
      };
      const textMap = {
        closeButtonAriaLabel: 'Close file status window',
        minimizeButtonAriaLabel: 'Minimize file status window',
        restoreButtonAriaLabel: 'Restore file status window',
        countdownTimerText: '5 min left...',
        addFilesButtonText: 'Add Files',
        headerText: `Uploading 2 files`,
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'center ' }}>
          <Button ref={triggerButtonRef} id="trigger-button-id" label="Click to toggle FileStatusWindow" onClick={() => setOpen(!open)} />
          <FileStatusWindow {...args}
            testId={args.testId}
            id={args.id}
            open={open}
            onOpen={handleOpen}
            onClose={handleClose}
            windowState={windowState}
            onWindowStateChange={handleWindowStateChange}
            width={args.width}
            ariaLabelledBy={args.ariaLabelledBy}
            ariaDescribedBy={args.ariaDescribedBy}
          >
            <FileStatusWindowHeader
              id={args.ariaLabelledBy}
              testId={`${args.testId}-header`}
              textMap={{ 
                closeButtonAriaLabel: textMap.closeButtonAriaLabel,
                minimizeButtonAriaLabel: textMap.minimizeButtonAriaLabel,
                restoreButtonAriaLabel: textMap.restoreButtonAriaLabel,
              }}
              onCloseButtonClick={() => setOpen(false)}
            >
              { textMap.headerText }
            </FileStatusWindowHeader>
            <FileStatusCountdownTimer
              id="countdown-timer-id"
              testId="countdown-timer-testid"
              label={ textMap.countdownTimerText }
              actions={<Button id="cancel-all-button" variant="tertiary" label="Cancel All" size="small" onClick={() => console.log('Cancel all uploads.')}/>}
            />
            {windowState === 'normal' && (
              <>
                <FileStatusWindowBody>
                  <FileStatusList files={files} />
                </FileStatusWindowBody>
                <FileStatusWindowFooter id="floating-window-footer-id" testId={`${args.testId}-footer`}>
                  <div style={{
                    display: 'flex',
                    padding: 'var(--evr-spacing-sm) var(--evr-spacing-sm)',
                    flexDirection: 'row',
                    justifyContent: 'flex-end'
                  }}>
                    <Button id="add-files-button" label={textMap.addFilesButtonText} size="small" onClick={() => console.log('Add files')} />
                  </div>
                </FileStatusWindowFooter>
              </>
            )}
          </FileStatusWindow>
        </div>
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="FileStatusWindow" />
