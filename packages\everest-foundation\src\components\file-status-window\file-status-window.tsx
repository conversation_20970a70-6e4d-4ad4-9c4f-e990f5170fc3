import React, { forwardRef, PropsWithChildren } from 'react';

import { FloatingWindow, IFloatingWindow } from '../floating-window';

export type TFileStatusWindowState = 'normal' | 'minimized';

export interface IFileStatusWindowProps extends Omit<IFloatingWindow, 'placement' | 'maxHeight' | 'windowState'> {
  /**
   * The window state of the file status window.
   * @default normal
   */
  windowState: TFileStatusWindowState;
}

export const FileStatusWindow = forwardRef<HTMLDialogElement, PropsWithChildren<IFileStatusWindowProps>>(
  (props, ref): JSX.Element => {
    const { children, windowState = 'normal', ...rest } = props;
    return (
      <FloatingWindow {...rest} placement="bottomRight" maxHeight="420px" windowState={windowState} ref={ref}>
        {children}
      </FloatingWindow>
    );
  }
);

FileStatusWindow.displayName = 'FileStatusWindow';
