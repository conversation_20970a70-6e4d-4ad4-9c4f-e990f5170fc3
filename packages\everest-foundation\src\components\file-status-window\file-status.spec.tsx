import React from 'react';
import { render, screen } from '@testing-library/react';

import { FileStatus, IFileStatusProps, TFileStatusDataItemStatus } from './file-status';
import { Button } from '../button';
import { IconButton } from '../icon-button';

describe('[FileStatus]', () => {
  const defaultProps: IFileStatusProps = {
    id: 'file-status-1',
    name: 'example-file.txt',
    status: 'queued',
    statusMessage: 'File is queued for upload',
  };

  it('renders the file name', () => {
    render(<FileStatus {...defaultProps} />);
    expect(screen.getByText('example-file.txt')).toBeInTheDocument();
  });

  it.each([
    ['hourglass', 'queued'],
    ['inProgress', 'uploading'],
    ['approval', 'success'],
    ['error', 'error'],
    ['cancel', 'cancelled'],
  ])('renders the "%s" icon when status=%s ', (icon, status) => {
    const { container } = render(<FileStatus {...defaultProps} status={status as TFileStatusDataItemStatus} />);
    expect(container.querySelector(`svg[data-evr-name="${icon}"]`)).toBeInTheDocument();
  });

  it('renders the status message', () => {
    render(<FileStatus {...defaultProps} />);
    expect(screen.getByText(defaultProps.statusMessage)).toBeInTheDocument();
  });

  it('renders the progress bar when status is uploading', () => {
    render(<FileStatus {...defaultProps} status="uploading" progressPercentage={50} statusMessage="Uploading file" />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText('50%')).toBeInTheDocument();
  });

  it('renders the error message when status is error', () => {
    render(<FileStatus {...defaultProps} status="error" statusMessage="Failed to upload file" />);
    expect(screen.getByText('Failed to upload file')).toBeInTheDocument();
  });

  it('renders action buttons if provided', () => {
    const actions = (
      <>
        <Button id="btnClear" label="Clear" />
        <IconButton iconName={'view'} ariaLabel="View" id="btnView" />
      </>
    );
    render(<FileStatus {...defaultProps} actions={actions} />);
    expect(screen.getByRole('button', { name: /Clear/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /View/i })).toBeInTheDocument();
  });
});
