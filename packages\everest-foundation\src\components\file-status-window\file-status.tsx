import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n, ReactNode } from 'react';
import classnames from 'classnames';

import { Icon, TIconName, TIconColor } from '../icon';
import { Progress } from '../progress';
import { StatusMessage } from '../status-message';

import styles from './file-status-window.module.scss';

export type TFileStatusDataItemStatus = 'queued' | 'uploading' | 'success' | 'error' | 'cancelled';

export interface IFileStatusProps {
  /**
   * A unique identifier.
   */
  id: string;
  /**
   * Optional. Sets **data-testid** attribute on the html element; used for automation testing.
   */
  testId?: string;
  /**
   * The name of the file.
   */
  name: string;
  /**
   * Optional. The progress percentage of the file.
   */
  progressPercentage?: number;
  /**
   * The current status of the file.
   */
  status: TFileStatusDataItemStatus;
  /**
   * A message to display about the current file.
   */
  statusMessage: string;
  /**
   * Optional. An array of action buttons to display.
   */
  actions?: ReactNode;
  /**
   * A flag to force rounded corners; used when the FileStatus component is rendered outside of the context of a file status window.
   */
  roundedCorners?: boolean;
}

const iconMap: { [key in TFileStatusDataItemStatus]: { name: TIconName; fillColor: TIconColor } } = {
  queued: { name: 'hourglass', fillColor: '--evr-content-primary-default' },
  uploading: { name: 'inProgress', fillColor: '--evr-content-primary-default' },
  success: { name: 'approval', fillColor: '--evr-content-status-success-default' },
  error: { name: 'error', fillColor: '--evr-content-status-error-default' },
  cancelled: { name: 'cancel', fillColor: '--evr-content-primary-default' },
};

export const FileStatus = (props: PropsWithChildren<IFileStatusProps>): JSX.Element => {
  const { id, testId, name, progressPercentage, status, statusMessage, actions, roundedCorners = false } = props;

  const getStatusMessage = (status: TFileStatusDataItemStatus) => {
    if (status === 'error') {
      return <StatusMessage visible variant="error" statusMessage={statusMessage} />;
    }
    return <StatusMessage visible variant="default" helperText={statusMessage} />;
  };

  return (
    <div
      id={id}
      data-testid={testId}
      className={classnames(styles.evrFileStatus, { [styles.evrRoundedCorners]: roundedCorners })}
    >
      <div className={styles.evrFileStatusContentContainer}>
        <Icon name={iconMap[status].name} fill={iconMap[status].fillColor} />
        <div className={styles.evrFileStatusInfoContainer}>
          <div className={styles.evrContentRow1}>
            <span className={classnames('evrBodyText2', 'evrBold', styles.evrFileName)}>{name}</span>
            {actions && <div className={styles.evrActionButtonsContainer}>{actions}</div>}
          </div>
          <div className={styles.evrContentRow1}>{getStatusMessage(status)}</div>
          {status === 'uploading' && (
            <div className={styles.evrProgressBar}>
              <Progress
                value={progressPercentage}
                showProgressValueInline={true}
                formatValue={(max, value, roundedValue) => `${roundedValue}%`}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

FileStatus.displayName = 'FileStatus';
