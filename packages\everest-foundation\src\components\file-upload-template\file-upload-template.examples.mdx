import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { FileUploadTemplate } from './file-upload-template';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { AlphaBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';

<AlphaBanner />

export const scope = { FileUploadTemplate };

File Upload Template is designed as a template to customise the <LinkTo kind="Everest Labs/Components/File Dropzone/File Dropzone">File Dropzone</LinkTo> container.

## Basic Usage

The upload icon, text, button label, and caption text may be customized inside the dropzone area using the following props: `uploadIcon`, `primaryText`, `buttonLabel`, and `captionText`.

export const defaultCode = `() => {
  return (
    <FileUploadTemplate
      id='file-upload-template-id' 
      primaryText="Drop files here to Upload, or"
      buttonLabel="Upload" 
      captionText="Max File size is 100MB"
    />
  );
}`;

<CodeExample scope={scope} code={defaultCode} />

## Accessibility

Ensure that File Upload Template is accessible. Add proper `aria` attributes, such as `aria-label`, for the label and browse button, so that screen readers can describe them properly. For example:

```tsx
<FileUploadTemplate
  id="file-upload-template-id"
  primaryText={
    <>
      Drop files here to Upload
      <br />
      or
    </>
  }
  buttonLabel="Upload"
  captionText="Max File size is 100MB"
/>
```
