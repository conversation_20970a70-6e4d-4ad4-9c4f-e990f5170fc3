import { Meta, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { FileUploadTemplate } from './file-upload-template';

<Meta
  title="Testing/Automation Test Cases/File Upload Template"
  component={FileUploadTemplate}
  args={{
    id: 'file-upload-template-id',
    primaryText: (
      <>
        Drop files here to Upload
        <br />
        or
      </>
    ),
    buttonLabel: 'Upload',
    captionText: 'max file size 100MB',
  }}
/>

# File Upload Template

## Live Demo

<Canvas>
  <Story name="Default">{({ ...args }) => <FileUploadTemplate {...args}></FileUploadTemplate>}</Story>
</Canvas>
