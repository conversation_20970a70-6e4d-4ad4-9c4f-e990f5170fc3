import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { FileUploadTemplate } from './file-upload-template';
import Examples from './file-upload-template.examples.mdx';
import { Button } from '@ceridianhcm/components';

<Meta
  title="Everest Labs/Components/File Dropzone/File Upload Template"
  component={FileUploadTemplate}
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/qiYLWZeZQemQVMTZsUoM3A/File-Uploader?node-id=500-27084&t=gLDR8fv5KaOqMXEE-0',
    },
  }}
  args={{
    id: 'file-upload-template-id',
    testId: 'file-upload-template-test-id',
    primaryText: (
      <>
        Drop files here to Upload
        <br />
        or
      </>
    ),
    buttonLabel: 'Upload',
    captionText: 'Max file size 100MB',
  }}
/>

# File Upload Template

<Examples />

## Live Demo

<Canvas>
  <Story name="File Upload Template">{(args) => <FileUploadTemplate {...args} />}</Story>
</Canvas>

<ArgsTable story="File Upload Template" />
