import React, { useContext } from 'react';

import { Button } from '../button';
import { FileDropzoneContext } from '../file-dropzone';
import { Icon, TIconName } from '../icon';

import styles from './file-upload-template.module.scss';

interface IFileUploadTemplate {
  /** Sets the `id` attribute on file upload template. */
  id: string;
  /** Sets a custom upload icon. */
  uploadIcon?: TIconName;
  /** Sets primary text on file upload template. */
  primaryText: React.ReactNode;
  /** Sets a label on the upload button. */
  buttonLabel: string;
  /** Sets caption text on file upload template. */
  captionText?: string;
  /** Sets the `data-testid` attribute on file upload template. */
  testId?: string;
}

export const FileUploadTemplate: React.FC<IFileUploadTemplate> = ({
  id,
  uploadIcon = 'export',
  primaryText,
  buttonLabel,
  captionText,
  testId,
}) => {
  const { openFileExplorer } = useContext(FileDropzoneContext);

  return (
    <div id={id} data-testid={testId} className={styles.evrFileUploadTemplateContainer}>
      <Icon name={uploadIcon} size="md" fill="--evr-content-primary-default" />
      <p className={styles.evrPrimaryText}>{primaryText}</p>
      <Button id={`${id}-file-btn`} variant="secondaryNeutral" label={buttonLabel} onClick={openFileExplorer} />
      <p className={styles.evrCaptionText}>{captionText}</p>
    </div>
  );
};

FileUploadTemplate.displayName = 'FileUploadTemplate';
