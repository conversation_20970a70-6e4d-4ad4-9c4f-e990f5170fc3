import React, { useRef, useState } from 'react';

import { FloatingWindow, IFloatingWindow, TFloatingWindowState } from './floating-window';
import { FloatingWindowBody } from './floating-window-body';
import { FloatingWindowHeader } from './floating-window-header';
import { Button } from '../button';

import { FloatingWindowFooter } from '.';

interface IFloatingWindowCoPilotExample
  extends Omit<IFloatingWindow, 'open' | 'windowState' | 'ariaLabelledBy' | 'ariaDescribedBy'> {
  initialOpen?: boolean;
  initialWindowState?: TFloatingWindowState;
  floatingWindowHeaderId: string;
  floatingWindowBodyId: string;
  floatingWindowFooterId: string;
  contentText: string;
}

export const FloatingWindowCoPilotExample = ({
  testId,
  id,
  initialWindowState = 'normal',
  onWindowStateChange,
  initialOpen = true,
  onOpen,
  onClose,
  placement = 'bottomRight',
  width = '300px',
  maxHeight = '500px',
  floatingWindowHeaderId = 'floating-window-header-id',
  floatingWindowBodyId = 'floating-window-body-id',
  floatingWindowFooterId = 'floating-window-footer-id',
  contentText = 'Find answers and make requests with Co-Pilot!',
}: IFloatingWindowCoPilotExample): JSX.Element => {
  const bodyFooterStyle = {
    padding: 'var(--evr-spacing-2xs) var(--evr-spacing-sm)',
    borderInline: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
  };
  const triggerButtonRef = useRef<HTMLButtonElement>(null);
  const [open, setOpen] = useState(initialOpen);
  const [windowState, setWindowState] = useState<TFloatingWindowState>(initialWindowState);
  const textMap = {
    closeButtonAriaLabel: 'close floating window',
    minimizeButtonAriaLabel: 'minimize floating window',
    restoreButtonAriaLabel: 'restore floating window',
  };
  const handleWindowStateChange = (windowState: TFloatingWindowState) => {
    setWindowState(windowState);
  };
  return (
    <div style={{ display: 'flex', justifyContent: 'center ' }}>
      <Button
        ref={triggerButtonRef}
        id="trigger-button-id"
        label="Click to toggle Floating Window"
        onClick={() => setOpen(!open)}
      />
      <FloatingWindow
        id={id}
        testId={testId}
        open={open}
        onOpen={onOpen}
        onClose={(e) => {
          onClose?.(e);
          setOpen(false);
          triggerButtonRef?.current?.focus();
        }}
        windowState={windowState}
        onWindowStateChange={(windowState) => {
          onWindowStateChange?.(windowState);
          handleWindowStateChange(windowState);
        }}
        placement={placement}
        width={width}
        maxHeight={maxHeight}
        ariaLabelledBy={floatingWindowHeaderId}
        ariaDescribedBy={floatingWindowBodyId}
      >
        <FloatingWindowHeader
          id={floatingWindowHeaderId}
          testId={floatingWindowHeaderId}
          textMap={textMap}
          onCloseButtonClick={() => setOpen(false)}
        >
          {'Co-Pilot'}
        </FloatingWindowHeader>
        {windowState === 'normal' && (
          <>
            <FloatingWindowBody id={floatingWindowBodyId} testId={floatingWindowBodyId}>
              <div className="evrBodyText" style={{ minHeight: '300px', ...bodyFooterStyle }}>
                {contentText}
              </div>
            </FloatingWindowBody>
            <FloatingWindowFooter id={floatingWindowFooterId} testId={floatingWindowFooterId}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  borderBlock: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
                  ...bodyFooterStyle,
                }}
              >
                <Button id="done-button" label="Done" onClick={() => setOpen(false)} />
              </div>
            </FloatingWindowFooter>
          </>
        )}
      </FloatingWindow>
    </div>
  );
};

FloatingWindowCoPilotExample.displayName = 'FloatingWindowCoPilotExample';
