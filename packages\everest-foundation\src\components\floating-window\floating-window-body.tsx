import React, { PropsWithChildren } from 'react';

import { OverlayBody } from '../overlay';

import styles from './floating-window.module.scss';

export interface IFloatingWindowBody {
  id: string;
  testId?: string;
}

export const FloatingWindowBody = React.forwardRef<HTMLElement, PropsWithChildren<IFloatingWindowBody>>(
  (props, ref): JSX.Element => {
    const { id, testId, children } = props;

    return (
      <div className={styles.evrFloatingWindowBody}>
        <OverlayBody id={id} testId={testId} ref={ref}>
          {children}
        </OverlayBody>
      </div>
    );
  }
);

FloatingWindowBody.displayName = 'FloatingWindowBody';
