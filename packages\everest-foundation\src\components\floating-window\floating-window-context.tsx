import React from 'react';

import { TFloatingWindowState } from './floating-window';
import { IFloatingWindowProvider } from './floating-window-provider';

export type IFloatingWindowContext = IFloatingWindowProvider;

export const defaultContext = {
  windowState: 'normal' as TFloatingWindowState,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onWindowStateChange: (windowState: TFloatingWindowState): void => undefined,
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const FloatingWindowContext = React.createContext<IFloatingWindowContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  FloatingWindowContext.displayName = 'FloatingWindowContext';
}
