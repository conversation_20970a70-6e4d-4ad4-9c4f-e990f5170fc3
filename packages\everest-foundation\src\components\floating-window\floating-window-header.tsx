import React, { PropsWithChildren, useContext, useRef } from 'react';

import { FloatingWindowContext } from './floating-window-context';
import { IconButton } from '../icon-button';

import styles from './floating-window.module.scss';

export interface IFloatingWindowHeaderTextMap {
  closeButtonAriaLabel: string;
  minimizeButtonAriaLabel: string;
  restoreButtonAriaLabel: string;
}

export interface IFloatingWindowHeader {
  id: string;
  testId?: string;
  onCloseButtonClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  textMap: IFloatingWindowHeaderTextMap;
}

export const FloatingWindowHeader = React.forwardRef<HTMLDivElement, PropsWithChildren<IFloatingWindowHeader>>(
  (props, ref) => {
    const { windowState, onWindowStateChange } = useContext(FloatingWindowContext);
    const windowStateButtonRef = useRef<HTMLButtonElement>(null);
    const { id, testId, textMap, onCloseButtonClick, children } = props;

    const handleWindowStateChange = () => {
      switch (windowState) {
        case 'normal':
          onWindowStateChange('minimized');
          windowStateButtonRef.current?.focus();
          break;
        case 'minimized':
          onWindowStateChange('normal');
          windowStateButtonRef.current?.focus();
          break;
        default:
          break;
      }
    };

    return (
      <section ref={ref} id={id} data-testid={testId} className={styles.evrFloatingWindowHeader}>
        <h4 className={'evrHeading4'}>{children}</h4>
        <div className={styles.buttonGroup}>
          <IconButton
            ref={windowStateButtonRef}
            id={`${id}-toggle-window-state`}
            iconName={windowState === 'normal' ? 'chevronUp' : 'chevronDown'}
            variant="tertiaryNeutral"
            ariaLabel={windowState === 'normal' ? textMap.minimizeButtonAriaLabel : textMap.restoreButtonAriaLabel}
            onClick={handleWindowStateChange}
          />
          <IconButton
            id={`${id}-close-window`}
            iconName="x"
            variant="tertiaryNeutral"
            ariaLabel={textMap.closeButtonAriaLabel}
            onClick={onCloseButtonClick}
          />
        </div>
      </section>
    );
  }
);

FloatingWindowHeader.displayName = 'FloatingWindowHeader';
