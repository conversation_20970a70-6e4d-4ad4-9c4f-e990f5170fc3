import React, { PropsWithChildren, useMemo } from 'react';

import { IFloatingWindow } from './floating-window';
import { FloatingWindowContext, defaultContext } from './floating-window-context';

export type IFloatingWindowProvider = Omit<
  IFloatingWindow,
  | 'testId'
  | 'id'
  | 'open'
  | 'onOpen'
  | 'onClose'
  | 'placement'
  | 'width'
  | 'height'
  | 'ariaLabelledBy'
  | 'ariaDescribedBy'
>;

export const FloatingWindowProvider = (props: PropsWithChildren<IFloatingWindowProvider>): JSX.Element => {
  const { children, windowState, onWindowStateChange } = props;
  const context = useMemo(
    () => ({
      ...defaultContext,
      windowState,
      onWindowStateChange,
    }),
    [windowState, onWindowStateChange]
  );

  return <FloatingWindowContext.Provider value={context}>{children}</FloatingWindowContext.Provider>;
};

FloatingWindowProvider.displayName = 'FloatingWindowProvider';
