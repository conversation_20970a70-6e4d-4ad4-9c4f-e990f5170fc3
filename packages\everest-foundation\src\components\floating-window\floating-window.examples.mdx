import { useState } from 'react';
import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { FloatingWindow, FloatingWindowBody, FloatingWindowFooter, FloatingWindowHeader } from '.';
import { <PERSON><PERSON> } from '../button';

export const scope = {
  Button,
  FloatingWindow,
  FloatingWindowBody,
  FloatingWindowFooter,
  FloatingWindowHeader,
  useState,
};

The **FloatingWindow** component displays content in a non-modal popup, allowing background windows to remain accessible.

## Using Floating Window

To use the Floating Window, wrap the contents of your dialog with the **FloatingWindow** component. Control its visibility using the `open` prop.

The **FloatingWindow** can be positioned in two locations, either `bottomRight` or `bottomCenter`, controlled by the `placement` prop.

The state of the **FloatingWindow** may be handled using the `windowState` prop, which currently supports two values: `normal` and `minimized`. When `windowState` is set to `normal`, the body and footer of the window should be rendered. Conversely, when `windowState` is set to `minimized`, these elements should be hidden. It is up to the consumer of the **FloatingWindow** to determine how to handle the rendering of content based on the current `windowState`.

The anatomy of the **FloatingWindow** is intended to consist of a header, body and footer. Templates for each subcomponent are provided, however, custom children may also be supplied to the window.

Pressing the <kbd>Escape</kbd> key triggers the `onClose` callback, which sets the open status of the **FloatingWindow** to `false`, closing the window. When activated by the <kbd>Escape</kbd> key, the `onClose` callback receives a reason of `escapedKeydown`. This reason allows the consumer to determine why `onClose` was invoked and to control whether the <kbd>Escape</kbd> key should close the window while also deciding where to place focus.

### Example

Click the below button to have the example **Floating Window** appear.

export const example = `() => {
  const bodyFooterStyle = {
    padding: 'var(--evr-spacing-2xs) var(--evr-spacing-sm)',
    borderInlineEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
    borderBlockEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
    borderInlineStart: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
  };
  const [open, setOpen] = useState(false);
  const [windowState, setWindowState] = useState('normal');
  const triggerButtonRef = useRef(null);
  const textMap = {
    closeButtonAriaLabel: 'close floating window',
    minimizeButtonAriaLabel: 'minimize floating window',
    restoreButtonAriaLabel: 'restore floating window',
  };
  const handleWindowStateChange = (windowState) => {
    setWindowState(windowState);
  };
  return (
    <div style={{ display: 'flex', justifyContent: 'center ' }}>
      <Button ref={triggerButtonRef} id="trigger-button-id" label="Click to toggle Floating Window" onClick={() => setOpen(!open)} />
      <FloatingWindow
        id="floating-window-id"
        open={open}
        windowState={windowState}
        onWindowStateChange={handleWindowStateChange}
        onClose={() => {
          setOpen(false);
          triggerButtonRef?.current?.focus();
        }}
        placement="bottomRight"
        width="300px"
        maxHeight="500px"
        ariaLabelledBy="floating-window-header-id"
        ariaDescribedBy="floating-window-body-id"
      >
        <FloatingWindowHeader
          id="floating-window-header-id"
          textMap={textMap}
          onCloseButtonClick={() => setOpen(false)}
        >
          {'Co-Pilot'}
        </FloatingWindowHeader>
        {windowState === 'normal' && (
          <>
            <FloatingWindowBody id="floating-window-body-id">
              <div className="evrBodyText" style={{ minHeight: '300px', ...bodyFooterStyle }}>
                {'Find answers and make requests with Co-Pilot!'}
              </div>
            </FloatingWindowBody>
            <FloatingWindowFooter id="floating-window-footer-id">
              <div style={{ display: 'flex', justifyContent: 'flex-end', ...bodyFooterStyle }}>
                <Button id="done-button" label="Done" onClick={() => setOpen(false)} />
              </div>
            </FloatingWindowFooter>
          </>
        )}
      </FloatingWindow>
    </div>
  );
}`;

<CodeExample scope={scope} code={example} />
