# Floating Window

## Summary

The Floating Window component can display content in a non-modal popup (allows background windows to remain accessible).

- Start Date: 2024-04-12
- Figma links:
  - https://www.figma.com/file/R6NynrSExN0pYK31CcgJWP/%F0%9F%A7%AA-File-Uploader-%2F-Importer-Component?type=design&node-id=476-37215&mode=design&t=DTv68dyNV3Pwg6WZ-0 (File Uploader)
  - https://www.figma.com/file/1EsMiPSiSQ2xJsyWiPanYi/Co-Pilot-Design-Handoff?type=design&node-id=4427-134090&mode=design&t=ZTZHpieUSfzJXUoj-0 (Co-Pilot)
  - https://www.figma.com/design/Bkay9m2xXxUIc6BDMi4tOr/Everest-Web?node-id=57712-14117&node-type=text&m=dev (Uploader Widget)
  - https://www.figma.com/design/e8eVNiZs3CtcHJfwQIlKk7/Patterns-Documentation-for-Designers?node-id=2001-19567&node-type=canvas&t=BbAe6KMVfymWaB0X-0 (Uploader Widget Pattern Docs)

## Detailed Design

Floating Window anatomy consists of the following and is divided into sections:

## API

### FloatingWindow

1.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the floating window.
1.  **id**: `string`
    Required. Sets the id of the floating window.
1.  **children**: `ReactNode | undefined`
    Optional. Sets the section contents of the floating window.
1.  **windowState**: `TFloatingWindowState`
    Required. Controls the state of the floating window.
1.  **onWindowStateChange**: `() => void | undefined`
    Required. Sets a callback function that is executed when the **windowState** is changed.
1.  **open**: `boolean`
    Show the floating window when it is set to true. Hide the floating window when it is set to false.
1.  **onOpen**: `() => void | undefined`
    Optional. Sets a callback function that is executed when floating window is opened.
1.  **onClose**: `() => void | undefined`
    Optional. Sets a callback function that is executed when floating window is closed.
1.  **placement**: `TFloatingWindowPlacement`
    Optional. Placement of the floating window with respect to the base window. Current options based on Figma docs. Default is `bottomRight`.
1.  **width**: `string`
    Required. Sets the width of the floating window.
1.  **maxHeight**: `string`
    Optional. Sets the max height of the floating window.
1.  **ariaLabelledBy**: `string | undefined`
    Optional. Sets the value for the **aria-labelledby** attribute of the floating window component. Recommended data should be `FloatingWindowHeader` id.
1.  **ariaDescribedBy**: `string | undefined`
    Optional. Sets the value for the **aria-describedby** attribute of the floating window component. Recommneded data should be `FloatingWindowBody` id.

#### TFloatingWindowState (type based on [windows.WindowState - Mozilla | MDN](https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/windows/WindowState))

```typescript
type TFloatingWindowState = 'normal' | 'minimized';
```

Using an enum instead of a boolean as we may need to support `fullscreen` in the future.

#### TFloatingWindowPlacement

Supports the following window placements: `bottomRight` | `bottomCenter`

```typescript
type TFloatingWindowPlacement = Exclude<
  TPlacement,
  | 'top'
  | 'left'
  | 'right'
  | 'bottom'
  | 'topLeft'
  | 'topCenter'
  | 'topRight'
  | 'rightCenter'
  | 'bottomLeft'
  | 'leftCenter'
>;
```

### FloatingWindowHeader (Optional)

1.  **id**: `string`
    Required. Sets the id of the header.
1.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the header.
1.  **onCloseButtonClick**: `() => void | undefined`
    Required. Sets a callback function that is executed on click of the close button.
1.  **textMap**: `IFloatingWindowHeaderTextMap`
    Required. Map of aria labels for floating window header elements.

#### IFloatingWindowHeaderTextMap

1.  **closeButtonAriaLabel**: `string`
    Required. Sets the aria label on the close button.
1.  **minimizeButtonAriaLabel**: `string`
    Required. Sets the aria label on the minimize button.
1.  **restoreButtonAriaLabel**: `string`
    Required. Sets the aria label on the restore button. Required if the floating window includes **minimizeButton**.

### FloatingWindowBody (Optional)

- Alias of **OverlayBody**

1.  **id**: `string`
    Required. Sets the id of the body.
1.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the body.
1.  **children**: `ReactNode | undefined`
    Optional. Sets the content of the floating window.

### FloatingWindowFooter (Optional)

- Alias of **OverlayFooter**

1.  **id**: `string`
    Required. Sets the id of the footer.
1.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the footer.
1.  **children**: `ReactNode | undefined`
    Optional. Sets content of the floating window footer.

## Usage

```typescript
() => {
  const [open, setOpen] = useState(false);
  const [windowState, setWindowState] = useState('normal');
  const textMap = {
    closeButtonAriaLabel: 'close-button',
    minimizeButtonAriaLabel: 'minimize-button',
    restoreButtonAriaLabel: 'restore-button',
  };
  const handleWindowStateChange = (ws) => {
    setWindowState(ws);
  };
  const handleClose = () => {
    setOpen(false);
  };
  return (
    <FloatingWindow
      id="floating-window-id"
      open={open}
      onClose={handleClose}
      windowState={windowState}
      onWindowStateChange={handleWindowStateChange}
      placement="bottomRight"
      width="200px"
      maxHeight="400px"
      ariaLabelledBy="floating-window-header-id"
      ariaDescribedBy="floating-window-body-id"
    >
      <FloatingWindowHeader id="floating-window-header-id" textMap={textMap} onCloseButtonClick={handleClose}>
        {'Co-Pilot'}
      </FloatingWindowHeader>
      <FloatingWindowBody id="floating-window-body-id">
        <div> Find answers and make requests with Co-Pilot! </div>
      </FloatingWindowBody>
      <FloatingWindowFooter id="floating-window-footer-id">
        <Button id="done-button" label="Done" />
      </FloatingWindowFooter>
    </FloatingWindow>
  );
};
```

## Dev Implementation Notes

- Context will be needed to pass information such as `TFloatingWindowState` between components
- Use `<Overlay>` without `fullscreen` and `lightboxVariant` (refer to `<SidePanel>` implementation)
- Create `FloatingWindowHeader` component that passes in minimize, fullscreen, restore buttons as children to `<OverlayHeader>`
  - Header will not receive a `title` prop; it will instead accept a title as children allowing for greater customization of the header
- Use `<OverlayBody>`, `<OverlayFooter>` for `<FloatingWindowBody>`, `<FloatingWindowBody>`
- **Floating Window** can accept any children, there will be no constraints - only templates
- Modify z-index
- Support `placement` prop instead of using `anchor`
- Handle header button visibility internally based on buttons and `windowState` props

## Accessibility

- The floating window component will use `aria-labelledBy` and `aria-described` on the container
- Screenreaders by default will announce the heading and content text when focused.
- <ESC> keyboard button is set to close the floating window.
- Check with a11y if the following features are required for the floating window (https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/dialog_role)
  - When a floating window is opened, focus should be moved to the floating window's default focusable control
  - After a floating window is closed, focus should be moved back to where it was before it moved into the floating window. Otherwise the focus can be moved to the beginning of the page
  - Consider adding a global keyboard shortcut that allows focus to be moved between floating window and the main page
- If a floating window is site/automatically activated, consider using `aria-live="polite"` for screenreaders (https://accessuse.eu/en/non-modal-dialogs.html)

## Q&A

**Does the floating window support drag/move?**
No. Currently, `placement`, `height`, `width` props of the floating window can be set, but it cannot be dragged nor moved. Further, the `FloatingWindowHeader` supports a minimize and restore button.

**What placements need to be supported for the floating window?**  
Currently, the window placements required to support the designs are `bottomRight` and `bottomCenter`.

**What is the z-index of the floating window? How can it be used in relation to a Modal?**  
~~As per design, Modals should supercede floating windows in terms of Z-index because it is designed to have an inactive background. Thus, the floating window requires the creation of a new Z-index token to reflect this.~~
The floating window will use the same token that is used for modal to ensure inputs function properly within the floating window.

## Other Design Systems

**Material UI** - https://mui.com/material-ui/react-dialog/#non-modal-dialog

- Has `fullscreen` and `maxWidth` props for dialogs, or can override styling with `sx` prop

**Kendo React** - https://www.telerik.com/kendo-react-ui/components/dialogs/window/

- API:
  - https://www.telerik.com/kendo-react-ui/components/dialogs/api/WindowProps/
  - Users can input `width` and `top`, `left` props to specify the coordinates of the window
  - https://www.telerik.com/kendo-react-ui/components/dialogs/api/DialogProps/
- Uses `minimizeButton`, `stage`, `onStageChange` to handle button configurations in different window stages

**Microsoft Fluent UI** - https://react.fluentui.dev/?path=/docs/components-dialog--default#non-modal

- Uses `modalType` to differentiates between 'modal' | 'non-modal' | 'alert'
- Can specify `action` in `DialogTitle` to add custom header buttons/actions

**Mews Design** - https://www.mews.design/latest/components/dialog/react-1QtP5JQP

- Has a `position` prop with 'aside' | 'center' | 'corner' and a `size` prop for width

## Required PBIs

- Create Floating Window Component (implementation, tests, ref app)

## Acceptance Criteria

- Component to be named `<FloatingWindow>`
- Build a component and setup in Storybook (Components, Foundations, Automation)
- Default template will be the File Uploader style
- Verify the following:
  - API works as intended (position, Header buttons, Footer buttons, etc.)
  - Accessibility (aria attributes, tab order, screen reader callouts, mouse, and keyboard interactivity)
  - Background window is still accessible
  - compare z-index with Modal and other relevant components
  - Input components work properly within FloatingWindow (dropdown, combobox, etc.)
- Unit and integration tests implemented

## Changelogs

04/11/2024 - Creating arch doc for Floating Window
10/09/2024 - Updating arch docs based on new Figma files
