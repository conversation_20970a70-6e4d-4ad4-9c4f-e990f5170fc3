@use '../../variables.scss';

.evrFloatingWindow {
  inset-block-end: 0;

  &.bottomRight {
    inset-inline-end: var(--evr-spacing-md);
  }

  .evrFloatingWindowDialog {
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 0;
    margin: 0;
    width: 100%;
    height: 100%;
    border: none;
    background-color: var(--evr-surfaces-primary-default);
    border-radius: var(--evr-radius-2xs) var(--evr-radius-2xs) 0 0;
    overflow: hidden;

    &:focus {
      outline: none; //removes outline around dialog that appears from keystrokes while dialog is focused, such as esc to close
    }
  }

  .evrFloatingWindowHeader {
    display: flex;
    padding: var(--evr-spacing-2xs) var(--evr-spacing-sm);
    align-items: center;
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
    background: var(--evr-surfaces-secondary-default);
    gap: var(--evr-spacing-2xs);

    .buttonGroup {
      display: flex;
      align-items: center;
      gap: var(--evr-spacing-2xs);
      margin-inline-start: auto;
    }
  }

  .evrFloatingWindowBody {
    overflow-y: auto;
    flex: 0 1 auto;
  }
}
