import { useState } from 'react';
import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { FloatingWindow, FloatingWindowHeader, FloatingWindowBody, FloatingWindowFooter } from '.';
import { Chromatic } from '../../../chromatic';
import { Button } from '../button';
import { FloatingWindowCoPilotExample } from './FloatingWindowCoPilotExample';

<Meta
  title="Testing/Automation Test Cases/Floating Window"
  component={FloatingWindow}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'floating-window-id',
    testId: 'floating-window-testid',
  }}
/>

# Floating Window

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => {
      return <FloatingWindowCoPilotExample id={args.id} testId={args.testId} />;
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Placement bottomCenter">
    {(args) => {
      return <FloatingWindowCoPilotExample id={args.id} testId={args.testId} placement="bottomCenter" />;
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Long content text">
    {(args) => {
      return (
        <FloatingWindowCoPilotExample
          id={args.id}
          testId={args.testId}
          placement="bottomCenter"
          contentText={
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur dignissim pharetra turpis, non vulputate turpis pellentesque at. Fusce vel ligula a libero volutpat fermentum. Proin varius orci sit amet suscipit rutrum. Curabitur blandit eleifend nisi, nec molestie dolor efficitur sit amet. Sed ut convallis nisi. Integer convallis turpis sit amet libero semper fermentum. Nunc lobortis efficitur arcu ut tincidunt. Interdum et malesuada fames ac ante ipsum primis in faucibus. Donec in nulla eu mi feugiat feugiat. Praesent eu risus ullamcorper, iaculis risus elementum, viverra felis. Aenean facilisis tempor turpis quis tempus. Sed auctor elementum tortor eget finibus. Suspendisse iaculis odio ut velit rhoncus, non sagittis quam eleifend. Nulla ut arcu a tellus tempus posuere quis eu justo. Morbi nunc lacus, commodo at semper eget, molestie non lacus. Nulla dui est, molestie vitae mauris in, mollis finibus felis. Praesent vehicula viverra sapien. Vestibulum non quam pellentesque, pharetra ligula quis, finibus mi. Proin fermentum finibus lacus sed ullamcorper. In hac habitasse platea dictumst. Nunc tincidunt libero sit amet libero aliquam mattis. Integer sit amet consectetur leo. Proin dictum, nulla eget consectetur ornare, elit enim porttitor velit, sed porttitor elit dui eget mi. Proin porttitor, velit ut fermentum laoreet, massa nibh pharetra urna, quis fermentum justo felis sed ex. Sed maximus diam quis massa fermentum rhoncus.'
          }
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => {
      return (
        <FloatingWindowCoPilotExample id={args.id} testId={args.testId} initialOpen={false} placement="bottomCenter" />
      );
    }}
  </Story>
</Canvas>
