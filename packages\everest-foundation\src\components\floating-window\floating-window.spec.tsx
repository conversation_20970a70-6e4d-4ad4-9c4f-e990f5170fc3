import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Button } from '../button';

import {
  IFloatingWindow,
  FloatingWindow,
  FloatingWindowBody,
  FloatingWindowFooter,
  FloatingWindowHeader,
  TFloatingWindowState,
  TFloatingWindowPlacement,
} from '.';

const floatingWindowId = 'floating-window-id';
const floatingWindowHeaderId = 'floating-window-header-id';
const floatingWindowBodyId = 'floating-window-body-id';
const floatingWindowFooterId = 'floating-window-footer-id';

const onOpen = jest.fn();
const onClose = jest.fn();
const onWindowStateChange = jest.fn();

const getFloatingWindow = () => screen.getByTestId(floatingWindowId);
const getHeaderCloseButton = () => screen.getByLabelText('close floating window');
const getMinimizeWindowStateButton = () => screen.getByLabelText('minimize floating window');
const getRestoreWindowStateButton = () => screen.getByLabelText('restore floating window');

const mockProps: IFloatingWindow = {
  id: floatingWindowId,
  testId: floatingWindowId,
  open: true,
  onOpen: onOpen,
  onClose: onClose,
  windowState: 'normal',
  onWindowStateChange: onWindowStateChange,
  ariaLabelledBy: floatingWindowHeaderId,
  ariaDescribedBy: floatingWindowBodyId,
  placement: 'bottomRight',
  width: '375px',
  maxHeight: '500px',
};

const renderFloatingWindow = (
  open: boolean,
  windowState: TFloatingWindowState,
  placement: TFloatingWindowPlacement
) => {
  const textMap = {
    closeButtonAriaLabel: 'close floating window',
    minimizeButtonAriaLabel: 'minimize floating window',
    restoreButtonAriaLabel: 'restore floating window',
  };
  return (
    <FloatingWindow {...mockProps} open={open} windowState={windowState} placement={placement}>
      <FloatingWindowHeader id={floatingWindowHeaderId} textMap={textMap} onCloseButtonClick={onClose}>
        {'Co-Pilot'}
      </FloatingWindowHeader>
      {windowState === 'normal' && (
        <>
          <FloatingWindowBody id={floatingWindowBodyId}>
            <div className="evrBodyText" style={{ minHeight: '300px' }}>
              {'Find answers and make requests with Co-Pilot!'}
            </div>
          </FloatingWindowBody>
          <FloatingWindowFooter id={floatingWindowFooterId}>
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button id="done-button" label="Done" onClick={onClose} />
            </div>
          </FloatingWindowFooter>
        </>
      )}
    </FloatingWindow>
  );
};

describe('Floating Window', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('should call onOpen or onClose as expected', () => {
    it('should run onOpen once and onClose once when starting closed, then opened, then closed', async () => {
      const { rerender } = render(renderFloatingWindow(false, 'normal', 'bottomRight'));
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderFloatingWindow(true, 'normal', 'bottomRight'));
      await waitFor(() => expect(onOpen).toHaveBeenCalledTimes(1));
      rerender(renderFloatingWindow(false, 'normal', 'bottomRight'));
      await waitFor(() => expect(onClose).toHaveBeenCalledTimes(1));
    });

    it('should run onOpen once and onClose once when starting open, then closed', async () => {
      const { rerender } = render(renderFloatingWindow(true, 'normal', 'bottomRight'));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderFloatingWindow(false, 'normal', 'bottomRight'));
      await waitFor(() => {
        expect(onClose).toHaveBeenCalledTimes(1);
        expect(onOpen).toHaveBeenCalledTimes(1);
      });
    });

    it('should run onClose once upon clicking header close button', async () => {
      render(renderFloatingWindow(true, 'normal', 'bottomRight'));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
      expect(onClose).not.toHaveBeenCalled();
      await userEvent.click(getHeaderCloseButton());
      await waitFor(() => {
        expect(onClose).toHaveBeenCalledTimes(1);
        expect(onOpen).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('should trigger onWindowStateChange as expected', () => {
    it('should run onWindowStateChange with minimized when minimize button clicked and windowState is currently normal', async () => {
      render(renderFloatingWindow(true, 'normal', 'bottomRight'));
      await userEvent.click(getMinimizeWindowStateButton());
      await waitFor(() => expect(onWindowStateChange).toHaveBeenCalledTimes(1));
      const onWindowStateChangeArgs = onWindowStateChange.mock.calls[0];
      expect(onWindowStateChangeArgs[0]).toBe('minimized');
    });

    it('should run onWindowStateChange with normal when restore button clicked and windowState is currently minimized', async () => {
      render(renderFloatingWindow(true, 'minimized', 'bottomRight'));
      await userEvent.click(getRestoreWindowStateButton());
      await waitFor(() => expect(onWindowStateChange).toHaveBeenCalledTimes(1));
      const onWindowStateChangeArgs = onWindowStateChange.mock.calls[0];
      expect(onWindowStateChangeArgs[0]).toBe('normal');
    });
  });

  describe('should render aria attributes', () => {
    it('should render aria-label when label not present', () => {
      render(renderFloatingWindow(true, 'normal', 'bottomRight'));
      expect(getFloatingWindow()).toHaveAttribute('aria-labelledby', floatingWindowHeaderId);
      expect(getFloatingWindow()).toHaveAttribute('aria-describedby', floatingWindowBodyId);
    });
  });
});
