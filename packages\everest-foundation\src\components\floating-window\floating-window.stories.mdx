import { useCallback, useRef, useState } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Button } from '../button';
import { Icon } from '../icon';
import { FloatingWindow, FloatingWindowBody, FloatingWindowFooter, FloatingWindowHeader } from '.';
import Examples from './floating-window.examples.mdx';
import { action } from '@storybook/addon-actions';
import { useArgs } from '@storybook/client-api';

<Meta
  title="Components/Floating Window"
  component={FloatingWindow}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/e8eVNiZs3CtcHJfwQIlKk7/Patterns-Documentation-for-Designers?node-id=2001-19567&node-type=canvas&t=zh9kmJXLjxPkc5ue-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Id for the `FloatingWindow`.',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets **data-testid** attribute on the floating window.',
    },
    windowState: {
      type: 'enum',
      control: 'inline-radio',
      options: ['normal', 'minimized'],
      description: 'Controls the state of the floating window.',
    },
    open: {
      type: 'boolean',
      control: 'boolean',
      description: 'Show the floating window when it is set to true. Hide the floating window when it is set to false.',
    },
    onOpen: {
      control: '-',
      description: 'Sets a callback function that is executed when floating window is opened.',
    },
    onClose: {
      control: '-',
      description: 'Sets a callback function that is executed when floating window is closed.',
    },
    onWindowStateChange: {
      control: '-',
      description: 'Sets a callback function that is executed when the **windowState** is changed.',
    },
    placement: {
      type: 'enum',
      control: 'inline-radio',
      options: ['bottomRight', 'bottomCenter'],
      description: 'Placement of the floating window with respect to the base window.',
      table: {
        defaultValue: { summary: 'bottomRight' },
      },
    },
    width: {
      type: 'string',
      control: 'text',
      description: 'Sets the width of the floating window.',
    },
    maxHeight: {
      type: 'string',
      control: 'text',
      description: 'Sets the maxHeight of the floating window.',
    },
    ariaLabelledBy: {
      type: 'string',
      control: 'text',
      description:
        'Sets the value for the **aria-labelledby** attribute of the floating window component. Recommended data should be `FloatingWindowHeader` id.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: 'text',
      description:
        'Sets the value for the **aria-describedby** attribute of the floating window component. Recommneded data should be `FloatingWindowBody` id.',
    },
  }}
  args={{
    id: 'floating-window-id',
    testId: 'floating-window-testid',
    placement: 'bottomRight',
    open: false,
    windowState: 'normal',
    width: '375px',
    maxHeight: '500px',
    ariaLabelledBy: 'floating-window-header-id',
    ariaDescribedBy: 'floating-window-body-id',
  }}
/>

# Floating Window

<Examples />

## Live Demo

<Canvas>
  <Story name="Floating Window">
    {(args) => {
      const styles = {
        floatingWindowBodyContainer: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
        },
        floatingWindowFooterContainer: {
          display: 'flex',
          padding: 'var(--evr-spacing-sm)',
          gap: 'var(--evr-spacing-2xs)',
          justifyContent: 'flex-end',
          borderInlineEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
          borderBlockEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
          borderInlineStart: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
        },
      };
      const [{ open, windowState }, updateArgs] = useArgs();
      const triggerButtonRef = useRef(null);
      const textMap = {
        closeButtonAriaLabel: 'close floating window',
        minimizeButtonAriaLabel: 'minimize floating window',
        restoreButtonAriaLabel: 'restore floating window',
      };
      const handleWindowStateChange = (windowState) => {
        updateArgs({ windowState: windowState });
        action('onWindowStateChange')(windowState);
      };
      const handleOpen = useCallback(() => {
        action('onOpen')();
      }, []);
      const handleClose = useCallback((e) => {
        updateArgs({ open: false });
        triggerButtonRef?.current?.focus();
        action('onClose')();
      }, []);
      const RemainingTimeSection = () => {
        const styles = {
          remainingTimeContainer: {
            display: 'flex',
            flexDirection: 'row',
            alignSelf: 'stretch',
            padding: 'var(--evr-spacing-2xs) var(--evr-spacing-sm)',
            justifyContent: '0',
            alignItems: 'center',
            borderInlineEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
            borderBlockEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
            borderInlineStart: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
            background: 'var(--evr-surfaces-primary-hovered)',
          },
          remainingTime: {
            display: 'flex',
          },
          cancelButtonContainer: { display: 'flex', alignItems: 'center', gap: 'var(--evr-spacing-2xs)', marginInlineStart: 'auto' },
        };
        return (
          <section id={'time-remaining-id'} style={styles.remainingTimeContainer}>
            <div className={'evrCaptionText'} style={styles.remainingTime}>
              {'5 min left...'}
            </div>
            <div style={styles.cancelButtonContainer}>
              <Button id="cancel-all-uploads" label="Cancel all" variant="tertiary" />
            </div>
          </section>
        );
      };
      const FileUpload = ({ id, iconName, title, subtext }) => {
        const styles = {
          fileUpload: {
            display: 'flex',
            padding: 'var(--evr-spacing-xs) var(--evr-spacing-sm)',
            alignItems: 'flex-start',
            alignSelf: 'stretch',
            gap: 'var(--evr-spacing-2xs)',
            borderInlineEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
            borderBlockEnd: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
            borderInlineStart: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
          },
          fileUploadContent: {
            display: 'flex',
            flexDirection: 'column',
            flex: '1',
            justifyContent: 'center',
            alignItems: 'flex-start',
            gap: 'var(--evr-spacing-3xs)',
          },
          fileUploadHeader: {
            display: 'flex',
            alignItems: 'center',
            gap: 'var(--evr-spacing-2xs)',
            alignSelf: 'stretch',
          },
          fileUploadTitle: {
            color: '#1f1f1f',
            fontFamily: 'Inter',
            fontSize: '14px',
            fontStyle: 'normal',
            fontWeight: '700',
            lineHeight: '150%',
            letterSpacing: '0.23px',
          },
          fileUploadCancelButton: {
            marginInlineStart: 'auto',
          },
          fileUploadSubtext: {
            display: 'flex',
            alignItems: 'center',
            gap: 'var(--evr-spacing-2xs)',
            alignSelf: 'stretch',
          },
        };
        return (
          <div id={id} style={styles.fileUpload}>
            <Icon name={iconName} fill="--evr-content-primary-default" />
            <div style={styles.fileUploadContent}>
              <div style={styles.fileUploadHeader}>
                <div style={styles.fileUploadTitle}>{title}</div>
                <div style={styles.fileUploadCancelButton}>
                  <Button id={`${id}-cancel`} size="small" label="Cancel" variant="secondary" />
                </div>
              </div>
              <div className={'evrCaptionText'} style={styles.fileUploadSubtext}>
                {subtext}
              </div>
            </div>
          </div>
        );
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'center ' }}>
          <Button ref={triggerButtonRef} id="trigger-button-id" label="Click to toggle Floating Window" onClick={() => updateArgs({ open: !open })} />
          <FloatingWindow
            id={args.id}
            testId={args.testId}
            open={open}
            onOpen={handleOpen}
            onClose={handleClose}
            windowState={windowState}
            onWindowStateChange={handleWindowStateChange}
            placement={args.placement}
            width={args.width}
            maxHeight={args.maxHeight}
            ariaLabelledBy={args.ariaLabelledBy}
            ariaDescribedBy={args.ariaDescribedBy}
          >
            <FloatingWindowHeader
              id={'floating-window-header-id'}
              testId={`${args.testId}-header`}
              onCloseButtonClick={() => updateArgs({ open: false })}
              textMap={textMap}
            >
              {'Uploading 3 files'}
            </FloatingWindowHeader>
            <RemainingTimeSection />
            {windowState === 'normal' && (
              <>
                <FloatingWindowBody id="floating-window-body-id" testId={`${args.testId}-body`}>
                  <div style={styles.floatingWindowBodyContainer}>
                    <FileUpload
                      id="file-upload-1"
                      iconName="hourglass"
                      title="Portfolio.pdf"
                      subtext={`Queued for upload\u00B7 200 MB`}
                    />
                    <FileUpload
                      id="file-upload-2"
                      iconName="inProgress"
                      title="Cover letter.pdf"
                      subtext={`Uploading \u00B7 300 KB / 10 MB`}
                    />
                    <FileUpload
                      id="file-upload-3"
                      iconName="inProgress"
                      title="Resume.pdf"
                      subtext={`Uploading \u00B7 1 MB / 5 MB`}
                    />
                  </div>
                </FloatingWindowBody>
                <FloatingWindowFooter id="floating-window-footer-id" testId={`${args.testId}-footer`}>
                  <div style={styles.floatingWindowFooterContainer}>
                    <Button id="view-import-history-button" label="View import history" variant="secondary" />
                    <Button id="add-files-button" label="Add files" />
                  </div>
                </FloatingWindowFooter>
              </>
            )}
          </FloatingWindow>
        </div>
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="Floating Window" />
