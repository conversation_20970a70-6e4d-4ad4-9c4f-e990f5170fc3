import React, { PropsWithChildren, useCallback, useEffect, useRef } from 'react';
import { TPlacement } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { FloatingWindowProvider } from './floating-window-provider';
import { mergeRefs } from '../../utils';
import { IDialogBase, Overlay, TDialogCloseEventReason } from '../overlay';

import styles from './floating-window.module.scss';

export type TFloatingWindowState = 'normal' | 'minimized' | 'fullscreen';
export type TFloatingWindowPlacement = Exclude<
  TPlacement,
  | 'top'
  | 'left'
  | 'right'
  | 'bottom'
  | 'topLeft'
  | 'topCenter'
  | 'topRight'
  | 'rightCenter'
  | 'bottomLeft'
  | 'leftCenter'
>;

export interface IFloatingWindow extends IDialogBase {
  onWindowStateChange: (windowState: TFloatingWindowState) => void;
  windowState: TFloatingWindowState;
  placement?: TFloatingWindowPlacement;
  width: string;
  maxHeight?: string;
}

export const FloatingWindow = React.forwardRef<HTMLDialogElement, PropsWithChildren<IFloatingWindow>>(
  (props, ref): JSX.Element => {
    const {
      testId,
      id,
      children,
      open,
      onOpen,
      onClose,
      windowState,
      onWindowStateChange,
      placement = 'bottomRight',
      width,
      maxHeight,
      ariaLabelledBy,
      ariaDescribedBy,
    } = props;

    const floatingWindowContext = { windowState, onWindowStateChange };

    const contentContainerRef = useRef<HTMLDialogElement>(null);
    const lightBoxRef = useRef<HTMLDivElement>(null);
    const onOpenPrevRef = useRef(open ? null : false);
    const reasonRef = useRef<TDialogCloseEventReason | null>(null);

    const handleEscKey = useCallback(
      (event: React.KeyboardEvent) => {
        if (event.key === 'Escape' && open) {
          reasonRef.current = 'escapeKeyDown';
          setTimeout(() => onClose?.({ reason: reasonRef.current as TDialogCloseEventReason }));
        }
      },
      [onClose, open]
    );

    useEffect(() => {
      if (open && !onOpenPrevRef.current) {
        setTimeout(() => {
          contentContainerRef && contentContainerRef.current?.focus();
        });
        onOpen &&
          setTimeout(() => {
            onOpen();
          });
      } else if (!open && onOpenPrevRef.current) {
        if (reasonRef.current === null) {
          onClose?.();
        } else {
          reasonRef.current = null;
        }
      }
      onOpenPrevRef.current = open;
    }, [open, onOpenPrevRef, onOpen, onClose]);

    return (
      <FloatingWindowProvider {...floatingWindowContext}>
        <Overlay
          ref={lightBoxRef}
          id={`${id}-overlay`}
          testId={testId ? `${testId}-overlay` : undefined}
          lightBoxVariant="clear"
          open={open}
          onLightBoxKeyDown={handleEscKey}
          className={classnames(styles.evrFloatingWindow, {
            [styles.bottomRight]: placement === 'bottomRight',
          })}
          style={{
            insetInline: placement === 'bottomCenter' ? '50%' : undefined,
          }}
        >
          <dialog
            tabIndex={-1}
            id={id}
            ref={mergeRefs([ref, contentContainerRef])}
            data-testid={testId}
            aria-labelledby={ariaLabelledBy}
            aria-describedby={ariaDescribedBy}
            className={styles.evrFloatingWindowDialog}
            style={{
              width: width,
              maxHeight: maxHeight,
              transform: placement === 'bottomCenter' ? 'translateX(-50%)' : undefined,
            }}
          >
            {children}
          </dialog>
        </Overlay>
      </FloatingWindowProvider>
    );
  }
);

FloatingWindow.displayName = 'FloatingWindow';
