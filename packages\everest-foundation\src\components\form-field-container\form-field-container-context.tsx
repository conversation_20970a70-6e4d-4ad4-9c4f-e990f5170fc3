import React from 'react';

import { TFormFieldStatus } from './form-field-container';

export interface IFormFieldContainerContext {
  label?: string;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  status?: TFormFieldStatus;
  statusMessage?: string;
  statusMessagePrefix?: string;
  helperText?: string;
  helperTextPrefix?: string;
}

const defaultContext = {
  label: '',
  disabled: false,
  readOnly: false,
  required: false,
  status: undefined,
  helperText: '',
  helperTextPrefix: '',
  statusMessage: '',
  statusMessagePrefix: '',
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const FormFieldContainerContext = React.createContext<IFormFieldContainerContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  FormFieldContainerContext.displayName = 'FormFieldContainerContext';
}
