@use '../../variables.scss';

.evrFormFieldContainerIconWrapper {
  position: absolute;
  display: flex;
  align-items: center;
  height: 100%;

  &.top {
    height: auto;
    padding-block: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
  }

  &.startIcon {
    inset-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
  }

  &.endIcon {
    inset-inline-end: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
  }

  & > svg {
    pointer-events: none;
  }

  &.disabled {
    svg {
      @media (forced-colors: active) {
        fill: GrayText;
      }
    }
  }
}
