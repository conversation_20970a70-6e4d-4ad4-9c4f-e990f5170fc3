import React, { useCallback } from 'react';
import classnames from 'classnames';

import { Icon, IIconProps } from '../icon';

import styles from './form-field-container-icon-wrapper.module.scss';

export type TFormFieldContainerIconWrapperVerticalAlign = 'top' | 'middle';

export interface IFormFieldContainerIconWrapperProps {
  id?: string;
  iconName: IIconProps['name'];
  disabled?: boolean;
  readOnly?: boolean;
  position?: string;
  verticalAlign?: TFormFieldContainerIconWrapperVerticalAlign;
  testId?: string;
  onClick?: (e: React.MouseEvent) => void;
}

export const FormFieldContainerIconWrapper = (props: IFormFieldContainerIconWrapperProps): JSX.Element => {
  const {
    id,
    iconName,
    disabled = false,
    readOnly = false,
    position = 'start',
    verticalAlign = 'middle',
    testId,
    onClick,
  } = props;

  const getIconFillColor = useCallback(() => {
    if (disabled || readOnly) return '--evr-inactive-content';
    return '--evr-content-primary-default';
  }, [disabled, readOnly]);

  const handleClick = (e: React.MouseEvent) => {
    if (disabled || readOnly) return;
    onClick?.(e);
  };

  return (
    <div
      className={classnames(styles.evrFormFieldContainerIconWrapper, {
        [styles.startIcon]: position === 'start',
        [styles.endIcon]: position === 'end',
        [styles.top]: verticalAlign === 'top',
        [styles.disabled]: disabled,
      })}
      id={id}
      data-testid={testId ? `${testId}-icon-container` : undefined}
      onClick={handleClick}
    >
      <Icon testId={testId ? `${testId}-icon` : undefined} name={iconName} fill={getIconFillColor()} />
    </div>
  );
};
