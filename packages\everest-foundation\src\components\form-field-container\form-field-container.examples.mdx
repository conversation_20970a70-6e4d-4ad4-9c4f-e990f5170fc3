import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { Form<PERSON>ieldContainer, FormFieldContainerContext } from '../form-field-container';
import { Warning } from '../../../.storybook/docs/shared/status-banner.tsx';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { TextField } from '../text-field';

export const scope = {
  FormFieldContainer,
  FormFieldContainerContext,
  TextField,
};

# Form Field Container

<Warning>This feature is still in experimental state.</Warning>

Form Field Container is a low-level building block for all Everest Form Fields.
It is a visual-only and non-interactive component.

Form Field Container will handle the Form Field FocusRing, Label, and Status Message.

The structure of the Form Field contains the following

- label element (optional)
- focus ring element
  - field container
- status message element (optional)

Using Form Field Container to ensure all the Form Field has the same style and behaviors.
Form Field Container utilizing the following existing components

- Focus ring
- label
- status message

The Form Field Container provided a `div` container for any content by using renderContent prop.
It doesn't have Focus/Blur event, it has focused and hovered prop to alter the styles.
Form Field Container will handle the focus ring, and it can be turned off with hideFocusRing prop.

## Examples

### Basic Usage (Text Field)

This is an example of using Form Field Container to build the Text Field.
This example doesn't consider all the edge cases and behaviors. Refer to <LinkTo kind="Components/Text Fields/Text Field">Text Field</LinkTo> for completed implementation.

The following example is utilizing `FormFieldContainerContext` to define

- `label`
- `helperText`
- `helperTextPrefix`

`renderContent` is used to set the input content within the container

export const textFieldExample = `() => {
    const renderContent = () => {
      return <input style={{ border: "none", "border-radius": "inherit", outline: "none", "padding-inline-start": "10px", "padding-inline-end": "10px" }}/>
    }
    const context = {
        label: 'This is a Label',
        helperText: 'This is a helper text',
        helperTextPrefix: 'Hint:',
    };
    return (
      <FormFieldContainerContext.Provider value={context}>
        <FormFieldContainer renderContent={renderContent}/>
      </FormFieldContainerContext.Provider>       
    )
}`;

<CodeExample scope={scope} code={textFieldExample} />

### Variations

Form Field Container has different variations based on the props being used.

The following examples showcase different variations with the combination of props being used.

export const basicExample = `() => {
      const styles = {
        row: {
          width: '55%',
          height: 'auto',
          marginBlockEnd: '30px',
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const HeaderText = ({ children }) => (
        <div style={styles.headerText}><p className='evrBodyText'>{children}</p></div>
    );
    const labelContext = {
        label: 'Form Field Container with Label',
    };
    const helperTextContext = {
        helperText: 'Form Field Container with Helper Text',
        helperTextPrefix: 'Hint:',
    };
    const context = {
        helperText: 'This is a helper text',
        helperTextPrefix: 'Hint:',
        statusMessage: 'This is a Status Message',
        statusMessagePrefix: 'Prefix:',
    };
    return (
      <div style={styles.column}>
        <Row>
          <FormFieldContainer />
        </Row>
        <Row>
          <FormFieldContainerContext.Provider value={labelContext}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>
        <Row>
          <FormFieldContainerContext.Provider value={helperTextContext}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>    
        <Row>
          <FormFieldContainerContext.Provider value={{...context, label:"Form Field Container"}}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>
        <Row>
          <FormFieldContainerContext.Provider 
            value={{...context, 
                    label:"Form Field Container with very long label. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. "
                  }}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>
        <Row>
          <FormFieldContainerContext.Provider 
            value={{...context,
                      disabled:true, 
                      label:"Disabled Form Field Container"
                  }}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>
        <Row>
          <FormFieldContainerContext.Provider 
            value={{...context,
                      readOnly:true, 
                      label:"Read Only Form Field Container"
                  }}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>
        <Row>
          <FormFieldContainerContext.Provider
            value={{...context,
                      required:true, 
                      label:"Required Form Field Container"
                  }}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>
        <Row>
          <FormFieldContainerContext.Provider
            value={{...context,
                      status:'error', 
                      label:"Error Form Field Container"
                  }}>
            <FormFieldContainer/>
          </FormFieldContainerContext.Provider>           
        </Row>
        <Row>
          <FormFieldContainerContext.Provider
            value={{...context,
                      label:"Form Field with Clear Button"
                  }}>
            <FormFieldContainer onClear={() => {}} />
          </FormFieldContainerContext.Provider>           
        </Row>
      </div>
    )
}`;

<CodeExample scope={scope} code={basicExample} />

#### FormFieldContainerContext properties

| Property Name        | Type             | Description                                                    |
| -------------------- | ---------------- | -------------------------------------------------------------- |
| label?               | string           | Label text.                                                    |
| disabled?            | boolean          | Sets the styles to make to FormFieldContainer appear disabled. |
| readOnly?            | boolean          | Sets the styles to make to FormFieldContainer appear readOnly. |
| required?            | boolean          | Adds a 'required' indicator next to the label.                 |
| status?              | TFormFieldStatus | Sets the status for the FormFieldContainer.                    |
| helperText?          | string           | Text displayed below the element content.                      |
| helperTextPrefix?    | string           | Prefix for the helper text.                                    |
| statusMessage?       | string           | Message for the user related to the current state.             |
| statusMessagePrefix? | string           | Prefix for the status message.                                 |
