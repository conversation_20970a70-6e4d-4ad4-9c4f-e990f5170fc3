@use '@ceridianhcm/theme/dist/scss/' as typography;
@use '../../variables.scss';

.evrFormFieldContainer {
  &.disabled {
    cursor: not-allowed;
  }
  .statusRow {
    display: flex;

    .counterContainer {
      margin-inline-start: auto;
    }
  }
}

.evrHeaderContainer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-block-end: var(--evr-spacing-3xs);

  .evrLabelContainer {
    margin-inline-end: auto;
  }

  .evrClearButtonContainer {
    flex-shrink: 0;
    margin-inline-start: auto;

    .evrClearButton {
      box-sizing: border-box;
      @include typography.captionBold;
      border-radius: var(--evr-radius-2xs);
      border: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-default);
      color: var(--evr-content-primary-default);
      padding-block: 0;
      padding-inline: var(--evr-spacing-2xs);
      background-color: var(--evr-surfaces-primary-default);
      cursor: pointer;

      &:hover {
        border-color: var(--evr-inactive-content); // intentional as per design specs
      }

      &:active {
        border-color: var(--evr-borders-primary-default); // intentional as per design specs
      }

      &.disabled {
        cursor: not-allowed;
        color: var(--evr-inactive-content);
        border: none;
      }
    }
  }
}

.evrFormFieldBorder {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default);
  border-radius: var(--evr-radius-2xs);
  background-color: var(--evr-surfaces-primary-default);
  overflow: hidden;

  &.hover {
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-hovered);
  }

  &:active,
  &.focused {
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-pressed);
  }

  &.error {
    border: var(--evr-border-width-thick-px) solid var(--evr-borders-status-error);
  }

  &.bottomBorder {
    border: 0;
    border-radius: 0;
    border-bottom: var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default);
  }

  &.hasMarginAtBottom {
    margin-block-end: var(--evr-spacing-3xs);
  }

  &.disabled {
    border: var(--evr-border-width-thin-px) solid var(--evr-inactive-border);
  }

  &.readOnly {
    background-color: var(--evr-inactive-surfaces);
  }
}
