import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { FormFieldContainer, FormFieldContainerContext } from '../form-field-container';
import { userEvent } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Form Field Container"
  component={FormFieldContainer}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    testId: 'form-field-container',
    onClear: undefined,
  }}
/>

# Text Field

## Live Demo

export const context = {
  label: 'This is a label',
  status: 'default',
  helperText: 'This is help text',
  helperTextPrefix: 'Hint:',
  statusMessage: 'This is status message',
  statusMessagePrefix: 'Note:',
};

export const renderSampleContent = () => {
  const styles = {
    evrTextField: {
      width: '100%',
      display: 'flex',
      position: 'relative',
    },
    evrTextFieldInput: {
      width: '100%',
      backgroundColor: '#FFFFFF',
      outline: 'none',
      textOverflow: 'ellipsis',
      paddingInlineStart: '0.75rem',
      paddingInlineEnd: '0.75rem',
      border: 0,
      borderRadius: '0.75rem',
    },
  };
  return (
    <div style={styles.evrTextField}>
      <input
        className={'evrBodyText1'}
        style={styles.evrTextFieldInput}
        value={'Sample Content'}
        aria-label={'This is aria label'}
      />
    </div>
  );
};

export const BasicFormFieldContainer = (props) => {
  return (
    <FormFieldContainerContext.Provider value={context}>
      <FormFieldContainer {...props} />
    </FormFieldContainerContext.Provider>
  );
};

<Canvas>
  <Story name="Default">{(args) => <FormFieldContainer {...args} hideStatusMessage />}</Story>
</Canvas>

<Canvas>
  <Story name="Long Label">
    {(args) => (
      <FormFieldContainerContext.Provider
        value={{
          label:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum',
        }}
      >
        <FormFieldContainer {...args} hideStatusMessage />
      </FormFieldContainerContext.Provider>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Hidden Label">{(args) => <BasicFormFieldContainer {...args} hideLabel hideStatusMessage />}</Story>
</Canvas>

<Canvas>
  <Story name="Hovered">{(args) => <BasicFormFieldContainer {...args} hovered hideStatusMessage />}</Story>
</Canvas>

<Canvas>
  <Story name="Focused">{(args) => <BasicFormFieldContainer {...args} focused hideStatusMessage />}</Story>
</Canvas>

<Canvas>
  <Story name="Bottom Border">{(args) => <BasicFormFieldContainer {...args} bottomBorder hideStatusMessage />}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled">
    {(args) => (
      <FormFieldContainerContext.Provider value={{ label: context.label, disabled }}>
        <FormFieldContainer {...args} hideStatusMessage />
      </FormFieldContainerContext.Provider>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly">
    {(args) => (
      <FormFieldContainerContext.Provider value={{ label: context.label, readOnly }}>
        <FormFieldContainer {...args} hideStatusMessage />
      </FormFieldContainerContext.Provider>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Helper Text">
    {(args) => (
      <FormFieldContainerContext.Provider
        value={{ label: context.label, helperTextPrefix: context.helperTextPrefix, helperText: context.helperText }}
      >
        <FormFieldContainer {...args} />
      </FormFieldContainerContext.Provider>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Success Status">
    {(args) => (
      <FormFieldContainerContext.Provider
        value={{
          label: context.label,
          status: 'success',
          statusMessagePrefix: context.statusMessagePrefix,
          statusMessage: context.statusMessage,
        }}
      >
        <FormFieldContainer {...args} />
      </FormFieldContainerContext.Provider>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Status">
    {(args) => (
      <FormFieldContainerContext.Provider
        value={{
          label: context.label,
          status: 'error',
          statusMessagePrefix: context.statusMessagePrefix,
          statusMessage: context.statusMessage,
        }}
      >
        <FormFieldContainer {...args} />
      </FormFieldContainerContext.Provider>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Clear Button">
    {(args) => (
      <FormFieldContainerContext.Provider
        value={{
          label: context.label,
        }}
      >
        <FormFieldContainer {...args} textMap={{ clearButton: 'Clear All' }} onClear={() => {}} />
      </FormFieldContainerContext.Provider>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Tab Focus"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <FormFieldContainer {...args} renderContent={renderSampleContent} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Clear Button Tab Focus"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => (
      <FormFieldContainer
        {...args}
        renderContent={renderSampleContent}
        textMap={{ clearButton: 'Clear All' }}
        onClear={() => {}}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Hidden Focus Ring on Tab Focus">
    {(args) => <FormFieldContainer {...args} hideFocusRing renderContent={renderSampleContent} />}
  </Story>
</Canvas>
