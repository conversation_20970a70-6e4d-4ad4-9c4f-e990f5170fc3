import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { FormFieldContainer, FormFieldContainerContext } from '../form-field-container';

describe('[FormFieldContainer]', () => {
  const testId = 'form-field-container';
  const label = 'This is a label';

  const getFormFieldContainer = () => screen.getByTestId(testId);
  const getClearButton = () => screen.getByTestId(`${testId}-clear-button`);
  const onClear = jest.fn();
  const renderContent = () => {
    const onChange = jest.fn();
    return <input value={'Sample Content'} onChange={onChange} />;
  };

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should display focus ring when tabbed', async () => {
    render(
      <FormFieldContainerContext.Provider
        value={{
          label,
        }}
      >
        <FormFieldContainer testId={testId} renderContent={renderContent} />
      </FormFieldContainerContext.Provider>
    );

    expect(getFormFieldContainer()).not.toHaveClass('evrFocusRingVisible');
    await userEvent.tab();
    expect(getFormFieldContainer()).toHaveClass('evrFocusRingVisible');
  });

  it('should not display focus ring when tabbed if hideFocusRing prop is provided', async () => {
    render(
      <FormFieldContainerContext.Provider
        value={{
          label,
        }}
      >
        <FormFieldContainer testId={testId} hideFocusRing={true} renderContent={renderContent} />
      </FormFieldContainerContext.Provider>
    );

    expect(getFormFieldContainer()).not.toHaveClass('evrFocusRingVisible');
    await userEvent.tab();
    expect(getFormFieldContainer()).not.toHaveClass('evrFocusRingVisible');
  });

  it('should not display focus ring when clicked', async () => {
    render(
      <FormFieldContainerContext.Provider
        value={{
          label,
        }}
      >
        <FormFieldContainer testId={testId} renderContent={renderContent} />
      </FormFieldContainerContext.Provider>
    );

    expect(getFormFieldContainer()).not.toHaveClass('evrFocusRingVisible');
    await userEvent.click(getFormFieldContainer());
    expect(getFormFieldContainer()).not.toHaveClass('evrFocusRingVisible');
  });
  it('should show clear button and call onClear when specified and clicked', async () => {
    render(
      <FormFieldContainerContext.Provider
        value={{
          label,
        }}
      >
        <FormFieldContainer testId={testId} hideFocusRing={true} renderContent={renderContent} onClear={onClear} />
      </FormFieldContainerContext.Provider>
    );
    expect(getClearButton()).toBeTruthy();
    await userEvent.click(getClearButton());
    expect(onClear).toHaveBeenCalledTimes(1);
  });
  it('should show clear button and call onClear when specified and on keyboard space key', async () => {
    render(
      <FormFieldContainerContext.Provider
        value={{
          label,
        }}
      >
        <FormFieldContainer testId={testId} hideFocusRing={true} renderContent={renderContent} onClear={onClear} />
      </FormFieldContainerContext.Provider>
    );
    expect(getClearButton()).toBeTruthy();
    await userEvent.tab();
    await userEvent.keyboard(' ');
    expect(onClear).toHaveBeenCalledTimes(1);
  });
  it('should pass focus to input when label is clicked on', async () => {
    const inputId = 'form-field-container--input';
    const inputRenderContent = () => <input id={inputId} data-testid={`${testId}-${inputId}`} />;
    render(
      <FormFieldContainerContext.Provider
        value={{
          label,
        }}
      >
        <FormFieldContainer
          testId={testId}
          hideFocusRing={true}
          renderContent={inputRenderContent}
          onClear={onClear}
          htmlFor={inputId}
        />
      </FormFieldContainerContext.Provider>
    );
    const labelElement = screen.getByText(label);
    const inputElement = screen.getByTestId(`${testId}-${inputId}`);
    expect(inputElement).not.toHaveFocus();
    await userEvent.click(labelElement);
    expect(inputElement).toHaveFocus();
  });
});
