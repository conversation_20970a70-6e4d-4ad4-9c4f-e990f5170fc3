import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { FormFieldContainer, FormFieldContainerContext } from '../form-field-container';
import Examples from './form-field-container.examples.mdx';
import { action } from '@storybook/addon-actions';

<Meta
  title="Toolbox/Form Field Container"
  component={FormFieldContainer}
  parameters={{
    controls: {
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    testId: {
      description:
        'Sets a value for `data-testid` attribute on the the Form Field Container element. Used for automation testing.',
    },
    id: {
      description: 'Unique identifier for the Form Field Container element.',
    },
    htmlFor: {
      description: 'Specifies which form element a label is bound to.',
    },
    labelId: {
      description: 'Unique identifier for the label element.',
    },
    statusMessageId: {
      description: 'Unique identifier for the status message element..',
    },
    focused: {
      type: 'boolean',
      control: 'boolean',
      description: 'Controls whether the Form Field Container is focused to set focused style.',
    },
    hovered: {
      type: 'boolean',
      control: 'boolean',
      description: 'Controls whether the Form Field Container is hovered to set hovered style.',
    },
    hideLabel: {
      type: 'boolean',
      control: 'boolean',
      description: 'Hide label element.',
    },
    hideStatusMessage: {
      type: 'boolean',
      control: 'boolean',
      description: 'Hide status message element.',
    },
    hideFocusRing: {
      type: 'boolean',
      control: 'boolean',
      description: 'Hide Focus Ring element.',
    },
    onMouseDown: {
      control: '-',
      description: 'Callback when Form Field Container element mouse down.',
    },
    renderContent: {
      control: '-',
      description: 'Callback to render content in Form Field Container.',
    },
    characterCounter: {
      control: '-',
      description: 'Character counter React Node in Form Field Container, mainly used for `TextArea` and `TextField`.',
    },
    height: {
      description: 'Sets the height on the Form Field Container.',
      defaultValue: { summary: '--evr-size-2xl' },
    },
    ariaHidden: {
      description: 'Sets the entire Form Field Container as aria-hidden.',
      defaultValue: { summary: false },
    },
    onClear: {
      description: 'Adds a clear button and callback when clicked to the Form Field Container.',
    },
  }}
  args={{
    id: 'form-field-container-id',
    testId: 'form-field-container-test-id',
    focused: false,
    hovered: false,
    hideLabel: false,
    hideStatusMessage: false,
    hideFocusRing: false,
    onMouseDown: action('onMouseDown'),
    renderContent: action('renderContent'),
    height: 'var(--evr-size-2xl)',
    onClear: undefined,
    htmlFor: 'form-field-container-input',
  }}
/>

<Examples />

# Form Field Container

<Canvas>
  <Story name="Form Field Container">
    {(args) => {
      const renderContent = () => {
        args.renderContent?.();
        return (
          <input
            id="form-field-container-input"
            style={{
              border: 'none',
              'border-radius': 'inherit',
              outline: 'none',
              'padding-inline-start': '10px',
              'padding-inline-end': '10px',
            }}
          />
        );
      };
      const context = {
        label: 'This is a Label',
        helperText: 'This is a helper text',
        helperTextPrefix: 'Hint:',
      };
      return (
        <FormFieldContainerContext.Provider value={context}>
          <FormFieldContainer {...args} renderContent={renderContent} />
        </FormFieldContainerContext.Provider>
      );
    }}
  </Story>
</Canvas>

### Form Field Container props

<ArgsTable story="Form Field Container" />
