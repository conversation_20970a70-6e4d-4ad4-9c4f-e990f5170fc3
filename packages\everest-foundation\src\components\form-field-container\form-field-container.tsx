import React, { forwardRef, RefObject, useMemo } from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { FormFieldContainerContext } from './form-field-container-context';
import { Label } from '../label';
import { StatusMessage } from '../status-message';

import styles from './form-field-container.module.scss';

/*
 * @deprecated 'success'
 * Based on guidance from design, the success variant for form fields is no longer supported
 * Storybook examples of success have been removed
 * Variant will remain to avoid any breaking changes
 */
export type TFormFieldStatus = 'default' | 'error' | 'success';

export interface IFormFieldContainerTextMap {
  clearButton?: string;
}

export interface IFormFieldContainer {
  testId?: string;
  id?: string;
  htmlFor?: string;
  labelId?: string;
  statusMessageId?: string;
  focused?: boolean;
  hovered?: boolean;
  hideLabel?: boolean;
  hideStatusMessage?: boolean;
  hideFocusRing?: boolean;
  bottomBorder?: boolean;
  borderContainerRef?: RefObject<HTMLElement>;
  height?: string;
  maxHeight?: string;
  onMouseDown?: (e: React.MouseEvent) => void;
  renderContent?: () => React.ReactNode;
  onClickLabel?: (e: React.MouseEvent) => void;
  onClear?: () => void;
  characterCounter?: React.ReactNode;
  textMap?: IFormFieldContainerTextMap;
  ariaHidden?: boolean;
}

export const FormFieldContainer = forwardRef<HTMLDivElement, IFormFieldContainer>((props, ref): JSX.Element => {
  const context = React.useContext(FormFieldContainerContext);

  const {
    ariaHidden = false,
    borderContainerRef,
    bottomBorder = false,
    characterCounter,
    focused,
    height = 'var(--evr-size-2xl)',
    hideFocusRing = false,
    hideLabel = !context.label,
    hideStatusMessage = false,
    hovered,
    htmlFor,
    id,
    labelId,
    maxHeight = 'none',
    onClear,
    onClickLabel,
    onMouseDown,
    renderContent,
    statusMessageId,
    testId,
    textMap,
  } = props;

  const isStatusMessageRendered = useMemo(() => {
    return !!context.statusMessage && context.status !== 'default';
  }, [context.status, context.statusMessage]);

  const isHelperTextRendered = useMemo(() => {
    return !!context.helperText && !isStatusMessageRendered;
  }, [context.helperText, isStatusMessageRendered]);

  return (
    <div
      id={id}
      className={classnames(styles.evrFormFieldContainer, {
        [styles.disabled]: context.disabled,
      })}
      data-testid={testId ? `${testId}-form-field-container` : undefined}
      ref={ref}
    >
      {(onClear || !hideLabel) && (
        <div className={styles.evrHeaderContainer}>
          {!hideLabel && (
            <div className={styles.evrLabelContainer} aria-hidden={ariaHidden}>
              <Label
                required={context.required}
                htmlFor={htmlFor}
                testId={testId ? `${testId}-label` : undefined}
                id={labelId}
                disabled={context.disabled}
                variant="bodyText2"
                onClick={onClickLabel}
              >
                {context.label}
              </Label>
            </div>
          )}
          {onClear && !context.readOnly && (
            <div className={styles.evrClearButtonContainer}>
              <FocusRing canFocusFromMouse disabled={context.disabled}>
                <button
                  id={`${id}-clear-button`}
                  data-testid={testId ? `${testId}-clear-button` : undefined}
                  className={classnames(styles.evrClearButton, { [styles.disabled]: context.disabled })}
                  onClick={onClear}
                  disabled={context.disabled || undefined}
                >
                  {textMap?.clearButton}
                </button>
              </FocusRing>
            </div>
          )}
        </div>
      )}
      <FocusRing manual={hideFocusRing} canFocusFromMouse>
        <div
          ref={borderContainerRef as React.RefObject<HTMLDivElement>}
          className={classnames(styles.evrFormFieldBorder, {
            [styles.hasMarginAtBottom]:
              (isStatusMessageRendered || isHelperTextRendered || characterCounter) && !hideStatusMessage,
            [styles.focused]: focused,
            [styles.hover]: hovered,
            [styles.bottomBorder]: bottomBorder,
            [styles.error]: context.status === 'error',
            [styles.disabled]: context.disabled,
            [styles.readOnly]: context.readOnly,
          })}
          onMouseDown={onMouseDown}
          style={{ height: `${height}`, maxHeight: `${maxHeight}` }}
          aria-hidden={ariaHidden}
          data-testid={testId}
        >
          {renderContent?.()}
        </div>
      </FocusRing>
      <div className={classnames(styles.statusRow)} aria-hidden={ariaHidden}>
        {!hideStatusMessage && (
          <StatusMessage
            id={statusMessageId}
            testId={testId && `${testId}-status-message`}
            visible
            variant={context.status}
            statusMessagePrefix={context.statusMessagePrefix}
            statusMessage={context.statusMessage}
            helperTextPrefix={context.helperTextPrefix}
            helperText={context.helperText}
            disabled={context.disabled}
          />
        )}
        {characterCounter && <div className={styles.counterContainer}>{characterCounter}</div>}
      </div>
    </div>
  );
});

FormFieldContainer.displayName = 'FormFieldContainer';
