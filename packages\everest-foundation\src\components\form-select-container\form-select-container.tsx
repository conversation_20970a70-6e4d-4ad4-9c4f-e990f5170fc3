import React, { PropsWithChildren } from 'react';

import { FormFieldContainer, IFormFieldContainer } from '../form-field-container';
import { SelectContainer, ISelectContainer } from '../select-container';

interface IFormSelectContainer
  extends Omit<IFormFieldContainer, 'renderContent'>,
    Omit<ISelectContainer, 'renderContent' | 'onClear'> {
  id: string;
}

export const FormSelectContainer = (props: PropsWithChildren<IFormSelectContainer>): JSX.Element => {
  const {
    id,
    testId,
    children,
    // form field props
    labelId,
    statusMessageId,
    focused,
    height,
    hovered,
    hideLabel,
    hideStatusMessage,
    hideFocusRing,
    htmlFor,
    bottomBorder,
    onMouseDown,
    onClickLabel,
    borderContainerRef,
    ariaHidden,
    textMap,
    maxHeight,
    // select container props
    disabled,
    readOnly,
    chevronIconOpen,
    chevronIconVerticalAlign,
    onChevronIconClick,
    onClear,
    clickInsteadOfMouseDownOverride,
  } = props;

  const formFieldProps = {
    ariaHidden,
    borderContainerRef,
    bottomBorder,
    focused,
    height,
    hideFocusRing,
    hideLabel,
    hideStatusMessage,
    hovered,
    htmlFor,
    id: `${id}-form-field`,
    labelId,
    maxHeight,
    onChevronIconClick,
    onClear,
    onClickLabel,
    onMouseDown,
    statusMessageId,
    testId: testId ? `${testId}-form-field` : undefined,
    textMap,
  };

  const selectContainerProps = {
    id: `${id}-select-container`,
    testId: testId ? `${testId}-select-container` : undefined,
    disabled,
    readOnly,
    chevronIconOpen,
    chevronIconVerticalAlign,
    onChevronIconClick,
    clickInsteadOfMouseDownOverride,
  };

  const contentToRender = () => <SelectContainer {...selectContainerProps} renderContent={() => children} />;

  return <FormFieldContainer {...formFieldProps} renderContent={contentToRender} />;
};

FormSelectContainer.displayName = 'FormSelectContainer';
