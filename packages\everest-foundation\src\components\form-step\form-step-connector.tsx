import React from 'react';
import classnames from 'classnames';

import styles from './form-step-connector.module.scss';

export type TFormStepConnectorStatus = 'incomplete' | 'complete' | 'none';

export interface IFormStepConnectorProps {
  id: string;
  testId?: string;
  status?: TFormStepConnectorStatus;
}

export const FormStepConnector = (props: IFormStepConnectorProps): JSX.Element => {
  const { id, testId, status } = props;

  return (
    <span
      id={id}
      data-testid={testId}
      aria-hidden="true"
      className={classnames(styles.evrFormStepConnector, { [styles.complete]: status === 'complete' })}
    />
  );
};

FormStepConnector.displayName = 'FormStepConnector';
