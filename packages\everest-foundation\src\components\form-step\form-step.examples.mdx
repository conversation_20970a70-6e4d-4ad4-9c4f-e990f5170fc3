import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { FormStep } from '../form-step';
import { Button } from '../button';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';

export const scope = {
  FormStep,
  Button,
};

A form step is comprised of an indicator and connector. The combination of these two items will determine the status of the step.

## Variations

### First Step

The first step in a stepper should have its `connectorStatus` explicitly set to `"none"`. The step will have just an indicator.

export const firstStepCode = `() => {
  return (
    <div style={{ width: '50%'}}>
        <FormStep
          stepPosition={1}
          id="complete-step-id"
          label="Active Step with No Connector"
          connectorStatus="none"
          active
          onClick={() =>{}}
        />
        <FormStep
          stepPosition={2}
          id="complete-step-id"
          label="Complete Step with No Connector"
          connectorStatus="none"
          complete
          onClick={() =>{}}
        />
    </div>
   )
}`;

<CodeExample scope={scope} code={firstStepCode} />

### Other Steps

Form step indicators and connectors are incomplete by default. Active and complete steps will have a colored connector.

export const otherStepsCode = `() => {
  return (
    <div style={{ width: '50%'}}>
        <FormStep
          stepPosition={1}
          id="complete-step-id"
          label="Incomplete Step"
          onClick={() =>{}}
        />
        <FormStep
          stepPosition={2}
          id="complete-step-id"
          label="Active Step"
          connectorStatus="complete"
          active
          onClick={() =>{}}
        />
        <FormStep
          stepPosition={3}
          id="complete-step-id"
          label="Complete Step"
          connectorStatus="complete"
          complete
          onClick={() =>{}}
        />
    </div>
   )
}`;

<CodeExample scope={scope} code={otherStepsCode} />

## Usage Guidelines

Form Step must be used with <LinkTo kind="Components/Form Stepper/Form Stepper">Form Stepper</LinkTo> for full functionality and accessibility.

Consumers are responsible for tracking the status of each Form Step.

## Accessibility

A `stepPosition` should be provided for each Form Step. This value is used to generate a screen reader label in Form Stepper showing step progress. For example, this label might say "Step 1 of 5".
