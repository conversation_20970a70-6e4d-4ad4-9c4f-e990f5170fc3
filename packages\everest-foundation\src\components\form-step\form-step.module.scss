@use '../../index.scss' as helper;

.evrFormStep {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  align-self: stretch;
  position: relative;

  button {
    background: none;
    border: 0;
    outline: none;

    &:focus {
      border-radius: var(--evr-radius-2xs); // 8px
      margin-inline: auto;
    }
  }

  .indicatorSection {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    padding-block-start: var(--evr-spacing-2xs);

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: var(--evr-size-lg);
      width: var(--evr-size-lg);

      &.clickable {
        cursor: pointer;
      }
    }
  }

  .label {
    text-align: center;
    color: var(--evr-content-primary-lowemp);

    &.clickable {
      cursor: pointer;
    }
    &.complete {
      color: var(--evr-content-primary-default);
    }
    &.active {
      font-weight: var(--evr-bold-weight);
      color: var(--evr-content-primary-highemp);
    }
  }

  .connector {
    position: absolute;
    // z-index: -1; // needed to move focus ring above step connectors

    // Consider RTL, is there a different styling we can use?
    top: calc(var(--evr-spacing-sm) + var(--evr-spacing-2xs));
    left: calc(-50% + var(--evr-spacing-sm));
    right: calc(52% + var(--evr-spacing-sm));
  }

  .visuallyHidden {
    @include helper.visuallyHidden();
  }
}
