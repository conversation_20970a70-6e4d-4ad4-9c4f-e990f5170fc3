import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { FormStep } from '.';
import { userEvent } from '@storybook/test';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Form Step"
  component={FormStep}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    testId: 'form-step-test-id',
    stepPosition: 1,
  }}
/>

# Form Step

## Live Demo

<Canvas>
  <Story name="Incomplete First">
    {({ ...args }) => (
      <FormStep {...args} id="incomplete-first-form-step" label="Incomplete-First Form Step" connectorStatus="none" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Active First">
    {({ ...args }) => (
      <FormStep {...args} id="active-first-form-step" label="Active-First Form Step" active connectorStatus="none" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Complete First">
    {({ ...args }) => (
      <FormStep
        {...args}
        id="complete-first-form-step"
        label="Complete-First Form Step"
        complete
        connectorStatus="none"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Incomplete">
    {({ ...args }) => <FormStep {...args} id="incomplete-form-step" label="Incomplete Form Step" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Active">
    {({ ...args }) => (
      <FormStep {...args} id="active-form-step" label="Active Form Step" active connectorStatus="complete" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Complete">
    {({ ...args }) => (
      <FormStep {...args} id="complete-form-step" label="Complete Form Step" complete connectorStatus="complete" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Incomplete with Focus Ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {({ ...args }) => <FormStep {...args} id="incomplete-focus-ring-form-step" label="Incomplete with Focus Ring" />}
  </Story>
</Canvas>
