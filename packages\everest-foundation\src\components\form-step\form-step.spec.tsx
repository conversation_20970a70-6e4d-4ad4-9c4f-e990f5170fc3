import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { FormStep } from '.';

const id = 'form-step-id';
const testId = 'form-step-test-id';
const stepPosition = 1;
const label = 'Label';
const onClick = jest.fn();

const defaultProps = {
  id,
  testId,
  stepPosition,
  label,
  onClick,
};

const getStep = () => screen.getByTestId(testId);
const getStepButton = () => screen.getByTestId(`${testId}-button`);
const getStepIcon = () => screen.getByTestId(`${testId}-icon`);
const getStepConnector = () => screen.getByTestId(`${testId}-connector`);
const getLabelText = () => screen.queryByText(label);

describe('[Form Step]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('has an icon', async () => {
    render(<FormStep {...defaultProps} />);
    expect(getStepIcon()).toBeInTheDocument();
  });

  it('has a label', async () => {
    render(<FormStep {...defaultProps} />);

    expect(getStep()).toBeInTheDocument();
    expect(getLabelText()).toBeInTheDocument();
  });

  it('has a connector', async () => {
    render(<FormStep {...defaultProps} />);

    expect(getStep()).toBeInTheDocument();
    expect(getStepConnector()).toBeInTheDocument();
  });

  it('should trigger onClick if onClick provided', async () => {
    render(<FormStep {...defaultProps} />);

    userEvent.click(getStepIcon());

    await waitFor(() => {
      expect(onClick).toHaveBeenCalledTimes(1);
    });
  });

  it('should call onClick when the keyboard space or enter key is pressed', async () => {
    render(<FormStep {...defaultProps} />);

    getStepButton().focus();
    userEvent.keyboard(' ');

    await waitFor(() => {
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    getStep().focus();
    userEvent.keyboard('{Enter}');

    await waitFor(() => {
      expect(onClick).toHaveBeenCalledTimes(2);
    });
  });

  it('should not trigger onClick if onClick is not provided or undefined', async () => {
    render(<FormStep {...defaultProps} onClick={undefined} />);

    userEvent.click(getStepIcon());

    await waitFor(() => {
      expect(onClick).toHaveBeenCalledTimes(0);
    });
  });
});
