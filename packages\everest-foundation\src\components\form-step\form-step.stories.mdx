import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { FormStep } from './form-step';
import { action } from '@storybook/addon-actions';
import Examples from './form-step.examples.mdx';

<Meta
  title="Components/Form Stepper/Form Step"
  component={FormStep}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/Everest-Web?node-id=67770-32303&m=dev',
    },
  }}
  argTypes={{
    onClick: {
      description: 'Sets a callback function that is executed when form step is clicked.',
      control: 'select',
      options: ['Defined', 'Undefined'],
      mapping: {
        Defined: action('onClick'),
        Undefined: undefined,
      },
    },
  }}
  args={{
    id: 'form-step-id',
    testId: 'form-step-test-id',
    stepPosition: 1,
    label: 'Label',
    active: false,
    complete: false,
    connectorStatus: 'incomplete',
    onClick: 'Defined',
  }}
/>

# Form Step

<Examples />

## Live Demo

<Canvas>
  <Story name="Form Step">{(args) => <FormStep {...args} />}</Story>
</Canvas>

<ArgsTable story="Form Step" />
