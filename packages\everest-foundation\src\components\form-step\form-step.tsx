import { forwardRef, useCallback, useContext } from 'react';
import React from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { FormStepConnector, TFormStepConnectorStatus } from './form-step-connector';
import { templateReplacer } from '../../utils/template-replacer';
import { FormStepperContext } from '../form-stepper/form-stepper-context';
import { Icon } from '../icon';

import styles from './form-step.module.scss';

export interface IFormStep {
  /** Sets the `id` attribute. */
  id: string;
  /** Sets `data-testid` attribute. */
  testId?: string;
  /** Sets position number of form step out of total steps. */
  stepPosition: number;
  /** Sets label on form step indicator. */
  label: string;
  /**
   * Sets form step indicator to active status.
   * @default false
   */
  active?: boolean;
  /**
   * Sets form step indicator to complete status.
   * @default false
   */
  complete?: boolean;
  /**
   * Sets connector status on form step.
   * @default incomplete
   */
  connectorStatus?: TFormStepConnectorStatus;
  onClick?: (e: React.MouseEvent | React.KeyboardEvent) => void;
}

export const FormStep = forwardRef<HTMLLIElement, IFormStep>((props: IFormStep, ref) => {
  const { id, testId, stepPosition, label, active, complete, connectorStatus = 'incomplete', onClick } = props;
  const { totalSteps, textMap } = useContext(FormStepperContext);

  const clickable = !!onClick;

  const getStepIndicator = () => {
    if (active) {
      return (
        <Icon
          name="semiCircleTilted"
          size="lg"
          fill="--evr-content-status-informative-default"
          testId={testId ? `${testId}-icon` : undefined}
        />
      );
    } else if (complete) {
      return (
        <Icon
          name="approvalFilled"
          fill="--evr-content-status-informative-default"
          testId={testId ? `${testId}-icon` : undefined}
        />
      );
    } else {
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          fill="none"
          data-testid={testId ? `${testId}-icon` : undefined}
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12Z"
            fill="var(--evr-content-primary-lowemp)"
          />
        </svg>
      );
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.stopPropagation();
      e.preventDefault();
      onClick?.(e);
    }
  };

  /** SR announces --> "Step 1 of 5" */
  const getStepPositionSRLabel = useCallback(() => {
    if (textMap) {
      const progressLabel = templateReplacer(textMap.stepXofY, [`${stepPosition}`, `${totalSteps}`]);
      return `${progressLabel}, `;
    }
  }, [stepPosition, textMap, totalSteps]);

  /** SR announces --> "current step, button, group" */
  const getStepStatusSRLabel = useCallback(() => {
    if (textMap) {
      if (active) return; //don't need ariaLabel bc aria-current announces "current step"
      if (complete) return `, ${textMap.completeLabel}`;
      // add "partially complete, in review"
      return `, ${textMap.incompleteLabel}`;
    }
  }, [active, complete, textMap]);

  return (
    <li ref={ref} key={id} id={id} data-testid={testId} className={styles.evrFormStep}>
      {connectorStatus !== 'none' && connectorStatus !== undefined && (
        <div className={styles.connector}>
          <FormStepConnector
            id={`${id}-connector`}
            testId={testId ? `${testId}-connector` : undefined}
            status={connectorStatus}
          />
        </div>
      )}
      <FocusRing disabled={!clickable}>
        <button
          onClick={onClick}
          onKeyDown={handleKeyDown}
          disabled={!clickable}
          aria-current={active ? 'step' : undefined}
          // aria-disabled={active || complete || clickable ? undefined : true} // only incomplete steps in form stepper are aria-disabled
          className={styles.indicatorSection}
          data-testid={testId ? `${testId}-button` : undefined}
        >
          <div className={classnames(styles.icon, { [styles.clickable]: clickable })}>{getStepIndicator()}</div>
          {/* SR announces --> "Step 1 of 5, 1. Create Account, current step." */}
          <span className={styles.visuallyHidden}>{getStepPositionSRLabel()}</span>
          <span
            id={`${id}-label`}
            className={classnames(styles.label, 'evrBodyText1', {
              [styles.active]: active,
              [styles.complete]: complete,
              [styles.clickable]: clickable,
            })}
          >
            {label}
          </span>
          <span className={styles.visuallyHidden}>{getStepStatusSRLabel()}</span>
        </button>
      </FocusRing>
    </li>
  );
});

FormStep.displayName = 'FormStep';
