import React from 'react';

import { IFormStepperTextMap } from './form-stepper';

export interface IFormStepperContext {
  totalSteps: number;
  textMap: IFormStepperTextMap;
}

const defaultContext = {
  totalSteps: 0,
  textMap: {
    ariaLabel: '',
    stepXofY: '',
    incompleteLabel: '',
    partialLabel: '',
    completeLabel: '',
  },
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const FormStepperContext = React.createContext<IFormStepperContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  FormStepperContext.displayName = 'FormStepperContext';
}
