import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { FormStepper } from '.';
import { FormStep } from '../form-step';
import { Button } from '../button';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';

export const scope = {
  FormStepper,
  FormStep,
  Button,
};

The form stepper component displays progress through a sequence of logical and numbered steps. There should be a **minimum of 2** steps and a **maximum of 5** in a Form Stepper.

Form Stepper was developed to be used with Form Step. Please use both components together for full functionality and accessibility.

Form Stepper accepts Form Step children. <LinkTo kind="Components/Form Stepper/Form Step">Form Step</LinkTo> has the following interface:

```typescript
export interface IFormStep {
  id: string;
  testId?: string;
  label: string;
  active?: boolean;
  complete?: boolean;
  connectorStatus?: TFormStepConnectorStatus;
  onClick?: (e: React.MouseEvent) => void;
}
```

## Basic Usage

The default form stepper is linear where steps must be completed in a sequential order.

export const defaultCode = `() => {
  const styles = {
    container: { 
      width: '90%'
    },
    buttonContainer: {
      display: 'flex',
      justifyContent: 'center',
      gap: 10,
      paddingTop: 50,
    },
    nextButtonContainer: { 
      display: 'flex',
      gap: 5
    }
  };
  const textMap = { 
    ariaLabel: 'Linear Form Stepper',
    stepXofY: 'Step {0} of {1}',
    completeLabel: 'Complete',
    partialLabel: 'In review',
    incompleteLabel: 'Incomplete',
  };
  const initialSteps = [
    {
      id: 'step-1',
      testId: 'step-1-test-id',
      label: '1. Create Account',
      connectorStatus: 'none',
    },
    {
      id: 'step-2',
      testId: 'step-2-test-id',
      label: '2. Add Personal Info',
    },
    {
      id: 'step-3',
      testId: 'step-3-test-id',
      label: '3. Adjust Settings',
    },
    {
      id: 'step-4',  
      testId: 'step-4-test-id',
      label: '4. Review',
    },
  ];
  const [formSteps, setFormSteps] = useState(initialSteps);
  const [activeStepIndex, setActiveStepIndex] = useState<number>(0);
  const handleToggleStepComplete = (index) => {
    const newFormSteps = [...formSteps];
    newFormSteps[index].complete = !newFormSteps[index].complete;
    setFormSteps(newFormSteps);
  };
  const canNavigateToStep = (index) => {
    if (index < 0 || index >= formSteps.length) {
      return false;
    }
    if (index <= activeStepIndex || formSteps[index].complete || formSteps[index - 1]?.complete) {
      return true;
    }
    return false;
  };
  const handleActiveStepIndexChange = (newActiveStepIndex) => {
    if (activeStepIndex === newActiveStepIndex || !canNavigateToStep(newActiveStepIndex)) return;
    const newFormSteps = [...formSteps];
    if (newActiveStepIndex > activeStepIndex) {
        newFormSteps[newActiveStepIndex].connectorStatus = 'complete';
    } else if (activeStepIndex !== 0 && !newFormSteps[activeStepIndex].complete) {
        newFormSteps[activeStepIndex].connectorStatus = 'incomplete';
    }
    setFormSteps(newFormSteps);
    setActiveStepIndex(newActiveStepIndex);
  };
  const handleReset = () => {
    setFormSteps(initialSteps);
    setActiveStepIndex(0);
  };
  return (
    <div style={styles.container}>
      <FormStepper id="linear-form-stepper-id" testId="linear-form-stepper-test-id" ariaLabel="Linear Form Stepper" textMap={textMap}>
        {formSteps.map((step, index) => {
          return (
            <FormStep
              id={step.id}
              label={step.label}
              stepPosition={index + 1}
              active={index === activeStepIndex}
              complete={step.complete}
              connectorStatus={step.connectorStatus}
              onClick={canNavigateToStep(index) ? () => handleActiveStepIndexChange(index) : undefined}
            />
          );
        })}
      </FormStepper>
      <div>
        <div style={styles.buttonContainer}>
          <Button
            id="back-btn"
            label="Back"
            disabled={!canNavigateToStep(activeStepIndex - 1)}
            onClick={() => handleActiveStepIndexChange(activeStepIndex - 1)}
          />
          <Button
            id="toggle-complete-btn"
            label={formSteps[activeStepIndex].complete ? 'Toggle Incomplete' : 'Toggle Complete'}
            onClick={() => handleToggleStepComplete(activeStepIndex)}
          />
          <Button
            id="next-btn"
            label="Next"
            disabled={!canNavigateToStep(activeStepIndex + 1)}
            onClick={() => handleActiveStepIndexChange(activeStepIndex + 1)}
          />
        </div>
        <div style={styles.buttonContainer}>
          <Button id="reset-btn" label="Reset" onClick={() => handleReset()} />
        </div>
      </div>
    </div>
   )
}`;

<CodeExample scope={scope} code={defaultCode} />

## Variations

### Non-linear

In a non-linear form stepper, users can jump to any step in the stepper by clicking on it, regardless of whether the previous step is complete. To make a form step clickable, pass it an `onClick`.

The connectors for a non-linear form stepper are not colored.

export const nonLinearCode = `() => {
  const styles = {
    container: { 
      width: '90%'
    },
    buttonContainer: {
      display: 'flex',
      justifyContent: 'center',
      gap: 10,
      paddingTop: 50,
    },
  };
  const textMap = { 
    ariaLabel: 'Non-Linear Form Stepper',
    stepXofY: 'Step {0} of {1}',
    completeLabel: 'Complete',
    partialLabel: 'In review',
    incompleteLabel: 'Incomplete',
  };
  const initialSteps = [
    {
      id: 'step-1',
      testId: 'step-1-test-id',
      label: 'Profile',
      connectorStatus: 'none',
    },
    {
      id: 'step-2',
      testId: 'step-2-test-id',
      label: 'Performance',
    },
    {
      id: 'step-3',
      testId: 'step-3-test-id',
      label: 'Benefits',
    },
    {
      id: 'step-4',  
      testId: 'step-4-test-id',
      label: 'Review Changes',
    },
  ];
  const [formSteps, setFormSteps] = useState(initialSteps);
  const [activeStepIndex, setActiveStepIndex] = useState(0);
  const handleToggleStepComplete = (index) => {
    const newFormSteps = [...formSteps];
    newFormSteps[index].complete = !newFormSteps[index].complete;
    setFormSteps(newFormSteps);
  };
  const handleReset = () => {
    setFormSteps(initialSteps);
    setActiveStepIndex(0);
  };
  return (
    <div style={styles.container}>
      <FormStepper id="non-linear-form-stepper-id" testId="non-linear-form-stepper-test-id" ariaLabel="Non-linear Form Stepper" textMap={textMap}>
        {formSteps.map((step, index) => {
          return (
            <FormStep
              id={step.id}
              label={step.label}
              stepPosition={index + 1}
              active={index === activeStepIndex}
              complete={step.complete}
              connectorStatus={step.connectorStatus}
              onClick={() => setActiveStepIndex(index)}
            />
          );
        })}
      </FormStepper>
      <div style={styles.buttonContainer}>
        <Button
          id="toggle-complete-btn"
          label={formSteps[activeStepIndex].complete ? 'Toggle Incomplete' : 'Toggle Complete'}
          onClick={() => handleToggleStepComplete(activeStepIndex)}
        />
        <Button id="reset-btn" label="Reset" onClick={() => handleReset()} />
        </div>
      </div>
   )
}`;

<CodeExample scope={scope} code={nonLinearCode} />

## Accessing Form Stepper using ref

Click on the Button to access Form Stepper, refer to the console for the element details.

export const refCode = `() => {
  const styles = {
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '90%',
      rowGap: '10px'
    }
  };
  const textMap = { 
    ariaLabel: 'Form Stepper Ref Example',
    stepXofY: 'Step {0} of {1}',
    completeLabel: 'Complete',
    partialLabel: 'In review',
    incompleteLabel: 'Incomplete',
  };
  const initialSteps = [
    {
      id: 'step-1',
      testId: 'step-1-test-id',
      label: '1. Start',
      connectorStatus: 'none',
    },
    {
      id: 'step-2',
      testId: 'step-2-test-id',
      label: '2. Continue',
    },
    {
      id: 'step-3',
      testId: 'step-3-test-id',
      label: '3. Almost there',
    }
  ];
  const formStepperRef = useRef(null);
  const [activeStepIndex, setActiveStepIndex] = useState(0);
  return (
     <div style={styles.column}>
      <Button id="access-element-btn" label="Click to access element" onClick={()=>{console.log(formStepperRef.current)}}/>
      <FormStepper ref={formStepperRef} id="form-stepper-ref-example-id" testId="form-stepper-ref-example-test-id" textMap={textMap}>
      {initialSteps.map((step, index) => {
        return (
          <FormStep
            id={step.id}
            label={step.label}
            stepPosition={index + 1}
            active={index === activeStepIndex}
            complete={step.complete}
            connectorStatus={step.connectorStatus}
            onClick={() => setActiveStepIndex(index)}
          />
        );
      })}
      </FormStepper>
    </div>
  );
}`;

<CodeExample scope={scope} code={refCode} />

## Usage Guidelines

1. **Use a form stepper for long forms.** Break up lengthy forms into smaller, digestible parts to reduce user fatigue.
2. **Provide context.** Clearly label each step to indicate its purpose, so users know what information is required at each stage.
3. **Save progress.** Allow users to save their progress, especially for forms that require substantial time to complete.
4. **Indicate required fields.** Clearly mark required fields and validate them before users move to the next step.

## Accessibility

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

The following values should be provided as part of the `textMap` prop:

| Label           | Description                                     | <div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div> |
| --------------- | ----------------------------------------------- | --------------------------------------------------------- |
| ariaLabel       | Aria label for Form Stepper                     | "Example Form Stepper"                                    |
| stepXofY        | Describes position of Form Step in Form Stepper | "Step `{0}` of `{1}`"                                     |
| incompleteLabel | Aria label for incomplete Form Step             | "Incomplete"                                              |
| partialLabel    | Aria label for partial Form Step                | "In review"                                               |
| completeLabel   | Aria label for complete Form Step               | "Complete"                                                |

The `ariaLabel` in `textMap` is optional, but should be provided if an id for `ariaLabelledBy` is not provided.
