# Form Stepper

## Summary

- Start Date: 2024-10-31
- Figma link: https://www.figma.com/design/yzd3fHCowfWDL4iTBAoJNv/branch/WMx1pijjtczLXiejL6GjdA/Component-Documentation-for-Designers?node-id=12137-29&p=f&m=dev

## Detailed Design

The Form Stepper component displays progress in the form of a sequence. It supports both horizontal and vertical orientations. There are two types of steppers: linear and non-linear. The linear type requires steps be completed in a sequence. Non-linear allows users to jump to any step in the stepper.

There are status states for each Form Step:

1. Incomplete: empty circle
2. Active: semi-tilted icon
3. Complete: filled check icon

## Prerequisite / Other Components

Form Stepper will not re-use Radio Button or the Divider component. There will instead be a sub-component similar to RadioButton just for FormStepper.

## API

### Form Stepper

1. **id**: `string`
   Sets id attribute on step.
1. **testId?**: `string`
   Sets **data-test-id** attribute on step.
1. **orientation?**: `TFormStepperOrientation`
   Sets the orientation of Stepper. Default is `'horizontal'`.
1. **ariaLabel?**: `string`
   Sets aria-label on form stepper.

```typescript
export type TFormStepperOrientation = 'horizontal' | 'vertical';
```

### Form Step

1. **id**: `string`
   Sets id attribute.
1. **testId?**: string;
   Sets **data-test-id** attribute on the element.
1. **label**: `string`
   Sets label on step indicator.
1. **active?**: `boolean`
   Sets step indicator to active state. Default is `false`.
1. **complete?**: `boolean`
   Sets step indicator to completed state. Default is `false`.
1. **connectorStatus?**: `TFormStepConnectorStatus`
   Sets status of step connector. Default is `"incomplete"`.
1. **onClick?**: `() => void`
   Sets a callback function that is executed when form step is clicked.

```typescript
export type TFormStepConnectorStatus = 'incomplete' | 'complete' | 'none';
```

## Usage

Basic usage of `FormStepper`:

```typescript
const initialSteps = [
  {
    id: 'step-1',
    testId: 'step-1-test-id',
    label: '1. Start',
    complete: false,
    connectorStatus: 'none',
  },
  {
    id: 'step-2',
    testId: 'step-2-test-id',
    label: '2. Continue',
    complete: false,
  },
  {
    id: 'step-3',
    testId: 'step-3-test-id',
    label: '3. Almost there',
    complete: false,
  },
  {
    id: 'step-4',
    testId: 'step-4-test-id',
    label: '4. End',
    complete: false,
  },
];

const [activeStepIndex, setActiveStepIndex] = React.useState(0);

<FormStepper id="example-form-stepper-id" testId="example-form-stepper-test-id">
  {formSteps.map((step, index) => {
    return (
      <FormStep
        id={step.id}
        label={step.label}
        index={index}
        active={index === activeStepIndex}
        complete={step.complete}
        connectorStatus={step.connectorStatus}
      />
    );
  })}
</FormStepper>;
```

## Accessibility

- Stepper will be an \<ol\> with each step being a \<li\>.
- If Step is current, set `aria-current="step"` on it. Else, set `aria-current="false"` on Step.
- Each step will have `role="listitem"`.
- Use `FocusRing` for each step.
- To navigate to a different step, use arrow keys.
- Tab out of Form Stepper to reach the active step's content.
- A step can be selected using the <kbd>Enter</kbd> or <kbd>Space</kbd> key.

## Q&A

**What is the min and max number of steps?**
Design's guideline is to have a minimum of 2 steps and a max of 5 steps. This min/max will not be enforced in our code but this guideline will be outlined in Storybook.

**Can users navigate to each step by clicking on the step?**
Yes, a step is clickable if an `onClick` prop is passed into it.

**Is Form Stepper responsive?**
Yes, on large screens it will have a horizontal orientation. On smaller screens, it'll take on a vertical orientation. It will be up to the consumer to determine when to use the appropriate layout. The layout can be changed using the `orientation` prop.

**Are the number orderings in the labels baked into each Form Step?**
No, teams should pass in the numbers as part their steps' labels.

**Will we track if a step has been interacted with?**
No, consumers can track interaction states from their side (ex. active, complete, connectorStatus).

**What happens if a step was navigated to but not completed and user navigates to a previous step?**
A step that has been interacted with will have a partial state step indicator and incomplete connector.

## Other Design Systems

**Material UI** - https://mui.com/material-ui/react-stepper/#non-linear

- Offers linear & non-linear options
- Step indicators include number labels

**Material Angular** - https://material.angular.io/cdk/stepper/api

- Nice API, considers different step states (ex. interacted, error)

**Carbon** - https://carbondesignsystem.com/components/progress-indicator/usage/

- Includes descriptions for each step underneath the indicator

**Ontario Design System** - https://designsystem.ontario.ca/components/detail/step-indicator.html

- Actually opposes using the typical step style indicators
- Uses back and next buttons to navigate through steps instead

**PatternFly** - https://www.patternfly.org/components/progress-stepper/

- Sets aria-label on each step as well as on stepper

## Required PBIs

1. [Create Form Stepper component](https://dayforce.atlassian.net/browse/PWEB-16914)
1. [Add center alignment](https://dayforce.atlassian.net/browse/PWEB-17431)
1. [Add linear prop](https://dayforce.atlassian.net/browse/PWEB-17430)
1. [Add error variant to FormStep](https://dayforce.atlassian.net/browse/PWEB-17429)
1. [Create Vertical Stepper](https://dayforce.atlassian.net/browse/PWEB-17126)
1. [Create hook for Step states](https://dayforce.atlassian.net/browse/PWEB-17240)
1. [Storybook Documentation](https://dayforce.atlassian.net/browse/PWEB-16910)
1. [A11y](https://dayforce.atlassian.net/browse/PWEB-16911)
1. [Tests](https://dayforce.atlassian.net/browse/PWEB-16912)

   - Unit
   - Visual
   - Pa11y
   - Playwright
   - Manual (screen reader)

1. [Add Form Stepper to Ref App](https://dayforce.atlassian.net/browse/PWEB-16915)
1. [Push to production](https://dayforce.atlassian.net/browse/PWEB-16913)
