import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { FormStepper } from './form-stepper';
import { FormStep } from '../form-step';
import { expect, screen, userEvent } from '@storybook/test';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Form Stepper"
  component={FormStepper}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: {
        breakpointXs: { disable: true }, // breakpointXs is smaller than Form Stepper min width, use breakpointSm instead
        breakpointSm: defaultModes['breakpointSm'],
      },
    },
  }}
  args={{
    id: 'form-stepper-id',
    testId: 'form-stepper-test-id',
    textMap: {
      ariaLabel: 'Example Form Stepper',
      stepXofY: 'Step {0} of {1}',
      completeLabel: 'Complete',
      partialLabel: 'In review',
      incompleteLabel: 'Incomplete',
    },
  }}
/>

# Form Stepper

## Live Demo

export const linearSteps = [
  {
    id: 'step-1-id',
    testId: 'step-1-test-id',
    label: '1. Create Account',
    complete: true,
    connectorStatus: 'none',
  },
  {
    id: 'step-2-id',
    testId: 'step-2-test-id',
    label: '2. Add Personal Info',
    active: true,
    connectorStatus: 'complete',
  },
  {
    id: 'step-3-id',
    testId: 'step-3-test-id',
    label: '3. Adjust Settings',
  },
  {
    id: 'step-4-id',
    testId: 'step-4-test-id',
    label: '4. Review',
  },
];
export const nonLinearSteps = [
  {
    id: 'step-1-id',
    testId: 'step-1-test-id',
    label: 'Create Account',
    complete: true,
    connectorStatus: 'none',
  },
  {
    id: 'step-2-id',
    testId: 'step-2-test-id',
    label: 'Add Personal Info',
    active: true,
  },
  {
    id: 'step-3-id',
    testId: 'step-3-test-id',
    label: 'Adjust Settings',
  },
  {
    id: 'step-4-id',
    testId: 'step-4-test-id',
    label: 'Review',
  },
];

export const renderFormStepper = (args, isLinear, steps) => {
  return (
    <FormStepper {...args}>
      {steps.map((step, index) => {
        return (
          <FormStep
            id={step.id}
            testId={step.testId}
            label={step.label}
            stepPosition={index + 1}
            active={step.active}
            complete={step.complete}
            connectorStatus={step.connectorStatus}
            onClick={!isLinear ? () => {} : undefined} // non-linear steps to be clickable/focusable
          />
        );
      })}
    </FormStepper>
  );
};

<Canvas>
  <Story name="Default">
    {({ ...args }) => {
      return renderFormStepper(args, true, linearSteps);
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Non-Linear">
    {({ ...args }) => {
      return renderFormStepper(args, false, nonLinearSteps);
    }}
  </Story>
</Canvas>
