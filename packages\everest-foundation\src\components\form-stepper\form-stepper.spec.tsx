import React, { ReactElement } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { FormStep, IFormStep } from '../form-step/form-step';

import { FormStepper } from '.';

const id = 'form-stepper-id';
const testId = 'form-stepper-test-id';
const textMap = {
  ariaLabel: 'Example Form Stepper',
  stepXofY: 'Step {0} of {1}',
  completeLabel: 'Complete',
  partialLabel: 'In review',
  incompleteLabel: 'Incomplete',
};
const linearSteps = [
  {
    id: 'step-1-id',
    testId: 'step-1-test-id',
    label: '1. Create Account',
    complete: true,
    connectorStatus: 'none',
  },
  {
    id: 'step-2-id',
    testId: 'step-2-test-id',
    label: '2. Add Personal Info',
    active: true,
    connectorStatus: 'complete',
  },
  {
    id: 'step-3-id',
    testId: 'step-3-test-id',
    label: '3. Adjust Settings',
  },
  {
    id: 'step-4-id',
    testId: 'step-4-test-id',
    label: '4. Review',
  },
];
const nonLinearSteps = [
  {
    id: 'step-1-id',
    testId: 'step-1-test-id',
    label: 'Create Account',
    complete: true,
    connectorStatus: 'none',
  },
  {
    id: 'step-2-id',
    testId: 'step-2-test-id',
    label: 'Add Personal Info',
    active: true,
  },
  {
    id: 'step-3-id',
    testId: 'step-3-test-id',
    label: 'Adjust Settings',
  },
  {
    id: 'step-4-id',
    testId: 'step-4-test-id',
    label: 'Review',
  },
];

const defaultProps = {
  id,
  testId,
  textMap,
};

const getStepper = () => screen.getByTestId(`${testId}`);
const getStep = (stepTestId: string) => screen.getByTestId(`${stepTestId}-button`);

export const renderFormStepper = (steps: any, activeStepIndex: number, isLinear: boolean): ReactElement => {
  const canNavigateToStep = (index: number, isLinear: boolean) => {
    if (index < 0 || index >= steps.length) {
      return false;
    }
    if (!isLinear || index <= activeStepIndex || steps[index].complete || steps[index - 1]?.complete) {
      return true;
    }
    return false;
  };
  return (
    <FormStepper {...defaultProps}>
      {steps.map((step: IFormStep, index: number) => {
        return (
          // eslint-disable-next-line react/jsx-key
          <FormStep
            key={step.id}
            id={step.id}
            testId={step.testId}
            label={step.label}
            stepPosition={index + 1}
            active={step.active}
            complete={step.complete}
            connectorStatus={step.connectorStatus}
            onClick={canNavigateToStep(index, isLinear) ? jest.fn() : undefined} // non-linear steps to be clickable/focusable
          />
        );
      })}
    </FormStepper>
  );
};

describe('[Form Stepper]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('[Linear]', () => {
    it('should be able to tab to any step that has an onClick', async () => {
      render(renderFormStepper(linearSteps, 1, true));

      expect(getStepper()).toBeInTheDocument();

      await userEvent.tab();
      expect(getStep(linearSteps[0].testId)).toHaveClass('evrFocusRingVisible');

      await userEvent.tab();
      expect(getStep(linearSteps[1].testId)).toHaveClass('evrFocusRingVisible');

      // last 2 steps are not tabbable
      await userEvent.tab();
      expect(getStep(linearSteps[2].testId)).not.toHaveClass('evrFocusRingVisible');

      await userEvent.tab();
      expect(getStep(linearSteps[3].testId)).not.toHaveClass('evrFocusRingVisible');
    });
  });

  describe('[Non-Linear]', () => {
    it('should be able to tab to any step that has an onClick', async () => {
      render(renderFormStepper(nonLinearSteps, 1, false));

      expect(getStepper()).toBeInTheDocument();

      await userEvent.tab();
      expect(getStep(nonLinearSteps[0].testId)).toHaveClass('evrFocusRingVisible');

      await userEvent.tab();
      expect(getStep(nonLinearSteps[1].testId)).toHaveClass('evrFocusRingVisible');

      await userEvent.tab();
      expect(getStep(nonLinearSteps[2].testId)).toHaveClass('evrFocusRingVisible');

      await userEvent.tab();
      expect(getStep(nonLinearSteps[3].testId)).toHaveClass('evrFocusRingVisible');
    });
  });
});
