import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { FormStepper } from './form-stepper';
import { FormStep } from '../form-step';
import { Button } from '../button';
import { Switch } from '../switch';
import { action } from '@storybook/addon-actions';
import Examples from './form-stepper.examples.mdx';
import { useState, useEffect } from 'react';

<Meta
  title="Components/Form Stepper/Form Stepper"
  component={FormStepper}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/Everest-Web?node-id=67770-32303&m=dev',
    },
  }}
  args={{
    id: 'form-stepper-id',
    testId: 'form-stepper-test-id',
    textMap: {
      ariaLabel: 'Example Form Stepper',
      stepXofY: 'Step {0} of {1}',
      completeLabel: 'Complete',
      partialLabel: 'In review',
      incompleteLabel: 'Incomplete',
    },
  }}
/>

# Form Stepper

<Examples />

## Live Demo

<Canvas>
  <Story name="Form Stepper">
    {(args) => {
      const styles = {
        switch: {
          display: 'flex',
          justifyContent: 'start',
          paddingTop: 10,
          paddingBottom: 40,
        },
        buttonContainer: {
          display: 'flex',
          justifyContent: 'center',
          gap: 10,
          paddingTop: 50,
        },
      };
      const initialLinearSteps = [
        {
          id: 'linear-step-1-id',
          testId: 'linear-step-1-test-id',
          label: '1. Create Account',
          connectorStatus: 'none',
        },
        {
          id: 'linear-step-2-id',
          testId: 'linear-step-2-test-id',
          label: '2. Add Personal Info',
        },
        {
          id: 'linear-step-3-id',
          testId: 'linear-step-3-test-id',
          label: '3. Adjust Settings',
        },
        {
          id: 'linear-step-4-id',
          testId: 'linear-step-4-test-id',
          label: '4. Review',
        },
      ];
      const initialNonLinearSteps = [
        {
          id: 'non-linear-step-1-id',
          testId: 'non-linear-step-1-test-id',
          label: 'Profile',
          connectorStatus: 'none',
        },
        {
          id: 'non-linear-step-2-id',
          testId: 'non-linear-step-2-test-id',
          label: 'Performance',
        },
        {
          id: 'non-linear-step-3-id',
          testId: 'non-linear-step-3-test-id',
          label: 'Benefits',
        },
        {
          id: 'non-linear-step-4-id',
          testId: 'non-linear-step-4-test-id',
          label: 'Review',
        },
      ];
      const [linear, setLinear] = useState(true);
      const [formSteps, setFormSteps] = useState(initialLinearSteps);
      const [activeStepIndex, setActiveStepIndex] = useState(0);
      const handleToggleStepComplete = (index) => {
        const newFormSteps = [...formSteps];
        newFormSteps[index].complete = !newFormSteps[index].complete;
        setFormSteps(newFormSteps);
      };
      const canNavigateToStep = (index) => {
        // Out of bounds
        if (index < 0 || index >= formSteps.length) {
          return false;
        }
        // 1. Non-linear
        // 2. Any completed step is ok
        // 3. Next step is OK as long as active step is complete
        if (!linear || index <= activeStepIndex || formSteps[index].complete || formSteps[index - 1]?.complete) {
          return true;
        }
        return false;
      };
      const handleActiveStepIndexChange = (newActiveStepIndex) => {
        if (activeStepIndex === newActiveStepIndex || !canNavigateToStep(newActiveStepIndex)) return;
        if (linear) {
          const newFormSteps = [...formSteps];
          if (newActiveStepIndex > activeStepIndex) {
            // Next step
            newFormSteps[newActiveStepIndex].connectorStatus = 'complete';
          } else if (activeStepIndex !== 0 && !newFormSteps[activeStepIndex].complete) {
            // Undo connector to previous step
            newFormSteps[activeStepIndex].connectorStatus = 'incomplete';
          }
          setFormSteps(newFormSteps);
        }
        setActiveStepIndex(newActiveStepIndex);
        action('onActiveStepIndexChange')(newActiveStepIndex);
      };
      const handleReset = () => {
        setFormSteps(linear ? initialLinearSteps : initialNonLinearSteps);
        setActiveStepIndex(0);
      };
      const handleLinearSwitch = (e) => {
        setLinear(e);
        handleReset();
      };
      useEffect(() => {
        setFormSteps(linear ? initialLinearSteps : initialNonLinearSteps);
      }, [linear]);
      return (
        <>
          <div style={styles.switch}>
            <Switch checked={linear} onChange={handleLinearSwitch} label="Linear Stepper" />
          </div>
          <FormStepper {...args}>
            {formSteps.map((step, index) => {
              return (
                <FormStep
                  id={step.id}
                  testId={step.testId}
                  label={step.label}
                  stepPosition={index + 1}
                  active={index === activeStepIndex}
                  complete={step.complete}
                  connectorStatus={step.connectorStatus}
                  onClick={canNavigateToStep(index) ? () => handleActiveStepIndexChange(index) : undefined}
                />
              );
            })}
          </FormStepper>
          {linear ? (
            <div>
              <div style={styles.buttonContainer}>
                <Button
                  id="linear-back-btn"
                  label="Back"
                  disabled={!canNavigateToStep(activeStepIndex - 1)}
                  onClick={() => handleActiveStepIndexChange(activeStepIndex - 1)}
                />
                <Button
                  id="linear-toggle-complete-btn"
                  label={formSteps[activeStepIndex].complete ? 'Toggle Incomplete' : 'Toggle Complete'}
                  onClick={() => handleToggleStepComplete(activeStepIndex)}
                />
                <Button
                  id="linear-next-btn"
                  label="Next"
                  disabled={!canNavigateToStep(activeStepIndex + 1)}
                  onClick={() => handleActiveStepIndexChange(activeStepIndex + 1)}
                />
              </div>
              <div style={styles.buttonContainer}>
                <Button id="linear-reset-btn" label="Reset" onClick={() => handleReset()} />
              </div>
            </div>
          ) : (
            <div style={styles.buttonContainer}>
              <Button
                id="non-linear-toggle-complete-btn"
                label={formSteps[activeStepIndex].complete ? 'Toggle Incomplete' : 'Toggle Complete'}
                onClick={() => handleToggleStepComplete(activeStepIndex)}
              />
              <Button id="non-linear-reset-btn" label="Reset" onClick={() => handleReset()} />
            </div>
          )}
        </>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Form Stepper" />
