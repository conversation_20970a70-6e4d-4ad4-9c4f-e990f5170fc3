import React, { Children, forwardRef, PropsWithChildren, useMemo } from 'react';

import { FormStepperContext } from './form-stepper-context';

import styles from './form-stepper.module.scss';

export interface IFormStepperTextMap {
  /** Sets the `aria-label` attribute on form stepper. */
  ariaLabel?: string;
  /** Describes step position in form stepper. */
  stepXofY: string;
  /** Sets the incomplete step SR label on form stepper. */
  incompleteLabel: string;
  /** Sets the partial step SR label on form stepper. */
  partialLabel: string;
  /** Sets the complete step SR label on form stepper. */
  completeLabel: string;
}

export interface IFormStepperProps {
  /** Sets the `id` attribute on form stepper. */
  id: string;
  /** Sets the `data-testid` attribute on form stepper. */
  testId?: string;
  /** Sets the `aria-labelledby` attribute on form stepper. */
  ariaLabelledBy?: string;
  /** Sets aria labels for form stepper. */
  textMap: IFormStepperTextMap;
}

export const FormStepper = forwardRef<HTMLOListElement, PropsWithChildren<IFormStepperProps>>(
  (props: PropsWithChildren<IFormStepperProps>, ref) => {
    const { id, testId, ariaLabelledBy, textMap, children } = props;
    const totalSteps = Children.toArray(children).length;
    const formStepperContext = useMemo(() => ({ textMap, totalSteps }), [textMap, totalSteps]);

    return (
      <FormStepperContext.Provider value={formStepperContext}>
        {/* <ul> used instead of <ol> bc numbering is not built into Stepper, rather passed by user as part of label */}
        <ul
          ref={ref}
          id={id}
          data-testid={testId}
          aria-label={ariaLabelledBy ? undefined : textMap?.ariaLabel}
          aria-labelledby={ariaLabelledBy}
          className={styles.evrFormStepper}
        >
          {children}
        </ul>
      </FormStepperContext.Provider>
    );
  }
);

FormStepper.displayName = 'FormStepper';
