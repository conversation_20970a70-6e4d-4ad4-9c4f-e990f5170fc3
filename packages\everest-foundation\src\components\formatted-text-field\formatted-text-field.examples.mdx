import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { FormattedTextField } from './formatted-text-field';
import { <PERSON><PERSON> } from '../button';
import { AlphaBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';

<AlphaBanner />

export const scope = { Button, FormattedTextField };

### FormattedTextField With Postal code format

This is an example of potential usage of FormattedTextField component.
As user enters spaces will appear to format the text into postal code, after it loses focus.

export const formatTextFieldInPostalCode = `() => {
    const [value, setValue] = React.useState('');
    const [message, setMessage] = React.useState('Enter postal code in l2p 4h4')
    const [status, setStatus] = React.useState("default")
    const validatePostalCode = (val) => {
      return val.match(/^[A-Za-z]\\d[A-Za-z][ ]?\\d[A-Za-z]\\d$/)
    }
    const handleChange = (val) => { 
      setValue(val)
    };
    const handleBlur = () => {
      if (!value) {
        setStatus("default")
        setMessage("");
        return;
      }
      if (validatePostalCode(value)) {
        setStatus("default")
        setMessage(null)
      } else {
        setStatus("error")
        setMessage("Enter postal code in l2p 4h4")
      }
    }
    const formatPostalCode = (postal) => {
      if (validatePostalCode(postal)) {
        return postal.slice(0,3) + ' ' + postal.slice(-3)
      }
      return postal;
    }
    return (
        <FormattedTextField
            label="Enter your Postal Code" 
            onChange={handleChange}
            onBlur={handleBlur}
            value={value} 
            status={status}
            statusMessagePrefix={status === "error" ? "Error:" : undefined}
            statusMessage={message}
            id={'postal-blur-box'}
            onConvertToFormatString={(val) => {
                return formatPostalCode(val.toString());
            }}
        />   
    );   
}`;

<CodeExample scope={scope} code={formatTextFieldInPostalCode} />

### FormattedTextField With SIN Number format

This is an example of potential usage of FormattedTextField component.
As user enters spaces will appear to format the digits into SIN digits, for nine digits numbers.

export const formatTextFieldInSINCode = `() => {
    const [value, setValue] = React.useState('');
    const handleChange = (val) => setValue(val);
    const splitSIN = (sin) => {
      const newFormat = sin
        .match(/(\\d{1,3})? ?(\\d{1,3})? ?(\\d{1,3})?/)
        .slice(1)
        .filter(Boolean)
        .join(" ");
      return newFormat
    }
    return (
        <FormattedTextField
          label="Enter your SIN" 
          onChange={handleChange} 
          value={value} 
          id="sin-box"
          onConvertToFormatString={(val) => {
              return splitSIN(val);
          }}
        />   
    );   
}`;

<CodeExample scope={scope} code={formatTextFieldInSINCode} />

### Accessing FormattedTextField using ref

Click on the Button to access the FormattedTextField, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '0.625rem'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    const [value, setValue] = React.useState('');
    const [message, setMessage] = React.useState('Enter postal code in l2p 4h4')
    const [status, setStatus] = React.useState("error")
    const validatePostalCode = (val) => {
      return val.match(/^[A-Za-z]\\d[A-Za-z][ ]?\\d[A-Za-z]\\d$/)
    }
    const handleChange = (val) => { 
      setValue(val)
    };
    const handleBlur = () => {
      if (!value) {
        setStatus("default")
        setMessage("");
        return;
      }
      if (validatePostalCode(value)) {
        setStatus("default")
        setMessage(null)
      } else {
        setStatus("error")
        setMessage("Enter postal code in l2p 4h4")
      }
    }
    const formatPostalCode = (postal) => {
      if (validatePostalCode(postal)) {
        return postal.slice(0,3) + ' ' + postal.slice(-3)
      }
      return postal;
    }
    return (
        <Column>
            <Row>
                <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
            </Row>
            <Row>
              <FormattedTextField
                ref={ref} 
                label="Enter your Postal Code" 
                onChange={handleChange}
                onBlur={handleBlur}
                value={value} 
                status={status}
                statusMessagePrefix={status === "error" ? "Error:" : undefined}
                statusMessage={message}
                id={'postal-blur-box'}
                onConvertToFormatString={(val) => {
                    return formatPostalCode(val.toString());
                }}
              /> 
            </Row>
        </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Use `<FormattedTextField/>` component whenever formatted text field values need to be entered, remember this just behaves like a `TextField` component when a user is typing.
Please pass `onConvertToFormatString` to ensure that the component can format the text as intended upon onBlur or when the user clicks away.

## Accessibility

TODO
