import React, { useState } from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { FormattedTextField } from './formatted-text-field';

const onChange = jest.fn().mockImplementation((newValue: string, setValue) => act(() => setValue(newValue)));

interface IRenderControlledFormattedTextField {
  labelText: string;
  id: string;
  testId?: string;
  experimentalFormatOnType: boolean;
  onConvertToFormatString: (value: string) => string;
}

const renderControlledFormattedTextField = (params: IRenderControlledFormattedTextField) => {
  const { labelText, id, testId, experimentalFormatOnType, onConvertToFormatString } = params;

  const FormattedTextFieldWrapper = () => {
    const [value, setValue] = useState<string>('');

    return (
      <FormattedTextField
        label={labelText}
        id={id}
        testId={testId}
        value={value}
        experimentalFormatOnType={experimentalFormatOnType}
        onConvertToFormatString={onConvertToFormatString}
        onChange={(newValue) => onChange(newValue, setValue)}
      />
    );
  };

  render(<FormattedTextFieldWrapper />);
};

describe('[FormattedTextField]', () => {
  const testId = 'formatted-text-field-test-id';
  const labelText = 'This is a label';
  const id = 'formatted-text-field-id';

  const onConvertToFormatString = (postal: string) => {
    if (postal.length <= 3 && postal.match(/^[A-Za-z]\d[A-Za-z]$/)) {
      return postal.concat('-');
    }
    return postal;
  };

  const formatPostalCode = (postal: string) => {
    if (postal.length === 6 && postal.match(/^[A-Za-z]\d[A-Za-z]\d[A-Za-z]\d$/)) {
      return postal.slice(0, 3) + '-' + postal.slice(3);
    }
    return postal;
  };

  const user = userEvent.setup();
  const getTextField = () => screen.getByRole('textbox');

  beforeEach(onChange.mockClear);

  // Very flaky, fails on value assertion: expects 'LIP-P', but gets 'LIPP-'
  it.skip('renders formatted values as values are entered when experimentalFormatOnType is set to true and custom functions are provided', async () => {
    renderControlledFormattedTextField({
      labelText,
      id,
      testId,
      experimentalFormatOnType: true,
      onConvertToFormatString,
    });

    await user.tab();
    await waitFor(() => expect(getTextField()).toHaveFocus());

    await user.type(getTextField(), 'L1PP');
    await waitFor(() => expect(getTextField()).toHaveValue('L1P-P'), { timeout: 2000 });
  });

  it('should render formatted values upon on blur when experimentalFormatOnType is set to false and behaves like a regular text field', async () => {
    renderControlledFormattedTextField({
      labelText,
      id,
      testId,
      experimentalFormatOnType: true,
      onConvertToFormatString: formatPostalCode,
    });

    await user.type(getTextField(), 'L1P1s4');
    await user.tab(); // blur
    await waitFor(() => expect(getTextField()).toHaveValue('L1P-1s4'));
  });

  it('should render formatted values when given an initial value without onFocus and onBlur', async () => {
    render(
      <FormattedTextField
        label={labelText}
        testId={testId}
        id={id}
        value="L1P1s4"
        onConvertToFormatString={formatPostalCode}
      />
    );

    await waitFor(() => expect(getTextField()).toHaveValue('L1P-1s4'));
  });
});
