import { useState } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { FormattedTextField } from './formatted-text-field';
import Examples from './formatted-text-field.examples.mdx';
import { action } from '@storybook/addon-actions';
import { ICON_NAMES } from '../icon';
import { useArgs } from '@storybook/client-api';

<Meta
  title="Everest Labs/Components/Text Fields/Formatted Text Field"
  component={FormattedTextField}
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: '',
    },
  }}
  argTypes={{
    onConvertToFormatString: {
      description: 'Takes the input value and returns a formatted string',
    },
    value: {
      control: 'text',
      description: 'Accepts the value of the formatted text field.',
    },
    experimentalFormatOnType: {
      type: 'boolean',
      description: 'Sets the experimentalFormatOnType prop value. Allows the user to input in the formatted',
      table: {
        defaultValue: { summary: false },
      },
    },
    id: {
      description: 'Specifies the id attribute of the formatted text field.',
    },
    testId: {
      description: 'An id used for automation testing.',
    },
    required: {
      type: 'boolean',
      description: 'Sets the required attribute on the formatted text field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    ariaDescribedBy: {
      description:
        'Specifies the id of the parapraph (or other similar element) which will be used to provide the description of the text input.',
    },
    ariaLabel: {
      description: 'Accessible label to describe formatted text field.',
    },
    autocomplete: {
      type: 'enum',
      control: 'radio',
      options: ['off', 'on'],
      description: 'Sets the autocomplete attribute on the formatted text field.',
      table: {
        defaultValue: { summary: 'off' },
      },
    },
    disabled: {
      type: 'boolean',
      description: 'Sets the disabled attribute on the formatted text field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    helperText: {
      description: 'Sets the helper text rendered under the formatted text field.',
    },
    helperTextPrefix: {
      description: 'Sets the prefix to the helper text rendered under the formatted text field.',
    },
    iconName: {
      type: { name: 'enum' },
      control: 'select',
      options: ['', ...ICON_NAMES.filter((name) => name !== 'search')].sort(),
      description: 'Sets the name of the icon. Search icon should not be used in the formatted text field.',
    },
    inputMode: {
      description: 'Sets the data type hint for the formatted text field',
    },
    label: {
      description: 'Specifies the label rendered in the formatted text field.',
    },
    maxLength: {
      description: 'Specifies the maximum value to be allowed in the input.',
    },
    name: {
      description: 'Sets the name attribute on the formatted text field.',
    },
    onChange: {
      control: '-',
      description: 'Accepts the callback function for the onchange event.',
    },
    onFocus: {
      control: '-',
      description: 'Accepts the callback function for the onfocus event.',
    },
    onBlur: {
      control: '-',
      description: 'Accepts the callback function for the onblur event.',
    },
    readOnly: {
      type: 'boolean',
      description: 'Sets the readonly attribute on the formatted text field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    status: {
      type: 'enum',
      control: 'radio',
      options: ['default', 'error', 'success'],
      description: 'Sets the status of the formatted text field.',
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    statusMessage: {
      description: 'Sets the status message rendered under the formatted text field.',
    },
    statusMessagePrefix: {
      description: 'Sets the prefix to the status message rendered under the formatted text field.',
    },
  }}
  args={{
    label: 'Entered text will be capitalized',
    experimentalFormatOnType: false,
    id: 'formatted-text-field-id',
    testId: 'test-id',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    autocomplete: 'off',
    status: 'default',
    disabled: false,
    readOnly: false,
    required: true,
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',
    helperText: 'This is some helper text',
    helperTextPrefix: 'Hint:',
    name: 'test-name',
    inputMode: 'text',
  }}
/>

# Formatted Text Field

<Examples />

## Live Demo

<Canvas>
  <Story name="Formatted Text Field">
    {(args) => {
      const [{ value }, updateArgs] = useArgs();
      const handleChange = (value) => {
        action('onChange')(value);
        updateArgs({ value });
      };
      return (
        <FormattedTextField
          {...args}
          label={args.label}
          experimentalFormatOnType={args.experimentalFormatOnType}
          onConvertToFormatString={(val) => (val ? val.toUpperCase() : undefined)}
          onChange={handleChange}
          value={value}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Formatted Text Field" />
