import React, { useState, useEffect } from 'react';

import { ITextFieldProps, TextField } from '../text-field';

export interface IFormattedTextField extends Omit<ITextFieldProps, 'onChange' | 'value' | 'type'> {
  // Experimental feature, not production ready
  experimentalFormatOnType?: boolean;
  onConvertToFormatString: (value: string) => string;
  onChange?: (value: string) => void;
  value: string;
}

export const FormattedTextField = React.forwardRef<HTMLInputElement, IFormattedTextField>((props, ref) => {
  const {
    value,
    onChange,
    onBlur,
    onFocus,
    experimentalFormatOnType = false,
    onConvertToFormatString,
    name,
    label,
    maxLength,
    disabled,
    readOnly,
    required,
    iconName,
    status,
    statusMessage,
    statusMessagePrefix,
    helperText,
    helperTextPrefix,
    id,
    autocomplete,
    testId,
    ariaLabel,
    ariaDescribedBy,
    inputMode,
  } = props;
  const [displayValue, setDisplayValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const onValueChange = (val: string) => {
    onChange?.(val);
  };

  const handleBlur = (e: React.FocusEvent<HTMLElement>) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleFocus = (e: React.FocusEvent<HTMLElement>) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const getValue = () => {
    return isFocused && !experimentalFormatOnType ? value : displayValue;
  };

  useEffect(() => {
    if (!isFocused || experimentalFormatOnType) {
      setDisplayValue(onConvertToFormatString?.(value) || value);
    }
  }, [onConvertToFormatString, value, isFocused, experimentalFormatOnType]);

  return (
    <TextField
      name={name}
      label={label}
      maxLength={maxLength}
      disabled={disabled}
      readOnly={readOnly}
      required={required}
      iconName={iconName}
      status={status}
      statusMessage={statusMessage}
      statusMessagePrefix={statusMessagePrefix}
      helperText={helperText}
      helperTextPrefix={helperTextPrefix}
      id={id}
      autocomplete={autocomplete}
      testId={testId}
      ariaLabel={ariaLabel}
      ariaDescribedBy={ariaDescribedBy}
      inputMode={inputMode}
      value={getValue()}
      ref={ref}
      onChange={(val: string) => onValueChange(val)}
      onBlur={(e: React.FocusEvent<HTMLElement>) => handleBlur(e)}
      onFocus={(e: React.FocusEvent<HTMLElement>) => handleFocus(e)}
    />
  );
});

FormattedTextField.displayName = 'FormattedTextField';
