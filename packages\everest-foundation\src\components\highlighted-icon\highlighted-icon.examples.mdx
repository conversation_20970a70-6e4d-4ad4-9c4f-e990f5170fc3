import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { HighlightedIcon } from './highlighted-icon';

export const scope = { HighlightedIcon };

The `HighlightedIcon` component is a simple decorative component that renders an <LinkTo kind="Components/Icons/Icon">Icon</LinkTo> with a specified color scheme.

## Fill

By default, the fill of HighlightedIcon is `neutral`. You can change the fill by setting the name of the `fill`.

export const fillCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            columnGap: '10px'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
            <HighlightedIcon id='fill-neutral' iconName='file' fill='neutral' />
            <HighlightedIcon id='fill-blue' iconName='file' fill='blue' />
            <HighlightedIcon id='fill-green' iconName='file' fill='green' />
            <HighlightedIcon id='fill-red' iconName='file' fill='red' />
            <HighlightedIcon id='fill-yellow' iconName='file' fill='yellow' />
        </Row>
    );

}`;

<CodeExample scope={scope} code={fillCode} />

## Accessibility

`HighlightedIcon` is a decorative component that is not focusable and not accessible to screen readers. The aria-hidden attribute is set to true.
