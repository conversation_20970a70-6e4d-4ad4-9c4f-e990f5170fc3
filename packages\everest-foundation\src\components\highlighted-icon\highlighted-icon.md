# HighlightedIcon

## Summary

Research and documenting on the Everest `HighlightedIcon` Component was made based on the Figma file:
https://www.figma.com/design/3U0GoCKpRdEkwu859tEXBR/Pattern-Component-Dependencies?node-id=1-7814&t=Tr92HIjwM8agG4uo-1

## Design

The `HighlightedIcon` component is a simple decorative component that renders an `<Icon/>` with a specified background color. It supports various sizes and color schemes.

## API

1. **size?**: `IHighlightedIconSize` {container: `string`, icon: `TIconSize`}

   Sets the size of the for the background container and icon.

   Default: {container: `--evr-size-2xl`, icon: `md`} (48x48px | 4x4rem)

2. **fill?**: `THighlightedIconFillPreset` | `IHighlightedIconFill` {container: `TIconColor`, icon: `TIconColor`}

   Sets the fill for the background container and icon. Default value is `neutral`.

   You can also customize the fill for the container and icon by setting `IHighlightedIconFill`. for example:

   {container: `--evr-surfaces-tertiary-default`, icon: `--evr-content-primary-highemp`}

3. **iconName**: `TIconName`  
   Sets the icon of TIconName to be displayed.
4. **testId?**: `undefined | string`  
   Sets **data-testid** attribute on the HighlightedIcon.
5. **id**: `string`  
   Sets the id of the HighlightedIcon.

## Usage

Basic usage of `HighlightedIcon`

```typescript
type TIHighlightedIconFillFillPreset = 'neutral' | 'blue' | 'green' | 'red' | 'yellow';

<HighlightedIcon id="HighlightedIcon" iconName="file" />;
```

## Accessibility

- follow the same accessibility guideline from `<Icon/>`

## PBIs

1. [Create HighlightedIcon Arch doc](https://dayforce.atlassian.net/browse/PWEB-18757)

2. [Create HighlightedIcon component](https://dayforce.atlassian.net/browse/PWEB-18942)

3. [Create HighlightedIcon Tests](https://dayforce.atlassian.net/browse/PWEB-19374)

## Acceptance Criteria

- Component to be named `<HighlightedIcon>`
- Build a component and setup in Storybook (Components, Foundations, Automation)
- Styles are on par with Design specs in Figma
- Verify the API works as intended
  - size
  - fill
  - name

## Changelog
