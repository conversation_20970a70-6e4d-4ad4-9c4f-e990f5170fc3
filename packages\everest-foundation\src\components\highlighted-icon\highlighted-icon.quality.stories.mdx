import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';
import { HighlightedIcon } from '../highlighted-icon';

<Meta
  title="Testing/Automation Test Cases/HighlightedIcon"
  component={HighlightedIcon}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'highlighted-icon-id',
    iconName: 'file',
  }}
/>

# HighlightedIcon

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => (
      <div style={{ display: 'flex', gap: '20px' }}>
        <HighlightedIcon {...args} id={`${args.id}-fill-neutral`} fill="neutral" />
        <HighlightedIcon {...args} id={`${args.id}-fill-blue`} fill="blue" />
        <HighlightedIcon {...args} id={`${args.id}-fill-green`} fill="green" />
        <HighlightedIcon {...args} id={`${args.id}-fill-red`} fill="red" />
        <HighlightedIcon {...args} id={`${args.id}-fill-yellow`} fill="yellow" />
      </div>
    )}
  </Story>
</Canvas>
