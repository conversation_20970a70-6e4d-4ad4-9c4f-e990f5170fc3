import React from 'react';
import { render, screen } from '@testing-library/react';

import { HighlightedIcon, IHighlightedIconProps } from './highlighted-icon';

describe('[HighlightedIcon]', () => {
  const testId = 'highlighted-icon-test-id';
  const defaultProps: IHighlightedIconProps = {
    id: 'test-highlighted-icon',
    testId: testId,
    iconName: 'file',
  };

  // HighlightedIcon is a div without a semantic role or accessible text. Using testId for querying in tests.
  const getHighlightedIcon = () => screen.getByTestId(testId);
  const getHighlightedIconSvg = () => screen.getByTestId(`${testId}-icon`);

  // Custom size and fill are not encouraged for use within the component.
  // Instead of adding this to our Chromatic tests where it would become discoverable,
  // we are creating a unit test to ensure it is covered. This is not a style test but
  // rather a test confirming that the provided custom size and fill are correctly applied.
  // All UI tests should use Chromatic
  it('should apply custom size and fill when provided', () => {
    const customProps: IHighlightedIconProps = {
      ...defaultProps,
      size: { container: '--evr-size-lg', icon: 'lg' },
      fill: { container: '--evr-content-status-informative-default', icon: '--evr-surfaces-tertiary-hovered' },
    };
    render(<HighlightedIcon {...customProps} />);
    expect(getHighlightedIcon()).toHaveStyle({
      backgroundColor: 'var(--evr-content-status-informative-default)',
      height: 'var(--evr-size-lg)',
      width: 'var(--evr-size-lg)',
    });
    expect(getHighlightedIconSvg().getAttribute('fill')).toEqual('var(--evr-surfaces-tertiary-hovered)');
    expect(getHighlightedIconSvg().getAttribute('class')).toEqual('evrIcon lg');
  });
});
