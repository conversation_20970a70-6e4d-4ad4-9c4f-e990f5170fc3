import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { HighlightedIcon } from './highlighted-icon';
import { ICON_NAMES } from '../icon';
import Examples from './highlighted-icon.examples.mdx';

<Meta
  title="Components/Icons/HighlightedIcon"
  component={HighlightedIcon}
  parameters={{
    status: { type: 'ready' },
    controls: {
      sort: 'requiredFirst',
      exclude: ['size'], // Custom size available only as an escape hatch, but not actively promoting it
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/3U0GoCKpRdEkwu859tEXBR/Pattern-Component-Dependencies?node-id=1-7814&t=Tr92HIjwM8agG4uo-1',
    },
  }}
  argTypes={{
    fill: {
      type: 'enum',
      control: 'select', // Custom fill available only as an escape hatch, but not actively promoting it
      options: ['blue', 'green', 'neutral', 'red', 'yellow'],
    },
    iconName: {
      control: 'select',
      options: [...ICON_NAMES.sort(), null],
    },
  }}
  args={{
    id: 'highlighted-icon-id',
    testId: 'highlighted-icon-test-id',
    iconName: 'file',
    fill: 'green',
  }}
/>

# HighlightedIcon

<Examples />

## Live Demo

<Canvas>
  <Story name="HighlightedIcon">{(args) => <HighlightedIcon {...args}></HighlightedIcon>}</Story>
</Canvas>

<ArgsTable story="HighlightedIcon" />
