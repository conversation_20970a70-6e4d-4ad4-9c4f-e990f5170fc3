import * as React from 'react';

import { Icon, TIconName, TIconColor, TIconSize } from '../icon';

import styles from './highlighted-icon.module.scss';

export interface IHighlightedIconSize {
  container?: string;
  icon?: TIconSize;
}
export interface IHighlightedIconFill {
  container: TIconColor;
  icon: TIconColor;
}

export type THighlightedIconFillPreset = 'neutral' | 'blue' | 'green' | 'red' | 'yellow';

const ICON_COLOR_MAP: Record<THighlightedIconFillPreset, IHighlightedIconFill> = {
  neutral: {
    container: '--evr-surfaces-tertiary-default',
    icon: '--evr-content-primary-highemp',
  },
  blue: {
    container: '--evr-surfaces-status-informative-lowemp',
    icon: '--evr-content-status-informative-default',
  },
  green: {
    container: '--evr-surfaces-status-success-lowemp',
    icon: '--evr-content-status-success-lowemp',
  },
  red: {
    container: '--evr-surfaces-status-error-lowemp',
    icon: '--evr-content-status-error-default',
  },
  yellow: {
    container: '--evr-surfaces-status-warning-lowemp',
    icon: '--evr-content-status-warning-lowemp',
  },
};

export interface IHighlightedIconProps {
  /** ID of the HighlightedIcon. */
  id: string;
  /** An ID used for automation testing. */
  testId?: string;
  /**
   * Size of the contaner and icon.
   * @default { container: '--evr-size-2xl', icon: 'md' }
   */
  size?: IHighlightedIconSize;
  /**
   * Fill for the background and icon.
   * @default neutral
   */
  fill?: THighlightedIconFillPreset | IHighlightedIconFill;
  /** Name of the icon. */
  iconName: TIconName;
}

export const HighlightedIcon = React.forwardRef<HTMLDivElement, IHighlightedIconProps>(
  (props: IHighlightedIconProps, ref) => {
    const { id, testId, size, fill = 'neutral', iconName } = props;

    const sizeDerived = {
      container: size?.container ?? '--evr-size-2xl',
      icon: size?.icon ?? 'md',
    };
    const fillDerived = typeof fill === 'string' ? ICON_COLOR_MAP[fill] : fill;

    return (
      <div
        id={id}
        data-testid={testId}
        aria-hidden={true}
        className={styles.evrHighlightedIcon}
        style={{
          backgroundColor: `var(${fillDerived.container})`,
          height: `var(${sizeDerived.container})`,
          width: `var(${sizeDerived.container})`,
          minWidth: `var(${sizeDerived.container})`,
        }}
        ref={ref}
      >
        <Icon
          id={`${id}-icon`}
          testId={testId ? `${testId}-icon` : undefined}
          name={iconName}
          size={sizeDerived.icon}
          fill={fillDerived.icon}
        ></Icon>
      </div>
    );
  }
);

HighlightedIcon.displayName = 'HighlightedIcon';
