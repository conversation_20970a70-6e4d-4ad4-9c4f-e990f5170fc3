import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { IconButton } from './icon-button';
import { Button, Tooltip } from '../..';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';

export const scope = {
  IconButton,
  Button,
  Tooltip,
};

Icon Button can be used for floating action buttons and sub-navigation actions. They can be either a square or a circle shape for stylistic purposes.<br />

## Variations

### Primary Icon Button

Used in a hierarchy to establish importance. They direct the user’s attention to a primary action the user flow. Usage for actions to complete a task or to move forward in a process. It should never be doubled up to sit side by side. It is used when an action is clearly more important than the other actions and you need to draw attention to it. I.e., Submit, next, etc.

export const primaryCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            alignItems: 'center',
            width: '100%',
            rowGap: '30px'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const IconButtonWithTooltip = (props) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} />
        </Tooltip> 
    );
    return (
        <Column>
            <Row>
                <IconButtonWithTooltip id="primary-circle-btn" iconName="add" ariaLabel="This is a Primary Circle Icon Button" />
                <IconButtonWithTooltip id="primary-circle-btn-disabled" iconName="add" disabled ariaLabel="This is a Disabled Primary Circle Icon Button" />
            </Row>
            <Row>
                <IconButtonWithTooltip id="primary-square-btn" iconName="add" square ariaLabel="This is a Primary Square Icon Button" />
                <IconButtonWithTooltip id="primary-square-btn-disabled" iconName="add" square disabled ariaLabel="This is a Disabled Primary Square Icon Button" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={primaryCode} />

### Secondary Icon Button

Used lower hierarchy to primary. Establish an action that is not as important as the primary action that invoke an action associated with an alternate path. I.e., cancel, redo, etc.

export const secondaryCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            alignItems: 'center',
            width: '100%',
            rowGap: '30px'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const IconButtonWithTooltip = (props) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} />
        </Tooltip> 
    );
    return (
        <Column>
            <Row>
                <IconButtonWithTooltip id="secondary-circle-btn" iconName="add" variant="secondary" ariaLabel="This is a Secondary Circle Icon Button" />
                <IconButtonWithTooltip id="secondary-circle-btn-disabled" iconName="add" variant="secondary" disabled ariaLabel="This is a Disabled Secondary Circle Icon Button" />
            </Row>
            <Row>
                <IconButtonWithTooltip id="secondary-square-btn" iconName="add" variant="secondary" square ariaLabel="This is a Secondary Square Icon Button" />
                <IconButtonWithTooltip id="secondary-square-btn-disabled" iconName="add" variant="secondary" square disabled ariaLabel="This is a Disabled Secondary Square Icon Button" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={secondaryCode} />

### Tertiary Icon Button

Used for actions which are intentionally wanted to be less visible to the user. A tertiary icon button is used as a trigger to invoke alternative options not directly associated with primary or secondary paths. I.e., previous page, go back, etc.

export const tertiaryCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            alignItems: 'center',
            width: '100%',
            rowGap: '30px'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const IconButtonWithTooltip = (props) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} />
        </Tooltip> 
    );
    return (
        <Column>
            <Row>
                <IconButtonWithTooltip id="tertiary-circle-btn" iconName="add" variant="tertiary" ariaLabel="This is a Tertiary Circle Icon Button" />
                <IconButtonWithTooltip id="tertiary-circle-btn-disabled" iconName="add" variant="tertiary" disabled ariaLabel="This is a Disabled Tertiary Circle Icon Button" />
            </Row>
            <Row>
                <IconButtonWithTooltip id="tertiary-square-btn" iconName="add" variant="tertiary" square ariaLabel="This is a Tertiary Square Icon Button" />
                <IconButtonWithTooltip id="tertiary-square-btn-disabled" iconName="add" variant="tertiary" square disabled ariaLabel="This is a Disabled Tertiary Square Icon Button" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={tertiaryCode} />

### Secondary Neutral Icon Button

Used lower hierarchy to primary. Establish an neutral action associated with an alternate path. I.e., cancel, redo, etc.

export const secondaryNeutralCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            alignItems: 'center',
            width: '100%',
            rowGap: '30px'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const IconButtonWithTooltip = (props) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} />
        </Tooltip> 
    );
    return (
        <Column>
            <Row>
                <IconButtonWithTooltip id="secondary-neutral-circle-btn" iconName="add" variant="secondaryNeutral" ariaLabel="This is a Secondary Neutral Circle Icon Button" />
                <IconButtonWithTooltip id="secondary-neutral-circle-btn-disabled" iconName="add" variant="secondaryNeutral" disabled ariaLabel="This is a Disabled Secondary Neutral Circle Icon Button" />
            </Row>
            <Row>
                <IconButtonWithTooltip id="secondary-neutral-square-btn" iconName="add" variant="secondaryNeutral" square ariaLabel="This is a Secondary Neutral Square Icon Button" />
                <IconButtonWithTooltip id="secondary-neutral-square-btn-disabled" iconName="add" variant="secondaryNeutral" square disabled ariaLabel="This is a Disabled Secondary Neutral Square Icon Button" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={secondaryNeutralCode} />

### Tertiary Neutral Icon Button

Used for actions which are intentionally wanted to be less visible to the user. A tertiary icon button is used as a trigger to invoke alternative options not directly associated with primary or secondary paths. I.e., previous page, go back, etc.

export const tertiaryNeutralCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            columnGap: '10px'
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            alignItems: 'center',
            width: '100%',
            rowGap: '30px'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
   const IconButtonWithTooltip = (props) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} />
        </Tooltip> 
    );
    return (
        <Column>
            <Row>
                <IconButtonWithTooltip id="tertiary-neutral-circle-btn" iconName="add" variant="tertiaryNeutral" ariaLabel="This is a Tertiary Neutral Circle Icon Button" />
                <IconButtonWithTooltip id="tertiary-neutral-circle-btn-disabled" iconName="add" variant="tertiaryNeutral" disabled ariaLabel="This is a Disabled Tertiary Neutral Circle Icon Button" />
            </Row>
            <Row>
                <IconButtonWithTooltip id="tertiary-neutral-square-btn" iconName="add" variant="tertiaryNeutral" square ariaLabel="This is a Tertiary Neutral Square Icon Button" />
                <IconButtonWithTooltip id="tertiary-neutral-square-btn-disabled" iconName="add" variant="tertiaryNeutral" square disabled ariaLabel="This is a Disabled Tertiary Neutral Square Icon Button" />
            </Row>
        </Column>
    );
}`;

<CodeExample scope={scope} code={tertiaryNeutralCode} />

### Inverse

The `inverse` prop is used to change the appearance of the IconButton to be used on the dark surfaces. This prop only affects the `Tertiary IconButton`.

export const inverseIconButton = `() => {
    const IconButtonWithTooltip = (props) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} />
        </Tooltip> 
    );
    return (
        <>
            <IconButtonWithTooltip id="inverse-circle-btn" iconName="add" variant="tertiary" inverse ariaLabel="This is an Inverse Icon Button" />
            <IconButtonWithTooltip id="inverse-circle-btn-disabled" iconName="add" variant="tertiary" inverse disabled ariaLabel="This is a Disabled Inverse Icon Button" />
        </>
    );
}`;

<CodeExample scope={scope} code={inverseIconButton} background={'dark'} />

### Sizing

The `size` prop allows for the customization of the IconButton's dimensions. You can specify different sizes to suit different design requirements.

export const sizingCode = `() => {
    const styles = {
        container: {
            display: 'flex',
            flexDirection: 'column',
            rowGap: '30px',
        },
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            columnGap: '20px',
            alignItems: 'center',
        },
    };
    const Container = ({ children }) => (
        <div style={styles.container}>{children}</div>
    );
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const IconButtonWithTooltip = (props) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} />
        </Tooltip>
    );
    return (
        <Container>
            <Row>
                <IconButtonWithTooltip id="small-icon-btn" iconName="add" size="small" ariaLabel="This is a Small Icon Button" />
                <IconButtonWithTooltip id="medium-icon-btn" iconName="add" size="medium" ariaLabel="This is a Medium Icon Button" />
                <IconButtonWithTooltip id="large-icon-btn" iconName="add" size="large" ariaLabel="This is a Large Icon Button" />
            </Row>
            <Row>
                <IconButtonWithTooltip id="small-icon-btn-square" square iconName="add" size="small" ariaLabel="This is a Small Square Icon Button" />
                <IconButtonWithTooltip id="medium-icon-btn-square" square iconName="add" size="medium" ariaLabel="This is a Medium Square Icon Button" />
                <IconButtonWithTooltip id="large-icon-btn-square" square iconName="add" size="large" ariaLabel="This is a Large Square Icon Button" />
            </Row>
        </Container>
    );
}`;

<CodeExample scope={scope} code={sizingCode} />

## Accessing Icon Button using ref

Click on the Button to access the Icon Button, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            columnGap: '10px'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const ref=React.useRef(null);

    const IconButtonWithTooltip = React.forwardRef((props, ref) => (
        <Tooltip id={\`\${props.id}-tooltip\`} title={props.ariaLabel} placement="topCenter">
            <IconButton {...props} ref={ref} />
        </Tooltip>
    ));

    return (
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
            <IconButtonWithTooltip id="primary-circle-btn-with-ref" ref={ref} iconName="add" ariaLabel="This is a Primary Circle Icon Button" />
        </Row>
    );

}
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

When grouping Icon Buttons, there should only be one primary action and one secondary action. <br />

IconButtons should be used with <LinkTo kind="Components/Tooltip">Tooltip</LinkTo>. Users would benefit from the consistent availability of the accessible name given to the button.

## Accessibility

`ariaLabel` is required to meet the accessibility requirement for Icon Button.<br />

The `ariaHasPopup`, `ariaExpanded`, and `ariaControls` properties are only applicable when the `IconButton` is used to trigger another element.

Accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
