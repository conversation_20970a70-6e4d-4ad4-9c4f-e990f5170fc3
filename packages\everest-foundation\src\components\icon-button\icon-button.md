# Icon Button

## Summary

Research and document implementations for the Everest Icon Button.

- Start Date: 2022-01-26
- Figma link: https://www.figma.com/file/JyOoJ0NaQWElyNPpsNE5hP/%F0%9F%A7%AAButtons?node-id=1411%3A13947

## Detailed Design

Icon Button Component consists of three variants - Primary, Secondary, and Tertiary

Icon Button Component has 5 states.

1. **Default** - This state is when there is no interaction made.
2. **Active** - This state is when the interaction has been made.
3. **Hover** - This state is to show the user that this button is interactable.
4. **Focus Ring** - This state is when the user interacts with the keyboard and has keyboard focus. It doesn't have a hover or disabled state.
5. **Disabled** - This state is to show to user that the element is not interactive. This is also used when no other above state applies.

Icon Button should always be 32px by 32px.

This component has two types:

1. **Icon Circle Container** - Icon component is 24px by 24px. The margin above and below the icon is 4px. Radius is 100%.
2. **Icon Square Container** - Icon component is 24px by 24px. The margin above and below the icon is 4px. Radius is 8px.

## API

1. **id** : string
   Sets **id** attribute on the html element.
1. **testId** : string
   Sets **data-test-id** attribute on the html element.
1. **variant** : primary | secondary | tertiary | secondaryNeutral | tertiaryNeutral
   Determines the variant.
1. **ariaLabel**: string
   This is going to be used as a value for the aria-label attribute.
1. **ariaHasPopup**: 'false' | 'true' | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog'
   This is going to be used as a value for the aria-haspopup attribute. Indicates the availability and type of interactive popup element that can be triggered by the icon button.
1. **ariaExpanded**: 'true' | 'false' | undefined
   This is going to be used as a value for the aria-expanded attribute. Indicates if a control is expanded or collapsed, and whether or not its child elements are displayed or hidden. Used when the icon button triggers another element.
1. **ariaControls**: string
   This is going to be used as a value for the aria-controls attribute. Identifies the element(s) whose contents or presence are controlled by the icon button.
1. **disabled** : boolean
   Enables the disabled state of the component.
   1. **iconName** : string
1. **square** : boolean
   Determines the types of the icon button - Circle or Square.
1. **onClick** : () => void
   A callback function is executed on an icon button click.
1. **onFocus** : () => void
   A callback function is executed when icon button is focused.
1. **onBlur** : () => void
   A callback function is executed when icon button loses focus.

## Accessibility

This component has an alternative text label to announce the purpose of the button. aria-labelledby is used to indicate the tooltip text.

The `ariaHasPopup`, `ariaExpanded`, and `ariaControls` properties are only applicable when the `IconButton` is used to trigger another element.

**Focus Ring** - This state is when the user interacts with the keyboard and has keyboard focus. It doesn't have a hover or disabled state.

## Other Design Systems

- Material UI - https://mui.com/api/icon-button/
  Supports disabled, disableFocusRipple, disableRipple, edge, size.

- Polaris (Shopify) – The icon button isn't part of the Button component.
- Carbon (IBM) - https://www.carbondesignsystem.com/components/button/usage
- Fluent (Microsoft) - https://developer.microsoft.com/en-us/fluentui#/controls/web/button

None of the above Design Systems support **square** API.

## Required PBIs

**712** - Icon Button Architecture  
**635** - Update Button API, remove Icon Button related API  
**634** - Create Icon Button component in react

## Acceptance Criteria

1. React Icon Button name, for now, is icon button. **This might change in the future when DS team proceed with other components**
1. Variant (primary, secondary, and tertiary) **Only 3 variants are required, confirmed by Nick**
1. mouseover icon should be the following
   Disable - cursor.not-allow
   Other states - cursor.pointer
1. APIs:
   a. testId
   b. variant
   c. ariaLabel
   d. disabled
   e. iconName
   f. square
   g. onClick
   h. onFocus
   i. onBlur

**Split off Icon Button as a separate component from Button component to simplify the API**
Remove **square** prop from button.

## Changelog

01/12/2024 - [WC Conversion] Replace IconButton with IconButtonV2 [(**EDS-3231**)](https://ceridian.atlassian.net/browse/EDS-3231)

09/16/2024 - Remove ariaLabelledBy and make ariaLabel required [EDS-4787](https://dayforce.atlassian.net/browse/EDS-4787)

12/20/2024 - Introduced `size` prop to allow T-shirt sizing [PWEB-17827](https://dayforce.atlassian.net/browse/EDS-17827)
