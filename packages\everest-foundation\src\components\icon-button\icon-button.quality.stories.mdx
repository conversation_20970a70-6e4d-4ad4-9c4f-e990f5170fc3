import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { IconButton } from './icon-button';
import { Chromatic, defaultModes } from '../../../chromatic';
import { Tooltip } from '../tool-tip';

<Meta
  title="Testing/Automation Test Cases/Icon Button"
  component={IconButton}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  argTypes={{
    variant: {
      type: 'enum',
      control: 'select',
      options: ['primary', 'secondary', 'tertiary', 'secondaryNeutral', 'tertiaryNeutral'],
      description: 'Changes the appearance of the button. Each variant is associated with a different priority.',
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
  }}
  args={{
    testId: 'test-id',
    disabled: false,
    square: false,
    variant: 'primary',
    ariaLabel: 'This is icon button',
    iconName: 'add',
  }}
/>

export const defaultStyle = { display: 'flex', flexWrap: 'wrap', gap: '10px' };
export const inverseStyle = { backgroundColor: 'black', height: '50px', alignItems: 'center', display: 'flex' };

# Icon Button

## Live Demo

export const IconButtonWithTooltip = (args) => (
  <Tooltip id={`${args.id}-tooltip`} title={args.ariaLabel} placement="rightCenter">
    <IconButton {...args} />
  </Tooltip>
);

<Canvas>
  <Story name="Small Round">
    {(args) => (
      <div style={defaultStyle}>
        <IconButtonWithTooltip {...args} id="round-small-btn" size="small" />
        <IconButtonWithTooltip
          {...args}
          id="round-small-secondary-btn"
          variant="secondary"
          size="small"
        />
        <IconButtonWithTooltip {...args} id="round-small-tertiary-btn" variant="tertiary" size="small" />
        <IconButtonWithTooltip
          {...args}
          id="round-small-secondary-neutral-btn"
          variant="secondaryNeutral"
          size="small"
        />
        <IconButtonWithTooltip
          {...args}
          id="round-small-tertiary-neutral-btn"
          variant="tertiaryNeutral"
          size="small"
        />
        <IconButtonWithTooltip {...args} disabled id="round-small-disabled-btn" size="small" />
        <div style={inverseStyle}>
          <IconButtonWithTooltip {...args} id="round-small-inverse-btn" variant="tertiary" inverse />
          <IconButtonWithTooltip
            {...args}
            id="round-small-inverse-disabled-btn"
            variant="tertiary"
            inverse
            disabled
          />
        </div>
      </div>
    )}

  </Story>
</Canvas>

<Canvas>
  <Story name="Small Square">
    {(args) => (
      <div style={defaultStyle}>
        <IconButtonWithTooltip {...args} id="square-small-btn" square size="small" />
        <IconButtonWithTooltip {...args} id="square-small-secondary-btn" square variant="secondary" size="small" />
        <IconButtonWithTooltip {...args} id="square-small-tertiary-btn" square variant="tertiary" size="small" />
        <IconButtonWithTooltip
          {...args}
          id="square-small-secondary-neutral-btn"
          square
          variant="secondaryNeutral"
          size="small"
        />
        <IconButtonWithTooltip
          {...args}
          id="square-small-tertiary-neutral-btn"
          square
          variant="tertiaryNeutral"
          size="small"
        />
        <IconButtonWithTooltip {...args} disabled id="square-small-disabled-btn" square size="small" />
        <div style={inverseStyle}>
          <IconButtonWithTooltip {...args} id="square-small-inverse-btn" square variant="tertiary" inverse />
          <IconButtonWithTooltip
            {...args}
            id="square-small-inverse-disabled-btn"
            square
            variant="tertiary"
            inverse
            disabled
          />
        </div>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Medium Round">
    {(args) => (
      <div style={defaultStyle}>
        <IconButtonWithTooltip {...args} id="round-medium-btn" size="medium" />
        <IconButtonWithTooltip
          {...args}
          id="round-medium-secondary-btn"
          variant="secondary"
          size="medium"
        />
        <IconButtonWithTooltip
          {...args}
          id="round-medium-tertiary-btn"
          variant="tertiary"
          size="medium"
        />
        <IconButtonWithTooltip
          {...args}
          id="round-medium-secondary-neutral-btn"
          variant="secondaryNeutral"
          size="medium"
        />
        <IconButtonWithTooltip
          {...args}
          id="round-medium-tertiary-neutral-btn"
          variant="tertiaryNeutral"
          size="medium"
        />
        <IconButtonWithTooltip {...args} disabled id="round-medium-disabled-btn" size="medium" />
        <div style={inverseStyle}>
          <IconButtonWithTooltip
            {...args}
            id="round-medium-inverse-btn"
            variant="tertiary"
            inverse
            size="medium"
          />
          <IconButtonWithTooltip
            {...args}
            id="round-medium-inverse-disabled-btn"
            variant="tertiary"
            inverse
            disabled
            size="medium"
          />
        </div>
      </div>
    )}

  </Story>
</Canvas>

<Canvas>
  <Story name="Medium Square">
    {(args) => (
      <div style={defaultStyle}>
        <IconButtonWithTooltip {...args} id="square-medium-btn" square size="medium" />
        <IconButtonWithTooltip
          {...args}
          id="square-medium-secondary-btn"
          square
          variant="secondary"
          size="medium"
        />
        <IconButtonWithTooltip
          {...args}
          id="square-medium-tertiary-btn"
          square
          variant="tertiary"
          size="medium"
        />
        <IconButtonWithTooltip
          {...args}
          id="square-medium-secondary-neutral-btn"
          square
          variant="secondaryNeutral"
          size="medium"
        />
        <IconButtonWithTooltip
          {...args}
          id="square-medium-tertiary-neutral-btn"
          square
          variant="tertiaryNeutral"
          size="medium"
        />
        <IconButtonWithTooltip {...args} disabled id="square-medium-disabled-btn" square size="medium" />
        <div style={inverseStyle}>
          <IconButtonWithTooltip
            {...args}
            id="square-medium-inverse-btn"
            square
            variant="tertiary"
            inverse
            size="medium"
          />
          <IconButtonWithTooltip
            {...args}
            id="square-medium-inverse-disabled-btn"
            square
            variant="tertiary"
            inverse
            disabled
            size="medium"
          />
        </div>
      </div>
    )}

  </Story>
</Canvas>

<Canvas>
  <Story name="Large Round">
    {(args) => (
      <div style={defaultStyle}>
        <IconButtonWithTooltip {...args} id="round-large-btn" size="large" />
        <IconButtonWithTooltip {...args} id="round-large-secondary-btn" variant="secondary" size="large" />
        <IconButtonWithTooltip {...args} id="round-large-tertiary-btn" variant="tertiary" size="large" />
        <IconButtonWithTooltip
          {...args}
          id="round-large-secondary-neutral-btn"
          variant="secondaryNeutral"
          size="large"
        />
        <IconButtonWithTooltip {...args} id="round-large-tertiary-neutral-btn" variant="tertiaryNeutral" size="large" />
        <IconButtonWithTooltip {...args} disabled id="round-large-disabled-btn" size="large" />
        <div style={inverseStyle}>
          <IconButtonWithTooltip {...args} id="round-large-inverse-btn" variant="tertiary" inverse size="large" />
          <IconButtonWithTooltip
            {...args}
            id="round-large-inverse-disabled-btn"
            variant="tertiary"
            inverse
            disabled
            size="large"
          />
        </div>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Large Square">
    {(args) => (
      <div style={defaultStyle}>
        <IconButtonWithTooltip {...args} id="square-large-btn" square size="large" />
        <IconButtonWithTooltip {...args} id="square-large-secondary-btn" square variant="secondary" size="large" />
        <IconButtonWithTooltip {...args} id="square-large-tertiary-btn" square variant="tertiary" size="large" />
        <IconButtonWithTooltip
          {...args}
          id="square-large-secondary-neutral-btn"
          square
          variant="secondaryNeutral"
          size="large"
        />
        <IconButtonWithTooltip
          {...args}
          id="square-large-tertiary-neutral-btn"
          square
          variant="tertiaryNeutral"
          size="large"
        />
        <IconButtonWithTooltip {...args} disabled id="square-large-disabled-btn" square size="large" />
        <div style={inverseStyle}>
          <IconButtonWithTooltip
            {...args}
            id="square-large-inverse-btn"
            square
            variant="tertiary"
            inverse
            size="large"
          />
          <IconButtonWithTooltip
            {...args}
            id="square-large-inverse-disabled-btn"
            square
            variant="tertiary"
            inverse
            disabled
            size="large"
          />
        </div>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="[Playwright] - Interactions"
    parameters={{
      chromatic: Chromatic.DISABLE,
    }}
    args={{
      onClick: () => alert('clicked'),
    }}
  >
    {(args) => (
      <div style={defaultStyle}>
        <IconButtonWithTooltip {...args} id="primary" ariaLabel="primary" />
        <IconButtonWithTooltip {...args} id="disabled" ariaLabel="disabled" disabled />
        <IconButtonWithTooltip {...args} id="secondary" ariaLabel="secondary" variant="secondary" />
      </div>
    )}
  </Story>
</Canvas>
