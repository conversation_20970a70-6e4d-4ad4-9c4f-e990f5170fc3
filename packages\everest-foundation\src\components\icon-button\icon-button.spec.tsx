import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { IconButton } from './icon-button';

describe('[IconButton]', () => {
  const testId = 'icon-button-test-id';
  const ariaLabel = 'aria-label content';
  const ariaDescribedBy = 'aria-describedby content';
  const onClick = jest.fn();
  const onBlur = jest.fn();
  const onFocus = jest.fn();
  const onKeyDown = jest.fn();

  const getIconButton = () => screen.getByRole('button');

  beforeEach(jest.clearAllMocks);

  it('should render aria-label when value is set', async () => {
    render(<IconButton testId={testId} iconName="add" onClick={onClick} ariaLabel={ariaLabel} id="test-button-id" />);

    expect(getIconButton()).toHaveAttribute('aria-label', ariaLabel);
  });

  it('should render aria-describedby when value is set', async () => {
    render(
      <IconButton
        testId={testId}
        iconName="add"
        onClick={onClick}
        ariaLabel="icon"
        ariaDescribedBy={ariaDescribedBy}
        id="test-button-id"
      />
    );

    expect(getIconButton()).toHaveAttribute('aria-describedby', ariaDescribedBy);
  });

  describe('click event', () => {
    it('dispatch a click event by mouse click', async () => {
      render(<IconButton testId={testId} iconName="add" ariaLabel="icon" onClick={onClick} id="test-button-id" />);

      await userEvent.click(getIconButton());
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('dispatch a click event by Keyboard Enter key', async () => {
      render(<IconButton testId={testId} iconName="add" ariaLabel="icon" onClick={onClick} id="test-button-id" />);

      await userEvent.tab();
      await userEvent.keyboard('[Enter]');
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('dispatch a click event by Keyboard Enter key', async () => {
      render(<IconButton testId={testId} iconName="add" ariaLabel="icon" onClick={onClick} id="test-button-id" />);

      await userEvent.tab();
      await userEvent.keyboard('[Space]');
      expect(onClick).toHaveBeenCalledTimes(1);
    });
  });

  it('dispatch a onKeyDown event by Keyboard Space key', async () => {
    render(<IconButton testId={testId} iconName="add" ariaLabel="icon" onKeyDown={onKeyDown} id="test-button-id" />);

    await userEvent.tab();
    await userEvent.keyboard('[Space]');
    expect(onKeyDown).toHaveBeenCalledTimes(1);
  });

  describe('blur event', () => {
    it('dispatch a blur event', async () => {
      render(<IconButton testId={testId} iconName="add" ariaLabel="icon" onBlur={onBlur} id="test-button-id" />);

      await userEvent.tab();
      await userEvent.tab(); // blur
      expect(onBlur).toHaveBeenCalledTimes(1);
    });
  });

  describe('focus event', () => {
    it('dispatch a focus event', async () => {
      render(<IconButton testId={testId} iconName="add" ariaLabel="icon" onFocus={onFocus} id="test-button-id" />);

      await userEvent.tab();
      expect(onFocus).toHaveBeenCalledTimes(1);
    });
  });
});
