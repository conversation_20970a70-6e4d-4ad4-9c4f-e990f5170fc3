import { Meta, Story, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { IconButton } from './icon-button';
import Examples from './icon-button.examples.mdx';
import { action } from '@storybook/addon-actions';
import { ICON_NAMES } from '../icon';
import { Tooltip } from '../tool-tip';

<Meta
  title="Components/Buttons/Icon Button"
  component={IconButton}
  parameters={{
    status: {
      type: 'ready',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/JyOoJ0NaQWElyNPpsNE5hP/%F0%9F%A7%AAButtons?node-id=1411%3A13946',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'Required unique identifier.',
    },
    disabled: {
      description: 'Disables the Button to prevent any user action.',
      table: {
        defaultValue: { summary: false },
      },
    },
    iconName: {
      type: { required: true },
      control: 'select',
      options: ICON_NAMES.sort(),
      description: 'Changes the icon used.',
    },
    square: {
      description: 'Changes the shape of the Icon Button.',
      table: {
        defaultValue: { summary: false },
      },
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    variant: {
      type: 'enum',
      control: 'select',
      options: ['primary', 'secondary', 'tertiary', 'secondaryNeutral', 'tertiaryNeutral'],
      description: 'Changes the appearance of the button. Each variant is associated with a different priority.',
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    type: {
      type: 'enum',
      control: 'select',
      options: ['submit', 'button', 'reset'],
      description: 'Sets the type of the button.',
    },
    size: {
      description: 'Defines the size of the Icon Button.',
      control: 'select',
      options: ['small', 'medium', 'large'],
      table: {
        defaultValue: { summary: 'small' },
      },
    },
    inverse: {
      table: {
        defaultValue: { summary: false },
      },
      description:
        'Changes the appearance of the button to be used on dark surfaces. Affects the `Tertiary IconButton` only.',
    },
    ariaLabel: {
      description:
        "A clear description of the Button's purpose. For common icon labeling, refer to `Foundation > Icons` for the descriptions.",
      type: { required: true },
    },
    ariaDescribedBy: {
      type: 'string',
      control: 'text',
      description: ' Identifies the element (or elements) that describes the element on which the attribute is set.',
    },
    ariaHasPopup: {
      description:
        'Indicates the availability and type of interactive popup element that can be triggered by the icon button',
      type: 'string',
    },
    ariaExpanded: {
      description:
        'Indicates if a control is expanded or collapsed, and whether or not its child elements are displayed or hidden',
      type: 'boolean',
    },
    ariaControls: {
      description: 'Identifies the element(s) whose contents or presence are controlled by the icon button.',
      type: 'string',
    },
    ariaOwns: {
      description: 'Identifies the element(s) whose contents or presence are owned by the icon button.',
      type: 'string',
    },
    onClick: {
      control: '-',
      description: 'Callback fired when user clicks on the button.',
    },
    onKeyDown: {
      control: '-',
      description: 'Callback fired when user presses a key when button is focused.',
    },
    onFocus: {
      control: '-',
      description: 'Callback fired when button receives focus.',
    },
    onBlur: {
      control: '-',
      description: 'Callback fired when button loses focus.',
    },
    className: {
      table: {
        disable: true,
      },
    },
  }}
  args={{
    id: 'button-id',
    iconName: 'add',
    ariaLabel: 'Add Item',
    testId: 'test-id',
    variant: 'primary',
    square: false,
    inverse: false,
    disabled: false,
    type: 'button',
    size: undefined,
    ariaExpanded: undefined,
    ariaControls: undefined,
    ariaDescribedBy: undefined,
    ariaHasPopup: undefined,
    onFocus: action('onFocus'),
    onClick: action('onClick'),
    onKeyDown: action('onKeyDown'),
    onBlur: action('onBlur'),
  }}
/>

# Icon Button

<Examples />

## Live Demo

<Canvas>
  <Story name="Icon Button">
    {(args) => {
      const backgroundStyle = {
        backgroundColor: '#333',
        height: 'auto',
        width: 'auto',
        padding: '2rem',
      };
      return (
        <div
          style={args.inverse && (args.variant === 'secondary' || args.variant === 'tertiary') ? backgroundStyle : null}
        >
          <Tooltip id={`${args.id}-tooltip`} title={args.ariaLabel} placement="rightCenter">
            <IconButton {...args} onClick={action('onClick')} onFocus={action('onFocus')} onBlur={action('onBlur')} />
          </Tooltip>
        </div>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Icon Button" />
