import React, { forwardRef } from 'react';
import classNames from 'classnames';

import { ButtonBase, IButtonBaseProps, TButtonSize } from '../button-base';
import { Icon, TIconName } from '../icon';

import styles from './icon-button.module.scss';

// Omitted the props from ButtonBase interface that will not be used so consumer will get error during compilation,
// if they trying to use them.
export interface IIconButtonProps
  extends Omit<
    IButtonBaseProps,
    | 'size'
    | 'children'
    | 'tabIndex'
    | 'radius'
    | 'uniformSize'
    | 'ariaLabelledBy'
    | 'ariaPressed'
    | 'callToAction'
    | 'focusRingStyleTransform'
  > {
  square?: boolean;
  size?: TButtonSize;
  iconName: TIconName;
  ariaLabel: string;
}

export const IconButton = forwardRef<HTMLButtonElement, IIconButtonProps>((props, ref) => {
  const { square = false, iconName, size = 'small', className, ...rest } = props;

  return (
    <ButtonBase
      ref={ref}
      radius={square ? '--evr-radius-2xs' : '--evr-radius-circle'}
      {...rest}
      className={classNames(
        styles.evrIconButton,
        {
          [styles.medium]: size === 'medium',
          [styles.small]: size === 'small',
          [styles.large]: size === 'large',
        },
        className
      )}
    >
      <Icon name={iconName} size="md" />
    </ButtonBase>
  );
});

IconButton.displayName = 'IconButton';
