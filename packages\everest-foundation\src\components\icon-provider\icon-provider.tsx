import React, { useState, useEffect, PropsWithChildren, useContext, useCallback, useRef, useMemo } from 'react';

import { getGlobalContext } from '../../utils';
import { fetchIconManifest, IconManifestType } from '../../utils/asset-helper';
import { useEverestContext } from '../everest-provider';

interface IconContextType {
  iconManifest: IconManifestType | undefined; // svg icons manifest json
  accentIconManifest: IconManifestType | undefined; // accent svg icons manifest json
}

const defaultContext: IconContextType = {
  iconManifest: undefined,
  accentIconManifest: undefined,
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const IconContext = getGlobalContext<IconContextType>('iconContext', defaultContext);

// Create the IconProvider component
export const IconProvider = (props: PropsWithChildren<unknown>): JSX.Element => {
  const { children } = props;
  const [iconManifest, setIconManifest] = useState<IconManifestType | undefined>(undefined);
  const [accentIconManifest, setAccentIconManifest] = useState<IconManifestType | undefined>(undefined);

  const context = useEverestContext();

  // Ref to track if component is mounted
  const isMounted = useRef(true);

  const fetchIconManifestCallback = useCallback(async () => {
    const manifest = await fetchIconManifest(context.librariesUrl);
    const accentManifest = await fetchIconManifest(context.librariesUrl, false, true);
    // Only update state if component is still mounted
    if (manifest && isMounted.current) {
      setIconManifest(manifest);
    }

    if (accentManifest && isMounted.current) {
      setAccentIconManifest(accentManifest);
    }
  }, [context.librariesUrl]);

  useEffect(() => {
    // Set isMounted to true when component mounts
    isMounted.current = true;

    // Fetch the icon data
    fetchIconManifestCallback();
    // Cleanup to set isMounted to false when component unmounts
    return () => {
      isMounted.current = false;
    };
  }, [fetchIconManifestCallback]);

  const iconManifestContext = useMemo(() => ({ iconManifest, accentIconManifest }), [iconManifest, accentIconManifest]);

  return <IconContext.Provider value={iconManifestContext}>{children}</IconContext.Provider>;
};

export const useIconContext = (): IconContextType => useContext(IconContext);

IconProvider.displayName = 'IconProvider';
