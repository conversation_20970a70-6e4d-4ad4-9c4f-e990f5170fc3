import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Icon } from './icon';
import { Button } from '../button';

export const scope = { Icon, Button };

Icon component is a decorative component. It is used to visually indicate application functionality and available actions.

Available Everest Icons page will be provided

## Variations

### Default Icon

Icon component with `name` prop to generate different icon

export const defaultCode = `
  <Icon name='add' fill='--evr-interactive-primary-default'/>
`;

<CodeExample scope={scope} code={defaultCode} />

## Accessing Icon using ref

Click on the Button to access the Icon, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            columnGap: '10px'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
            <Icon ref={ref} name='add' fill='--evr-interactive-primary-default'/>
        </Row>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

It used by other components to enhance the visual presentation.
Example: Icon Button, checkbox, etc.

## Accessibility

Icon is a decorative component, it is not focusable, not screen reader enabled. The `aria-hidden` attribute is set to true.
