# Icon

## Table of Contents

1. [Summary](#summary)
1. [Detailed Design](#detailed-design)
   1. [Type Guard](#type-guard)
1. [API](#api)
1. [Accessibility](#accessibility)
1. [Q&A](#q-and-a)
1. [Future consideration](#future-considerations)
1. [Documentation](#documentation)
1. [Other Design Systems](#other-design-systems)
1. [Required PBIs](#required-pbis)
1. [Naming Convention](#naming-convention)
1. [Icon Architecture Research](#icon-architecture-research)
   1. [Dayforce Icons](#dayforce-icons)
   1. [Design System Icon](#design-system-icon)
   1. [FontIcon vs inline SVG](#fonticon-vs-inline-svg)
   1. [SVG](#svg)
   1. [Web Component or React Component](#web-component-or-react-component)
1. [Icon Manifest Structure](#icon-manifest-structure)
1. [Changelog](#changelog)

## Summary

Research and document implementations for the Everest Icon.

- Start Date: 2022-01-26
- Figma link: https://www.figma.com/file/XwOWhFtqzlWicOjfSe8GuR/%F0%9F%A7%AAIcons?node-id=800%3A784

## Detailed Design

- Icon Component will stay as Web Component and has React Icon component wrapping the WC. For more information [Web Component or React Component](#web-component-or-react-component)
- Inline SVG Icon with single manifest.json file for System Icon Component.
  - manifest.json file structure discussion [Icon Manifest Structure](#icon-manifest-structure)
- Spotlight and Illustrative Icon Component as [Future consideration](#future-considerations)
- Inline SVG with individual SVG - [Future consideration](#future-considerations)

For any architecture decision and documentation, please visit Section : [Icon Architecture Research](#icon-architecture-research)

### Type Guard

icon-types.ts and aliased-colors.ts

We want to use typescript to guard the icon-types and aliased-colors.
For example:

```
<Icon name="nonexisticonname" fill="nonexisticoncolor" size="lg"/>

Should produce the following messages

  TS2322: Type '"nonexisticonname"' is not assignable to type '"link" | "add" | "minus"...'

and

  TS2322: Type '"nonexistingcolor"' is not assignable to type '"--evr-interactive-primary-default" | "--evr-interactive-primary-hovered" | "--evr-interactive-primary-pressed" | "--evr-interactive-primary-decorative" | "--evr-interactive-primary-focus" | ... 62 more ... | undefined'.
```

Icons are dynamically created as more icons can be added into the icon-manifest.json file.

```
{
  "link": {
    "svg": "<svg xmlns=\"http: //www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path fill-rule=\"evenodd\" d=\"M5.497 19.574c1.995 1.901 5.23 1.901 7.226 0l2.167-2.066c1.996-1.901 1.996-4.984 0-6.885l-.722-.689c-.4-.38-1.046-.38-1.445 0-.4.38-.4.997 0 1.378l.722.688a2.825 2.825 0 010 4.131l-2.168 2.066c-1.197 1.14-3.138 1.14-4.335 0a2.825 2.825 0 010-4.131c.399-.38.399-.997 0-1.377-.4-.38-1.046-.38-1.445 0-1.996 1.901-1.996 4.984 0 6.885z\" clip-rule=\"evenodd\"/><path fill-rule=\"evenodd\" d=\"M18.503 4.426c-1.995-1.901-5.23-1.901-7.226 0L9.11 6.492c-1.996 1.901-1.996 4.984 0 6.885l.722.688c.4.38 1.046.38 1.445 0 .4-.38.4-.996 0-1.377L10.556 12a2.825 2.825 0 010-4.131l2.168-2.066c1.197-1.14 3.138-1.14 4.335 0a2.825 2.825 0 010 4.131c-.399.38-.399.997 0 1.377.4.38 1.046.38 1.445 0 1.996-1.901 1.996-4.984 0-6.885z\" clip-rule=\"evenodd\"/></svg>"
  },
  "add": {
    "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M13 5a1 1 0 10-2 0v6H5a1 1 0 100 2h6v6a1 1 0 102 0v-6h6a1 1 0 100-2h-6V5z\"/></svg>"
  },
  "minus": {
    "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path fill-rule=\"evenodd\" d=\"M20 12a1 1 0 01-1 1H5a1 1 0 110-2h14a1 1 0 011 1z\" clip-rule=\"evenodd\"/></svg>"
  },
  ... many more ...
}
```

To get the type checked by Typescript, the available list has to be static and assigned as const.
For example :

```
export const iconNames = [
  'link',
  'add',
  'minus',
  'menu',
  'x',
  'refresh',
  'maximize',
  ... many more ...
] as const;
```

To achieve this, we are required to update the iconNames in icon-types.ts when updating the manifest.json file.

UnitTest has been added to make sure the icon-manifest.json - icon-types.ts and temp-core-colors.json - aliased-colors.ts have the same required keys to prevent any misalignment.

## API

1. **testId**: string
   Sets **data-test-id** attribute on the html element.  
   We might need to add a base interface to the React repo which all of the components will need to inherit since we expect all of them to have the testId prop.
1. **name** : TIconName (iconManifest)
1. **size** : sizeToken (sm | md | lg)
   - sm (16 x 16 px | 1 x 1 rem)
   - md (24 x 24 px | 1.5 x 1.5 rem)
   - lg (32 x 32 px | 2 x 2 rem)
1. **fill** : TIconColor | default: --evr-icon-default

implement **aria-hidden=true** and **display:"block"**

## Acceptance Criteria

1. The name of a component is **&lt;Icon&gt;**
1. Icon is a Decorative component, it is not focusable, not screen reader enabled. aria-hidden=true
1. APIs:
   1. name
   1. size
   1. fill
   1. testId
1. update storybook default icon using the first icon on the available list
1. update icon button default icon to first icon
1. make sure storybook iconName would be alphabetically ordered

## Accessibility

1. Should an icon be used individually without being associated with button/component?
2. Are all icons Decorative icons? aria-hidden=true
3. Do we support Semantic icons?
4. Does the icon component clickable/focusable? Should it be handled by the parent wrapper component? iconButton?
5. tooltip?
6. It should support WHCM - need research.

**Decision** Icon is a Decorative component, it is not focusable, not screen reader enabled. aria-hidden=true

## Q and A

1. Do we support clickable/focusable icons? (Semantic/Decorative)
   - **Answer**: Icon component is a Decorative Component, not clickable/focusable
1. Support WHCM?
   - **Answer**: Yes, devs have to make sure Disabled color is active on WHCM
1. System, Spotlight, and Illustrative icons are all different components?
   - **Answer**: Yes, we only focus on System Icon Component.
     - Spotlight and Illustrative Icons [Future consideration](#future-considerations)
1. icon bounding box size (sm | md | lg)
   - sm (16 x 16 px | 1 x 1 rem)
   - md (24 x 24 px | 1.5 x 1.5 rem)
   - lg (32 x 32 px | 2 x 2 rem)
   - **Answer**: Keep all three
1. fill/color, limited set of color? Do we have a list of supported colors?
   - **Answer**: Yes, based on the token. (control set of tokens) - [Color Figma](https://www.figma.com/file/P9mKLQA8PMDVPCJQ2EKxh7/%F0%9F%A7%AAWeb-Tokens?node-id=2666%3A22753) (future improvement - possible different use cases - error color, etc )
1. default/hover/active states color prop?
   - **Answer**: No, Icon is a Decorative component.
1. Variants?
   - Outline (separate icon with different name i.e. print-outlined)
   - Filled (separate icon with different name i.e. print-filled)
   - TwoToned (for spotlight and illustrative)
   - **Answer**: system icon is monotone, [Future consideration](#future-considerations)
1. Naming convention
   - **Answer**: Icon naming convention ([Dev discussion on Naming convention](#naming-convention))
     - {iconName} : iconName as camelCasing
     - example
       - **core** icons: print, unlock, home
       - **navigation** icons: chevronRight, arrowLeft

## Future Considerations

1. Spotlight and Illustrative Icon Components
1. Variants (Outline, Filled, TwoToned)
1. Inline SVG with individual SVG file
   - Ongoing Investigation on how to serve SVG as an individual SVG file
   - Benefit of updating to individual SVG file
     - Designer can just upload the SVG files to the Theme repo
     - Network access to SVG would be individual SVG instead of the full manifest.json file.
     - Individual SVG can be dynamically loaded and cached to increase performance.

## Documentation

1. Do we need to display all icons on the storybook? To show all available icons.
   - A: Yes
2. Do we need a Searchable feature now?
   - A: Yes

**Decision** Yes and PBI Created

## Other Design Systems

### Material - https://mui.com/components/icons/

- Standardized Material Design icons exported as React components (SVG icons).
  - Material Design icons have a set of icons as a component.
- With the SvgIcon component, a React wrapper for custom SVG icons. (do we need to support custom SVG icon?)
- TitleAccess - Provides a human-readable title for the element that contains it. (for a11y)

### Shopify Polaris - https://polaris.shopify.com/components/images-and-icons/icon#navigation

- accessibilityLabel (title which also shows up on tooptip?)
- backdrop (backdrop behind the icon) - background color? do we support it?

### Microsoft Fluent UI - https://developer.microsoft.com/en-us/fluentui#/controls/web/icon

- Fluent UI uses a custom font for its iconography.
- Fluent UI React has SVG-based icons
- It obsoletes ariaLable and uses native prop aria-label.

### User Base - https://baseweb.design/components/icon/

- title (accessibility)

### CoreUI - https://coreui.io/react/docs/4.1/components/icon/

- title (Title tag content)

### chakra - https://chakra-ui.com/docs/media-and-icons/icon

- focusable (Denotes that the icon is not an interactive element, and only used for presentation.) - any interactive icon should be using icon Button.

### Ant Design - https://ant.design/components/icon/

- rotate (Rotate by n degrees)
- spin (Rotate icon with animation)
- twoToneColor (Only supports the two-tone icon. Specify the primary color)

## Required PBIs

- [EVR-700 - [Spike] PoC webpack bundle with manifest.json/svg icons](https://ceridianpt.atlassian.net/browse/EVR-700)
- [EVR-697 - [Dev] Create EvrIcon Web Component](https://ceridianpt.atlassian.net/browse/EVR-697)
- [EVR-699 - [Dev] Update Button/CheckBox icon](https://ceridianpt.atlassian.net/browse/EVR-699)
- [EVR-698 - [Dev] Create icon React Component](https://ceridianpt.atlassian.net/browse/EVR-698)
- [EVR-769 - [Dev] Migrate manifest.json or svg icons to platform theme](https://ceridianpt.atlassian.net/browse/EVR-769)
- [EVR-701 - [Dev] Iconography showcase on Storybook (documentation)](https://ceridianpt.atlassian.net/browse/EVR-701)

## Naming Convention

Dev Discussion

```
- icon name structure (based on the naming convention and manifest structure discussion)
  - Core
  - Navigation
  - Feature

Three types of icons:

- System icons
- Spotlight icons
- Illustrative icons

### System icons

No distinction between core/navigation/feature icons for naming convention

evr (Everest Prefix) - iconsys (System icons) - time/checkbox (icon Name)

<b>Example</b>

- evr-iconsys-time
- evr-iconsys-checkbox

or

evr (Everest Prefix) - iconsys (System icons) - core/nav/feat - time/checkbox (icon Name)

<b>Example</b>

- evr-iconsys-feat-time
- evr-iconsys-core-checkbox
```

Decision:

1. remove evr-icon prefix
1. remove category (since categories are subject to change and we don't want to make breaking changes to feature teams just by changing the category name)
1. checkbox and arrowLeft (i.e., keep just the icon's name, which is unique across all categories)

Discussion with Designer

1. Structure on Figma
   - core/print, core/unlock, core/lock
   - on manifest file
     - print, unlock
   - when converted to loading individual SVG
     - using the same folder structure as core/unlock, core/lock, etc.

# Icon Architecture Research

We started by looking into Dayforce implementation

## Dayforce Icons

Dayforce is using font Icon

The icon component will replace all the Dayforce icons, the architecture should be able to be utilized by React and Mobile. Support for dojo is not required.

Dayforce icons only have system icons type with 2 categories, FeatureIcons, and GlobalIcons

- FeatureIcons
- GlobalIcons
- [Web font naming convention](https://wiki.dayforce.com/display/UIG/Web+font+naming+convention+and+creation)
- [Web Font Generation](https://wiki.dayforce.com/display/PLAT/Web+Font+Generation)

## Design System Icon

There are 2 main approaches to handling icons. FontIcon and InlineSVG

## FontIcon vs inline SVG

1. font icons -
   - based on previous research
     - There was no documentation from the prior research but the decision was indeed to use SVGs. Font Icons have their accessibility concerns as we mentioned but just in general are harder to work with
   - not compatible with WAI-ARIA specification - aria-labelledby attribute (can setup icon fonts to be accessible - https://www.filamentgroup.com/lab/bulletproof_icon_fonts)
   - icon fonts are vulnerable to anti-aliasing techniques implemented by the browser which affects the visual quality of the icon making it blurry and less sharp.
   - SVGs over a much higher degree of versatility as compared to Icon Fonts in terms of modifications and styling control.
   - It is much easier to position SVGs as compared to Icon Fonts.
1. References (SVG icons vs font icons)
   - http://dev.azure.com/Ceridian/Platform/\_git/platform-icon
   - https://github.blog/2016-02-22-delivering-octicons-with-svg/
   - https://betterprogramming.pub/inline-svg-or-icon-fonts-which-one-to-use-77c0daf1c527
   - https://www.lambdatest.com/blog/its-2019-lets-end-the-debate-on-icon-fonts-vs-svg-icons/

**Decision** : Inline SVG

## SVG

**Implementation (APIs)**

1.  Icon component with name prop to handle different icon
    - Providing a icon name would auto generate the icon
    - Current implementation
2.  Standardized Material Design icons
    - Example:
      - import AccessAlarmIcon from '@mui/icons-material/AccessAlarm';
      - import ThreeDRotation from '@mui/icons-material/ThreeDRotation';
    - For different variants
      - Filled theme (default) is exported as @mui/icons-material/Delete,
      - Outlined theme is exported as @mui/icons-material/DeleteOutlined,
      - Rounded theme is exported as @mui/icons-material/DeleteRounded,
      - Twotone theme is exported as @mui/icons-material/DeleteTwoTone,
      - Sharp theme is exported as @mui/icons-material/DeleteSharp.
    - Usage
      ```
      <Grid>
        <DeleteRoundedIcon />
        <DeleteForeverRoundedIcon />
      </Grid>
      ```
    - We can use SVGR to transform SVGs into React Component
    - Minimum bundle size with individual icon version
    - Concern: how to handle the Button's dynamic icon usage with this approach.
    - We have to change the button and checkbox implementation.

**Decision**: Icon component with name prop to handle different icon

**SVG Technique**

1. Inline SVG
1. Image
1. Background
1. Mask

For performance comparison with the different techniques, please refer to the following [Which SVG technique performs best for way too many icons?](https://cloudfour.com/thinks/svg-icon-stress-test/)

> There are also slight differences in how image elements render compared to inline SVG: Negative space appears slightly diminished. This will be true for all remaining techniques in this article.

- Remaining techniques are Image, Background, and Mask.

**Decision** : Inline SVG

**SVG Implementation**

1.  manifest.json
    - Current implementation, we have the structure ready without reinventing and reinvestigating
    - It is easier for designers to update the manifest, and it would automatically become available for consumption.
    - We always load all icons, which might not be efficient for the package.
    - Metadata can be included within the manifest.json file
    - We can break down different manifest.json for different usages to reduce the size and eliminate the unnecessary load.
    - We can further minimize the JSON file by only using the SVG children element to reduce the size.
1.  SVG Sprite
    - combine all individual SVG into a sprite SVG with symbol
    - Many existing webpack loader
      - https://github.com/JetBrains/svg-sprite-loader
      - https://www.npmjs.com/package/svg-sprite-loader
    - We can group icon usage on the different sprite to increase performance
      - Example article : [Embedding SVG files in a JS bundle using Webpack](https://maqduni.com/2019/07/16/embedding-svg-files-in-a-webpack-js-bundle/)
    - This is a similar idea as manifest.json, combine a group of SVG into a bigger SVG sprite.
1.  Individual SVG
    - Designer can import/update the individual created SVG on the repo and it will become available for the end-user.
    - We can implement Dynamic SVG loading.
      - [A Story About Dynamic SVG Loading](https://tech.busuu.com/a-story-about-dynamic-svg-loading-82e97c3230d7)
    - Created a PoC by using SVG-Injector
      - [PoC branch](http://dev.azure.com/Ceridian/Platform/_git/platform-web-component?version=GBkey%2F275_icon_architecture)
      - [svg-injector](https://github.com/tanem/svg-injector)
    - Still under investigation on how are we handling the individual SVGs assets.
      - Separate http host
      - bundle all svgs in web-components

**Decision**: manifest.json

**Future improvement**: Individual SVG (Dynamic)

## Web Component or React Component

Should it be web component or React component?

- Dojo support is not required.

- Web component

  - Icon can be used anywhere not limited to React
  - Icon component contains all available icons which can distribute to Dayforce dojo/Mobile etc.
  - We have the infrastructure for the icon component, easier to implement and enables icon-manifest.json for the Design team to update.
  - the icon-manifest.json can be an external file/ within the Platform Theme.

- React component
  - Need to scaffolding for the icon component - can we use the same structure on web component for the icon component?
  - Investigate, and this will be restricted to React only.
  - Need to convert checkbox and button to React

<b>[Early investigation work](http://dev.azure.com/Ceridian/Platform/_git/platform-components?ver[%E2%80%A6]fcfeab6dd&path=%2F.storybook%2Fdoc-blocks%2Ficon-library.tsx)</b>

**Further investigation and discussion**

1. Keep web component and create React Wrapper

   - The build issue from Nancy, json bundle issue (we decided to keep the json within web-component)
   - Tested web-component Button with icon on Dayforce, using Catalog button which wrapping platform-components button that wraps the web-component button with button using manifest.json.
   - We can keep button and checkbox as web-component, instead of porting everything over to React (A lot of effort)
   - We have to separate the Button Component into both Button and Icon Button components. This approach can work by updating the Button APIs to removed icon button-related APIs and Creating an Icon Button React component wrapping the web component.
   - We can achieve to create all the new Components in React. Only icon in web component.

1. Keep web component and create independent react Component

   - Improved the web component for hover/active icon color for the button
   - There will be a discrepancy on the icon component, when we have to update icon, it is required to update both the web component and react Component.
   - A lot of potential maintained issues.

1. Create icon as react component

   - Create all new component as React
   - Update web-component to use slot for any child react component (icon) [WC + React](https://wiki.dayforce.com/pages/viewpage.action?pageId=201069884)
   - More effort compare to "Keep webcomponent and create react Wrapper"

1. Create icon as React component and port all web component to react component

   - To keep it consistent.
   - Technically will be easier to maintain
   - Redo the work done before for the web component, a lot more effort when considering we have to re-test and go thru the PBIs checklist
   - When porting all the web components to React, we would retire the web component, any future development for web components might be halted. and it will affect the decision of if we are continuing to support web-component finalized after Beta1

**Decision**: Keep web component and create react Wrapper

## Icon Manifest Structure

### Single icon-manifest.json

An icon-manifest.json file and using json structure to define different icons type.

- System icons
  - Core icons
  - Feature icons
  - Navigation icons
- Spotlight icons (Out of scope)
- Illustrative icons (Out of scope)

Example:

```
{
  "system-icons": {
    "right-chevron": {
      "category": 0,
      "svg": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16 12L10 6L8.59 7.41L13.17 12L8.59 16.59L10 18L16 12Z\"/></svg>"
    },
    "left-chevron": {
      "category": 0,
      "svg": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8 12L14 18L15.41 16.59L10.83 12L15.41 7.41L14 6L8 12Z\"/></svg>"
    }
  },
  "spotlight-icons": {
  },
  "illustrative-icons": {
  }
}
```

### Multiple manifest files

Multiple manifest files to handle different types of icons

- icon-manifest.json (core/navigation/feature icons)
- spotlight-icon-manifest.json
- illustrative-icon-manifest.json

**Decision**: Multiple files

1. icon-manifest.json (core/navigation icons)
2. spotlight-icon-manifest.json
3. illustrative-icon-manifest.json
4. [feature-name]-icon-manifest.json (feature icons - TBD)

## Changelog

11/15/2023 - Icon - Convert Web Component to React (**EDS-3783**)

07/26/2024 - Icon utilize static assets [EDS-4377](https://dayforce.atlassian.net/browse/EDS-4377)

1. Utilize static asset [platform-df-assets](https://github.com/DayforceGlobal/platform-df-assets) for icons resource
1. All icon processing has been moved to the `platform-df-assets` repository, significantly reducing our package size.
   - Parsed size: from 633.99KB to 393.98KB
   - Gzipped size: from 174.63KB to 101.18KB
1. The icon update process has been simplified. We no longer require `figma-icons.json` from Design. Instead, the process involves reading SVGs from a structured folder, streamlining SVG updates.
1. Instead of making individual HTTP requests for each SVG icon, we use `icon-manifest.json` (as SVG sprites). This allows a single HTTP request to retrieve all required information.
1. Implementing security sanitization for icon paths to prevent potential security risks, currently using `MarkupUtil.replaceMarkupTags`.

Future Consideration

1. Load icon-manifest.json from app_shell, this would increase the responsiveness of icon.

1. Research https://github.com/cure53/DOMPurify and possible replace `MarkupUtil.replaceMarkupTags`

1. MultiTones SVGs -> future implementation (TBD)
