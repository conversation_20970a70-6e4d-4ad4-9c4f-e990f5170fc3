import { Meta, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { Icon, ICON_NAMES, ICON_COLORS } from '../icon';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Icon"
  component={Icon}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  argTypes={{
    name: {
      type: { name: 'enum', required: true },
      control: 'select',
      options: ICON_NAMES.sort(),
    },
    fill: {
      type: { name: 'enum' },
      control: 'select',
      options: ICON_COLORS.sort(),
    },
  }}
  args={{
    name: 'add',
    fill: '--evr-interactive-primary-default',
    size: 'md',
    testId: 'test-id',
  }}
/>

# Icon

## Live Demo

<Canvas>
  <Story name="Small">{(args) => <Icon {...args} size="sm" />}</Story>
</Canvas>

<Canvas>
  <Story name="Medium">{(args) => <Icon {...args} size="md" />}</Story>
</Canvas>

<Canvas>
  <Story name="Large">{(args) => <Icon {...args} size="lg" />}</Story>
</Canvas>
