import React from 'react';
import { iconNames } from '@ceridianhcm/platform-df-assets';
import { colorNames } from '@ceridianhcm/theme';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import { Icon, ICON_NAMES, ICON_COLORS, ALLOWED_COLOR_TYPES } from './icon';

describe('[Icon]', () => {
  const testId = 'icon-test-id';
  /**
   * the engine that <PERSON><PERSON> uses by default to render React components is JSDOM, which doesn't support web components at this time.
   * using data-testid as the selector for icon
   * instead of using container and getElementsByTagName()
   */
  const getIcon = () => screen.getByTestId(testId);

  [
    {
      name: 'medium icon',
      jsx: <Icon testId={testId} name="add" fill="--evr-content-primary-lowemp" size="md" />,
    },
    {
      name: 'large icon',
      jsx: <Icon testId={testId} name="add" fill="--evr-content-primary-lowemp" size="lg" />,
    },
    {
      name: 'small icon',
      jsx: <Icon testId={testId} name="add" fill="--evr-content-primary-lowemp" size="sm" />,
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getIcon()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('Verify synchronization between the JSON files and the array constants', () => {
    it('iconNames.length equal to ICON_NAMES.length', () => {
      expect(ICON_NAMES.length).toEqual(iconNames.length);
    });

    it('ICON_COLORS.length should be less then colorNames', () => {
      expect(ICON_COLORS.length).toBeLessThan(colorNames.length);
    });

    it('should have interactive, content, inactive colors only', () => {
      colorNames.forEach((color) => {
        // color format is "--evr-interactive-status-error-default" we want the second word
        const colorType = color.slice(2).split('-')[1];
        if (ALLOWED_COLOR_TYPES.includes(colorType)) {
          expect(ICON_COLORS.includes(color)).toBeTruthy;
        }
      });
    });
  });

  it('should update the svg fill attribute', async () => {
    render(<Icon testId={testId} name="search" fill="--evr-content-primary-lowemp" size="md" />);
    expect(getIcon().getAttribute('fill')).toEqual('var(--evr-content-primary-lowemp)');
  });

  it(`should render md size`, async () => {
    render(<Icon testId={testId} name="search" fill="--evr-content-primary-lowemp" size="md" />);
    expect(getIcon().getAttribute('class')).toEqual('evrIcon md');
  });
});
