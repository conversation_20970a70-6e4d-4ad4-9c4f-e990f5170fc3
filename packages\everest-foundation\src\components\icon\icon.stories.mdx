import { Meta, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { Icon, ICON_NAMES, ICON_COLORS } from './icon';
import Examples from './icon.examples.mdx';

<Meta
  title="Components/Icons/Icon"
  component={Icon}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/XwOWhFtqzlWicOjfSe8GuR/%F0%9F%A7%AAIcons?node-id=874%3A1028',
    },
  }}
  argTypes={{
    name: {
      type: { name: 'enum', required: true },
      control: 'select',
      options: ICON_NAMES.sort(),
      description: 'Name of the icon.',
    },
    fill: {
      type: { name: 'enum' },
      control: 'select',
      options: ICON_COLORS.sort(),
      description: 'Color of the icon.',
    },
    size: {
      type: { name: 'enum' },
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the icon. Available sizes: ',
      table: {
        defaultValue: { summary: 'md' },
      },
    },
    testId: {
      description: 'An id used for automation testing.',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'Optional unique identifier.',
    },
  }}
  args={{
    name: 'add',
    fill: '--evr-interactive-primary-default',
    size: 'md',
    testId: 'test-id',
  }}
/>

# Icon

<Examples />

## Live Demo

<Canvas>
  <Story name="Icon">{(args) => <Icon {...args} />}</Story>
</Canvas>

<ArgsTable story="Icon" />
