import React, { useLayoutEffect, useRef } from 'react';
import { iconNames } from '@ceridianhcm/platform-df-assets';
import { colorNames } from '@ceridianhcm/theme';
import { MarkupUtil } from '@platform/core';
import classnames from 'classnames';

import { mergeRefs } from '../../utils';
import { useIconContext } from '../icon-provider';

import styles from './icon.module.scss';

export const ALLOWED_COLOR_TYPES = ['interactive', 'content', 'inactive'];
export const ICON_NAMES = iconNames;

export const ICON_COLORS = colorNames.filter((color: string) => {
  // color format is "--evr-interactive-status-error-default" we want the second word
  const colorType = color.slice(2).split('-');
  return ALLOWED_COLOR_TYPES.includes(colorType[1]);
});

export type TIconColor = (typeof ICON_COLORS)[number];

export type TIconName = (typeof iconNames)[number];

export type TIconSize = 'sm' | 'md' | 'lg';
export interface IIconProps {
  id?: string;
  testId?: string;
  name: TIconName;
  size?: TIconSize;
  fill?: TIconColor;
}

export const Icon = React.forwardRef<SVGSVGElement, IIconProps>((props: IIconProps, ref) => {
  const { id, testId, fill, name, size = 'md' } = props;
  const svgRef = useRef<SVGSVGElement | null>(null);

  const { iconManifest } = useIconContext() as unknown as {
    iconManifest: Record<TIconName, { paths: string }>;
  };

  useLayoutEffect(() => {
    const svgElement = svgRef.current;
    if (svgElement) {
      for (const child of Array.from(svgElement.children)) {
        // remove the `fill` attribute from the child elements to enable the parent component to control the fill colour using `fill` prop
        // future improvement: update the icon manifest in df-asset to exclude `fill` attribute from the paths
        child.removeAttribute('fill');
      }
    }
  }, [svgRef, iconManifest, name]);

  return (
    <svg
      id={id}
      data-evr-name={name}
      data-testid={testId ? testId : undefined}
      viewBox="0 0 24 24"
      className={classnames(styles.evrIcon, styles[size])}
      fill={`var(${fill})`}
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden={true}
      ref={mergeRefs([ref, svgRef])}
      // eslint-disable-next-line @typescript-eslint/naming-convention
      dangerouslySetInnerHTML={{ __html: MarkupUtil.replaceMarkupTags(iconManifest?.[name]?.paths || '').toString() }}
    ></svg>
  );
});

Icon.displayName = 'Icon';
