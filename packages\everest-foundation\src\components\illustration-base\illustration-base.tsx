import React from 'react';

import { getIllustrationUrl } from '../../utils/asset-helper';
import { useEverestContext } from '../everest-provider';
import { Image } from '../image/image';

export type TIllustrationBaseType = 'spot';

/**
 * Interface representing the base properties for an illustration component.
 */
export interface IIllustrationBase {
  /**
   * Unique identifier for the illustration.
   */
  id?: string;

  /**
   * Test identifier for use in testing frameworks.
   */
  testId?: string;

  /**
   * The name of the illustration. This is required and must correspond to a predefined type.
   */
  name: string;

  /**
   * The type of the illustration, defining its category.
   */
  type: TIllustrationBaseType;

  /**
   * Height of the illustration, specified as a string (e.g., "100px", "10rem").
   * @default auto
   */
  height?: string;

  /**
   * Width of the illustration, specified as a string (e.g., "100px", "10rem").
   * @default auto
   */
  width?: string;

  /**
   * Flag to render the illustration in an inverse color scheme.
   */
  inverse?: boolean;
}

export const IllustrationBase = React.forwardRef<HTMLImageElement, IIllustrationBase>((props, ref): JSX.Element => {
  const { id, testId, name, type, height = 'auto', width = 'auto', inverse } = props;
  // Convert name from camelCase to PascalCase
  const fileName = (name.charAt(0).toUpperCase() || '') + (name?.slice(1) || '');
  return (
    <Image
      id={id}
      testId={testId}
      ref={ref}
      src={getIllustrationUrl(useEverestContext().librariesUrl ?? '', type, fileName, inverse)}
      fit="contain"
      height={height}
      width={width}
      ariaHidden={true}
    ></Image>
  );
});

IllustrationBase.displayName = 'IllustrationBase';
