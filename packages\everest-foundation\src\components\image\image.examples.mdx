import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Image } from './image';
import { Button } from '../button';

export const scope = { Image, Button };

## Usage

### Default Image

The size of the image will fill 100% of the parent container's width and height. Adjust the width and height of the parent container to change the size of the image.

export const defaultExample = `
<div style={{ width: '300px', height: '300px' }}>
  <Image
    id='default-img'
    src="images/image-sample-1.jpg"
    alt="Mt. Everest during sunset"
  />
</div>
`;

<CodeExample scope={scope} code={defaultExample} />

### Title prop

The title prop can be used to show a tooltip while hovering over an image with the mouse pointer.

export const titlePropExample = `
<div style={{ width: '300px', height: '300px' }}>
  <Image
    id='title-prop-img'
    src="images/image-sample-1.jpg"
    alt="Mt. Everest during sunset"
    title="Mt. Everest during sunset"
  />
</div>
`;

<CodeExample scope={scope} code={titlePropExample} />

### Image with the fit prop set to "contain"

The **fit** property controls the way an image is rendered inside of the parent container. By default, if the **fit** prop is not set, the image will get the **"cover"** class assigned to it. This property sets the corresponding class to an image which assigns the **object-fit** property to the img element.
Possible options: **"cover"** and **"contain"**. Border is used in this example to demonstrate the dimensions of the image container vs the contained image dimensions.

export const fitExample = `
<div style={{ width: '300px', height: '300px', border: '2px solid #666' }}>
  <Image
    id='fit-img'
    src="images/image-sample-1.jpg"
    alt="Mt. Everest during sunset"
    fit="contain"
  />
</div>
`;

<CodeExample scope={scope} code={fitExample} />

### Radius prop

The **radius** property controls how the **border-radius** CSS attribute is set on the image. The **radius** property accepts only a value from the list of pre-defined design tokens: **3xs** | **2xs** | **xs** | **sm** | **md** | **lg** | **circle**.

export const radiusExample = `
<div style={{ width: '300px', height: '300px' }}>
  <Image
    id='radius-img'
    src="images/image-sample-1.jpg"
    alt="Mt. Everest during sunset"
    radius="md"
  />
</div>
`;

<CodeExample scope={scope} code={radiusExample} />

If you would like to use the **radius** prop along with the **fit** property, this might not work well in all of the screnarios. By default, the **fit** property is set to **cover** which means that the rendered image will take 100% width and height of the **&lt;img&gt;** element's dimensions. In this scenario, the border radius css attribute will give image some rounded shape. However, if you try to use the **fit** property set to **contain**, the image will not take 100% width and hight of the **&lt;img&gt;** element's dimensions, unless the image is **square**! In a case of a non-square image, its borders might not get circled as expected. Here is such an example of this scenario:

export const radiuswithContainedNonSqaureImageExample = `
<div style={{ width: '300px', height: '300px' }}>
  <Image
    id='radius-contained-nonsquare-img'
    src="images/image-sample-1.jpg"
    alt="Mt. Everest during sunset"
    radius="circle"
    fit="contain"
  />
</div>
`;

<CodeExample scope={scope} code={radiuswithContainedNonSqaureImageExample} />

The recommendation in this case is to not use the **contain** value for the **fit** prop with a **non-square** image. Below are shown a few examples of how the combination of **fit** and **radius** properties work well.

export const radiuswithCoveredSquareImageExample = `
<div style={{ width: '300px', height: '300px' }}>
  <Image
    id='radius-covered-square-img'
    src="images/image-sample-1.jpg"
    alt="Mt. Everest during sunset"
    radius="circle"
    fit="cover"
  />
</div>
`;

<CodeExample scope={scope} code={radiuswithCoveredSquareImageExample} />

Below is an example of a **square** image used with a **contained** image.

export const radiuswithContainedSquareImageExample = `
<div style={{ width: '300px', height: '300px' }}>
  <Image
    id='radius-contained-square-img'
    src="images/image-sample-square.png"
    alt="Mt. Everest during sunset"
    radius="lg"
    fit="contain"
  />
</div>
`;

<CodeExample scope={scope} code={radiuswithContainedSquareImageExample} />

## Accessing Image using ref

Click on the Button to access the Image, refer to the console for the element details.

export const refCode = `()=>{
    const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
        column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '10px'
        }
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
      <Column>
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
        </Row>
        <Row>
            <div style={{ width: '300px', height: '300px' }}>
              <Image
                id='access-ref-img'
                ref={ref}
                src="images/image-sample-1.jpg"
                alt="Mt. Everest during sunset"
              />
            </div>
        </Row>
      </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## Accessibility

Accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

One of alt, ariaLabel, title must be set to ensure `<img>` element has alternate text.
