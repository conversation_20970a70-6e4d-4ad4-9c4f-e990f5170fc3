﻿# <a name="_hmmbg5ivp6kb"></a>Summary

Research and documenting on the Everest Image Container Component was made based on the Figma file: https://www.figma.com/file/lOkUqIkLHrAbWTCwlXqjMo/%F0%9F%A7%AAImage-Containers?node-id=1%3A73

Firstly, FAST Foundation Component Library doesn't have an Image or an Image Container or any similar component, so this component will be implemented using fast-element. There's no alternatives where to look at (Material UI / Bootstrap / Carbon / etc.), as this component doesn't have any functionality. All the APIs are supposed to only change the way our component looks.

## <a name="_tqo6yuonl026"></a>Ways to implement

**1. Using an image tag**

<img src="/img/test.jpg" alt="Test" />

This is a straightforward way of implementing an image container and probably a better one. An image should be rendered in all the display / accessibility modes and there should not be any accessibility issues.

**2. Use background-image CSS attribute on a div container to render an image**

<div class="img-container" style="background-image: url(/img/test.jpg);" role="img" title="test" aria-label="test"></div>

This approach is also possible because of the background-size css attribute which lets us control how the image is positioned (cover / contain). However, images are not accessible in non-graphical presentations, and background images specifically might be turned off in high-contrast display modes. This method seems to work fine for aesthetic purposes only, not to present data.

## <a name="_4vwvisawbz7l"></a>Q&A:

**How do we make an image fill or fit within a container?**

We need to use object-fit css property:

object-fit: fill;

object-fit: cover; // fit

**How do we need to position contained images within a container?**

The solution is to use the object-position css property;

object-position: left | right | bottom | center | top;

object-position: 5px 10%;

**Note**: designer's decision is that the image is always centered

**Do we need to use the “loading” image attribute?**

This attribute can be used to instruct the browser to defer loading of images/iframes that are off-screen until the user scrolls near them. It does not impact the design of the component, so no need to use it.

**Do we need to support image maps?**

We do need to support image maps because of accessibility issues. More details here:
<https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/useMap>

**NOTES:**
Border and border radius props will not be implemented into the image container component since we are going to use the shape component for that purpose. This is confirmed with the design team.

Also, the design team’s decision is that the image always takes 100% of width and height of a parent container, so therefore height and width props won’t exist on this component.

## <a name="_tjejn1k9xqed"></a>API:

1\. src (url)

Sets the url of the image.

2\. alt

Sets the alternative text (image description).

3\. imageFit: fill / cover

Specifies the way the image will be rendered inside of its parent container (using object-fit property).

4\. imageMap

A JSON array passed as a string to represent the area tags of a map element, e.g.:

[{ “shape”: “rect”, “coords”: [0, 0, 150, 300], “href”: “/test”, “alt”: “Test” }]

## <a name="_vh02bnos2lhm"></a>Other design systems

Couldn't find at least one implementation of the Image / Image Container component in any major design systems (e.g. Material UI / Bootstrap / Carbon / Microsoft FAST).

## <a name="_ye3v0gelxfun"></a>Acceptance Criteria:

1. The name of a React component is `<Image>`
1. 2. Available variants:
   1. fill (object-fit: fill;)
   1. fit (object-fit: cover;)
1. No mouseover icon
1. Image container width and height are always 100% width of the parent container
1. APIs:
   1. src
   1. alt
   1. type // fill/fit
   1. imageMap

## Change Log

[EDS-3784 Image - Convert Web Components to React](https://dayforce.atlassian.net/browse/EDS-3784)

[EDS-3947 [WC Conversion] Replace Image with ImageV2](https://dayforce.atlassian.net/browse/EDS-3947)

[[Image] Mock images in tests and use static assets in story](https://dayforce.atlassian.net/browse/PWEB-14486)

[PWEB-20499 Added `height`, `width`, and `aria-hidden` props to support `SpotIllustration`. Made `id` optional.](https://dayforce.atlassian.net/browse/PWEB-20499)
