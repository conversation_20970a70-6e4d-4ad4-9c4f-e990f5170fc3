import { <PERSON>a, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { Image } from './image';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Image"
  component={Image}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'image-id',
    src: 'images/image-sample-1.jpg',
    alt: 'Mt. Everest during sunset',
    title: 'Mt. Everest during sunset',
    testId: 'image-test',
    fit: 'cover',
    radius: 'none',
  }}
/>

# Image

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => (
      <div style={{ width: '300px', height: '300px' }}>
        <Image {...args} id={`${args.id}-default`} />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Object Fit Contain">
    {(args) => (
      <div style={{ width: '300px', height: '300px' }}>
        <Image {...args} id={`${args.id}-object-fit-contain`} fit="contain" />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Border Radius Circle">
    {(args) => (
      <div style={{ width: '300px', height: '300px' }}>
        <Image {...args} id={`${args.id}-border-radius-circle`} radius="circle" />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Border Radius Unset">
    {(args) => (
      <div style={{ width: '300px', height: '300px' }}>
        <Image {...args} id={`${args.id}-border-radius-unset`} radius={undefined} />
      </div>
    )}
  </Story>
</Canvas>
