import React from 'react';
import { render, screen } from '@testing-library/react';

import { Image } from './image';

describe('[Image]', () => {
  const id = 'image-id';
  const testId = 'image-test-id';
  const src = 'https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg';

  const getImage = () => screen.getByTestId(testId);

  it('should set src attribute for an image', async () => {
    render(<Image id={id} testId={testId} src={src} />);
    const innerImg = getImage();

    expect(innerImg?.getAttribute('src')).toEqual(src);
  });

  it('should set alt attribute for an image', async () => {
    render(<Image id={id} testId={testId} src={src} alt="test" />);
    const innerImg = getImage();

    expect(innerImg?.getAttribute('alt')).toEqual('test');
  });

  it('should set title attribute for an image', async () => {
    render(<Image id={id} testId={testId} src={src} title="test" />);
    const innerImg = getImage();

    expect(innerImg?.getAttribute('title')).toEqual('test');
  });
});
