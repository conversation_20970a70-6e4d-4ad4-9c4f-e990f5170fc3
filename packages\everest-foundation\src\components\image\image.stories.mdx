import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Image } from './image';
import Examples from './image.examples.mdx';

<Meta
  title="Components/Image"
  component={Image}
  parameters={{
    status: {
      type: 'ready',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/lOkUqIkLHrAbWTCwlXqjMo/%F0%9F%A7%AAImage-Containers?node-id=1%3A71',
    },
    controls: {
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    fit: {
      control: { type: 'radio' },
      options: ['cover', 'contain'],
    },
    radius: {
      control: { type: 'select' },
      options: ['none', '3xs', '2xs', 'xs', 'sm', 'md', 'lg', 'circle'],
    },
    connectedCallback: { table: { disable: true } },
    disconnectedCallback: { table: { disable: true } },
    selectTemplate: { table: { disable: true } },
  }}
  args={{
    id: 'image-id',
    src: 'images/image-sample-1.jpg',
    alt: 'Mt. Everest during sunset',
    title: 'Mt. Everest during sunset',
    testId: 'test-id',
    fit: 'cover',
    radius: 'none',
  }}
/>

# Image

<Examples />

## Live Demo

<Canvas>
  <Story name="Image">
    {(args) => (
      <div style={{ width: '300px', height: '300px' }}>
        <Image {...args} />
      </div>
    )}
  </Story>
</Canvas>

<ArgsTable story="Image" />
