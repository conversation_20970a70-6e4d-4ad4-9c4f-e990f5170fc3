import React, { forwardRef, ForwardedRef, SyntheticEvent } from 'react';
import classNames from 'classnames';

import styles from './image.module.scss';

export type TImageFit = 'contain' | 'cover';
export type TImageRadius = '3xs' | '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'circle';

export interface IImageProps {
  /**
   * Unique identifier.
   */
  id?: string;

  /**
   * An id used for automation testing.
   */
  testId?: string;

  /**
   * Sets the Picture.
   */
  src?: string;

  /**
   * Set of images that allow the browser to choose the image to load.
   */
  srcSet?: string;

  /**
   * Provides alternative information for an image should the image fail to display.
   */
  alt?: string;

  /**
   * Show a tooltip while hovering over an image with the mouse pointer.
   */
  title?: string;

  /**
   * Controls the way an image is rendered inside of the parent container.
   * Options: 'cover' | 'contain'
   */
  fit?: TImageFit;

  /**
   * The height of the image.
   */
  height?: string;

  /**
   * The width of the image.
   */
  width?: string;

  /**
   * Controls how the border-radius CSS attribute is set on the image.
   * Options: '3xs' | '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'circle'
   */
  radius?: TImageRadius;

  /**
   * A clear description of the Image's purpose.
   */
  ariaLabel?: string;

  /**
   * Function to handle error events during image loading.
   */
  onError?: (e: SyntheticEvent<HTMLImageElement>) => void;

  /**
   * React ref for accessing the underlying image element.
   */
  ref?: ForwardedRef<HTMLImageElement>;

  /**
   * Hides the image from screen readers when true.
   */
  ariaHidden?: boolean;
}

export const Image = forwardRef<HTMLImageElement, IImageProps>((props: IImageProps, ref) => {
  const {
    id,
    src,
    srcSet,
    alt,
    title,
    testId,
    fit = 'cover',
    height = '100%',
    width = '100%',
    radius,
    ariaLabel,
    onError,
    ariaHidden,
  } = props;

  return (
    <img
      ref={ref}
      id={id}
      src={src}
      srcSet={srcSet}
      alt={alt}
      title={title}
      onError={onError}
      className={classNames(styles.evrImage, styles[fit], styles[`borderRadius-${radius}`])}
      style={{ width, height }}
      data-testid={testId}
      aria-label={ariaLabel}
      aria-hidden={ariaHidden}
    />
  );
});

Image.displayName = 'Image';
