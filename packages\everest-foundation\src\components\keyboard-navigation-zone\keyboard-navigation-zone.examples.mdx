import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { KeyboardNavigationZone, IKeyboardNavMethod, IKeyboardNavZone } from '@ceridianhcm/everest-cdk';
import { ListBox } from '../list-box';
import { Button } from '../button';

export const scope = { KeyboardNavigationZone, ListBox, Button };

The KeyboardNavigationZone aims to abstracts arrow key navigation behaviors from components. It handles arrow key navigation and return appropriate index for consumption.

## Keys supported

- Use <kbd>Down Arrow</kbd> / <kbd>Up Arrow</kbd> to increase / decrease index if `mode` is `vertical`
- Use <kbd>Right Arrow</kbd> / <kbd>Left Arrow</kbd> to increase / decrease index if `mode` is `horizontal`
- Use <kbd>Home</kbd> to set index to first index
- Use <kbd>End</kbd> to set index to last index
- Use <kbd>Enter</kbd> / <kbd>Space</kbd> to trigger confirmation

## Usage

### Simple example

Simple example to show how index are incremented.

export const basicCode = `() => {
  const FocusableContainer = (props) => 
    <div tabIndex={0} style={{ padding: '15px', backgroundColor: 'wheat'}}>
        {props.children}
    </div>        
  return (
    <KeyboardNavigationZone total={5} render={(index) => {
      return <FocusableContainer>{index}</FocusableContainer>
    }} />
  )
}`;

<CodeExample scope={scope} code={basicCode} />

### Simple component example with listbox

export const basicCodeWithList = `() => {
  const data = [
    { id: 'first', title: 'First Option' },
    { id: 'second', title: 'Second Option' },
    { id: 'third', title: 'Third Option' },
    { id: 'fourth', title: 'Fourth Option' },
  ];
  return (
    <KeyboardNavigationZone
      total={data.length}
      focusable
      render={(index) => {
        return <ListBox
          softSelectedIndex={index}
          options={data}
        />
      }} />
  )
}`;

<CodeExample scope={scope} code={basicCodeWithList} />

### Exclusion example

Setting exclusion will inform `KeyboardNavigationZone` to skip the index defined in `exclude`. Please note, you still have to handle how your component will handle excluded index.

export const basicCodeWithListExclude = `() => {
  const [exclude, setExclude] = React.useState([]);
  const DivContainer = (props) => <div>{props.children}</div>;
  const rawdata = [
    { id: 'first', title: 'First Option' },
    { id: 'second', title: 'Second Option' },
    { id: 'third', title: 'Third Option' },
    { id: 'fourth', title: 'Fourth Option' },
  ];
  const data = rawdata.map((item, index) =>
    exclude.indexOf(index) >= 0 ? { ...item, title: item.title + ' - Excluded' } : item
  );
  return (
    <DivContainer>      
      <KeyboardNavigationZone
        total={data.length}
        exclude={exclude}
        focusable
        render={(index) => {
          return <ListBox softSelectedIndex={index} options={data} />;
        }}
      />
      <Button id='exclude-options-btn' label="Exclude Options!" onClick={() => setExclude([1, 2])} />
    </DivContainer>
  );
}`;

<CodeExample scope={scope} code={basicCodeWithListExclude} />
