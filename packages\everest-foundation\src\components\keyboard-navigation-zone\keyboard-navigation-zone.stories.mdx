import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { KeyboardNavigationZone } from '@ceridianhcm/everest-cdk';
// import { KeyboardNavigationZone } from './keyboard-navigation-zone';
import { ListBox } from '../list-box';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import Examples from './keyboard-navigation-zone.examples.mdx';

<Meta
  title="Toolbox/KeyboardNavigationZone"
  component={KeyboardNavigationZone}
  argTypes={{
    onBlur: {
      type: 'function',
      description: 'onBlur event handler',
    },
    loop: {
      type: 'boolean',
      description: 'Determine if circular navigation is needed',
      table: {
        defaultValue: { summary: false },
      },
    },
    mode: {
      type: 'string',
      control: 'select',
      options: ['vertical', 'horizontal'],
      description: '`vertical` will use `up`/`down` key. `horizontal` will use `left`/`right` key',
      table: {
        defaultValue: { summary: 'vertical' },
      },
    },
    total: {
      description: 'Set the total count',
      control: { type: null },
    },
    focusable: {
      description: 'Decide if the children of the zone will be focusable.',
      control: { type: null },
    },
    disabled: {
      control: { type: null },
    },
    stopFocusPropagation: {
      description: 'If true, stopPropgation will run when arrow / confirmation / pre-defined keys are pressed',
      control: { type: null },
    },
    resetIndexOnBlur: {
      description: ' If true, will reset index when blur out of the KeyboardNavigationZone',
      control: { type: null },
    },
    defaultIndex: {
      description: 'The initial starting index of the zone',
      control: { type: null },
    },
    exclude: {
      description: 'Defined which indices to exclude from the zone',
      control: { type: null },
    },
  }}
  args={{
    mode: 'vertical',
    total: 0,
    loop: false,
    focusable: false,
    render: () => null,
    onConfirm: () => null,
    onIndexChanged: () => null,
    onBlur: () => null,
    onKeyDown: () => null,
    exclude: [],
    defaultIndex: -1,
    disabled: false,
    stopFocusPropagation: false,
    resetIndexOnBlur: false,
  }}
/>

# Keyboard Navigation Zone

<Examples />

## Live Demo

<Canvas>
  <Story name="KeyboardNavigationZone">
    {(args) => {
      const data = [
        { id: 'first', title: 'First Option' },
        { id: 'second', title: 'Second Option' },
        { id: 'third', title: 'Third Option' },
        { id: 'fourth', title: 'Fourth Option' },
      ];
      const [exclude, setExclude] = React.useState([]);
      const [hardIndex, setHardIndex] = React.useState(-1);
      return (
        <KeyboardNavigationZone
          total={data.length}
          loop={args['loop']}
          mode={args['mode']}
          exclude={exclude}
          onConfirm={(index) => setHardIndex(index)}
          focusable
          render={(index) => {
            return <ListBox softSelectedIndex={index} selectedIndex={[hardIndex]} options={data} />;
          }}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="KeyboardNavigationZone" />
