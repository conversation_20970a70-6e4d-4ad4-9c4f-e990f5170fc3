@use '@ceridianhcm/theme/dist/scss/' as typography;
@use '../../variables.scss';

.evrLabel {
  :global(.evrCaptionText) {
    color: var(
      --evr-content-primary-lowemp
    ); // this is necessary as we do not have a typography class for floating-label spec at this time

    & .disabled {
      color: var(--evr-inactive-content);
    }
  }

  & .negativeIcon {
    color: var(--evr-content-status-error-default);
    margin-inline-start: variables.$labelAsteriskMarginLeft;
  }

  & .disabled {
    color: var(--evr-inactive-content);
  }

  &.container {
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;

    &.alignStart {
      align-items: start;
    }

    &.disabled {
      cursor: not-allowed;
    }
  }

  &.labelText {
    height: 100%;

    &.padding {
      padding-inline-start: var(--evr-spacing-3xs);
      padding-inline-end: var(--evr-spacing-3xs);
    }
  }
}
