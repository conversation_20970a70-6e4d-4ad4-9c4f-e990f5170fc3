import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Label } from './label';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Label"
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  component={Label}
  args={{
    text: 'This is a Label component',
    testId: 'label-wrap-test-id',
  }}
/>

# Label

## Live Demo

<Canvas>
  <Story name="BodyText1">{(args) => <Label {...args}>{args.text}</Label>}</Story>
</Canvas>

<Canvas>
  <Story name="BodyText1 disabled">
    {(args) => (
      <Label {...args} disabled>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="BodyText1 with text padding">
    {(args) => (
      <Label {...args} textPadding>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="BodyText2">
    {(args) => (
      <Label {...args} variant="bodyText2">
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="BodyText2 disabled">
    {(args) => (
      <Label {...args} variant="bodyText2" disabled>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="BodyText2 with text padding">
    {(args) => (
      <Label {...args} variant="bodyText2" textPadding>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Caption">
    {(args) => (
      <Label {...args} variant="caption">
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Caption disabled">
    {(args) => (
      <Label {...args} variant="caption" disabled>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Caption with text padding">
    {(args) => (
      <Label {...args} variant="caption" textPadding>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Bold">
    {(args) => (
      <Label {...args} weight="bold">
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Bold disabled">
    {(args) => (
      <Label {...args} weight="bold" disabled>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Bold with text padding">
    {(args) => (
      <Label {...args} weight="bold" textPadding>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Required">
    {(args) => (
      <Label {...args} required>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Required disabled">
    {(args) => (
      <Label {...args} required disabled>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Required with text padding">
    {(args) => (
      <Label {...args} required textPadding>
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Wrapped input">
    {(args) => (
      <Label {...args} textPadding>
        <input type="checkbox" data-testid="wrapped-input-id" />
        {args.text}
      </Label>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Attached input">
    {(args) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <input type="checkbox" id="attached-input-id" data-testid="attached-input-test-id" />
        <Label {...args} textPadding htmlFor="attached-input-id" testId="attached-label-test-id">
          {args.text}
        </Label>
      </div>
    )}
  </Story>
</Canvas>
