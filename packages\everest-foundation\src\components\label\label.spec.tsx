import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { Label } from './label';

describe('[Label]', () => {
  const testId = 'label-wrap-test-id';
  const getLabel = () => screen.getByTestId(testId);
  [
    {
      name: 'Default',
      jsx: <Label testId={testId}>Label Text</Label>,
    },
    {
      name: 'Default disabled',
      jsx: (
        <Label testId={testId} disabled>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Default with text padding',
      jsx: (
        <Label testId={testId} disabled textPadding>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Caption',
      jsx: (
        <Label testId={testId} variant="caption">
          Label Text
        </Label>
      ),
    },
    {
      name: 'Caption disabled',
      jsx: (
        <Label testId={testId} variant="caption" disabled>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Caption with text padding',
      jsx: (
        <Label testId={testId} variant="caption" textPadding>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Bold',
      jsx: (
        <Label testId={testId} weight="bold">
          Label Text
        </Label>
      ),
    },
    {
      name: 'Bold disabled',
      jsx: (
        <Label testId={testId} weight="bold" disabled>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Bold with text padding',
      jsx: (
        <Label testId={testId} weight="bold" textPadding>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Required',
      jsx: (
        <Label testId={testId} required>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Required disabled',
      jsx: (
        <Label testId={testId} required disabled>
          Label Text
        </Label>
      ),
    },
    {
      name: 'Required with text padding',
      jsx: (
        <Label testId={testId} required textPadding>
          Label Text
        </Label>
      ),
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getLabel()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  it('should display label text content when provided', () => {
    const labelText = 'This is a label';
    const { getByText } = render(
      <Label weight="bold" testId={testId}>
        {labelText}
      </Label>
    );
    expect(getByText(labelText)).toBeInTheDocument();
  });

  it('should display asterisk * when required is set', () => {
    const { getByText } = render(
      <Label required testId={testId}>
        Label Text
      </Label>
    );
    expect(getByText('*')).toBeInTheDocument();
  });

  it('should render child react component', () => {
    const { getByText } = render(
      <Label testId={testId}>
        Label Text
        <div>Child Element</div>
      </Label>
    );
    expect(getByText('Child Element')).toBeInTheDocument();
  });

  it('should focus child react component when clicked', async () => {
    const labelText = 'Label Text';
    const { getByText, getByTestId } = render(
      <Label testId={testId}>
        {labelText}
        <input type="text" data-testid="text-input" />
      </Label>
    );
    await userEvent.click(getByText(labelText));
    expect(getByTestId('text-input')).toHaveFocus();
  });

  it('should focus the attached input react component when clicked', async () => {
    const labelText = 'Label Text';
    const { getByText, getByTestId } = render(
      <>
        <input type="text" data-testid="test-text-input" id="text-input" />
        <Label htmlFor="text-input">{labelText}</Label>
      </>
    );
    await userEvent.click(getByText(labelText));
    expect(getByTestId('test-text-input')).toHaveFocus();
  });
});
