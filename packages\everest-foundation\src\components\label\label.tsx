import React from 'react';
import classnames from 'classnames';

import { getClassName } from './label-helper';
import { mergeRefs } from '../../utils';
import { useCreateTestId } from '../../utils/use-create-testid';

import styles from './label.module.scss';

export type TLabelAlignment = 'center' | 'start';

export interface ILabelProps {
  weight?: 'regular' | 'bold';
  variant?: 'bodyText1' | 'bodyText2' | 'caption';
  required?: boolean;
  disabled?: boolean;
  testId?: string;
  id?: string;
  dir?: 'ltr' | 'rtl';
  htmlFor?: string;
  textPadding?: boolean;
  ref?: React.ForwardedRef<HTMLLabelElement>;
  onClick?: (e: React.MouseEvent) => void;
  alignment?: TLabelAlignment;
  /**
   * @deprecated This property should not be used.
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  _tabIndex?: number;
}

export const Label = React.forwardRef<HTMLLabelElement, React.PropsWithChildren<ILabelProps>>(
  (props: React.PropsWithChildren<ILabelProps>, ref): JSX.Element => {
    const {
      weight = 'regular',
      testId,
      children,
      required = false,
      variant = 'bodyText1',
      disabled = false,
      id,
      htmlFor,
      textPadding = false,
      onClick,
      alignment = 'center',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      _tabIndex,
      // dir,
    } = props;
    const dataRef = useCreateTestId(testId);

    const labelText = (text: string) => (
      <span
        className={classnames(
          styles.evrLabel,
          styles.labelText,
          getClassName(variant, weight),
          { [styles.disabled]: disabled },
          { [styles.padding]: textPadding }
        )}
      >
        {text}
        {required && (
          <span
            className={classnames(styles.negativeIcon, {
              [styles.disabled]: disabled,
            })}
            //Hiding the asterik from screen reader. Since SR will read out "required" when input is focused, there is no need for SR to read out "star".
            aria-hidden="true"
          >
            *
          </span>
        )}
      </span>
    );

    const childWrapper = () => {
      return React.Children.map(children, (child) => {
        if (typeof child === 'string') return labelText(child);
        return child;
      });
    };

    return (
      // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
      <label
        className={classnames(styles.evrLabel, styles.container, {
          [styles.disabled]: disabled,
          [styles.alignStart]: alignment === 'start',
        })}
        id={id}
        ref={mergeRefs([ref, dataRef])}
        htmlFor={htmlFor}
        onClick={onClick}
        tabIndex={_tabIndex}
      >
        {childWrapper()}
      </label>
    );
  }
);

Label.displayName = 'Label';
