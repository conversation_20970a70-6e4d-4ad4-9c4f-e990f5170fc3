import * as React from 'react';
import classnames from 'classnames';

import { ILayoutContext, LayoutContext } from '../../layout-context';

import styles from './body-content.module.scss';

export const BodyContent = ({ children }: React.PropsWithChildren<ILayoutContext>): JSX.Element => {
  const context = React.useContext(LayoutContext);
  return (
    <div className={classnames(context.contextPanelOpen ? [styles.bodyWidth70] : [styles.bodyWidth100])}>
      {children}
    </div>
  );
};
