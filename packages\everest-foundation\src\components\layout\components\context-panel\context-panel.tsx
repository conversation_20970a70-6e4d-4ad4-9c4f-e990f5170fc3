import * as React from 'react';

import { ILayoutContext, LayoutContext } from '../../layout-context';

import styles from './context-panel.module.scss';

export const ContextPanel = ({ children }: React.PropsWithChildren<ILayoutContext>): JSX.Element | null => {
  const context = React.useContext(LayoutContext);
  return context.contextPanelOpen ? <div className={styles.contextPanelWidth}>{children}</div> : null;
};
