import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Layout } from './layout';
import { Card } from '../card';
import { Button } from '../button';
import { Image } from '../image';
import { BodyContent } from './components/body-content/body-content';
import { ContextPanel } from './components/context-panel/context-panel';
import { AlphaBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';

<AlphaBanner />

export const scope = {
  Layout,
  BodyContent,
  ContextPanel,
  Card,
  Image,
  Button,
};

Layout is a macro page level container, which consists of 2 columns with right end column being optional.

## Variations

### One Column Layout

With one column layout, one column will take 100% width of the viewport screen.

export const oneColumnLayoutCode = `() => {
    const styles = {
        buttonContainer: {
            margin: '1rem 0 2rem 0'
        },
        cardSection: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: '25rem'
        }
    };
    const Header = ({ children }) => (
        <div>{children}</div>
    );
    const ButtonContainer = ({ children }) => (
        <div style={styles.buttonContainer}>{children}</div>
    );
    const CardSection = ({ children }) => (
        <div style={styles.cardSection}>{children}</div>
    );
    const CardContainer = ({ children }) => (
        <div style={styles.cardContainer}>{children}</div>
    );
    return (
        <Layout contextPanelOpen={false}>
            <Layout.BodyContent>
                <Header>
                    <h1 className='evrHeading1'>Intelligence at Work</h1>
                    <h2 className='evrHeading2'>It takes smart technology to outsmart
                    a changing world</h2>
                    <ButtonContainer>
                        <Button id='learn-about-dayforce-btn' label="Learn about Dayforce" />
                    </ButtonContainer>
                </Header>
                <CardSection>
                    <CardContainer>
                        <Card>
                            <Card.Media>
                                <Image
                                    id='one-column-layout-img-1'
                                    src="images/image-sample-1.jpg"
                                    alt="Mount Everest during sunset"
                                    title="Mount Everest during sunset"
                                />
                            </Card.Media>
                            <Card.Content>
                                <Card.Header 
                                    title="Work Smarter, Not Harder"
                                    description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua." />
                            </Card.Content>
                        </Card>
                    </CardContainer>
                    <CardContainer>
                        <Card>
                            <Card.Media>
                                <Image
                                    id='one-column-layout-img-2'
                                    src="images/image-sample-2.jpeg"
                                    alt="Lake Moraine"
                                    title="Lake Moraine"
                                />
                            </Card.Media>
                            <Card.Content>
                                <Card.Header 
                                    title="Tech employees will love"
                                    description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua." />
                            </Card.Content>
                        </Card>
                    </CardContainer>
                </CardSection>
            </Layout.BodyContent>
        </Layout>
    );
}`;

<CodeExample scope={scope} code={oneColumnLayoutCode} />

### Two Columns Layout

With two columns layout, center column takes 70% of width and right end column takes remaining 30% of width at above 960px viewport width.
Under 960px breakpoint, right end column will disappear and center column will take 100% of the screen.

export const twoColumnsLayoutCode = `() => {
    const styles = {
        buttonContainer: {
            margin: '1rem 0 2rem 0'
        },
        cardSection: {
            display: 'flex',
            flexDirection: 'column',
            gap: '1rem'
        },
        cardContainer: {
            width: '18.75rem',
            height: 'inherit'
        }
    };
    const Header = ({ children }) => (
        <div>{children}</div>
    );
    const ButtonContainer = ({ children }) => (
        <div style={styles.buttonContainer}>{children}</div>
    );
    const CardSection = ({ children }) => (
        <div style={styles.cardSection}>{children}</div>
    );
    const CardContainer = ({ children }) => (
        <div style={styles.cardContainer}>{children}</div>
    );
    return (
        <Layout contextPanelOpen={true}>
            <Layout.BodyContent>
            <Header>
                <h1 className='evrHeading1'>Intelligence at Work</h1>
                <h2 className='evrHeading2'>It takes smart technology to outsmart
                a changing world</h2>
                <ButtonContainer>
                    <Button id='learn-about-dayforce-btn' label="Learn about Dayforce" />
                </ButtonContainer>
            </Header>
            </Layout.BodyContent>
            <Layout.ContextPanel>
                    <CardSection>
                        <CardContainer>
                            <Card>
                                <Card.Media>
                                    <Image
                                        id='two-column-layout-img-1'
                                        src="images/image-sample-1.jpg"
                                        alt="Mount Everest during sunset"
                                        title="Mount Everest during sunset"
                                    />
                                </Card.Media>
                                <Card.Content>
                                    <Card.Header 
                                        title="Work Smarter, Not Harder"
                                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua." />
                                </Card.Content>
                            </Card>
                        </CardContainer>
                        <CardContainer>
                            <Card>
                                <Card.Media>
                                    <Image
                                        id='two-column-layout-img-2'
                                        src="images/image-sample-2.jpeg"
                                        alt="Lake Moraine"
                                        title="Lake Moraine"
                                    />
                                </Card.Media>
                                <Card.Content>
                                    <Card.Header 
                                        title="Tech employees will love"
                                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua." />
                                </Card.Content>
                            </Card>
                        </CardContainer>
                    </CardSection>
            </Layout.ContextPanel>
        </Layout>
    );
}`;

<CodeExample scope={scope} code={twoColumnsLayoutCode} />

## How to Use

Layout component consists of 2 sub-components, which are BodyContent and ContextPanel. There is only one prop for Layout component - `contextPanelOpen`.
By default, `contextPanelOpen` props is set to false. When `contextPanelOpen` is false, only `<Layout.BodyContent>` should be used which sets width of container at 100%.
When `contextPanelOpen` is true then both `<Layout.BodyContent>` and `<Layout.ContextPanel>` should be used.
In this case, `<Layout.BodyContent>` will take 70% width of container and remaining 30% is allocated to `<Layout.ContextPanel>`. Both sub-components should always be nested inside of `<Layout>`.

## Accessibility

For Layout component, there is no accessibility requirement as main purpose is to provide macro level container.
