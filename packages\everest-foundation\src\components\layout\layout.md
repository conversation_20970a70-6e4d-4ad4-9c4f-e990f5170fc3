# Layout

## Summary

Research and document implementations for the Everest Layout.

- Start Date: 2022-03-09
- Figma link: https://www.figma.com/file/bVrR2qp7zcrf8HOMDWZDml/%F0%9F%A7%AALayout

## Detailed Design

Layout is page level container that has body section and optional context panel section on the right end column. Fluid percentage approach will be applied, where layout will be adjusted based on the percentage of viewport width. In this case, starting at width of 960px and above, body will take 70% of the screensize and context panel will take 30% when expanded. If there is no context panel then body will occupy 100%. To accommodate this, 2 sub-components will be used: BodyContent and ContextPanel
Here is the structure of Layout component:

```
<Layout>
   <Layout.BodyContent>
   </Layout.BodyContent>
   <Layout.ContextPanel>
   </Layout.ContextPanel>
</Layout>
```

## API

There are 3 components with different API

**Layout**

1. **testId**: `string | undefined`  
   Optional. Sets `data-testid` attribute on the containing html element.
1. **id**: `undefined | string`  
   Optional
1. **contextPanelOpen**: `boolean | undefined`
   Decides whether to include context panel or not.

## Accessibility

There is no accessibility requirement for page level container. However, there was one design system that had `<main>` and `<aside>` elements built in, which are HTML sectioning elements that by default define ARIA landmark roles.

## Q&A

**Why Fluid percentage approach instead of responsive or adaptive layout?**
Dynamic right panel dictated this approach. Combination of percentage, min/max width and various breakpoints will be used to create macro page layout.

**While a liquid layout will look good across a wide range animationof widths, it will begin to worsen at the extremes. On a wide screen the layout looks stretched. On a narrow screen the layout looks squashed. Both scenarios aren't ideal. How did you over come this?**
By setting `min-width` and `max-width` pixel on certain column this problem can be mitigated.

**Will left naviation panel always be there?**
Yes, so far Ceridian is going with 3 columns page layout structure for future Dayforce page design. Since left navigation panel will be part of app, we have decided to focus on rest 2 columns page layout. This is subject to change as nothing is set in stone.

**How is right side panel be populated?**
According to designer, "flyouts" may be used, which acts like modal where content will lay on top of base content. There is on going discussion and nothing has been approved for now.

**How will component inside of body content pass the information to Layout component to enable/disable the contextPanelOpen prop?**
Context api from React will be used to communicate between sub-components of Layout.

**Can flexbox or css grid be used to build Layout component?**
Yes, but needs to be very cautious as it might restrict consumer's ability to create content with various designs inside the page layout. The grid system will be looked into separately down the road once we gain more context.

**Can CSS Container Queries be used for this component?**
CSS Container Quesries could come in handy for micro page layout as it is useful when changing elements based on size of parent element rather than browser window. Biggest drawback is it is still in experimental phase so we cannot implement this in production, but we will keep this in mind for potential future use.

## Future Considerations

Micro page layout that consists of gutter/margin/grid are under considertation to be implemented inside of macro page layout in the future. Currently, we don't know how left navigation will look like and how right side panel will be populated. There are a lot of uncertainties, therefore we expect more changes to come in the future.

## Other Design Systems

For example:

**Material UI** - https://mui.com/components/container/

- Supports fluid and fixed witdth of the container.
- Centers contents horizontlly and does not support multi-columns based container.

**Shopify Polaris** - https://polaris.shopify.com/components/structure/layout

- Supports up to 3 columns container.
- Uses flexbox to control the width of the container.
- For two columns layout, 2/3 + 1/3 layout dimension is applied(Our scenario require 70% + 30% so this technique can't be applied)
- Layout stacks the columns on small screens.

**Orbit Kiwi** - https://orbit.kiwi/components/layout/layout/

- Supports responsive width with 4 different breakpoints.
- Separate layout was created specifically for desktop view.
- Controls the width of the column by combining percentage width and px based max-width. Code below is example for large mobile view(576px).

```
   margin: 0px auto;
   width: 100%;
   max-width: 576px;
```

**Zendesk Garden** - https://zendeskgarden.github.io/react-containers/?path=/story/splitter-container--container&args=defaultValueNow:203

- Supports up to 3 columns where user can click and drag the pane to control the size of the width.
- Horizontal and vertical orientation are supported where 3 columns can instantly turn into 3 rows by changing orientation prop.
- Supports fixed/variable type and also max/min width.
- Even supports rtl/ltr direction! This is the most versatile layout that's been found so far.
- Mainly uses flexbox, which seems to be most popular technique for controlling size of the layout.

**Duet Design System** - https://www.duetds.com/components/layout/

- Built in `<main>` and `<aside>` elements to meet accessibility requirement.
- Uses #shadow-root technique. Was not able to identify pros/cons of using this technique for layout.

**Pluralsight Design System** - https://design-system.pluralsight.com/components/appframe

- Uses callback to trigger when the sidenav should be opened or closed. This is applied if controlling the open state.

**MongoDB Design System** - https://www.mongodb.design/foundation/grid/

- Based on 4px baseline grid system, applies css grid to control the margin and gutter.
- Width of the collapseable side nav bar is manually set at 184px max width and body content is set at 100% width.

## Required PBIs

- Develop the component and include testing (React + a11y + unit + integration + visual + bug fixes)

## Acceptance Criteria

1. The name of a component is &lt;Layout&gt;
2. APIs:  
   a. contextPanelOpen
3. When right side panel is enabled, width of right side panel must be 30% of viewport and the rest of body content must be 70% of viewport for 960px screen and above.
4. When right side panel is disabled, width of body content becomes 100%.
5. For screen below 960px, width of body content becomes 100%.
