import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Layout } from './layout';
import { Card } from '../card';
import { Image } from '../image';
import { Button } from '../button';
import { BodyContent } from './components/body-content/body-content';
import { ContextPanel } from './components/context-panel/context-panel';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Layout"
  component={Layout}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/bVrR2qp7zcrf8HOMDWZDml/%F0%9F%A7%AALayout?node-id=681%3A10897',
    },
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: {
        breakpointXs: { disable: true }, // disable mobile mode for page layout story
        breakpointXxl: defaultModes['breakpointXxl'],
      },
    },
  }}
  argTypes={{
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets the `data-test-id` attribute on the html element for automation testing.',
    },
    contextPanelOpen: {
      type: 'boolean',
      control: 'boolean',
      description: 'Sets the option to open right panel.',
      table: {
        defaultValue: { summary: false },
      },
    },
  }}
  args={{
    testId: 'layout-test-id',
  }}
/>

# Layout

## Live Demo

<Canvas>
  <Story name="One Column Layout">
    {(args) => {
      return (
        <Layout {...args} contextPanelOpen={false}>
          <Layout.BodyContent>
            <h1 className="evrHeading1">Intelligence at Work</h1>
            <h2 className="evrHeading2">It takes smart technology to outsmart a changing world</h2>
            <div style={{ marginTop: '1rem' }}>
              <Button id="learn-about-dayforce-btn" label="Learn about Dayforce" />
            </div>
          </Layout.BodyContent>
          <Layout.ContextPanel>
            <Card>
              <Card.Media>
                <Image
                  id="one-column-layout-img"
                  src="images/image-sample-1.jpg"
                  alt="Mount Everest during sunset"
                  title="Mount Everest during sunset"
                />
              </Card.Media>
              <Card.Content>
                <Card.Header
                  title="Work Smarter, Not Harder"
                  description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
                />
              </Card.Content>
            </Card>
          </Layout.ContextPanel>
        </Layout>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Two Columns Layout">
    {(args) => {
      return (
        <Layout {...args} contextPanelOpen={true}>
          <Layout.BodyContent>
            <h1 className="evrHeading1">Intelligence at Work</h1>
            <h2 className="evrHeading2">It takes smart technology to outsmart a changing world</h2>
            <div style={{ marginTop: '1rem' }}>
              <Button id="learn-about-dayforce-btn" label="Learn about Dayforce" />
            </div>
          </Layout.BodyContent>
          <Layout.ContextPanel>
            <Card>
              <Card.Media>
                <Image
                  id="two-column-layout-img"
                  src="images/image-sample-1.jpg"
                  alt="Mount Everest during sunset"
                  title="Mount Everest during sunset"
                />
              </Card.Media>
              <Card.Content>
                <Card.Header
                  title="Work Smarter, Not Harder"
                  description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
                />
              </Card.Content>
            </Card>
          </Layout.ContextPanel>
        </Layout>
      );
    }}
  </Story>
</Canvas>
