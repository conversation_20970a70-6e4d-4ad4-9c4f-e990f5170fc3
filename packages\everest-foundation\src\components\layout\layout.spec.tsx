import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import { Layout } from './layout';

const bodyContentText = 'This is a layout component';
const contextPanelText = 'This is a context panel';

describe('[Layout]', () => {
  const testId = 'layout-test-id';
  const getLayout = () => screen.getByTestId(testId);
  [
    {
      name: 'One column layout',
      jsx: (
        <Layout testId={testId} contextPanelOpen={false}>
          <Layout.BodyContent>{bodyContentText}</Layout.BodyContent>
        </Layout>
      ),
    },
    {
      name: 'Two columns layout',
      jsx: (
        <Layout testId={testId} contextPanelOpen={true}>
          <Layout.BodyContent>{bodyContentText}</Layout.BodyContent>
          <Layout.ContextPanel>{contextPanelText}</Layout.ContextPanel>
        </Layout>
      ),
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getLayout()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  it('should render body content when only body content is provided', () => {
    render(
      <Layout testId={testId} contextPanelOpen={false}>
        <Layout.BodyContent>{bodyContentText}</Layout.BodyContent>
      </Layout>
    );
    expect(screen.getByText(bodyContentText)).toBeInTheDocument();
  });

  it('should not render context panel when context panel is provided and contextPanelOpen is set to false,', () => {
    render(
      <Layout testId={testId} contextPanelOpen={false}>
        <Layout.BodyContent>{bodyContentText}</Layout.BodyContent>
        <Layout.ContextPanel>{contextPanelText}</Layout.ContextPanel>
      </Layout>
    );
    expect(screen.queryByText(contextPanelText)).not.toBeInTheDocument();
  });

  it('should render both body content and context panel when both body content and context panel are provided and contextPanelOpen is set to true', () => {
    render(
      <Layout testId={testId} contextPanelOpen={true}>
        <Layout.BodyContent>{bodyContentText}</Layout.BodyContent>
        <Layout.ContextPanel>{contextPanelText}</Layout.ContextPanel>
      </Layout>
    );
    expect(screen.getByText(bodyContentText)).toBeInTheDocument();
    expect(screen.getByText(contextPanelText)).toBeInTheDocument();
  });
});
