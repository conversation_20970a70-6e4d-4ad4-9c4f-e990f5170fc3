import * as React from 'react';
import classnames from 'classnames';

import { BodyContent } from './components/body-content/body-content';
import { ContextPanel } from './components/context-panel/context-panel';
import { ILayoutContext, LayoutContext } from './layout-context';
import { useCreateTestId } from '../../utils';

import styles from './layout.module.scss';

export interface ILayout extends ILayoutContext {
  id?: string;
  testId?: string;
  children: React.ReactNode;
}

export const Layout: React.FunctionComponent<ILayout> & {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  ContextPanel: typeof ContextPanel;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  BodyContent: typeof BodyContent;
  // eslint-disable-next-line @typescript-eslint/naming-convention
} = function Layout({ children, id, testId, contextPanelOpen }: ILayout) {
  const dataRef = useCreateTestId(testId);
  const context = React.useMemo(
    () => ({
      contextPanelOpen,
    }),
    [contextPanelOpen]
  );

  return (
    <LayoutContext.Provider value={context}>
      <div className={classnames(styles.evrLayout)} id={id} ref={dataRef}>
        {children}
      </div>
    </LayoutContext.Provider>
  );
};

Layout.ContextPanel = ContextPanel;
Layout.BodyContent = BodyContent;
