import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Link } from './link';
import { Icon } from '../icon';
import { BrowserRouter, Route, Routes, Link as ReactRouterDomLink } from 'react-router-dom';

export const scope = { Link, Icon, BrowserRouter, ReactRouterDomLink, Route, Routes };

The Link allows the user to navigate to a different place, such as a webpage or a specific section of a webpage. The Link itself is an `<a>` which does the navigation and Link is used for the styling purpose of the text only.

## Variations

`Link` has different Variations based on props being used. Default value of `variant` is `primary`.

export const variationsCode = `() => {
  const styles = {
        row: {
            width: '55%',
            marginBottom: '2.5rem'        
        },
        column: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%'
        },
        textStyle: {
            marginBlockEnd: '0.625rem',
            width: '70%'
        },
        darkBackgroundContainerStyle: {
            backgroundColor: 'rgb(51,51,51)', 
            height: '2.5rem', 
            width: '20%', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center'
        }
    };  
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const HeaderText = ({ children }) => (
        <div style={styles.textStyle}><p className='evrBodyText evrBold'>{children}</p></div>
    );
    const DescriptionText = ({ children }) => (
        <div style={styles.textStyle}><span className='evrBodyText2'>{children}</span></div>
    );
    const DarkBackgroundContainer = ({ children }) => (
        <div style={styles.darkBackgroundContainerStyle}>{children}</div>
    );
     return (
      <div style={styles.column}>
        <Row>
          <HeaderText>Primary Link</HeaderText>
          <DescriptionText>Set the variant prop to primary</DescriptionText>
          <p className="evrBodyText">
            <Link href="https://www.dayforce.com/ca" target="_blank" >Dayforce</Link>
          </p>
        </Row>
        <Row>
          <HeaderText>Neutral Link</HeaderText>
          <DescriptionText>Set the variant prop to neutral</DescriptionText>
          <p className="evrBodyText">
            <Link variant="neutral" href="https://www.dayforce.com/ca" target="_blank" >Dayforce</Link>
          </p>
        </Row>
        <Row>
          <HeaderText>Inherit Link</HeaderText>
          <DescriptionText>Set the variant prop to inherit. Color style is inherited from the parent.</DescriptionText>
          <p className="evrBodyText" style={{color:'var(--evr-surfaces-status-error-highemp)'}}>
            <Link variant="inherit" href="https://www.dayforce.com/ca" target="_blank" >Dayforce</Link>
          </p>
        </Row>
        <Row>
          <HeaderText>Inverse Link</HeaderText>
          <DescriptionText>The inverse prop is used to change the appearance of the Link to be used on the dark surfaces. This prop only affects the primary link.</DescriptionText>
          <DarkBackgroundContainer>
            <p className="evrBodyText">
              <Link inverse href="https://www.dayforce.com/ca" target="_blank" >Dayforce</Link>
            </p>
          </DarkBackgroundContainer>
        </Row>
      </div>
    );
}
`;

<CodeExample scope={scope} code={variationsCode} />

### Link with Icon

Wrap the `Icon` with the link so that the `Icon` inherits the click and hovering behaviour of the Link. The `size` of the `Icon` should be `md`.

export const iconCode = `() => {
      const styles = {
        row: {
          display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
            gap: '1rem'
        },
      }
      const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
      );
      return (
         <Row>
            <p className="evrBodyText1">
              <Link href="https://www.dayforce.com/ca" target="_blank" >Dayforce Testing<Icon name="linkNewWindow" size="md" /></Link>
            </p>
            <p className="evrBodyText1">
              <Link href="https://www.dayforce.com/ca" target="_blank" variant="neutral" >Dayforce <Icon name="linkNewWindow" size="md" /></Link>
            </p>
            <p className="evrBodyText1" style={{color:'var(--evr-surfaces-status-error-highemp)'}}>
              <Link href="https://www.dayforce.com/ca" target="_blank" variant="inherit" >Dayforce<Icon name="linkNewWindow" size="md" /></Link>
            </p>
        </Row>
      );
}`;

<CodeExample scope={scope} code={iconCode} />

### Font Styles are inherited from the container

Font styles for example `font-size`, `font-family` etc. are inherited from the container.

export const inheritCode = `
() => {
      const styles = {
        row: {
          display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
            gap: '1rem'
        },
      }
      const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
      );
      return (
        <Row>
          <p className="evrBodyText">
            <Link href="https://www.dayforce.com/ca" target="_blank"> 
              Dayforce<Icon name="linkNewWindow" size="md" />
            </Link>
          </p>
          <p className="evrBodyText1">
            <Link href="https://www.dayforce.com/ca" target="_blank">
              Dayforce<Icon name="linkNewWindow" size="md" />
            </Link>
          </p>
          <p className="evrBodyText2">
            <Link href="https://www.dayforce.com/ca" target="_blank">
              Dayforce<Icon name="linkNewWindow" size="md" />
            </Link>
          </p>
        </Row>
      );
}`;

<CodeExample scope={scope} code={inheritCode} />

{/*

### Link with react-router-dom

The link can be used with third party routing libraries like <Link  href="https://reactrouter.com/en/6.13.0/start/overview" referrerPolicy="no-referrer" rel="noopener">React Router</Link>

export const reactRouterCode = `() => {
      return (
        <BrowserRouter>
            <Link>
              <ReactRouterDomLink to="https://www.dayforce.com/ca" target="_blank">
                <Icon name="linkNewWindow" size="md" />Dayforce
              </ReactRouterDomLink>
            </Link>
          <Routes>
            <Route path="/" />
          </Routes>
        </BrowserRouter>
      );
}`;

<CodeExample scope={scope} code={reactRouterCode} />
*/}

## How to Use

The `Link` is used for the styling purpose only and the validations should be handled by the Feature teams.

## Accessibility

Screen reader announces the text of the Link. If any additional information needs to be provided you can use `ariaLabel` or `ariaDescribedBy` properties to set the appropriate attributes.
