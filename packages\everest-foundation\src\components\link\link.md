# Link

## Summary

Research and document implementations for the Everest Link.

- Start Date: 2023-04-28
- Figma links:
  https://www.figma.com/file/4XOArTPa7My2rfW2b03Wk6/%F0%9F%A7%AA-Toggletip?node-id=14-4540&t=mZZe6Wfz08KupyjT-0
  https://www.figma.com/file/mOCsVs0Er9LRKs4V6ejGzZ/%F0%9F%A7%AATables?node-id=3361-2837&t=tmwjRsXbqIqvrdM2-0

## Detailed Design

The Link allows the user to navigate to a different place, such as a webpage or a specific section of a webpage. This component accepts the anchor element or any third-party routing libraries as a child which does the actual navigation and the Link is used for the styling purpose of the text only.

## API

1. **id**: `string`
   Mandatory. Sets the id of the link.
2. **testId**: `string | undefined`
   Optional. Sets **data-test-id** attribute on the link.
3. **download**: `string`
   Optional. The browser will treat the linked URL as a download.
   Note: In `<a>` download attribute can be used as a string and boolean, below are the examples.
   ```
   <a href="/image.jpg" download>
   <a href="/image.jpg" download="change_the_image_name">
   ```
   when download is passed as a prop in the `<Link>`, then by default typescript treats it as a boolean though the type is string and we can treat the `<Link>` as the above first example. In our implementation, set it to empty string (`download=''`) when the type of the download is boolean. If value is passed then set the value (`download={download}`).
4. **href**: `string`
   Optional. The URL that the hyperlink points to.
5. **hreflang**: `string`
   Optional. It specifies the language of the hyperlink page.
6. **ping**: `string`
   Optional. Used to set an URL. When the user clicks on the hyperlink a POST request is sent to the set URL.
7. **referrerpolicy**: `no-referrer | no-referrer-when-downgrade | origin | origin-when-cross-origin | same-origin |    strict-origin | strict-origin-when-cross-origin | unsafe-url`
   Optional. How much of the referrer to send when following the link.
8. **rel**: `string`
   Optional. Specifies the relationship between the current document and the linked document. Only used if the `href` is set.
9. **target**: `_self | _blank | _parent | _top`
   Optional. The default value is `_self`. Specifies where to open the linked document.
10. **type**: `string`
    Optional. Specifies the Internet media type of the linked document. Only used if the `href` is set.
11. **variant**: `primary | neutral`
    Default to primary. Mandatory. Sets the styling of the text.
12. **inverse**: `boolean`
    Optional. When set to true, changes the color of the text on the inverse surface.
13. **ref**: `ReactRef`
    Optional. Makes a reference to a DOM node.

Below are different scenarios of using the `<Link>`

## Simple anchor

### Usage

```
<Link href=www.google.com>
  Google
</Link>

```

### Internal Implementation

```
<FocusRing>
  <a href={href} className={styles.evrLink}>{children} </a>
</FocusRing>
```

## Simple anchor with icon

### Usage

```
<Link href=www.google.com>
  <Icon name="blah"/>
  Google
</Link>
```

## Internal approach

```
<FocusRing>
  <a href={href} className={styles.evrLink}>{children} </a>
</FocusRing>
```

Feature teams have to pass the `<Icon>` as a child of the `<a>` so that the `<Icon>` inherits the click and hovering behaviour of the `<a>`. We don't have to specifically handle the onClick callback on our side. Even though the `<Icon>` is a child, the underline behaviour of the link doesn't affect the `<Icon>` i.e. only the text will have underline.

There is a gap between the `<Icon>` and the text. This gap is set in the CSS by targeting the `<a>`'s first child and then setting the margin-right.

For hyperlink text that opens in a new tab, conventionally the `<Icon>` should be placed after the text, to appear at the end of a text.

## Using <Link> with react-router-dom

### Usage

```
Import { Link as ReactRouterDomLInk} from ‘react-router-dom’;

<ReactRouterDomLink to="employee/1">
  <Link>
    <Icon name="blah"/>
    Google
  </Link>
</ReactRouterDomLink>

```

### Internal Implementation

`<span>` is used in place of `<a>` when `href` is not provided as nested `<a>` is not a valid html.

When href is not provided.

```
<FocusRing>
  <span className={styles.evrLink}>{children}</span>
</FocusRing>

```

When href is provided.

```
<FocusRing>
  <a href={href} className={styles.evrLink}>{children} </a>
</FocusRing>
```

## Accessibility

FocusRing to appear on the `<a>` when focused.

## Q&A

**Should we handle the link validation?**  
Link validation should be handled by the feature team as Link is used just for the styling purpose only.

**Should there always be an underline?**
Yes, the link should always have an underline.

**Should the link have a focus ring?**
Yes, it should have a focus ring when focused and it should be around the text and the Icon.

**What should be the token for the inverse?**
Inverse tokens for the neutral variant are yet to be decided. For now, we are using the inverse for just the primary variant.

**Does the link has multiple colors?**
Yes, the link has two variants which set the color of the text and change color with respect to the `inverse` prop.

**Should we update the `<BreadcrumbItem>` with the `<LinkBase>`?**
Breadcrumb needs to be revisited later. Just create `<Link>` for now.

**How is the click of an Icon handled?**
Icon is passed as a child to the `<a>`, so Icon by default inherits the click behaviour of the `<a>`.

**What should happen when the Icon hovers?**
Icon color changes and it should be the same as the text.

**Why are we using a variant prop?**
We are using variant so that we accommodate different styling like fonts. For example, an error link inside the Banner.

**Why are we not using the approach of styling the `<a>` with a evrHyperlinkNeutral class though we already have evrHyperlink?**
If we use classes for styling, we will not be able to get the focus ring.

**Can we use `<ReactRouterDomLink>` as a child of `<Link>`?**
This scenario is not recommended as we will not be able to provide the gap between the `<Icon>` and text.

## Future Considerations

`<BreadcrumbItem>` may need to be revisited if the design changes the text color to be consistent with `<Link>`.

## Other Design Systems

**Material UI** - https://mui.com/material-ui/react-link/

- Link handles the navigation i.e., anchor element is not provided by the user as a child.

**React Spectrum** - https://react-spectrum.adobe.com/react-spectrum/Link.html

- Link is used just for the styling purpose whereas navigation is handled by the child element. Refer to the below Carbon's link for an example of Link with Icon.

**Carbon** - https://react.carbondesignsystem.com/?path=/story/components-link--paired-with-icon

- Same as Spectrum, Link is used just for the styling purpose and navigation is handled by the child element.

## Required PBIs

**3123** - Link Architecture
**3133** - Create Link component
**3134** - Update storybook documentation and write test scripts.
**3135** - Add to Ref App

## Acceptance Criteria

- Component to be named `<Link>`
- Build a component and setup in Storybook (Components, Foundations, Automation)
- Styles are on par with Design specs in Figma
- Verify the following:
  - API works as intended
    - id
    - testId
    - download
    - href
    - hreflang
    - ping
    - referrerpolicy
    - rel
    - target
    - type
    - variant
    - inverse
    - ref
  - Accessibility (focus ring to appear on the `<a>`)
- Unit and integration tests implemented

## Changelog

09/05/2023 - Created a new prop `onClick` which has to be triggred by a click and when `Enter` keyboard button is pressed when `href` is not passed. Please refer to PBI https://ceridian.atlassian.net/browse/EDS-3432 for more details.

12/10/2024: EDS-16210 - Move Link Icon to the end
