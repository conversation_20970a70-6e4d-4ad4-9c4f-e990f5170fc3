.evrLink {
  display: inline-flex;
  vertical-align: top;
  cursor: pointer;
  text-underline-position: under;
  gap: var(--evr-spacing-2xs);
  outline: none;

  &.primary {
    &,
    a:first-child {
      color: var(--evr-interactive-primary-default);
      svg {
        fill: var(--evr-interactive-primary-default);
      }
      &:hover {
        color: var(--evr-interactive-primary-hovered);
        svg {
          fill: var(--evr-interactive-primary-hovered);
        }
      }
      &:active {
        color: var(--evr-interactive-primary-pressed);
        svg {
          fill: var(--evr-interactive-primary-pressed);
        }
      }
      &.inverse {
        color: var(--evr-content-primary-inverse);
        svg {
          fill: var(--evr-content-primary-inverse);
        }
      }
    }
  }

  &.neutral {
    &,
    a:first-child {
      color: var(--evr-interactive-neutral-default);
      svg {
        fill: var(--evr-interactive-neutral-default);
      }
      &:hover {
        color: var(--evr-interactive-neutral-hovered);
        svg {
          fill: var(--evr-interactive-neutral-hovered);
        }
      }
      &:active {
        color: var(--evr-interactive-neutral-active);
        svg {
          fill: var(--evr-interactive-neutral-active);
        }
      }
    }
  }

  &.inherit {
    color: inherit;
    svg {
      fill: currentColor;
    }
  }
}
