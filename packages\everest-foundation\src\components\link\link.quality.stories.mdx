import { Meta, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { <PERSON> } from './link';
import { Icon } from '../icon';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Link"
  component={Link}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'link-id',
    testId: 'link-test-id',
    href: 'https://www.dayforce.com/ca',
    target: '_blank',
  }}
/>

# Link

## Live Demo

<Canvas>
  <Story name="Primary Link">
    {(args) => (
      <p className="evrBodyText">
        <Link {...args}>Dayforce</Link>
      </p>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Primary Link Inverse">
    {(args) => (
      <div style={{ backgroundColor: 'black', height: '2rem', alignItems: 'center', display: 'flex' }}>
        <p className="evrBodyText">
          <Link {...args} inverse>
            Dayforce
          </Link>
        </p>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Neutral Link">
    {(args) => (
      <p className="evrBodyText">
        <Link {...args} variant="neutral">
          Dayforce
        </Link>
      </p>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Primary Link with Icon">
    {(args) => (
      <p className="evrBodyText">
        <Link {...args}>
          Dayforce
          <Icon name="linkNewWindow" size="md" />
        </Link>
      </p>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Primary Link Inverse with Icon">
    {(args) => (
      <div style={{ backgroundColor: 'black', height: '2rem', alignItems: 'center', display: 'flex' }}>
        <p className="evrBodyText">
          <Link {...args} inverse>
            Dayforce
            <Icon name="linkNewWindow" size="md" />
          </Link>
        </p>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Neutral Link with Icon">
    {(args) => (
      <p className="evrBodyText">
        <Link {...args} variant="neutral">
          Dayforce
          <Icon name="linkNewWindow" size="md" />
        </Link>
      </p>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Inherit Link (Color is inherited)">
    {(args) => (
      <p className="evrBodyText" style={{ color: 'var(--evr-surfaces-status-error-highemp)' }}>
        <Link {...args} variant="inherit">
          Dayforce
        </Link>
      </p>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Inherit Link with Icon (Color is inherited)">
    {(args) => (
      <p className="evrBodyText" style={{ color: 'var(--evr-surfaces-status-error-highemp)' }}>
        <Link {...args} variant="inherit">
          Dayforce
          <Icon name="linkNewWindow" size="md" />
        </Link>
      </p>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Text styles are inherited from container(Link with Icon)">
    {(args) => (
      <p className="evrBodyText1">
        Text styles are inherited from container:
        <Link {...args}>
          Dayforce
          <Icon name="linkNewWindow" size="md" />
        </Link>
      </p>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Text styles are inherited from container">
    {(args) => (
      <p className="evrBodyText2">
        Text styles are inherited from container:
        <Link {...args}>Dayforce</Link>
      </p>
    )}
  </Story>
</Canvas>
