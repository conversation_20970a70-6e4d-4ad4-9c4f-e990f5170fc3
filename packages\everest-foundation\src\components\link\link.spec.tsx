import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Link } from './link';

const linkId = 'link-id';
const testId = 'link-test-id';

const getLink = () => screen.getByTestId(`${testId}`);

const onClick = jest.fn((e) => {
  // Otherwise will get console error "Not implemented: navigation (except hash changes)"
  // because jsdom doesn't implement window navigation, which is triggered when a link is clicked
  e.preventDefault();
});

describe('Link', () => {
  describe('should have all the attributes as expected', () => {
    it('should show valid href value', () => {
      render(
        <Link id={linkId} testId={testId} href="https://www.dayforce.com/ca">
          Dayforce
        </Link>
      );

      expect(getLink()).toHaveAttribute('href', 'https://www.dayforce.com/ca');
    });

    it('should have aria-describedby value', () => {
      render(
        <Link id={linkId} testId={testId} href="https://www.dayforce.com/ca" ariaDescribedBy="element1 element2">
          Dayforce
        </Link>
      );

      expect(getLink()).toHaveAttribute('aria-describedby', 'element1 element2');
    });

    it('Link has <a> tag', () => {
      render(
        <Link id={linkId} testId={testId} href="https://www.dayforce.com/ca">
          Dayforce
        </Link>
      );

      expect(getLink().tagName).toEqual('A');
    });

    it('should have focus-ring', async () => {
      render(
        <Link id={linkId} testId={testId} href="https://www.dayforce.com/ca">
          Dayforce
        </Link>
      );

      expect(getLink()).not.toHaveClass('evrFocusRingVisible');
      await userEvent.tab();
      expect(getLink()).toHaveClass('evrFocusRingVisible');
    });

    it('should trigger onClick with click', async () => {
      render(
        <Link id={linkId} testId={testId} onClick={onClick} href="https://www.dayforce.com/ca">
          Dayforce
        </Link>
      );

      await userEvent.click(getLink());
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('should trigger onClick when enter keyboard button is pressed', async () => {
      render(
        <Link id={linkId} testId={testId} onClick={onClick} href="https://www.dayforce.com/ca">
          Dayforce
        </Link>
      );

      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
      expect(onClick).toHaveBeenCalled(); // toHaveBeenCalledTimes(1) is not working here as the onClick is called twice on enter keydown. This doesnt happen on storybook and when tested manually.
    });

    it('should have focus-ring when href is not passed', async () => {
      render(
        <Link id={linkId} testId={testId} onClick={onClick}>
          Dayforce
        </Link>
      );

      expect(getLink()).not.toHaveClass('evrFocusRingVisible');
      await userEvent.tab();
      expect(getLink()).toHaveClass('evrFocusRingVisible');
    });
  });
});
