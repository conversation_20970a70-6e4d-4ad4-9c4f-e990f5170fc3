import { Meta, Story, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { Link } from './link';
import Examples from './link.examples.mdx';
import { Icon } from '../icon';
import { action } from '@storybook/addon-actions';

<Meta
  title="Components/Link"
  component={Link}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: '',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Sets the `id` attribute on the link',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets data-testid',
    },
    download: {
      type: 'string',
      control: '-',
      description: 'The browser will treat the linked URL as a download. Pass the `filename` as the value.',
    },
    href: {
      type: 'string',
      control: 'text',
      description: 'The URL that the link points to.',
    },
    hreflang: {
      type: 'string',
      control: 'text',
      description: 'It specifies the language of the hyperlink page.',
    },
    ping: {
      type: 'string',
      control: 'text',
      description: 'Used to set an URL. When the user clicks on the hyperlink a POST request is sent to the set URL.',
    },
    referrerPolicy: {
      description: 'How much of the referrer to send when following the link.',
      table: {
        defaultValue: { summary: 'strict-origin-when-cross-origin' },
      },
    },
    rel: {
      type: 'string',
      control: 'text',
      description:
        'Specifies the relationship between the current document and the linked document. Only used if the `href` is set.',
    },
    target: {
      description: 'Specifies where to open the linked document.',
      table: {
        defaultValue: { summary: '_self' },
      },
    },
    type: {
      type: 'string',
      control: 'text',
      description: 'Specifies the Internet media type of the linked document. Only used if the `href` is set.',
    },
    variant: {
      description: 'Sets the styling of the text.',
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    inverse: {
      description:
        'When set to true, changes the color of the text on the inverse surface. Affects the `primary` link only.',
      table: {
        defaultValue: { summary: false },
      },
    },
    onClick: {
      control: '-',
      description: 'Callback when the link is triggered by mouse click or Enter keydown event.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: 'text',
      description:
        'Specifies one or more ids of elements with the text that provides additional context for the screen reader users.',
    },
  }}
  args={{
    id: 'link-id',
    href: 'https://www.dayforce.com/ca',
    target: '_blank',
    referrerPolicy: 'strict-origin-when-cross-origin',
    inverse: false,
    variant: 'primary',
    onClick: action('onClick'),
  }}
/>

# Link

<Examples />

## Live Demo

<Canvas>
  <Story name="Link">
    {(args) => {
      const backgroundStyle = {
        backgroundColor: '#333',
        height: 'auto',
        width: 'auto',
        padding: '1rem',
      };
      const inheritedColorStyle = {
        color: 'red',
      };
      const DarkBackgroundContainer = ({ children }) => (
        <>
          <p className="evrBodyText evrBold">The inverse prop only affects the primary link.</p>
          <div style={backgroundStyle}>{children}</div>
        </>
      );
      const LinkComponent = () => ( 
        <p className="evrBodyText" style={(args.variant === 'inherit') ? inheritedColorStyle : null}>
          <Link {...args}>
            Dayforce
            <Icon name="linkNewWindow" size="md" />
          </Link>
        </p>
      );
      return args.inverse ? (
        <DarkBackgroundContainer>
          <LinkComponent />
        </DarkBackgroundContainer>
      ) : (
        <>
          {(args.variant === 'inherit') && <p className="evrBodyText evrBold">Color style is inherited from the parent.</p>}
          <LinkComponent />
        </>
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="Link" />
