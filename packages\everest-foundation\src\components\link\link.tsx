import React, { PropsWithChildren } from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import styles from './link.module.scss';

type TLinkReferrerPolicy =
  | 'no-referrer'
  | 'no-referrer-when-downgrade'
  | 'origin'
  | 'origin-when-cross-origin'
  | 'same-origin'
  | 'strict-origin'
  | 'strict-origin-when-cross-origin'
  | 'unsafe-url';

type TLinkTarget = '_self' | '_blank' | '_parent' | '_top';

type TLinkVariant = 'primary' | 'neutral' | 'inherit';

export interface ILinkProps {
  id: string;
  testId?: string;
  download?: string;
  href?: string;
  hreflang?: string;
  ping?: string;
  referrerPolicy?: TLinkReferrerPolicy;
  rel?: string;
  target?: TLinkTarget;
  type?: string;
  variant?: TLinkVariant;
  inverse?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement> | React.KeyboardEvent<HTMLAnchorElement>) => void;
}

export const Link = React.forwardRef<HTMLAnchorElement, PropsWithChildren<ILinkProps>>(
  (props: PropsWithChildren<ILinkProps>, ref): JSX.Element => {
    const {
      id,
      testId,
      download,
      href,
      hreflang,
      ping,
      referrerPolicy,
      rel,
      target,
      type,
      variant = 'primary',
      inverse,
      children,
      ariaLabel,
      ariaDescribedBy,
      onClick,
    } = props;
    const linkStyles = classnames('evrHyperlink', styles.evrLink, {
      [styles.primary]: variant === 'primary',
      [styles.neutral]: variant === 'neutral',
      [styles.inherit]: variant === 'inherit',
      [styles.inverse]: inverse,
    });

    function handleClick(e: React.MouseEvent<HTMLAnchorElement>) {
      onClick?.(e);
    }

    function handleKeyDown(e: React.KeyboardEvent<HTMLAnchorElement>) {
      if (e.key === 'Enter') onClick?.(e);
    }

    const getLink = () => {
      return (
        <a
          id={id}
          data-testid={testId}
          className={linkStyles}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedBy}
          download={download}
          href={href}
          hrefLang={hreflang}
          ping={ping}
          referrerPolicy={referrerPolicy}
          rel={rel}
          target={target}
          type={type}
          onKeyDown={handleKeyDown}
          ref={ref}
          onClick={handleClick}
          tabIndex={href ? undefined : 0}
        >
          {children}
        </a>
      );
      //When used with third-party routing nested <a> is invalid. So link uses <span> with third-party routing.
      //uncomment the below code when we support third-party routing
      // ) : (
      // <span
      //   id={id}
      //   data-testid={testId}
      //   className={linkStyles}
      //   aria-label={ariaLabel}
      //   onKeyDown={onKeyDown}
      //   tabIndex={0}
      //   ref={ref}
      // ></span>
    };

    return (
      <FocusRing canFocusFromMouse inverse={inverse}>
        {getLink()}
      </FocusRing>
    );
  }
);

Link.displayName = 'Link';
