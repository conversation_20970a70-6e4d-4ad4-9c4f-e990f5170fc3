import React from 'react';
import classnames from 'classnames';

import { Divider } from '../divider';
import { Icon, TIconName } from '../icon';

import styles from './list-box-footer-item.module.scss';

export interface IListBoxFooterItem {
  id: string;
  testId?: string;
  label: string;
  ariaLabel: string;
  iconName?: TIconName;
}

export const ListBoxFooterItem = (props: IListBoxFooterItem): JSX.Element => {
  const { id, testId, label, ariaLabel, iconName } = props;

  return (
    <>
      <div className={styles.evrListBoxFooterDivider}>
        <Divider />
      </div>
      <div className={styles.evrListBoxFooter} id={id} data-testid={testId} aria-label={ariaLabel}>
        {!!iconName && (
          <div className={styles.evrListBoxFooterIconContainer}>
            <Icon
              name={iconName}
              fill={'--evr-content-primary-default'}
              testId={testId ? `${testId}-icon` : undefined}
            />
          </div>
        )}
        <p className={classnames(styles.evrStringListBoxFooter, 'evrBodyText1')}>{label}</p>
      </div>
    </>
  );
};

ListBoxFooterItem.displayName = 'ListBoxFooterItem';
