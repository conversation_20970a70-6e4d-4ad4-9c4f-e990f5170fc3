import { CodeExample } from '../../../.storybook/doc-blocks/example/example.tsx';
import { ListBox } from './list-box.tsx';
import { Icon } from '../icon/index.ts';
import { LinkTo } from '../../../.storybook/docs/shared/link-to.tsx';
import { Button } from '../button/index.ts';

export const scope = { ListBox, Icon, Button };

The `ListBox` component is a list of grouped <LinkTo kind="Toolbox/ListItem">ListItems</LinkTo>.

Multiple components use `ListBox` to populate dropdown options, including:

- <LinkTo kind="Components/ComboBox">ComboBox</LinkTo>
- <LinkTo kind="Components/Dropdown">Dropdown</LinkTo>
- <LinkTo kind="Components/MultiSelect">MultiSelect</LinkTo>
- <LinkTo kind="Components/Text Fields/SearchField">SearchField</LinkTo>

## Variations

### Default ListBox

`ListBox` has one default presentation with variations determined by its props.

Below are the different variations.

export const hasOptionsCode = `() => {
  const styles = {
    row: {
      marginBottom: '2.5rem',
    },
    rowWidth: {
      width: '60%',
    },
    headerText: {
      marginBlockEnd: '0.625rem',
      width: '100%'
    },
    code: {
      // copied from storybook
      lineHeight: '1',
      margin: '0 2px',
      padding: '3px 5px',
      whiteSpace: 'nowrap',
      borderRadius: '3px',
      fontSize: '13px',
      border: '1px solid #EEEEEE',
      color: 'rgba(51,51,51,0.9)',
      backgroundColor: '#F8F8F8',
    }
  };
  const HeaderText = ({ children }) => (
    <div style={styles.headerText}><p className='evrBodyText evrBold'>{children}</p></div>
  );
  const options = [
    { title: 'First Option', id: 'item1' },
    { title: 'Second Option', id: 'item2' },
    { title: 'Third Option', id: 'item3' },
  ];
  const optionsWithIcon = [
    { title: 'First Option', iconName: 'clipboard', id: 'item1' },
    { title: 'Second Option', iconName: 'clipboard', id: 'item2' },
    { title: 'Third Option', iconName: 'clipboard', id: 'item3' },
  ];
  const optionsWithGroups = [
    { title: 'First Option', id: 'item1' },
    { title: 'Group 1', id: 'group1',
      items: [
        { title: 'Second Option', id: 'item2' },
        { title: 'Third Option', id: 'item3' }
      ]
    },
    { title: 'Group 2', id: 'group2',
      items: [
        { title: 'Fourth Option', id: 'item4' },
        { title: 'Fifth Option', id: 'item5' }
      ]
    },
  ];
  return (
    <div>
      <div style={styles.row}>
        <HeaderText>Default <code style={styles.code}>ListBox</code></HeaderText>
        <div style={styles.rowWidth}>
          <ListBox options={options}/>
        </div>
      </div>
      <div style={styles.row}>
        <HeaderText><code style={styles.code}>ListBox</code> with selectedIndex</HeaderText>
        <div style={styles.rowWidth}>
          <ListBox selectedOptions={[options[1]]} options={options}/>
        </div>
      </div>
      <div style={styles.row}>
        <HeaderText><code style={styles.code}>ListBox</code> with softSelectedIndex</HeaderText>
        <div style={styles.rowWidth}>
          <ListBox softSelectedId={'item2'} options={options}/>
        </div>
      </div>
      <div style={styles.row}>
        <HeaderText><code style={styles.code}>ListBox</code> with selectedIndex and softSelectedIndex</HeaderText>
        <div style={styles.rowWidth}>
          <ListBox selectedOptions={[options[1]]} softSelectedId={'item2'} options={options}/>
        </div>
      </div>
      <div style={styles.row}>
        <HeaderText><code style={styles.code}>ListBox</code> - ListItem with Icon</HeaderText>
        <div style={styles.rowWidth}>
          <ListBox options={optionsWithIcon}/>
        </div>
      </div>
      <div style={styles.row}>
        <HeaderText>MultiSelect <code style={styles.code}>ListBox</code></HeaderText>
        <div style={styles.rowWidth}>
          <ListBox selectedOptions={[options[1], options[2]]} softSelectedId={'item3'} options={optionsWithIcon} ariaMultiselectable/>
        </div>
      </div>
      <div style={styles.row}>
        <HeaderText><code style={styles.code}>ListBox</code> with grouping</HeaderText>
        <div style={styles.rowWidth}>
          <ListBox softSelectedId={'item2'} options={optionsWithGroups}/>
        </div>
      </div>
    </div>
  );
};
`;

<CodeExample scope={scope} code={hasOptionsCode} />

### ListBox with itemRenderer

The rendering of the `ListBox` child items can be customized using an `itemRenderer` function which is forwarded to all child `ListItem` and `ListItemGroup` components.

An example of using an `itemRenderer` function is defined as follows:

```js
const itemRenderer = (dataItem, selected) => {
  const containerStyle = {
    display: 'flex',
    overflow: 'hidden',
  };
  const leftSideStyle = {
    display: 'flex',
    alignItems: 'center',
    marginRight: '1rem',
  };
  const rightSideStyle = {
    overflow: 'hidden',
  };
  const textResultStyle = {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  };
  return dataItem.items ? (
    <>
      {/* list-item-group */}
      <div style={containerStyle}>
        <div className="evrBodyText1" title={dataItem.title}>
          {dataItem.title}
        </div>
      </div>
    </>
  ) : (
    <>
      {/* list-item */}
      <div style={containerStyle}>
        <div style={leftSideStyle}>
          <Icon name={dataItem.iconName} />
        </div>
        <div style={rightSideStyle}>
          <div style={textResultStyle} className={selected ? 'evrBodyText1 evrBold' : 'evrBodyText1'}>
            {dataItem.title}
          </div>
          <div style={textResultStyle} className="evrBodyText2" title={dataItem.additionalData?.description}>
            {dataItem.additionalData?.description}
          </div>
        </div>
      </div>
    </>
  );
};
```

The `options` are defined as follows:

```js
const listBoxOptions = [
  {
    title: 'Employees',
    id: 'group-1',
    items: [
      {
        id: 'item-1',
        title: 'Andy Brown',
        iconName: 'person',
        additionalData: {
          description: 'Senior Software Engineer',
        },
      },
      {
        id: 'item-2',
        title: 'Andrea Johnson',
        iconName: 'person',
        additionalData: {
          description: 'Markeing Manager',
        },
      },
      {
        id: 'item-3',
        title: 'Andrew Williams',
        iconName: 'person',
        additionalData: {
          description: 'Sales Representative',
        },
      },
    ],
  },
  {
    title: 'Recruiters',
    id: 'group-2',
    items: [
      {
        id: 'item-4',
        title: 'Anderson McCoy',
        iconName: 'person',
        additionalData: {
          description: 'HR Business Representative',
        },
      },
    ],
  },
];
```

export const itemRendererListBoxCode = `() => {
  const listBoxOptions = [
    {
      title: 'Employees',
      id: 'group-1',
      items: [
        {
          id: 'item-1',
          title: 'Andy Brown',
          iconName: 'person',
          additionalData: {
            description: 'Senior Software Engineer',
          },
        },
        {
          id: 'item-2',
          title: 'Andrea Johnson',
          iconName: 'person',
          additionalData: {
            description: 'Markeing Manager',
          },
        },
        {
          id: 'item-3',
          title: 'Andrew Williams',
          iconName: 'person',
          additionalData: {
            description: 'Sales Representative',
          },
        },
      ],
    },
    {
      title: 'Recruiters',
      id: 'group-2',
      items: [
        {
          id: 'item-4',
          title: 'Anderson McCoy',
          iconName: 'person',
          additionalData: {
            description: 'HR Business Representative',
          },
        },
      ],
    },
  ];
  const itemRenderer = (dataItem, selected) => {
    const containerStyle = {
      display: 'flex',
      overflow: 'hidden',
    };
    const leftSideStyle = {
      display: 'flex',
      alignItems: 'center',
      marginRight: '1rem',
    };
    const rightSideStyle = {
      overflow: 'hidden',
    };
    const textResultStyle = {
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    };
    return dataItem.items ? (
      <>
        { /* list-item-group */}
        <div style={containerStyle}>
          <div className="evrBodyText1" title={dataItem.title}>
            {dataItem.title}
          </div>
        </div>
      </>
    ) : (
      <>
        { /* list-item */}
        <div style={containerStyle}>
          <div style={leftSideStyle}>
            <Icon name={dataItem.iconName} />
          </div>
          <div style={rightSideStyle}>
            <div style={textResultStyle} className={selected ? 'evrBodyText1 evrBold' : 'evrBodyText1'}>
              {dataItem.title}
            </div>
            <div style={textResultStyle} className="evrBodyText2" title={dataItem.additionalData?.description}>
              {dataItem.additionalData?.description}
              </div>
          </div>
        </div>
      </>
    );
  };
  return (
    <ListBox selectedOptions={[listBoxOptions[1]]} softSelectedId={'item3'} itemRenderer={itemRenderer} options={listBoxOptions}/>
  );
};
`;

<CodeExample scope={scope} code={itemRendererListBoxCode} />

## Accessing ListBox using ref

Click on the Button to access the `ListBox`, refer to the console for the element details.

export const refCode = `() => {
  const styles = {
    row: {
      display: 'flex',
      justifyContent: 'space-around',
      flexWrap: 'wrap',
      marginBottom: '2.5rem',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      rowGap: '10px'
    }
  };
  const ref = React.useRef(null);
  const options = [
    { title: 'First Option', id: 'item1' },
    { title: 'Second Option', id: 'item2' },
    { title: 'Third Option', id: 'item3' },
  ];
  return (
    <div styles={styles.column}>
      <div style={styles.row}>
        <Button id='access-element-btn' label='Click to access element' onClick={() => {console.log(ref.current);}}/>
      </div>
      <div style={styles.row}>
        <ListBox ref={ref} selectedOptions={[options[1]]} softSelectedId={'item3'} options={options}/>
      </div>
    </div>
  );
};
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Use `ListBox` to render a collection of related items. Provide the `options` prop with a list of items which will in turn be rendered as `ListItem` items.

Each item in `options` should contain at least a `title` and `id`. For the full list of supported `options` item properties see the `IDataItem` interface
in the <LinkTo kind="Components/ListItem">ListItem</LinkTo> component.

To create a list box with a selected option, provide the `selectedOptions` prop with an array of objects.

To create a list box with a soft-selected option, provide the `softSelectedId` prop with the id of the option to be soft-selected.

To create a list box with nested items, add an array property called `items` to any options entry that is to be represented as a group. Add the sub-items of each group to its `items` array.
The sub-items will be rendered as nested items in a group.
Note: currently we only support **one** level of nesting, but future versions may support multiple levels.

## Accessibility

It creates a group of related documents for assistive technologies.

`ListBox` is rendered as an unordered list `<ul>` and the options for the list box are renderd as list items `<li>`.
Nested groups are rendered in a `<section>` block containing a `<header>` element for the group title, and `<li>` elements for each nested item.

`ListBox` has the ARIA role of `listbox` to identify it as a listbox.

When using `ListBox` to make multiple selections, specify the prop `ariaMultiselectable` to enable the `aria-multiselectable` attribute.

`ListBox` will announce any groupings if the `options` array contains nested items.

The following values should be provided as part of the `textMap` prop:

| Label            | Description                               | <div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div> |
| ---------------- | ----------------------------------------- | --------------------------------------------------------- |
| spinnerAriaLabel | Aria label for announcing loading spinner | "Loading"                                                 |
