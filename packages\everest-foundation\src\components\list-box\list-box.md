# ListBox

## Summary

Research and document implementations for the Everest ListBox.

- Start Date: 2022-02-28
- Figma link: https://www.figma.com/file/YhvqNFsWapyJnaoyAiDNMm/%F0%9F%A7%AADropdown?node-id=553%3A41694 (List Items)

## Detailed Design

ListBox is a controlled component, it doesn't store or update its states.

ListBox is a list wrapper

It is using `<ul>`

## API

1. **testId**: `undefined | string`  
   Sets **data-testid** attribute on the html element.
1. **ariaLabel**: `undefined | string`  
   `aria-label` for _list-box_
1. **options**: `string[] | strictOption[]` \*strictOption is future improvement  
   _list-box_ options - we only support `string[]`
1. **itemRenderer**: `undefined | (dataItem: unknown, selected: boolean) => React.ReactNode` - internal till the strictOption is ready  
   Custom item style depended on `strictOption[]`
1. **selectedIndex**: `undefined | number | number[]` \*number[] is future improvement (`multiSelect`)  
   Index of which item on the list has been selected, valid number range from `0` to `options.length - 1`.
1. **softSelectedIndex**: `undefined | number`  
   Index of _list-Item_ which is highlighted or with it's corresponding index.
1. **onListItemKeyDown**: `undefined | (e: KeyboardEvent, index: number) => void`  
   Callback when Keyboard keydown triggered on the _list-item_
1. **onListItemKeyUp**: `undefined | (e: KeyboardEvent, index: number) => void`  
    Callback when Keyboard keyup triggered on the _list-item_
   ~~1. **onListItemMouseClick**: `undefined | (e: MouseEvent, index: number) => void` ~~
   Callback when mouse click triggered on the _list-item_
1. **onListItemBlur**: `undefined | (e: FocusEvent, index: number) => void`  
   Callback when user blur on a _list-item_
1. **onListItemFocus**: `undefined | (e: FocusEvent, index: number) => void`  
   Callback when user focus on a _list-item_
1. **ref**: `undefined | unknown`  
   Ref of the _list-box_
1. **dir**: `"ltr" | "rtl"`  
   Specifies the direction of the _list-box_
1. **ariaMultiselectable**: `boolean | undefined`  
   Optional. Enables the `aria-multiselectable` attribute.
1. **pinnedItems**: `number[] | undefined`  
   Optional. Indexes of items to appear at the top of the list.

## Accessibility

Please refer to [list-item.md](../list-item/list-item.md)

## Alternatives/Trade-Offs

Please refer to [list-item.md](../list-item/list-item.md)

## Q&A

Please refer to [dropdown.md](../dropdown/dropdown.md)

## Future Considerations

Support **selectedIndex** multiple selection `number[]`
Support `<ol>` or other list wrapper, separator, grouping and heading

## Other Design Systems

Please refer to [list-item.md](../list-item/list-item.md)

## Required PBIs

Please refer to [list-item.md](../list-item/list-item.md)

## ListBoxV3

### Changelog

06/12/2024 - Create ListBoxFooterItem component to solve feature request of a footer within the Combobox. ListBoxFooterItem is reused within ComboboxFooterItem [EDS-4111](https://ceridian.atlassian.net/browse/EDS-4111)

## ListBoxV4 - with Grouping (Jan 2025)

- Start Date: 2025-01-17
- Figma Links: Grouping examples in the [Search design](https://www.figma.com/design/jMzOdXgPrIVZOnwXi06oaF/%F0%9F%A7%AA-Search?node-id=1-4&p=f&t=VB5Ljxwq8sYj2E2Z-0)

### Detailed Design

Create new `list-item-group` component for rendering an item that contains child items. Create under **components/list-item-group/**.

Update existing `list-item` component in **components/list-item/** to introduce a `level` prop which will be used to conditionally render the grouping level CSS classes. Add the new grouping classes to list-item.module.scss.

Update the `list-box` component to conditionally render `list-item-group` and `list-item` items accordingly. While iterating over a grouping item, the child items will subsequently be recursively rendered.

The consuming component (_search-field_, _dropdown_, etc) will need to provide a means of taking the consumer-provided options, which may or may not contain nested items, and flatten the option tree into a one-dimensional array of selectable items suitable for rendering (as `selectableItems[]`); the consuming component will also need to maintain a total count of the selectable items, which will assist with rendering.

See example implementation in the [dropdown grouping POC](https://github.com/DayforceGlobal/platform-components/compare/master...platform/jchiu/eds-4234-poc-dropdown-grouping).

### Pre-requisites/Other Components

Re-use existing components:

- ListItem (will have non-breaking updates)
- ListBoxV3 (V4 will use V3 as starting point)

### API

_list-box_

1. **selectableOptions**: `IDataItem[]`  
   _list-box_ options that are selectable.

_list-item_

1. **level**: number  
   Optional. Tracks the current group level and is used to render grouping-level-specific CSS classes.

_list-item-group_

1. **testId**: string  
   Optional. Sets **data-testid** attribute on the html element.
1. **ariaLabel**: string  
   Optional. The aria-label for the _list-item-group_.
1. **id**: string  
   The id for the _list-item-group_.
1. **data**: `IDataItem`  
   The data for the active _list-item-group_ (id, title, etc)
1. **itemRenderer**: (dataItem: IDataItem, selected: boolean) => React.ReactNode  
   Optional. Custom renderer for the _list-item-group_.
1. **compact**: boolean  
   Optional. When `true` the group header is rendered with classes for reducing padding and font-size.
1. **level**: number  
   Optional. The grouping level of the current group (1-4, defaults to 1).

The existing `IDataItem` type will get new `items` property for the child nodes.

```typescript
export type TDataItemType = 'loading' | 'noResults';

export interface IDataItem {
  title: string;
  value?: string;
  id: string;
  iconName?: TIconName;
  additionalData?: unknown;
  type?: TDataItemType;
  items?: IDataItem[];
}
```

### Usage

```typescript
// consumer of component that consumes ListBoxV4
const options: [
  {
    title: 'Group 1';
    id: 'group1';
    items: [
      { title: 'First Option'; id: 'item1' },
      { title: 'Second Option'; id: 'item2' },
      { title: 'Third Option'; id: 'item3' }
    ];
  },
  {
    title: 'Group 2';
    id: 'group2';
    items: [{ title: 'Fourth Option'; id: 'item4' }, { title: 'Fifth Option'; id: 'item5' }];
  },
  {
    title: 'Group 3';
    id: 'group3';
    items: [
      { title: 'Sixth Option'; id: 'item6' },
      {
        title: 'Group 3 nested 1';
        id: 'group3-nested-1';
        items: [
          { title: 'Seventh Option'; id: 'item8a' },
          {
            title: 'Group 3 nested 1 nested';
            id: 'group3-nested-1-nested';
            items: [{ title: 'Eighth Option'; id: 'item8aa' }, { title: 'Ninth Option'; id: 'item9aa' }];
          },
          { title: 'Tenth Option'; id: 'item9a' }
        ];
      }
    ];
  }
];

// Consumer of ListBoxV4 (e.g SearchFieldV2, Dropdown, Combobox, MultiSelect)
const [selectableOptions, setSelectableOptions] = useState<IDataItem[]>([]);
...
<ListBoxV4
  ...
  selectableOptions={emptyOption ? [emptyOption] : selectableOptions}
  selectedOptions={value ? [value] : []}
  softSelectedId={softSelectedId}
  onSearchListItem={(searchValue) => {
    if (emptyOption) return;
    setSoftSelectedId(searchListItem(searchValue, selectableOptions, softSelectedId, overrideSearchMethod));
  }}
  ...
/>;

```

Please see the example implementation in the [dropdown grouping POC](https://github.com/DayforceGlobal/platform-components/compare/master...platform/jchiu/eds-4234-poc-dropdown-grouping) for more implementation details.

### Accessibility

The group text header is not selectable/focusable. The group text is read by a screen reader when focus arrives on an item inside the group when arriving from outside of the group. Switching from one item to another within a group will omit the group name.

Screen reader examples:

- When focusing on an option inside of the group, coming from outside the group
  > "Group 1" grouping, "Option 1", not selected 1 of 10
- When switching focus between items of the same group
  > "Option 2" not selected, 2 of 2

### Design Q&A

Q. Should the items that are at the _same level_ be rendered in the order they are encountered, or should the groups always come first/last (will require some pre-sorting before rendering).
A. They should be rendered _as is_, that way the consumer has full control over the order.

### Required PBI

1. [Implement grouping in SearchFieldV2 leveraging new ListBoxV4](https://dayforce.atlassian.net/browse/PWEB-18389)
1. [Storybook Documentation](https://dayforce.atlassian.net/browse/PWEB-18400)
1. [Tests (unit, visual, playwright)](https://dayforce.atlassian.net/browse/PWEB-18390)
1. [A11y](https://dayforce.atlassian.net/browse/PWEB-18391)
1. [Add to Ref app](https://dayforce.atlassian.net/browse/PWEB-18392)
1. [Push component to Production](https://dayforce.atlassian.net/browse/PWEB-18394)
1. [Implement Grouping for Dropdown](https://dayforce.atlassian.net/browse/PWEB-18396)
1. [Implement Grouping for Combobox](https://dayforce.atlassian.net/browse/PWEB-18398)
1. [Implement Grouping for MultiSelectV4](https://dayforce.atlassian.net/browse/PWEB-18399)
