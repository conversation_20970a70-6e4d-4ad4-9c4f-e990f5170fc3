@use '../../variables.scss';
@use '@ceridianhcm/theme/dist/scss/' as typography;

.evrListBox {
  margin-block: 0;
  margin-inline: 0;
  padding-inline-start: 0;
  padding-inline-end: 0;
  & .stubItem {
    @include typography.body2Regular;
    color: var(--evr-content-primary-default);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: 1;
    padding: calc(var(--evr-spacing-sm) + var(--evr-spacing-3xs)) 0;
    gap: var(--evr-spacing-sm) 0;
    & p {
      margin: 0;
      padding: 0;
      white-space: normal;
      text-align: center;
    }
  }
  & svg {
    margin-left: unset;
  }
}
