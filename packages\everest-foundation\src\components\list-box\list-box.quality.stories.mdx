import { <PERSON>a, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { ListBox } from './list-box';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/ListBox"
  component={ListBox}
  parameters={{
    controls: {
      exclude: ['dir', 'itemRenderer', 'ref'],
      sort: 'requiredFirst',
    },
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    options: [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
      { title: 'Third Option', id: 'item3' },
    ],
    ariaLabel: 'List Box',
    testId: 'list-box-test-id',
  }}
/>

# ListBox

## Live Demo

export const optionsWithIcon = [
  { title: 'First Option', iconName: 'clipboard', id: 'item1' },
  { title: 'Second Option', iconName: 'clipboard', id: 'item2' },
  { title: 'Third Option', iconName: 'clipboard', id: 'item3' },
];
export const optionsWithGrouping = [
  {
    id: 'group-1',
    title: 'Group 1',
    items: [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
    ],
  },
  {
    id: 'group-2',
    title: 'Group 2',
    items: [
      { title: 'Third Option', id: 'item3' },
      { title: 'Fourth Option', id: 'item4' },
    ],
  },
  {
    id: 'group-3',
    title: 'Group 3',
    items: [
      { title: 'Fifth Option', id: 'item5' },
      { title: 'Sixth Option', id: 'item6' },
    ],
  },
];

<Canvas>
  <Story name="Default">{(args) => <ListBox {...args}></ListBox>}</Story>
</Canvas>

<Canvas>
  <Story name="selectedIndex">{(args) => <ListBox {...args} selectedOptions={[args.options[0]]}></ListBox>}</Story>
</Canvas>

<Canvas>
  <Story name="softSelectedIndex">{(args) => <ListBox {...args} softSelectedId={'item2'}></ListBox>}</Story>
</Canvas>

<Canvas>
  <Story name="selectedIndex softSelectedIndex">
    {(args) => <ListBox {...args} selectedOptions={[args.options[0]]} softSelectedId={'item1'}></ListBox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="With List Item with Icon">{(args) => <ListBox {...args} options={optionsWithIcon}></ListBox>}</Story>
</Canvas>

<Canvas>
  <Story name="With List Item with Icon selectedIndex">
    {(args) => <ListBox {...args} options={optionsWithIcon} selectedOptions={[args.options[0]]}></ListBox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="With List Item with Icon softSelectedIndex">
    {(args) => <ListBox {...args} options={optionsWithIcon} softSelectedId={'item2'}></ListBox>}
  </Story>
</Canvas>

<Canvas>
  <Story name="With List Item with Icon selectedIndex softSelectedIndex">
    {(args) => (
      <ListBox
        {...args}
        options={optionsWithIcon}
        selectedOptions={[args.options[0]]}
        softSelectedId={'item1'}
      ></ListBox>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="With multiple selections">
    {(args) => (
      <ListBox
        {...args}
        selectedOptions={[args.options[0], args.options[1]]}
        softSelectedId={'item1'}
        ariaMultiselectable
      ></ListBox>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="With grouping">{(args) => <ListBox {...args} options={optionsWithGrouping}></ListBox>}</Story>
</Canvas>
