import * as React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { ListBox } from './list-box';
import { TIconName } from '../icon';

describe('[ListBox]', () => {
  const testId = 'list-item-box-id';
  const ariaLabel = 'list item';
  const optionIds = ['option-one', 'option-two', 'option-three'];
  const groupIds = ['alpha', 'beta', 'gamma'];
  const options = [
    { id: optionIds[0], title: 'First Option' },
    { id: optionIds[1], title: 'Second Option' },
    { id: optionIds[2], title: 'Third Option' },
  ];
  const optionsWithIcons = [
    {
      title: 'First Option',
      iconName: 'clipboard' as TIconName,
      id: optionIds[0],
    },
    {
      title: 'Second Option',
      iconName: 'clipboard' as TIconName,
      id: optionIds[1],
    },
    {
      title: 'Third Option',
      iconName: 'clipboard' as TIconName,
      id: optionIds[2],
    },
  ];
  const optionsWithGrouping = [
    {
      title: 'Group 1',
      id: groupIds[0],
      items: [
        { title: 'First Option', id: 'item1' },
        { title: 'Second Option', id: 'item2' },
      ],
    },
    {
      title: 'Group 2',
      id: groupIds[1],
      items: [
        { title: 'Third Option', id: 'item3' },
        { title: 'Fourth Option', id: 'item4' },
      ],
    },
    {
      title: 'Group 3',
      id: groupIds[2],
      items: [
        { title: 'Fifth Option', id: 'item5' },
        { title: 'Sixth Option', id: 'item6' },
      ],
    },
  ];
  const getListBox = () => screen.getByTestId(`${testId}`);
  // Get all li within the list component.
  const getListItems = () => screen.getAllByRole('option');
  const getGroupItems = () => screen.getAllByRole('group');
  const length = options.length;
  const getIconByName = (name: string) => {
    return document.querySelectorAll(`svg[data-evr-name="${name}"]`)[0] || null;
  };

  it('should have role "listbox"', () => {
    render(<ListBox testId={testId} ariaLabel={ariaLabel} options={options} />);
    expect(getListBox().firstElementChild).toHaveAttribute('role', 'listbox');
  });

  it('should have correct number of items based on options length', () => {
    render(<ListBox options={options} testId={testId} ariaLabel={ariaLabel} />);
    expect(getListItems()).toHaveLength(length);
  });

  it('should render groups if groups are defined in options', () => {
    render(<ListBox options={optionsWithGrouping} testId={testId} ariaLabel={ariaLabel} />);
    expect(getListItems()).toHaveLength(6);

    const groupItems = getGroupItems();
    expect(groupItems).toHaveLength(3);
    expect(groupItems[0]).toHaveAttribute('id', `${groupIds[0]}-group`);
    expect(groupItems[1]).toHaveAttribute('id', `${groupIds[1]}-group`);
    expect(groupItems[2]).toHaveAttribute('id', `${groupIds[2]}-group`);
  });

  it('should display checkSmall icon when selectedIndex is set', () => {
    render(<ListBox testId={testId} ariaLabel={ariaLabel} options={options} selectedOptions={[options[1]]} />);
    expect(getIconByName('checkSmall')).toBeInTheDocument();
  });

  it('should display icon', () => {
    render(<ListBox testId={testId} ariaLabel={ariaLabel} options={optionsWithIcons} />);
    expect(getIconByName('clipboard')).toBeInTheDocument();
  });

  it('should render soft selected list item when softSelectedIndex is set', () => {
    const { container } = render(
      <ListBox testId={testId} ariaLabel={ariaLabel} options={options} softSelectedId={optionIds[1]} />
    );
    expect(container.getElementsByClassName('evrListItem').length).toBe(length);
    expect(container.getElementsByClassName('evrListItem softSelected').length).toBe(1);
  });

  it('should render multiple selected items with aria-multiselectable', () => {
    const selectedItems = [options[1], options[2]];
    render(
      <ListBox
        testId={testId}
        ariaLabel={ariaLabel}
        options={options}
        selectedOptions={selectedItems}
        softSelectedId={optionIds[1]}
        ariaMultiselectable
      />
    );
    const listItems = getListItems();
    expect(listItems.length).toEqual(3);
    expect(listItems[0]).toHaveAttribute('id', optionIds[0]);
    expect(listItems[1]).toHaveAttribute('id', optionIds[1]);
    expect(listItems[2]).toHaveAttribute('id', optionIds[2]);
    expect(listItems[1]).toHaveAttribute('aria-selected', 'true');
    expect(listItems[2]).toHaveAttribute('aria-selected', 'true');
    const listBoxFirstChild = getListBox().firstChild;
    expect(listBoxFirstChild).toHaveAttribute('aria-multiselectable');
  });

  it('should softSelect list item with mouse hover', async () => {
    render(<ListBox testId={testId} ariaLabel={ariaLabel} options={options} />);
    const listItems = getListItems();
    await userEvent.hover(getListItems()[0]);
    expect(listItems[0]).toHaveClass('softSelected');
    expect(listItems[1]).not.toHaveClass('softSelected');
    expect(listItems[2]).not.toHaveClass('softSelected');
    await userEvent.hover(getListItems()[1]);
    expect(listItems[0]).not.toHaveClass('softSelected');
    expect(listItems[1]).toHaveClass('softSelected');
    expect(listItems[2]).not.toHaveClass('softSelected');
  });

  it('should softSelect list item with key up and key down button', async () => {
    render(<ListBox testId={testId} ariaLabel={ariaLabel} options={options} />);
    const listItems = getListItems();
    await userEvent.click(getListItems()[0]);
    await userEvent.keyboard('{arrowdown}');
    expect(listItems[0]).not.toHaveClass('softSelected');
    expect(listItems[1]).toHaveClass('softSelected');
    expect(listItems[2]).not.toHaveClass('softSelected');
    await userEvent.keyboard('{arrowup}');
    expect(listItems[0]).toHaveClass('softSelected');
    expect(listItems[1]).not.toHaveClass('softSelected');
    expect(listItems[2]).not.toHaveClass('softSelected');
  });

  it('should not be able to softSelect group items with mouse hover', async () => {
    render(<ListBox testId={testId} ariaLabel={ariaLabel} options={optionsWithGrouping} />);
    const listItems = getListItems();
    const groupItems = getGroupItems();

    await userEvent.hover(listItems[0]);
    expect(listItems[0]).toHaveClass('softSelected');

    await userEvent.hover(groupItems[0]);
    expect(groupItems[0]).not.toHaveClass('softSelected');
    expect(listItems[0]).toHaveClass('softSelected');
  });

  describe('onSelection event', () => {
    const onSelection = jest.fn();
    beforeEach(onSelection.mockReset);
    const confirmListItemIndex = 1;

    it('should dispatch an onSelection event by mouse click when clicking an item', async () => {
      render(<ListBox testId={testId} ariaLabel={ariaLabel} options={options} onSelection={onSelection} />);
      await userEvent.click(getListItems()[confirmListItemIndex]);
      expect(onSelection).toHaveBeenCalledTimes(1);
    });

    it('should not dispatch an onSelection event by mouse click when clicking a grouping header', async () => {
      render(<ListBox testId={testId} ariaLabel={ariaLabel} options={optionsWithGrouping} onSelection={onSelection} />);
      await userEvent.click(getGroupItems()[confirmListItemIndex]);
      expect(onSelection).toHaveBeenCalledTimes(0);
    });
  });
});
