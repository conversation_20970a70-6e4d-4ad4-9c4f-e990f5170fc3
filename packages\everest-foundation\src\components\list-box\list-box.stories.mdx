import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { ListBox } from './list-box';
import { action } from '@storybook/addon-actions';
import Examples from './list-box.examples.mdx';

<Meta
  title="Toolbox/ListBox"
  component={ListBox}
  parameters={{
    controls: {
      exclude: ['dir', 'itemRenderer', 'ref'],
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    onEscape: {
      control: '-',
    },
    onListItemBlur: {
      control: '-',
    },
    onListItemFocus: {
      control: '-',
    },
    onSearchListItem: {
      control: '-',
    },
    onSelection: {
      control: '-',
    },
    onSoftSelection: {
      control: '-',
    },
    onTabKeydown: {
      control: '-',
    },
    onKeyboardNavigation: {
      control: '-',
    },
  }}
  args={{
    options: [
      { title: 'First Option', id: 'item1' },
      { title: 'Second Option', id: 'item2' },
      { title: 'Third Option', id: 'item3' },
    ],
    ariaLabel: 'ListBox',
    testId: 'list-box-test-id',
    selectedOptions: [
      {
        title: 'First Option',
        id: 'item1',
      },
    ],
    softSelectedId: 'item2',
  }}
/>

# ListBox

<Examples />

## Live Demo

<Canvas>
  <Story name="ListBox">{(args) => <ListBox {...args}></ListBox>}</Story>
</Canvas>

<ArgsTable story="ListBox" />
