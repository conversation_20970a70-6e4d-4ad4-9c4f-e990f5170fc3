import React, {
  MutableRefObject,
  ReactNode,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';

import { isPrintableCharacters, useCreateTestId } from '../../utils';
import { IDataItem, ListItem } from '../list-item';
import { ListItemContext } from '../list-item/list-item-context';
import { getSelectableOptions } from '../list-item/list-item-helper';
import { ListItemGroup } from '../list-item-group';
import { Spinner } from '../spinner';

import styles from './list-box.module.scss';

export interface IListBoxMethods {
  /**
   * Focus on softSelectedIndex
   */
  focusOnSoftSelectedItem: () => void;
  /**
   * Make sure softSelectedIndex item is visible
   */
  scrollIntoViewOnSoftSelectedItem: () => void;
  /**
   * Scroll specific item into view
   */
  scrollListItemIntoView: (item?: IDataItem) => void;
}

// When changing this interface, remember to assess all other usages of it, since it's extended by several components such as Combobox, Dropdown and Multiselect.
export interface IListBoxTextMap {
  spinnerAriaLabel?: string;
}
export interface IListBoxProps {
  /**
   * An ID used for automation testing.
   */
  testId?: string;
  /**
   * ID of the ListBox
   */
  id?: string;
  /**
   * A clear description of the ListBox.
   */
  ariaLabel?: string;
  /**
   * An ID of an element with description.
   */
  ariaLabelledBy?: string;
  /**
   * The height of the ListBox
   * @default auto
   */
  height?: string;
  /**
   * The options in the ListBox.
   * @default []
   */
  options: IDataItem[];
  /**
   * The options in the ListBox that are selectable. If not provided, it will be derived from the options.
   * @default []
   */
  selectableOptions?: IDataItem[];
  /**
   * The options in the ListBox that are selected.
   * @default []
   */
  selectedOptions?: IDataItem[];
  /**
   * The ID of the list item that has focus
   */
  softSelectedId?: string;
  /**
   * The directionality of the text.
   */
  dir?: 'ltr' | 'rtl';
  /**
   * If `true`, the ListBox will focus on the soft selected item.
   * @default true
   */
  focusOnSoftSelected?: boolean;
  /**
   * Custom renderer for the items in the ListBox.
   */
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  /**
   * Callback for when a ListItem loses focus.
   */
  onListItemBlur?: (e: React.FocusEvent, dataItem: IDataItem) => void;
  /**
   * Callback for when a ListItem gains focus.
   */
  onListItemFocus?: (e: React.FocusEvent, dataItem: IDataItem) => void;
  /**
   * Callback for when a ListItem is selected. Emits the selected option.
   */
  onSelection?: (dataItem: IDataItem) => void;
  /**
   * Callback for when a soft-selected item changes. Emits the soft-selected option.
   */
  onSoftSelection?: (dataItem: IDataItem) => void;
  /**
   * Callback for when the Escape button is pressed.
   */
  onEscape?: () => void;
  /**
   * Search the ListItem based on the searchValue.
   */
  onSearchListItem?: (searchValue: string) => void;
  /**
   * Callback for when the Tab button is pressed.
   */
  onTabKeydown?: () => void;
  /**
   * Reference to the ListBox.
   */
  listBoxRef?: RefObject<HTMLUListElement>;
  /**
   * Enables the `aria-multiselectable` attribute.
   * @default false
   */
  ariaMultiselectable?: boolean;
  /**
   * When `true` stops aria-activedescendant from changing and the method onSoftSelectedChange from running.
   * @default false
   */
  overrideSetSoftSelected?: boolean;
  /**
   * Callback for when the Down Arrow, Up Arrow, Home and End buttons are pressed on the keyboard.
   */
  onKeyboardNavigation?: () => void;
  /**
   * Object containing localized text for various elements.
   */
  textMap?: IListBoxTextMap;
  /**
   * Footer element
   */
  footer?: ReactNode;
}

export const ListBox = React.forwardRef<IListBoxMethods, IListBoxProps>((props, ref) => {
  const context = useContext(ListItemContext);
  const {
    testId,
    id,
    ariaLabel,
    ariaLabelledBy,
    height = 'auto',
    options = [],
    selectableOptions,
    selectedOptions = [],
    softSelectedId: initialSoftSelectedId,
    // dir,
    focusOnSoftSelected = true,
    itemRenderer,
    onListItemBlur,
    onListItemFocus,
    onSelection,
    onSoftSelection,
    onEscape,
    onSearchListItem,
    onTabKeydown,
    onKeyboardNavigation,
    listBoxRef,
    ariaMultiselectable = false,
    overrideSetSoftSelected = false,
    textMap,
    footer,
  } = props;

  const isTempPresentation = useMemo(
    () =>
      options?.length === 1 && options[0].type && (options[0].type === 'loading' || options[0].type === 'noResults'),
    [options]
  );

  // if selectableOptions were not provided then derive them from options
  const selectableOpts = useMemo(() => {
    return selectableOptions?.length ? selectableOptions : getSelectableOptions(options).result;
  }, [selectableOptions, options]);

  useEffect(() => {
    if (!overrideSetSoftSelected) {
      setSoftSelectedId(initialSoftSelectedId);
      onSoftSelection?.(selectableOpts.filter((el) => el.id === initialSoftSelectedId)[0]);
    }
  }, [overrideSetSoftSelected, initialSoftSelectedId, onSoftSelection, selectableOpts]);

  const dataRef = useCreateTestId(testId);
  const liRefs = useRef<HTMLLIElement[]>([]);
  const [softSelectedId, setSoftSelectedId] = useState(initialSoftSelectedId);
  const currentSelectableListItemIndexRef = useRef(-1);

  // TODO in future could be a pure helper component
  const tempItemRenderer = useCallback(
    ({ title, type }) => (
      <div className={styles.stubItem} role="presentation" data-testid={testId ? `${testId}-stub` : undefined}>
        <p>{title}</p>
        {type && type === 'loading' && (
          <Spinner
            id={`${id}-spinner`}
            size="sm"
            testId={testId ? `${testId}-spinner` : undefined}
            //TODO: aria-label for Spinner cannot be optional. However, since Loading can be optional for Dropdown, Combobox and Multiselect,
            //mechanic needs to be developed to set spinnerAriaLabel to be mandatory only when Loading is set to true.
            //That issue will be solved in this ticket: https://ceridian.atlassian.net/browse/EDS-3885
            ariaLabel={textMap?.spinnerAriaLabel ?? ''}
          />
        )}
      </div>
    ),
    [id, testId, textMap?.spinnerAriaLabel]
  );

  const handleSoftSelectedChange = (index: number, dataItemId?: string, renderIndex?: number, dataItem?: IDataItem) => {
    if (dataItem && !overrideSetSoftSelected) {
      setSoftSelectedId(dataItem.id);
      onSoftSelection?.(dataItem);
    }
  };

  const handleListItemBlur = (e: React.FocusEvent, index: number, renderIndex: number, dataItem: IDataItem) => {
    onListItemBlur?.(e, dataItem);
  };

  const handleListItemFocus = (e: React.FocusEvent, index: number, renderIndex: number, dataItem: IDataItem) => {
    onListItemFocus?.(e, dataItem);
  };

  const renderListItem = (
    dataItem: IDataItem,
    index: number,
    selected: boolean,
    softSelected: boolean,
    level: number
  ) => {
    return (
      <ListItem
        key={dataItem.id}
        id={dataItem.id}
        testId={testId ? `${testId}-list-item-${dataItem.id}` : undefined}
        data={dataItem}
        selected={selected}
        softSelected={softSelected}
        focusOnSoftSelected={focusOnSoftSelected}
        itemRenderer={itemRenderer}
        onBlur={handleListItemBlur}
        onFocus={handleListItemFocus}
        onSoftSelectedChange={handleSoftSelectedChange}
        onSelection={onSelection}
        ref={(el: HTMLLIElement) => {
          liRefs.current[index] = el;
        }}
        level={level}
      />
    );
  };

  useImperativeHandle(ref, () => ({
    focusOnSoftSelectedItem: () => {
      if (softSelectedId) {
        liRefs.current[selectableOpts.findIndex((x) => x.id === softSelectedId)]?.focus();
      }
    },
    scrollIntoViewOnSoftSelectedItem: () => {
      if (softSelectedId) {
        const currentIndex = selectableOpts.findIndex((x) => x.id === softSelectedId);
        /*
         * scrollIntoView is not implemented in jsdom
         * Adding the check here, if scrollIntoView is not null and is not a function
         * Not execute it, this is mainly for UnitTest purpose - one place for all
         * Here's the issue: https://github.com/jsdom/jsdom/issues/1695.
         */
        if (
          liRefs.current[currentIndex] &&
          liRefs.current[currentIndex].scrollIntoView &&
          liRefs.current[currentIndex].scrollIntoView instanceof Function
        ) {
          liRefs.current[currentIndex].scrollIntoView({
            block: 'nearest',
            inline: 'nearest',
          });
        }
      }
    },
    scrollListItemIntoView: (item?: IDataItem) => {
      if (!item) return;

      const listItem = liRefs.current[selectableOpts.findIndex((x) => x.id === item?.id)];

      // scroll the current list item, UNLESS...
      // the current list item is the first selectable option, and
      // the current list item is immediately preceded by a sibling HEADER element (i.e. a group item)
      // ...in that case scroll the HEADER element into view instead
      if (selectableOpts[0]?.id === item?.id && listItem?.previousElementSibling?.tagName === 'HEADER') {
        listItem.previousElementSibling.scrollIntoView({
          block: 'nearest',
          inline: 'nearest',
        });
      } else if (listItem?.scrollIntoView && listItem?.scrollIntoView instanceof Function) {
        listItem.scrollIntoView({
          block: 'nearest',
          inline: 'nearest',
        });
      }
    },
  }));

  const generateListItems = (options: IDataItem[], level: number) => {
    if (level === 1) {
      currentSelectableListItemIndexRef.current = -1;
    }

    if (isTempPresentation) {
      return (
        <ListItem
          key={`${id}-list-item-stub`}
          id={`${id}-list-item-stub`}
          data={options[0]}
          selected={false}
          softSelected={false}
          focusOnSoftSelected={false}
          itemRenderer={tempItemRenderer}
          onBlur={handleListItemBlur}
          onFocus={handleListItemFocus}
          onSoftSelectedChange={handleSoftSelectedChange}
          onSelection={onSelection}
          ref={(el: HTMLLIElement) => {
            liRefs.current[0] = el;
          }}
        />
      );
    }
    const results: JSX.Element[] = [];
    for (const option of options) {
      if (option.items?.length) {
        const childOpts = generateListItems(option.items, level + 1);
        results.push(
          <ListItemGroup
            id={`${option.id}-group`}
            key={`${option.id}-group`}
            data={option}
            level={level}
            itemRenderer={itemRenderer}
          >
            {childOpts}
          </ListItemGroup>
        );
      } else {
        results.push(
          renderListItem(
            option,
            ++currentSelectableListItemIndexRef.current,
            selectedOptions.length ? selectedOptions.some((item) => item.id === option.id) : false,
            option.id === (!overrideSetSoftSelected ? softSelectedId : initialSoftSelectedId),
            level
          )
        );
      }
    }
    return results;
  };

  const handleKeyDown = (e: KeyboardEvent | React.KeyboardEvent) => {
    const optionSize = selectableOpts.length;
    const currentIndex = selectableOpts.findIndex((x) => x.id === softSelectedId);

    context.setIsFocusVisible?.(true);

    switch (e.key) {
      case 'Tab':
        onTabKeydown?.();
        break;
      case 'ArrowDown':
        if (currentIndex < optionSize - 1) {
          setSoftSelectedId(selectableOpts[currentIndex + 1].id);
          onSoftSelection?.(selectableOpts[currentIndex + 1]);
          onKeyboardNavigation?.();
        }
        break;
      case 'ArrowUp':
        if (currentIndex > 0) {
          setSoftSelectedId(selectableOpts[currentIndex - 1].id);
          onSoftSelection?.(selectableOpts[currentIndex - 1]);
          onKeyboardNavigation?.();
        }
        break;
      case 'Home':
        setSoftSelectedId(selectableOpts[0].id);
        onSoftSelection?.(selectableOpts[0]);
        onKeyboardNavigation?.();
        break;
      case 'End':
        setSoftSelectedId(selectableOpts[optionSize - 1].id);
        onSoftSelection?.(selectableOpts[optionSize - 1]);
        onKeyboardNavigation?.();
        break;
    }

    if (e.key === 'Escape' || (e.key === 'ArrowUp' && e.altKey)) {
      onEscape?.();
    } else if (
      isPrintableCharacters(e as React.KeyboardEvent) &&
      e.key !== ' ' // Exclude Space key as it is a printable characters and it also is confirm selection
    ) {
      onSearchListItem?.(e.key);
    }

    if (e.key !== 'Tab') {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    //prevent dropdown to close when clicked on scrollbar
    if (dataRef.current.contains(e.target as HTMLElement) && !listBoxRef?.current?.contains(e.target as HTMLElement)) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  return (
    <>
      <div
        ref={dataRef}
        tabIndex={-1}
        style={{
          height: isTempPresentation ? 'auto' : height,
          overflowY: 'auto',
        }}
        onMouseDown={handleMouseDown}
      >
        <ul
          id={id}
          ref={listBoxRef as MutableRefObject<HTMLUListElement>}
          className={styles.evrListBox}
          role={isTempPresentation ? 'presentation' : 'listbox'}
          aria-label={ariaLabel}
          aria-labelledby={ariaLabelledBy}
          aria-multiselectable={isTempPresentation ? undefined : ariaMultiselectable}
          onKeyDown={!overrideSetSoftSelected ? handleKeyDown : undefined}
        >
          {generateListItems(options, 1)}
        </ul>
      </div>
      {footer}
    </>
  );
});

ListBox.displayName = 'ListBox';
