import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { ListItemGroup } from './list-item-group';
import { Button } from '../button';

export const scope = { ListItemGroup, Button };

A `ListItemGroup` is rendered in place of a `ListItem` whenever the currently-iterated-over item contains an array of child `ListItem` objects.

## Variations

### Default ListItemGroup

A `ListItemGroup` cannot be selected or soft-selected.

export const defaultCode = `() => (
  <ListItemGroup data={{ title: 'Group Level One', id: 'item2' }} level={1}></ListItemGroup>
);
`;

<CodeExample scope={scope} code={defaultCode} />

### ListItemGroup content width is longer than the ListItemGroup

When the content of the group is longer than the group itself, the content will be truncated and a tooltip will be used to display the full content.

export const longerContentCode = `() => {
  const styles = { row: { width: '50%' } };
  return (
    <div style={styles.row}>
      <ListItemGroup
        data={{ 
          title: 'Very long content option, width is bigger than List Group Item width, it should be truncated and using tooltip.',
          id: 'item1'
        }}>
      </ListItemGroup>
    </div>
  );
}`;

<CodeExample scope={scope} code={longerContentCode} />

### ListItemGroup content with custom itemRenderer

The groupings can be custom rendered using an `itemRenderer` function passed in by the consumer.
The `itemRenderer` function will emit specific markup based on the presence or absence of `data.items` collection (see the example below.)

The following is an example of an `itemRenderer` as it applies to a ListItemGroup.

```js
const itemRenderer = (dataItem, selected) => {
  ...
  const styles = {
    containerStyle: { display: 'flex', overflow: 'hidden', flexDirection: 'column' },
    textStyle: { whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }
  };
  return dataItem.items ? (
    <>
      {/* handled by ListItemGroup, renders custom grouping markup */}
      <div style={styles.containerStyle}>
        <h4 style={styles.textStyle} className="evrHeading4">
          {dataItem.additionalData['category']}
        </h4>
        <p style={styles.textStyle} className="evrBodyText1">
          {dataItem.additionalData['description']}
        </p>
      </div>
    </>
  ) : (
    <>
      {/* handled by ListItem, renders custom item markup */}
      <div>...</div>
    </>
  );
};
```

with `data` defined as

```js
const listItemData = {
  id: 'item1',
  title: "Calendar",
  items: [
    ... // child list items defined here
  ],
  additionalData: {
    category: "Work",
    description: "View and Manage work details",
  }
};
```

export const itemRenderContentCode = `() => {
  const itemRenderer = (dataItem, selected) => {
    const styles = {
      containerStyle: {
        display: "flex",
        overflow: "hidden",
        flexDirection: "column",
      },
      textStyle: {
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    };
    return dataItem.items ? (
      <>
        {/* render custom grouping markup */}
        <div style={styles.containerStyle}>
          <h4 style={styles.textStyle} className="evrHeading4">
            {dataItem.additionalData['category']}
          </h4>
          <p style={styles.textStyle} className="evrBodyText1">
            {dataItem.additionalData['description']}
          </p>
        </div>
      </>
    ) : (
      <>
        {/* render custom item markup */}
        <div style={styles.containerStyle}></div>
      </>
    );
  };
  const listItemData = {
    id: 'item1',
    title: 'Calendar',
    items: [
      { id: 'child-item-0', title: 'Child Item 0' },
      { id: 'child-item-1', title: 'Child Item 1' },
    ],
    additionalData: {
      category: 'Work',
      description: 'View and Manage work details',
    },
  };
  return (
    <ListItemGroup
      data={listItemData}
      itemRenderer={itemRenderer}
    ></ListItemGroup>
  );
};
`;

<CodeExample scope={scope} code={itemRenderContentCode} />

## Accessing ListItemGroup using ref

Click on the Button to access the ListItemGroup, refer to the console for the element details.

export const refCode = `() => {
  const ref = React.useRef(null);
  return (
    <>
      <div>
        <Button
          id="access-element-btn"
          label="Click to access element"
          onClick={() => {
            console.log(ref.current);
          }}
        />
      </div>
      <div>
        <ListItemGroup
          data={{ title: 'Gamma Group' }}
          ref={ref}
        ></ListItemGroup>
      </div>
    </>
  );
};
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

If the parent `ListBox` component defines nested `options` (via an `items` array property) then the `ListItemGroup` component is used to group the nested items.

## Accessibility

A unique `id` is required to help assistive technologies.

An accessible name of the `ListItemGroup` is provided by the optional `ariaLabel` with `data.title` as the fallback.

`ListItemGroup` has a role of `group` to identify the element as a listbox option group. The `data` of the element provides the accessible name of the option.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
