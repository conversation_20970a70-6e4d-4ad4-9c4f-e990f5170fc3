import { Meta, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { ListItemGroup } from './list-item-group';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/ListItemGroup"
  component={ListItemGroup}
  parameters={{
    controls: {
      exclude: ['dir', 'itemRenderer', 'index'],
      sort: 'requiredFirst',
    },
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'option-1',
    testId: 'list-item-group-test-id',
    data: {
      id: 'group-1',
      title: 'Group 1',
      items: [
        { id: 'item-1', title: 'Item 1' },
        { id: 'item-2', title: 'Item 2' },
      ],
    },
    ariaLabel: '',
  }}
/>

# ListItemGroup

## Live Demo

<Canvas>
  <Story name="Default">{(args) => <ListItemGroup {...args}></ListItemGroup>}</Story>
</Canvas>

<Canvas>
  <Story name="Longer content width">
    {(args) => (
      <ListItemGroup
        {...args}
        data={{
          id: 'list-item-group-1',
          title:
            'This is a very long content option. It is so long that the content width is bigger than group width, it should be truncated and using tooltip. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.',
        }}
      ></ListItemGroup>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Compact">
    {(args) => (
      <>
        <ListItemGroup {...args} data={{ ...args.data, title: 'Default List Item' }}></ListItemGroup>
        <ListItemGroup {...args} data={{ ...args.data, title: 'Compact List Item' }} compact={true}></ListItemGroup>
      </>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Level">
    {(args) => {
      return (
        <>
          <ListItemGroup
            {...args}
            data={{ ...args.data, title: `Nesting level not specified (defaults to level 1)` }}
            level={level}
          ></ListItemGroup>
          {[1, 2, 3, 4].map((level) => (
            <ListItemGroup
              {...args}
              data={{ ...args.data, title: `Nesting level ${level}` }}
              level={level}
            ></ListItemGroup>
          ))}
        </>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => <ListItemGroup {...args}></ListItemGroup>}
  </Story>
</Canvas>
