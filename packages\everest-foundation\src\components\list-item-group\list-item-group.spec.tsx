import * as React from 'react';
import { render, screen } from '@testing-library/react';

import { ListItemGroup, IListItemGroupProps } from './list-item-group';
import { IDataItem } from '../list-item';

describe('[ListItemGroup]', () => {
  const defaultProps = { id: 'list-group-id', data: { id: 'group-id', title: 'Group 1' } };

  const renderComponent = (props: IListItemGroupProps) => {
    return render(
      <ListItemGroup {...props}>
        <div>Child Content</div>
      </ListItemGroup>
    );
  };

  it('should render with default props', () => {
    const { getByRole, getByText } = renderComponent(defaultProps);
    expect(getByRole('group')).toBeInTheDocument();
    expect(getByText(defaultProps.data.title)).toBeInTheDocument();
    expect(getByText('Child Content')).toBeInTheDocument();
  });

  it('should apply custom aria-label when provided', () => {
    renderComponent({ ...defaultProps, ariaLabel: 'Custom Label' });
    expect(screen.getByRole('group')).toHaveAttribute('aria-label', 'Custom Label');
  });

  it('should apply custom testId when provided', () => {
    renderComponent({ ...defaultProps, testId: 'test-id' });
    expect(screen.getByRole('group')).toHaveAttribute('data-testid', 'test-id');
  });

  it('should render using custom itemRenderer', () => {
    const customItemRenderer = (dataItem: IDataItem) => (
      <div data-testid="custom-item">
        {dataItem.title}
        {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (dataItem.additionalData as any)['description']
        }
      </div>
    );

    renderComponent({
      ...defaultProps,
      data: { ...defaultProps.data, additionalData: { description: 'custom renderer description' } },
      itemRenderer: customItemRenderer,
    });

    expect(screen.getByTestId('custom-item')).toBeInTheDocument();
    expect(screen.getByTestId('custom-item')).toHaveTextContent('custom renderer description');
    expect(screen.getByRole('group')).toHaveTextContent('Group 1');
  });
});
