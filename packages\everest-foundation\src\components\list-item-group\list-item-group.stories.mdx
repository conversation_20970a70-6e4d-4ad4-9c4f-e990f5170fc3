import { Meta, Story, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { ListItemGroup } from './list-item-group';
import { action } from '@storybook/addon-actions';
import Examples from './list-item-group.examples.mdx';

<Meta
  title="Toolbox/ListItemGroup"
  component={ListItemGroup}
  parameters={{
    controls: {
      exclude: ['dir'],
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    itemRenderer: {
      control: '-',
    },
  }}
  args={{
    id: 'group-1',
    testId: 'list-item-groupt-test-id',
    data: {
      id: 'group-items-1',
      title: 'Group 1',
      items: [
        { id: 'item-1', title: 'Item 1' },
        { id: 'item-2', title: 'Item 2' },
      ],
    },
    ariaLabel: '',
  }}
/>

# ListItemGroup

<Examples />

## Live Demo

<Canvas>
  <Story name="ListItemGroup">{(args) => <ListItemGroup {...args}></ListItemGroup>}</Story>
</Canvas>

<ArgsTable story="ListItemGroup" />
