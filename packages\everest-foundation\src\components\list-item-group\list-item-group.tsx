import React, { useContext } from 'react';
import classnames from 'classnames';

import { resolvePropsContext } from '../../utils/resolve-props-context';
import { IDataItem } from '../list-item/list-item';
import { IListItemContext, ListItemContext } from '../list-item/list-item-context';

import itemStyles from '../list-item/list-item.module.scss';
import groupStyles from '../list-item-group/list-item-group.module.scss';

const styles = { ...itemStyles, ...groupStyles };

export interface IListItemGroupProps {
  /** Sets **data-testid** attribute on the html element; used for automation testing. */
  testId?: string;
  /** A clear description of the ListItemGroup. */
  ariaLabel?: string;
  /** A unique id required for accessibility purposes. */
  id: string;
  /** The data for the active ListItemGroup (id, title, items, etc). */
  data: IDataItem;
  /** Custom renderer for the ListItemGroup. The function receives a dataItem and selected state and should return a ReactNode. */
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  /** When `true` the ListItemGroup is rendered with classes for reducing padding and font-size.
   * @default false
   */
  compact?: boolean;
  /** The grouping level of the current group; presently the only supported value is `1`.
   * @default 1
   */
  level?: number;
}

export const ListItemGroup = React.forwardRef<HTMLElement, React.PropsWithChildren<IListItemGroupProps>>(
  (props, ref) => {
    const context = useContext(ListItemContext);
    const {
      children,
      testId,
      ariaLabel,
      id,
      data,
      itemRenderer,
      compact,
      level = 1,
    } = resolvePropsContext<React.PropsWithChildren<IListItemGroupProps>, IListItemContext>(props, context);

    return (
      <section
        ref={ref}
        id={id}
        data-testid={testId ? testId : undefined}
        role="group"
        aria-label={ariaLabel ?? data.title}
      >
        <header
          role="presentation"
          className={classnames(styles.evrListItem, styles.evrListItemGroup, {
            [styles.compact]: compact,
            [`evrBodyText${compact ? '2' : '1'}`]: true,
            [styles[`nested${level}`]]: level > 1,
          })}
          title={data.title}
        >
          {itemRenderer ? (
            <>{itemRenderer(data, false)}</>
          ) : (
            <div className={styles.evrListItemContainer}>
              <div className={classnames('evrBold', styles.evrStringListItem)}>{data.title}</div>
            </div>
          )}
        </header>
        {children}
      </section>
    );
  }
);

ListItemGroup.displayName = 'ListItemGroup';
