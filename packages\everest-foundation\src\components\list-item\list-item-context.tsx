import React, { SetStateAction, Dispatch } from 'react';

import { IDataItem, TSelectionType } from './list-item';

export interface IListItemContext {
  setAriaActiveDescendantId?: Dispatch<SetStateAction<string>>;
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  onMouseDown?: (
    e: React.MouseEvent,
    index: number,
    renderIndex: number,
    // TODO review if this should be optional, along with index,
    // and render index both in here and below functions
    data?: IDataItem
  ) => void;
  onMouseMove?: (e: React.MouseEvent, renderIndex: number) => void;
  onBlur?: (e: React.FocusEvent, index: number, renderIndex: number) => void;
  onFocus?: (e: React.FocusEvent, index: number, renderIndex: number) => void;
  onKeyDown?: (e: React.KeyboardEvent, index: number, renderIndex: number) => void;
  onKeyUp?: (e: React.KeyboardEvent, index: number, renderIndex: number) => void;
  onSoftSelectedChange?: (index: number, id?: string, renderIndex?: number) => void;
  isFocusVisible?: boolean;
  setIsFocusVisible?: (isFocusVisible: boolean) => void;
  stopMouseOverSoftSelection?: boolean;
  compact?: boolean;
  selectionType?: TSelectionType;
}

const defaultContext = {
  setAriaActiveDescendantId: undefined,
  itemRenderer: undefined,
  onMouseDown: () => undefined,
  onMouseMove: () => undefined,
  onBlur: () => undefined,
  onFocus: () => undefined,
  onKeyDown: () => undefined,
  onKeyUp: () => undefined,
  onSoftSelectedChange: () => undefined,
  isFocusVisible: false,
  stopMouseOverSoftSelection: false,
  compact: false,
  selectionType: 'checkmark' as TSelectionType,
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const ListItemContext = React.createContext<IListItemContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  ListItemContext.displayName = 'ListItemContext';
}
