import { IDataItem } from './list-item';
import { getSelectableOptions } from './list-item-helper';

describe('[list-item-helper]', () => {
  describe('getSelectableOptions', () => {
    it('should return an empty result for an empty array', () => {
      const result = getSelectableOptions([]);
      expect(result).toEqual({ result: [], totalOptionsCount: 0 });
    });

    it('should return the same array for a flat list of options', () => {
      const options: IDataItem[] = [
        { id: '1', title: 'Option 1' },
        { id: '2', title: 'Option 2' },
      ];
      const result = getSelectableOptions(options);
      expect(result).toEqual({ result: options, totalOptionsCount: 2 });
    });

    it('should flatten nested options and count all options', () => {
      const options: IDataItem[] = [
        {
          id: '1',
          title: 'Group 1',
          items: [
            { id: '1.1', title: 'Option 1.1' },
            { id: '1.2', title: 'Option 1.2' },
          ],
        },
        { id: '2', title: 'Option 2' },
      ];
      const result = getSelectableOptions(options);
      expect(result).toEqual({
        result: [
          { id: '1.1', title: 'Option 1.1' },
          { id: '1.2', title: 'Option 1.2' },
          { id: '2', title: 'Option 2' },
        ],
        totalOptionsCount: 4,
      });
    });

    it('should handle deeply nested options', () => {
      const options: IDataItem[] = [
        {
          id: '1',
          title: 'Group 1',
          items: [
            {
              id: '1.1',
              title: 'Group 1.1',
              items: [{ id: '1.1.1', title: 'Option 1.1.1' }],
            },
          ],
        },
      ];
      const result = getSelectableOptions(options);
      expect(result).toEqual({
        result: [{ id: '1.1.1', title: 'Option 1.1.1' }],
        totalOptionsCount: 3,
      });
    });
  });
});
