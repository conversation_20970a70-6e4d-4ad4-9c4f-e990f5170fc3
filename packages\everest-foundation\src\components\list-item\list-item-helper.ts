import { IDataItem } from './list-item';

/**
 * Recursively flattens nested options into a 1D array of just the selectable options
 * while counting the total number of options (including groups)
 * @param options Array of options
 * @returns An object containing the flattened array of selectable options and the total number of options
 */
export function getSelectableOptions(options: IDataItem[] = []): { result: IDataItem[]; totalOptionsCount: number } {
  const result = [];
  let totalOptionsCount = 0;
  for (const option of options) {
    totalOptionsCount++;
    if (option.items) {
      const resp = getSelectableOptions(option.items);
      result.push(...resp.result);
      totalOptionsCount += resp.totalOptionsCount;
    } else {
      result.push(option);
    }
  }
  return { result, totalOptionsCount };
}
