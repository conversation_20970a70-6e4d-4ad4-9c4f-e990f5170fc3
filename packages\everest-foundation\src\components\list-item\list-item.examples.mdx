import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { ListItem } from './list-item';
import { Icon } from './../icon';
import { Button } from '../button';

export const scope = { ListItem, Icon, Button };

List item is used as a dropdown option to populate the dropdown.

It refers to the individual dropdown item.

## Variations

### Default List Item

A `ListItem` can be `selected` and/or `softSelected`.

export const defaultCode = `() => {
  const styles = {
    row: {
      width: '50%',
    },
    column: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      rowGap: '10px'
    }
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  return (
    <Column>
      <Row>
        <ListItem data={{ title: 'First Option', id: 'item1' }}></ListItem>
      </Row>
      <Row>
        <ListItem
          data={{ title: 'First Option', id: 'item2' }}
          selected
        ></ListItem>
      </Row>
      <Row>
        <ListItem
          data={{ title: 'First Option', id: 'item3' }}
          softSelected
          isFocusVisible
        ></ListItem>
      </Row>
      <Row>
        <ListItem
          data={{ title: "First Option", id: "item4" }}
          selected
          softSelected
          isFocusVisible
        ></ListItem>
      </Row>
    </Column>
  );
};
`;

<CodeExample scope={scope} code={defaultCode} />

### List Item with Icon

`ListItem` with Icon by setting `data` object's `iconName`

export const listItemIconCode = `() => {
  const styles = {
    row: {
      width: '50%',
    },
    column: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      rowGap: '10px'
    }
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  const listItemData = { title: 'First Option', iconName: 'clipboard', id: 'item1' };
  return (
    <Column>
      <Row>
        <ListItem data={listItemData}></ListItem>
      </Row>
      <Row>
        <ListItem data={listItemData} selected></ListItem>
      </Row>
      <Row>
        <ListItem data={listItemData} softSelected isFocusVisible></ListItem>
      </Row>
      <Row>
        <ListItem data={listItemData} selected softSelected isFocusVisible></ListItem>
      </Row>
    </Column>
  );
};
`;

<CodeExample scope={scope} code={listItemIconCode} />

### List Item content width is longer than the List Item

List item content will be truncated with ellipsis and using tooltip to show hidden info

export const longerContentCode = `() => {
  const styles = {
    row: {
      width: '50%',
    },
    column: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      rowGap: '10px'
    }
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  const listItemData = {
    title: 'Very long content option, width is bigger than List Item width, it should be truncated and using tooltip',
    id: 'item1'
  };
  return (
    <Column>
      <Row>
        <ListItem data={listItemData}></ListItem>
      </Row>
      <Row>
        <ListItem data={listItemData} selected></ListItem>
      </Row>
      <Row>
        <ListItem data={listItemData} softSelected isFocusVisible></ListItem>
      </Row>
      <Row>
        <ListItem data={listItemData} selected softSelected isFocusVisible></ListItem>
      </Row>
    </Column>
  );
};
`;

<CodeExample scope={scope} code={longerContentCode} />

### List Item content with itemRenderer

Constructing the List Item with custom `itemRenderer`, The following example defined the `itemRenderer` as

```js
const itemRenderer = (dataItem, selected) => {
  const containerStyle = { display: 'flex', overflow: 'hidden' };
  const leftSideStyle = { display: 'flex', alignItems: 'center', marginRight: '1rem' };
  const rightSideStyle = { overflow: 'hidden', marginRight: '0.5rem' };
  const textResultStyle = { whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' };
  return (
    <div style={containerStyle}>
      <div style={leftSideStyle}>
        <Icon name={dataItem.iconName} />
      </div>
      <div style={rightSideStyle}>
        <h4 style={textResultStyle} className="evrHeading4">
          {dataItem.additionalData['category']}
        </h4>
        <p style={textResultStyle} className={selected ? 'evrBodyText1 evrBold' : 'evrBodyText1'}>
          {dataItem.title}
        </p>
        <p style={textResultStyle} className="evrBodyText1">
          {dataItem.description}
        </p>
      </div>
    </div>
  );
};
```

with `data` defined as

```js
const listItemData = {
  iconName: 'calendar',
  title: 'Calendar',
  id: 'item1',
  additionalData: {
    category: 'Work',
    description:
      'View and Manage work details such as scheduled shifts, worked shifts, shift trades, availability, attendance records, and time away from one unified view.',
  },
};
```

For selected item, it updates the title style to bold.

export const itemRenderContentCode = `() => {
  const styles = {
    row: { width: '50%' },
    column: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      rowGap: '10px',
    },
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  const itemRenderer = (dataItem, selected) => {
    const containerStyle = { display: 'flex', overflow: 'hidden' };
    const leftSideStyle = { display: 'flex', alignItems: 'center', marginRight: '0.5rem' };
    const rightSideStyle = { overflow: 'hidden', marginRight: '1rem' };
    const textResultStyle = { whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' };
    return (
      <div style={containerStyle}>
        <div style={leftSideStyle}>
          <Icon name={dataItem.iconName} />
        </div>
        <div style={rightSideStyle}>
          <h4 style={textResultStyle} className="evrHeading4">
            {dataItem.additionalData['category']}
          </h4>
          <p style={textResultStyle} className={selected ? 'evrBodyText1 evrBold' : 'evrBodyText1'}>
            {dataItem.title}
          </p>
          <p style={textResultStyle} className='evrBodyText1'>
            {dataItem.description}
          </p>
        </div>
      </div>
    );
  };
  const listItemData = {
    iconName: "calendar",
    id: 'item1',
    title: 'Calendar',
    additionalData: {
      category: 'Work',
      description:
        'View and Manage work details such as scheduled shifts, worked shifts, shift trades, availability, attendance records, and time away from one unified view.',
    }
  };
  return (
    <Column>
      <Row>
        <ListItem data={listItemData} itemRenderer={itemRenderer}></ListItem>
      </Row>
      <Row>
        <ListItem
          data={listItemData}
          itemRenderer={itemRenderer}
          selected
        ></ListItem>
      </Row>
      <Row>
        <ListItem
          data={listItemData}
          itemRenderer={itemRenderer}
          softSelected
          isFocusVisible
        ></ListItem>
      </Row>
      <Row>
        <ListItem
          data={listItemData}
          itemRenderer={itemRenderer}
          selected
          softSelected
          isFocusVisible
        ></ListItem>
      </Row>
    </Column>
  );
};
`;

<CodeExample scope={scope} code={itemRenderContentCode} />

## Accessing List Item using ref

Click on the Button to access the List Item, refer to the console for the element details.

export const refCode = `() => {
  const styles = {
    row: {
      display: 'flex',
      justifyContent: 'space-around',
      flexWrap: 'wrap',
      width: '50%',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      rowGap: '10px'
    }
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  const ref = React.useRef(null);
  return (
    <Column>
      <Row>
        <Button
          id="access-element-btn"
          label="Click to access element"
          onClick={() => {
            console.log(ref.current);
          }}
        />
      </Row>
      <Row>
        <ListItem ref={ref} data={{ title: "First Option" }} selected></ListItem>
      </Row>
    </Column>
  );
};
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Use list items as an item within a list.

Dropdown component is using list box to populate as dropdown options to populate the dropdown

## Accessibility

A unique `id` is required to help assistive technologies.

Use required `data` or optional `ariaLabel` to satisfy accessibility requirements.

When selected `aria-selected` will be set to **true**

List item has a role `option` to identify the element as a listbox option. The `data` of the element provides the accessible name of the option.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
