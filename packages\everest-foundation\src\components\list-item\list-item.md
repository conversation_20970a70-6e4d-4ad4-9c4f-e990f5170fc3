# List Item

## Summary

Research and document implementations for the Everest List Item.

- Start Date: 2022-02-28
- Figma link: https://www.figma.com/file/YhvqNFsWapyJnaoyAiDNMm/%F0%9F%A7%AADropdown?node-id=553%3A41694 (List Items)

## Detailed Design

List Item is a controlled component, it doesn't store or update its states.

We only support text with checkmark only for the List Item, not consider other possible templates

It is using `<li>`

This should handle all the onFocus, onBlur style of a list-item.

## API

1. **testId**: `undefined | string`  
   Sets **data-test-id** attribute on the html element.
1. **ariaLabel**: `undefined | string`  
   `aria-label` for _list-item_, if set, this will be used, otherwise use value
1. **id**: `undefined | string`  
   Required or auto generated
1. **value**: `string | strictOption` \*strictOption is future improvement
   Define value of _list-item_
1. **selected**: `boolean`  
   Sets the `aria-selected` attribute on the _list-item_, update _list-item_ style with checked **icon**.
1. **softSelected**: `boolean`  
   Update **softSelected** style
1. **itemRenderer**: `undefined | (dataItem: unknown, selected: boolean) => React.ReactNode` - internal till the strictOption is ready  
   Custom item style depended on **strictOption[]**
1. **onMouseClick**: `undefined | (e: MouseEvent) => void`
   Callback when mouse click triggered
1. **onKeyDown**: `undefined | (e: KeyboardEvent) => void`
   Callback when keyboard keydown triggered
1. **onKeyUp**: `undefined | (e: KeyboardEvent) => void`
   Callback when keyboard keyup triggered
1. **onBlur**: `undefined | (e: FocusEvent) => void`
   Optional callback on blur.
1. **onFocus**: `undefined | (e: FocusEvent) => void`  
   Optional callback on focus.
1. **dir**: `"ltr" | "rtl"`  
   Specifies the direction of the _list-item_
1. **index**: `undefined | number`  
   Specifies the index of the _list-item_

## Accessibility

[Select-Only Combobox Example](https://www.w3.org/TR/wai-aria-practices-1.2/examples/combobox/combobox-select-only.html)

**Role**

- `combobox`: Identifies the input as a combobox.
- `listbox`: Identifies the element as a listbox.
- `option`: Identifies the element as a listbox option. The text content of the element provides the accessible name of the option.

**Attribute**

- `aria-labelledby`: Identifies the element that labels the combobox.
- `aria-controls`: Identifies the element that serves as the popup.
- `aria-expanded`: Indicates that the popup element is displayed or not.
- `aria-activedescendant`
  - When an option in the listbox is visually indicated as having keyboard focus, refers to that option.
  - When navigation keys, such as _Down Arrow_, are pressed, the JavaScript changes the value.
  - Enables assistive technologies to know which element the application regards as focused while DOM focus remains on the `input` element.
- `aria-selected`
  - Specified on an option in the listbox when it is visually highlighted as selected.
  - Occurs only when an option in the list is referenced by `aria-activedescendant`.
- `aria-haspopup`: attribute indicates the availability and type of interactive popup element that can be triggered by the element on which the attribute is set.

Example:

```
<label id='dropdownLabel1' for="comboBox1">Dropdown Label</label>
<div id="combobox-1" role='combobox' aria-labelledby="dropdownLabel1" aria-controls="listbox1" aria-expanded="false" aria-haspopup="listbox" aria-activedescendant="listbox1">
   <ul role="listbox" aria-label="States" id="list-box-1">
      <li role="option"></li>
      <li role="option" aria-selected="true"></li>
      <li role="option"></li>
   </ul>
</div>
```

## Alternatives/Trade-Offs

Please refer to [dropdown.md](../dropdown/dropdown.md)

## Q&A

Please refer to [dropdown.md](../dropdown/dropdown.md)

- States? Disabled required for dropdown?
  - **Answer** : Currently out of scope for disabled dropdown item
- Confirm the padding, 13, 8, 13, 16 --> We don't have spacing token for 13px.. Only 12 which is var(--evr-spacing-xs)
  - **Answer**
    - Icon using size medium 24x24 bounding box with padding top and bottom 12px var(--evr-spacing-xs)
    - Text would have to apply proper padding to total height 48px of the list Item
- Hover and softSelected are the same background?
  - **Answer** : Yes, same color
- Is callout for Text be the callout component or callout preset (mixin) - using callout component on the Figma would be confusing if we only use the callout mixin as callout component only has blue color text, overwriting the color for a component isn't a best practice
  - **Answer** : Using callout mixin for now - Design still working on a standard implementation (TBD)
    - Discussion on possible solution
      - update callout component to have different variants
      - using the body text component instead
      - Design take it back for more discussion
      - temporary solution by using callout mixin and list item color

## Future Considerations

Support **value** `strictOption` and **itemRenderer**

Support Everest version of IDropdownOption / Templates

Support `disabled`, grouping and heading _list-item_

## Other Design Systems

### Material - https://mui.com/components/selects/

- using children prop (node - MenuItem)
- props - https://mui.com/api/select/
- using MenuItem as the children and generate using `<ul>` and `<li>`

### Microsoft Fluent UI - https://developer.microsoft.com/en-us/fluentui#/controls/web/dropdown#IDropdownOption

- options prop (IDropdownOption)
- [IDropdownOption](https://github.com/microsoft/fluentui/blob/master/packages/react/src/components/Dropdown/Dropdown.types.ts)
- [ISelectableOption](https://github.com/microsoft/fluentui/blob/master/packages/react/src/utilities/selectableOption/SelectableOption.types.ts)
- Fluent UI using ISelectableOption instead of itemRenderer
- It used ISelectableOption to handle if the list-item a header, disabled or just plain text
- Instead of using `<ul>` and `<li>`, Fluent UI using `<div>` for grouping and `<button>` for actual item list
- For a11y, it is using `aria-selected`, `aria-posinset` and `aria-setsize`

### Shopify Polaris - https://polaris.shopify.com/components/lists-and-tables/option-list#navigation

- using options prop (String | StrictOption | SelectGroup)[]
- using <`select`> and <`option`>
- not consistent with their comboBox, which is using <`ul`> and <`li`>

## Required PBIs

1. [Create List-Item and List-box component](https://ceridianpt.atlassian.net/browse/EVR-889) (functionality, styled and a11y)
