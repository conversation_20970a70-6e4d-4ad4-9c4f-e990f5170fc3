@use '../../variables.scss';
@use '../../index.scss' as helper;

.evrListItem {
  list-style: none;
  padding-block: var(--evr-spacing-xs);
  padding-inline-start: var(--evr-spacing-sm);
  padding-inline-end: var(--evr-spacing-2xs);
  display: flex;
  align-items: center;
  outline: none;
  cursor: pointer;

  &.presentation {
    padding-inline-start: var(--evr-spacing-xs);
  }

  &.softSelected {
    &.focusVisible {
      border: var(--evr-border-width-thick-px) solid var(--evr-interactive-primary-focus);
      padding-block: calc(var(--evr-spacing-xs) - var(--evr-border-width-thick-rem));
      padding-inline-start: calc(var(--evr-spacing-sm) - var(--evr-border-width-thick-rem));
      padding-inline-end: calc(var(--evr-spacing-2xs) - var(--evr-border-width-thick-rem));
    }

    &.presentation {
      &.focusVisible {
        padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thick-rem));
      }
    }

    &:hover {
      background: var(--evr-surfaces-primary-hovered);
    }
  }

  &.compact {
    padding-block: var(--evr-spacing-3xs);
    padding-inline-start: var(--evr-spacing-sm);
    padding-inline-end: var(--evr-spacing-2xs);
    height: var(--evr-size-md);

    &.softSelected.focusVisible {
      padding-block: calc(var(--evr-spacing-3xs) - var(--evr-border-width-thick-rem));
      padding-inline-start: calc(var(--evr-spacing-sm) - var(--evr-border-width-thick-rem));
      padding-inline-end: calc(var(--evr-spacing-2xs) - var(--evr-border-width-thick-rem));
    }
  }

  @for $i from 2 through 5 {
    &.nested#{$i} {
      $nested-padding: calc(
        variables.$listItemNestedBasePadding + ($i - 2) * variables.$listItemNestedPaddingIncrement
      );
      padding-inline-start: $nested-padding;

      &.softSelected.focusVisible {
        padding-inline-start: calc($nested-padding - var(--evr-border-width-thick-rem));
      }
    }
  }

  .evrListItemContainer {
    overflow: hidden;
    display: flex;
    width: 100%;
  }

  .evrIconContainer {
    display: flex;

    svg {
      margin-inline-start: auto;
      padding-block: 0;
      padding-inline-start: var(--evr-spacing-4xs);
      padding-inline-end: var(--evr-spacing-2xs);
    }
  }

  .evrStringListItem {
    color: var(--evr-content-primary-default);
    user-select: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-inline-end: var(--evr-size-xs);

    &.presentation {
      margin-inline-start: var(--evr-spacing-3xs);
    }

    &.selected:not(.presentation) {
      color: var(--evr-interactive-primary-default);
      font-weight: var(--evr-bold-weight);
      padding-inline-end: 0;
    }

    &.softSelected:not(.presentation) {
      color: var(--evr-interactive-primary-hovered);
    }
  }

  &.hasIcon {
    .evrStringListItem {
      padding-block-start: var(--evr-spacing-4xs);
    }
  }

  svg {
    margin-left: auto;
    padding: 0 var(--evr-spacing-2xs);
    box-sizing: content-box; // border-box will shrink check icon
  }
}
