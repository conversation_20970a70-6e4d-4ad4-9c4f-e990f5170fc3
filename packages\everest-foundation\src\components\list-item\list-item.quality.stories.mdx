import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { ListItem } from './list-item';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/List Item"
  component={ListItem}
  parameters={{
    controls: {
      exclude: ['dir', 'itemRenderer', 'index'],
      sort: 'requiredFirst',
    },
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'option-1',
    testId: 'list-item-test-id',
    selected: false,
    softSelected: false,
    data: { title: 'First Option' },
    ariaLabel: '',
    isFocusVisible: true,
  }}
/>

# List Item

## Live Demo

export const dataWithIcon = { title: 'First Option', iconName: 'clipboard' };

<Canvas>
  <Story name="Default">{(args) => <ListItem {...args}></ListItem>}</Story>
</Canvas>

<Canvas>
  <Story name="Selected">{(args) => <ListItem {...args} selected={true}></ListItem>}</Story>
</Canvas>

<Canvas>
  <Story name="SoftSelected with IsFocusVisible off">
    {(args) => <ListItem {...args} softSelected isFocusVisible={false}></ListItem>}
  </Story>
</Canvas>

<Canvas>
  <Story name="SoftSelected">{(args) => <ListItem {...args} softSelected={true}></ListItem>}</Story>
</Canvas>

<Canvas>
  <Story name="Selected SoftSelected">
    {(args) => <ListItem {...args} selected={true} softSelected={true}></ListItem>}
  </Story>
</Canvas>

<Canvas>
  <Story name="List Item with Icon">{(args) => <ListItem {...args} data={dataWithIcon}></ListItem>}</Story>
</Canvas>

<Canvas>
  <Story name="List Item with Icon Selected">
    {(args) => <ListItem {...args} data={dataWithIcon} selected={true}></ListItem>}
  </Story>
</Canvas>

<Canvas>
  <Story name="List Item with Icon SoftSelected">
    {(args) => <ListItem {...args} data={dataWithIcon} softSelected={true}></ListItem>}
  </Story>
</Canvas>

<Canvas>
  <Story name="List Item with Icon Selected SoftSelected">
    {(args) => <ListItem {...args} data={dataWithIcon} selected={true} softSelected={true}></ListItem>}
  </Story>
</Canvas>

<Canvas>
  <Story name="Longer content width">
    {(args) => (
      <ListItem
        {...args}
        data={{
          title:
            'This is a very long content option, it is so long that the content width is bigger than List Item width, it should be truncated and using tooltip. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.',
        }}
      ></ListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Longer content width Selected">
    {(args) => (
      <ListItem
        {...args}
        selected={true}
        data={{
          title:
            'This is a very long content option, it is so long that the content width is bigger than List Item width, it should be truncated and using tooltip. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.',
        }}
      ></ListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Longer content width SoftSelected">
    {(args) => (
      <ListItem
        {...args}
        softSelected={true}
        data={{
          title:
            'This is a very long content option, it is so long that the content width is bigger than List Item width, it should be truncated and using tooltip. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.',
        }}
      ></ListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Longer content width Selected SoftSelected">
    {(args) => (
      <ListItem
        {...args}
        selected={true}
        softSelected={true}
        data={{
          title:
            'This is a very long content option, it is so long that the content width is bigger than List Item width, it should be truncated and using tooltip. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat viverra libero, luctus auctor velit ultricies sit amet. Nullam eu porttitor erat, et faucibus nulla. Vestibulum nunc felis, blandit ac leo eget, consectetur luctus velit. Nam nulla urna, molestie a lobortis eu, commodo et metus. Duis pharetra nisi sed tempor molestie. Aliquam eget velit sollicitudin tortor convallis fringilla. Sed in pretium ligula, quis vulputate arcu. In sit amet ex mauris.',
        }}
      ></ListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Compact">
    {(args) => (
      <>
        <ListItem {...args} data={{ ...args.data, title: 'Default List Item' }}></ListItem>
        <ListItem {...args} data={{ ...args.data, title: 'Compact List Item' }} compact={true}></ListItem>
      </>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Level">
    {(args) => {
      return (
        <>
          <ListItem
            {...args}
            data={{ ...args.data, title: `Nesting level not specified (defaults to level 1)` }}
            level={level}
          ></ListItem>
          {[1, 2, 3, 4].map((level) => (
            <ListItem {...args} data={{ ...args.data, title: `Nesting level ${level}` }} level={level}></ListItem>
          ))}
        </>
      );
    }}
  </Story>
</Canvas>
