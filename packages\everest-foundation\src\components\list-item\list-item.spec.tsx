import * as React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { IDataItem, ListItem } from './list-item';
import { TIconName } from '../icon';

describe('[ListItem]', () => {
  const dataTitle = 'Title';
  const dataIcon: TIconName = 'clipboard';
  const data = {
    id: 'data-id-0',
    title: dataTitle,
  };
  const dataWithIcon = {
    id: 'data-id-0',
    title: dataTitle,
    iconName: dataIcon,
  };
  const itemRendererTitle = 'Item Render Title';
  const itemRendererSelectedTitle = 'Selected Item Render Title';
  const itemRendererData: IDataItem = {
    id: 'item-render-0',
    title: itemRendererTitle,
    additionalData: {
      selectedText: itemRendererSelectedTitle,
    },
  };
  const itemRenderer = (dataItem: unknown, selected: boolean) => {
    return (
      <div>
        <p>
          {selected
            ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
              ((dataItem as IDataItem).additionalData as any)['selectedText']
            : (dataItem as IDataItem).title}
        </p>
      </div>
    );
  };
  const testId = 'list-item-test-id';
  const id = 'option-1';
  const getListItem = () => screen.getByTestId(`${testId}`);
  const getIconByName = (name: string) => {
    return document.querySelectorAll(`svg[data-evr-name="${name}"]`)[0] || null;
  };

  /**
   * Adding ul parent container with role listbox and aria-label to satisfy
   * Certain ARIA roles must be contained by particular parents a11y rules
   */
  [
    {
      name: 'Default ListItem',
      jsx: (
        <ul role="listbox" aria-label="dummy">
          <ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} />
        </ul>
      ),
    },
    {
      name: 'Selected ListItem',
      jsx: (
        <ul role="listbox" aria-label="dummy">
          <ListItem data={data} testId={testId} id={id} selected={true} softSelected={false} />
        </ul>
      ),
    },
    {
      name: 'SoftSelected ListItem',
      jsx: (
        <ul role="listbox" aria-label="dummy">
          <ListItem data={data} testId={testId} id={id} selected={false} softSelected={true} />
        </ul>
      ),
    },
    {
      name: 'Selected and SoftSelected ListItem',
      jsx: (
        <ul role="listbox" aria-label="dummy">
          <ListItem data={data} testId={testId} id={id} selected={true} softSelected={true} />
        </ul>
      ),
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getListItem()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  it('should render', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} />);
    expect(getListItem()).toBeInTheDocument();
  });

  it('should have data', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} />);
    expect(screen.getByText(dataTitle)).toBeInTheDocument();
  });

  it('should have dataIcon', () => {
    render(<ListItem data={dataWithIcon} testId={testId} id={id} selected={false} softSelected={false} />);
    expect(screen.getByText(dataTitle)).toBeInTheDocument();
    expect(getIconByName(dataIcon)).toBeInTheDocument();
  });

  it('should have dataTitle and not itemRendererTitle', () => {
    render(
      <ListItem data={data} itemRenderer={itemRenderer} testId={testId} id={id} selected={false} softSelected={false} />
    );
    expect(screen.getByText(dataTitle)).toBeInTheDocument();
  });

  it('should have itemRendererTitle', () => {
    render(
      <ListItem
        data={itemRendererData}
        itemRenderer={itemRenderer}
        testId={testId}
        id={id}
        selected={false}
        softSelected={false}
      />
    );
    expect(screen.getByText(itemRendererTitle)).toBeInTheDocument();
  });

  it('should have itemRendererSelectedTitle', () => {
    render(
      <ListItem
        data={itemRendererData}
        itemRenderer={itemRenderer}
        testId={testId}
        id={id}
        selected={true}
        softSelected={false}
      />
    );
    expect(screen.getByText(itemRendererSelectedTitle)).toBeInTheDocument();
  });

  it('should have id', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} />);
    expect(getListItem()).toHaveAttribute('id', id);
  });

  it('should have role "option"', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} />);
    expect(getListItem()).toHaveAttribute('role', 'option');
  });

  it('should not have aria-selected when selected is false', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} />);
    expect(getListItem()).toHaveAttribute('aria-selected', 'false');
  });

  it('should have aria-selected when selected is false', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={true} softSelected={false} />);
    expect(getListItem()).toHaveAttribute('aria-selected', 'true');
  });

  it('should not display checkSmall icon when selected is false', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} />);
    expect(getIconByName('checkSmall')).toBeNull;
  });

  it('should display checkSmall icon when selected is true', () => {
    render(<ListItem data={data} testId={testId} id={id} selected={true} softSelected={false} />);
    expect(getIconByName('checkSmall')).toBeInTheDocument();
  });

  describe('keyDown and keyUp event', () => {
    const onKeyDown = jest.fn();
    const onKeyUp = jest.fn();
    beforeEach(onKeyDown.mockReset);
    beforeEach(onKeyUp.mockReset);
    it('dispatch a keyDown and a keyUp event by keyboard space key', async () => {
      render(
        <ListItem
          data={data}
          testId={testId}
          id={id}
          selected={false}
          softSelected={false}
          onKeyDown={onKeyDown}
          onKeyUp={onKeyUp}
        />
      );
      await userEvent.type(getListItem(), ' ');
      expect(onKeyDown).toHaveBeenCalledTimes(1);
      expect(onKeyUp).toHaveBeenCalledTimes(1);
    });

    it('dispatch a keyDown and a keyUp event by keyboard enter key', async () => {
      render(
        <ListItem
          data={data}
          testId={testId}
          id={id}
          selected={false}
          softSelected={false}
          onKeyDown={onKeyDown}
          onKeyUp={onKeyUp}
        />
      );
      await userEvent.type(getListItem(), '{enter}');
      expect(onKeyDown).toHaveBeenCalledTimes(1);
      expect(onKeyUp).toHaveBeenCalledTimes(1);
    });
  });

  describe('Selection event', () => {
    const onSelection = jest.fn();
    beforeEach(onSelection.mockReset);
    it('dispatch a onSelection event by mouse click', async () => {
      render(
        <ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} onSelection={onSelection} />
      );
      await userEvent.click(getListItem());
      expect(onSelection).toHaveBeenCalled();
    });

    it('dispatch a onSelection event by keyboard space key', async () => {
      render(
        <ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} onSelection={onSelection} />
      );
      await userEvent.type(getListItem(), ' ');
      expect(onSelection).toHaveBeenCalled();
    });

    it('dispatch a onSelection event by keyboard enter key', async () => {
      render(
        <ListItem data={data} testId={testId} id={id} selected={false} softSelected={false} onSelection={onSelection} />
      );
      await userEvent.type(getListItem(), '{enter}');
      expect(onSelection).toHaveBeenCalled();
    });
  });
});
