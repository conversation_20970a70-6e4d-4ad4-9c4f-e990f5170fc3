import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { ListItem } from './list-item';
import { action } from '@storybook/addon-actions';
import Examples from './list-item.examples.mdx';

<Meta
  title="Toolbox/ListItem"
  component={ListItem}
  parameters={{
    controls: {
      exclude: ['dir'],
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    onKeyDown: {
      control: '-',
    },
    onKeyUp: {
      control: '-',
    },
    onSelection: {
      control: '-',
    },
    onFocus: {
      control: '-',
    },
    onBlur: {
      control: '-',
    },
    itemRenderer: {
      control: '-',
    },
    onMouseDown: {
      control: '-',
    },
    onMouseMove: {
      control: '-',
    },
    onSoftSelectedChange: {
      control: '-',
    },
  }}
  args={{
    id: 'option-1',
    testId: 'list-item-test-id',
    selected: false,
    softSelected: false,
    data: { id: 'item-0', title: 'First Option' },
    ariaLabel: '',
    onSelection: action('onSelection'),
  }}
/>

# ListItem

<Examples />

## Live Demo

<Canvas>
  <Story name="ListItem">
    {(args) => (
      <ul role="listbox" aria-label="dummy">
        <ListItem {...args}></ListItem>
      </ul>
    )}
  </Story>
</Canvas>

<ArgsTable story="ListItem" />
