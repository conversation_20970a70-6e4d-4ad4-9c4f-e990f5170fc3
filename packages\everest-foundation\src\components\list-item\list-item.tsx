import React, { RefObject, useContext, useEffect, useRef } from 'react';
import classnames from 'classnames';

import { IListItemContext, ListItemContext } from './list-item-context';
import { mergeRefs, useCreateTestId } from '../../utils';
import { resolvePropsContext } from '../../utils/resolve-props-context';
import { CheckboxBase } from '../checkbox-base';
import { Icon, TIconName } from '../icon';

import styles from './list-item.module.scss';

export type TDataItemType = 'loading' | 'noResults';
export type TSelectionType = 'checkmark' | 'checkbox';

export interface IDataItem {
  /** The title of the list item. */
  title: string;
  /** The value of the list item. */
  value?: string;
  /** The unique id of the list item */
  id: string;
  /** The icon to display next to the list item. */
  iconName?: TIconName;
  /** Additional data to be passed to the list item. */
  additionalData?: unknown;
  /** The type of data item. */
  type?: TDataItemType;
  /** The aria-describedby attribute for the list item. */
  ariaDescribedBy?: string;
  /** The children of the list item */
  items?: IDataItem[];
}

export interface IListItemProps {
  /** Sets **data-testid** attribute on the html element; used for automation testing. */
  testId?: string;
  /** A clear description of the list item. */
  ariaLabel?: string;
  /** A unique id required for accessibility purposes. */
  id: string;
  /** The data for the active list item (id, title, etc). */
  data: IDataItem;
  /** Indicates if the ListItem is selected.
   * @default false
   */
  selected: boolean;
  /** Indicates if the ListItem is soft selected (which can happen when focused by using keyboard or when the mouse is hovered).
   * @default false
   */
  softSelected: boolean;
  dir?: 'ltr' | 'rtl';
  /** Custom renderer for the ListItem. The function receives the dataItem and selected state and should return a ReactNode. */
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  /** Callback when mouse `down` is triggered on the list item. */
  onMouseDown?: (e: React.MouseEvent, index: number, renderIndex: number, data?: IDataItem) => void;
  /** Callback when mouse `move` is triggered on the list item. */
  onMouseMove?: (e: React.MouseEvent) => void;
  /** Callback when keyboard `keyDown` is triggered on the list item. */
  onKeyDown?: (e: React.KeyboardEvent, index: number, renderIndex: number, dataItem: IDataItem) => void;
  /** Callback when keyboard `keyUp` is triggered on the list item. */
  onKeyUp?: (e: React.KeyboardEvent, index: number, renderIndex: number, dataItem: IDataItem) => void;
  /** Callback when the list item loses focus. */
  onBlur?: (e: React.FocusEvent, index: number, renderIndex: number, dataItem: IDataItem) => void;
  /** Callback when the list item gains focus. */
  onFocus?: (e: React.FocusEvent, index: number, renderIndex: number, dataItem: IDataItem) => void;
  /** Callback when the list item is soft selected. */
  onSoftSelectedChange?: (
    index: number,
    id?: string, // keep this to support listBox version 1, should remove when combined
    renderIndex?: number,
    dataItem?: IDataItem
  ) => void;
  /** Callback on selection of the list item. */
  onSelection?: (dataItem: IDataItem) => void;
  /** When `true` stops aria-activedescendant from changing and the method onSoftSelectedChange from running. */
  stopMouseOverSoftSelection?: boolean;
  /** Index of LI element in iterator. */
  index?: number;
  /** Indicates if the focus ring is visible when `softSelected` is true. */
  isFocusVisible?: boolean;
  /** Index of a list item that is rendered. */
  renderIndex?: number;
  /** Indicates if the list item is focused when soft selected.
   * @default true
   */
  focusOnSoftSelected?: boolean;
  ref?: RefObject<HTMLLIElement>;
  /** When `true` the ListItem is rendered with classes for reducing padding and font-size.
   * @default false
   */
  compact?: boolean;
  /** The type of selection to use for the list item.
   * @default checkmark
   */
  selectionType?: TSelectionType;
  /** The grouping level of the current group; currently the only support the value of `1`.
   * @default 1
   */
  level?: number;
}

export const ListItem = React.forwardRef((props: IListItemProps, ref) => {
  const context = useContext(ListItemContext);
  const {
    testId,
    ariaLabel,
    id,
    data,
    selected = false,
    softSelected = false,
    // dir,
    itemRenderer,
    onMouseDown,
    onMouseMove,
    onKeyDown,
    onKeyUp,
    onBlur,
    onFocus,
    onSoftSelectedChange,
    onSelection,
    selectionType = 'checkmark',
    isFocusVisible,
    stopMouseOverSoftSelection = false,
    index = -1,
    renderIndex = -1,
    focusOnSoftSelected = true,
    compact,
    level = 1,
  } = resolvePropsContext<IListItemProps, IListItemContext>(props, context);

  const dataRef = useCreateTestId(testId);

  const handleMouseDown = (event: React.MouseEvent) => {
    onSelection?.(data);
    onMouseDown?.(event, index, renderIndex, data);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case ' ':
      case 'Enter':
        onSelection?.(data);
        break;
    }
    onKeyDown?.(event, index, renderIndex, data);
  };

  const handleKeyUp = (event: React.KeyboardEvent) => {
    onKeyUp?.(event, index, renderIndex, data);
  };

  const handleBlur = (event: React.FocusEvent) => {
    onBlur?.(event, index, renderIndex, data);
  };

  const handleFocus = (event: React.FocusEvent) => {
    onFocus?.(event, index, renderIndex, data);
  };

  const handleMouseOver = () => {
    if (!stopMouseOverSoftSelection) {
      context.setAriaActiveDescendantId?.(id);
      onSoftSelectedChange?.(index, id, renderIndex, data);
    }
  };

  const getFillColor = () => {
    if (selected && softSelected) return '--evr-interactive-primary-hovered';
    if (selected) return '--evr-interactive-primary-default';
    return '--evr-content-primary-default';
  };

  /**
   * When first initialized listItem, the listItem is not always on the correct position
   */
  const firstRun = useRef(true);
  useEffect(() => {
    if (softSelected && !firstRun.current && focusOnSoftSelected) {
      dataRef.current && (dataRef.current as HTMLElement).focus();
    }
    if (firstRun.current) {
      firstRun.current = false;
    }
    if (softSelected) {
      context.setAriaActiveDescendantId?.(id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataRef, softSelected, focusOnSoftSelected]);

  return (
    // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
    <li
      className={classnames(styles.evrListItem, {
        [styles.softSelected]: softSelected,
        [styles.focusVisible]: isFocusVisible,
        [styles.presentation]: selectionType === 'checkbox',
        [styles.compact]: compact,
        [styles[`nested${level}`]]: level > 1,
      })}
      role={data.type ? 'presentation' : 'option'}
      id={id}
      key={id}
      aria-label={ariaLabel || data.title}
      aria-selected={data.type ? undefined : selected}
      onMouseDown={handleMouseDown}
      onKeyDown={handleKeyDown}
      onKeyUp={handleKeyUp}
      onBlur={handleBlur}
      onFocus={handleFocus}
      onMouseOver={handleMouseOver}
      onMouseMove={onMouseMove}
      ref={mergeRefs([ref, dataRef])}
      tabIndex={-1}
      aria-describedby={data.ariaDescribedBy}
    >
      {selectionType === 'checkbox' && (
        <CheckboxBase
          id={`${id}-checkbox`}
          testId={testId ? `${testId}-checkbox` : undefined}
          checkedState={selected ? 'checked' : 'unchecked'}
          presentation={true}
        />
      )}
      <div className={styles.evrListItemContainer}>
        {itemRenderer ? (
          <>{itemRenderer(data, selected)}</>
        ) : (
          <>
            {!!data.iconName && (
              <div className={classnames(styles.evrIconContainer)}>
                <Icon name={data.iconName} fill={getFillColor()} testId={testId ? `${testId}-icon` : undefined}></Icon>
              </div>
            )}
            <p
              className={classnames(
                styles.evrStringListItem,
                { [`evrBodyText${compact ? '2' : '1'}`]: true },
                {
                  [styles.presentation]: selectionType === 'checkbox',
                  [styles.selected]: selected,
                  [styles.softSelected]: selected && softSelected,
                }
              )}
              title={data.title}
            >
              {data.title}
            </p>
          </>
        )}
      </div>
      {selected && selectionType === 'checkmark' && (
        <Icon
          name="checkSmall"
          fill={softSelected ? '--evr-interactive-primary-hovered' : '--evr-interactive-primary-default'}
          testId={testId ? `${testId}-checked-icon` : undefined}
        ></Icon>
      )}
    </li>
  );
});

ListItem.displayName = 'ListItem';
