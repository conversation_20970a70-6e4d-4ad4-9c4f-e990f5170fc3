import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Avatar } from '../avatar';
import { MediaObject } from './media-object';
import { Tag } from '../tag';
import { HighlightedIcon } from '../highlighted-icon';
import userTaro from '../../../assets/images/user-taro.jpg';
import userCage from '../../../assets/images/avatar-sample.png';

export const scope = { Avatar, MediaObject, HighlightedIcon, Tag, userTaro, userCage };

`MediaObject` combines a visual element with a text-based component, creating a layout to present content in a clear and organized way. 

### Basic Example

This example passes `Avatar` component to the `media` prop. The `media` prop can accept any valid React element, including custom components.

export const basicCode = `
  <MediaObject
    media={
      <Avatar
        id='picture-avatar'
        title='This is a title'
        src={userTaro}
        size="lg"
      />
    }
    id='basic-media-object'
    title='<PERSON>'
    subtitle='2 days ago'
    gap="--evr-spacing-sm"
  </MediaObject>
`;

<CodeExample scope={scope} code={basicCode} />

## Sizing

The `MediaObject` component has no defined sizes and will adapt to the size of the `media` element.

The default font styles are applied to the `title` and `subtitle` using the `evrHeading3` and `evrBodyText2` classes respectively.
Font styles and colors can be overridden by providing custom templates for the `title` and `subtitle` prop.

export const sizeCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            alignItems: 'flex-start'
            flexWrap: 'wrap',
            gap: '16px',
            padding: '16px'
        },
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
            <MediaObject
                media={<Avatar id='size-avatar-lg' ariaLabel='This is an Avatar with picture - size lg' size='lg' src={userCage} />}
                id='media-object-with-large-avatar'
                title={<><h3 className="evrHeading3">Nicolas Cage</h3></>}
                subtitle={<span className="evrBodyText2">Legendary actor</span>}
                gap="--evr-spacing-sm"
            />
            <MediaObject
                media={<Avatar id='size-avatar-md' ariaLabel='This is an Avatar with picture - size md' size='md' src={userCage} />}
                id='media-object-with-medium-avatar'
                title={<><h3 className="evrHeading3">Nicolas Cage</h3></>}
                subtitle={<span className="evrBodyText2">Legendary actor</span>}
                gap="--evr-spacing-sm"
            />
            <MediaObject
                media={<Avatar id='size-avatar-sm' ariaLabel='This is an Avatar with picture - size sm' size='sm' src={userCage} />}
                id='media-object-with-small-avatar'
                title={<><h3 className="evrBodyText1 evrBold">Nicolas Cage</h3></>}
                subtitle={<span className="evrBodyText2">Legendary actor</span>}
                gap="--evr-spacing-2xs"
            />
            <MediaObject
                media={<Avatar id='size-avatar-xs' ariaLabel='This is an Avatar with picture - size xs' size='xs' src={userCage} />}
                id='media-object-with-extra-small-avatar'
                title={<><h4 className="evrBodyText2 evrBold">Nicolas Cage</h4></>}
                subtitle={<p className='evrCaptionText'>Legendary actor</p>}
                gap="--evr-spacing-2xs"
            />
        </Row>
    );
}`;

<CodeExample scope={scope} code={sizeCode} />

## Examples

### With an Avatar And a Tag

export const tagCode = `
    <MediaObject
        media={
            <Avatar
                id='picture-avatar'
                title='This is a title'
                src={userTaro}
                size="lg"
            />
        }
        id='media-object-with-picture-and-tag'
        title={<><h3 className="evrHeading3">This is a title with a tag</h3><Tag label="tag"></Tag></>}
        subtitle='This is a subtitle'
        gap="--evr-spacing-sm"
    </MediaObject>
`;

<CodeExample scope={scope} code={tagCode} />

### With a HighlightedIcon

export const highlightedIcon = `
    <MediaObject
        media={
            <HighlightedIcon  
                iconName='rocketship' 
                fill='neutral' 
            />
        }
        id='media-object-with-picture-and-tag'
        title={<h3 className="evrHeading3">This is a title</h3>}
        subtitle='This is a subtitle'
        gap="--evr-spacing-sm"
    </MediaObject>
`;

<CodeExample scope={scope} code={highlightedIcon} />

### With No Media Provided. 

export const noMedia = `
    <MediaObject
        id='media-object-with-no-media'
        title={<h3 className="evrHeading3">this is a title</h3>}
        subtitle={<span className="evrBodyText2">this is a subtitle</span>}
        gap="--evr-spacing-sm"
    </MediaObject>
`;

<CodeExample scope={scope} code={noMedia} />

### Top vs Center Media Alignment

export const alignmentExamples = `() => {
    const styles = {
        row: {
            display: 'flex',
            gap: '16px',
            padding: '16px'
        },
        customWidth: {
            width: '200px',
        },
    };
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Wrapper = ({ children }) => (
        <div style={styles.customWidth}>{children}</div>
    );
    return (
        <Row>
        <Wrapper>
            <MediaObject
                media={<Avatar id='size-avatar-lg' ariaLabel='This is an Avatar with picture - size lg' size='lg' src={userCage} />}
                id='alignment-1'
                title='Nicolas Cage'
                subtitle='Extra long description for demonstration purposes.'
                gap="--evr-spacing-sm"
                mediaAlignment='top'
            />
          </Wrapper>
          <Wrapper>
            <MediaObject
                media={<Avatar id='size-avatar-lg' ariaLabel='This is an Avatar with picture - size lg' size='lg' src={userCage} />}
                id='alignment-2'
                title='Nicolas Cage'
                subtitle='Extra long description for demonstration purposes.'
                gap="--evr-spacing-sm"
                mediaAlignment='center'
            />
            </Wrapper>
        </Row>
    );
}
`;

<CodeExample scope={scope} code={alignmentExamples} />