# MediaObject

- Start Date: 2025-04-17
- Figma link: <https://www.figma.com/design/yzd3fHCowfWDL4iTBAoJNv/Everest-Documentation-for-Designers?node-id=16054-280&p=f&t=mGL0jUf4gj8eyWUq-0>
- Epic: <https://dayforce.atlassian.net/browse/PWEB-19687>

## Summary

The `MediaObject` component is a horizontally stacked layout that combines a media slot (e.g., Avatar, Icon) on the left and a content section on the right. The content section includes a title and subtitle, which are vertically stacked.

## Detailed Design

The component consists of two main sections:

1. **Media Slot**:
   - Accepts media content (e.g., Avatar, Icon) via the `media` prop.
   - The vertical alignment of the media slot relative to the content is controlled by the `mediaAlignment` prop, which can be set to `'top'` or `'center'`.

2. **Content Section**:
   - Contains a title and subtitle, stacked vertically.
   - The layout adjusts based on the `mediaAlignment` prop:
     - When `mediaAlignment` is `'top'`, the content section aligns with the top of the media slot, ensuring proper alignment when the title and subtitle wrap onto multiple lines.
     - When `mediaAlignment` is `'center'`, the content section is vertically centered with the media slot, creating a balanced layout for shorter content that fits on a single line.

The layout is horizontally stacked with a gap between the media slot and the content section.

## API

1. **id**: `string`  
   Sets the **id** attribute on the root HTML element.

2. **testId**: `undefined | string`  
   Sets the **data-testid** attribute on the root HTML element for testing purposes.

3. **title**: `string | ReactNode`  
   Renders the title content. Can be a string or a custom ReactNode.

4. **subtitle**: `undefined | string | ReactNode`  
   Optional. Renders the subtitle content. Can be undefined or a custom ReactNode.

5. **media**: `undefined | ReactNode`  
   Optional. Renders the media content (e.g., Avatar, Icon). The media is clipped to the size of the slot.

6. **mediaAlignment**: `'top' | 'center'`  
   Optional. Determines the vertical alignment of the media slot relative to the content (title and subtitle). The default value is `'center'`.

   `top`: Aligns the media slot to the top of the content. This is ideal when the content (title and subtitle) is expected to wrap onto multiple lines.

   `center`: Vertically centers the media slot with the content. This is ideal for shorter content that fits on a single line.

7. **className**: `undefined | string`  
   Optional. Enables additional styling control (e.g., min-width, width, max-width, borders) and allows customization of the default gap between elements.

8. **gap**: `--evr-spacing-2xs` | `--evr-spacing-sm`
    This will be used to set the CSS variable name for the gap between media and content.

### Usage With Default Styling

```jsx
 <MediaObject
        media={
            <Avatar
                id='picture-avatar'
                title='This is a title'
                src={mountain}
                size="lg"
            />
        }
        id='media-object-with-picture'
        title={<h3 class="evrHeading3">Some heading </h3>}
        subtitle={<div>this is a subtitle</div>}
        mediaAlignment='center'
        gap='--evr-spacing-sm'
/>
```


### Usage With Custom Styling

```jsx
   <MediaObject
        media={
            <Avatar
                id='picture-avatar'
                title='This is a title'
                src={mountain}
                size="lg"
            />
        }
        id='media-object-with-picture-and-tag'
        title={<><h3 class="evrHeading3">Some heading </h3><Tag label="this is a tag"></Tag></>}
        subtitle={<div>this is a subtitle</div>}
        mediaAlignment='top'
        gap='--evr-spacing-2xs'
/>
```

## Accessibility

- The `media` prop is responsible for handling its own ARIA properties and attributes.

- The title and subtitle are rendered as text nodes by default, ensuring they are accessible to screen readers.

## Future Considerations

- Support for additional layout variations (e.g., vertical stacking of media and content).
- Add support for secondary actions or buttons in the content section.

## Other Design Systems


## Required PBIs

1. [Create Architecture document](https://dayforce.atlassian.net/browse/PWEB-20339)
1. [Create React Component + Docs](https://dayforce.atlassian.net/browse/PWEB-20341)
1. [Tests: A11y, Manual, Visual tests, Unit tests, playwright tests](https://dayforce.atlassian.net/browse/PWEB-20342)


## Q&A

## Changelog