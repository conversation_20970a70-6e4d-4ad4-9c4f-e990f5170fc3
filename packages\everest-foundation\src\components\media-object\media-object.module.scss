.evrMediaObject {
  display: flex;
  
  .contentWrapper {
    display: flex;
    flex-direction: column;
    
    .title {
      
      display: flex;
      gap: var(--evr-spacing-xs);
      
      > * {
        color: var(--evr-content-primary-highemp); // evr classes introduce color that we don't want
        margin: 0; // evr classes introduce margin that we don't want
      }
    }
  
    .subtitle {
      color: var(--evr-content-primary-lowemp);

      > * {
        color: var(--evr-content-primary-lowemp); // evr classes introduce color that we don't want
      }
    }
  }
}
