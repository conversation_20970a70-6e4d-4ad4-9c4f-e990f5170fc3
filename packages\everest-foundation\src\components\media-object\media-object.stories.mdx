import { Meta, <PERSON>vas, Story, ArgsTable } from '@storybook/addon-docs';
import { MediaObject } from './media-object';
import { Avatar } from '../avatar';
import userCage from '../../../assets/images/avatar-sample.png';

import Examples from './media-object.examples.mdx';

<Meta title="Everest Labs/Components/MediaObject" component={MediaObject}
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/yzd3fHCowfWDL4iTBAoJNv/Everest-Documentation-for-Designers?node-id=16054-280&p=f&t=mGL0jUf4gj8eyWUq-0',
    },
  }}
  args={{
    id: "media-object-demo",
    title: "Nicolas Cage",
    subtitle: "Legendary actor",
    gap: '--evr-spacing-2xs',
    media: 'Defined',
    mediaAlignment: 'center',
  }}
  argTypes={{
    media: {
      control: 'select',
      options: ['Defined', 'Undefined'],
      mapping: {
        Defined: <Avatar id='size-avatar-lg' ariaLabel='This is an Avatar with picture - size lg' size='lg' src={userCage} />,
        Undefined: undefined,
      },
    },
    gap: {
      control: 'select',
    },
    mediaAlignment: {
      control: 'select',
    }
  }}/>

# MediaObject

<Examples />

## Live Demo

<Canvas>
  <Story name="MediaObject">{(args) => <MediaObject {...args} />}</Story>
</Canvas>

<ArgsTable story="MediaObject" />