import React, { ReactNode } from 'react';
import classnames from 'classnames';

import styles from './media-object.module.scss';

export type TMediaAlignment = 'top' | 'center';

export type TMediaGap = '--evr-spacing-2xs' | '--evr-spacing-sm';

export interface IMediaObjectProps {
  /** Sets the `id` attribute on the root container element */
  id?: string;
  /** Sets the `data-testid` attribute for testing purposes */
  testId?: string;
  /** Custom class name to provide additional styling such as width, gap etc..*/
  className?: string;
  /** Renders the title content. Can be a string or a custom ReactNode */
  title: string | ReactNode;
  /** Renders the subtitle content. Can be undefined or a custom ReactNode */
  subtitle?: string | ReactNode;
  /** Renders the media content (e.g., Avatar, Icon).*/
  media?: ReactNode;
  /** Determines the vertical alignment of the media slot relative to the content */
  mediaAlignment?: TMediaAlignment;
  /** CSS variable name for the gap between media and content (e.g., --evr-spacing-sm) */
  gap: TMediaGap;
}

export const MediaObject: React.FC<IMediaObjectProps> = ({
  id,
  testId,
  className,
  title,
  subtitle,
  media,
  mediaAlignment = 'center',
  gap = '--evr-spacing-sm',
}: IMediaObjectProps): JSX.Element => {
  return (
    <div
      id={id}
      data-testid={testId}
      className={classnames(styles.evrMediaObject, className)}
      style={{ gap: `var(${gap})` }}
    >
      {media && <div style={{ alignSelf: mediaAlignment === 'top' ? 'start' : 'center' }}>{media}</div>}

      <div className={classnames(styles.contentWrapper)}>
        {title && <div className={classnames(styles.title, 'evrHeading3')}>{title}</div>}
        {subtitle && <div className={classnames(styles.subtitle, 'evrBodyText2')}>{subtitle}</div>}
      </div>
    </div>
  );
};

MediaObject.displayName = 'MediaObject';
