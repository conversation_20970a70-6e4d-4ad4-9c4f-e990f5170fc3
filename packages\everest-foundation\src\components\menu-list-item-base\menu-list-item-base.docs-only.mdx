import { Meta } from '@storybook/addon-docs';
import { LinkTo } from '../../../.storybook/docs/shared/link-to.tsx';

<Meta title="Toolbox/Menu List Item/Menu List Item Base" />

# Menu List Item Base

The `MenuListItemBase` serves as the base component for all menu list item components offering the necessary functionality while allowing to
customize the look to match specific design requirements.

Currently the two components that are based on the `MenuListItemBase` are:

- `MenuListItem` - exposed externally
- `MenuListItemToolbar` - internal component used only in a `PopoverMenu` when inside the `Toolbar` component

The `MenuListItem` is aliased as `PopoverMenuItem` when used in the <LinkTo kind="Components/Popover Menu">PopoverMenu</LinkTo>.

For API specification and examples, please refer to the <LinkTo kind="Toolbox/Menu List Item/Menu List Item">MenuListItem</LinkTo>.
