@use '@ceridianhcm/theme/dist/scss/' as typography;
@use '../../index.scss' as helper;

.evrMenuListItemBase {
  @include typography.body1Regular;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  list-style-type: none;
  color: var(--evr-content-primary-default);
  background-color: var(--evr-surfaces-primary-default);
  padding: var(--evr-spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  outline: none;
  box-sizing: border-box;
  cursor: pointer;
  min-height: var(--evr-size-2xl);

  &.hover {
    background-color: var(--evr-surfaces-primary-hovered);
    transition: background-color 0.15s;
  }
  &.focus,
  &.active {
    color: var(--evr-interactive-primary-default);
  }
  &:active {
    background-color: var(--evr-surfaces-secondary-hovered);
  }
  &.disabled {
    color: var(--evr-inactive-content);
    cursor: not-allowed;
  }

  &.disabled:active {
    background-color: var(--evr-surfaces-primary-default);
    cursor: not-allowed;
  }

  // This is to support sub sublist, we are only going to be supporting 2 levels of submenus
  & .evrMenuListItemBase {
    > .evrMenuListItemBaseText {
      padding-inline: var(--evr-spacing-xs);
      padding-block: 0;
    }

    > .evrMenuListItemBaseHeader {
      padding: 0 var(--evr-spacing-sm) 0 var(--evr-spacing-lg);
    }

    & .evrMenuListItemBase {
      > .evrMenuListItemBaseText {
        padding: 0 var(--evr-spacing-lg) 0 var(--evr-spacing-lg);
      }
    }
  }
}

.evrMenuListItemBaseDivider {
  margin: var(--evr-spacing-3xs) 0 var(--evr-spacing-3xs) 0;
}

.evrMenuListItemBaseText {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  width: 100%;
  padding-inline-end: var(--evr-spacing-2xs);

  &.hover {
    color: var(--evr-interactive-primary-hovered);
  }
}

.evrMenuListItemBaseHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--evr-spacing-2xs);
  max-width: helper.applyRemFactor(23rem); // 384px same as contextual menu width without padding
  padding: var(--evr-spacing-xs);
  min-height: var(--evr-size-2xl);
  width: 100%;
  box-sizing: border-box;
}

.evrMenuListItemBaseExpanded {
  flex-direction: column;
  justify-content: center;
  padding: 0;
}
