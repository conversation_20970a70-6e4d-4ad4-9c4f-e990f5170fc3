import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { MenuListItemHeader } from '../menu-list-item-header';

import { MenuListItemBase } from '.';

const testId = 'menu-item-test';
const id = 'menu-item';
const setActiveDescendantId = jest.fn();
const activeDescendantId = '';
const onChange = jest.fn();
const checkedIds: string[] = [];
const closeMenu = jest.fn();

const props = {
  id,
  testId,
  setActiveDescendantId,
  activeDescendantId,
  onChange,
  checkedIds,
  closeMenu,
};

const user = userEvent.setup({ skipHover: true });
const getMenuItem = () => screen.getByRole('menuitem');
const getMenuItemCheckbox = () => screen.getByRole('menuitemcheckbox');
const getMenuItemRadio = () => screen.getByRole('menuitemradio');

const defaultText = 'default item';

describe('[MenuListItemBase]', () => {
  beforeEach(() => {
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  afterEach(() => {
    jest.resetAllMocks();
  });

  [
    {
      name: 'Default item with text',
      jsx: <MenuListItemBase {...props}>Enabled Item</MenuListItemBase>,
    },
    {
      name: 'Default item with text and divider',
      jsx: (
        <MenuListItemBase {...props} divider>
          Enabled Item with Divider
        </MenuListItemBase>
      ),
    },
    {
      name: 'Disabled item with text',
      jsx: (
        <MenuListItemBase {...props} disabled>
          Disabled Item
        </MenuListItemBase>
      ),
    },
    {
      name: 'Disabled item with text and divider',
      jsx: (
        <MenuListItemBase {...props} disabled divider>
          Disabled Item with Divider
        </MenuListItemBase>
      ),
    },
    {
      name: 'Menu item with various children',
      jsx: (
        <MenuListItemBase {...props}>
          Menu Item
          <div>
            <span>Non-string Child</span>
          </div>
        </MenuListItemBase>
      ),
    },
    {
      name: 'Checked item',
      jsx: (
        <MenuListItemBase {...props} checkedIds={[props.id]}>
          Enabled Item
        </MenuListItemBase>
      ),
    },
  ].forEach(function (item) {
    it(`${item.name} should render without issue`, () => {
      render(item.jsx);
    });
  });

  it('should have default role menuitem, tab index equal to -1, and no aria-checked attribute', () => {
    const componentText = 'Item';
    const component = <MenuListItemBase {...props}>{componentText}</MenuListItemBase>;
    render(component);
    expect(getMenuItem()).toHaveAttribute('role', 'menuitem');
    expect(getMenuItem()).toHaveAttribute('tabindex', '-1');
    expect(getMenuItem()).not.toHaveAttribute('aria-checked');
  });
  it('should have role menuitemcheckbox when specified', () => {
    const componentText = 'Item';
    const component = (
      <MenuListItemBase {...props} role="menuitemcheckbox">
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    expect(getMenuItemCheckbox()).toHaveAttribute('role', 'menuitemcheckbox');
  });
  it('should have role menuitemradio when specified', () => {
    const componentText = 'Item';
    const component = (
      <MenuListItemBase {...props} role="menuitemradio">
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    expect(getMenuItemRadio()).toHaveAttribute('role', 'menuitemradio');
  });
  it('should have aria-checked when specified', () => {
    const componentText = 'Item';
    const component = (
      <MenuListItemBase {...props} role="menuitemcheckbox" checkedIds={[id]}>
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    expect(getMenuItemCheckbox()).toHaveAttribute('aria-checked', 'true');
  });
  it('should not have aria-checked when not specified', () => {
    const componentText = 'Item';
    const component = (
      <MenuListItemBase {...props} role="menuitemcheckbox" checkedIds={['invalid-id']}>
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    expect(getMenuItemCheckbox()).toHaveAttribute('aria-checked', 'false');
  });
  it('should not have aria-checked when passed id but missing role menuitemcheckbox', () => {
    const componentText = 'Item';
    const component = (
      <MenuListItemBase {...props} checkedIds={[id]}>
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    expect(getMenuItem()).not.toHaveAttribute('aria-checked');
  });
  it('should have aria-disabled attribute be false by default', () => {
    const componentText = 'Enabled Item';
    const component = <MenuListItemBase {...props}>{componentText}</MenuListItemBase>;
    render(component);
    expect(getMenuItem()).toHaveAttribute('aria-disabled', 'false');
  });

  // both onMouseOver and onMouseEnter are called when hovering.
  it('should call setActiveDescendantId twice when hovered', async () => {
    const component = (
      <MenuListItemBase {...props} activeDescendantId="">
        {defaultText}
      </MenuListItemBase>
    );

    const { rerender } = render(component);
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(0);

    await userEvent.hover(getMenuItem());
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(2);

    rerender(
      <MenuListItemBase {...props} activeDescendantId={id}>
        {defaultText}
      </MenuListItemBase>
    );

    await userEvent.hover(getMenuItem());
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(2);
  });

  it('should call setActiveDescendantId once when moused over', async () => {
    const componentText = 'Enabled Item';
    const component = (
      <MenuListItemBase {...props} activeDescendantId="">
        {componentText}
      </MenuListItemBase>
    );
    const { rerender } = render(component);
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(0);
    // userEvent.hover appears to trigger multiple events, so using fireEvent
    fireEvent.mouseMove(screen.getByText(componentText));
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(1);
    rerender(
      <MenuListItemBase {...props} activeDescendantId={id}>
        {componentText}
      </MenuListItemBase>
    );
    // check that setActiveDescendantId isn't called again when mousing over the same element
    await userEvent.hover(getMenuItem()); //fireEvent.mouseMove(screen.getByText(componentText));
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(1);
  });

  it('should call setActiveDescendantId once when mouse moved over', () => {
    const componentText = 'Enabled Item';
    const component = (
      <MenuListItemBase {...props} activeDescendantId="">
        {componentText}
      </MenuListItemBase>
    );
    const { rerender } = render(component);
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(0);
    fireEvent.mouseMove(screen.getByText(componentText));
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(1);
    rerender(
      <MenuListItemBase {...props} activeDescendantId={id}>
        {componentText}
      </MenuListItemBase>
    );
    // check that setActiveDescendantId isn't called again when mousing over the same element
    fireEvent.mouseMove(screen.getByText(componentText));
    expect(props.setActiveDescendantId).toHaveBeenCalledTimes(1);
  });

  it('should call onChange (with id and role menuitem), closeMenu, and setActiveDescendantId when clicked', async () => {
    const componentText = 'Enabled Item';
    const component = <MenuListItemBase {...props}>{componentText}</MenuListItemBase>;
    render(component);
    await user.click(screen.getByText(componentText));
    expect(setActiveDescendantId).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id, role: 'menuitem' });
    expect(closeMenu).toHaveBeenCalledTimes(1);
  });

  it('should call onChange with role menuitemcheckbox as specified when clicked', async () => {
    const componentText = 'Enabled Item';
    const component = (
      <MenuListItemBase {...props} role="menuitemcheckbox">
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    await user.click(screen.getByText(componentText));
    expect(setActiveDescendantId).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id, role: 'menuitemcheckbox' });
    expect(closeMenu).toHaveBeenCalledTimes(1);
  });

  it('should call onChange with role menuitemradio as specified when clicked', async () => {
    const componentText = 'Enabled Item';
    const component = (
      <MenuListItemBase {...props} role="menuitemradio">
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    await user.click(screen.getByText(componentText));
    expect(setActiveDescendantId).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id, role: 'menuitemradio' });
    expect(closeMenu).toHaveBeenCalledTimes(1);
  });

  it('should have aria-disabled attribute with disabled prop', () => {
    const componentText = 'Disabled Item';
    const component = (
      <MenuListItemBase {...props} disabled>
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    expect(getMenuItem()).toHaveAttribute('aria-disabled', 'true');
  });

  it('should not call setActiveDescdantId when disabled and moused over', async () => {
    const componentText = 'Disabled Item';
    const component = (
      <MenuListItemBase {...props} disabled>
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    await user.hover(screen.getByText(componentText));
    expect(setActiveDescendantId).toHaveBeenCalledTimes(0);
  });

  it('should not call setActiveDescdantId, onChange, and closeMenu when disabled item is clicked', async () => {
    const componentText = 'Disabled Item';
    const component = (
      <MenuListItemBase {...props} disabled>
        {componentText}
      </MenuListItemBase>
    );
    render(component);
    await user.click(screen.getByText(componentText));
    expect(onChange).toHaveBeenCalledTimes(0);
    expect(setActiveDescendantId).toHaveBeenCalledTimes(0);
    expect(closeMenu).toHaveBeenCalledTimes(0);
  });

  it('should render divider with divider prop', () => {
    const componentText = 'Item';
    const component = (
      <MenuListItemBase {...props} divider>
        {componentText}
      </MenuListItemBase>
    );
    const { container } = render(component);
    expect(container.querySelector('hr')).toBeInTheDocument();
  });

  it('should not render divider without divider prop', () => {
    const componentText = 'Item';
    const component = <MenuListItemBase {...props}>{componentText}</MenuListItemBase>;
    const { container } = render(component);
    expect(container.querySelector('hr')).toBeNull();
  });

  describe('SubMenu', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    const headerText = 'header';
    const componentHeader = <MenuListItemHeader>{headerText}</MenuListItemHeader>;
    const component = (
      <MenuListItemBase {...props}>
        {componentHeader}
        <MenuListItemBase id="submenu" testId="submenu-test">
          submenu 1
        </MenuListItemBase>
        <MenuListItemBase id="submenu2" testId="submenu-test-2">
          submenu 2
        </MenuListItemBase>
      </MenuListItemBase>
    );
    it('should render header and submenu items should not be in document', () => {
      const { getByText } = render(component);
      expect(getByText('header')).toBeInTheDocument();
      expect(screen.queryByText('submenu 1')).not.toBeInTheDocument();
      expect(screen.queryByText('submenu 2')).not.toBeInTheDocument();
    });

    it('should display submenu items when user clicks on the heading', async () => {
      render(component);
      await user.click(screen.getByText(headerText));
      expect(screen.getByText('submenu 1')).toBeInTheDocument();
      expect(screen.getByText('submenu 2')).toBeInTheDocument();
    });
  });
});
