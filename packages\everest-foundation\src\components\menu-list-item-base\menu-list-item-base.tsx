import React, {
  PropsWithChildren,
  useContext,
  useEffect,
  useImperativeHandle,
  useState,
  ReactNode,
  Children,
  useRef,
} from 'react';
import classnames from 'classnames';

import { TAriaMenuItemRole } from '../../types';
import { mergeRefs, useCreateTestId } from '../../utils';
import { resolvePropsContext } from '../../utils/resolve-props-context';
import { Divider } from '../divider';
import { Icon } from '../icon';
import { MenuList, IMenuListContext, MenuListContext } from '../menu-list';
import { MenuListItemHeader } from '../menu-list-item-header';

import styles from './menu-list-item-base.module.scss';

export interface IMenuListItemExpandedLIElement extends HTMLLIElement {
  /**
   * Update state with Keyboard Navigation
   */
  updateExpandedState: () => void;
}

export interface IMenuListItemBase extends IMenuListContext {
  id: string;
  testId?: string;
  disabled?: boolean;
  children?: ReactNode;
  divider?: boolean;
  className?: string; // use for override sizing/padding
  closeMenu?: () => void; // contextualmenu context
  ref?: React.ForwardedRef<HTMLLIElement>;
  role?: TAriaMenuItemRole;
}

export const MenuListItemBase = React.forwardRef<IMenuListItemExpandedLIElement, PropsWithChildren<IMenuListItemBase>>(
  (props: PropsWithChildren<IMenuListItemBase>, ref): JSX.Element => {
    const [open, setOpen] = useState(false);
    const ulRef = useRef<HTMLUListElement>(null);
    const context = useContext(MenuListContext);
    const {
      id,
      testId,
      disabled = false,
      setActiveDescendantId,
      activeDescendantId,
      onChange = () => undefined,
      closeMenu,
      divider = false,
      children,
      checkedIds,
      role = 'menuitem',
      className,
    } = resolvePropsContext<IMenuListItemBase, IMenuListContext>(props, context);
    const testIdRef = useCreateTestId(testId);
    const isCheckedId = !!checkedIds?.includes(id);
    const isAriaChecked = role === 'menuitemcheckbox' || role === 'menuitemradio';
    const listItemChildren: ReactNode[] = [];
    let headingChild: ReactNode;

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    useImperativeHandle(ref, () => ({
      updateExpandedState: () => {
        setOpen(!open);
      },
    }));

    function handleHeadingClick(e: React.MouseEvent) {
      e.preventDefault();
      e.stopPropagation();
      if (disabled) return;
      setOpen(!open);
    }

    function handleOnClick(e: React.MouseEvent) {
      e.preventDefault();
      e.stopPropagation();
      if (disabled) return;
      onChange?.({ id: e.currentTarget.id, role });
      setActiveDescendantId?.(e.currentTarget.id);
      closeMenu?.();
    }

    function mouseHandling(e: React.MouseEvent) {
      e.preventDefault();
      e.stopPropagation();
      if (disabled) return;
      if (e.currentTarget.id !== activeDescendantId) {
        if (e.currentTarget.parentElement !== document.activeElement) {
          e.currentTarget.parentElement?.focus({ preventScroll: true });
        }
        setActiveDescendantId?.(e.currentTarget.id);
        if (open) {
          setActiveDescendantId?.('');
        }
      }
    }

    function handleOnMouseMove(e: React.MouseEvent) {
      mouseHandling(e);
    }

    function handleOnMouseOver(e: React.MouseEvent) {
      mouseHandling(e);
    }

    function handleKeyDown(e: React.KeyboardEvent) {
      // Close submenu and move focus back to parent menu item
      if ('Escape' === e.key) {
        if (e.currentTarget.parentElement) {
          e.stopPropagation();
          e.currentTarget.parentElement.focus({ preventScroll: true });
          setActiveDescendantId?.(e.currentTarget.id);
          setOpen(false);
        }
      }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    React.Children.forEach(children, (child: any) => {
      if (child?.type === MenuListItemHeader) {
        headingChild = child;
      } else {
        listItemChildren.push(child);
      }
    });

    useEffect(() => {
      if (open) {
        // set focus on the submenu items
        ulRef?.current?.focus();
      }
    }, [open]);

    const childrenRender = () => (
      <>
        {Children.map(children, (child) => {
          return typeof child === 'string' ? (
            <>
              <div
                className={classnames(isCheckedId ? 'evrBold' : '', [
                  styles.evrMenuListItemBaseText,
                  isCheckedId && id === activeDescendantId ? styles.hover : '',
                ])}
                role="none"
              >
                {child}
              </div>
              {isCheckedId && isAriaChecked && (
                <Icon
                  name="checkSmall"
                  fill={
                    // fill is a union of 62 strings, so this remains
                    // until we type fill
                    disabled
                      ? '--evr-inactive-content'
                      : id === activeDescendantId
                      ? '--evr-interactive-primary-hovered'
                      : '--evr-interactive-primary-default'
                  }
                />
              )}
            </>
          ) : (
            child
          );
        })}
      </>
    );

    const renderListItem = () => {
      if (headingChild) {
        return (
          /* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */
          <li
            id={id}
            key={id}
            className={classnames(styles.evrMenuListItemBase, styles.evrMenuListItemBaseExpanded, {
              [styles.hover]: !disabled && id === activeDescendantId,
              [styles.active]: !disabled && isCheckedId,
              [styles.disabled]: disabled,
            })}
            // eslint-disable-next-line jsx-a11y/no-noninteractive-element-to-interactive-role
            role={role}
            tabIndex={-1}
            onClick={handleHeadingClick}
            onKeyDown={handleKeyDown}
            onMouseOver={handleOnMouseOver}
            onMouseMove={handleOnMouseMove}
            onFocus={() => undefined} // satisfy linting
            aria-disabled={disabled}
            aria-checked={isAriaChecked ? isCheckedId : undefined}
            aria-expanded={open}
            ref={mergeRefs([ref, testIdRef])}
          >
            {/* Setting tabIndex to -1 to fix EDS-4585, sub-menu items not getting announced in Toolbar overflow menu */}
            <div tabIndex={-1} className={classnames(styles.evrMenuListItemBaseHeader, className, { evrBold: open })}>
              {headingChild}
              {open ? (
                <Icon
                  name="chevronUpSmall"
                  fill={!disabled ? '--evr-content-primary-default' : '--evr-inactive-content'}
                />
              ) : (
                <Icon
                  name="chevronDownSmall"
                  fill={!disabled ? '--evr-content-primary-default' : '--evr-inactive-content'}
                />
              )}
            </div>
            {open && (
              // TODO consider how we might build props and context
              // for submenus with 'toggleable' items that may or may not
              // close portions of the menu when selected
              <MenuList
                id={`${id}-menu-list`}
                onChange={onChange}
                ref={ulRef}
                checkedIds={checkedIds}
                closeMenu={closeMenu}
              >
                {listItemChildren}
              </MenuList>
            )}
          </li>
        );
      }
      return (
        /* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */
        <li
          id={id}
          key={id}
          className={classnames(
            styles.evrMenuListItemBase,
            {
              [styles.hover]: !disabled && id === activeDescendantId,
              [styles.active]: !disabled && isCheckedId,
              [styles.disabled]: disabled,
            },
            className
          )}
          // eslint-disable-next-line jsx-a11y/no-noninteractive-element-to-interactive-role
          role={role}
          tabIndex={-1}
          onClick={handleOnClick}
          onMouseOver={handleOnMouseOver}
          onMouseMove={handleOnMouseMove}
          onFocus={() => undefined} // satisfy linting
          aria-disabled={disabled}
          aria-checked={isAriaChecked ? isCheckedId : undefined}
          ref={mergeRefs([ref, testIdRef])}
        >
          {childrenRender()}
        </li>
      );
    };

    return (
      <>
        {renderListItem()}
        {divider && (
          <div aria-hidden className={styles.evrMenuListItemBaseDivider}>
            <Divider />
          </div>
        )}
      </>
    );
  }
);

MenuListItemBase.displayName = 'MenuListItemBase';
