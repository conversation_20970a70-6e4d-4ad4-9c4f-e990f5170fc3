/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { PropsWithChildren, ReactNode } from 'react';

import styles from './menu-list-item-header.module.scss';
export interface IMenuListItemHeader {
  children?: ReactNode;
}

export const MenuListItemHeader = React.forwardRef<HTMLLIElement, PropsWithChildren<IMenuListItemHeader>>(
  (props, ref) => {
    const { children } = props;

    return <div className={styles.evrMenuListItemHeaderText}>{children}</div>;
  }
);

MenuListItemHeader.displayName = 'MenuListItemHeader';
