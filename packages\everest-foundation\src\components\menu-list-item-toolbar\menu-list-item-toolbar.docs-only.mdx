import { Meta } from '@storybook/addon-docs';
import { LinkTo } from '../../../.storybook/docs/shared/link-to.tsx';

<Meta title="Toolbox/Menu List Item/Menu List Item Toolbar" />

# Menu List Item Toolbar

The `MenuListItemToolbar` is an enhanced variation of the `MenuListItemBase` component that retains all its core functionality
while incorporating additional style adjustments to achieve a specific visual appearance.

The `MenuListItemToolbar` is the component used as a child of `PopoverMenu` component inside the `Toolbar`.

For documentation and demo examples, please refer to the `MenuListItemBase` documentation.
This will provide you with detailed information on the inherited functionalities and how to implement them effectively in your project.

For API specification and examples, please refer to the <LinkTo kind="Toolbox/Menu List Item/Menu List Item">MenuListItem</LinkTo>.
