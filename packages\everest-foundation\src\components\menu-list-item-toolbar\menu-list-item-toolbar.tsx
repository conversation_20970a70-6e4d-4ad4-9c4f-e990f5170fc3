import React, { PropsWithChildren } from 'react';
import classNames from 'classnames';

import { IMenuListContext } from '../menu-list';
import { IMenuListItemBase, IMenuListItemExpandedLIElement, MenuListItemBase } from '../menu-list-item-base';

import styles from './menu-list-item-toolbar.module.scss';

export type IMenuListItemToolbar = Omit<IMenuListItemBase & IMenuListContext, 'classname'>;

// INTERNAL USE ONLY. DO NOT EXPORT THIS. It's only needed to match the Toolbar's Popover Menu list item styles
export const MenuListItemToolbar = React.forwardRef<
  IMenuListItemExpandedLIElement,
  PropsWithChildren<IMenuListItemToolbar>
>((props: PropsWithChildren<IMenuListItemToolbar>, ref): JSX.Element => {
  return (
    <MenuListItemBase
      {...props}
      ref={ref}
      className={classNames(styles.toolbarMenuListItemOverrides, {
        [styles.toolbarMenuListItemColorOverrides]: !props.disabled,
      })}
    >
      {props.children}
    </MenuListItemBase>
  );
});

MenuListItemToolbar.displayName = 'MenuListItemToolbar';
