import { CodeExample } from '../../../.storybook/doc-blocks/example/example.tsx';
import { LinkTo } from '../../../.storybook/docs/shared/link-to.tsx';
import { MenuListItem } from './menu-list-item';
import { MenuList } from '../menu-list';
import { MenuListItemHeader } from '../menu-list-item-header';
import { Button } from '../button';

export const scope = { MenuList, MenuListItem, MenuListItemHeader, Button };

# Menu List Item

The `MenuListItem` is the default component that should be used as a child of `MenuList` component.

It is an enhanced variation of the `MenuListItemBase` component that retains all its core functionality
while incorporating additional style adjustments to achieve a specific visual appearance.

### Disabled State

The `MenuListItem` can be disabled with the `disabled` prop, and visual groups of menu items can be distguished with a `divider`.

export const defaultCode = `() => {
  const [selectedMenuItemId, setSelectedMenuItemId] = React.useState('');
  return (
    <div style={{ width: '100%', maxWidth: '10rem' }}>
      <MenuList id="my-user-menu" ariaLabel="User Menu" onChange={({ id }) => setSelectedMenuItemId(id)}>
        <MenuListItem id="user-item-profile">Profile</MenuListItem>
        <MenuListItem id="user-item-prefs" divider>
          Preferences
        </MenuListItem>
        <MenuListItem id="user-item-logout" disabled>
          Logout
        </MenuListItem>
      </MenuList>
    </div>
  );
}`;

<CodeExample scope={scope} code={defaultCode} />

### SubMenu MenuListItem

`MenuListItem` allows users to attach Sublists within an item. Users must provide a `Ref` to the `MenuListItem` to ensure keyboard navigation works as expected.

Users also need to make the sure all SubMenu header related content must be inside `MenuListItemHeader`.

The `MenuListItem` can be still be disabled with the `disabled` prop, and visual groups of menu items can be distguished with a `divider` if needed.

export const submenuCode = `() => {
  const [selectedMenuItemId, setSelectedMenuItemId] = React.useState('');
  const subMenuRef = React.useRef(null);
  return (
    <div style={{ width: '100%', maxWidth: '10rem' }}>
      <MenuList id="my-user-menu" ariaLabel="User Menu" onChange={({ id }) => setSelectedMenuItemId(id)}>
        <MenuListItem id="user-item-profile">Profile</MenuListItem>
        <MenuListItem id="user-item-prefs" divider>
          Preferences
        </MenuListItem>
        <MenuListItem id="user-item-submenu" ref={subMenuRef}>
          <MenuListItemHeader>SubMenu</MenuListItemHeader>
          <MenuListItem id="sub-list-item-1">Sub item 1</MenuListItem>
          <MenuListItem id="sub-list-item-2">Sub item 2</MenuListItem>
        </MenuListItem>
        <MenuListItem id="user-item-logout" disabled>
          Logout
        </MenuListItem>
      </MenuList>
    </div>
  );
}`;

<CodeExample scope={scope} code={submenuCode} />

## Menu item with checked selection

The prop `checkedIds` on the <LinkTo kind="Toolbox/Menu List">MenuList</LinkTo> specifies when a menu item should stay checked. Set the individual menu item to have role `menuitemcheckbox` when multiple items may be checked or `menuitemradio` when only a single item may be checked. See the example below.

export const checkedExample = `() => {
  const [lastSelectedId, setLastSelectedId] = React.useState('');
  const [checkedIds, setCheckedIds] = React.useState(['profile-item']);
  return (
    <div style={{ width: '100%', maxWidth: '10rem' }}>
      <MenuList
        id="user-menu"
        ariaLabel="User Menu"
        onChange={({ id, role }) => {
          // setLastSelectedId is not required
          setLastSelectedId(id);
          if (role === 'menuitemcheckbox') {
            if (!checkedIds.includes(id)) {
              setCheckedIds([id]);
            } else {
              setCheckedIds([]);
            }
          }
        }}
        checkedIds={checkedIds}
      >
        <MenuListItem id="profile-item" role="menuitemcheckbox">
          Profile
        </MenuListItem>
      </MenuList>
    </div>
  );
}`;

<CodeExample scope={scope} code={checkedExample} />

## Accessing MenuListItem using ref

Click on the Button to access the MenuListItem, refer to the console for the element details.

export const refCode = `() => {
  const styles = {
    row: {
      display: 'flex',
      justifyContent: 'space-around',
      flexWrap: 'wrap',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      rowGap: '0.625rem',
    },
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  const ref = React.useRef(null);
  return (
    <Column>
      <Row>
        <Button
          id="access-element-btn"
          label="Click to access element"
          onClick={() => {
            console.log(ref.current);
          }}
        />
      </Row>
      <Row>
        <div style={{ width: '100%', maxWidth: '10rem' }}>
          <MenuListItem ref={ref} id="profile-item">
            Profile
          </MenuListItem>
        </div>
      </Row>
    </Column>
  );
}`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Menu items should have succinct descriptions and are limited to two lines of text.

## Accessibility

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
