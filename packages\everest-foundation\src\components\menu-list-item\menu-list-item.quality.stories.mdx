import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { MenuListItem } from './menu-list-item';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Menu List Item"
  component={MenuListItem}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'menu-item',
    testId: 'menu-item',
  }}
  decorators={[
    (Story) => (
      <ul
        role="menu"
        style={{
          WebkitAppearance: 'none',
          MozAppearance: 'none',
          appearance: 'none',
          listStyleType: 'none',
          margin: 0,
          padding: 0,
        }}
      >
        <Story />
      </ul>
    ),
  ]}
/>

# Menu List Item

## Live Demo

<Canvas>
  <Story name="Default">{(args) => <MenuListItem {...args}>Default</MenuListItem>}</Story>
</Canvas>

<Canvas>
  <Story name="Default with aria-activedescendant">
    {(args) => (
      <MenuListItem {...args} activeDescendantId="menuitem">
        Default with aria-activedescendant
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default with checked id and role of menuitemcheckbox">
    {(args) => (
      <MenuListItem {...args} checkedIds={[args.id]} role="menuitemcheckbox">
        Default with checked id and role of menuitemcheckbox
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default with checked id and role of menuitemradio">
    {(args) => (
      <MenuListItem {...args} checkedIds={[args.id]} role="menuitemradio">
        Default with checked id and role of menuitemradio
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled with checked id">
    {(args) => (
      <MenuListItem {...args} checkedIds={[args.id]} disabled role="menuitemcheckbox">
        Disabled with checked id
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Divider">
    {(args) => (
      <MenuListItem {...args} divider>
        Divider
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Divider and aria-activedescendant">
    {(args) => (
      <MenuListItem {...args} activeDescendantId="menuitem" divider>
        Divider and aria-activedescendant
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled">
    {(args) => (
      <MenuListItem {...args} disabled>
        Disabled
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled with divider">
    {(args) => (
      <MenuListItem {...args} disabled divider>
        Disabled with Divider
      </MenuListItem>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Multiline text">
    {(args) => (
      <MenuListItem {...args}>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus eget cursus magna. Ut auctor venenatis
        facilisis. Quisque porta elit et libero rhoncus, non sodales sapien congue. Aliquam ac augue tincidunt, egestas
        mi et, hendrerit nunc. Nulla facilisi. Orci varius natoque penatibus et magnis dis parturient montes, nascetur
        ridiculus mus. Morbi rhoncus tellus elit, in vehicula mi bibendum in. Pellentesque habitant morbi tristique
        senectus et netus et malesuada fames ac turpis egestas. Morbi ornare dui ut purus gravida, at congue neque
        facilisis. Pellentesque sagittis in velit sit amet viverra. Fusce ultrices, est mollis convallis imperdiet, nibh
        eros consectetur lacus, in aliquet nisl ex eget ex. Vivamus in vehicula massa, a aliquam lorem.
      </MenuListItem>
    )}
  </Story>
</Canvas>
