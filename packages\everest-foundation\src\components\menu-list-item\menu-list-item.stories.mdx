import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { MenuList } from '../menu-list';
import { MenuListItem } from './menu-list-item';
import { useState } from 'react';
import Examples from './menu-list-item.examples.mdx';

<Meta
  title="Toolbox/Menu List Item/Menu List Item"
  component={MenuListItem}
  parameters={{
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/C5r0pQ9nTQiump044rnI8s/%F0%9F%A7%AAMenus?node-id=2%3A349',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Id for the `PopoverMenuItem`.',
    },
    disabled: {
      description: 'Disable the button and menu.',
      table: {
        defaultValue: { summary: false },
      },
    },
    divider: {
      description: 'Displays a horizontal divider at the bottom of the menu item.',
      table: {
        defaultValue: { summary: false },
      },
    },
    testId: {
      control: 'text',
      description: 'Sets `data-testid` attribute on the html element.',
    },
    role: {
      control: 'radio',
      description: 'Set the role of the menu item.',
    },
    activeDescendantId: {
      table: { disable: true },
    },
    setActiveDescendantId: {
      table: { disable: true },
    },
    closeMenu: {
      table: { disable: true },
    },
    persistSelected: {
      table: { disable: true },
    },
    selectedId: {
      table: { disable: true },
    },
    onChange: {
      table: { disable: true },
    },
    checkedIds: {
      table: { disable: true },
    },
  }}
  args={{
    id: 'planes-item',
    testId: 'planes-item',
    disabled: false,
    divider: false,
    role: 'menuitem',
  }}
/>

<Examples />

## Live Demo

<Canvas>
  <Story name="Menu List Item">
    {(args) => {
      const [lastSelectedId, setLastSelectedId] = React.useState('');
      const [checkedIds, setCheckedIds] = React.useState([]);
      return (
        <MenuList
          id="user-menu"
          ariaLabel="User Menu"
          onChange={({ id, role }) => {
            // setLastSelectedId is not required
            setLastSelectedId(id);
            if (role === 'menuitemcheckbox') {
              if (!checkedIds.includes(id)) {
                setCheckedIds([id]);
              } else {
                setCheckedIds([]);
              }
            }
          }}
          checkedIds={checkedIds}
        >
          <MenuListItem
            id={args.id}
            testId={args.testId}
            disabled={args.disabled}
            divider={args.divider}
            role={args.role}
          >
            Profile
          </MenuListItem>
        </MenuList>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Menu List Item" />
