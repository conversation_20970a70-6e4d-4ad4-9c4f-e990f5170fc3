import React, { PropsWithChildren } from 'react';

import { IMenuListContext } from '../menu-list';
import { IMenuListItemBase, IMenuListItemExpandedLIElement, MenuListItemBase } from '../menu-list-item-base';

export type IMenuListItem = Omit<IMenuListItemBase & IMenuListContext, 'classname'>;

export const MenuListItem = React.forwardRef<IMenuListItemExpandedLIElement, PropsWithChildren<IMenuListItem>>(
  (props: PropsWithChildren<IMenuListItem>, ref): JSX.Element => {
    return (
      <MenuListItemBase {...props} ref={ref}>
        {props.children}
      </MenuListItemBase>
    );
  }
);

MenuListItem.displayName = 'MenuListItem';
