import React, { SetStateAction, Dispatch } from 'react';

import { TAriaMenuItemRole } from '../../types';

export type TMenuListItemValue = {
  id: string;
  role: TAriaMenuItemRole;
};
export interface IMenuListContext {
  setActiveDescendantId?: Dispatch<SetStateAction<string>>;
  activeDescendantId?: string;
  onChange?: (value: TMenuListItemValue) => void;
  closeMenu?: () => void;
  checkedIds?: string[];
}

const defaultContext = {
  setActiveDescendantId: () => undefined,
  activeDescendantId: '',
  onChange: () => undefined,
  closeMenu: () => undefined,
  checkedIds: [],
};

// esLint doesn't like this context object capitalized -- ignoring rule
// eslint-disable-next-line @typescript-eslint/naming-convention
export const MenuListContext = React.createContext<IMenuListContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  MenuListContext.displayName = 'MenuListContext';
}
