import React, { ReactChild, ReactFragment, ReactPortal, ReactElement, ReactNode, Children, KeyboardEvent } from 'react';

import { TMenuListItemValue } from './menu-list-context';
import { TAriaMenuItemRole } from '../../types';
import { IMenuListItemExpandedLIElement } from '../menu-list-item-base';

import MenuListItemStyles from '../menu-list-item-base/menu-list-item-base.module.scss';

// type for object used to collect first characters of strings from contexual menu items
type TFirstChars = { firstChar: string; disabled: boolean };

// javascript modulo requies some adjustment
// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Remainder
function modulo(numberOne: number, numberTwo: number) {
  return ((numberOne % numberTwo) + numberTwo) % numberTwo;
}

function setSoftSelectIndexFromPressedDirectionKey(
  e: KeyboardEvent,
  softSelectedIndex: number,
  childArrayLength: number
) {
  let index = -1;
  if (e.key === 'ArrowUp') {
    if (softSelectedIndex === -1) index = childArrayLength - 1;
    else index = softSelectedIndex - 1;
    return index;
  }
  if (e.key === 'ArrowDown') {
    if (softSelectedIndex === -1) index = 0;
    else index = softSelectedIndex + 1;
    return index;
  }
  if (e.key === 'Home') {
    index = 0;
    return index;
  }
  if (e.key === 'End') {
    index = childArrayLength - 1;
  }
  return index;
}

export function findEnabledMenuItem(
  key: string,
  index: number,
  childrenArray: (ReactChild | ReactFragment | ReactPortal)[]
): string {
  let newIndex = index;
  let activeDescendantId = (childrenArray[newIndex] as ReactElement).props.id as string;
  let counter = 0;
  while ((childrenArray[newIndex] as ReactElement).props.disabled) {
    let i = 0;
    if (key === 'ArrowUp' || key === 'End') i = -1;
    if (key === 'ArrowDown' || key === 'Home') i = 1;
    newIndex = modulo(newIndex + i, childrenArray.length);
    activeDescendantId = (childrenArray[newIndex] as ReactElement).props.id as string;
    counter++;
    if (counter === childrenArray.length) return '';
  }
  return activeDescendantId;
}

function setChildrenFirstCharactersArray(
  childrenArray: (ReactChild | ReactFragment | ReactPortal)[],
  firsCharacters: TFirstChars[]
) {
  // childrenArray is the collection of contextual list items
  for (let i = 0; i < childrenArray.length; i++) {
    const reactProps = (childrenArray[i] as ReactElement).props;
    const propsChildren = reactProps.children;
    // when the child is a string only
    if (typeof propsChildren === 'string' && propsChildren.length > 0) {
      firsCharacters.push({
        firstChar: propsChildren[0].toLowerCase(),
        disabled: !!reactProps.disabled,
      });
      continue;
    }
    // when there is an array of children, use the first string found to search by
    if (Array.isArray(propsChildren)) {
      for (let j = 0; j < propsChildren.length; j++) {
        if (propsChildren[j] && typeof propsChildren[j] === 'string') {
          firsCharacters.push({
            firstChar: propsChildren[j][0].toLowerCase(),
            disabled: !!reactProps.disabled,
          });
          break;
        }
      }
    }
    // in any other case push a placeholder
    else {
      firsCharacters.push({
        firstChar: '',
        disabled: true,
      });
    }
  }
}

function getSoftSelectIndexForFirstChar(
  e: KeyboardEvent,
  firstChars: TFirstChars[],
  softSelectedIndex: number,
  numberOfMenuItems: number
) {
  if (numberOfMenuItems === 0) return -1;
  const firstIndex = firstChars
    .reduce((prev, curr) => {
      prev.push(curr.firstChar);
      return prev;
    }, [] as string[])
    .indexOf(e.key.toLowerCase());
  if (firstIndex === -1) return -1;
  if (numberOfMenuItems === 1 && firstIndex === 0 && !firstChars[0].disabled) {
    return firstIndex;
  }
  const startIndex = softSelectedIndex === -1 ? 0 : modulo(softSelectedIndex + 1, numberOfMenuItems);
  let k = startIndex;
  while (k > -1) {
    if (e.key.toLowerCase() === firstChars[k].firstChar.toLowerCase() && !firstChars[k].disabled) {
      return k;
    }
    k = modulo(k + 1, numberOfMenuItems);
    if (k === startIndex) break;
  }
  return -1;
}

export function handleOnKeyDown(
  e: KeyboardEvent,
  children: ReactNode,
  setActiveDescendantId: (value: React.SetStateAction<string>) => void,
  onChange: (value: TMenuListItemValue) => void,
  softSelectedIndex: number,
  closeMenu?: () => void
): void {
  if ('Tab' === e.key || 'Escape' === e.key) {
    const parent = document.getElementById((e.target as HTMLElement).id)?.parentNode as HTMLElement;
    // Don't close entire menu when pressing escape on submenu
    if (parent?.hasAttribute('aria-expanded') && 'Escape' === e.key) {
      return;
    }
    e.stopPropagation();
    closeMenu?.();
    return;
  }
  if (!/^[a-zA-Z ]/g.test(e.key)) return;
  e.preventDefault();
  e.stopPropagation();
  const childrenArray = Children.toArray(children);
  if (['Enter', ' '].some((value) => value === e.key)) {
    if (softSelectedIndex === -1) return;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const child = childrenArray[softSelectedIndex] as ReactElement | any;
    const selectedId = child.props.id as string;
    setActiveDescendantId(selectedId);
    let role: TAriaMenuItemRole | null = null;
    role = (document.getElementById(selectedId)?.getAttribute('role') as TAriaMenuItemRole | null) ?? 'menuitem';

    if (document.getElementById(selectedId)?.hasAttribute('aria-expanded') && child.ref && child.ref.current) {
      (child.ref.current as IMenuListItemExpandedLIElement).updateExpandedState();
      setActiveDescendantId('');
    } else {
      onChange({ id: selectedId, role });
      document.getElementById(selectedId)?.classList.add(MenuListItemStyles.active);
      closeMenu?.();
    }
    return;
  }
  let itemIndex = -1;
  const childArrayLength = childrenArray.length;
  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].some((value) => value === e.key)) {
    itemIndex = setSoftSelectIndexFromPressedDirectionKey(e, softSelectedIndex, childArrayLength);
  } else if (/[a-zA-Z]/.test(e.key)) {
    const firstChars: TFirstChars[] = [];
    setChildrenFirstCharactersArray(childrenArray, firstChars);
    itemIndex = getSoftSelectIndexForFirstChar(e, firstChars, softSelectedIndex, childArrayLength);
    if (itemIndex === -1) return;
  }
  const itemIndexModLength = modulo(itemIndex, childArrayLength);
  const activeDescendantId = findEnabledMenuItem(e.key, itemIndexModLength, childrenArray);
  setActiveDescendantId(activeDescendantId);
}
