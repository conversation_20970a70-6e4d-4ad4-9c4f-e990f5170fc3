import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { MenuList } from './menu-list';
import { MenuListItem } from '../menu-list-item';
import { Button } from '../button';
import { AlphaBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';

export const scope = { MenuList, MenuListItem, Button };

`MenuList` is a menu with an actions list. It was developed for use in the <LinkTo kind="Components/Popover Menu">PopoverMenu</LinkTo>, but can be used standalone.

The intent is to use `MenuList` in a component that follows the <a href="https://www.w3.org/WAI/ARIA/apg/example-index/menu-button/menu-button-actions-active-descendant.html" target="_blank" rel="noreferrer">menu button with active descendant</a> pattern.

## Variations

### Default MenuList

The default presentation for the `MenuList` consists of a menu, and the <LinkTo kind='Toolbox/Menu List Item/Menu List Item'>MenuListItems</LinkTo>.

The `MenuList` is a controlled component, and the choice of menu item id is held in state.

export const defaultCode = `
  () => {
    const [lastSelectedId, setLastSelectedId] = React.useState('');
    return (
      <div style={{width: '100%', maxWidth: '10rem'}}>
        <MenuList
          id="my-user-menu"
          ariaLabel="User Menu"
          onChange={({id}) => setLastSelectedId(id) }                    
        >
          <MenuListItem id="profile-item">Profile</MenuListItem>
          <MenuListItem id="prefs-item" divider>Preferences</MenuListItem>
          <MenuListItem id="logout-item">Logout</MenuListItem>
        </MenuList>
      </div>
    );
  }
`;

<CodeExample scope={scope} code={defaultCode} />

## MenuList with checked selection

Use the `checkedIds` prop when items should remain checked. Set the individual menu item to have role `menuitemcheckbox` when multiple items may be checked or `menuitemradio` when only a single item may be checked.

export const persistSelectedCode = `
  () => {
    const [lastSelectedId, setLastSelectedId] = React.useState('');
    const [checkedIds, setCheckedIds] = React.useState(['show-word-wrap']);
    return (
      <div style={{width: '100%', maxWidth: '12rem'}}>
        <MenuList
          id="view-menu"
          ariaLabel="View Menu"          
          onChange={({id,role}) => {
            // setLastSelectedId is not required
            setLastSelectedId(id);
            if(role === "menuitemcheckbox") {
              if (checkedIds.includes(id)) {
                const index = checkedIds.indexOf(id);
                const checkedIdsCopy = [...checkedIds];
                checkedIdsCopy.splice(index, 1);
                setCheckedIds(checkedIdsCopy);
                return;
              }            
              setCheckedIds((prev) => [...prev, id]);
            }
          }}           
          checkedIds={checkedIds}
        >
          <MenuListItem id="show-word-wrap" role="menuitemcheckbox">Word Wrap</MenuListItem>
          <MenuListItem id="show-mini-map" role="menuitemcheckbox">Show Minimap</MenuListItem>
          <MenuListItem id="show-breadcrumbs" disabled>Show Breadcrumbs</MenuListItem>
        </MenuList>
      </div>
    );
  }
`;

<CodeExample scope={scope} code={persistSelectedCode} />

## Accessing MenuList using ref

Click on the Button to access the MenuList, refer to the console for the element details.

export const refCode = `()=>{
     const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
         column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '10px'
        }
    }
     const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    const [selectedId, setSelectedId] = React.useState('');
    return (
      <Column>
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
        </Row>
        <Row>
            <div style={{width: '100%', maxWidth: '10rem'}}>
              <MenuList
                ref={ref}
                id="my-user-menu"
                ariaLabel="User Menu"
                onChange={({id}) => setSelectedId(id)}
              >
                <MenuListItem id="profile-item">Profile</MenuListItem>
                <MenuListItem id="prefs-item" divider>Preferences</MenuListItem>
                <MenuListItem id="logout-item">Logout</MenuListItem>
              </MenuList>
          </div>
        </Row>
      </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

`MenuList` was developed for use in the <LinkTo kind="Components/Popover Menu">PopoverMenu</LinkTo>, but can be used standalone.

By default the `MenuList` is not tab focusable as per the <a href="https://www.w3.org/WAI/ARIA/apg/example-index/menu-button/menu-button-actions-active-descendant.html" target="_blank" rel="noreferrer">menu button with active descendant</a> pattern. Nevertheless it can be made tab focusable by passing the `tabFocusable` prop.

To access the `MenuList`'s underlying `ul` HTML element, pass a ref to the `ref` prop. This could be useful if you want manually focus the menu.

The default and suggested maximum number of menu items is `7`. This can be adjusted with the `maxItems` prop on `MenuList`.

Related menu items can be visually grouped using the divider prop on the <LinkTo kind='Toolbox/Menu List Item/Menu List Item'>MenuListItem</LinkTo> component.

## Accessibility

The intent is to use `MenuList` in a component that follows the <a href="https://www.w3.org/WAI/ARIA/apg/example-index/menu-button/menu-button-actions-active-descendant.html" target="_blank" rel="noreferrer">menu button with active descendant</a> pattern.

When used standalone the `MenuList` should be provided an `ariaLabel`. If it is labelled by another element, then the `ariaLabelledBy` prop should be set to the label's id.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

### Keys supported

- Use <kbd>Down Arrow</kbd> / <kbd>Up Arrow</kbd> to navigate up and down the menu.
- Use <kbd>Home</kbd> to jump to the first menu item.
- Use <kbd>End</kbd> to jump to the last menu item.
- Use <kbd>Enter</kbd> / <kbd>Space</kbd> to select a menu item.

Jump to a menu item by pressing the first letter of a menu item's text.
