# Menu List

## Summary

Research and document implementations for the Everest Menu List.

- Start Date: 2022-05-31
- Figma link: N/A

## Detailed Design

`MenuList` is a menu following the pattern outlined at https://www.w3.org/WAI/ARIA/apg/example-index/menu-button/menu-button-actions-active-descendant.html. Although it can be used standalone, it was developed to facilitate the `PopoverMenu`.

## API

1. **id**: `string`  
   Id placed on the `ul` element.
1. **onChange**: `(value: string) => void`  
   Callback that runs when a menu selection is made and should update React state with the `MenuListItem` id.
1. **ariaLabel**: `string | undefined`  
   Optional. Label for the `MenuList` when standalone.
1. **ariaLabelledBy**: `string | undefined`  
   Optional. Id of the label element when not standalone.
1. **closeMenu**: `() => void | undefined`  
   Optional. Callback used to close the menu.
1. **initialPosition**: `TMenuListInitialPosition | undefined`  
   Optional. Specifies the default `MenuListItem` when opened. It is either `start` or `end`. Default is `start`.
1. **maxItems**: `number | undefined`  
   Optional. Maximum number of menu items. Default is `7`.
1. **selectedId**: `string | undefined`  
   Optional. Specifies the last selected `MenuListItem` id. When specified the last chosen menu id remains highlighted after it's been pressed.
1. **tabFocusable**: `boolean | undefined`  
   Optional. Indicates if the menu can receive tab focus. Default is `false`.
1. **testId**: `string | undefined`  
   Optional. Sets data-testid attribute on the html elements.

The `ref` prop can be used to manually focus the menu as needed.

## Usage

A simple example is as follows.

```
   const [selectedId, setSelectedId] = React.useState('');
   return (
      <MenuList
         id="my-menu-list"
         ariaLabel="Menu List"
         onChange={(value) => setSelectedId(value)}
      >
         <MenuListItem id="planes-item">Planes</MenuListItem>
         <MenuListItem id="trains-item">Trains</MenuListItem>
         <MenuListItem id="cats-item">Cats</MenuListItem>
         <MenuListItem id="dogs-item">Dogs</MenuListItem>
      </MenuList>
   );
```

This example demonstrates the case where the selected menu item styling is persisted after it has been chosen. The default highlighted menu item when the menu is opened is the `cats-item` (not `dogs-item` since it is disabled).

```
   const [selectedId, setSelectedId] = React.useState('');
   return (
      <MenuList
         id="my-menu-list"
         ariaLabel="Menu List"
         onChange={(value) => setSelectedId(value)}
         selectedId={selectedId}
         persistSelected
         initialPosition={'end'}
      >
         <MenuListItem id="planes-item">Planes</MenuListItem>
         <MenuListItem id="trains-item">Trains</MenuListItem>
         <MenuListItem id="cats-item">Cats</MenuListItem>
         <MenuListItem id="dogs-item" disabled>Dogs</MenuListItem>
      </MenuList>
   );
```

## Accessibility

Follows the `aria-activedescendant` pattern specified at https://www.w3.org/WAI/ARIA/apg/example-index/menu-button/menu-button-actions-active-descendant.html.

## Other Design Systems

Most design systems use some variation of a `Menu` or `MenuList` with varying levels of responsibility.

**Material UI**

- https://mui.com/material-ui/api/menu-list/
- can be auto-focused or have be manually focused

**Spectrum**

- https://react-spectrum.adobe.com/react-spectrum/Menu.html
- pattern is similar to the implementation of the `PopoverMenu`

**Polaris**

- https://polaris.shopify.com/components/actions/action-list#navigation
- uses arrays of data to generate menu items rather than a compositional pattern

## Required PBIs

- Develop component, tests and story
- Refactor `ContextualMenu` and `PopoverMenu` to take advantage of the `MenuList`. Re-work all tests.
