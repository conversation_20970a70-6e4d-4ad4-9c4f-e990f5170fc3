import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { useState } from 'react';
import { MenuList } from './menu-list';
import { MenuListItem } from '../menu-list-item';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Menu List"
  component={MenuList}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'menu-list',
    testId: 'menu-list',
    ariaLabel: 'Menu List',
  }}
/>

# Menu List

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => {
      const [selectedMenuItemId, setSelectedMenuItemId] = useState('');
      return (
        <MenuList {...args} onChange={({ id }) => setSelectedMenuItemId(id)}>
          <MenuListItem id="planes-item" testId="planes-item">
            Planes
          </MenuListItem>
          <MenuListItem id="trains-item" testId="trains-item">
            Trains
          </MenuListItem>
          <MenuListItem id="autos-item" testId="autos-item" disabled>
            Automobiles
          </MenuListItem>
          <MenuListItem id="autogyros-item" testId="autogyros-item">
            Autogyros
          </MenuListItem>
          <MenuListItem id="tugboats-item" testId="tugboats-item">
            Tugboats
          </MenuListItem>
        </MenuList>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default with checked id">
    {() => {
      const [checkedIds, setCheckedIds] = useState(['planes-item-selected-id', 'autogyros-item-selected-id']);
      return (
        <MenuList
          id="menu-list-selected-id"
          testId="menu-list-selected-id"
          ariaLabel="Menu List with checked id"
          onChange={({ id }) => setCheckedIds([id])}
          checkedIds={checkedIds}
        >
          <MenuListItem id="planes-item-selected-id" testId="planes-item-selected-id" role="menuitemcheckbox">
            Planes
          </MenuListItem>
          <MenuListItem id="trains-item-selected-id" testId="trains-item-selected-id" role="menuitemcheckbox">
            Trains
          </MenuListItem>
          <MenuListItem id="autos-item-selected-id" testId="autos-item-selected-id" disabled role="menuitemcheckbox">
            Automobiles
          </MenuListItem>
          <MenuListItem id="autogyros-item-selected-id" testId="autogyros-item-selected-id" role="menuitemcheckbox">
            Autogyros
          </MenuListItem>
          <MenuListItem id="tugboats-item-selected-id" testId="tugboats-item-selected-id" role="menuitemcheckbox">
            Tugboats
          </MenuListItem>
        </MenuList>
      );
    }}
  </Story>
</Canvas>
