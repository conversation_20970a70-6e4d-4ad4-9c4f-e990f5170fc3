import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { MenuList } from './menu-list';
import { MenuListItem } from '../menu-list-item';

const id = 'menu-list';
const testId = 'menu-list';
const onChange = jest.fn();
const closeMenu = jest.fn();
const ariaLabelledBy = '';
const ariaLabel = 'Menu List';

const propsMock = {
  id,
  testId,
  onChange,
  closeMenu,
  ariaLabelledBy,
  ariaLabel,
};

const getMenuList = () => screen.getByRole('menu');

describe('[MenuList]', () => {
  beforeEach(() => {
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should render the menu and menu items without issue', () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toBeInTheDocument();
    expect(screen.getByText('Planes')).toBeInTheDocument();
    expect(screen.getByText('Trains')).toBeInTheDocument();
  });

  it('should initialize with aria-activedescendant id set to first menu item', () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
  });

  it('should initialize with aria-activedescendant id set to second contextual menu item when first menu item is disabled', () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item" disabled>
          Planes
        </MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'trains-item');
  });

  it('should initialize with aria-activedescendant id set to second contextual menu item when the initial position is "end"', () => {
    render(
      <MenuList {...propsMock} initialPosition="end">
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'trains-item');
  });

  it('should initialize with aria-activedescendant id set to first contextual menu item when the initial position is "end" and the last menu item is disabled', () => {
    render(
      <MenuList {...propsMock} initialPosition="end">
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" disabled>
          Trains
        </MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
  });

  it('should change aria-activedescendant id and not call onChange when mousing over menu item', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" testId="trains-item">
          Trains
        </MenuListItem>
      </MenuList>
    );

    await userEvent.hover(screen.getByTestId('trains-item'));
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'trains-item');
    expect(onChange).toHaveBeenCalledTimes(0);
  });

  it('should not change aria-activedescendant id and not call onChange when mousing over disabled menu item', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" testId="trains-item" disabled>
          Trains
        </MenuListItem>
      </MenuList>
    );

    await userEvent.hover(screen.getByTestId('trains-item'));
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
    expect(onChange).toHaveBeenCalledTimes(0);
  });

  it('should focus the menu when moused over', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item" testId="planes-item">
          Planes
        </MenuListItem>
        <MenuListItem id="trains-item" testId="trains-item">
          Trains
        </MenuListItem>
      </MenuList>
    );

    await userEvent.hover(screen.getByTestId('trains-item'));
    expect(getMenuList()).toHaveFocus();
  });

  it('should set aria-activedescendant to the empty string when the mouse leaves menu list ', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    fireEvent.mouseOut(getMenuList());
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', '');
  });

  it('should call onChange and closeMenu when menu item is clicked', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" testId="trains-item">
          Trains
        </MenuListItem>
      </MenuList>
    );

    await userEvent.click(screen.getByTestId('trains-item'));
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id: 'trains-item', role: 'menuitem' });
    expect(closeMenu).toHaveBeenCalledTimes(1);
  });

  // keyboard

  it('should change aria-activedescendant by pressing arrow keys', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('{ArrowDown}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'trains-item');
    await userEvent.keyboard('{ArrowDown}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'autos-item');
    await userEvent.keyboard('{ArrowUp}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'trains-item');
    await userEvent.keyboard('{ArrowUp}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
  });

  it('should change aria-activedescendant by pressing arrow keys and loop to top or bottom as appropriate', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('{ArrowUp}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'autos-item');
    await userEvent.keyboard('{ArrowDown}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
  });

  it('should change the selected menu item by pressing enter or space keys', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('{arrowdown}');
    await userEvent.keyboard('{enter}');
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id: 'trains-item', role: 'menuitem' });

    onChange.mockClear();

    await userEvent.keyboard('{arrowup}');
    await userEvent.keyboard(' ');
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id: 'planes-item', role: 'menuitem' });
  });

  it('should skip disabled menu items when pressing arrow keys', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" disabled>
          Trains
        </MenuListItem>
        <MenuListItem id="boats-item" disabled>
          Boats
        </MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('{arrowdown}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'autos-item');
  });

  it('should have aria-activedescendant equal to empty string if all menu items are disabled and arrow keys are pressed', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item" disabled>
          Planes
        </MenuListItem>
        <MenuListItem id="trains-item" disabled>
          Trains
        </MenuListItem>
        <MenuListItem id="autos-item" disabled>
          Autos
        </MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('{arrowdown}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', '');
    await userEvent.keyboard('{arrowup}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', '');
  });

  it('should set aria-activedescendant to first menu item when Home key is pressed', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" testId="trains-item">
          Trains
        </MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.hover(screen.getByTestId('trains-item'));
    await userEvent.keyboard('{home}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
  });

  it('should should set aria-activedescendant to last menu item when End key is pressed', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" testId="trains-item">
          Trains
        </MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.hover(screen.getByTestId('trains-item'));
    await userEvent.keyboard('{end}');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'autos-item');
  });

  it('should search for the first item in menu with first letter matching letter key pressed', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item" testId="trains-test">
          Trains
        </MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('a');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'autos-item');
    await userEvent.keyboard('p');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
  });

  it('should cycle through items in list whose first letter matches the key pressed', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="trains-item">Trains</MenuListItem>
        <MenuListItem id="automobiles-item">Automobiles</MenuListItem>
        <MenuListItem id="autos-item">Autos</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('a');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'automobiles-item');
    await userEvent.keyboard('a');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'autos-item');
  });

  it('should not find menu items that are disabled when searching by first letter pressed', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="trains-item">Trains</MenuListItem>
        <MenuListItem id="automobiles-item">Automobiles</MenuListItem>
        <MenuListItem id="autos-item" disabled>
          Autos
        </MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('a');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'automobiles-item');
    await userEvent.keyboard('a');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'automobiles-item');
  });

  it('should not change aria-activedescendant if there is no menu item match with letter key pressed', async () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    getMenuList().focus();
    await userEvent.keyboard('a');
    expect(getMenuList()).toHaveAttribute('aria-activedescendant', 'planes-item');
  });

  it('should have role menu', () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('role', 'menu');
  });

  it('should have tab index equal to -1', () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('tabindex', '-1');
  });

  it('should have tab index equal to 0 when tabFocusable prop is passed', async () => {
    render(
      <MenuList {...propsMock} tabFocusable>
        <MenuListItem id="planes-item">Planes</MenuListItem>
        <MenuListItem id="trains-item">Trains</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('tabindex', '0');
    await userEvent.tab();
    await waitFor(() => {
      expect(getMenuList()).toHaveFocus();
    });
  });

  it('should have an aria label when specified', () => {
    render(
      <MenuList {...propsMock}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('aria-label', ariaLabel);
  });

  it('should have a aria-labelledBy when specified', () => {
    const labelId = 'label-id';
    render(
      <MenuList {...propsMock} ariaLabelledBy={labelId}>
        <MenuListItem id="planes-item">Planes</MenuListItem>
      </MenuList>
    );

    expect(getMenuList()).toHaveAttribute('aria-labelledBy', labelId);
  });
});
