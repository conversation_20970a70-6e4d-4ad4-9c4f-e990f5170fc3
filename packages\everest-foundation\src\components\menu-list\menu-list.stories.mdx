import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import Examples from './menu-list.examples.mdx';
import { MenuList } from './menu-list';
import { MenuListItem } from '../menu-list-item';

<Meta
  title="Toolbox/Menu List"
  component={MenuList}
  parameters={{
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9870&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Id placed on the `ul` HTML element.',
    },
    onChange: {
      control: '-',
      description:
        'Callback that runs when a menu selection is made and should update React state with the `MenuListItem` id.',
    },
    testId: {
      control: 'text',
      description: 'Optional. Sets `data-testid` attribute on the html element.',
    },
    ariaLabel: {
      control: 'text',
      description: 'Optional. Label for the `MenuList` when standalone.',
    },
    ariaLabelledBy: {
      control: 'string',
      description: 'Optional. Id of the label element when not standalone.',
    },
    initialPosition: {
      control: 'select',
      options: ['start', 'end'],
      description:
        'Optional. Specifies the default `MenuListItem` when opened. It is either `start` or `end`. Default is `start`.',
    },
    closeMenu: {
      control: '-',
      description: 'Optional. Callback used to close the menu.',
    },
    checkedIds: {
      control: '-',
      description: 'Optional. Specifies the `MenuListItem` ids which remain checked.',
    },
    tabFocusable: {
      description: 'Optional. Indicates if the menu can receive tab focus. Default is `false`.',
      table: {
        defaultValue: { summary: false },
      },
    },
  }}
  args={{
    id: 'transport-menu-list',
    testId: 'transport-menu-list',
    ariaLabel: 'Transport Menu',
    tabFocusable: false,
    initialPosition: 'start',
  }}
/>

# Menu List

<Examples />

## Live Demo

<Canvas>
  <Story name="Menu List">
    {(args) => {
      const [lastSelectedId, setLastSelectedId] = React.useState('');
      const [checkedIds, setCheckedIds] = React.useState(['toolbar-item']);
      return (
        <MenuList
          id={args.id}
          testId={args.testId}
          ariaLabel={args.ariaLabel}
          onChange={({ id, role }) => {
            // setLastSelectedId is not required
            setLastSelectedId(id);
            if (role === 'menuitemcheckbox') {
              if (checkedIds.includes(id)) {
                const index = checkedIds.indexOf(id);
                const checkedIdsCopy = [...checkedIds];
                checkedIdsCopy.splice(index, 1);
                setCheckedIds(checkedIdsCopy);
                return;
              }
              setCheckedIds((prev) => [...prev, id]);
            }
          }}
          checkedIds={checkedIds}
          initialPosition={args.initialPosition}
          tabFocusable={args.tabFocusable}
        >
          <MenuListItem id="bookmarks-item" role="menuitemcheckbox">
            Always Show Bookmarks Bar
          </MenuListItem>
          <MenuListItem id="toolbar-item" role="menuitemcheckbox">
            Always Show Toolbar in Full Screen
          </MenuListItem>
          <MenuListItem id="url-item" role="menuitemcheckbox">
            Always Show Full URLs
          </MenuListItem>
          <MenuListItem id="touch-item" role="menuitemcheckbox" disabled>
            Customize Touch Bar... (disabled)
          </MenuListItem>
        </MenuList>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Menu List" />
