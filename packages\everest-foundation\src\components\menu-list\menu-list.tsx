import React, { Children, ReactElement, ReactNode, forwardRef, useEffect, useMemo, useRef, useState } from 'react';

import { MenuListContext, TMenuListItemValue } from './menu-list-context';
import { findEnabledMenuItem, handleOnKeyDown } from './menu-list-helpers';
import { mergeRefs, useCreateTestId } from '../../utils';

import styles from './menu-list.module.scss';
import MenuListItemStyles from '../menu-list-item-base/menu-list-item-base.module.scss';

export type TMenuListInitialPosition = 'start' | 'end';

export interface IMenuList {
  id: string;
  testId?: string;
  onChange: (value: TMenuListItemValue) => void;
  closeMenu?: () => void;
  ariaLabelledBy?: string;
  ariaLabel?: string;
  initialPosition?: TMenuListInitialPosition;
  tabFocusable?: boolean;
  children: ReactNode;
  checkedIds?: string[];
}

export const MenuList = forwardRef<HTMLUListElement, IMenuList>((props, ref): JSX.Element => {
  const {
    id,
    onChange,
    testId,
    children,
    closeMenu,
    ariaLabelledBy,
    ariaLabel,
    initialPosition = 'start' as TMenuListInitialPosition,
    tabFocusable = false,
    checkedIds,
  } = props;

  const softSelectedIndexRef = useRef(-1);
  const ulRef = useRef<HTMLUListElement>(null);
  const [activeDescendantId, setActiveDescendantId] = useState('');

  const context = useMemo(
    () => ({
      setActiveDescendantId,
      activeDescendantId,
      onChange,
      closeMenu,
      checkedIds,
    }),
    [setActiveDescendantId, activeDescendantId, onChange, closeMenu, checkedIds]
  );

  useEffect(() => {
    if (activeDescendantId === '') {
      softSelectedIndexRef.current = -1;
      return;
    }
    const childrenArray = Children.toArray(children);
    for (let i = 0; i < childrenArray.length; i++) {
      if ((childrenArray[i] as ReactElement).props.id === activeDescendantId) {
        softSelectedIndexRef.current = i;
        break;
      }
    }
  }, [activeDescendantId, children]);

  useEffect(() => {
    const childrenArray = Children.toArray(children);
    setActiveDescendantId(
      findEnabledMenuItem(
        initialPosition === ('start' as TMenuListInitialPosition) ? 'Home' : 'End',
        initialPosition === ('start' as TMenuListInitialPosition) ? 0 : childrenArray.length - 1,
        childrenArray
      )
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // scroll to active item
  useEffect(() => {
    scrollToMenuItem(activeDescendantId);
  }, [activeDescendantId]);

  // focus a menu list item
  function scrollToMenuItem(menuItemId: string) {
    if (menuItemId) {
      const el = document.getElementById(menuItemId);
      el &&
        el.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'nearest',
        });
    }
  }

  const testIdRef = useCreateTestId(testId);

  function handleOnKeyUp(e: React.KeyboardEvent) {
    if (['Enter', ' '].includes(e.key) && checkedIds?.includes(activeDescendantId)) {
      document.getElementById(activeDescendantId)?.classList.remove(MenuListItemStyles.active);
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleOnMouseLeave(e: React.MouseEvent) {
    setActiveDescendantId('');
  }

  return (
    <MenuListContext.Provider value={context}>
      <ul
        id={id}
        role="menu"
        tabIndex={tabFocusable ? 0 : -1}
        aria-labelledby={ariaLabelledBy}
        aria-label={ariaLabel}
        aria-activedescendant={activeDescendantId}
        className={styles.evrMenuList}
        ref={mergeRefs([testIdRef, ref, ulRef])}
        onKeyDown={(e) => {
          handleOnKeyDown(e, children, setActiveDescendantId, onChange, softSelectedIndexRef.current, closeMenu);
        }}
        onKeyUp={handleOnKeyUp}
        onMouseLeave={handleOnMouseLeave}
      >
        {children}
      </ul>
    </MenuListContext.Provider>
  );
});

MenuList.displayName = 'MenuList';
