# Submenu list item

## Summary

Research and document implementations for the Everest SubMenu List Item.

- Start Date: 2023-07-30
- Figma link: https://www.figma.com/file/UmMM8itkMOkTafd1XW5DjK/%F0%9F%A7%AA-Toolbar?type=design&node-id=1468-101685&mode=design&t=vqsEjbA5JcFQ7Wgu-0
- POC: https://dev.azure.com/Ceridian/Platform/_git/platform-components/pullrequest/332940

## Detailed Design

SubMenu anatomy consists of the following and is divided into sections:

- **MenuListItem** - The parent container which sets expandable item.
- **MenuListItemHeader:** - The header contains the text or the children to display in the submenu header

## API

### Menu List Item

1.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the menu list item.
2.  **id**: `string`
    Mandatory. Sets the id of the menu list item .
3.  **children**: `ReactNode | undefined`
    Optional. Sets the contents of the menu list item.
4.  **disabled**: `boolean`
    Optional. Disable the button and menu. Default is false
5.  **divider**: `boolean`
    Optional. Displays a horizontal divider at the bottom of the menu item.
6.  **role**: `['menuitem', 'menuitemcheckbox'] (TAriaMenuItemRole)`
    Optional. Allows you to set the different roles to the menu list item
7.  **ref**: `ReactRef | undefined`
    Optional but Mandatory if Submenu. Allows users to interact with items using the keyboard

### MenuListItemHeader(Required if it is a submenu)

1.  **id**: `string`
    Mandatory. Sets the id of the header.
2.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the header.
3.  **children**: `ReactNode | undefined`
    Mandatory. Sets the contents of the submenu menu list item

## Usage

We will use the existing `MenuListItem` along with a new `MenuListItemHeader` for creating a submenu within our existing menu. If `MenuListItemHeader` is passed as a child we will identify the `MenuListItem` as a sub menu and use the following implementation

### MenuList Item dev implementation when expandable

```
()=>{
    const [open, setOpen] = useState(false);
    return (
        if (headingChild: MenuListItemHeader) {
            return (
            <li>
                    {headingChild}
                    { open && (
                        <MenuList>
                            {display MenuList .... }
                        </MenuList>
                    )}
            </li>
            )
        }
        {Otherwise its a common list item and render how it is being rendered right now}
    )
}

```

As part of building submenu, we will internally control the state and utilize the `MenuList` to build the submenu to utilize the keyboard behavior. For now we will not limit the number of levels the users can have for submenus

### MenuList Item User implementation when expandable

```
()=>{
    const submenuRef = useRef(null);
    return (
        <MenuList id="user-menu">
            <MenuListItem id="list-item-1">NUMBER 1</MenuListItem>
            <MenuListItem id="list-item-2">NUMBER 2</MenuListItem>
            <MenuListItem id="list-item-3">NUMBER 3</MenuListItem>
            <MenuListItem id="list-item-4">NUMBER 4</MenuListItem>
            <MenuListItem id="list-item-5" ref={submenuRef}>
                <MenuListItemHeading>SubMenu</MenuListItemHeading>
                <MenuListItem id="sub-list-item-1">SUB NUMBER 1</MenuListItem>
                <MenuListItem id="sub-list-item-2">SUB NUMBER 2</MenuListItem>
            </MenuListItem>
        </MenuList>
    )
}

```

## Accessibility

- Screenreaders by default will announce the number of items available in a list.
- Will follow the Keyboard behavior and utilize MenuList accessibility

## Q&A

**Should we let users handle the state externally?**
Initially we had planned on this and was using that appraoch as alternative but the more we investigated we learned how complicated state management can get for end users the more levels of submenus we have

**How many levels of submenus we will allow the users?**
We will not explicity limit the number of submenus allowed but design has suggested that we will see at most 2 levels of submenus. We should be able to handle that and will include examples with that

**Should we let users add MenuList instead of encapsulating it inside the components?**
We discovered when building the component that encapsulating the component allowed us to move focus and pass context like `closeMenu` a lot easier. It also gave us much more control in managing state.

## Other Design Systems

### Material UI - https://mui.com/material-ui/react-list/

- Uses divs instead of list html elements like ul or li
- User needs to handle the state themselves instead of internally

### Carbon Design System - https://carbondesignsystem.com/components/list/usage/

- Utilizes the list html elements
- Read only lists, this design system only allows to display lists and sublist without any iteraction or flexibility

## Required Stories

### EDS-3430: Create SubMenu component - https://ceridian.atlassian.net/browse/EDS-3430

## Change Log
