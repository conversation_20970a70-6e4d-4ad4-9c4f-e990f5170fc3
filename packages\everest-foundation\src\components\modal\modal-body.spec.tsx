import React from 'react';
import { render } from '@testing-library/react';

import { ModalBody, ModalContentText } from '.';

const modalBodyTestId = 'modal-body-test-id';
const modalContentTextTestId = 'modal-content-text-test-id';

describe('ModalBody', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render ModalContentText as child', () => {
    const { getByTestId } = render(
      <ModalBody id="modal-body-id" testId={modalBodyTestId}>
        <ModalContentText id="modal-content-text-id" testId={modalContentTextTestId} content="Content Text" />
      </ModalBody>
    );
    expect(getByTestId(modalBodyTestId).textContent).toEqual('Content Text');
  });
});
