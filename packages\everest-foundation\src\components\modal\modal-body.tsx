import React, { PropsWithChildren } from 'react';

import { IOverlayBody, OverlayBody } from '../overlay';

import styles from './modal.module.scss';

export const ModalBody = React.forwardRef<HTMLDivElement, PropsWithChildren<IOverlayBody>>(
  (props, ref): JSX.Element => {
    const { children, id, testId } = props;

    return (
      <div className={styles.evrModalBody}>
        <OverlayBody id={id} testId={testId} ref={ref}>
          {children}
        </OverlayBody>
      </div>
    );
  }
);

ModalBody.displayName = 'ModalBody';
