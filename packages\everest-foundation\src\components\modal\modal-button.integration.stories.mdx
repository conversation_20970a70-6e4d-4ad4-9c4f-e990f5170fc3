import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON>dalBody, ModalContentText, <PERSON>dal<PERSON>ooter, ModalFooterActions } from '../modal';
import { Button } from '../button';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Integration"
  component={Modal}
  parameters={{ chromatic: Chromatic.DISABLE }}
/>

# Open Modal from Button

## Live Demo

<Canvas>
  <Story name="Open Modal from Button">
    {() => {
      const [open, setOpen] = React.useState(false);
      return (
        <>
          <Button
            id="trigger-button-integration"
            label="Open Modal"
            onClick={() => {
              setOpen(true);
            }}
          />
          <Modal
            size="md"
            id="modal-integration"
            open={open}
            onClose={(e) => {
              setOpen(false);
              const triggerBtn = document.getElementById('trigger-button-integration');
              triggerBtn?.focus();
            }}
            ariaLabelledBy="modal-header-integration"
            ariaDescribedBy="modal-body-integration"
          >
            <ModalHeader
              title="Modal Heading"
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="modal-header-integration"
              closeButtonAriaLabel="Close Modal"
            ></ModalHeader>
            <ModalBody id="modal-body-integration">
              <ModalContentText id="modal-body-content-integration" content="Test content" />
            </ModalBody>
            <ModalFooter id="modal-footer-integration">
              <ModalFooterActions
                id="modal-action-buttons-integration"
                primaryAction={{
                  id: 'primary-button-integration',
                  label: 'Continue',
                }}
                secondaryAction={{
                  id: 'close-button-integration',
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
    }}
  </Story>
</Canvas>
