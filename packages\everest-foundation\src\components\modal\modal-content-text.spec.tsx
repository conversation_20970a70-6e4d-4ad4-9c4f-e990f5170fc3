import React from 'react';
import { render } from '@testing-library/react';

import { ModalContentText } from './modal-content-text';

const modalContentTextTestId = 'modal-content-text-test-id';

describe('ModalContentText', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should contain content text', () => {
    const { getByTestId } = render(
      <ModalContentText id="modal-content-text-id" testId={modalContentTextTestId} content="Content Text" />
    );
    expect(getByTestId(modalContentTextTestId).textContent).toEqual('Content Text');
  });
});
