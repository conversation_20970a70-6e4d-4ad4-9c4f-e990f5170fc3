import React from 'react';

import { useCreateTestId, mergeRefs } from '../../utils';

export interface IModalContentText {
  id: string;
  testId?: string;
  content: string;
}

export const ModalContentText = React.forwardRef<HTMLParagraphElement, IModalContentText>(
  (props: IModalContentText, ref) => {
    const { id, testId, content } = props;
    const dataRef = useCreateTestId(testId);
    return (
      <p ref={mergeRefs([ref, dataRef])} id={id} className="evrBodyText">
        {content}
      </p>
    );
  }
);

ModalContentText.displayName = 'ModalContentText';
