import React from 'react';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { ModalFooter, ModalFooterActions } from '.';

const onClick = jest.fn();

const footerActionsTestId = 'footer-actions-test-id';
const modalFooterActionsTestId = 'overlay-footer-actions-test-id';

const getFooterActions = () => screen.getByTestId(footerActionsTestId);
const getPrimaryButton = () => screen.getByText(/continue/i);
const getSecondaryButton = () => screen.getByText(/cancel/i);
const getTertiaryButton = () => screen.getByText(/tertiary/i);

describe('ModalFooterActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render ModalFooterActions as child', () => {
    const { getByTestId } = render(
      <ModalFooter id="modal-footer-id" testId={footerActionsTestId}>
        <ModalFooterActions
          id="modal-action-buttons-id"
          testId={modalFooterActionsTestId}
          primaryAction={{
            id: 'primary-button-id',
            label: 'Continue',
            onClick: onClick,
          }}
          secondaryAction={{
            id: 'secondary-button-id',
            label: 'Cancel',
            onClick: onClick,
          }}
        ></ModalFooterActions>
      </ModalFooter>
    );
    expect(getByTestId(footerActionsTestId)).toBeInTheDocument();
    expect(within(getFooterActions()).getAllByRole('button').length).toBe(2);
    expect(getByTestId(footerActionsTestId).firstChild).toHaveAttribute('data-testid', modalFooterActionsTestId);
  });

  it('should contain clickable primary button', async () => {
    render(
      <ModalFooterActions
        id="modal-action-buttons-id"
        testId={footerActionsTestId}
        primaryAction={{
          id: 'primary-button-id',
          label: 'Continue',
          onClick: onClick,
        }}
      />
    );
    expect(getFooterActions()).toBeInTheDocument();
    expect(within(getFooterActions()).getAllByRole('button').length).toBe(1);
    expect(getPrimaryButton()).toBeInTheDocument();
    await userEvent.click(getPrimaryButton());
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('should contain clickable primary button and secondary button', async () => {
    render(
      <ModalFooterActions
        id="modal-action-buttons-id"
        testId={footerActionsTestId}
        primaryAction={{
          id: 'primary-button-id',
          label: 'Continue',
          onClick: onClick,
        }}
        secondaryAction={{
          id: 'secondary-button-id',
          label: 'cancel',
          onClick: onClick,
        }}
      />
    );
    expect(getFooterActions()).toBeInTheDocument();
    expect(within(getFooterActions()).getAllByRole('button').length).toBe(2);
    await userEvent.click(getPrimaryButton());
    expect(onClick).toHaveBeenCalledTimes(1);
    onClick.mockClear();
    await userEvent.click(getSecondaryButton());
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('should contain clickable primary, secondary, and tertiary buttons', async () => {
    render(
      <ModalFooterActions
        id="modal-action-buttons-id"
        testId={footerActionsTestId}
        primaryAction={{
          id: 'primary-button-id',
          label: 'Continue',
          onClick: onClick,
        }}
        secondaryAction={{
          id: 'secondary-button-id',
          label: 'cancel',
          onClick: onClick,
        }}
        tertiaryAction={{
          id: 'tertiary-button-id',
          label: 'tertiary',
          onClick: onClick,
        }}
      />
    );
    expect(getFooterActions()).toBeInTheDocument();
    expect(within(getFooterActions()).getAllByRole('button').length).toBe(3);
    await userEvent.click(getPrimaryButton());
    expect(onClick).toHaveBeenCalledTimes(1);
    onClick.mockClear();
    await userEvent.click(getSecondaryButton());
    expect(onClick).toHaveBeenCalledTimes(1);
    onClick.mockClear();
    await userEvent.click(getTertiaryButton());
    expect(onClick).toHaveBeenCalledTimes(1);
  });
});
