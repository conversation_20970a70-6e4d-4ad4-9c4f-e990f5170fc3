import React from 'react';

import { useCreateTestId, mergeRefs } from '../../utils';
import { Button } from '../button';
import { TIconName } from '../icon';

import styles from './modal.module.scss';

export interface IModalButtonAction {
  id: string;
  label: string;
  ariaLabel?: string;
  onClick?: (e: React.MouseEvent) => void;
  startIcon?: TIconName;
  endIcon?: TIconName;
}

export interface IModalFooterActions {
  id: string;
  testId?: string;
  primaryAction: IModalButtonAction;
  secondaryAction?: IModalButtonAction;
  tertiaryAction?: IModalButtonAction;
}

export const ModalFooterActions = React.forwardRef<HTMLElement, IModalFooterActions>(
  (props: IModalFooterActions, ref) => {
    const { id, testId, primaryAction, secondaryAction, tertiaryAction } = props;
    const dataRef = useCreateTestId(testId);

    return (
      <section id={id} ref={mergeRefs([ref, dataRef])} className={styles.evrModalActionButtons}>
        {tertiaryAction ? (
          <Button
            id={tertiaryAction.id}
            testId={testId && `${testId}-tertiary-action`}
            variant="tertiary"
            label={tertiaryAction.label}
            ariaLabel={tertiaryAction.ariaLabel}
            onClick={tertiaryAction.onClick}
            startIcon={tertiaryAction.startIcon}
            endIcon={tertiaryAction.endIcon}
          />
        ) : null}
        <div className={styles.evrModalFooterActionButtonsFiller}></div>
        {secondaryAction ? (
          <Button
            id={secondaryAction.id}
            testId={testId && `${testId}-secondary-action`}
            variant="secondary"
            label={secondaryAction.label}
            ariaLabel={secondaryAction.ariaLabel}
            onClick={secondaryAction.onClick}
            startIcon={secondaryAction.startIcon}
            endIcon={secondaryAction.endIcon}
          />
        ) : null}
        <Button
          id={primaryAction.id}
          testId={testId && `${testId}-primary-action`}
          variant="primary"
          label={primaryAction.label}
          ariaLabel={primaryAction.ariaLabel}
          onClick={primaryAction.onClick}
          startIcon={primaryAction.startIcon}
          endIcon={primaryAction.endIcon}
        />
      </section>
    );
  }
);

ModalFooterActions.displayName = 'ModalFooterActions';
