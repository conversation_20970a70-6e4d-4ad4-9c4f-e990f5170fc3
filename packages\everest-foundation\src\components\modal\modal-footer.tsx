import React, { PropsWithChildren } from 'react';

import { IOverlayFooter, OverlayFooter } from '../overlay';

import styles from './modal.module.scss';

export const ModalFooter = React.forwardRef<HTMLDivElement, PropsWithChildren<IOverlayFooter>>(
  (props, ref): JSX.Element => {
    const { children, id, testId } = props;

    return (
      <div className={styles.evrModalFooter}>
        <OverlayFooter id={id} testId={testId} ref={ref}>
          {children}
        </OverlayFooter>
      </div>
    );
  }
);

ModalFooter.displayName = 'ModalFooter';
