import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { ModalHeader } from './modal-header';

const modalHeaderTestId = 'modal-header-test-id';
const onCloseButtonClick = jest.fn();

const getIconButton = () => screen.getByTestId(modalHeaderTestId).lastChild as HTMLElement;
const getTitle = () => screen.getByTestId(modalHeaderTestId).firstChild as HTMLElement;

const mockProps = {
  id: 'modal-body-id',
  testId: modalHeaderTestId,
  title: 'Heading',
  onCloseButtonClick: onCloseButtonClick,
  closeButtonAriaLabel: 'close button',
};

describe('ModalHeader', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should contain title', () => {
    render(<ModalHeader {...mockProps} />);
    expect(getTitle().textContent).toEqual('Heading');
  });

  it('should contain icon button', () => {
    render(<ModalHeader {...mockProps} />);
    expect(getIconButton()).toBeInTheDocument();
  });

  it('icon button is clickable', async () => {
    render(<ModalHeader {...mockProps} />);
    await userEvent.click(getIconButton());
    expect(onCloseButtonClick).toHaveBeenCalled();
  });

  it('dispatch onClick event when spacebar and enter keys are pressed ', async () => {
    render(<ModalHeader {...mockProps} />);
    await userEvent.type(getIconButton(), '{enter}');
    expect(onCloseButtonClick).toHaveBeenCalled();
    onCloseButtonClick.mockClear();
    await userEvent.type(getIconButton(), '{space}');
    expect(onCloseButtonClick).toHaveBeenCalled();
  });
});
