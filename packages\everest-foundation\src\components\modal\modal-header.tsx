import React, { useEffect } from 'react';

import { useEverestContext } from '../everest-provider';
import { IOverlayHeader, OverlayHeader } from '../overlay';

import styles from './modal.module.scss';

export interface IModalHeader extends IOverlayHeader {
  title: string;
}

export const ModalHeader = React.forwardRef<HTMLElement, IModalHeader>((props: IModalHeader, ref) => {
  const { id, testId, title, closeButtonAriaLabel, onCloseButtonClick } = props;

  const [isModalLarge, setIsModalLarge] = React.useState(false);

  const getTitle = () => {
    if (isModalLarge) return <h3 className="evrHeading3">{title}</h3>;
    return <h4 className="evrHeading4">{title}</h4>;
  };

  const everestContext = useEverestContext();

  useEffect(() => {
    setIsModalLarge(everestContext.breakpoint === 'lg');
  }, [everestContext.breakpoint]);

  return (
    <div className={styles.evrModalHeader}>
      <OverlayHeader
        id={id}
        testId={testId}
        closeButtonAriaLabel={closeButtonAriaLabel}
        onCloseButtonClick={onCloseButtonClick}
        ref={ref}
      >
        {getTitle()}
      </OverlayHeader>
    </div>
  );
});

ModalHeader.displayName = 'ModalHeader';
