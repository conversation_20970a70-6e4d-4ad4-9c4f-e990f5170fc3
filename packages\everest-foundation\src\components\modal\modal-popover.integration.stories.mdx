import { <PERSON><PERSON>, Story, Canvas } from '@storybook/addon-docs';
import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, ModalHeader, ModalBody, ModalContentText, ModalFooter, ModalFooterActions } from '.';
import { <PERSON>over, PopoverHeader, PopoverBody, PopoverFooter } from '../popover';
import { Button } from '../button';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Integration"
  component={Modal}
  parameters={{ chromatic: Chromatic.DISABLE }}
/>

# Open Popover from Modal

<Examples />

## Live Demo

<Canvas>
  <Story name="Open Popover from Modal">
    {(args) => {
      const popoverTriggerRef = useRef(null);
      const [popoverOpen, setPopoverOpen] = useState(false);
      const [open, setOpen] = useState(false);
      const triggerBtnRef = useRef(null);
      return (
        <>
          <Button
            id="trigger-button-integration"
            label="Open Modal"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerBtnRef}
          />
          <Modal
            size="md"
            id="modal-integration"
            open={open}
            onClose={(e) => {
              setOpen(false);
              triggerBtnRef?.current?.focus();
            }}
            ariaLabelledBy="modal-header-integration"
            ariaDescribedBy="modal-body-integration"
          >
            <ModalHeader
              title="Modal Heading"
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="modal-header-integration"
              closeButtonAriaLabel="Close Modal"
            />
            <ModalBody id="modal-body-integration">
              <Button
                id="popover-trigger-button-integration"
                label="Open Popover"
                ref={popoverTriggerRef}
                onClick={() => setPopoverOpen((prev) => !prev)}
              />
              <div>
                <Popover
                  id="popover-integration"
                  ariaLabelledBy="popoverHeader-integration"
                  ariaDescribedBy="popoverBody-integration"
                  placement="bottom"
                  open={popoverOpen}
                  triggerRef={popoverTriggerRef}
                  onOpen={(e) => {}}
                  onClose={(e) => {
                    setPopoverOpen(false);
                    popoverTriggerRef?.current?.focus();
                  }}
                >
                  <PopoverHeader
                    closeButtonAriaLabel={'close popover'}
                    onCloseButtonClick={(e) => {
                      setPopoverOpen(false);
                    }}
                    id="popoverHeader-header-integration"
                    testId="popover-header-integration"
                  >
                    <h3 id="header-integration" className="evrHeading3">
                      Popover Heading
                    </h3>
                  </PopoverHeader>
                  <PopoverBody id="popoverBody-integration">
                    <p className="evrBodyText">Popover Body</p>
                  </PopoverBody>
                  <PopoverFooter id="popoverFooter-integration">
                    <p className="evrBodyText">Popover Footer</p>
                  </PopoverFooter>
                </Popover>
              </div>
            </ModalBody>
            <ModalFooter id="modal-footer-integration">
              <ModalFooterActions
                id="modal-action-buttons-integration"
                primaryAction={{
                  id: 'primary-button-integration',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  id: 'close-button-integration',
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
    }}
  </Story>
</Canvas>
