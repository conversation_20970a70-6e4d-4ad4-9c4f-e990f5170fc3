import { <PERSON><PERSON>, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalBody, ModalContent<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON>dalFooterActions } from '../modal';
import { Button } from '../button';
import { Chromatic, defaultModes } from '../../../chromatic';
import { TextArea } from '../text-area';
import { TextField } from '../text-field';

<Meta
  title="Testing/Automation Test Cases/Integration"
  component={Modal}
  parameters={{ chromatic: Chromatic.DISABLE }}
/>

# Modal focus with TextField TextArea

## Live Demo

<Canvas>
  <Story name="Modal focus with TextField TextArea">
    {() => {
      const [open, setOpen] = React.useState(false);
      const [valueTextField, setValueTextField] = React.useState('');
      const handleChangeTextField = (valueTextField) => setValueTextField(valueTextField);
      const [valueTextArea, setValueTextArea] = React.useState('');
      const handleChangeTextArea = (valueTextArea) => setValueTextArea(valueTextArea);
      const triggerButtonRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-send-message-integration"
            label="Open Modal"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerButtonRef}
          />
          <Modal
            size="md"
            open={open}
            id="modal-send-message-integration"
            onClose={() => {
              setOpen(false);
              // When the Modal closes, we must return the focus back to the trigger button.
              triggerButtonRef?.current?.focus();
            }}
            ariaLabelledBy="modal-header-send-message-integration"
            ariaDescribedBy="modal-body-send-message-integration"
          >
            <ModalHeader
              title="New message"
              onCloseButtonClick={() => {
                setOpen(false);
              }}
              id="modal-header-send-message-integration"
              closeButtonAriaLabel={'Close Modal'}
            ></ModalHeader>
            <ModalBody id="modal-body-send-message-integration">
              <TextField
                label="Recipient"
                id="text-field-recipient-integration"
                value={valueTextField}
                onChange={handleChangeTextField}
                testId="modal-text-field-integration"
              />
              <TextArea
                label="Message"
                id="text-area-message-integration"
                onChange={handleChangeTextArea}
                value={valueTextArea}
                testId="modal-text-area-integration"
              />
            </ModalBody>
            <ModalFooter id="modal-footer-send-message-integration">
              <ModalFooterActions
                id="modal-action-buttons-send-message-integration"
                primaryAction={{
                  id: 'primary-button-send-message-integration',
                  label: 'Send message',
                  onClick: () => alert('new message sent'),
                }}
                secondaryAction={{
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
    }}
  </Story>
</Canvas>
