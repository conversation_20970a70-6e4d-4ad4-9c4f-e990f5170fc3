import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { useEffect, useRef, useState } from 'react';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { <PERSON>dal, ModalHeader, ModalBody, ModalContentText, ModalFooter, ModalFooterActions } from '.';
import { But<PERSON> } from '../button';
import { TextArea } from '../text-area';
import { TextField } from '../text-field';
import { useEverestContext } from '../everest-provider';

export const scope = {
  Modal,
  Button,
  ModalHeader,
  ModalBody,
  ModalContentText,
  ModalFooter,
  ModalFooterActions,
  TextArea,
  TextField,
  useEffect,
  useRef,
  useState,
  useEverestContext,
};

A modal is a dialog box/pop-up window that is displayed on top of the current page with an overlay covering the rest of the screen. It is used to prompt the user for additional input, display information, or confirm an action.

Modal has three sections, `ModalHeader`, `ModalBody` and `<PERSON>dalFooter`.

`ModalBody` can render any child. `ModalContentText` can be used to render the text inside the `ModalBody`.

Below is the interface of the `ModalContentText`.

```typescript
export interface IModalContentText {
  id: string;
  testId?: string;
  content: string;
}
```

`ModalFooterActions` is used to render primary, secondary, and tertiary action buttons in the `ModalFooter`. The primary action button is mandatory, while the secondary and tertiary action buttons are optional.

Each action button is defined by `IModalButtonAction` as following:

```typescript
export interface IModalButtonAction {
  id: string;
  label: string;
  ariaLabel?: string;
  onClick?: (e: React.MouseEvent) => void;
  startIcon?: TIconName;
  endIcon?: TIconName;
}
```

## How to Use

It is recommended for Modal to contain the children of `ModalHeader`, `ModalBody` and `ModalFooter`.

`open` is used to toggle the visibility of the Modal. When `open` is set to true, the Modal opens, and it triggers the `onOpen` callback function. Modal can be closed by setting the `open` to false and when it is set to false, it triggers the `onClose` callback function.

The `onOpen` and `onClose` callback functions are used to perform operations when the Modal opens and closes respectively. When the modal is opened, the modal will focus the content container by default. The consumer can change this focus behavior if they want by setting custom focus behavior in `onOpen`.

When the Modal closes, the focus <b>must</b> be returned to the element that triggered the Modal. This can be done using the `onClose` callback. See [Modal Default](#default-modal) for example.

The `onCloseButtonClick` callback runs when the close button that is inside the ModalHeader is clicked and it is used to set the `open` to false to close the Modal.

The Close IconButton of the `ModalHeader` will be rendered only when the `onCloseButtonClick` callback and `closeButtonAriaLabel` prop are passed.

The Modal can be closed by pressing the <kbd>Escape</kbd> keyboard button and can be controlled by a return reason of the `onClose` callback. See [Prevent rendering of Close IconButton and disable Modal closing on Escape key](#prevent-rendering-of-close-iconbutton-and-disable-modal-closing-on-escape-key) for example.

The feature team should prevent the document/page from scrolling when the Modal is open.

## Responsive

A responsive Modal can be achieved through use of the `useEverestContext` hook provided by <LinkTo kind="Foundations/Everest Provider">EverestProvider</LinkTo>. The following breakpoint ranges should be referenced to determine which value to supply to `size`. This is demonstrated in the [Use useEverestContext to dynamically size the Modal](#use-useeverestcontext-to-dynamically-size-the-modal) example below.

| Breakpoints              | Size       |
| ------------------------ | ---------- |
| &leq; 429 (xs)           | fullscreen |
| 430 - 767 (sm)           | sm         |
| 768 - 1279 (md)          | md         |
| &geq; 1280 (lg, xl, xxl) | lg         |

## Accessibility

`ariaLabelledBy` should be set to `ModalHeader`'s id and `ariaDescribedBy` should be set to `ModalBody`'s id for the screen reader to announce the Modal's children when the modal opens.

## Variations

### Default Modal

Modal has 4 fixed sizes, which can be set using the `size` prop.

- Mobile (`size`: "fullscreen"): covers the entire screen
- Small (`size`: "sm"): width 375px
- Medium (`size`: "md"): width 560px
- Large (`size`: "lg"): width 720px

The Max Height of the Small, Medium, and Large is 75% of the viewport. The mobile view covers the entire screen.

Below is an example of a Modal with a `size` of "lg".

export const defaultCode = `() => {
      const [open, setOpen] = useState(false);
      const triggerButtonRef=useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id"
            label="Modal (Size:lg)"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerButtonRef}
          />
          <Modal
            open={open}
            id="modal-id"
            onClose={(e) => {
                setOpen(false);
                // When the Modal closes, we must return the focus back to the trigger button.
                triggerButtonRef && triggerButtonRef.current.focus();
            }}
            ariaLabelledBy="modal-header-id"
            ariaDescribedBy="modal-body-id"
            size="lg"
          >
            <ModalHeader
              title="Heading"
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="modal-header-id"
              closeButtonAriaLabel={'Close Modal'}
            ></ModalHeader>
            <ModalBody id="modal-body-id">
              <ModalContentText id="modal-body-content-id" content="Content text"></ModalContentText>
            </ModalBody>
            <ModalFooter id="modal-footer-id">
              <ModalFooterActions
                id="modal-action-buttons-id"
                primaryAction={{
                  id: 'primary-button-id',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
}`;

<CodeExample scope={scope} code={defaultCode} />

### Modal with Long ContentText

ModalBody will have a scrolling effect if the content overflows the max height of the Modal. ModalHeader and ModalFooter are always fixed.

export const longContentText = `() => {
      const [open, setOpen] = useState(false);
      const triggerButtonRef=useRef(null);
      return (
        <>
          <Button
            id="trigger-button-long-content-text-id"
            label="Modal Long ContentText"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerButtonRef}
          />
          <Modal
            size="md"
            open={open}
            id="modal-long-content-text-id"
            onClose={() => {
                setOpen(false);
                // When the Modal closes, we must return the focus back to the trigger button.
                triggerButtonRef && triggerButtonRef.current.focus();
            }}
            ariaLabelledBy="modal-header-long-content-text-id"
            ariaDescribedBy="modal-body-long-content-text-id"
          >
            <ModalHeader
              title="Long ContentText (Scrollable Body)"
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="modal-header-long-content-text-id"
              closeButtonAriaLabel={'Close Modal'}
            ></ModalHeader>
            <ModalBody id="modal-body-long-content-text-id">
              <ModalContentText id="modal-body-content-id" content="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin interdum pellentesque viverra. Mauris eget consectetur neque. Curabitur venenatis accumsan sapien non interdum. Praesent vitae eros feugiat, molestie quam vitae, tincidunt ipsum. Vivamus non justo arcu. Phasellus magna turpis, maximus id rhoncus faucibus, fringilla vitae quam. Praesent malesuada lorem justo, eget iaculis elit tristique id. Aenean ullamcorper augue a scelerisque eleifend. Donec sed accumsan metus, et sollicitudin nisl. Nulla porttitor dictum turpis vitae finibus. Quisque vitae quam sollicitudin, sodales diam et, condimentum neque. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Ut at est bibendum, tincidunt sem vel, congue eros. Vivamus est augue, gravida eu lacus id, consequat hendrerit diam. Mauris ultricies, libero ac semper laoreet, est nisi pretium urna, sed finibus diam lectus vel arcu. Nullam posuere nunc a odio pellentesque, nec rhoncus nibh scelerisque. Ut venenatis nisi ut vulputate porta. Fusce id lorem nec libero malesuada interdum vitae vitae lectus. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nunc id accumsan lacus. Donec vel ligula pharetra ligula elementum efficitur. Mauris accumsan mi in dui consequat, sed cursus felis auctor. Nunc vitae fermentum quam, vitae laoreet ipsum. Praesent sed varius nisl. Nunc vehicula gravida nulla non vestibulum. Nulla commodo neque neque, id interdum urna sollicitudin sit amet. Nulla volutpat congue elementum. Sed rhoncus sapien sed risus ornare, eu semper sem mattis. Sed sollicitudin, libero ut imperdiet viverra, nisi erat iaculis tellus, vel pretium arcu erat id nunc. Ut massa orci, varius sit amet lectus et, consectetur suscipit mauris. Sed in fringilla risus, vel mattis nulla. Praesent dapibus, turpis eget sagittis sodales, augue felis pulvinar nisi, sed accumsan elit sem non neque. Suspendisse facilisis sit amet dolor eu finibus"></ModalContentText>
            </ModalBody>
            <ModalFooter id="modal-footer-long-content-text-id">
              <ModalFooterActions
                id="modal-action-buttons-long-content-text-id"
                primaryAction={{
                  id: 'primary-button-long-content-text-id',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
}`;

<CodeExample scope={scope} code={longContentText} />

### ModalBody can render any child

`TextArea` and `TextField` are rendered inside the `ModalBody`.

export const sendMessageCode = `() => {
      const [open, setOpen] = useState(false);
      const [valueTextField, setValueTextField] = useState('');
      const handleChangeTextField = (valueTextField) => setValueTextField(valueTextField);
      const [valueTextArea, setValueTextArea] = useState('');
      const handleChangeTextArea = (valueTextArea) => setValueTextArea(valueTextArea);
      const triggerButtonRef=useRef(null);
      return (
        <>
          <Button
            id="trigger-button-send-message-id"
            label="ModalBody can render any child"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerButtonRef}
          />
          <Modal
            size="md"
            open={open}
            id="modal-send-message-id"
            onClose={() => {
                setOpen(false);
                // When the Modal closes, we must return the focus back to the trigger button.
                triggerButtonRef && triggerButtonRef.current.focus();
            }}
            ariaLabelledBy="modal-header-send-message-id"
            ariaDescribedBy="modal-body-send-message-id"
          >
            <ModalHeader
              title="New message"
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="modal-header-send-message-id"
              closeButtonAriaLabel={'Close Modal'}
            ></ModalHeader>
            <ModalBody id="modal-body-send-message-id">
              <TextField label="Recipient" id="text-field-recipient-id"  value={valueTextField} onChange={handleChangeTextField} />
              <TextArea label="Message" id="text-area-message-id" onChange={handleChangeTextArea} value={valueTextArea} />
            </ModalBody>
            <ModalFooter id="modal-footer-send-message-id">
              <ModalFooterActions
                id="modal-action-buttons-send-message-id"
                primaryAction={{
                  id: 'primary-button-send-message-id',
                  label: 'Send message',
                  onClick: () => alert('new message sent'),
                }}
                secondaryAction={{
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
}`;

<CodeExample scope={scope} code={sendMessageCode} />

### ModalFooterActions with Tertiary Variant Button

`<ModalFooterActions>` must include a primary variant button and can include an optional secondary and/or tertiary variant button as well. This example shows a `<ModalFooterActions>` with all three actions included.

export const tertiaryButtonCode = `() => {
      const [open, setOpen] = useState(false);
      const triggerButtonRef=useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id"
            label="Modal with tertiary button"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerButtonRef}
          />
          <Modal
            size="md"
            open={open}
            id="modal-id"
            onClose={(e) => {
                setOpen(false);
                // When the Modal closes, we must return the focus back to the trigger button.
                triggerButtonRef && triggerButtonRef.current.focus();
            }}
            ariaLabelledBy="modal-header-id"
            ariaDescribedBy="modal-body-id"
          >
            <ModalHeader
              title="Heading"
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="modal-header-id"
              closeButtonAriaLabel={'Close Modal'}
            ></ModalHeader>
            <ModalBody id="modal-body-id">
              <ModalContentText id="modal-body-content-id" content="Content text"></ModalContentText>
            </ModalBody>
            <ModalFooter id="modal-footer-id">
              <ModalFooterActions
                id="modal-action-buttons-id"
                primaryAction={{
                  id: 'primary-button-id',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
                tertiaryAction={{
                  label: 'Save as Draft',
                  onClick: () => alert('Draft saved'),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
}`;

<CodeExample scope={scope} code={tertiaryButtonCode} />

### Prevent `<body>` from scrolling when Modal is open

The feature team should prevent the document/page from scrolling when the Modal is open.

In this example we have targeted the `<body>`, but the feature team should target what's appropriate for them, most likely their MFE's `<main>` instead of `<body>`

export const bodyScroll = `() => {
      const [open, setOpen] = useState(false);
      const triggerButtonRef=useRef(null);
      return (
        <>
          <Button
            id="trigger-button-body-scroll-id"
            label="Modal stop page scrolling"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerButtonRef}
          />
          <Modal
            size="md"
            open={open}
            id="modal-body-scroll-id"            
            onOpen={() => {
              // we have targeted the <body>, but the feature team should target what's appropriate for them, most likely their MFE's <main> instead of <body>
              document.body.style.overflowY = 'hidden';
            }}
            onClose={() => {
                setOpen(false);
                //reset the scrolling effect
                document.body.style.overflowY = 'auto';
                // When the Modal closes, we must return the focus back to the trigger button.
                triggerButtonRef && triggerButtonRef.current.focus();
            }}
            ariaLabelledBy="modal-header-body-scroll-id"
            ariaDescribedBy="modal-body-body-scroll-id"
          >
            <ModalHeader
              title="Heading"
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="modal-header-body-scroll-id"
              closeButtonAriaLabel={'Close Modal'}
            ></ModalHeader>
            <ModalBody id="modal-body-body-scroll-id">
              <ModalContentText content="Content text"></ModalContentText>
            </ModalBody>
            <ModalFooter id="modal-footer-body-scroll-id">
              <ModalFooterActions
                id="modal-action-buttons-body-scroll-id"
                primaryAction={{
                  id: 'primary-button-body-scroll-id',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
}`;

<CodeExample scope={scope} code={bodyScroll} />

### Prevent rendering of Close IconButton and disable Modal closing on Escape key

The Close IconButton of the ModalHeader will only be rendered when the `onCloseButtonClick` callback and `closeButtonAriaLabel` prop are passed. The Modal closing by pressing the <kbd>Escape</kbd> keyboard button can be prevented by the return reason of the `onClose` i.e. `onClose` return a reason `escapeKeyDown`.

export const closeButtonCode = `() => {
      const [open, setOpen] = useState(false);
      const triggerButtonRef=useRef(null);
      return (
        <>
          <Button
            id="trigger-button-render-closebutton-id"
            label="Modal prevent rendering of Close IconButton"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerButtonRef}
          />
          <Modal
            size="md"
            open={open}
            id="modal-render-closebutton-id"
            onClose={(e) => {
              if (!e || e.reason!=='escapeKeyDown') { 
                setOpen(false); // check if the return reason is 'escapeKeyDown'                 
                // When the Modal closes, we must return the focus back to the trigger button.
                triggerButtonRef && triggerButtonRef.current.focus();
              }
            }}
            ariaLabelledBy="modal-header-render-closebutton-id"
            ariaDescribedBy="modal-body-render-closebutton-id"
          >
            <ModalHeader
              title="Heading"
              id="modal-header-render-closebutton-id"
            ></ModalHeader>
            <ModalBody id="modal-body-render-closebutton-id">
              <ModalContentText id="modal-body-content-render-closebutton-id" content="Content text"></ModalContentText>
            </ModalBody>
            <ModalFooter id="modal-footer-render-closebutton-id">
              <ModalFooterActions
                id="modal-action-buttons-id"
                primaryAction={{
                  id: 'primary-button-id',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
}`;

<CodeExample scope={scope} code={closeButtonCode} />

### Use `useEverestContext` to dynamically size the Modal

This example demonstrates a responsive Modal by dynamically updating the `size` prop using Everest Provider's `breakpoint` property from the `useEverestContext` hook.

export const responsiveModal = `() => {
  const { breakpoint } = useEverestContext();
  const [size, setSize] = useState('md');
  const [open, setOpen] = useState(false);
  const triggerButtonRef=useRef(null);
  useEffect(() => {
    switch (breakpoint) {
      case 'xs':
        setSize('fullscreen');
        break;
      case 'sm':
        setSize('sm');
        break;
      case 'md':
        setSize('md');
        break;
      case 'lg':
      case 'xl':
      case 'xxl':
        setSize('lg');
        break;
      default:
        setSize('md');
    }
  }, [breakpoint]);
  return (
    <>
      <Button
        id="trigger-button-responsive-id"
        label={'Responsive Example (Breakpoint: ' + breakpoint + ') (Size: ' + size + ')'}
        onClick={() => {
          setOpen(true);
        }}
        ref={triggerButtonRef}
      />
      <Modal
        open={open}
        id="modal-responsive-id"
        onClose={(e) => {
            setOpen(false);
            // When the Modal closes, we must return the focus back to the trigger button.
            triggerButtonRef && triggerButtonRef.current.focus();
        }}
        ariaLabelledBy="modal-header-responsive-id"
        ariaDescribedBy="modal-body-responsive-id"
        size={size}
      >
        <ModalHeader
          title={<h3 id="main-menu-header" className='evrHeading3'>Breakpoint: {breakpoint} <br /> Size: {size}</h3>}
          onCloseButtonClick={(e) => {
            setOpen(false);
          }}
          id="modal-header-responsive-id"
          closeButtonAriaLabel={'Close Modal'}
        ></ModalHeader>
        <ModalBody id="modal-body-responsive-id">
          <ModalContentText id="modal-body-content-responsive-id" content={<p className="evrBodyText" style={{ margin: '15px' }}>Altering the size of the window will dynamically update the breakpoint value and the size of the Modal.</p>}></ModalContentText>
        </ModalBody>
        <ModalFooter id="modal-footer-responsive-id">
          <ModalFooterActions
            id="modal-action-buttons-responsive-id"
            primaryAction={{
              id: 'primary-button-responsive-id',
              label: 'Continue',
              onClick: () => alert('Continue'),
            }}
            secondaryAction={{
              label: 'Close',
              onClick: () => setOpen(false),
            }}
          ></ModalFooterActions>
        </ModalFooter>
      </Modal>
    </>
  );
}`;

<CodeExample scope={scope} code={responsiveModal} />
