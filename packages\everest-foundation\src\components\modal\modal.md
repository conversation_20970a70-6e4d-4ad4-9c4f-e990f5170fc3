# Modal

## Summary

Research and document implementations for the Everest Modal.

- Start Date: 2023-03-13
- Figma link: https://www.figma.com/file/A38hY1rPdCEZNVe7fZjQfY/%F0%9F%A7%AAModal?t=kuOyBeH6WjrJB0hC-0

## Detailed Design

Modal anatomy consists of the following and is divided into sections:

- **Modal Base (mandatory):** - The parent container which sets the height of the Modal.
- **Header (mandatory):** - Heading of the Modal and a mandatory close button.
- **Body (mandatory):** - Takes the Content Text as a child.
- **Content Text (mandatory):** - Content of the Modal.
- **Footer (mandatory):** - At least one button (primary button) with a maximum of three buttons (primary, secondary, and tertiary buttons).

## API

### Modal

1.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the modal.
2.  **id**: `string`
    Mandatory. Sets the id of the modal.
3.  **children**: `ReactNode | undefined`
    Optional. Sets the section contents of the modal.
4.  **open**: `boolean`
    Open the Modal when it is set to true. Close the Modal when it is set to false.
5.  **onOpen**: `() => void | undefined`
    Optional. Sets a callback function that is executed when Modal is opened.
6.  **onClose**: `() => void | undefined`
    Optional. Sets a callback function that is executed when Modal is closed.

### ModalHeader

1.  **id**: `string`
    Mandatory. Sets the id of the header.
2.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the header.
3.  **title**: `string`
    Mandatory. Sets the heading.
4.  **onCloseButtonClick**: `() => void | undefined`
    Optional. Sets a callback function that is executed on click of the close button.

### ModalBody

1.  **id**: `string`
    Mandatory. Sets the id of the body.
2.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the body.
3.  **children**: `ReactNode | undefined`
    Optional. Sets the content of the modal.

### ModalContentText

1.  **id**: `string`
    Mandatory. Sets the id of the content text.
2.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the content text.
3.  **content**: `string`
    Mandatory. Sets the content text of the modal.

### ModalFooter

1.  **id**: `string`
    Mandatory. Sets the id of the footer.
2.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the footer.
3.  **primaryActions** : `[{label:string,ariaLabel:string, onClick:()=>void}]`
    Mandatory. Sets the label, a callback function when clicked and **aria-label** of the primary button.
4.  **secondaryActions** : `[{label:string,ariaLabel:string, onClick:()=>void}]`
    Optional. Sets the label, a callback function when clicked and **aria-label** of the secondary button.
5.  **tertiaryActions** : `[{label:string,ariaLabel:string, onClick:()=>void}]`
    Optional. Sets the label, a callback function when clicked and **aria-label** of the tertiary button.

## Usage

```
()=>{
    const [open, setOpen] = useState(false);
    return (
        <Modal id="modal-id"  onClose={} onOpen={} open={open}>
            <ModalHeader id="header-id" title="Heading" onCloseButtonClick={()=>{setOpen(false)}} />
            <ModalBody>
                 <ModalContentText id="content-text-id" content="Text" />
            </ModalBody>
            <ModalFooter id="footer-id" primaryActions={[{label:"Continue", ariaLabel:"altText", onClick=()=>{alert('button clicked')}}]} />
        </Modal>
    )
}

```

ModalHeader uses an internal OverlayHeader component

```
const ModalHeader = ({ content } ) => {
    return (
        <OverlayHeader id="overlay-header-id" title="Heading" onCloseButtonClick={()=>{setOpen(false)}} />
    )
}

```

ModalBody handles:

Scrolling
Breakpoints
Other styling (if it comes up)
etc.

## Accessibility

- When the buttons are focused, the purpose of the buttons is announced using **ariaLabel** set in **primaryActions**, **secondaryActions**, and **tertiaryActions**.
- Screenreaders by default will announce the heading and content text when focused.
- <ESC> keyboard button is set to close the modal.

The order of the focus is yet to be decided.

## Q&A

**Should the header always have a close button**  
Yes, it is mandatory.

**Can we have more than two buttons**
Yes, Modal should contain at least one button (primary button) and a maximum of three buttons (primary, secondary, and tertiary buttons).

**How should content text handle a long text**
The long text shouldn't affect the height of the container. A scroll effect should be enabled for the context text section only.

**what is the max-height of the container**
Shouldn't exceed 50% of the browser size.

**What should be the width and height of the container**
Modal supports three different sizes, small, medium, and large. These sizes are set based on the screen breakpoints.

**Does primartyActions and secondaryActions accepts more than one element**
Array is restricted to accept only one element.

**What happens when the Modal is closed**
Focus should go back to the element that triggered the Modal.

**What happens when the browser size is less than 375px**
Modal format should switch to the mobile screen.

**Should Modal be closed when clicked on lightbox**
No. Modal can be only closed when ESC button is pressed, by clicking on the "X" icon button(in the header) or by closing the cancel button. The functionality of ESC and "X" will be included in the logic, but the dev should take care of the cancel button, i.e. they need to set the **open** prop to false.

## Future Considerations

Modal should be programmed to handle new sections. The current content text section is restricted to show text only. If another component (for example Image) should be included in the Modal then create a new section under the Modal base.

## Other Design Systems

**Material UI** - https://mui.com/material-ui/react-dialog/

- Supports different sizes and scrolling of the content.

**Spectrum** - https://react-spectrum.adobe.com/react-spectrum/Dialog.html

- Supports different sections. We are using this structure as a reference to build our modal.

## Required PBIs

**2631** - Modal architecture
**2869** - Create Modal component
**2896** - Manual, unit, visual and playwright test.
**2897** - Add Modal to ref app

## Acceptance Criteria

- Component to be named `<Modal>`
- Build a component and setup in Storybook (Components, Foundations, Automation)
- Styles are on par with Design specs in Figma
- Verify the following:
  - API works as intended
    - testId
    - children
    - title
    - primaryActions
    - secondaryActions
    - onCloseButtonClick
    - open
    - onOpen
    - onClose
  - Verify modal can take in custom child components
  - Accessibility (aria attributes, tab order, screen reader callouts, mouse, and keyboard interactivity)
- Unit and integration tests implemented

## Changelogs

10/10/2023 - Changed the mobile view breakpoint from 375px to 430px.

10/11/2023: EDS-3564 - updated onClick callback to return a reason 'escapeKeyDown' when escape button is pressed.

10/23/2023: EDS-3620 - added a new `size` prop.

05/16/2024: AXS-437 - changed the `role` from `alertdialog` to `dialog`.

02/26/2024: EDS-3668 - added a new optional `tertiaryActions` prop to `ModalFooterActions` to include a tertiary button.

07/02/2024: EDS-4113 - updated border radius to be `8px` instead of `16px`.

09/05/2024: EDS-3986 - update size prop to mandatory
