@use '../../variables.scss' as variables;

.evrModalLightBox {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin smallWidth {
  &.evrModal {
    width: variables.$modalSmallWidth;
    border-radius: var(--evr-radius-2xs);

    & .evrModalActionButtons {
      flex-direction: row;
      align-items: unset;
      justify-content: end;
      & button {
        width: auto;
      }
    }
  }
}

@mixin mediumWidth {
  &.evrModal {
    width: variables.$modalMediumWidth;

    & .containerBodyFooter {
      overflow-y: hidden;
      display: flex;
      flex-direction: column;
    }

    & .evrModalBody {
      overflow-y: auto;
      flex-grow: 1;
    }

    & .evrModalActionButtons {
      flex-direction: row;
      align-items: unset;
      justify-content: end;
      & button {
        width: auto;
      }
    }
  }

  .evrModalTopGap {
    height: 25%;
  }
}

@mixin largeWidth {
  &.evrModal {
    width: variables.$modalLargeWidth;
    padding: 0 var(--evr-spacing-2xs);
  }
}

.evrModal {
  width: 100vw; // fallback for dvw
  width: 100dvw;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: var(--evr-surfaces-primary-default);
  top: 0;
  overflow: hidden;
  outline: none;

  & .evrModalHeader {
    padding: var(--evr-spacing-sm);
  }

  & .containerBodyFooter {
    overflow-y: auto;
  }

  & .evrModalBody {
    overflow-y: hidden;
    padding: 0 var(--evr-spacing-sm);
  }

  & .evrModalFooter {
    padding: var(--evr-spacing-sm);
    padding-bottom: var(--evr-spacing-md);
  }

  @media (min-width: variables.$overlayMobileScreenWidth) {
    & .evrModalFooter {
      padding: var(--evr-spacing-sm);
    }
  }

  & .evrModalActionButtons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--evr-spacing-2xs);
    position: relative;
    & button {
      width: 100%;
    }
  }

  &.sm {
    @include smallWidth;
  }

  &.md {
    @extend .sm;
    @include mediumWidth;
  }

  &.lg {
    @extend .md;
    @include largeWidth;
  }
}

.evrModalFooterActionButtonsFiller {
  flex-grow: 1;
  margin: calc(var(--evr-size-2xs) * -1);
}
