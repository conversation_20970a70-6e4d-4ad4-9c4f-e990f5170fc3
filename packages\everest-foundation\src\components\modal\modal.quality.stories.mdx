import { <PERSON><PERSON>, <PERSON>, <PERSON>vas } from '@storybook/addon-docs';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalBody, ModalContentTex<PERSON>, <PERSON><PERSON><PERSON>ooter, ModalFooterActions } from '.';
import { Button } from '../button';
import { useRef } from 'react';
import { screen } from '@storybook/test';
import { Chromatic, defaultModes } from '../../../chromatic';
import { Icon, IconButton } from '../../../src';

<Meta
  title="Testing/Automation Test Cases/Modal"
  component={Modal}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: { breakpointXs: { disable: true } }, // turn off default and specify per story
    },
  }}
  args={{
    id: 'modal-id',
    testId: 'modal-test-id',
    open: true,
    size: 'md',
  }}
/>

# Modal

## Live Demo

export const CustomModalHeader = ({ title }) => (

<section
  style={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 'var(--evr-spacing-sm)',
  }}
>
  <div
    style={{
      display: 'flex',
      alignItems: 'center',
      gap: 'var(--evr-spacing-2xs)',
    }}
  >
    <Icon name="information" fill="--evr-content-primary-highemp" />
    <h3 className="evrHeading3">{title}</h3>
  </div>
  <IconButton id="header-close-button" iconName="x" variant="tertiaryNeutral" ariaLabel="close header" />
</section>

)

<Canvas>
  <Story
    name="Fullscreen Size"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const button = screen.getByRole('button', { name: 'Continue' });
      await button.focus();
    }}
    parameters={{
      chromatic: { modes: { breakpointXs: { disable: false } } } /* re-enable breakpointXs for fullscreen only */,
    }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-id"
          ariaDescribedBy="modal-body-id"
          size="fullscreen"
        >
          <ModalHeader
            title="Heading"
            onCloseButtonClick={() => {
              null;
            }}
            id="modal-header-id"
            closeButtonAriaLabel="Close Modal"
          ></ModalHeader>
          <ModalBody id="modal-body-id">
            <ModalContentText id="modal-body-content-id" content="Content text"></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Continue',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Close',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="SM Size"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const button = screen.getByRole('button', { name: 'Continue' });
      await button.focus();
    }}
    parameters={{ chromatic: { modes: { breakpointSm: defaultModes['breakpointSm'] } } }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-id"
          ariaDescribedBy="modal-body-id"
          size="sm"
        >
          <ModalHeader
            title="Heading"
            onCloseButtonClick={() => {
              null;
            }}
            id="modal-header-id"
            closeButtonAriaLabel="Close Modal"
          ></ModalHeader>
          <ModalBody id="modal-body-id">
            <ModalContentText id="modal-body-content-id" content="Content text"></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Continue',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Close',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="MD Size"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const button = screen.getByRole('button', { name: 'Continue' });
      await button.focus();
    }}
    parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-id"
          ariaDescribedBy="modal-body-id"
        >
          <ModalHeader
            title="Heading"
            onCloseButtonClick={() => {
              null;
            }}
            id="modal-header-id"
            closeButtonAriaLabel="Close Modal"
          ></ModalHeader>
          <ModalBody id="modal-body-id">
            <ModalContentText id="modal-body-content-id" content="Content text"></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Continue',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Close',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="LG Size"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const button = screen.getByRole('button', { name: 'Continue' });
      await button.focus();
    }}
    parameters={{
      chromatic: {
        modes: {
          breakpointLg: defaultModes['breakpointLg'],
          breakpointXl: defaultModes['breakpointXl'],
          breakpointXxl: defaultModes['breakpointXxl'],
        },
      },
    }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-id"
          ariaDescribedBy="modal-body-id"
          size="lg"
        >
          <ModalHeader
            title="Heading"
            onCloseButtonClick={() => {
              null;
            }}
            id="modal-header-id"
            closeButtonAriaLabel="Close Modal"
          ></ModalHeader>
          <ModalBody id="modal-body-id">
            <ModalContentText id="modal-body-content-id" content="Content text"></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Continue',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Close',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Modal Long Content Text(Scrollable body) - Fullscreen size"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const button = screen.getByRole('button', { name: 'Continue' });
      await button.focus();
    }}
    parameters={{
      chromatic: { modes: { breakpointXs: { disable: false } } } /* re-enable breakpointXs for fullscreen only */,
    }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-id"
          ariaDescribedBy="modal-body-id"
          size="fullscreen"
        >
          <ModalHeader
            title="Heading"
            onCloseButtonClick={() => {
              null;
            }}
            id="modal-header-id"
            closeButtonAriaLabel="Close Modal"
          ></ModalHeader>
          <ModalBody id="modal-body-id">
            <ModalContentText content="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur dignissim pharetra turpis, non vulputate turpis pellentesque at. Fusce vel ligula a libero volutpat fermentum. Proin varius orci sit amet suscipit rutrum. Curabitur blandit eleifend nisi, nec molestie dolor efficitur sit amet. Sed ut convallis nisi. Integer convallis turpis sit amet libero semper fermentum. Nunc lobortis efficitur arcu ut tincidunt. Interdum et malesuada fames ac ante ipsum primis in faucibus. Donec in nulla eu mi feugiat feugiat. Praesent eu risus ullamcorper, iaculis risus elementum, viverra felis. Aenean facilisis tempor turpis quis tempus. Sed auctor elementum tortor eget finibus. Suspendisse iaculis odio ut velit rhoncus, non sagittis quam eleifend. Nulla ut arcu a tellus tempus posuere quis eu justo. Morbi nunc lacus, commodo at semper eget, molestie non lacus. Nulla dui est, molestie vitae mauris in, mollis finibus felis. Praesent vehicula viverra sapien. Vestibulum non quam pellentesque, pharetra ligula quis, finibus mi. Proin fermentum finibus lacus sed ullamcorper. In hac habitasse platea dictumst. Nunc tincidunt libero sit amet libero aliquam mattis. Integer sit amet consectetur leo. Proin dictum, nulla eget consectetur ornare, elit enim porttitor velit, sed porttitor elit dui eget mi. Proin porttitor, velit ut fermentum laoreet, massa nibh pharetra urna, quis fermentum justo felis sed ex. Sed maximus diam quis massa fermentum rhoncus."></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Continue',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Close',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Modal Long Content Text(Scrollable body) - size MD"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const button = screen.getByRole('button', { name: 'Continue' });
      await button.focus();
    }}
    parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-id"
          ariaDescribedBy="modal-body-id"
        >
          <ModalHeader
            title="Heading"
            onCloseButtonClick={() => {
              null;
            }}
            id="modal-header-id"
            closeButtonAriaLabel="Close Modal"
          ></ModalHeader>
          <ModalBody id="modal-body-id">
            <ModalContentText content="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur dignissim pharetra turpis, non vulputate turpis pellentesque at. Fusce vel ligula a libero volutpat fermentum. Proin varius orci sit amet suscipit rutrum. Curabitur blandit eleifend nisi, nec molestie dolor efficitur sit amet. Sed ut convallis nisi. Integer convallis turpis sit amet libero semper fermentum. Nunc lobortis efficitur arcu ut tincidunt. Interdum et malesuada fames ac ante ipsum primis in faucibus. Donec in nulla eu mi feugiat feugiat. Praesent eu risus ullamcorper, iaculis risus elementum, viverra felis. Aenean facilisis tempor turpis quis tempus. Sed auctor elementum tortor eget finibus. Suspendisse iaculis odio ut velit rhoncus, non sagittis quam eleifend. Nulla ut arcu a tellus tempus posuere quis eu justo. Morbi nunc lacus, commodo at semper eget, molestie non lacus. Nulla dui est, molestie vitae mauris in, mollis finibus felis. Praesent vehicula viverra sapien. Vestibulum non quam pellentesque, pharetra ligula quis, finibus mi. Proin fermentum finibus lacus sed ullamcorper. In hac habitasse platea dictumst. Nunc tincidunt libero sit amet libero aliquam mattis. Integer sit amet consectetur leo. Proin dictum, nulla eget consectetur ornare, elit enim porttitor velit, sed porttitor elit dui eget mi. Proin porttitor, velit ut fermentum laoreet, massa nibh pharetra urna, quis fermentum justo felis sed ex. Sed maximus diam quis massa fermentum rhoncus."></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Continue',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Close',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Modal prevent rendering of Close IconButton"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const button = screen.getByRole('button', { name: 'Continue' });
      await button.focus();
    }}
    parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-render-closebutton-id"
          ariaDescribedBy="modal-body-render-closebutton-id"
        >
          <ModalHeader title="Heading" id="modal-header-render-closebutton-id"></ModalHeader>
          <ModalBody id="modal-body-render-closebutton-id">
            <ModalContentText content="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur dignissim pharetra turpis, non vulputate turpis pellentesque at. Fusce vel ligula a libero volutpat fermentum. Proin varius orci sit amet suscipit rutrum. Curabitur blandit eleifend nisi, nec molestie dolor efficitur sit amet. Sed ut convallis nisi. Integer convallis turpis sit amet libero semper fermentum. Nunc lobortis efficitur arcu ut tincidunt. Interdum et malesuada fames ac ante ipsum primis in faucibus. Donec in nulla eu mi feugiat feugiat. Praesent eu risus ullamcorper, iaculis risus elementum, viverra felis. Aenean facilisis tempor turpis quis tempus. Sed auctor elementum tortor eget finibus. Suspendisse iaculis odio ut velit rhoncus, non sagittis quam eleifend. Nulla ut arcu a tellus tempus posuere quis eu justo. Morbi nunc lacus, commodo at semper eget, molestie non lacus. Nulla dui est, molestie vitae mauris in, mollis finibus felis. Praesent vehicula viverra sapien. Vestibulum non quam pellentesque, pharetra ligula quis, finibus mi. Proin fermentum finibus lacus sed ullamcorper. In hac habitasse platea dictumst. Nunc tincidunt libero sit amet libero aliquam mattis. Integer sit amet consectetur leo. Proin dictum, nulla eget consectetur ornare, elit enim porttitor velit, sed porttitor elit dui eget mi. Proin porttitor, velit ut fermentum laoreet, massa nibh pharetra urna, quis fermentum justo felis sed ex. Sed maximus diam quis massa fermentum rhoncus."></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-render-closebutton-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Continue',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Close',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Modal with three buttons"
    parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}
  >
    {(args) => {
      return (
        <Modal
          {...args}
          onClose={() => {
            null;
          }}
          ariaLabelledBy="modal-header-three-buttons-id"
          ariaDescribedBy="modal-body-three-buttons-id"
        >
          <ModalHeader title="Heading" id="modal-header-three-buttons-id"></ModalHeader>
          <ModalBody id="modal-body-three-buttons-id">
            <ModalContentText content="Modal with primary, secondary, and tertiary buttons"></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-footer-three-buttons-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Primary',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Secondary',
                onClick: () => null,
              }}
              tertiaryAction={{
                label: 'Tertiary',
                onClick: () => null,
              }}
            ></ModalFooterActions>
          </ModalFooter>
        </Modal>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Modal With Custom Header"
    parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}
  >
    {(args) => {
      return (
        <Modal {...args}>

          <CustomModalHeader title="Modal With Custom Header" />

          <ModalBody id="modal-custom-header-body-id">
            <ModalContentText content="Only the header is custom"></ModalContentText>
          </ModalBody>
          <ModalFooter id="modal-custom-header-footery-id">
            <ModalFooterActions
              id="modal-action-buttons-id"
              primaryAction={{
                id: 'primary-button-id',
                label: 'Primary',
                onClick: () => alert('Continue'),
              }}
              secondaryAction={{
                label: 'Secondary',
                onClick: () => null,
              }}
            >
            </ModalFooterActions>
          </ModalFooter>

        </Modal>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story
    name="Modal With Custom Contents"
    parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}
  >
    {(args) => {
      return (
        <Modal {...args}>

          <CustomModalHeader title="Modal With Custom Content" />

          <section style={{padding: "0 var(--evr-spacing-sm)"}}>
            <p className="evrBodyText">Custom Modal without <b>ModalHeader</b>, <b>ModalBody</b>, and <b>ModalFooter</b> </p>
          </section>

          <section style={{display: "flex", justifyContent: "end", gap: "var(--evr-spacing-2xs)", padding: "1em"}}>
            <Button style={{marginRight:"1em"}} label='Custom Button' onClick = {()=>{alert("continue")}} />
            <Button style={{marginLeft: "1em"}} disabled label='Disabled Custom Button' />
          </section>

        </Modal>
      );
    }}

  </Story>
</Canvas>
