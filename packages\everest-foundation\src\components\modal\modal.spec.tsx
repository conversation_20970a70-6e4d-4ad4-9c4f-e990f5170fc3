import React from 'react';
import { screen, render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Modal, ModalBody, ModalContentText, ModalHeader, ModalFooter, ModalFooterActions, TModalSize } from '.';

const modalId = 'modal-id';
const modalHeaderId = 'modal-header-id';
const modalBodyId = 'modal-body-id';
const modalFooterId = 'modal-footer-id';

const modalTestId = 'modal-test-id';
const modalHeaderTestId = 'modal-header-test-id';
const modalBodyTestId = 'modal-body-test-id';
const modalContentTextTestid = 'modal-content-text-test-id';
const modalFooterTestid = 'modal-footer-test-id';
const modalFooterActionsTestid = 'modal-footer-actions-test-id';
const onOpen = jest.fn();
const onClose = jest.fn();
const contentText = 'Content text';
const onDocumentClick = jest.fn();
const onDocumentKeyDown = jest.fn();

const modalHeaderTitle = 'Heading';
const modalExtraTagText = 'Extra HTML Children';
const onCloseReasonEscapeKey = 'escapeKeyDown';

const mockProps = {
  id: modalId,
  testId: modalTestId,
  open: true,
  onOpen,
  onClose,
  ariaLabelledBy: modalHeaderId,
  ariaDescribedBy: modalFooterId,
  size: 'md' as TModalSize,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const modalTestComponent = (open: boolean, args?: any | undefined[]) => {
  return (
    <Modal {...mockProps} open={open} testId={modalTestId} {...args}>
      <ModalHeader
        title={modalHeaderTitle}
        onCloseButtonClick={() => undefined}
        id={modalHeaderId}
        testId={modalHeaderTestId}
        closeButtonAriaLabel={''}
      />
      <b>{modalExtraTagText}</b>
      <ModalBody id={modalBodyId} testId={modalBodyTestId}>
        <ModalContentText id="modal-content-text-id" testId={modalContentTextTestid} content={contentText} />
      </ModalBody>
      <ModalFooter id={modalFooterId} testId={modalFooterTestid}>
        <ModalFooterActions
          id="modal-action-buttons-id"
          testId={modalFooterActionsTestid}
          primaryAction={{
            id: 'primary-button-id',
            label: 'Continue',
            onClick: () => undefined,
          }}
        />
      </ModalFooter>
    </Modal>
  );
};

describe('[Modal]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all the children when modal is visible', () => {
    const { getByTestId } = render(modalTestComponent(true));
    expect(getByTestId(modalTestId)).toBeInTheDocument();
    expect(getByTestId(modalHeaderTestId)).toBeInTheDocument();
    expect(getByTestId(modalBodyTestId)).toBeInTheDocument();
    expect(getByTestId(modalContentTextTestid)).toBeInTheDocument();
    expect(getByTestId(modalFooterTestid)).toBeInTheDocument();
    expect(getByTestId(modalFooterActionsTestid)).toBeInTheDocument();
    expect(screen.getByText(modalExtraTagText)).toBeInTheDocument();
  });

  it('should dispatch onClose when modal is open then closes', async () => {
    const { rerender } = render(modalTestComponent(true));
    await waitFor(() => expect(onOpen).not.toHaveBeenCalled());
    rerender(modalTestComponent(false));
    await waitFor(() => expect(onClose).toHaveBeenCalled());
  });

  it('should dispatch onOpen when modal is closed then opens', async () => {
    const { rerender } = render(modalTestComponent(false));
    expect(onClose).not.toHaveBeenCalled();
    rerender(modalTestComponent(true));
    await waitFor(() => expect(onOpen).toHaveBeenCalled());
  });

  it('should run onOpen once and onClose once when starting closed, then opened, then closed ', async () => {
    const { rerender } = render(modalTestComponent(false));
    expect(onClose).not.toHaveBeenCalled();
    rerender(modalTestComponent(true));
    await waitFor(() => expect(onOpen).toHaveBeenCalledTimes(1));
    rerender(modalTestComponent(false));
    await waitFor(() => expect(onClose).toHaveBeenCalledTimes(1));
  });

  it('should run onOpen once and onClose once when starting open, then closed ', async () => {
    const { rerender } = render(modalTestComponent(true));
    await waitFor(() => expect(onOpen).toHaveBeenCalled());
    expect(onClose).not.toHaveBeenCalled();
    rerender(modalTestComponent(false));
    await waitFor(() => {
      expect(onClose).toHaveBeenCalledTimes(1);
      expect(onOpen).toHaveBeenCalledTimes(1);
    });
  });

  it('should focus the modal content when onOpen is undefined', async () => {
    render(modalTestComponent(true, { onOpen: undefined }));
    await waitFor(() => {
      expect(document.activeElement?.id).toBe(modalId);
    });
  });
  it('onClose should return a reason when Escape keyboard button is pressed', async () => {
    render(modalTestComponent(true, { onOpen: undefined }));
    await waitFor(() => {
      expect(screen.getByText(modalHeaderTitle)).toBeInTheDocument();
    });
    userEvent.keyboard('{Escape}');
    await waitFor(() => {
      expect(onClose).toHaveBeenCalledWith({ reason: onCloseReasonEscapeKey });
    });
  });
  it('should not allow click events to propagate to its parent ', () => {
    document.addEventListener('click', onDocumentClick);
    render(modalTestComponent(true, { onOpen: undefined }));
    screen.getByText(contentText).click();
    expect(onDocumentClick).not.toHaveBeenCalled();
    document.removeEventListener('click', onDocumentClick);
  });
  it('should not allow keydown events to propagate to its parent ', async () => {
    document.addEventListener('keydown', onDocumentKeyDown);
    render(modalTestComponent(true, { onOpen: undefined }));
    await waitFor(() => {
      expect(document.activeElement?.id).toBe(modalId);
    });
    userEvent.keyboard('a');
    expect(onDocumentKeyDown).not.toHaveBeenCalled();
    document.removeEventListener('keydown', onDocumentKeyDown);
  });
  it('data-focus-lock-disabled should be set to "disabled" when disableFocusTrap is true', async () => {
    render(modalTestComponent(true, { onOpen: undefined, disableFocusTrap: true }));
    await waitFor(() => {
      expect(document.activeElement?.id).toBe(modalId);
    });
    //Need to get the FocusTrap element, this is done by getting the parent of the modal-id div
    const isFocusLockDisabled =
      document.activeElement?.parentElement?.attributes?.getNamedItem('data-focus-lock-disabled')?.value;
    expect(isFocusLockDisabled).toBe('disabled');
  });
  it('data-focus-lock-disabled should be set to "false" when disableFocusTrap is false', async () => {
    render(modalTestComponent(true, { onOpen: undefined, disableFocusTrap: false }));
    await waitFor(() => {
      expect(document.activeElement?.id).toBe(modalId);
    });
    //Need to get the FocusTrap element, this is done by getting the parent of the modal-id div
    const isFocusLockDisabled =
      document.activeElement?.parentElement?.attributes?.getNamedItem('data-focus-lock-disabled')?.value;
    expect(isFocusLockDisabled).toBe('false');
  });
});
