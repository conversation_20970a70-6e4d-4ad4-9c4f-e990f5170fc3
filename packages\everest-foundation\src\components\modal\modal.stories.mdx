import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { useState, useEffect, useRef } from 'react';
import { Modal, ModalHeader, ModalBody, ModalContentText, ModalFooter, ModalFooterActions } from '.';
import { Button } from '../button';
import Examples from './modal.examples.mdx';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';

<Meta
  title="Components/Modal"
  component={Modal}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/A38hY1rPdCEZNVe7fZjQfY/%F0%9F%A7%AAModal?node-id=4225-13100&t=vq8s44cfC6E6yqV1-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Sets the `id` attribute on the modal',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets data-testid',
    },
    open: {
      description: 'Opens or closes the modal.',
      table: {
        defaultValue: { summary: false },
      },
      control: '-',
    },
    onClose: {
      control: '-',
      description:
        "Callback that runs when the modal closes. Returns an object {reason: 'escapeKeyDown'} when the escape key is pressed.",
    },
    onOpen: {
      control: '-',
      description: 'Callback that runs when the modal opens.',
    },
    ariaLabelledBy: {
      type: 'string',
      control: '-',
      description: 'Id of the ModalHeader.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: '-',
      description: 'Id of the ModalBody.',
    },
    size: {
      description: 'Sets the fixed size of the Modal.',
      type: 'enum',
      control: 'select',
      options: ['fullscreen', 'sm', 'md', 'lg'],
    },
    disableFocusTrap: {
      control: {
        type: 'boolean',
      },
      table: {
        defaultValue: { summary: false },
      },
      description: 'Determines whether the focus trap is disabled.',
    },
  }}
  args={{
    id: 'modal-id',
    open: false,
    disableFocusTrap: false,
    size: 'md',
  }}
/>

# Modal

<Examples />

## Live Demo

<Canvas>
  <Story name="Modal">
    {(args) => {
      // useArgs was not used since it creates focus issues
      const [open, setOpen] = useState(false);
      const triggerBtnRef = useRef(null);
      return (
        <>
          <Button
            id="trigger-button-live-demo-id"
            label="Open Modal"
            onClick={() => {
              setOpen(true);
            }}
            ref={triggerBtnRef}
          />
          <Modal
            {...args}
            open={open}
            onOpen={(e) => {
              action('onOpen')(e);
            }}
            onClose={(e) => {
              console.log('onClose e', e);
              action('onClose')(e);
              setOpen(false);
              triggerBtnRef?.current?.focus();
            }}
            ariaLabelledBy="modal-header-live-demo-id"
            ariaDescribedBy="modal-body-live-demo-id"
          >
            <ModalHeader
              title="Test heading"
              onCloseButtonClick={(e) => {
                action('onCloseButtonClick')(e);
                setOpen(false);
              }}
              id="modal-header-live-demo-id"
              closeButtonAriaLabel="Close Modal"
            ></ModalHeader>
            <ModalBody id="modal-body-live-demo-id">
              <ModalContentText id="modal-body-content-live-demo-id" content="Test content" />
            </ModalBody>
            <ModalFooter id="modal-footer-live-demo-id">
              <ModalFooterActions
                id="modal-action-buttons-live-demo-id"
                primaryAction={{
                  id: 'primary-button-live-demo-id',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  id: 'close-button-live-demo-id',
                  label: 'Close',
                  onClick: () => setOpen(false),
                }}
                tertiaryAction={{
                  id: 'tertiary-button-live-demo-id',
                  label: 'Save as Draft',
                  onClick: () => alert('Draft saved'),
                }}
              ></ModalFooterActions>
            </ModalFooter>
          </Modal>
        </>
      );
    }}
  </Story>
</Canvas>

## Props

### Modal Props

<ArgsTable story="Modal" />

### ModalHeader Props

<ArgsTable of={ModalHeader} />

### ModalBody Props

<ArgsTable of={ModalBody} />

### ModalContentText Props

<ArgsTable of={ModalContentText} />

### ModalFooter Props

<ArgsTable of={ModalFooter} />

### ModalFooterActions Props

<ArgsTable of={ModalFooterActions} />
