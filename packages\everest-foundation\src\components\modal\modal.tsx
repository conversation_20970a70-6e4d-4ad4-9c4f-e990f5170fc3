import React, { PropsWithChildren, ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import classnames from 'classnames';

import { ModalBody } from './modal-body';
import { ModalFooter } from './modal-footer';
import { mergeRefs } from '../../utils';
import { FocusTrap } from '../../utils/focus-trap';
import { IDialogBase, Overlay, TDialogCloseEventReason } from '../overlay';
import { useWindowSizeContext } from '../window-size-provider';

import styles from './modal.module.scss';

export type TModalSize = 'fullscreen' | 'sm' | 'md' | 'lg';
export interface IModal extends IDialogBase {
  size: TModalSize;
  ariaLabelledBy: string; // redefining forces the prop to be required
  ariaDescribedBy: string; // redefining forces the prop to be required
  disableFocusTrap?: boolean;
}

export const Modal = React.forwardRef<HTMLDivElement, PropsWithChildren<IModal>>((props, ref): JSX.Element => {
  const {
    id,
    testId,
    children,
    disableFocusTrap = false,
    open = false,
    size,
    onOpen,
    onClose,
    ariaLabelledBy,
    ariaDescribedBy,
  } = props;
  const { innerHeight } = useWindowSizeContext();
  const isFullscreen = size === 'fullscreen';
  const percentageOfViewport = useRef(0.75);
  const modalContainerRef = useRef<HTMLDivElement>(null);
  const reasonRef = useRef<TDialogCloseEventReason | null>(null);
  const onOpenPrevRef = useRef(open ? null : false);
  const [containerCss, setContainerCss] = useState({
    height: isFullscreen ? innerHeight : 'auto',
    maxHeight: isFullscreen ? innerHeight : innerHeight * percentageOfViewport.current,
  });
  const otherChildren: ReactNode[] = [];
  const handleLightBoxKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      e.stopPropagation();
      if (e.key === 'Escape' && open) {
        setTimeout(() => {
          reasonRef.current = 'escapeKeyDown';
          onClose?.({ reason: reasonRef.current });
        });
      }
    },
    [onClose, open]
  );
  const handleLightBoxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // consider running a callback in future instead
    modalContainerRef?.current?.focus();
  };
  const handleOnClickModalContainer = (e: React.MouseEvent) => {
    // prevent clicks from bubbling into the lightbox
    // consider running a callback in future instead
    e.stopPropagation();
  };

  let bodyChild: ReactNode;
  let footerChild: ReactNode;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  React.Children.forEach(children, (child: any) => {
    switch (child.type) {
      case ModalFooter:
        footerChild = child;
        break;
      case ModalBody:
        bodyChild = child;
        break;
      default:
        otherChildren.push(child);
        break;
    }
  });

  useEffect(() => {
    if (open && !onOpenPrevRef.current) {
      // Like SidePanel, we focus on the modal container when it opens.
      // With this, consumers no longer need to manage focus on the container when modal opens.
      // Consumer can override this focus behavior by setting focus behavior in onOpen.
      setTimeout(() => {
        modalContainerRef && modalContainerRef.current?.focus();
      });
      onOpen &&
        setTimeout(() => {
          onOpen();
        });
    } else if (!open && onOpenPrevRef.current) {
      if (reasonRef.current === null) {
        onClose && setTimeout(() => onClose());
      } else {
        reasonRef.current = null;
      }
    }
    onOpenPrevRef.current = open;
  }, [open, onOpen, onClose]);

  useEffect(() => {
    if (
      open &&
      (containerCss.height !== (isFullscreen ? innerHeight : 'auto') ||
        containerCss.maxHeight !== (isFullscreen ? innerHeight : innerHeight * percentageOfViewport.current))
    )
      setContainerCss({
        height: isFullscreen ? innerHeight : 'auto',
        maxHeight: isFullscreen ? innerHeight : innerHeight * percentageOfViewport.current,
      });
  }, [isFullscreen, innerHeight, containerCss.height, containerCss.maxHeight, open]);

  return (
    <>
      {open && (
        <Overlay
          id={`${id}-overlay`}
          testId={testId ? `${testId}-overlay` : undefined}
          open={open}
          fullscreen
          className={styles.evrModalLightBox}
          onLightBoxKeyDown={handleLightBoxKeyDown}
          onLightBoxClick={handleLightBoxClick}
        >
          <FocusTrap
            // eslint-disable-next-line jsx-a11y/no-autofocus
            autoFocus={false}
            disabled={disableFocusTrap}
          >
            {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
            <div
              id={id}
              className={classnames(styles.evrModal, styles[size])}
              // NVDA won't announce the word "dialog" as modal opens due to its own glitch where if dialog container receive focus,
              // then no announcement is made- https://github.com/nvaccess/nvda/issues/8620
              role="dialog"
              aria-labelledby={ariaLabelledBy}
              aria-describedby={ariaDescribedBy}
              data-testid={testId}
              style={containerCss}
              tabIndex={-1}
              ref={mergeRefs([ref, modalContainerRef])}
              onClick={handleOnClickModalContainer}
            >
              {bodyChild && footerChild ? (
                /**
                 * Since our ModalBody and ModalFooter accept any children, consumers are unlikely to use custom versions of them.
                 * If they provide our ModalBody and ModalFooter, we can automatically help them with mobile/scrolling (see styles.containerBodyFooter).
                 * Other custom components are assumed to be the header and rendered before the body.
                 */
                <>
                  {otherChildren}
                  <div className={styles.containerBodyFooter}>
                    {bodyChild}
                    {footerChild}
                  </div>
                </>
              ) : (
                // Can't really help them with mobile/scrolling via styles.containerBodyFooter => render exactly what they provided
                <>{children}</>
              )}
            </div>
          </FocusTrap>
        </Overlay>
      )}
    </>
  );
});

Modal.displayName = 'Modal';
