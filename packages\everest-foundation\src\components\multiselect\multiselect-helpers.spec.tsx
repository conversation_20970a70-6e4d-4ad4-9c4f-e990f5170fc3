import { getFormFieldMaxHeight } from './multiselect-helpers';

import variables from '../../variables.scss';

const maxHeightLineLimit = (lineLimit: number) =>
  `calc(${variables.multiSelectTextFieldBaseMaxHeight} + ((${lineLimit} - 1) * ${variables.multiSelectTextFieldNewLineHeight}))`;
const maxHeightLineLimitWithError = (lineLimit: number) =>
  `calc(${variables.errorMultiSelectTextFieldBaseMaxHeight} + ((${lineLimit} - 1) * ${variables.multiSelectTextFieldNewLineHeight}))`;
const maxHeightLineLimitLowValue = () =>
  `calc(${variables.multiSelectTextFieldBaseMaxHeight} + ((2 - 1) * ${variables.multiSelectTextFieldNewLineHeight}))`;
const maxHeightLineLimitLowValueWithError = () =>
  `calc(${variables.errorMultiSelectTextFieldBaseMaxHeight} + ((2 - 1) * ${variables.multiSelectTextFieldNewLineHeight}))`;

describe('[MultiSelect helpers]', () => {
  describe('getFormFieldMaxHeight', () => {
    it('should return a CSS background height value totalling to the specified number of lines provided by lineLimitWhenClosed parameter', () => {
      expect(getFormFieldMaxHeight(true, 'default', undefined, 4)).toBe(maxHeightLineLimit(4));
    });

    it('should return a CSS background height value totalling to the specified number of lines provided by lineLimitWhenClosed parameter, with thick error border values when error status provided', () => {
      expect(getFormFieldMaxHeight(true, 'error', undefined, 4)).toBe(maxHeightLineLimitWithError(4));
    });

    it('should return a CSS background height value totalling to 2 lines when provided lineLimitWhenClosed value is less than 2', () => {
      expect(getFormFieldMaxHeight(true, 'default', undefined, 1)).toBe(maxHeightLineLimitLowValue());
    });

    it('should return a CSS background height value totalling to 2 lines when provided lineLimitWhenClosed value is less than 2, with thick error border values when error status provided', () => {
      expect(getFormFieldMaxHeight(true, 'error', undefined, 1)).toBe(maxHeightLineLimitLowValueWithError());
    });

    it('should return a CSS background height value totalling to the specified number of lines provided by lineLimitWhenOpen parameter', () => {
      expect(getFormFieldMaxHeight(false, 'default', 5, undefined)).toBe(maxHeightLineLimit(5));
    });

    it('should return a CSS background height value totalling to the specified number of lines provided by lineLimitWhenClosed parameter, with thick error border values when error status provided', () => {
      expect(getFormFieldMaxHeight(false, 'error', 5, undefined)).toBe(maxHeightLineLimitWithError(5));
    });

    it('should return a CSS background height value totalling to 2 lines when provided lineLimitWhenClosed value is less than 2', () => {
      expect(getFormFieldMaxHeight(false, 'default', 1, undefined)).toBe(maxHeightLineLimitLowValue());
    });

    it('should return a CSS background height value totalling to 2 lines when provided lineLimitWhenClosed value is less than 2, with thick error border values when error status provided', () => {
      expect(getFormFieldMaxHeight(false, 'error', 1, undefined)).toBe(maxHeightLineLimitLowValueWithError());
    });

    it('should return undefined when lineLimitWhenOpened or lineLimitWhenClosed is not provided', () => {
      expect(getFormFieldMaxHeight(false, 'default', undefined, undefined)).toBe(undefined);
    });
  });
});
