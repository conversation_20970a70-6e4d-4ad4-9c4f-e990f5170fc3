import { TFormFieldStatus } from '../form-field-container';
import { IDataItem } from '../list-item';

import variables from '../../variables.scss';

// identifies if the element is in the background or foreground
export type TMultiSelectPosition = 'bg' | 'fg';

// keyboard control keys
export type TMultiSelectControlInputKeys = 'ArrowUp' | 'ArrowDown' | ' ' | 'Home' | 'End' | 'Enter' | 'Tab' | null;

/**
 * Ids
 */

export const getInputId = (multiselectId: string, position: TMultiSelectPosition): string =>
  `${multiselectId}-input-${position}`;
export const getOverlayContainerId = (multiselectId: string): string => `${multiselectId}-overlay-container`;
export const getListBoxId = (multiselectId: string): string => `${multiselectId}-list-box`;
export const getFormFieldId = (multiselectId: string, position: TMultiSelectPosition): string =>
  `${multiselectId}-${position}-form-field`;
export const getFormFieldLabelId = (multiselectId: string, position: TMultiSelectPosition): string =>
  `${multiselectId}-label-${position}`;
export const getFormFieldStatusMessageId = (multiselectId: string, position: TMultiSelectPosition): string =>
  `${multiselectId}-status-message-${position}`;
export const getChevronIconId = (multiselectId: string, position: TMultiSelectPosition): string =>
  `${multiselectId}-${position}-select-container-chevron-icon`;
export const getChevronIconTestId = (testId: string, position: TMultiSelectPosition): string =>
  `${testId}-${position}-select-container-icon-wrapper-icon-container`;
export const getInputTestId = (testId: string, position: TMultiSelectPosition): string => `${testId}-${position}`;
export const getClearButtonTestId = (testId: string, position: TMultiSelectPosition): string =>
  `${getFormFieldId(testId, position)}-clear-button`;
export const getTagRemoveButtonId = (selectedOption: IDataItem): string => `${selectedOption.id}-tag-remove-button`;
export const getTagRemoveButtonTestId = (
  testId: string,
  selectedOption: IDataItem,
  position: TMultiSelectPosition
): string => `${testId}-${selectedOption.id}-tag-${position}-remove-button`;
export const getNoResultsId = (multiselectId: string): string => `${multiselectId}-no-results`;

/**
 * DOMRects
 */

export const getTextFieldListBoxBoundingRect = (multiselectId: string): DOMRect | undefined =>
  document.getElementById(`${multiselectId}-text-field-list-box-bg`)?.getBoundingClientRect();
export const getInputBoundingRect = (multiselectId: string): DOMRect | undefined =>
  document.getElementById(getInputId(multiselectId, 'bg'))?.getBoundingClientRect();
export const getSelectedOptionTagBoundingRect = (selectedOption: IDataItem): DOMRect | undefined =>
  document.getElementById(`${selectedOption.id}-tag`)?.getBoundingClientRect();
export const getMoreSelectedOptionLabelBoundingRect = (multiselectId: string): DOMRect | undefined =>
  document.getElementById(`${multiselectId}-more-selected-label-bg`)?.getBoundingClientRect();

// update selections and call onChange as given by user
export function makeSelection(
  selection: IDataItem,
  previousSelections: IDataItem[],
  onChange: (selections: IDataItem[]) => void,
  focusExceptionsRef: React.MutableRefObject<Map<string, boolean>>
): void {
  let result: IDataItem[] = [];
  if (!previousSelections.map((item) => item.id).includes(selection.id)) {
    // use deep copy in anticipation of virtualization
    const selectionsDeepCopy = JSON.parse(JSON.stringify([...previousSelections, selection]));
    result = selectionsDeepCopy;
    // set focus exception to the added tag so useComponentFocused does not trigger an unneeded blur
    // focus exception is not updated when the tag is removed because we don't want the blur the trigger
    // and does not harm to keep it in the map
    focusExceptionsRef.current.set(getTagRemoveButtonId(selection), true);
  } else {
    const prevPositionOfIndex = previousSelections.map((item) => item.id).indexOf(selection.id);
    const prevSelectionsShallowCopy = [...previousSelections];
    prevSelectionsShallowCopy.splice(prevPositionOfIndex, 1);
    result = prevSelectionsShallowCopy;
  }
  onChange(result);
}

/**
 * Helper methods
 */

export function getFormFieldMaxHeight(
  background: boolean,
  status: TFormFieldStatus,
  lineLimitWhenOpened?: number,
  lineLimitWhenClosed?: number
): string | undefined {
  const baseMaxHeight =
    status === 'error' ? variables.errorMultiSelectTextFieldBaseMaxHeight : variables.multiSelectTextFieldBaseMaxHeight;
  // minimum line limit will set to 2, regardless if the value passed is less than 2
  // this is to prevent the input not being able to fit, causing issues with the component
  const effectiveOpenLineLimit = lineLimitWhenOpened && lineLimitWhenOpened < 2 ? 2 : lineLimitWhenOpened;
  const effectiveClosedLineLimit = lineLimitWhenClosed && lineLimitWhenClosed < 2 ? 2 : lineLimitWhenClosed;
  const calculatedMaxLineHeightOnOpened = `calc(${baseMaxHeight} + ((${effectiveOpenLineLimit} - 1) * ${variables.multiSelectTextFieldNewLineHeight}))`;
  const calculatedMaxLineHeightOnClosed = `calc(${baseMaxHeight} + ((${effectiveClosedLineLimit} - 1) * ${variables.multiSelectTextFieldNewLineHeight}))`;

  if (background) {
    if (lineLimitWhenClosed) return calculatedMaxLineHeightOnClosed;
    if (status === 'error') return variables.errorMultiSelectTextFieldMaxHeight;
    return variables.multiSelectTextFieldMaxHeight;
  }

  if (lineLimitWhenOpened) {
    return calculatedMaxLineHeightOnOpened;
  }

  return undefined;
}

// keyboard logic for input (background or foreground/overlay)
export function inputOnKeyDown(
  disabled: boolean,
  readOnly: boolean,
  background: boolean,
  overlayVisible: boolean,
  softSelectedId: string,
  options: IDataItem[],
  lastControlKeyPressed: React.MutableRefObject<TMultiSelectControlInputKeys>,
  event: React.KeyboardEvent,
  setSoftSelectedId: (value: React.SetStateAction<string>) => void,
  setOverlayVisible: (value: React.SetStateAction<boolean>) => void,
  onSelection: (dataItem: IDataItem) => void,
  handleOnEscape: () => void,
  backgroundInputRef: React.RefObject<HTMLInputElement>,
  listBoxRef: React.MutableRefObject<HTMLUListElement | null>,
  inputValue: string
): void {
  if (disabled || readOnly) return;
  // keyboard logic for input in foreground
  if (!background) {
    if (event.key === 'Escape') {
      handleOnEscape();
      if (overlayVisible) event.stopPropagation();
      return;
    }
    if (event.key === 'ArrowUp' && event.altKey) {
      setOverlayVisible(false);
      backgroundInputRef?.current?.focus();
      return;
    }
    if ((event.key === 'ArrowUp' || event.key === 'ArrowDown') && overlayVisible) {
      event.preventDefault();
      const listItem = document.getElementById(softSelectedId);
      let el: ChildNode | null | undefined = null;

      if (event.key === 'ArrowUp') {
        el = listItem?.previousSibling;
      } else {
        el = listItem?.nextSibling;
      }
      el && setSoftSelectedId((el as HTMLElement).id);
      return;
    }
    if (event.key === 'Home' || event.key === 'End') {
      let el: HTMLLIElement | null = null;
      if (!listBoxRef?.current) return;
      event.preventDefault();
      if (event.key === 'Home') {
        el = listBoxRef?.current.firstChild as HTMLLIElement;
      } else {
        el = listBoxRef?.current.lastChild as HTMLLIElement;
      }
      el && setSoftSelectedId(el.id);
      return;
    }
    if (event.key === 'Enter') {
      if (!options.length) return;
      if (softSelectedId) {
        const dataItem = options.find((option) => option.id === softSelectedId);
        dataItem && onSelection(dataItem);
      }
      return;
    }

    if (event.key !== 'Tab' && event.shiftKey) {
      return;
    }

    if (event.key === 'Tab' && event.shiftKey) {
      setOverlayVisible(false);
      backgroundInputRef?.current?.focus();
      return;
    }

    if (event.key === 'Tab') {
      event.preventDefault();
      setOverlayVisible(false);
      backgroundInputRef?.current?.focus();
      return;
    }

    return;
  }
  // keyboard logic for trigger element input
  if (background) {
    if (
      ['ArrowUp', 'ArrowDown', 'Home', 'End', 'Enter'].includes(event.key) ||
      (event.key === ' ' && inputValue === '')
    ) {
      event.preventDefault();
      if (!overlayVisible) setOverlayVisible(true);
      // key has to be remembered and then is logic run
      // after the ul renders -- see inputOnKeyDownOnceUlRendered
      lastControlKeyPressed.current = event.key as TMultiSelectControlInputKeys;
      return;
    }

    if (event.key === 'Tab' || event.key === 'Shift') return;

    return;
  }
}

// keyboard logic for keys pressed on background/trigger input
// that must be run on the ul once rendered
export function inputOnKeyDownOnceUlRendered(
  overlayInputRef: React.RefObject<HTMLInputElement>,
  lastControlKeyPressed: React.MutableRefObject<TMultiSelectControlInputKeys>,
  listBoxRef: React.MutableRefObject<null>,
  setSoftSelectedId: (value: React.SetStateAction<string>) => void,
  setPressedEndKey: (value: React.SetStateAction<boolean>) => void,
  softSelectedId: string
): void {
  if (overlayInputRef?.current) {
    (overlayInputRef?.current as HTMLInputElement).selectionStart = overlayInputRef?.current?.value.length ?? 0;
  }

  if (listBoxRef?.current) {
    let el: HTMLLIElement | null = null;
    if (['ArrowUp', 'ArrowDown', 'Home', ' ', 'Enter'].includes(lastControlKeyPressed.current || '')) {
      if (softSelectedId) return;
      el = (listBoxRef?.current as HTMLUListElement).firstChild as HTMLLIElement;
      el && setSoftSelectedId(el.id);
    }
    if (lastControlKeyPressed.current === 'End') {
      el = (listBoxRef?.current as HTMLUListElement).lastChild as HTMLLIElement;
      el && setSoftSelectedId(el.id);
      // setting the softSelectedId to lastChild takes time
      // so added state in multiselect just for this case
      setPressedEndKey(true);
    }
    lastControlKeyPressed.current = null;
  }
}
