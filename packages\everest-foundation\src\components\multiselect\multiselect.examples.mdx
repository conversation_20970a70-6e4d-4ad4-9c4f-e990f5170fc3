import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { MultiSelect } from './multiselect';
import { Button } from '../button';
import { FunctionUtil } from '@platform/core';
import { Markdown } from '@storybook/blocks';
import IDataItem from '../list-item/i-data-item.md?raw';

export const scope = { MultiSelect, Button, FunctionUtil };

`MultiSelect` presents a list of choices from which the user can make more than one selection.

## Variations

### Default MultiSelect

`MultiSelect` has one default presentation with variations determined by its props.

It is a controlled component with user made selections held in external state.

It shares many of its props and behaviors with <LinkTo kind="Components/Dropdown">Dropdown</LinkTo>.

export const variationsCode = `() => {
  const styles = {
    row: {
      width: '55%',
      marginBottom: '2.5rem',
    },
    column: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
    },
    headerText: {
      marginBlockEnd: '0.625rem',
      width: '70%',
    },
    code: {
      // copied from storybook
      lineHeight: '1',
      margin: '0 2px',
      padding: '3px 5px',
      whiteSpace: 'nowrap',
      borderRadius: '3px',
      fontSize: '13px',
      border: '1px solid #EEEEEE',
      color: 'rgba(51,51,51,0.9)',
      backgroundColor: '#F8F8F8',
    },
  };
  const HeaderText = ({ children }) => (
    <div style={styles.headerText}>
      <p className="evrBodyText">{children}</p>
    </div>
  );
  const firstOption = { id: 'first-id', title: 'First Option' };
  const secondOption = { id: 'second-id', title: 'Second Option' };
  const thirdOption = { id: 'third-id', title: 'Third Option' };
  const options = [firstOption, secondOption, thirdOption];
  const scrollbarOptions = [
    ...options,
    { id: 'fourth-id', title: 'Fourth Option' },
    { id: 'fifth-id', title: 'Fifth Option' },
    { id: 'sixth-id', title: 'Sixth Option' },
  ];
  const textMap = {
    clearButton: 'Clear All',
    removeTagButtonAriaLabel: 'Remove {0}',
    selectedItem: '{0} selected',
    unselectedItem: '{0} not selected',
    moreSelectedOptionsLabel: '+{0} more',
  };
  const [selectedOptions, setselectedOptions] = React.useState([firstOption, secondOption]);
  const [selectedReadOnlyOptions, setSelectedReadOnlyOptions] = React.useState([
    firstOption,
    secondOption,
    thirdOption,
  ]);
  const [inputValue, setInputValue] = React.useState('');
  const ariaListBoxResults = React.useRef(options.length.toString() + ' results found.');
  const handleChange = (values) => {
    setselectedOptions(values);
  };
  const handleClear = () => {
    setselectedOptions([]);
    setInputValue('');
  };
  const handleBlur = () => {
    setInputValue('');
  };
  return (
    <div style={styles.column}>
      <div style={styles.row}>
        <HeaderText>
          <code style={styles.code}>MultiSelect</code> with Label
        </HeaderText>
        <MultiSelect
          id="multiselect-label"
          selectedOptions={[]}
          options={options}
          onChange={(dataItems) => {}}
          onClear={() => {}}
          label="This is a label"
          ariaInputLabel="This is an input arial-label, 0 items selected"
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          <code style={styles.code}>MultiSelect</code> with scrollbar
        </HeaderText>
        <MultiSelect
          id="multiselect-scrollbar"
          selectedOptions={[]}
          options={scrollbarOptions}
          onChange={() => {}}
          onClear={() => {}}
          label="This is a label"
          ariaInputLabel="This is an input arial-label, 0 items selected"
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          <code style={styles.code}>MultiSelect</code> with selected values
        </HeaderText>
        <MultiSelect
          id="multiselect-selected-values"
          selectedOptions={selectedOptions}
          options={options}
          onChange={handleChange}
          onClear={handleClear}
          onBlur={handleBlur}
          ariaInputLabel="This is an input arial-label, 2 items selected"
          label="This is a label"
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          Required <code style={styles.code}>MultiSelect</code> with helper text
        </HeaderText>
        <MultiSelect
          id="multiselect-helper-text"
          selectedOptions={[]}
          options={options}
          onChange={(dataItems) => {}}
          onClear={() => {}}
          ariaInputLabel="This is an input arial-label, 0 items selected"
          label="This is a label"
          helperText="This is helper text"
          helperTextPrefix="Hint: "
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
          required
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          <code style={styles.code}>MultiSelect</code> with an error message
        </HeaderText>
        <MultiSelect
          id="multiselect-error-message"
          selectedOptions={[]}
          options={options}
          onChange={(dataItems) => {}}
          onClear={() => {}}
          ariaInputLabel="This is an input arial-label, 0 items selected"
          label="This is a label"
          status="error"
          statusMessage="This is an error message"
          statusMessagePrefix="Error: "
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          Disabled <code style={styles.code}>MultiSelect</code>
        </HeaderText>
        <MultiSelect
          id="multiselect-disabled"
          selectedOptions={[]}
          options={options}
          onChange={(dataItems) => {}}
          onClear={() => {}}
          label="This is a label"
          ariaInputLabel="This is an input arial-label, 0 items selected"
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
          disabled
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          Disabled <code style={styles.code}>MultiSelect</code> with selected values
        </HeaderText>
        <MultiSelect
          id="multiselect-disabled-selected-values"
          selectedOptions={selectedReadOnlyOptions}
          options={options}
          onChange={handleChange}
          onBlur={handleBlur}
          ariaInputLabel="This is an input arial-label, 2 items selected"
          label="This is a label"
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
          disabled
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          ReadOnly <code style={styles.code}>MultiSelect</code>
        </HeaderText>
        <MultiSelect
          id="multiselect-readOnly"
          selectedOptions={[]}
          options={options}
          onChange={(dataItems) => {}}
          onClear={() => {}}
          label="This is a label"
          ariaInputLabel="This is an input arial-label, 0 items selected"
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
          readOnly
        />
      </div>
      <div style={styles.row}>
        <HeaderText>
          ReadOnly <code style={styles.code}>MultiSelect</code> with selected values
        </HeaderText>
        <MultiSelect
          id="multiselect-readonly-selected-values"
          selectedOptions={selectedReadOnlyOptions}
          options={options}
          onChange={handleChange}
          onBlur={handleBlur}
          ariaInputLabel="This is an input arial-label, 2 items selected"
          label="This is a label"
          noResultsText="No results"
          ariaListboxResult={ariaListBoxResults.current}
          textMap={textMap}
          inputValue={''}
          readOnly
        />
      </div>
    </div>
  );
}`;

<CodeExample scope={scope} code={variationsCode} />

### MultiSelect List Items

List items have interface `IDataItem`.

<Markdown>{IDataItem}</Markdown>

### Filter Options by Text Input

Text entered into the input can be used to filter list items shown.

export const filterTextCode = `
() => {  
  const options = [
    { id: '1-filter-text-id', title: 'First Option' },
    { id: '2-filter-text-id', title: 'Second Option' },
    { id: '3-filter-text-id', title: 'Third Option' },
    { id: '4-filter-text-id', title: 'Fourth Option' },
    { id: '5-filter-text-id', title: 'Fifth Option' },
    { id: '6-filter-text-id', title: 'Sixth Option' },
  ];
  const textMap = {
    clearButton: 'Clear All',
    removeTagButtonAriaLabel: 'Remove {0}',
    selectedItem: '{0} selected',
    unselectedItem: '{0} not selected',
    moreSelectedOptionsLabel: '+{0} more'
  };
  const [inputValue, setInputValue] = React.useState('');
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filteredOptions, setFilterOptions] = React.useState(options);
  const ariaListBoxResults = React.useRef(options.length.toString() + 'results found.');
  const handleInputValueChange = (value) => {
    setInputValue(value);
  };
  const handleChange = (values) => {
    setSelectedOptions(values);
  };
  const handleClear = () => {
    setSelectedOptions([]);
    setInputValue('');
  };
  const handleBlur = () => {
    setInputValue('');
  };
  React.useEffect(() => {
    if (inputValue.trim().length > 0) {
      const filteredResults = options.filter((option) =>
        option.title.toUpperCase().includes(inputValue.toUpperCase())
      );
      setFilterOptions(filteredResults);
      ariaListBoxResults.current = filteredResults.length + " results found.";
    } else {
      setFilterOptions(options);
      ariaListBoxResults.current = options.length + " results found.";
    }
  }, [inputValue]);
  const label = "Enter text to filter options";
  return (
    <div style={{ width: '50%' }}>
      <MultiSelect
        id="filter-text-multiselect"
        options={filteredOptions}
        selectedOptions={selectedOptions}
        onChange={handleChange}
        onClear={handleClear}
        label={label}
        ariaLabel={label}
        ariaInputLabel={\`\${label}, \${selectedOptions.length} \${selectedOptions.length === 1 ? 'item' : 'items'} selected\`}
        onBlur={handleBlur}
        inputValue={inputValue}
        onInputValueChange={handleInputValueChange}
        noResultsText='No results'
        ariaListboxResult={ariaListBoxResults.current}
        textMap={textMap}
      />
    </div>
  );
}
`;

<CodeExample scope={scope} code={filterTextCode} />

### MultiSelect with Data Load

Use the `loading` prop in combination with `loadingText` and `noResultsText` to indicate to the user when you are fetching data. See the below example.

export const multiSelectWithDataLoadCode = `() => {
  const exampleProps = {
    id: 'multiselect-data-loading-example',        
    testId: 'multiselect-data-loading-example-test-id',
    status: 'default',
    disabled: false,
    textMap: {
      clearButton: 'Clear All',
      removeTagButtonAriaLabel: 'Remove {0}',
      selectedItem: '{0} selected',
      unselectedItem: '{0} not selected',
      moreSelectedOptionsLabel: '+{0} more',
      spinnerAriaLabel: 'Loading',
    },    
    maxItems: 5,    
    hideClearButton: false,
    ariaLabel: 'Data Loading Multiselect',        
    label: 'Data Loading Multiselect',
    helperText: 'Data load is delayed by three seconds',
    helperTextPrefix: 'Note:',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',      
    loadingText: 'Loading',
    noResultsText: 'No matches found',
  }
  const [options, setOptions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');
  const [filteredOptions, setFilterOptions] = React.useState(options);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const handleInputValueChange = (value) => {
    setInputValue(value);
  };
  const handleChange = (values) => {
    setSelectedOptions(values);
  };
  const handleClear = () => {
    setSelectedOptions([]);
    setInputValue('');
  };
  const handleBlur = () => {
    setInputValue('');
  };
  React.useEffect(() => {
    if (inputValue.trim().length > 0) {
      const filteredResults = options.filter((option) =>
        option.title.toUpperCase().includes(inputValue.toUpperCase())
      );
      setFilterOptions(filteredResults);
    } else {
      setFilterOptions(options);
    }
  }, [inputValue]);
  return (
    <div
      style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', gap: '1rem', width: '20rem' }}
    >
      <Button
        id="reset-load-data-btn"
        label="Reset & Load Data"
        onClick={() => {
          setInputValue('');
          setOptions([]);
          setFilterOptions([]);
          setSelectedOptions([]);
          setLoading(true);
          setTimeout(() => {
            const scopedOptions = [
              { id: '1-id', title: 'First Option' },
              { id: '2-id', title: 'Second Option' },
              { id: '3-id', title: 'Third Option' },
              { id: '4-id', title: 'Fourth Option' },
              { id: '5-id', title: 'Fifth Option' },
              { id: '6-id', title: 'Sixth Option' },
              { id: '7-id', title: 'Seventh Option' },
              { id: '8-id', title: 'Eighth Option' },
              { id: '9-id', title: 'Ninth Option' },
            ];
            setInputValue('');
            setOptions(scopedOptions);
            setSelectedOptions([]);
            setFilterOptions(scopedOptions);
            setLoading(false);
          }, 3000);
        }}
      />
      <MultiSelect
        {...exampleProps}
        selectedOptions={selectedOptions}
        inputValue={inputValue}
        onInputValueChange={handleInputValueChange}
        options={filteredOptions}
        onChange={handleChange}
        onClear={handleClear}
        onBlur={handleBlur}            
        ariaInputLabel={\`Data Load example, $\{selectedOptions.length\} $\{
          selectedOptions.length === 1 ? 'item' : 'items'
        } selected\`}
        ariaListboxResult={filteredOptions.length ? \`$\{filteredOptions.length\} results found.\` : ''}
        loading={loading}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={multiSelectWithDataLoadCode} />

### MultiSelect Data Load when Typing

The below shows how data could be loaded when typing.

export const multiSelectWithDataLoadOnTypingCode = `
  () => {
    const exampleProps = {
      id: 'multiselect-data-loading-typing-example',
      testId: 'multiselect-data-loading-typing-example-test-id',
      status: 'default',
      disabled: false,
      textMap: {
        clearButton: 'Clear All',
        removeTagButtonAriaLabel: 'Remove {0}',
        selectedItem: '{0} selected',
        unselectedItem: '{0} not selected',
        moreSelectedOptionsLabel: '+{0} more',
        spinnerAriaLabel: 'Loading',
      },
      maxItems: 5,
      hideClearButton: false,
      ariaLabel: 'Data Load When Typing Multiselect',
      label: 'Data Load When Typing Multiselect',
      helperText: 'Data load is delayed by one second',
      helperTextPrefix: 'Note:',
      statusMessage: 'This is a status message',
      statusMessagePrefix: 'Prefix:',
      loadingText: 'Loading',
      noResultsText: 'No matches found or selected',
    };
    const startingOptions = React.useRef([]);
    const [options, setOptions] = React.useState([]);
    const [loading, setLoading] = React.useState(false);
    const [inputValue, setInputValue] = React.useState('');
    const [selectedOptions, setSelectedOptions] = React.useState([]);
    const [firstOpen, setFirstOpen] = React.useState(true);
    const simulatedAPIDelay = React.useRef(1000);
    const typingDebounce = React.useRef(500);
    const handleInputValueChange = (value) => {
      setInputValue(value);
    };
    const handleChange = (values) => {
      setSelectedOptions(values);
    };
    const handleClear = () => {
      setOptions([]);
      setSelectedOptions([]);
      setInputValue('');
    };
    const handleBlur = () => {
      setInputValue('');
    };
    const handleOnOpen = React.useCallback(() => {
      // set options here rather than onChange so that
      // user sees item deselected from list
      setOptions(selectedOptions);
    }, [selectedOptions]);      
    const fetchAndFilter = React.useCallback(
      // FunctionUtil is imported from @platform/core
      FunctionUtil.debounce((value) => {
        // simulated API call
        setTimeout(() => {
          if (value.trim().length === 0) {
            setOptions(selectedOptions);
          } else {
            const fetchedOptions = [
              { id: '1-id', title: 'First Option' },
              { id: '2-id', title: 'Second Option' },
              { id: '3-id', title: 'Third Option' },
              { id: '4-id', title: 'Fourth Option' },
              { id: '5-id', title: 'Fifth Option' },
              { id: '6-id', title: 'Sixth Option' },
              { id: '7-id', title: 'Seventh Option' },
              { id: '8-id', title: 'Eighth Option' },
              { id: '9-id', title: 'Ninth Option' },
            ];
            const filteredOptions = fetchedOptions.filter((option) =>
              option.title.toUpperCase().includes(value.toUpperCase())
            );
            setOptions(filteredOptions);
          }
          setLoading(false);
        }, simulatedAPIDelay.current);
      }, typingDebounce.current),
      [selectedOptions]
    );
    React.useEffect(() => {
      if (firstOpen) {
        setFirstOpen(false);
      } else {
        setLoading(true);
        fetchAndFilter(inputValue);
      }
    }, [inputValue]);
    return (
      <div
        style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', gap: '1rem', width: '20rem' }}
      >
        <MultiSelect
          {...exampleProps}
          selectedOptions={selectedOptions}
          inputValue={inputValue}
          onInputValueChange={handleInputValueChange}
          options={options}
          onChange={handleChange}
          onClear={handleClear}
          onBlur={handleBlur}
          ariaInputLabel={\`Data load when typing example, \${selectedOptions.length} \${
            selectedOptions.length === 1 ? 'item' : 'items'
          } selected\`}
          ariaListboxResult={options.length ? \`\${options.length} results found.\` : ''}
          loading={loading}
          onOpen={handleOnOpen}
        />
      </div>
    );
  }
`;

<CodeExample scope={scope} code={multiSelectWithDataLoadOnTypingCode} />

## How to Use

The default and suggested maximum number of list items is `5`. This can be adjusted with the `maxItems` prop.

The `MultiSelect` component should only contain a maximum of 50 items in your `options` array. If you have more than 50 items, consider using a different control such as the <LinkTo kind="Components/Table/Overview">Table component.</LinkTo>

List items should have succinct descriptions.

## Accessibility

`MultiSelect` and its list items require distinct ids to function correctly and facilitate assistive technologies.

### Text

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

As outlined in the API table below, labels and aria-labels should be specified.

The following values should be provided as part of the `textMap` prop:

| Label                    | Description                                                                                                                                        | <div style={{whiteSpace: 'nowrap'}}><div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div></div> |
| ------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------- |
| spinnerAriaLabel         | Aria label for announcing loading spinner                                                                                                          | "Loading"                                                                                           |
| clearButton              | Button to clear Multiselect                                                                                                                        | "Clear Input"                                                                                       |
| selectedItem             | Selection announcement text for selected item. The component will internally replace the placeholder `{0}` with the item's title as appropriate.   | "{0} selected"                                                                                      |
| unselectedItem           | Selection announcement text for unselected item. The component will internally replace the placeholder `{0}` with the item's title as appropriate. | "{0} not selected"                                                                                  |
| removeTagButtonAriaLabel | Aria label for the remove tag button. The component will internally replace the placeholder `{0}` with the item's title as appropriate.            | "Remove {0}"                                                                                        |
| moreSelectedOptionsLabel | Label for more selected options. The component will internally replace the placeholder `{0}` with the overflowed number of tags.                   | "+{0} more"                                                                                         |

### Supported Keys

- <kbd>Down Arrow</kbd> / <kbd>Up Arrow</kbd> navigates up and down the list, and opens it when closed
- <kbd>Home</kbd> jumps to the first list item
- <kbd>End</kbd> jumps to the last list item
- <kbd>Enter</kbd> selects a list item, and opens the list when closed
- <kbd>Space</kbd> opens the list when closed
- <kbd>Alt + Up Arrow</kbd> / <kbd>Escape</kbd> closes the list when open
- <kbd>Tab</kbd> moves tab focus

When NVDA is enabled, the `Esc` key needs to be pressed twice in order to close the overlay. The 1st key press is to switch to NVDA's browse mode and the 2nd press is to collapse the overlay.

- https://github.com/nvaccess/nvda/issues/4428
- https://github.com/nvaccess/nvda/issues/15170
- https://github.com/nvaccess/nvda/issues/10608
