# MultiSelect

## Summary

Research and document implementations for the Everest Multiselect.

- Start Date: 2022-08-23
- Figma link: https://www.figma.com/file/YhvqNFsWapyJnaoyAiDNMm/%F0%9F%A7%AADropdown?node-id=661%3A26544

## Detailed Design

`Multiselect` is a modified `Combobox` to accomodate multiple selections. See `Combobox` for details on underlying design decisions.

## API

1. **id**: `string`  
   Sets the `id` attribute on `MultiSelect` html elements. The label is assigned id `${id}-multiselect-label`, the list box is given id `${id}-multiselect-list-box`, and the help text has id `${id}-multiselect-helptext`.
1. **values**: `IDataItem[]`  
   Array of selections the user has made.
1. **options**: `IDataItem[]`  
   Array of options the user may choose from.
1. **onChange**: `(dataItems?: IDataItem[], index?: number) => void`  
   Callback that runs when a list selection is made and should update React state with the list item object.
1. **onClear**: `() => void`  
   Callback that runs when prior selections are removed by pressing the clear button. It should update React state.
1. **testId?**: `string`  
   Optional. Sets `data-testid` attribute on the html elements. The input is assigned test id `${testId}` and the generated overlay has test id `${testId}-overlay`
1. **ariaLabel?**: `string`  
   Optional. Aria-label for the `MultiSelect` list box.
1. **disabled?**: `boolean`  
   Optional. Disable the button and menu. Default is `false`.
1. **required?**: `boolean`  
   Optional. Sets the required attribute on the `MultiSelect`.
1. **label?**: `string`  
   Optional. Label rendered on `MultiSelect`.
1. **status?**: `TSelectContainerTriggerAreaStatus`  
   Optional. Sets the status of the `MultiSelect`.
1. **helperText?**: `string`  
   Optional. Helper text rendered under the `MultiSelect`.
1. **helperTextPrefix?**: `string`  
   Optional. Helper text prefix beneath `MultiSelect`.
1. **statusMessage?**: `string`  
   Optional. Sets the status message rendered under the `MultiSelect`.
1. **statusMessagePrefix?**: `string`  
   Optional. Sets the prefix to the status message rendered under the `MultiSelect`.
1. **inputValue?**: `string`  
   Optional. Text input entered by the user and expected to be React state.
1. **onInputValueChange**: `(value: string) => void`  
   Optional. Callback when the user changes text input value. It should update React state.
1. **itemRenderer?**: `(dataItem: IDataItem, selected: boolean) => React.ReactNode`  
   Optional. Custom item template.
1. **maxItems?**: `number`  
   Optional. Maximum number of menu items. Default is `5`.
1. **altTextMap?**: `ISelectContainerTriggerAreaAltText`  
   Optional. Object containing localized text for various elements.
1. **overrideNoResultRender?**: `(id: string) => React.ReactNode`  
   Optional. Override no results template.
1. **onInputValueChange?**: `(value: string) => void`  
   Optional. Callback when the user changes text input value. It should update React state.
1. **onFocus?**: `(e: React.FocusEvent) => void`  
   Optional. Callback when `MultiSelect` is focused.
1. **onBlur?**: `(e: React.FocusEvent) => void`  
   Optional. Callback when `MultiSelect` is blurred.
1. **ariaInputLabel**: `string`  
   Aria-label for the `MultiSelect` text input element.
1. **noResultsText**: `string`
   Text shown when there are no results.
1. **resultsText**: `string`
   'Text announced when there are results in the format `<number of options> <resultsText>`'

## Usage

A simple example is as follows.

```
() => {
  const args = {
    id: 'multiselect-example',
    label: 'This is a Multiselect Label',
    testId: 'multiselect-test-id',
    ariaLabel: 'This is a list box aria-label',
    disabled: false,
    required: true,
    helperText: 'This is some helper text',
    helperTextPrefix: 'Hint:',
    status: 'default',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',
    maxItems: 7,
    altTextMap: {
      clearButton: 'Clear multiselect values',
      selectedItem: 'selected',
      unselectedItem: 'not selected',
    },
    noResultsText: 'No results'
    resultsText: 'results found.'
  };
  const options = [
    { id: '1-id', title: 'First Option' },
    { id: '2-id', title: 'Second Option' },
    { id: '3-id', title: 'Third Option' },
    { id: '4-id', title: 'Fourth Option' },
    { id: '5-id', title: 'Fifth Option' },
    { id: '6-id', title: 'Sixth Option' },
  ];
  const [inputValue, setInputValue] = useState('');
  const [values, setValues] = React.useState([]);
  const [filteredOptions, setFilterOptions] = useState(options);
  const handleInputValueChange = (value) => {
    setInputValue(value);
  };
  const handleChange = (values, index) => {
    setValues(values);
  };
  const handleClear = () => {
    setValues([]);
    setInputValue('');
  };
  const handleBlur = () => {
    setInputValue('');
  };
  useEffect(() => {
    if (values.length > 0 && !inputValue) {
      setFilterOptions(options);
    } else
      setFilterOptions(
        options.filter((option) =>
          option.title.toUpperCase().includes(inputValue.toUpperCase())
        )
      );
  }, [inputValue]);
  return (
    <MultiSelect
      {...args}
      ariaInputLabel={`${args.ariaInputLabel}, ${values.length} ${
        values.length === 1 ? 'item' : 'items'
      } selected`}
      onBlur={handleBlur}
      inputValue={inputValue}
      options={filteredOptions}
      onInputValueChange={handleInputValueChange}
      values={values}
      onChange={handleChange}
      onClear={handleClear}
    />
  );
}
```

## MultiSelect V4 Updates (Nov 2023)

- Figma link: https://www.figma.com/file/uYLyl0DIcXVMm6SjAGHIae/%F0%9F%A7%AA-Multiselect?type=design&node-id=1-195&mode=design&t=UDshtantDGElr4ZS-0

### Detailed Design Changes

- Inclusion of a "Clear All" button to remove all selected entries as opposed to an 'X' button
- Selected entries in the dropdown are using checkboxes instead of checkmarks
- Selected entries are now removable tags

### Detailed Dev Changes

- New components to use: `Tag`, `TagGroup`, `Checkbox`
- The "Clear All" button is a one-of component, as it is not a standard size as (height of 21px, left/right padding of 8px)
- `itemRenderer` is brought back from V1, as it was removed in V3
- With the inclusion of string literals (ie. "Clear All" button, overflow selected entries counter), we will need to hook up Globalization to handle these cases (consolidate globalized labels and SR labels into new `textMap` object prop that will replace `altTextMap`)
- MultiSelect `ListBox` will need to be refactored so it can support the new design and not break older versions (an inclusion to a `checkedStyle` prop to use between the checkbox or checkmark styles)
- If checkboxes are purely presentational, will need to create a presentational Checkbox component from `CheckboxBase` instead of `Checkbox`, or directly utilize `CheckboxBase` with an exposed `role` prop
- Loading should include `spinnerAriaLabel` to announce dropdown is in loading state if it is needed
- Tag component styles now has `pointer-events: none` on the remove button SVG to reliably target the parent container as the event target when adding focus exceptions on the MultiSelect component (needed to not trigger unnecessary onBlur calls); this is so that depending
  on where the click event happens on the remove button, it does not target between the SVG element or the SVG path element

### Q&A

Q: "Clear All" button size in Figma is smaller (height of 21px) than our small sized button (height of 24px), also the left/right padding is less (8px padding vs 16px), can you clarify if this is a new button size?
A: The size is confirmed to be smaller than our 'small' sized Button, however this button is a one-of component used only in MultiSelect V4, and is only to be used internally (not exposed to consumer).

Q: When focused on MultiSelect and when selecting more entries than the allocated input field space, do we increase the size dynamically or include scrollbar? Is there a limit for the case where we dynamically increase the size?
A: Dynamically increase the rows as needed. There is no limit, and designers have design recs that we don't want this component to be used in the case where there are an enormous amount of selected entries.

Q: When the MultiSelect has selected entries is not focused, how do we handle showing multiple selected entries? ie. Do we only show one tag and a "+X more" label, or we use up the allocated space with removable tags and only collapse to one tag and a "+X more" label when we cannot fit all the selected tags?
A: Max of two rows, use up allocated space for selected entries, when it would show more than two rows of selected entries, reduce it to the "+X more" view just like the Figma mocks.

Q: When using the checkboxes in the List Box, should they be purely presentational (role=presentation) as opposed to a native out-of-the-box checkbox at the DOM level? Reason being the selected/unselected callout is tied to the ListBox as opposed to the checkmark/checkbox.
A: Checkboxes should be purely presentational, but not with role=presentation; they should be styled like a checkbox but not use the native checkbox under the hood (ie. no input markup).

### Testing

Some notes with regards to doing unit testing on the "+X more" label - the current logic to determine boundaries (getClientBoundingRect) doesn't translate well when trying to mock it, therefore at the current state we are relying on Chromatic visual tests to determine the presence of this label.

## Accessibility

Should follow the pattern as outlined at https://www.w3.org/TR/wai-aria-practices-1.2/examples/combobox/combobox-select-only.html

When NVDA is enabled, the `Esc` key needs to be pressed twice in order to close the overlay. The 1st key press is to switch to NVDA's browse mode and the 2nd press is to collapse the overlay.

- https://github.com/nvaccess/nvda/issues/4428
- https://github.com/nvaccess/nvda/issues/15170
- https://github.com/nvaccess/nvda/issues/10608

## Other Design Systems

**Material UI**

- https://mui.com/material-ui/react-select/#multiple-select
- Concatenates selections in the input, including text, chips

**Ant Design**

- https://ant.design/components/select/
- Many different configurations

**Polaris**

- https://polaris.shopify.com/components/combobox
- Uses tags to inidicate selections beneath the input

**Carbon**

- https://react.carbondesignsystem.com/?path=/story/components-multiselect--default
- Tag indicates number of items chosen
- Label does not float

It should be noted that none of the observed design systems appear to create an overlay eclipsing the original input.

## Required PBIs

**EDS-3679** - [MultiSelectv4] [Development] MultiSelect - Component
**EDS-4065** - [MultiSelectV4][Development] Multiline support
**EDS-4066** - [MultiSelectV4][Development] A11y/focus ring behavior
**EDS-4068** - [MultiSelectV4][Development] Consolidate `onChange` and `onTagRemove`
**EDS-4069** - [MultiSelectV4][Checkbox][Development] Update presentational checkbox
**EDS-4065** - [MultiSelectV4][Development] Multiline support (opened state)
**EDS-4161** - [MultiSelectV4][Development] Multiline support (closed state)
**EDS-4174** - [MultiSelectV4][Development] Post-multiline support implementation - outstanding issues
**EDS-4216** - [MultiSelectV4][Development] Trigger tag overflow logic on resize
**EDS-4218** - [MultiSelectV4][Development] Retain focus when removing a selected option tag
**EDS-4219** - [MultiSelectV4][Development] Smaller viewport fixes
**EDS-4544** - [MultiSelectV4][Development] Fix displacement issue when tabbing with a filled input field and selected tags

## Changelog

4/29/2024 - Added docs about `pointer-events` change on the remove button Tag at facilitate focus exception on the MultiSelect
2/12/2024 - Added docs about `spinnerAriaLabel` for loading situations, added more relevant PBIs
11/09/2023 - Added architecture for MultiSelect V4 (**EDS-3102**)
3/28/2025 - Added `tagMaxWidth` prop to control width of selected items (**PWEB-19846**)
