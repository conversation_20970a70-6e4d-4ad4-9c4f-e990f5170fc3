@use '../../variables.scss';
@use '@ceridianhcm/theme/dist/scss/' as typography;
@use '../../mixins.scss' as mixins;
@use '../../index.scss' as helper;

.evrMultiSelect {
  position: relative;
  border: 0;
  outline: 0;
  padding: 0;
  display: flex;
  color: var(--evr-content-primary-default);
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thin-px));
  padding-inline-end: calc(
    var(--evr-spacing-xs) - var(--evr-border-width-thin-px) + (var(--evr-spacing-2xs) + var(--evr-spacing-md))
  );

  & .evrTextFieldListBox {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    text-wrap: nowrap;
    margin: 0;
    padding-block-start: var(--evr-spacing-xs);
    padding-block-end: var(--evr-spacing-xs);
    padding-inline: 0;
    gap: var(--evr-spacing-2xs);

    li {
      list-style: none;
    }
  }

  &.error {
    padding-inline-start: calc(var(--evr-spacing-xs) - var(--evr-border-width-thick-px));
  }

  &.disabled {
    cursor: not-allowed;
    color: var(--evr-inactive-content);
    background: var(--evr-inactive-surfaces);
    @media (forced-colors: active) {
      color: GrayText;
    }
  }

  & .evrMoreSelectedOptionLabel {
    box-sizing: border-box;
    @include typography.body2Regular;
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  & .tagListItem {
    position: relative;
  }

  & .inputLabel {
    cursor: pointer;
    position: absolute;
    opacity: 0;
    inset-block-start: 0;
    inset-inline-start: 0;
    inset-block-end: 0;
    inset-inline-end: 0;

    &.disabled {
      cursor: not-allowed;
    }

    &.readOnly {
      cursor: text;
    }
  }

  & .input {
    background: transparent;
    border: 0;
    outline: 0;
    padding: 0;
    cursor: text;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: auto;
    width: var(--evr-spacing-4xs);
  }

  &:not(.open) {
    .input {
      &.nonEmpty {
        width: 100%;
      }
    }
  }

  &.open {
    flex-wrap: wrap;
    align-items: flex-start;

    & .input {
      width: 100%;
    }
  }
}

.evrOverlayContainer {
  padding: 0;
  border: 0;
  margin: 0;
  outline: none;
  color: var(--evr-content-primary-default);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  height: 100%;
  width: 100%;
  margin-block-start: calc(var(--evr-border-width-thin-px) * -1);

  &.error {
    margin-block-start: calc(var(--evr-border-width-thick-px) * -1);

    & .input {
      width: 100%;
      margin-block-start: 0;
    }
  }
}

.evrMultiSelectNoResult {
  @include mixins.listItemNoResults;
}

.evrMultiSelectVisuallyHidden {
  @include helper.visuallyHidden;
}
