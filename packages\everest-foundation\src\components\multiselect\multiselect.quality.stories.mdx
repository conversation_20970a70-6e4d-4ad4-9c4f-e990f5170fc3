import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { MultiSelect } from './multiselect';
import { Button } from '../button';
import { useState, useEffect, useCallback } from 'react';
import { action } from '@storybook/addon-actions';
import { userEvent, screen, waitFor } from '@storybook/test';
import {
  getInputTestId,
  getClearButtonTestId,
  getChevronIconTestId,
  getTagRemoveButtonTestId,
} from './multiselect-helpers';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

export const testId = 'multiselect-test-id';
export const longString =
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.';
export const longOption = {
  id: 'long-id',
  title: longString,
};
export const options = [
  { id: '1-id', title: 'First Option' },
  { id: '2-id', title: 'Second Option' },
  { id: '3-id', title: 'Third Option' },
  { id: '4-id', title: 'Fourth Option' },
  { id: '5-id', title: 'Fifth Option' },
  { id: '6-id', title: 'Sixth Option' },
  { id: '7-id', title: 'Seventh Option' },
  { id: '8-id', title: 'Eighth Option' },
  { id: '9-id', title: 'Ninth Option' },
  { id: '10-id', title: 'Tenth Option' },
  { id: '11-id', title: 'Eleventh Option' },
  { id: '12-id', title: 'Twelfth Option' },
  { id: '13-id', title: 'Thirteenth Option' },
  { id: '14-id', title: 'Fourteenth Option' },
  { id: '15-id', title: 'Fifteenth Option' },
  { id: '16-id', title: 'Sixteenth Option' },
  { id: '17-id', title: 'Seventeenth Option' },
  { id: '18-id', title: 'Eighteenth Option' },
  { id: '19-id', title: 'Nineteenth Option' },
  { id: '20-id', title: 'Twentieth Option' },
];

export function ControlledComponent(Story, { args, parameters }) {
  const { largeDataset } = parameters;
  // controls which options dataset to use based on the largeDataset parameter
  const opt = (() => {
    if (largeDataset) {
      const options = [];
      for (let i = 1; i <= 100; i++) {
        options.push({
          id: `${i}-id`,
          title: `Option ${i}`,
        });
      }
      return options;
    }
    return args.options;
  })();
  // useArgs is not used here as it was retaining arg changes between stories
  // https://github.com/storybookjs/storybook/discussions/27022
  const [filteredOptions, setFilterOptions] = useState(opt);
  const [inputValue, setInputValue] = useState('');
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const handleInputValueChange = (value) => {
    args.onInputValueChange?.(value);
    setInputValue(value);
    action('onInputValueChange')(value);
  };
  const handleChange = (values) => {
    args.onChange?.(values);
    setSelectedOptions(values);
  };
  const handleClear = () => {
    args.onClear?.();
    setSelectedOptions([]);
    setInputValue('');
  };
  const handleBlur = () => {
    args.onBlur?.();
    setInputValue('');
  };
  const handleFocus = () => {
    args.onFocus?.();
  };
  const filterOptions = (value) => {
    const debounceTimeout = setTimeout(() => {
      if (value.trim().length > 0) {
        const filteredResults = opt.filter((option) => option.title.toUpperCase().includes(value.trim().toUpperCase()));
        setFilterOptions(filteredResults);
      } else {
        setFilterOptions(opt);
      }
    }, 300);
    return () => clearTimeout(debounceTimeout);
  };
  useEffect(() => {
    filterOptions(inputValue);
  }, [inputValue]);
  return (
    <>
      {largeDataset && (
        <Button id="select-all-ms" testId="select-all-ms" label="Select All" onClick={() => setSelectedOptions(opt)} />
      )}
      <Story
        args={{
          ...args,
          ariaInputLabel: `${args.ariaLabel}, ${selectedOptions.length} ${
            selectedOptions.length === 1 ? 'item' : 'items'
          } selected`,
          ariaListboxResult: filteredOptions.length ? `${filteredOptions.length} results found.` : '',
          selectedOptions,
          options: filteredOptions,
          inputValue,
          onInputValueChange: handleInputValueChange,
          onChange: handleChange,
          onClear: handleClear,
          onBlur: handleBlur,
          onFocus: handleFocus,
        }}
      />
    </>
  );
}

<Meta
  title="Testing/Automation Test Cases/MultiSelect"
  component={MultiSelect}
  decorators={[ChromaticDecorators.padStory, ChromaticDecorators.setHeightTo100vh, ControlledComponent]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'multiselect-label',
    testId,
    label: 'My Label',
    ariaInputLabel: 'This is an input aria-label',
    disabled: false,
    required: true,
    helperText: 'This is some helper text',
    helperTextPrefix: 'Hint:',
    status: 'default',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',
    maxItems: 7,
    textMap: {
      clearButton: 'Clear All',
      removeTagButtonAriaLabel: 'Remove {0}',
      selectedItem: '{0} selected',
      unselectedItem: '{0} not selected',
      moreSelectedOptionsLabel: '+{0} more',
      spinnerAriaLabel: 'Loading',
    },
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    onInputValueChange: action('onInputValueChange'),
    onChange: action('onChange'),
    onClear: action('onClear'),
    onOpen: action('onOpen'),
    options,
    noResultsText: 'No matches found',
    loadingText: 'Loading',
    ariaListboxResult: 'results found.',
    selectedOptions: [],
    inputValue: '',
  }}
/>

# MultiSelect

## Live Demo

<Canvas>
  <Story name="Default without label nor helper text">
    {(args) => <MultiSelect {...args} label={undefined} helperText={undefined} helperTextPrefix={undefined} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default with label and helper text">{(args) => <MultiSelect {...args} />}</Story>
</Canvas>

<Canvas>
  <Story name="Default disabled with label">{(args) => <MultiSelect {...args} disabled />}</Story>
</Canvas>

<Canvas>
  <Story name="Default disabled with label and selected values">
    {(args) => <MultiSelect {...args} selectedOptions={args.options} disabled />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default with long label">{(args) => <MultiSelect {...args} label={longString} />}</Story>
</Canvas>

<Canvas>
  <Story name="Default with label and error status">
    {(args) => (
      <MultiSelect {...args} status="error" statusMessagePrefix="Error: " statusMessage="This is an error message" />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default with label and success status">
    {(args) => {
      return (
        <MultiSelect
          {...args}
          status="success"
          statusMessagePrefix="Success: "
          statusMessage="This is a success message"
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default mouse open"
    play={() => {
      userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} options={args.options.slice(0, 5)} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default mouse open with scrollbar"
    play={() => {
      userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default keyboard open"
    play={async () => {
      await userEvent.tab();
      await userEvent.keyboard('{ArrowDown}');
    }}
  >
    {(args) => <MultiSelect {...args} options={args.options.slice(0, 5)} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default keyboard open with scrollbar"
    play={async () => {
      await userEvent.tab();
      await userEvent.keyboard('{ArrowDown}');
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default mouse hover"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.hover(screen.getByText(options[1].title));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default mouse selections"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByText(options[1].title));
      await userEvent.click(screen.getByText(options[3].title));
    }}
  >
    {(args) => {
      return <MultiSelect {...args} />;
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default keyboard selections"
    play={async () => {
      await userEvent.tab();
      for (let i = 0; i < 2; i++) {
        await userEvent.keyboard('{ArrowDown}');
        await userEvent.keyboard('{ArrowDown}');
        await userEvent.keyboard('{Enter}');
      }
    }}
  >
    {(args) => {
      return <MultiSelect {...args} />;
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and overlay opened"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByText(options[3].title));
      await userEvent.click(screen.getByText(options[1].title));
      await userEvent.click(screen.getByText(options[5].title));
      await userEvent.click(screen.getByText(options[0].title));
      await userEvent.tab();
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and overlay closed"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByText(options[3].title));
      await userEvent.click(screen.getByText(options[1].title));
      await userEvent.click(screen.getByText(options[5].title));
      await userEvent.click(screen.getByText(options[0].title));
      await userEvent.tab();
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and +more label"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 30));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.tab();
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and overlay opened, followed with tag removal and overlay opened"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByText(options[3].title));
      await userEvent.click(screen.getByText(options[1].title));
      await userEvent.click(screen.getByText(options[5].title));
      await userEvent.click(screen.getByText(options[0].title));
      await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[3], 'fg')));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and closing overlay, followed with tag removal and overlay closed"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByText(options[3].title));
      await userEvent.click(screen.getByText(options[1].title));
      await userEvent.click(screen.getByText(options[5].title));
      await userEvent.click(screen.getByText(options[0].title));
      await userEvent.tab();
      await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[3], 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and text on input field, followed by closing overlay"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.type(screen.getByTestId(getInputTestId(testId, 'fg')), 'asdf');
      await userEvent.tab();
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and text on input field, followed by closing overlay and tag removals"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.type(screen.getByTestId(getInputTestId(testId, 'fg')), 'asdf');
      await userEvent.tab();
      await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[0], 'bg')));
      await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[1], 'bg')));
      await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[2], 'bg')));
      await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[3], 'bg')));
      await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[4], 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default no results found"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.type(screen.getByTestId(getInputTestId(testId, 'fg')), 'asd');
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with active descendant"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByText(options[0].title));
      await userEvent.click(screen.getByTestId(getChevronIconTestId(testId, 'fg')));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default loading"
    play={() => {
      userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} loading />}
  </Story>
</Canvas>

<Canvas>
  <Story name="ReadOnly with selections and not +more label">
    {(args) => <MultiSelect {...args} selectedOptions={args.options} readOnly />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="lineLimitWhenOpened with selections and overlay opened"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 50));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.tab();
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} lineLimitWhenOpened={3} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="lineLimitWhenOpened with selections and overlay closed"
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 50));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.tab();
    }}
  >
    {(args) => <MultiSelect {...args} lineLimitWhenOpened={3} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="lineLimitWhenClosed with selections and overlay opened"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.tab();
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} lineLimitWhenClosed={4} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="lineLimitWhenClosed with selections and overlay closed"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.tab();
    }}
  >
    {(args) => <MultiSelect {...args} lineLimitWhenClosed={4} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and long +more label"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.tab();
    }}
  >
    {(args) => (
      <MultiSelect
        {...args}
        textMap={{ ...args.textMap, moreSelectedOptionsLabel: '+{0} more confirmed selected options' }}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with selections and long +more label with lineLimitWhenOpened and overlay open"
    play={async () => {
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      for (const optionNumber in options) {
        await userEvent.click(screen.getByText(options[optionNumber].title));
      }
      await userEvent.tab();
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => (
      <MultiSelect
        {...args}
        textMap={{ ...args.textMap, moreSelectedOptionsLabel: '+{0} more confirmed selected options' }}
        lineLimitWhenOpened={2}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="MultiSelect with 100+ selected options, followed with clear all and select all, with overlay opened"
    parameters={{ largeDataset: true }}
    play={async () => {
      await userEvent.click(screen.getByTestId('select-all-ms'));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId(getClearButtonTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId('select-all-ms'));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="MultiSelect with 100+ selected options, followed with clear all and select all, with overlay closed"
    parameters={{ largeDataset: true }}
    play={async () => {
      await userEvent.click(screen.getByTestId('select-all-ms'));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId(getClearButtonTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId('select-all-ms'));
    }}
  >
    {(args) => <MultiSelect {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="MultiSelect with 100+ selected options and lineLimitWhenOpened specified, followed with clear all and select all, with overlay opened"
    parameters={{ largeDataset: true }}
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 25));
      await userEvent.click(screen.getByTestId('select-all-ms'));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId(getClearButtonTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId('select-all-ms'));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
    }}
  >
    {(args) => <MultiSelect {...args} lineLimitWhenOpened={4} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="MultiSelect with 100+ selected options and lineLimitWhenOpened specified, followed with clear all and select all, with overlay closed"
    parameters={{ largeDataset: true }}
    play={async () => {
      await new Promise((resolve) => setTimeout(resolve, 25));
      await userEvent.click(screen.getByTestId('select-all-ms'));
      await userEvent.click(screen.getByTestId(getInputTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId(getClearButtonTestId(testId, 'bg')));
      await userEvent.click(screen.getByTestId('select-all-ms'));
    }}
  >
    {(args) => <MultiSelect {...args} lineLimitWhenOpened={4} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="MultiSelect with tagMaxWidth specified">
    {(args) => (
      <MultiSelect
        {...args}
        options={[longOption, ...options]}
        selectedOptions={[longOption, ...options]}
        tagMaxWidth="100px"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="MultiSelect with long title no maxWidth specified">
    {(args) => <MultiSelect {...args} options={[longOption, ...options]} selectedOptions={[longOption, ...options]} />}
  </Story>
</Canvas>
