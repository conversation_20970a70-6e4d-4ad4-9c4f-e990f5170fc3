import React from 'react';
import { act, render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { MultiSelect } from './multiselect';
import {
  TMultiSelectPosition,
  getInputTestId,
  getFormFieldLabelId,
  getTagRemoveButtonTestId,
  getClearButtonTestId,
} from './multiselect-helpers';
import { mockResizeObserver } from '../../test-utils';
import { IDataItem } from '../list-item';

const testId = 'multiselect-test-id';
const options = [
  { title: 'First Option', id: '1-id' },
  { title: 'Second Option', id: '2-id' },
  { title: 'Third Option', id: '3-id' },
];
let selectedOptions: IDataItem[] = [];
const id = 'multiselect-id';
const label = 'This is a multiselect label';
const ariaInputLabel = 'aria input label';
const helperText = 'This is some helper text';
const helperTextPrefix = 'Hint:';
const statusMessage = 'This is a status message';
const statusMessagePrefix = 'Prefix:';
const clearButtonLabel = 'Clear All';
const selectedItemAriaLabel = '{0} selected';
const unselectedItemAriaLabel = '{0} not selected';
const removeTagButtonAriaLabel = 'Remove {0}';
const moreSelectedOptionsLabel = '+{0} more';
const noResultsText = 'No Results';
const loadingText = 'Loading';
const ariaListboxResult = 'ariaListboxResult text';
const inputValue = '';
const loading = false;
const textMap = {
  clearButton: clearButtonLabel,
  selectedItem: selectedItemAriaLabel,
  unselectedItem: unselectedItemAriaLabel,
  removeTagButtonAriaLabel: removeTagButtonAriaLabel,
  moreSelectedOptionsLabel: moreSelectedOptionsLabel,
};
const onChange = jest.fn();
const onClear = jest.fn();
const onBlur = jest.fn();
const onFocus = jest.fn();
const onInputValueChange = jest.fn();
const onOpen = jest.fn();
const getFormLabelElement = () =>
  document.getElementById(getFormFieldLabelId(id, 'bg' as TMultiSelectPosition)) as HTMLElement;
const getClearButton = () => screen.getByTestId(getClearButtonTestId(testId, 'bg'));
const getClearButtonLabel = () => screen.queryByText(textMap.clearButton);

const requiredPropsMock = {
  id,
  selectedOptions,
  options,
  testId,
  onChange,
  onClear,
  ariaInputLabel,
  noResultsText,
  ariaListboxResult,
  inputValue,
  onInputValueChange,
  textMap,
};

const defaultPropsMock = {
  ...requiredPropsMock,
  label,
  loading,
  loadingText,
  noResultsText,
};

describe('[MultiSelect]', () => {
  /**
   * Fix TypeError: window.ResizeObserver is not a constructor issue
   * mock out the basic API (observe, unobserve, disconnect) and
   * use jest.fn() to return particular mock entries in the test.
   * https://github.com/maslianok/react-resize-detector/issues/145
   * https://github.com/que-etc/resize-observer-polyfill/issues/50
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { ResizeObserver } = window;

  beforeEach(() => {
    mockResizeObserver();
    selectedOptions = [];
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  afterEach(() => {
    jest.resetAllMocks();
    window.ResizeObserver = ResizeObserver;
  });

  const getInputByPosition = (position: TMultiSelectPosition) => screen.getByTestId(getInputTestId(testId, position));
  const getListItems = () => screen.getAllByRole('option');
  const queryListItems = () => screen.queryAllByRole('option');
  const getIcons = (name: string) => document.querySelectorAll(`svg[data-evr-name="${name}"]`) || null;
  const getIconByName = (name: string) => {
    const multiselectIcons = getIcons(name);
    return multiselectIcons ? multiselectIcons[0] : null;
  };
  // checks list items are present, in dom, and in the same
  // original order
  const checkListItemsByText = (checkOrder?: boolean) => {
    const listItems = getListItems();
    expect(listItems.length).toEqual(options.length);
    for (let i = 0; i < listItems.length; i++) {
      screen.getAllByText(options[i].title).at(-1);
      checkOrder && expect(options[i].title).toBe(listItems[i].textContent);
    }
  };
  async function closeAndOpenMultiselect() {
    // close the multiselect
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    // re-open the multiselect
    await act(async () => await userEvent.click(getInputByPosition('bg')));
  }

  it('should render empty input with required props', () => {
    render(<MultiSelect {...requiredPropsMock} />);
    expect(getInputByPosition('bg').getAttribute('value')).toEqual('');
  });
  it('should render a label when given', () => {
    render(<MultiSelect {...defaultPropsMock} />);
    screen.getByText(label);
  });
  it('should render an input aria label when given', () => {
    render(<MultiSelect {...defaultPropsMock} />);
    expect(getInputByPosition('bg').getAttribute('aria-label')).toMatch(`${defaultPropsMock.ariaInputLabel}`);
  });
  it('should render a chevron icon pointing down', () => {
    render(<MultiSelect {...defaultPropsMock} />);
    expect(getIconByName('chevronDownSmall')).toBeInTheDocument();
  });

  it('should open overlay when clicking on chevronIcon', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    const chevronIcon = getIconByName('chevronDownSmall');
    expect(chevronIcon).toBeInTheDocument();
    await act(async () => await userEvent.click(chevronIcon as HTMLElement));
    expect(getIconByName('chevronUpSmall')).toBeInTheDocument();
    expect(queryListItems().length).toEqual(3);
  });

  it('should render helper text and prefix when given', () => {
    render(<MultiSelect {...defaultPropsMock} helperTextPrefix={helperTextPrefix} helperText={helperText} />);
    screen.getByText(helperTextPrefix);
    screen.getByText(helperText);
  });
  it('should render error status message and prefix when given', () => {
    render(
      <MultiSelect
        {...defaultPropsMock}
        statusMessage={statusMessage}
        statusMessagePrefix={statusMessagePrefix}
        status="error"
      />
    );
    screen.getByText(statusMessage);
    screen.getByText(statusMessagePrefix);
  });
  it('should render the input as disabled when specified', () => {
    render(<MultiSelect {...requiredPropsMock} disabled />);
    expect(getInputByPosition('bg').getAttribute('disabled')).toEqual('');
  });

  it('should render the input as readOnly when specified', () => {
    render(<MultiSelect {...requiredPropsMock} readOnly />);
    expect(getInputByPosition('bg').getAttribute('readonly')).toEqual('');
  });

  // there are many effects when interacting with the overlay, so
  // wrap interactions with act()
  // https://github.com/mrdulin/react-act-examples/blob/master/sync.md

  // mouse

  it('should render three options and preserve their order when text input is clicked', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => {
      await userEvent.click(getInputByPosition('bg'));
    });
    const checkOrder = true;
    checkListItemsByText(checkOrder);
  });
  it('should display a chevron pointing up when multiselect opened', async () => {
    // this only needs to be checked with mouse since it is independent of input
    render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => {
      await userEvent.click(getInputByPosition('bg'));
    });
    expect(getIconByName('chevronUpSmall')).toBeInTheDocument();
  });
  it('should update aria-activedescendant when hovering over an option', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => {
      await userEvent.click(getInputByPosition('bg'));
    });
    await act(async () => await userEvent.hover(screen.getByText(options[1].title)));
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[1].id);
  });
  it('should call onChange when a selection is clicked from the list of options', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[1].title)));
    expect(defaultPropsMock.onChange).toHaveBeenCalledWith([options[1]]);
  });
  it('should update state (selectedOptions) when two distinct selections are clicked from the list of options', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    expect(selectedOptions.length).toEqual(0);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[1].title)));
    expect(defaultPropsMock.onChange).toHaveBeenCalledWith([options[1]]);
    expect(selectedOptions.length).toEqual(1);
    expect(selectedOptions[0].id).toEqual(options[1].id);
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await act(async () => await userEvent.click(screen.getByText(options[2].title)));
    expect(defaultPropsMock.onChange).toHaveBeenNthCalledWith(2, [options[1], options[2]]);
    expect(selectedOptions.length).toEqual(2);
    expect(selectedOptions[0].id).toEqual(options[1].id);
    expect(selectedOptions[1].id).toEqual(options[2].id);
  });
  it('should remove a previously made selection from state when clicked', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[2].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await act(async () => await userEvent.click(screen.getByText(options[1].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    expect(selectedOptions.length).toEqual(2);
    expect(selectedOptions[0].id).toEqual(options[2].id);
    expect(selectedOptions[1].id).toEqual(options[1].id);
    await closeAndOpenMultiselect();
    // need to use getAllByText to select the dropdown element and not the tag elements
    await act(async () => await userEvent.click(screen.getAllByText(options[2].title)[2]));
    expect(selectedOptions.length).toEqual(1);
    expect(selectedOptions[0].id).toEqual(options[1].id);
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    // check that the list item is no longer aria-selected
    // expect(screen.getAllByText(options[2].title)[2].parentNode?.parentNode).toHaveAttribute('aria-selected', 'false');
  });
  it('clicked selections should appear in their original order when multiselect is reopened', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[2].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await act(async () => await userEvent.click(screen.getByText(options[1].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await closeAndOpenMultiselect();
    // the `true` argument indicates that order is checked
    checkListItemsByText(true);
  });
  it('should create a non-visual element with an alert announcing there are results when clicked', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await waitFor(() => {
      const firstChar = (unselectedItemAriaLabel.match('[a-zA-Z]') as RegExpMatchArray)[0];
      const indexOfFirstChar = unselectedItemAriaLabel.indexOf(firstChar);
      const subStr = unselectedItemAriaLabel.slice(indexOfFirstChar, unselectedItemAriaLabel.length);
      expect(screen.getByRole('alert').textContent).toBe(`${ariaListboxResult} ${options[0].title} ${subStr}`);
    });
  });
  it('should have listbox remain open when the multiselect is open and input field is then clicked', async () => {
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(getInputByPosition('fg')));
    rerender(<MultiSelect {...defaultPropsMock} />);
    expect(queryListItems().length).not.toEqual(0);
  });
  it('should call onFocus when clicked', async () => {
    render(<MultiSelect {...defaultPropsMock} onFocus={onFocus} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    expect(onFocus).toHaveBeenCalledTimes(1);
    // onFocus should not trigger when clicking other parts of the component
    await act(async () => await userEvent.click(getFormLabelElement()));
    expect(onFocus).toHaveBeenCalledTimes(1);
  });
  it('should call onOpen when the dropdown is opened on clicked', async () => {
    render(<MultiSelect {...defaultPropsMock} onOpen={onOpen} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    expect(onOpen).toHaveBeenCalledTimes(1);
  });
  it('should close the overlay when the label is clicked', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => {
      await userEvent.click(getInputByPosition('bg'));
    });
    await waitFor(() => {
      expect(screen.getByText(options[0].title)).toBeInTheDocument();
    });
    await act(async () => userEvent.click(getFormLabelElement()));
    expect(screen.queryByText(options[0].title)).not.toBeInTheDocument();
  });
  it('should call onBlur and set aria-activedescendant to empty when clicking outside an open multiselect', async () => {
    const divText = 'Click me!';
    render(
      <>
        <div>{divText}</div>
        <MultiSelect {...defaultPropsMock} onBlur={onBlur} />
      </>
    );
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(divText)));
    expect(onBlur).toHaveBeenCalledTimes(1);
    expect(getInputByPosition('bg').getAttribute('aria-activedescendant')).toEqual('');
  });

  // tag with selection count

  it.skip('should display tags with the clicked selections', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(
      <div id="container" style={{ width: '1000px' }}>
        <MultiSelect {...defaultPropsMock} />
      </div>
    );
    Object.defineProperty(getInputByPosition('bg'), 'clientWidth', { value: 500 });
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[2].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    // selected option title exists thrice (foreground tag + background tag + dropdown option)
    expect(screen.getAllByText(options[2].title).length).toEqual(3);
    await act(async () => await userEvent.click(screen.getByText(options[1].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    expect(screen.getAllByText(options[1].title).length).toEqual(3);
    expect(screen.getAllByText(options[2].title).length).toEqual(3);
    await act(async () => await userEvent.click(screen.getByText(options[2].title))); // deselection
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    expect(screen.getAllByText(options[1].title).length).toEqual(3);
    expect(screen.getAllByText(options[2].title).length).toEqual(2);
    const els = screen.queryAllByText(options[1].title);
    els.forEach((el) => {
      expect(el.parentElement?.parentElement?.getAttribute('aria-hidden')).toBe('true');
    });
  });

  // clear all button

  it('should not show Clear All button when no selected options are present', () => {
    render(<MultiSelect {...defaultPropsMock} />);
    expect(screen.queryByTestId(getClearButtonTestId(testId, 'bg'))).toBeFalsy();
  });

  it('should not show Clear All button when readOnly', () => {
    render(<MultiSelect {...defaultPropsMock} readOnly />);
    expect(screen.queryByTestId(getClearButtonTestId(testId, 'bg'))).toBeFalsy();
  });

  it('should show Clear All button once a selection has been clicked, contain the clear button label, and call onClear when clicked', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[2].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    expect(getClearButton()).toBeInTheDocument();
    expect(getClearButtonLabel()).toBeInTheDocument();
    await act(async () => await userEvent.click(getClearButton()));
    expect(onClear).toHaveBeenCalledTimes(1);
  });

  // keyboard

  it('should open the multiselect and render three options, preserving order, when enter is pressed on the text input', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    const checkOrder = true;
    checkListItemsByText(checkOrder);
  });
  it('should open the multiselect and render three options, preserving order, when space is pressed on the text input', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard(' ');
    });
    const checkOrder = true;
    checkListItemsByText(checkOrder);
  });
  it('should open the multiselect and render three options, preserving order, when up or down keys are pressed on the text input', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{ArrowUp}');
    });
    const checkOrder = true;
    checkListItemsByText(checkOrder);
    // close the multiselect
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    checkListItemsByText(checkOrder);
  });
  it('should open the multiselect when up or down arrows keys are pressed on a non-empty text input', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    // fill input with text
    await act(async () => {
      await userEvent.keyboard('option');
    });
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{ArrowUp}');
    });
    expect(queryListItems().length).toEqual(3);
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    expect(queryListItems().length).toEqual(3);
  });
  it('should not open the multiselect when ctrl is pressed', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{ctrl}');
    });
    expect(queryListItems().length).toBe(0);
  });
  it('should update aria-activedescendant when pressing up and down arrow keys on opened multiselect', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[0].id);
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[1].id);
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[2].id);
    await act(async () => {
      await userEvent.keyboard('{ArrowUp}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[1].id);
  });
  it('should call onChange when a selection is made by pressing enter on the list of options', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    expect(defaultPropsMock.onChange).toHaveBeenCalledWith([options[1]]);
  });
  it('should update state (selectedOptions) when two distinct selections are made by keyboard from the list of options', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    expect(selectedOptions.length).toEqual(0);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    expect(defaultPropsMock.onChange).toHaveBeenCalledWith([options[1]]);
    expect(selectedOptions.length).toEqual(1);
    expect(selectedOptions[0].id).toEqual(options[1].id);
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    expect(defaultPropsMock.onChange).toHaveBeenNthCalledWith(2, [options[1], options[2]]);
    expect(selectedOptions.length).toEqual(2);
    expect(selectedOptions[0].id).toEqual(options[1].id);
    expect(selectedOptions[1].id).toEqual(options[2].id);
  });
  it('should remove a previously made selection from state when using keyboard', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await act(async () => {
      await userEvent.keyboard('{ArrowUp}');
    });
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    expect(selectedOptions.length).toEqual(2);
    expect(selectedOptions[0].id).toEqual(options[2].id);
    expect(selectedOptions[1].id).toEqual(options[1].id);
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    // close the multiselect
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    // re-open the multiselect
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    // move to second item in list
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    // de-select item in list
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    expect(selectedOptions.length).toEqual(1);
    expect(selectedOptions[0].id).toEqual(options[2].id);
  });
  it('should select the first and last options using Home and End keys, respectively', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{End}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[2].id);
    await act(async () => {
      await userEvent.keyboard('{Home}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[0].id);
  });
  it('should close the multiselect when the Escape key is pressed', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    expect(queryListItems().length).toEqual(0);
  });
  it('Escape keydown should stopPropagation when overlay is visible', async () => {
    const onKeyDownParent = jest.fn();
    render(
      <div onKeyDown={onKeyDownParent}>
        <MultiSelect {...defaultPropsMock} />
      </div>
    );
    await act(async () => {
      await userEvent.click(getInputByPosition('bg'));
    });
    await waitFor(() => {
      expect(screen.getByText(options[0].title)).toBeInTheDocument();
    });
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    expect(screen.queryByText(options[0].title)).not.toBeInTheDocument();
    expect(onKeyDownParent).toHaveBeenCalledTimes(0);
    await waitFor(async () => {
      await userEvent.keyboard('{Escape}');
    });
    expect(onKeyDownParent).toHaveBeenCalledTimes(1);
  });
  it('should be tab focusable', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => {
      await userEvent.tab(); // tabbing once since clear button is not present when no selected options are present
    });
    expect(document.activeElement?.getAttribute('data-testid')).toEqual(getInputTestId(testId, 'bg'));
  });
  it('should focus on the input field when tabbing and overlay is visible', async () => {
    render(
      <div>
        <MultiSelect {...defaultPropsMock} />
      </div>
    );
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{Tab}');
    });
    expect(getInputByPosition('bg')).toHaveFocus();
  });
  it('should not allow selections below the last element in list when pressing down arrow key', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[2].id);
  });
  it('should not allow selections above the first element in list when pressing up arrow key', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowUp}');
    });
    await act(async () => {
      await userEvent.keyboard('{ArrowUp}');
    });
    expect(getInputByPosition('fg').getAttribute('aria-activedescendant')).toEqual(options[0].id);
  });
  it('should create an element with an alert announcing there are results when opened by keyboard', async () => {
    render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{ArrowDown}');
    });
    await waitFor(() => {
      const firstChar = (unselectedItemAriaLabel.match('[a-zA-Z]') as RegExpMatchArray)[0];
      const indexOfFirstChar = unselectedItemAriaLabel.indexOf(firstChar);
      const subStr = unselectedItemAriaLabel.slice(indexOfFirstChar, unselectedItemAriaLabel.length);
      expect(screen.getByRole('alert').textContent).toBe(`${ariaListboxResult} ${options[0].title} ${subStr}`);
    });
  });

  // clear all button - keyboard

  it('should have tab focusable clear button and elements are selected', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    // 'enter' to open
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    // 'enter' to make selection
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    // close the multiselect
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    // shift tab back to the clear all button
    await act(async () => {
      await userEvent.tab({ shift: true });
    });
    await act(async () => {
      await userEvent.tab({ shift: true });
    });
    expect(getClearButton()).toBeInTheDocument();
    expect(document.activeElement?.getAttribute('data-testid')).toEqual(getClearButtonTestId(testId, 'bg'));
  });

  it('should call onClear when Clear All button is pressed with Enter key and elements are selected', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      // 'enter' to open
      await userEvent.keyboard('{Enter}');
    });
    await act(async () => {
      // 'enter' to make selection
      await userEvent.keyboard('{Enter}');
    });
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    // close the multiselect
    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    // shift tab back to the clear all button
    await act(async () => {
      await userEvent.tab({ shift: true });
    });
    await act(async () => {
      await userEvent.tab({ shift: true });
    });
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    expect(onClear).toHaveBeenCalledTimes(1);
  });

  // other keyboard related events

  it('should call onFocus when focused with keyboard', async () => {
    render(<MultiSelect {...defaultPropsMock} onFocus={onFocus} />);
    await act(async () => await userEvent.tab());
    expect(onFocus).toHaveBeenCalledTimes(1);
  });
  it('should call onOpen when the dropdown is opened with keyboard', async () => {
    render(<MultiSelect {...defaultPropsMock} onOpen={onOpen} />);
    await act(async () => await userEvent.tab());
    await userEvent.keyboard(' ');
    expect(onOpen).toHaveBeenCalledTimes(1);
  });
  it('should call onBlur and set aria-activedescendant as empty when focused and then unfocused with keyboard', async () => {
    render(<MultiSelect {...defaultPropsMock} onBlur={onBlur} />);
    await act(async () => await userEvent.tab());
    await act(async () => await userEvent.tab());
    expect(onBlur).toHaveBeenCalledTimes(1);
    expect(getInputByPosition('bg').getAttribute('aria-activedescendant')).toEqual('');
  });
  it('should call handleInputValueChange when text is typed into the input', async () => {
    render(<MultiSelect {...defaultPropsMock} onInputValueChange={onInputValueChange} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{Enter}');
    });
    await act(async () => {
      await userEvent.type(getInputByPosition('fg'), 'abc', { skipClick: true });
    });
    expect(onInputValueChange).toHaveBeenCalledTimes(3);
  });

  // tag removal

  it('should call onFocus and not call onBlur when a tag is removed when overlay is opened', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} onBlur={onBlur} onFocus={onFocus} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[2].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await closeAndOpenMultiselect();
    await act(
      async () => await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[2], 'fg')))
    );
    expect(onFocus).toHaveBeenCalledTimes(1);
    expect(onBlur).toHaveBeenCalledTimes(0);
  });

  it('should call onFocus and not call onBlur when a tag is removed when overlay is closed', async () => {
    defaultPropsMock.onChange = jest.fn().mockImplementation((items) => (selectedOptions = items));
    const { rerender } = render(<MultiSelect {...defaultPropsMock} onBlur={onBlur} onFocus={onFocus} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await act(async () => await userEvent.click(screen.getByText(options[2].title)));
    rerender(<MultiSelect {...defaultPropsMock} selectedOptions={selectedOptions} />);
    await act(
      async () => await userEvent.click(screen.getByTestId(getTagRemoveButtonTestId(testId, options[2], 'bg')))
    );
    expect(onFocus).toHaveBeenCalledTimes(1);
    expect(onBlur).toHaveBeenCalledTimes(0);
  });

  // no results

  it('should render no results template with an annoucement', async () => {
    const { rerender } = render(<MultiSelect {...defaultPropsMock} options={[]} />);
    getInputByPosition('bg').focus();
    await act(async () => {
      await userEvent.keyboard('{enter}');
    });
    rerender(<MultiSelect {...defaultPropsMock} options={[]} />);
    await waitFor(() => screen.getByText(noResultsText));
    expect(screen.getByRole('alert').textContent).toBe(noResultsText);
  });
  it('should not render no results when options are not empty', async () => {
    const { rerender } = render(<MultiSelect {...defaultPropsMock} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    rerender(<MultiSelect {...defaultPropsMock} />);
    expect(screen.queryByText(noResultsText)).not.toBeInTheDocument();
  });

  // loading

  it('should show loading stub when loading is true with an announcement', async () => {
    render(<MultiSelect {...defaultPropsMock} loading={true} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    await waitFor(() => {
      const el = screen.getByTestId(`${testId}-list-box-stub`);
      expect(within(el).getByText(loadingText)).toBeInTheDocument();
      expect(screen.getByRole('alert').textContent).toBe(loadingText);
    });
  });
  it('should not show loading stub when loading is false', async () => {
    render(<MultiSelect {...defaultPropsMock} loading={false} />);
    await act(async () => await userEvent.click(getInputByPosition('bg')));
    expect(screen.queryByText(loadingText)).not.toBeInTheDocument();
  });
});
