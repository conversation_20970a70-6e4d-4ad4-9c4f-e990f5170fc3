import { useState, useEffect } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { MultiSelect } from './multiselect';
import { action } from '@storybook/addon-actions';
import Examples from './multiselect.examples.mdx';

<Meta
  title="Components/MultiSelect"
  component={MultiSelect}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uYLyl0DIcXVMm6SjAGHIae/%F0%9F%A7%AA-Multiselect?type=design&node-id=1-196&mode=design&t=zOJEzrSj3Qiou1k1-0',
    },
  }}
  argTypes={{
    onChange: {
      control: '-',
    },
    onClear: {
      control: '-',
    },
    onBlur: {
      control: '-',
    },
    onFocus: {
      control: '-',
    },
    selectedOptions: {
      control: '-',
    },
    onInputValueChange: {
      control: '-',
    },
    onOpen: {
      type: '-',
      control: '-',
    },

}}
args={{
    id: 'multiselect-id-example',
    testId: 'multiselect-test-id',
    ariaInputLabel: 'This is an input aria-label',
    textMap: {
      clearButton: 'Clear All',
      removeTagButtonAriaLabel: 'Remove {0}',
      selectedItem: '{0} selected',
      unselectedItem: '{0} not selected',
      moreSelectedOptionsLabel: '+{0} more',
    },
    noResultsText: 'No matches found',
    label: 'My Label',
    statusMessage: 'This is a Status Message',
    statusMessagePrefix: 'Prefix:',
    helperTextPrefix: 'Hint:',
    helperText: 'Helper text',
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    onChange: action('onChange'),
    onClear: action('onClear'),
    onOpen: action('onOpen'),
    onInputValueChange: action('onInputValueChange'),
    disabled: false,
    readOnly: false,
    maxItems: 5,
    required: false,
    loading: false,
    loadingText: 'Loading',
    status: 'default',
    options: [
      { id: '1-id', title: 'First Option' },
      { id: '2-id', title: 'Second Option' },
      { id: '3-id', title: 'Third Option' },
      { id: '4-id', title: 'Fourth Option' },
      { id: '5-id', title: 'Fifth Option' },
      { id: '6-id', title: 'Sixth Option' },
      { id: '7-id', title: 'Seventh Option' },
      { id: '8-id', title: 'Eighth Option' },
      { id: '9-id', title: 'Ninth Option' },
    ],
  }}
/>

# MultiSelect

<Examples />

<Canvas>
  <Story name="MultiSelect">
    {(args) => {
      const [inputValue, setInputValue] = useState('');
      const [filteredOptions, setFilterOptions] = useState(args.options);
      const [selectedOptions, setSelectedOptions] = useState([]);
      const handleInputValueChange = (value) => {
        args.onInputValueChange(value);
        setInputValue(value);
      };
      const handleChange = (values) => {
        setSelectedOptions(values);
        args.onChange(values);
      };
      const handleClear = () => {
        setSelectedOptions([]);
        setInputValue('');
        args.onClear();
      };
      const handleBlur = () => {
        setInputValue('');
        args.onBlur();
      };
      const handleFocus = () => {
        args.onFocus();
      };
      useEffect(() => {
        setInputValue('');
        setFilterOptions(args.options);
        setSelectedOptions([]);
      }, [args.options]);
      useEffect(() => {
        if (inputValue.trim().length > 0) {
          const filteredResults = args.options.filter((option) =>
            option.title.toUpperCase().includes(inputValue.trim().toUpperCase())
          );
          setFilterOptions(filteredResults);
        } else {
          setFilterOptions(args.options);
        }
      }, [inputValue]);
      return (
        <MultiSelect
          {...args}
          selectedOptions={selectedOptions}
          inputValue={inputValue}
          onInputValueChange={handleInputValueChange}
          options={filteredOptions}
          onChange={handleChange}
          onClear={handleClear}
          onBlur={handleBlur}
          onFocus={handleFocus}
          ariaInputLabel={`${args.ariaInputLabel}, ${selectedOptions.length} ${
            selectedOptions.length === 1 ? 'item' : 'items'
          } selected`}
          ariaListboxResult={filteredOptions.length ? `${filteredOptions.length} results found.` : ''}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="MultiSelect" />
