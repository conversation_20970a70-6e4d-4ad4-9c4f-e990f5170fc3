import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState, forwardRef } from 'react';
import { getTokenValue, convertToPx } from '@ceridianhcm/everest-cdk';
import { getRuntimeEnvironmentInfo } from '@platform/core';
import classnames from 'classnames';

import {
  TMultiSelectControlInputKeys,
  TMultiSelectPosition,
  getFormFieldId,
  getFormFieldLabelId,
  getFormFieldStatusMessageId,
  getInputId,
  getInputTestId,
  getListBoxId,
  getOverlayContainerId,
  inputOnKeyDown,
  inputOnKeyDownOnceUlRendered,
  makeSelection,
  getNoResultsId,
  getFormFieldMaxHeight,
  getTagRemoveButtonId,
  getChevronIconId,
} from './multiselect-helpers';
import {
  announce,
  clearAnnouncer,
  getAriaAttributes,
  mergeRefs,
  templateReplacer,
  useComponentFocused,
  useCreateTestId,
  useDynamicResize,
} from '../../utils';
import { FormFieldContainerContext, TFormFieldStatus } from '../form-field-container';
import { FormSelectContainer } from '../form-select-container';
import { ListBox } from '../list-box';
import { IDataItem, TSelectionType } from '../list-item';
import { IListItemContext, ListItemContext } from '../list-item/list-item-context';
import { ISelectContainerTriggerAreaAltText } from '../select-container';
import { Tag } from '../tag';
import { TriggerAreaStyledOverlay } from '../trigger-area-styled-overlay';

import multiSelectStyles from './multiselect.module.scss';

export interface IMultiSelectTextMap extends ISelectContainerTriggerAreaAltText {
  removeTagButtonAriaLabel?: string;
  moreSelectedOptionsLabel?: string;
}

export interface IMultiSelect {
  /**
   * Aria-label for the `MultiSelect` text input element. It should describe the number of items selected.
   */
  ariaInputLabel: string;
  /**
   * Aria-label for the listbox result. This should describe the state of the results in the dropdown.
   */
  ariaListboxResult: string;
  /**
   * Sets the `MultiSelect` to disabled state. When disabled, the component becomes non-interactive.
   * @default false
   */
  disabled?: boolean;
  /**
   * Helper text rendered under the `MultiSelect`, providing additional information or instructions.
   */
  helperText?: string;
  /**
   * Prefix for the helper text rendered beneath the `MultiSelect`.
   */
  helperTextPrefix?: string;
  /**
   * Sets the `id` attribute on `MultiSelect` html elements, e.g., the label is assigned id `${id}-label-${position}`,
   * where position has value "bg" or "fg", referring to the background or foreground, respectively.
   */
  id: string;
  /**
   * React state representing text input entered by the user.
   */
  inputValue: string;
  /**
   * Custom rendering function for list items, used to control how selected and unselected items are displayed.
   */
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  /**
   * Label rendered on the `MultiSelect`. Provides a description or name for the input.
   */
  label?: string;
  /**
   * Toggles the loading presentation, typically used when data is being fetched.
   */
  loading?: boolean;
  /**
   * Text to be presented when the component is loading. This is shown while the component is in a loading state.
   */
  loadingText?: string;
  /**
   * Maximum number of menu items to be displayed in the dropdown.
   * @default 5
   */
  maxItems?: number;
  /**
   * Text shown when there are no results for the current input.
   */
  noResultsText: string;
  /**
   * Callback when `MultiSelect` is blurred (loses focus).
   */
  onBlur?: () => void;
  /**
   * Callback that runs when a list selection is made and should update React state with the selected list item objects.
   */
  onChange: (dataItems?: IDataItem[]) => void;
  /**
   * Callback that runs when prior selections are removed by pressing the clear button. It should update React state.
   */
  onClear: () => void;
  /**
   * Callback when `MultiSelect` is focused (gains focus).
   */
  onFocus?: () => void;
  /**
   * Callback when the user changes text input value. It should update React state.
   */
  onInputValueChange: (value: string) => void;
  /**
   * Callback when the `MultiSelect` dropdown opens.
   */
  onOpen?: () => void;
  /**
   * Array of options the user may choose from. This array is required for the component to function.
   */
  options: IDataItem[];
  /**
   * Sets the `MultiSelect` to read-only state. When read-only, selections cannot be made or changed.
   * @default false
   */
  readOnly?: boolean;
  /**
   * Sets the required attribute on the `MultiSelect`. If set, the field must be filled before submission.
   * @default false
   */
  required?: boolean;
  /**
   * Array of selections the user has made. This is the controlled value for the selected items.
   */
  selectedOptions: IDataItem[];
  /**
   * Sets the status of the `MultiSelect`. This can be `default`, `error`, or `success`.
   * @default default
   */
  status?: TFormFieldStatus;
  /**
   * Sets the status message rendered under the `MultiSelect`. Typically used for validation or informational messages.
   */
  statusMessage?: string;
  /**
   * Sets the prefix to the status message rendered under the `MultiSelect`. Can be used to prepend text to status messages.
   */
  statusMessagePrefix?: string;
  /**
   * Sets `data-testid` attribute on the HTML elements, used for testing purposes. The `id` is combined with `position` to form unique test IDs.
   */
  testId?: string;
  /**
   * Object containing localized text and ARIA labels for various elements in the `MultiSelect`.
   * Refer to the 'Accessibility' section on the MultiSelect Docs page for more details.
   */
  textMap: IMultiSelectTextMap;
  /**
   * Number of lines to show when the `MultiSelect` dropdown is open.
   */
  lineLimitWhenOpened?: number;
  /**
   * Number of lines to show when the `MultiSelect` dropdown is closed.
   */
  lineLimitWhenClosed?: number;
  /**
   * Maximum width of the selected items in the Multiselect. This can be used to limit the width of tags.
   */
  tagMaxWidth?: string;
}

export const MultiSelect = forwardRef<HTMLInputElement, IMultiSelect>((props, ref) => {
  const {
    ariaInputLabel,
    ariaListboxResult,
    disabled = false,
    helperText,
    helperTextPrefix,
    id,
    inputValue,
    itemRenderer,
    label,
    lineLimitWhenClosed,
    lineLimitWhenOpened,
    loading = false,
    loadingText,
    maxItems = 5,
    noResultsText,
    onBlur,
    onChange,
    onClear,
    onFocus,
    onInputValueChange,
    onOpen,
    options,
    readOnly = false,
    required = false,
    selectedOptions,
    status = 'default',
    statusMessage,
    statusMessagePrefix,
    testId,
    textMap,
    tagMaxWidth: consumerProvidedTagMaxWidth,
  } = props;

  /**
   * States
   **/

  const runtimeInfo = getRuntimeEnvironmentInfo();
  const isWindows = runtimeInfo.os === 'windows';
  const isMobileOs = runtimeInfo.os === 'android' || runtimeInfo.os === 'ios';

  // listBoxHeight is the calculated height of the ListBox based on list item height
  const [listBoxHeight, setListBoxHeight] = useState(0);
  // overlayVisible controls opening and closing the overlay
  const [overlayVisible, setOverlayVisible] = useState(false);
  // isOverlayRendered is set once the overlay is finished painting
  const [isOverlayRendered, setIsOverlayRendered] = useState(false);
  // option id currently highlighted by keyboard or mouse
  const [softSelectedId, setSoftSelectedId] = useState('');
  const [pressedEndKey, setPressedEndKey] = useState(false);
  // fistOpened indicates when the overlay first becomes visible
  const [firstOpened, setFirstOpened] = useState(true);
  // keyboardInput remembers if a keyboard was used to open the overlay
  const [keyboardInput, setKeyboardInput] = useState(false);
  // track if the component is focused
  const [componentFocused, setComponentFocused] = useState(false);
  // show ul with list items
  const showListBox = useMemo(() => options.length || loading, [options.length, loading]);
  // isListItemFocusVisible monitors when to show focus ring on keyboard navigation to the list item
  const [isListItemFocusVisible, setIsListItemFocusVisible] = useState(false);

  /**
   *  Refs
   */

  // listBoxRef points to the list box
  const listBoxRef = useRef(null);
  // overlayTriggerRef is only a placeholder to satisfy form-field-container's
  // interface
  const overlayTriggerRef = useRef(null);
  // backgroundTriggerRef is used for list element and anchored overlay sizing
  const backgroundTriggerRef = useRef(null);
  // refs to the inputs in the foreground and background
  const overlayInputRef = useRef<HTMLInputElement>(null);
  const backgroundInputRef = useRef<HTMLInputElement>(null);
  // overlayContainerRef points to the container used in the achored overlay
  const overlayContainerRef = useRef<HTMLDivElement>(null);
  // lastControlKeyPressed remembers what control key was used to open the
  // overlay
  const lastControlKeyPressed = useRef<TMultiSelectControlInputKeys>(null);
  // prevOverlayVisible tracks the previous state value of overlayVisible
  // and is used to ensure we correctly set firstOpened to true
  const prevOverlayVisible = useRef(false);
  // map of focus/blur exceptions initialized when the component mounts
  // key is an id, value is a boolean indicating if it should or should not be focused
  const focusExceptionsRef = useRef<Map<string, boolean>>(new Map());
  // test id refs for background and foreground inputs
  const testIdBgRef = useCreateTestId(testId ? `${testId}-bg` : undefined);
  const testIdFgRef = useCreateTestId(testId && overlayVisible && isOverlayRendered ? `${testId}-fg` : undefined);

  /**
   * A11y
   */

  const ariaAttributes = getAriaAttributes(MultiSelect.displayName || 'MultiSelect', {
    id,
    // ariaInputLabel: loading && isMobileOs ? loadingLabel : ariaInputLabel,
    ariaInputLabel,
    bgStatusMessageId: getFormFieldStatusMessageId(id, 'bg'),
    listBoxId: !showListBox ? getNoResultsId(id) : getListBoxId(id),
    required,
    getStatus: props.status === 'error',
    softSelectedId: !showListBox || loading ? undefined : softSelectedId,
    readOnly,
  });
  const loadingStub = useMemo(
    () => (loading ? { title: loadingText, id: `${id}-multiselect-stub`, type: 'loading' } : undefined),
    [loading, loadingText, id]
  );

  const removeTagButtonAriaLabel = textMap.removeTagButtonAriaLabel || '';

  /**
   * Resize/overflow handlers
   */

  const updateItems = useCallback(() => {
    const backgroundElements: HTMLElement[] = [];
    const foregroundElements: HTMLElement[] = [];
    selectedOptions.forEach((option) => {
      const backgroundEl = document.getElementById(`${option.id}-bg`);
      const foregroundEl = document.getElementById(`${option.id}-fg`);
      backgroundEl && backgroundElements.push(backgroundEl as HTMLElement);
      foregroundEl && foregroundElements.push(foregroundEl as HTMLElement);
    });
    setSelectedBackgroundElements(backgroundElements);
    overlaySelectedOptionsRef.current = foregroundElements;
  }, [selectedOptions]);

  const listBoxContainerRef = useRef<HTMLElement | null>(null);
  const overlayListBoxContainerRef = useRef<HTMLElement | null>(null);
  const overlaySelectedOptionsRef = useRef<HTMLElement[]>([]);
  const [selectedBackgroundElements, setSelectedBackgroundElements] = useState<HTMLElement[]>([]);

  const maxItemsToShowForeground = useDynamicResize(
    overlayTriggerRef.current,
    overlaySelectedOptionsRef.current,
    overlayInputRef.current,
    inputValue,
    readOnly,
    overlayVisible
  );

  const maxItemsToShow = useDynamicResize(
    listBoxContainerRef.current,
    selectedBackgroundElements,
    backgroundInputRef.current,
    inputValue,
    readOnly,
    !overlayVisible
  );

  useLayoutEffect(() => {
    updateItems();
  }, [updateItems, selectedOptions]);

  useLayoutEffect(() => {
    // reset items to show all items and determine what items are out of bounds
    if (!overlayVisible && maxItemsToShow === -1) {
      updateItems();
    }
  }, [updateItems, maxItemsToShow, overlayVisible]);

  useLayoutEffect(() => {
    // reset items to show all items and determine what items are out of bounds
    if (overlayVisible && maxItemsToShowForeground === -1) {
      updateItems();
    }
  }, [updateItems, maxItemsToShowForeground, overlayVisible]);

  /**
   * Functions
   */

  function runAnnouncement(selectedId: string, makingSelection?: boolean) {
    if (!selectedId) return;
    const el = document.getElementById(selectedId);
    if (!el) return;
    let textMappingSwitch = selectedOptions.map((item) => item.id).includes(el.id);
    if (makingSelection) {
      textMappingSwitch = !textMappingSwitch;
    }
    const selectedItem = textMap.selectedItem || '';
    const unSelectedItem = textMap.unselectedItem || '';
    const selectionTemplate = textMappingSwitch ? selectedItem : unSelectedItem;
    const listItemTitle = el.textContent || '';
    clearAnnouncer('assertive');
    announce(templateReplacer(selectionTemplate, [listItemTitle]), 'assertive');
  }

  function handleMultilineDisplay(
    maxItemsToShowForeground: number,
    maxItemsToShow: number,
    isBackground: boolean
  ): IDataItem[] {
    // foreground multiline handling, also include maxItemsToShowForeground at 0 foreground for smaller viewports scenarios
    if (maxItemsToShowForeground >= 0 && !isBackground) {
      return selectedOptions.slice(0, maxItemsToShowForeground);
    }
    // background multiline handling
    if (maxItemsToShow >= 0 && isBackground) {
      return selectedOptions.slice(0, maxItemsToShow);
    }
    return selectedOptions;
  }

  function showMoreLabelDisplay(
    maxItemsToShowForeground: number,
    maxItemsToShow: number,
    isBackground: boolean
  ): boolean {
    return (
      (isBackground && maxItemsToShow >= 0 && selectedOptions.length - maxItemsToShow >= 1) ||
      (!isBackground && maxItemsToShowForeground >= 0 && selectedOptions.length - maxItemsToShowForeground >= 1)
    );
  }

  // onSelection is used by the listbox when a selection is made
  function onSelection(dataItem: IDataItem): void {
    if (loading) return;
    makeSelection(dataItem, selectedOptions, onChange, focusExceptionsRef);
    !isWindows && runAnnouncement(dataItem.id, true);
    overlayInputRef?.current?.focus({ preventScroll: true });
  }

  // focus a list box item
  function scrollToListItem(listItemId: string) {
    if (listItemId) {
      const el = document.getElementById(listItemId);
      el &&
        el.scrollIntoView({
          block: 'nearest',
          inline: 'nearest',
        });
      // focus is only used here to get the focus ring
      // to activate as expected, otherwise it would be
      // omitted
      el && el.focus();
    }
  }

  // used for mouse move on the list box items
  function handleMouseMovements(e: React.MouseEvent) {
    setSoftSelectedId(e.currentTarget.id);
    setKeyboardInput(false);
  }

  // toggle visibility of the overlay when clicking
  // on the input in the foreground
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleInputOnClick(e: React.MouseEvent) {
    if (disabled || readOnly) return;
    setKeyboardInput(false);
    if (!overlayVisible) {
      setOverlayVisible(true);
    }
  }

  function handleOnEscape() {
    setOverlayVisible(false);
    setKeyboardInput(false);
    onInputValueChange('');
    backgroundInputRef.current && (backgroundInputRef.current as HTMLElement).focus();
  }

  function handleChevronIconClick() {
    if (disabled || readOnly) return;
    setOverlayVisible((prev) => !prev);
    setKeyboardInput(false);
  }

  function handleOnInput(e: React.ChangeEvent<HTMLInputElement>) {
    setOverlayVisible(true);
    onInputValueChange(e.currentTarget.value);
  }

  /**
   *  Context
   */

  const formSelectContainerContext = useMemo(
    () => ({
      label,
      disabled,
      readOnly,
      required,
      status,
      helperText,
      helperTextPrefix,
      statusMessage,
      statusMessagePrefix,
    }),
    [label, disabled, readOnly, required, status, helperText, helperTextPrefix, statusMessage, statusMessagePrefix]
  );

  const listItemContext: IListItemContext = {
    itemRenderer,
    selectionType: loading ? ('checkmark' as TSelectionType) : ('checkbox' as TSelectionType),
    onMouseMove: handleMouseMovements,
    isFocusVisible: isListItemFocusVisible,
  };

  /**
   * Effects
   */

  // setup focus/blur exceptions when component mounts
  useEffect(
    () => {
      focusExceptionsRef.current.set(getInputId(id, 'fg'), true);
      focusExceptionsRef.current.set(getInputId(id, 'bg'), true);
      focusExceptionsRef.current.set(getChevronIconId(id, 'fg'), true);
      focusExceptionsRef.current.set(getChevronIconId(id, 'bg'), true);
      selectedOptions.forEach((selectedOption) => {
        focusExceptionsRef.current.set(getTagRemoveButtonId(selectedOption), true);
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  // TODO in future -- these should be refs, not ids, so they don't need to be
  // searched inside the hook -- but this could introduce timing issues
  useComponentFocused(
    // check if elements have mouse or keyboard event fire inside
    // including background & foreground form-field, listbox
    [getFormFieldId(id, 'bg'), getOverlayContainerId(id)],
    // eventListener types
    undefined,
    // onFocus callback
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      onFocus?.();
      setComponentFocused(true);
    },
    // onBlur callback
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (targetId: string) => {
      setOverlayVisible(false);
      setComponentFocused(false);
      setIsListItemFocusVisible(false);
      onBlur?.();
    },
    focusExceptionsRef.current,
    false
  );
  // run keyboard actions from the background/trigger element
  // once the overlay is painted
  useEffect(() => {
    if (overlayVisible && keyboardInput && isOverlayRendered) {
      inputOnKeyDownOnceUlRendered(
        overlayInputRef,
        lastControlKeyPressed,
        listBoxRef,
        setSoftSelectedId,
        setPressedEndKey,
        softSelectedId
      );
    }
  }, [
    overlayVisible,
    isOverlayRendered,
    overlayInputRef,
    listBoxRef,
    setSoftSelectedId,
    softSelectedId,
    keyboardInput,
  ]);

  // Effects to run when opening and closing the multiselect
  useEffect(() => {
    if (overlayVisible && isOverlayRendered) {
      if (prevOverlayVisible.current !== overlayVisible && !prevOverlayVisible.current) {
        setFirstOpened(true);
      } else setFirstOpened(false);
      // logic to highlight the first list item when using only the mouse
      firstOpened && onOpen?.();
      !keyboardInput && firstOpened && setSoftSelectedId(options.length ? options[0].id : '');
      // occasionally some mouse & keyboard combinations do not focus the overlay input,
      // and this cannot always be reproduced, hence setTimeout was added
      setTimeout(() => overlayInputRef?.current?.focus({ preventScroll: true }));
    } else if (!overlayVisible) {
      setFirstOpened(true);
      setIsOverlayRendered(false);
      setSoftSelectedId('');
      // focus the background input if the overlay closed and still in focus
      if (
        prevOverlayVisible.current !== overlayVisible &&
        prevOverlayVisible.current &&
        componentFocused &&
        (!selectedOptions.length || !keyboardInput)
      ) {
        backgroundInputRef?.current?.focus();
      }
    }
    prevOverlayVisible.current = overlayVisible;
  }, [
    overlayVisible,
    keyboardInput,
    overlayInputRef,
    componentFocused,
    isOverlayRendered,
    selectedOptions.length,
    options,
    firstOpened,
    onOpen,
  ]);

  // scroll to soft selected list item and then focus the
  // foreground input
  useEffect(() => {
    !isWindows && runAnnouncement(softSelectedId);
    scrollToListItem(softSelectedId);
    overlayInputRef?.current?.focus({ preventScroll: true });
    pressedEndKey && setPressedEndKey(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pressedEndKey, softSelectedId]);

  // elements used to calculate the max width of the tags
  const textFieldContainerElement = document.getElementById(`${id}-text-field-container-bg`);

  const tagMaxWidth = useMemo(() => {
    const chevronIconButtonElementWidth = document.getElementById(getChevronIconId(id, 'bg'))?.offsetWidth;
    if (!chevronIconButtonElementWidth || !textFieldContainerElement) {
      return `100%`;
    }
    const formFieldElementWidth = textFieldContainerElement.clientWidth;
    // Consider padding
    const computedStyle = window.getComputedStyle(textFieldContainerElement);
    const paddingLeft = parseFloat(computedStyle.paddingLeft || '0');
    const paddingRight = parseFloat(computedStyle.paddingRight || '0');
    // Calculate the max width for the tag
    // extra padding between tag and chevronIcon
    const extraPadding = convertToPx(getTokenValue('--evr-spacing-2xs')); // 8px
    const maxWidth = formFieldElementWidth - chevronIconButtonElementWidth - paddingLeft - paddingRight - extraPadding;

    return `${Math.max(maxWidth, 0)}px`;
    // Disabling the eslint rule because this calculation depends on the dynamic clientWidth of textFieldContainerElement
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, textFieldContainerElement?.clientWidth]);

  useLayoutEffect(() => {
    if (overlayVisible) {
      if (!listBoxRef.current) {
        return;
      }
      const listItem = (listBoxRef.current as HTMLElement).firstElementChild as HTMLElement;
      if (!listItem) return;
      const newListBoxHeight = loadingStub
        ? listItem.offsetHeight
        : Math.min(maxItems, options.length) * listItem.offsetHeight;
      setListBoxHeight(newListBoxHeight);

      const optionsIds = options.map((option) => option.id);
      if (!options.length) {
        setSoftSelectedId('');
        return;
      }
      !optionsIds.includes(softSelectedId) && setSoftSelectedId(options[0].id);
    }
  }, [
    maxItems,
    options,
    options.length,
    overlayVisible,
    selectedOptions,
    selectedOptions.length,
    inputValue,
    softSelectedId,
    loadingStub,
  ]);

  // ensure the overlay input gets focused when there are
  // no options
  useEffect(() => {
    if (!options.length) {
      overlayInputRef?.current?.focus({ preventScroll: true });
    }
  }, [options]);

  const renderNoResults = () => {
    return (
      <div className={multiSelectStyles.evrMultiSelectNoResult} id={getNoResultsId(id)} role="alert">
        <p>{noResultsText}</p>
      </div>
    );
  };

  const handleOnRemove = (selectedOption: IDataItem, isBackground: boolean) => {
    if (readOnly) {
      return undefined;
    }

    return () => {
      makeSelection(selectedOption, selectedOptions, onChange, focusExceptionsRef);
      isBackground ? backgroundInputRef?.current?.focus() : overlayInputRef?.current?.focus({ preventScroll: true });
    };
  };

  const renderInput = (
    isBackground: boolean,
    focused: boolean,
    chevronIconOpen: boolean,
    borderContainerRef: React.MutableRefObject<null>
  ) => {
    const position: TMultiSelectPosition = isBackground ? 'bg' : 'fg';
    const inputId = getInputId(id, position);
    const backgroundTabIndex = overlayVisible ? -1 : 0;
    // form select container id is transformed by the FormSelectContainer
    const formSelectContainerId = `${id}-${position}`;
    const formSelectContainerProps = {
      id: formSelectContainerId,
      testId: testId ? getInputTestId(testId, position) : undefined,
      // form field props
      labelId: getFormFieldLabelId(id, position),
      statusMessageId: getFormFieldStatusMessageId(id, position),
      focused,
      borderContainerRef,
      height: 'auto',
      maxHeight: !readOnly
        ? getFormFieldMaxHeight(isBackground, status, lineLimitWhenOpened, lineLimitWhenClosed)
        : undefined,
      hideFocusRing: !isBackground,
      htmlFor: inputId,
      onClickLabel: (e: React.MouseEvent) => {
        e.preventDefault();
        if (overlayVisible) {
          setOverlayVisible(false);
        }
        backgroundInputRef?.current?.focus();
      },
      textMap: { clearButton: textMap.clearButton, spinnerAriaLabel: textMap.spinnerAriaLabel },
      // select container props
      disabled,
      readOnly,
      chevronIconOpen,
      onClear:
        isBackground && selectedOptions.length > 0
          ? () => {
              // using setTimeout to resolve focus-timing issue on ipad
              setTimeout(() => {
                overlayInputRef?.current?.focus({ preventScroll: true });
              });
              onClear?.();
            }
          : undefined,
      clickInsteadOfMouseDownOverride: true,
      ariaHidden: isBackground && overlayVisible,
    };

    return (
      <FormSelectContainer
        {...formSelectContainerProps}
        hideLabel={!isBackground || !label}
        bottomBorder={!isBackground}
        hideStatusMessage={!isBackground}
        onChevronIconClick={handleChevronIconClick}
        chevronIconVerticalAlign={'top'}
      >
        <div
          id={`${id}-text-field-container-${isBackground ? 'bg' : 'fg'}`}
          className={classnames(multiSelectStyles.evrMultiSelect, {
            [multiSelectStyles.error]: status === 'error',
            [multiSelectStyles.disabled]: disabled,
            [multiSelectStyles.open]: !isBackground,
          })}
        >
          {/* We already have ariaInputLabel and its associated mechanism as a mandatory field, 
          so we left this with aria-hidden: true to prevent any overlap. This is mainly to add 
          the label markup so we can add CSS to trigger the clickable area. Reasons is to 
          eliminate the need to add a clickable (onClick) event emitter on containers that isn't 
          focusable/in the tab order as per MDN docs.*/}
          <label
            className={classnames(multiSelectStyles.inputLabel, {
              [multiSelectStyles.disabled]: disabled,
              [multiSelectStyles.readOnly]: readOnly,
            })}
            data-testid={testId ? `${testId}-input-label-${isBackground ? 'bg' : 'fg'}` : undefined}
            htmlFor={inputId}
            aria-hidden={true}
          >
            {ariaInputLabel}
          </label>
          {
            <ul
              id={`${id}-text-field-list-box-${isBackground ? 'bg' : 'fg'}`}
              className={multiSelectStyles.evrTextFieldListBox}
              ref={
                (isBackground ? listBoxContainerRef : overlayListBoxContainerRef) as React.RefObject<HTMLUListElement>
              }
            >
              {handleMultilineDisplay(maxItemsToShowForeground, maxItemsToShow, isBackground).map((selectedOption) => (
                <li
                  id={`${selectedOption.id}-${isBackground ? 'bg' : 'fg'}`}
                  className={multiSelectStyles.tagListItem}
                  key={selectedOption.id}
                >
                  <Tag
                    id={`${selectedOption.id}-tag`}
                    testId={testId ? `${testId}-${selectedOption.id}-tag-${isBackground ? 'bg' : 'fg'}` : undefined}
                    label={selectedOption.title}
                    onRemove={handleOnRemove(selectedOption, isBackground)}
                    removeButtonAriaLabel={templateReplacer(removeTagButtonAriaLabel, [selectedOption.title])}
                    disabled={(isBackground && overlayVisible) || disabled}
                    maxWidth={consumerProvidedTagMaxWidth ?? tagMaxWidth}
                  />
                </li>
              ))}
              {showMoreLabelDisplay(maxItemsToShowForeground, maxItemsToShow, isBackground) &&
                textMap.moreSelectedOptionsLabel && (
                  <li id={`${id}-more-selected-label-${isBackground ? 'bg' : 'fg'}`}>
                    <div
                      className={multiSelectStyles.evrMoreSelectedOptionLabel}
                      data-testid={testId ? `${testId}-more-label-${isBackground ? 'bg' : 'fg'}` : undefined}
                    >
                      <span>
                        {isBackground
                          ? templateReplacer(textMap.moreSelectedOptionsLabel, [
                              (selectedOptions.length - maxItemsToShow).toString(10),
                            ])
                          : templateReplacer(textMap.moreSelectedOptionsLabel, [
                              (selectedOptions.length - maxItemsToShowForeground).toString(10),
                            ])}
                      </span>
                    </div>
                  </li>
                )}
              {/* Setting the input inside the listbox as a listitem mainly due to styling - we need this to be within the
              same flex container as with the tags so it can appropriately wrap alongside with it */}
              <li id={`li-${inputId}`} key={inputId}>
                {/* eslint-disable-next-line jsx-a11y/aria-activedescendant-has-tabindex */}
                <input
                  {...ariaAttributes.input(isBackground)}
                  className={classnames('evrBodyText1', multiSelectStyles.evrMultiSelect, multiSelectStyles.input, {
                    [multiSelectStyles.disabled]: disabled,
                    [multiSelectStyles.nonEmpty]: inputValue.length > 0,
                  })}
                  type="text"
                  ref={mergeRefs(
                    isBackground ? [ref, backgroundInputRef, testIdBgRef] : [ref, overlayInputRef, testIdFgRef]
                  )}
                  id={inputId}
                  tabIndex={disabled || isBackground ? backgroundTabIndex : 0}
                  value={inputValue}
                  onClick={handleInputOnClick}
                  onKeyDown={(e) => {
                    setIsListItemFocusVisible(true);
                    setKeyboardInput(true);
                    inputOnKeyDown(
                      disabled,
                      readOnly,
                      isBackground,
                      overlayVisible,
                      softSelectedId,
                      options,
                      lastControlKeyPressed,
                      e,
                      setSoftSelectedId,
                      setOverlayVisible,
                      onSelection,
                      handleOnEscape,
                      backgroundInputRef,
                      listBoxRef,
                      inputValue
                    );
                  }}
                  disabled={disabled}
                  readOnly={readOnly} // NVDA issue with readonly: https://github.com/nvaccess/nvda/issues/13672#issuecomment-1641843666
                  onInput={handleOnInput}
                  autoComplete={'off'}
                  autoCorrect={'off'}
                  spellCheck={false}
                />
              </li>
            </ul>
          }
        </div>
      </FormSelectContainer>
    );
  };

  const renderOverlayContent = () => {
    let overlayContentResults: JSX.Element = <></>;
    if (showListBox) {
      const scopedOptions = loading ? ([loadingStub] as IDataItem[]) : options;
      const firstListItemTitle = scopedOptions[0].title || '';
      const isSelectedOption = loading
        ? false
        : selectedOptions.map((option) => option.id).includes(scopedOptions[0].id);

      const selectedItem = textMap.selectedItem || '';
      const unSelectedItem = textMap.unselectedItem || '';
      const selectionTemplate = isSelectedOption ? selectedItem : unSelectedItem;
      const ariaOptionAnnoucement = loading
        ? loadingText
        : `${ariaListboxResult} ${templateReplacer(selectionTemplate, [firstListItemTitle])}`;

      overlayContentResults = (
        <>
          <ListItemContext.Provider value={listItemContext}>
            <ListBox
              {...ariaAttributes.listBox}
              options={scopedOptions}
              height={listBoxHeight ? `${listBoxHeight}px` : 'auto'}
              selectedOptions={selectedOptions}
              softSelectedId={softSelectedId}
              onSelection={onSelection}
              id={getListBoxId(id)}
              listBoxRef={listBoxRef}
              overrideSetSoftSelected
              focusOnSoftSelected={false}
              testId={testId ? `${testId}-list-box` : undefined}
              textMap={textMap}
            />
          </ListItemContext.Provider>
          <div
            className={multiSelectStyles.evrMultiSelectVisuallyHidden}
            role="alert"
            tabIndex={isMobileOs ? undefined : -1}
          >
            {ariaOptionAnnoucement}
          </div>
        </>
      );
    } else {
      overlayContentResults = renderNoResults();
    }
    return (
      <div
        className={classnames(multiSelectStyles.evrOverlayContainer, {
          [multiSelectStyles.error]: status === 'error',
        })}
        ref={overlayContainerRef}
        id={getOverlayContainerId(id)}
      >
        {renderInput(false, false, true, overlayTriggerRef)}
        {overlayContentResults}
      </div>
    );
  };

  return (
    <FormFieldContainerContext.Provider value={formSelectContainerContext}>
      <TriggerAreaStyledOverlay
        id={`${id}-trigger-area-overlay`}
        triggerRef={backgroundTriggerRef}
        overlayVisible={overlayVisible}
        overlayHeight={'auto'}
        error={status === 'error'}
        renderTriggerAreaContent={() => renderInput(true, false, false, backgroundTriggerRef)}
        renderOverlayContent={renderOverlayContent}
        onInitPosition={() => {
          setIsOverlayRendered(true);
        }}
        overrideFocusRingDefaultBehavior={isListItemFocusVisible}
      />
    </FormFieldContainerContext.Provider>
  );
});

MultiSelect.displayName = 'MultiSelect';
