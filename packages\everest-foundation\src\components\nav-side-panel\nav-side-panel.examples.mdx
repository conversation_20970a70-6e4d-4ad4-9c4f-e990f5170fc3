import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { useEffect, useRef, useState } from 'react';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { AlphaBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';
import { NavSidePanel } from '.';
import { Button } from '../button';
import { useEverestContext } from '../everest-provider';

<AlphaBanner />

export const scope = {
  Button,
  NavSidePanel,
  useEffect,
  useRef,
  useState,
  useEverestContext,
};

## How to Use

`open` is used to toggle the visibility of the NavSidePanel. When `open` is set to true, the NavSidePanel opens, and it triggers the `onOpen` callback function. NavSidePanel can be closed by setting `open` to false and when it is set to false, it triggers the `onClose` callback function. <br /><br />
The `onOpen` and `onClose` callback functions are used to perform operations when the NavSidePanel opens and closes respectively. <br /><br />
The NavSidePanel can be closed by pressing the <kbd>Escape</kbd> keyboard button or by clicking outside the side panel element. Close on click outside functionality can be controlled by a return reason of the `onClose` callback.

Similarly closing by <kbd>Escape</kbd> keyboard button can be controlled by a return reason of the `onClose` callback. See [Disable NavSidePanel closing on Escape key](#disable-navsidepanel-closing-on-escape-key) for example. <br /> <br /><br />

## Responsive

A responsive NavSidePanel can be achieved through use of the `useEverestContext` hook provided by <LinkTo kind="Foundations/Everest Provider">EverestProvider</LinkTo>. The following breakpoint ranges should be referenced to determine which value to supply to `size`. This is demonstrated in the [Use useEverestContext to dynamically size the NavSidePanel](#use-useeverestcontext-to-dynamically-size-the-navsidepanel) example below.

| Breakpoints          | Size       |
| -------------------- | ---------- |
| &leq; 429 (xs)       | fullscreen |
| 430 - 767 (sm)       | sm         |
| 768 - 1279 (md)      | md         |
| 1280 - 1439 (lg)     | lg         |
| &geq; 1440 (xl, xxl) | xl         |

## Accessibility

`ariaLabel` should be set for the screen reader to announce the purpose of the NavSidePanel

## Variations

### NavSidePanel with Children

export const navSidePanelChildren = `() => {
    const [open, setOpen] = useState(false);
    const sidePanelRef = useRef(null);
    const triggerBtnChildrenRef = useRef(null);
    return (
        <>
          <Button
            id="trigger-button-id-children"
            label="Example with Children"
            ref={triggerBtnChildrenRef}
            onClick={() => {
              setOpen(!open)
            }}
          />
          <div>
            <NavSidePanel
              open={open}
              id="nav-side-panel-id-children"
              open={open}
              ariaLabelledBy="main-menu-header"
              anchor="left"
              size="lg"
              ref={sidePanelRef}
              onOpen={()=>{
                // focus the container as per a11y team
                sidePanelRef && sidePanelRef.current.focus();
              }}
              onClose={(e) => {
                setOpen(false);
                triggerBtnChildrenRef && triggerBtnChildrenRef.current.focus()
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2-children"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    setOpen(!open);
                  }}
                />
              </div>
              <h3 id="main-menu-header" className="evrHeading3" style={{ marginLeft: '15px' }}>Main Menu</h3>
              <div className="evrBodyText">
                <ul id="my-menu-list-2" aria-label="Menu List">
                  <li id="planes-item-2">Menu Item #1</li>
                  <li id="trains-item-2">Menu Item #2</li>
                  <li id="cats-item-2">Menu Item #3</li>
                  <li id="dogs-item-2">Menu Item #4</li>
                </ul>
              </div>
            </NavSidePanel>
          </div>
        </>
    )
}`;

<CodeExample scope={scope} code={navSidePanelChildren} />

### Disable NavSidePanel closing on Escape key

The NavSidePanel closing by pressing the <kbd>Escape</kbd> keyboard button can be prevented by the return reason of the `onClose` i.e. `onClose` return a reason `escapeKeyDown`.

export const escapeButtonCode = `() => {
    const [open, setOpen] = useState(false);
    const sidePanelRef = useRef(null);
    const triggerBtnEscapeRef = useRef(null);
    return (
        <>
          <Button
            id="trigger-button-id-escape-keydown"
            label="Disable NavSidePanel closing on Escape key"
            ref={triggerBtnEscapeRef}
            onClick={() => {
              setOpen(!open)
            }}
          />
          <div>
            <NavSidePanel
              open={open}
              id="nav-side-panel-id-escape-keydown"
              open={open}
              ariaLabelledBy="main-menu-header"
              anchor="left"
              size="lg"
              ref={sidePanelRef}
              onOpen={()=>{
                // focus the container as per a11y team
                sidePanelRef && sidePanelRef.current.focus();
              }}
              onClose={(e) => {
                if (e && e.reason!=='escapeKeyDown'){ 
                  setOpen(false);
                  setTimeout(() => {
                    triggerBtnEscapeRef && triggerBtnEscapeRef.current.focus();
                  });
                }
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2-escape-keydown"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    setOpen(!open);
                    setTimeout(() => {
                      triggerBtnEscapeRef && triggerBtnEscapeRef.current.focus();
                    });
                  }}
                />
              </div>
              <h3 id="main-menu-header" className="evrHeading3" style={{ marginLeft: '15px' }}>Main Menu</h3>
              <div className="evrBodyText">
                <ul id="my-menu-list-2" aria-label="Menu List">
                  <li id="planes-item-2">Menu Item #1</li>
                  <li id="trains-item-2">Menu Item #2</li>
                  <li id="cats-item-2">Menu Item #3</li>
                  <li id="dogs-item-2">Menu Item #4</li>
                </ul>
              </div>
            </NavSidePanel>
          </div>
        </>
    )
}`;

<CodeExample scope={scope} code={escapeButtonCode} />

### Use `useEverestContext` to dynamically size the NavSidePanel

This example demonstrates a responsive NavSidePanel by dynamically updating the `size` prop using Everest Provider's `breakpoint` property from the `useEverestContext` hook.

export const responsiveNavSidePanel = `() => {
  const { breakpoint } = useEverestContext();
  const [open, setOpen] = useState(false);
  const [size, setSize] = useState('md');
  const navSidePanelRef = useRef(null);
  const triggerBtnUseEverestContext = useRef(null);
  useEffect(() => {
    switch (breakpoint) {
      case 'xs':
        setSize('fullscreen');
        break;
      case 'sm':
        setSize('sm');
        break;
      case 'md':
        setSize('md');
        break;
      case 'lg':
        setSize('lg');
        break;
      case 'xl':
      case 'xxl':
        setSize('xl');
        break;
      default:
        setSize('md');
    }
  }, [breakpoint]);
  return (
    <>
      <Button
        id='trigger-button-use-everest-context'
        label={'Responsive Example (Breakpoint: ' + breakpoint + ') (Size: ' + size + ')'}
        ref={triggerBtnUseEverestContext}
        onClick={() => setOpen(!open)}
      />
      <div>
        <NavSidePanel
          open={open}
          id="nav-side-panel-id-use-everest-context"
          open={open}
          ariaLabelledBy="main-menu-header"
          anchor="left"
          size={size}
          ref={navSidePanelRef}
          onOpen={()=>{
            // focus the container as per a11y team
            navSidePanelRef && navSidePanelRef.current.focus();
          }}
          onClose={(e) => {
            setOpen(false);
            triggerBtnUseEverestContext && triggerBtnUseEverestContext.current.focus()
          }}
        >
          <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
            <Button
              id="trigger-button-id-2-use-everest-context"
              label="Close NavSidePanel"
              onClick={(e) => {
                setOpen(!open);
              }}
            />
          </div>
          <div style={{ marginLeft: '15px' }}>
            <h3 id="main-menu-header" className='evrHeading3'>Breakpoint: {breakpoint} <br /> Size: {size}</h3>
          </div>
          <div className="evrBodyText">
            <p className="evrBodyText" style={{ margin: '15px' }}>Altering the size of the window will dynamically update the breakpoint value and the size of the SidePanel.</p>
            <ul id="my-menu-list-2" aria-label="Menu List">
              <li id="planes-item-2">Menu Item #1</li>
              <li id="trains-item-2">Menu Item #2</li>
              <li id="cats-item-2">Menu Item #3</li>
              <li id="dogs-item-2">Menu Item #4</li>
            </ul>
          </div>
        </NavSidePanel>
      </div>
    </>
  );
}`;

<CodeExample scope={scope} code={responsiveNavSidePanel} />
