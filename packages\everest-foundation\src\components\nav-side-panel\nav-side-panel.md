# Nav Side Panel

## Summary

Research and document implementations for the Everest Nav Side Panel.

- Start Date: 2023-08-17

- Figma link: https://www.figma.com/file/NWBs6gjIqjocI5BBWNT4gT/Message-Center?type=design&node-id=1-47965&mode=design&t=3S5qm3Eod7C5H2dF-0 (based on Message Center mocks)

- Side Panel Figma link: https://www.figma.com/file/yR0VRua16iizwZrN1SOiUH/%F0%9F%A7%AA-Side-Panel?type=design&node-id=6306-111064&mode=design&t=Jcl2KQCVL46ZsQwH-0

## Detailed Design

`SidePanel` and `NavSidePanel` share similar traits, with some minor differences, hence both components be partially passing down to common props to an internal `SidePanelBase` component.

## API

`SidePanelBase` (internal) - The base component will take in `children` props, hence will use `PropsWithChildren`

1.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the side panel.
1.  **id**: `string`
    Mandatory. Sets the id of the side panel.
1.  **open**: `boolean`
    Open side panel when it is set to true, closes the side panel when it is false.
1.  **anchor**: `left | right` (TAnchor)
    Optional. Placement of the side panel, default is `left`.
1.  **ariaLabel**: `string`
    Optional. Sets the value for **aria-label** attribute on the side panel.
1.  **ariaLabelledBy**: `string | undefined`
    Optional. Sets the value for the **aria-labelledby** attribute of the side panel.
1.  **ariaDescribedBy**: `string | undefined`
    Optional. Sets the value for the **aria-describedby** attribute of the side panel.
1.  **onOpen**: `() => void | undefined`
    Optional. Sets a callback function that is executed when the side panel is opened.
1.  **onClose**: `() => void | undefined`
    Optional. Sets a callback function that is executed when the side panel is closed.
1.  **disableCloseOnClickOutside**: `boolean`
    Optional. Default value is `false`. It will close the side panel when the user clicks outside the side panel component.
1.  **width**: `fullscreen | md | lg | xl`
    Mandatory. Sets the width of the side panel.

`NavSidePanel` will include all of `SidePanelBase`'s props

The `NavSidePanel` is composed of a `<nav>` element to provide a landmark for accessibility.

### NavSidePanel User Implementation

```jsx
    () => {
        const [open, setOpen] = useState(false);
        return (
            <NavSidePanel id="nav-side-panel" onClose={} onOpen={} open={open}>
                <ul>
                    ...
                </ul>
            </NavSidePanel>
        )
    }
```

### SidePanelBase Dev Implementation

```jsx
return (
  <Overlay>
    <FocusTrap>{children}</FocusTrap>
  </Overlay>
);
```

### SidePanel Dev Implementation

```jsx
return (
  <SidePanelBase>
    <SidePanelHeader />
    <SidePanelBody />
    <SidePanelFooter />
  </SidePanelBase>
);
```

### NavSidePanel Dev Implementation

```jsx
return (
  <SidePanelBase>
    <nav aria-label={ariaLabel}>{children}</nav>
  </SidePanelBase>
);
```

## Usage

Similar to `SidePanel`, the NavSidePanel will use the overlay component to take up a part of the section on the screen.

## Future Considerations

- Offset/double lightbox
  Enable SidePanel to mimic design with top offset for the dark lightbox, and keep the clear lightbox to make it visually looks like the Dayforce top navigation wasn't covered.
- NavSidePanel Header/Footer aren't required and we don't know if we will need it. in case we need to add Header/Footer later, that would just be Breaking Changes

## Accessibility

- Screenreaders by default will announce the contents when `aria-label`, `aria-labelledby`, and/or `aria-describedby` is provided on the NavSidePanel
- <ESC> keyboard button is set to close the NavSidePanel
- FocusTrap will be set in the NavSidePanel until an action is performed
- Besides mouse click outside the NavSidePanel dismissing it, the lightbox should prevent any other mouse click interactions on contents outside the NavSidePanel
- Utilizing NavSidePanel adds the <nav> landmark, providing `ariaLabel` prop to this should be necessary if no visible title element is provided for the navigation

## Q&A

1. Where does Compose button go?
   It is up to the feature team, but probably as a list item under nav > ul > li
1. Where does Close button go?
   Feature team will handle it, we don't have close button built in

## Other Design Systems

### Dell Design System - https://www.delldesignsystem.com/components/side-navigation/?tab=Code

- Navigation landmark with nav element and usage of aria-label for title-less side panel

## Required PBIs

EDS-3390 - Create Nav Side Panel - component

## Acceptance Criteria

- Create `<NavSidePanel>` component
- Add NavSidePanel stories, examples, and documentation
- Styles are on par with Figma design
- Verify all API props and behaviors are working as intended
- Guidance on accessibility and usage of NavSidePanel
- Unit, visual, and integration tests implemented

## Change Log

10/17/2023: EDS-3564 - updated onClick callback to return a reason 'escapeKeyDown' when escape button is pressed and 'lightBoxClick' when clicked outside. Deprecated the `disableCloseOnClickOutside` prop.

10/19/2023: EDS-3336: Remove triggerRef prop from SidePanel as it is not used in the code due to an approach change.

11/7/2023: EDS-3672 - Removed the `disableCloseOnClickOutside` prop from SidePanel
