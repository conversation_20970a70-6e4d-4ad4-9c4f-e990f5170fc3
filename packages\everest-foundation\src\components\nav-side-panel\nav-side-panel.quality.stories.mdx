import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { But<PERSON> } from '../button';
import { NavSidePanel } from '../nav-side-panel';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/NavSidePanel"
  component={NavSidePanel}
  decorators={[ChromaticDecorators.setHeightTo100vh, ChromaticDecorators.setWidthTo100vw]}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: { breakpointXs: { disable: true } }, // disable default mode and specify per story
    },
  }}
/>

# Fullscreen Size

## Live Demo

<Canvas>
  <Story
    name="Fullscreen Size"
    parameters={{
      chromatic: { modes: { breakpointXs: { disable: false } } } /* re-enable breakpointXs for fullscreen only */,
    }}
  >
    {() => {
      const [open, setOpen] = React.useState(true);
      const sidePanelRef = React.useRef(null);
      const triggerBtnFullscreenRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id-fullscreen"
            label="Open Panel"
            ref={triggerBtnFullscreenRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <div>
            <NavSidePanel
              open={open}
              id="nav-side-panel-id-fullscreen"
              anchor="left"
              size="fullscreen"
              ref={sidePanelRef}
              onOpen={() => {
                // focus the container as per a11y team
                sidePanelRef && sidePanelRef.current.focus();
              }}
              onClose={(e) => {
                setOpen(false);
                triggerBtnFullscreenRef && triggerBtnFullscreenRef.current?.focus();
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2-fullscreen"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    setOpen(!open);
                  }}
                />
              </div>
              <p className="evrBodyText" style={{ marginLeft: '30px' }}>
                Hello world!
              </p>
            </NavSidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>

# XL Size

## Live Demo

<Canvas>
  <Story name="XL Size" parameters={{ chromatic: { modes: { breakpointXl: defaultModes['breakpointXl'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const sidePanelRef = React.useRef(null);
      const triggerBtnXLRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id-xl"
            label="Open Panel"
            ref={triggerBtnXLRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <div>
            <NavSidePanel
              id="nav-side-panel-id-xl"
              open={open}
              anchor="left"
              size="xl"
              ref={sidePanelRef}
              onOpen={() => {
                // focus the container as per a11y team
                sidePanelRef && sidePanelRef.current.focus();
              }}
              onClose={(e) => {
                setOpen(false);
                triggerBtnXLRef && triggerBtnXLRef.current?.focus();
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2-xl"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    setOpen(!open);
                  }}
                />
              </div>
              <p className="evrBodyText" style={{ marginLeft: '30px' }}>
                Hello world!
              </p>
            </NavSidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>

# LG Size

## Live Demo

<Canvas>
  <Story name="LG Size" parameters={{ chromatic: { modes: { breakpointLg: defaultModes['breakpointLg'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const sidePanelRef = React.useRef(null);
      const triggerBtnLGRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id-lg"
            label="Open Panel"
            ref={triggerBtnLGRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <div>
            <NavSidePanel
              open={open}
              id="nav-side-panel-id-lg"
              anchor="left"
              size="lg"
              ref={sidePanelRef}
              onOpen={() => {
                // focus the container as per a11y team
                sidePanelRef && sidePanelRef.current.focus();
              }}
              onClose={(e) => {
                setOpen(false);
                triggerBtnLGRef && triggerBtnLGRef.current?.focus();
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2-lg"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    setOpen(!open);
                  }}
                />
              </div>
              <p className="evrBodyText" style={{ marginLeft: '30px' }}>
                Hello world!
              </p>
            </NavSidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>

# MD Size

## Live Demo

<Canvas>
  <Story name="MD Size" parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const sidePanelRef = React.useRef(null);
      const triggerBtnMDRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id-md"
            label="Open Panel"
            ref={triggerBtnMDRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <div>
            <NavSidePanel
              id="nav-side-panel-id-md"
              open={open}
              anchor="left"
              size="md"
              ref={sidePanelRef}
              onOpen={() => {
                // focus the container as per a11y team
                sidePanelRef && sidePanelRef.current.focus();
              }}
              onClose={(e) => {
                setOpen(false);
                triggerBtnMDRef && triggerBtnMDRef.current?.focus();
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2-md"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    setOpen(!open);
                  }}
                />
              </div>
              <p className="evrBodyText" style={{ marginLeft: '30px' }}>
                Hello world!
              </p>
            </NavSidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>

# SM Size

## Live Demo

<Canvas>
  <Story name="SM Size" parameters={{ chromatic: { modes: { breakpointSm: defaultModes['breakpointSm'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const sidePanelRef = React.useRef(null);
      const triggerBtnSMRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id-sm"
            label="Open Panel"
            ref={triggerBtnSMRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <div>
            <NavSidePanel
              id="nav-side-panel-id-sm"
              open={open}
              anchor="left"
              size="sm"
              ref={sidePanelRef}
              onOpen={() => {
                // focus the container as per a11y team
                sidePanelRef && sidePanelRef.current.focus();
              }}
              onClose={(e) => {
                setOpen(false);
                triggerBtnSMRef && triggerBtnSMRef.current?.focus();
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2-sm"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    setOpen(!open);
                  }}
                />
              </div>
              <p className="evrBodyText" style={{ marginLeft: '30px' }}>
                Hello world!
              </p>
            </NavSidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>
