import React from 'react';
import { render, waitFor, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { NavSidePanel, INavSidePanel } from '.';

const sidepanelId = 'side-panel-id';

const sidePanelTestid = 'test-side-panel-id';

const onOpen = jest.fn();
const onClose = jest.fn();

const mockProps: INavSidePanel = {
  id: sidepanelId,
  testId: sidePanelTestid,
  open: true,
  onOpen: onOpen,
  onClose: onClose,
  ariaLabelledBy: '',
  ariaDescribedBy: '',
  anchor: 'right',
  ariaLabel: '',
  size: 'lg',
};
const renderNavSidePanel = (open: boolean) => {
  return (
    <NavSidePanel {...mockProps} open={open}>
      <ul>
        <li>Item 1</li>
      </ul>
    </NavSidePanel>
  );
};

describe('Nav Side Panel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  describe('should open or close as expected', () => {
    it('should render all the children when side panel is visible', () => {
      const { getByTestId } = render(renderNavSidePanel(true));
      expect(getByTestId(sidePanelTestid)).toBeInTheDocument();
    });

    it('should dispatch onClose when side panel is open then closes', async () => {
      const { rerender } = render(renderNavSidePanel(true));
      await waitFor(() => expect(onOpen).not.toHaveBeenCalled());
      rerender(renderNavSidePanel(false));
      await waitFor(() => expect(onClose).toHaveBeenCalled());
    });

    it('should dispatch onOpen when side panel is closed then opens', async () => {
      const { rerender } = render(renderNavSidePanel(false));
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderNavSidePanel(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
    });

    it('should run onOpen and onClose when opened, then closed ', async () => {
      const { rerender } = render(renderNavSidePanel(false));
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderNavSidePanel(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
      rerender(renderNavSidePanel(false));
      await waitFor(() => expect(onClose).toHaveBeenCalled());
    });

    it('onClose should return a reason escapeKeyDown when Escape keyboard button is pressed', async () => {
      render(renderNavSidePanel(true));
      await waitFor(() => expect(onOpen).not.toHaveBeenCalled());
      // we need to focus the nav side panel
      screen.getByTestId(sidePanelTestid).focus();
      await userEvent.keyboard('{Escape}');
      await waitFor(() => expect(onClose).toHaveBeenCalledWith({ reason: 'escapeKeyDown' }));
    });
  });

  describe('click outside', () => {
    const lightBoxTestId = `${sidePanelTestid}-overlay-light-box`;
    it('should call onClose function on side panel when clicked outside', async () => {
      render(renderNavSidePanel(true));

      // clicking on the side panel itself should not close it
      await userEvent.click(screen.getByTestId(sidePanelTestid));
      expect(onClose).toHaveBeenCalledTimes(0);

      await userEvent.click(screen.getByTestId(lightBoxTestId));
      expect(onClose).toHaveBeenCalledWith({ reason: 'lightBoxClick' });
    });
  });
});
