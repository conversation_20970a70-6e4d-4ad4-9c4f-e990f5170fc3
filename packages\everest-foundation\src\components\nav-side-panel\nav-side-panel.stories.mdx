import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { useState, useEffect, useRef } from 'react';
import { NavSidePanel } from '.';
import { MenuList } from '../menu-list';
import { MenuListItem } from '../menu-list';
import { SidePanelHeader, SidePanelNavBody, SidePanelFooter } from '.';
import { Button } from '../button';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';
import Examples from './nav-side-panel.examples.mdx';

<Meta
  title="Everest Labs/Components/NavSidePanel"
  component={NavSidePanel}
  parameters={{
    status: {
      type: 'alpha', //EDS-4035 - no plans to update status from alpha until revisited by design
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/yR0VRua16iizwZrN1SOiUH/%F0%9F%A7%AASide-Panel?type=design&node-id=5520-32197&t=xkaz3RHvJij0p6YA-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Mandatory. Sets the `id` attribute on the side panel',
    },
    open: {
      description: 'Mandatory. Opens or closes the side panel.',
    },
    onClose: {
      control: '-',
      description:
        "Optional. Callback that runs when the side panel closes. Returns an object {reason: 'escapeKeyDown'} when the escape key is pressed and {reason: 'lightBoxClick'} when LightBox is clicked.",
    },
    onOpen: {
      control: '-',
      description: 'Optional. Callback that runs when the side panel opens.',
    },
    anchor: {
      type: 'enum',
      control: 'inline-radio',
      options: ['left', 'right'],
      description: 'Optional. The placement of the side panel relative to the screen',
      table: {
        defaultValue: { summary: 'right' },
      },
    },
    ariaLabelledBy: {
      type: 'string',
      control: '-',
      description: 'Optional. Id of the content inside SidePanelHeader.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: '-',
      description: 'Optional. Id of the SidePanelBody.',
    },
    size: {
      type: 'enum',
      control: 'radio',
      options: ['fullscreen', 'sm', 'md', 'lg', 'xl'],
      description: 'Optional. Sets the width of the side panel.',
      table: {
        defaultValue: { summary: 'sm' },
      },
    },
    ariaLabel: {
      type: 'string',
      description: 'Optional. Sets the value for **aria-label** attribute on the side panel.',
    },
    lightBoxVariant: {
      control: 'inline-radio',
      options: ['dark', 'clear'],
      description:
        'Optional. Define the lightbox variant. **This is an experimental feature and may be removed at any time.**',
      table: {
        defaultValue: { summary: 'dark' },
      },
    },
  }}
  args={{
    id: 'nav-side-panel-id',
    open: false,
    ariaLabelledBy: 'main-menu-header',
    anchor: 'right',
    size: 'sm',
    lightBoxVariant: 'dark',
  }}
/>

# NavSidePanel

<Examples />

## Live Demo

<Canvas>
  <Story name="NavSidePanel">
    {(args) => {
      const [{ open }, updateArgs] = useArgs();
      const navPanelRef = useRef(null);
      const triggerBtnRef = useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id"
            label="NavSidePanel"
            ref={triggerBtnRef}
            onClick={() => updateArgs({ open: !open })}
          />
          <div>
            <NavSidePanel
              {...args}
              ref={navPanelRef}
              onOpen={(e) => {
                action('onOpen')(e);
                // focus the container as per a11y team
                navPanelRef && navPanelRef.current.focus();
              }}
              onClose={(e) => {
                action('onClose')(e);
                updateArgs({ open: false });
                triggerBtnRef && triggerBtnRef.current.focus();
              }}
            >
              <div style={{ marginTop: '30px', marginBottom: '30px', marginLeft: '15px' }}>
                <Button
                  id="trigger-button-id-2"
                  label="Close NavSidePanel"
                  onClick={(e) => {
                    action('onCloseButtonClick')(e);
                    updateArgs({ open: false });
                  }}
                />
              </div>
              <h3 id="main-menu-header" className="evrHeading3" style={{ marginLeft: '15px' }}>
                Main Menu
              </h3>
              <div className="evrBodyText">
                <ul id="my-menu-list" aria-label="Menu List">
                  <li id="planes-item">Menu Item #1</li>
                  <li id="trains-item">Menu Item #2</li>
                  <li id="cats-item">Menu Item #3</li>
                  <li id="dogs-item">Menu Item #4</li>
                </ul>
              </div>
            </NavSidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>

## Props

### Nav Side Panel Props

<ArgsTable story="NavSidePanel" />
