import React, { PropsWithChildren } from 'react';

import { SidePanelBase, ISidePanelBase, TSidePanelSize } from '../side-panel-base';

export interface INavSidePanel extends Omit<ISidePanelBase, 'size'> {
  size?: TSidePanelSize;
}

export const NavSidePanel = React.forwardRef<HTMLDivElement, PropsWithChildren<INavSidePanel>>(
  (props, ref): JSX.Element => {
    const {
      id,
      testId,
      children,
      open,
      onOpen,
      onClose,
      ariaLabel,
      ariaLabelledBy,
      ariaDescribedBy,
      anchor = 'right',
      size = 'sm',
      lightBoxVariant,
    } = props;

    return (
      <SidePanelBase
        id={id}
        testId={testId}
        open={open}
        onOpen={onOpen}
        onClose={onClose}
        ariaLabel={ariaLabel}
        ariaLabelledBy={ariaLabelledBy}
        ariaDescribedBy={ariaDescribedBy}
        anchor={anchor}
        size={size}
        lightBoxVariant={lightBoxVariant}
        ref={ref}
      >
        <nav>{children}</nav>
      </SidePanelBase>
    );
  }
);

NavSidePanel.displayName = 'NavSidePanel';
