import React, { ReactElement, useState } from 'react';

import { Button, ILinkProps } from '../..';

import styles from './notification-banner.module.scss';

export interface INotificationBannerBodyLinkProps {
  id: string;
  testId?: string;
  maxCollapsedLinks?: number; // if < 1, show all links
  expandActionLabel?: string;
  collapseActionLabel?: string;
}

export const NotificationBannerBodyLinks = (
  props: React.PropsWithChildren<INotificationBannerBodyLinkProps>
): JSX.Element => {
  const { id, testId, maxCollapsedLinks, expandActionLabel, collapseActionLabel, children } = props;

  const childrenArray = React.Children.toArray(children) as ReactElement<ILinkProps>[];
  const linksLength = childrenArray.length;

  const [expanded, setExpanded] = useState(false);
  const [linksToRender, setLinksToRender] = useState<ReactElement<ILinkProps>[]>(
    childrenArray.slice(0, maxCollapsedLinks)
  );

  const handleClick = () => {
    if (expanded) {
      setExpanded(false);
      setLinksToRender(childrenArray.slice(0, maxCollapsedLinks));
    } else {
      setExpanded(true);
      setLinksToRender(childrenArray);
    }
  };

  const getButtonLabel = () => {
    if (expanded) return collapseActionLabel;
    return expandActionLabel;
  };

  if (linksLength > 0)
    return (
      <div id={id} data-testid={testId}>
        <ul className={styles.linkList}>
          {linksToRender.map((link) => {
            return <li key={link.props.id}>{link}</li>;
          })}
        </ul>
        {maxCollapsedLinks && linksLength > maxCollapsedLinks && (
          <div className={styles.linkListButton}>
            <Button
              id={`${id}-button`}
              variant="secondaryNeutral"
              label={getButtonLabel() || ''}
              endIcon={expanded ? 'chevronUp' : 'chevronDown'}
              onClick={handleClick}
              testId={testId ? `${testId}-button` : undefined}
            />
          </div>
        )}
      </div>
    );

  return <></>;
};

NotificationBannerBodyLinks.displayName = 'NotificationBannerBodyLinks';
