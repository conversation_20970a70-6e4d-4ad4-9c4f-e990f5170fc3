import React from 'react';

import { ContainerBodyBase, IContainerBodyBaseProps } from '../container-base/container-body-base';

import styles from './notification-banner.module.scss';

export type INotificationBannerBodyProps = Omit<IContainerBodyBaseProps, 'className' | 'roleType'>;

export const NotificationBannerBody = (props: React.PropsWithChildren<INotificationBannerBodyProps>): JSX.Element => {
  const { id, testId, children } = props;

  return (
    <ContainerBodyBase id={id} testId={testId} className={styles.evrNotificationBannerBody}>
      {children}
    </ContainerBodyBase>
  );
};

NotificationBannerBody.displayName = 'NotificationBannerBody';
