import React from 'react';

import { Button } from '../button';

import styles from './notification-banner.module.scss';

export interface INotificationBannerActionButton {
  id: string;
  label: string;
  ariaLabel?: string;
  onClick?: (e: React.MouseEvent) => void;
  testId?: string;
}
export interface INotificationBannerFooterActions {
  id: string;
  primaryAction?: INotificationBannerActionButton;
  secondaryAction?: INotificationBannerActionButton;
  testId?: string;
}

export const NotificationBannerFooterActions = (props: INotificationBannerFooterActions): JSX.Element => {
  const { id, testId, primaryAction, secondaryAction } = props;

  const renderPrimaryActionButton = () => {
    if (primaryAction) {
      const { id: actionId, testId, label, ariaLabel, onClick } = primaryAction;
      return (
        <Button
          id={actionId}
          label={label}
          variant="secondaryNeutral"
          onClick={onClick}
          ariaLabel={ariaLabel}
          testId={testId ? `${testId}-primary-action` : undefined}
        />
      );
    }
  };
  const renderSecondaryActionButton = () => {
    if (secondaryAction) {
      const { id: actionId, testId, label, ariaLabel, onClick } = secondaryAction;
      return (
        <Button
          id={actionId}
          label={label}
          variant="tertiaryNeutral"
          onClick={onClick}
          ariaLabel={ariaLabel}
          testId={testId ? `${testId}-secondary-action` : undefined}
        />
      );
    }
  };

  return (
    <div id={id} className={styles.evrNotificationBannerFooterActions} data-testid={testId}>
      {renderPrimaryActionButton()}
      {renderSecondaryActionButton()}
    </div>
  );
};

NotificationBannerFooterActions.displayName = 'NotificationBannerFooterActions';
