import React from 'react';

import { ContainerFooterBase, IContainerFooterBaseProps } from '../container-base/container-footer-base';

import styles from './notification-banner.module.scss';

export type INotificationBannerFooterProps = Omit<IContainerFooterBaseProps, 'className' | 'roleType'>;

export const NotificationBannerFooter = (
  props: React.PropsWithChildren<INotificationBannerFooterProps>
): JSX.Element => {
  const { id, testId, children } = props;

  return (
    <ContainerFooterBase id={id} testId={testId} className={styles.evrNotificationBannerFooter}>
      {children}
    </ContainerFooterBase>
  );
};

NotificationBannerFooter.displayName = 'NotificationBannerFooter';
