import React from 'react';

import { ContainerHeaderBase, IContainerHeaderBaseProps } from '../container-base/container-header-base';

import styles from './notification-banner.module.scss';

export type INotificationBannerHeaderProps = Omit<IContainerHeaderBaseProps, 'className' | 'minHeight' | 'roleType'>;

export const NotificationBannerHeader = (
  props: React.PropsWithChildren<INotificationBannerHeaderProps>
): JSX.Element => {
  const { id, testId, children } = props;

  return (
    <ContainerHeaderBase id={id} testId={testId} className={styles.evrNotificationBannerHeader}>
      {children}
    </ContainerHeaderBase>
  );
};

NotificationBannerHeader.displayName = 'NotificationBannerHeader';
