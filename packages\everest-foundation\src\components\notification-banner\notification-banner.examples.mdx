import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import {
  NotificationBanner,
  NotificationBannerBody,
  NotificationBannerFooter,
  NotificationBannerFooterActions,
  NotificationBannerHeader,
  NotificationBannerBodyLinks,
} from '.';
import { Button } from '../button';
import { Checkbox } from '../checkbox';
import { Link } from '../link';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';

export const scope = {
  Button,
  NotificationBanner,
  NotificationBannerBody,
  NotificationBannerFooter,
  NotificationBannerFooterActions,
  NotificationBannerHeader,
  NotificationBannerBodyLinks,
  Checkbox,
  Link,
};

The notification banner component is used to alert or communicate a message to the user. This component communicates feedback and is considered a top-level notification.

## Basic Usage

Notification Banner anatomy is made up of the following parts:

- <b>Header:</b> Consists of brief information to identify the message <b>(required)</b>
- <b>Body:</b> Explains or indicates the message, what it affects, and how to resolve
- <b>Footer:</b> Can include actions users can take to resolve the message

export const defaultCode = `() => {
  const styles = {
    container: {
      margin: '0px 20px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      gap: '20px'
    }
  };
  const [visible, setVisible] = React.useState(true);
  const handleClose = () => {
    setVisible(false);
  };
  return !visible ? (
    <div style={styles.container}>
      <Button
        id="basic-usage-trigger-button-id"
        label={'Show banner'}
        onClick={() => setVisible(true)}      
      />
    </div> 
    ) : (
    <div style={styles.container}>
      <NotificationBanner id="basic-usage-id" onCloseButtonClick={handleClose} textMap={{ ariaLabel: 'Basic Usage Banner', closeButtonAriaLabel: 'Close' }} testId="basic-usage-test-id">
        <NotificationBannerHeader id="basic-usage-header-id">
          <h4 className="evrHeading4">Info to help with Subject/Employee</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="basic-usage-body-id">
          <p className="evrBodyText1">Indicate employee/subject with insight. Indicate how it can improve process and what will happen.</p>
        </NotificationBannerBody>
        <NotificationBannerFooter id="basic-usage-footer-id">
          <NotificationBannerFooterActions
            id="basic-usage-footer-actions-id"
            primaryAction={{
              id: 'basic-usage-footer-primary-action-id',
              label: 'Contact Admin',
              onClick: () => {},
            }}
            secondaryAction={{
              id: 'basic-usage-footer-secondary-action-id',
              label: 'See All',
              onClick: () => {}
            }}
          />
        </NotificationBannerFooter>
      </NotificationBanner>
    </div>
  )}`;

<CodeExample scope={scope} code={defaultCode} />

## Status

There are three status types for Notification Banner: `info`, `warning`, and `error`.

- `Info`: Provides informational content
- `Warning`: Signifies a warning or caution, generally used to warn users of a potential issues or alerts
- `Error`: Signifies all the errors on the page or in a view, <b>cannot be dismissed</b>

export const infoStatusCode = `() => {
  const styles = {
    container: {
      margin: '0px 20px',
      display: 'flex', 
      flexDirection: 'column',
      gap: '20px'
    }
  };
  return (
    <div style={styles.container}>
      <NotificationBanner id="info-status-id" status="info" onCloseButtonClick={() => {}} textMap={{ ariaLabel: 'Info Status Banner', closeButtonAriaLabel: 'Close' }} testId="info-status-test-id">
        <NotificationBannerHeader id="info-status-header-id">
          <h4 className="evrHeading4">Info Status</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="info-status-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
      <NotificationBanner id="warning-status-id" status="warning" onCloseButtonClick={() => {}} textMap={{ ariaLabel: 'Warning Status Banner', closeButtonAriaLabel: 'Close' }} testId="warning-status-test-id">
        <NotificationBannerHeader id="warning-status-header-id">
          <h4 className="evrHeading4">Warning Status</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="warning-status-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
      <NotificationBanner id="error-status-id" status="error" textMap={{ ariaLabel: 'Error Status Banner' }} testId="error-status-test-id">
        <NotificationBannerHeader id="error-status-header-id">
          <h4 className="evrHeading4">Error Status</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="error-status-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
    </div>
   )
}`;

<CodeExample scope={scope} code={infoStatusCode} />

## Variations

### Without Dismiss Button

The dismiss button is optional for banners of status `info` and `warning`. If the `onCloseButtonClick` or `closeButtonAriaLabel` prop is not provided, the dismiss button will not be shown. `Error` status banners are not dismissible.

export const nonDismissibleCode = `() => {
  const styles = {
    container: {
      margin: '0px 20px',
      display: 'flex', 
      flexDirection: 'column',
      gap: '20px'
    }
  };
  return (
    <div style={styles.container}>
      <NotificationBanner id="non-dismissible-info-example-id" textMap={{ ariaLabel: 'Non-dismissible Banner' }} testId="non-dismissible-info-example-test-id">
        <NotificationBannerHeader id="non-dismissible-info-example-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="non-dismissible-info-example-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
      <NotificationBanner id="non-dismissible-warning-example-id" status="warning" textMap={{ ariaLabel: 'Non-dismissible Banner' }} testId="non-dismissible-warning-example-test-id">
        <NotificationBannerHeader id="non-dismissible-warning-example-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="non-dismissible-warning-example-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
    </div>
   )
}`;

<CodeExample scope={scope} code={nonDismissibleCode} />

### With Action Buttons

Notification Banner can have up to two call-to-action <LinkTo kind="Components/Buttons/Button">buttons</LinkTo>. Check out our `<NotificationBannerFooterActions>` template.

export const actionButtonsCode = `() => {
  const styles = {
    container: {
      margin: '0px 20px',
    }
  };
  return (
    <div style={styles.container}>
      <NotificationBanner id="action-buttons-example-id" onCloseButtonClick={() => {}} textMap={{ ariaLabel: 'Action Buttons Banner', closeButtonAriaLabel: 'Close' }} testId="action-buttons-example-test-id">
        <NotificationBannerHeader id="action-buttons-example-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="action-buttons-example-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
        <NotificationBannerFooter id="action-buttons-example-footer-id">
          <NotificationBannerFooterActions
            id="action-buttons-example-footer-actions-id"
            primaryAction={{
              id: 'action-buttons-example-footer-primary-action-id',
              label: 'Label',
              onClick: () => {},
            }}
            secondaryAction={{
              id: 'action-buttons-example-footer-secondary-action-id',
              label: 'Label',
              onClick: () => {}
            }}
          />
        </NotificationBannerFooter>
      </NotificationBanner>
    </div>
   )
}`;

<CodeExample scope={scope} code={actionButtonsCode} />

### With Links

An expandable list of Links can be rendered using our `<NotificationBannerBodyLinks>` template. This template takes in an array of <LinkTo kind="Components/Link">Links</LinkTo> as children.

export const linksCode = `() => {
  const styles = {
    container: {
      margin: '0px 20px',
    }
  };
  return (
    <div style={styles.container}>
      <NotificationBanner id="links-example-id" onCloseButtonClick={() => {}} textMap={{ ariaLabel: 'Links Banner', closeButtonAriaLabel: 'Close' }} testId="links-example-test-id">
        <NotificationBannerHeader id="links-example-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="links-example-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
          <NotificationBannerBodyLinks id="links-example-body-links-id" maxCollapsedLinks={3} expandActionLabel={"Show All"} collapseActionLabel={"Collapse"}>
            <Link id="link-1" variant={'inherit'} href="https://www.google.com" target="_blank">Google</Link>
            <Link id="link-2" variant={'inherit'} href="https://www.yahoo.com" target="_blank">Yahoo</Link>
            <Link id="link-3" variant={'inherit'} href="https://www.microsoft.com" target="_blank">Microsoft</Link>
            <Link id="link-4" variant={'inherit'} href="https://www.duckduckgo.com" target="_blank">DuckDuckGo</Link>
            <Link id="link-5" variant={'inherit'} href="https://www.firefox.com" target="_blank">Firefox</Link>
            <Link id="link-6" variant={'inherit'} href="https://www.apple.com" target="_blank">Apple</Link>
          </NotificationBannerBodyLinks>
        </NotificationBannerBody>
      </NotificationBanner>
    </div>
   )
}`;

<CodeExample scope={scope} code={linksCode} />

### With Checkbox

<LinkTo kind="Components/Checkbox">Checkbox</LinkTo> can be used to manipulate Notification Banner into not being shown again
once it is dismissed with the Checkbox toggled on.

export const checkboxCode = `() => {
  const styles = {
    container: {
      margin: '0px 20px',
    }
  };
  const [checkedState, setCheckedState] = React.useState(false);
  return (
    <div style={styles.container}>
      <NotificationBanner id="checkbox-example-id" onCloseButtonClick={() => {}} textMap={{ ariaLabel: 'Checkbox Banner', closeButtonAriaLabel: 'Close' }} onCloseButtonClick={() => {}} testId="checkbox-example-test-id">
        <NotificationBannerHeader id="checkbox-example-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="checkbox-example-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
        <NotificationBannerFooter id="checkbox-example-footer-id">
          <Checkbox id="checkbox-id" label="Don't show this again" checkedState={checkedState ? 'checked' : 'unchecked'} onChange={() => setCheckedState(!checkedState)} testId="checkbox-test-id"/>
        </NotificationBannerFooter>
      </NotificationBanner>
    </div>
   )
}`;

<CodeExample scope={scope} code={checkboxCode} />

## Accessing Notification Banner using ref

Click on the Button to access Notification Banner, refer to the console for the element details.

export const refCode = `() => {
  const styles = {
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      rowGap: '10px'
    }
  }
  const bannerRef = React.useRef(null);
  return (
    <div style={styles.column}>
      <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(bannerRef.current)}}/>
      <NotificationBanner ref={bannerRef} id="ref-example-id" onCloseButtonClick={() => {}} textMap={{ ariaLabel: 'Ref Banner', closeButtonAriaLabel: 'Close' }} testId="ref-example-test-id">
        <NotificationBannerHeader id="ref-example-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="ref-example-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
    </div>
  );
}`;

<CodeExample scope={scope} code={refCode} />

## Usage Guidelines

Notification Banners should be placed at the top of the page or section content. Only one banner on a page or on an overlay is allowed.

Notification Banner can have children `NotificationBannerHeader`, `NotificationBannerBody` and `NotificationBannerFooter`. `NotificationBannerHeader` needs to be provided along with title text.

Some templates have been provided for consumers to use. These include:

- `NotificationBannerBodyLinks`: Provides anchor links. For use in `NotificationBannerBody`.
- `NotificationBannerFooterActions`: Provides call-to-action buttons (primary, secondary). For use in `NotificationBannerFooter`.

## Content Guidelines

### Title (Header)

- Required
- Clearly indicate the message to easily understand at a glance
- Title can wrap to a max of 2 lines and should not truncate

### Description (Body)

- Description can wrap to multiple lines
- Describe what was affected based on the message indicated in the title
- Indicate how to address the message
- For multiple messages within a banner:
  - Describe the number of messages
  - Include anchor links if messages are spread across different areas

## Accessibility

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.

The following values should be provided as part of the `textMap` prop:

| Label                | Description                        | <div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div> |
| -------------------- | ---------------------------------- | --------------------------------------------------------- |
| ariaLabel            | Aria label for notification banner | "Invalid dates notification"                              |
| closeButtonAriaLabel | Aria label for close button        | "Close"                                                   |

Users should have the ability to navigate to a Notification Banner using landmark regions with their screen reader. For example, for NVDA, pressing <kbd>F7</kbd> + <kbd>Insert</kbd> opens the elements list, allowing the user to select the landmark region to jump into the banner. In Voiceover, pressing <kbd>VO</kbd> + <kbd>U</kbd> will do the same.

export const a11yInfoCode = `() => {
  const styles = {
    container: {
      margin: '0px 20px',
      display: 'flex',
      flexDirection: 'column', 
      alignItems: 'center',
    },
    button: {
       margin: '10px'
    }
  };
  const [infoVisible, setInfoVisible] = React.useState(false);
  const [warningVisible, setWarningVisible] = React.useState(false);
  const [errorVisible, setErrorVisible] = React.useState(false);
  return (
    <div style={styles.container}>
      <div style={styles.button}>
        <Button
          id="a11y-info-trigger-button-id"
          label={infoVisible ? 'HIDE info banner' : 'SHOW info banner'}
          onClick={() => setInfoVisible(!infoVisible)}
        />
      </div>
      {infoVisible && (
        <NotificationBanner id="a11y-info-id" onCloseButtonClick={() => setInfoVisible(false)} textMap={{ ariaLabel: 'A11y Info Banner', closeButtonAriaLabel: 'Close' }} testId="a11y-info-test-id">
          <NotificationBannerHeader id="a11y-info-header-id"><h4 className="evrHeading4">Info Heading</h4></NotificationBannerHeader>
          <NotificationBannerBody id="a11y-info-body-id"> 
            <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
          </NotificationBannerBody>
        </NotificationBanner>
      )}
      <div style={styles.button}>
        <Button
          id="a11y-warning-trigger-button-id"
          label={warningVisible ? 'HIDE warning banner' : 'SHOW warning banner'}
          onClick={() => setWarningVisible(!warningVisible)}
        />
      </div>
      {warningVisible && (
        <NotificationBanner id="a11y-warning-id" status="warning" onCloseButtonClick={() => setWarningVisible(false)} textMap={{ ariaLabel: 'A11y Warning Banner', closeButtonAriaLabel: 'Close' }} testId="a11y-warning-test-id">
          <NotificationBannerHeader id="a11y-warning-header-id"><h4 className="evrHeading4">Warning Heading</h4></NotificationBannerHeader>
          <NotificationBannerBody id="a11y-warning-body-id"> 
            <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
          </NotificationBannerBody>
        </NotificationBanner>
      )}
      <div style={styles.button}>
        <Button
          id="a11y-error-trigger-button-id"
          label={errorVisible ? 'HIDE error banner' : 'SHOW error banner'}
          onClick={() => setErrorVisible(!errorVisible)}
        />
      </div>
      {errorVisible && (
        <NotificationBanner id="a11y-error-id" status="error" textMap={{ ariaLabel: 'A11y Error Banner' }} testId="a11y-error-test-id">
          <NotificationBannerHeader id="a11y-error-header-id"><h4 className="evrHeading4">Error Heading</h4></NotificationBannerHeader>
          <NotificationBannerBody id="a11y-error-body-id">
            <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
          </NotificationBannerBody>
        </NotificationBanner>
      )}
    </div>
  )}`;

<CodeExample scope={scope} code={a11yInfoCode} />
