# Notification Banner

## Summary

- Start Date: 2024-02-20
- Figma link: https://www.figma.com/file/va9QuZPmGm49yBfSlqzbeW/%F0%9F%A7%AA-Banner-%2F-Problems-Pane?type=design&node-id=3361-2837&mode=design&t=WPr3n0WwsYWCf5kE-0

## Detailed Design

The banner component is used to alert or communicate a message to the user. Banners are located at the top of the page or section content. There are 3 status states: `info`, `warning`, and `error`. Banner will use templates similar to what Modal uses to group its content. However, all 3 parts will not be required:

- BannerHeader: Includes icon, title, and close button
- BannerBody: Includes children passed in
- BannerFooter: Includes children passed in
- BannerFooterActions: Includes call-to-action buttons (primary, secondary)

As per design requirements, banners can be stacked with a limit of up to 3; each banner status can be displayed once. This guideline should be followed and taken care of on the consumer side by feature teams. Therefore, all error alerts should be grouped into the error status banner, warnings should be grouped into the warning banner, etc. Unlike Toast `Provider` and `Context` are not needed since Banner will be shown within page content and not in a `Portal` above page content.

Removal of banner on close is easy enough and can be accomplished simply by removing the element from the DOM. The component will trigger `onCloseButtonClick` when the close button is clicked.

## Other Components

Banner will have a similar API to Toast and Modal. It will also re-use some parts of the `everest-community` Banner.

## Requirements

- [] Has minWidth of 320px and maxWidth of 1280px
- [] Dismisses when the close button is clicked
- [] Will not dismiss automatically
- [] Will be contextually placed at the top of each section and scrolls with page content [GUIDANCE for feature teams]
- [] Can be stacked up to a limit of 3 (one banner of each status) [GUIDANCE for feature teams]

## API

1.  **id**: `string`
    Sets the id.
2.  **testId**: `string`
    Optional. Sets **data-test-id** attribute.
3.  **status**: `info | warning | error`
    Optional. Sets status of banner. Default is `info`.

### Header

1.  **icon**: `TIconNames`
    Optional. Sets icon in banner header.
2.  **title**: `string`
    Sets the heading title.
3.  **closeButtonAriaLabel**: `string`
    Optional. Sets the aria label on close button.
4.  **onCloseButtonClick**: `() => void`
    Optional. Sets a callback function that is executed when banner is dismissed using the close button.

### Body

1.  **children**: `React.ReactNode`
    Optional. Sets content of body section.

### Footer

```typescript
export interface IBannerAction {
  id: string;
  testId?: string;
  label: string;
  ariaLabel?: string;
  onClick?: (e: React.MouseEvent) => void;
}
```

1.  **action**: `IBannerAction`
    Optional. Sets action on the primary action button.
2.  **secondaryAction**: `IBannerAction`
    Optional. Sets lesser prominent action on the secondary action button.

## Usage

`banner-header.tsx`:

```typescript
export const BannerHeader = () => {
  const bannerStatusStyles: TStatusStyles = {
    icon: 'information',
    iconFill: '--evr-content-status-informative-lowemp',
  };
  if (status === 'warning') {
    bannerStatusStyles.icon = 'alert';
    bannerStatusStyles.iconFill = '--evr-content-status-warning-lowemp'
  } else if (status === 'error') {
    bannerStatusStyles.icon = 'information';
    bannerStatusStyles.iconFill = '--evr-content-status-error-lowemp';
  };
  const handleClose = () => {
      onCloseButtonClick?.();
  };
  return (
    <div id={id} data-testId={testId} className={styles.evrBannerHeader}>
      {!!icon && (
        <Icon name={bannerStatusStyles.icon as TBannerIcon} fill={bannerStatusStyles.iconFill as TBannerIconFill} />
      )}
      {!!title && <h1<{title}</h1>}
      {!!closeButtonAriaLabel && (
        <IconButton
            id={`${id}-close-button`}
            iconName="x"
            onClick={handleClose}
            ariaLabel={closeButtonAriaLabel}
            testId={testId ? `${testId}-close-button` : undefined}
        />
      )}
    </div>
  )
};
```

`banner-body.tsx`:

```typescript
export const BannerBody = () => {
  return (
    <div id={id} data-testId={testId} className={styles.evrBannerBody}>
      {children}
    </div>
  );
};
```

`banner-footer.tsx`:

```typescript
export const BannerFooter = () => {
  return (
    <div id={id} data-testId={testId} className={styles.evrBannerFooter}>
      {children}
    </div>
  );
};
```

`banner-footer-actions.tsx`:

```typescript
export const BannerFooterActions = () => {
  const renderPrimaryActionButton = () => {
    if (action) {
      const { id: actionId, testId, label, ariaLabel, onClick } = action;
      return <Button id={actionId} testId={testId} onClick={onClick} label={label} ariaLabel={ariaLabel} />;
    }
  };
  const renderSecondaryActionButton = () => {
    if (secondaryAction) {
      const { id: actionId, testId, label, ariaLabel, onClick } = secondaryAction;
      return <Button id={actionId} testId={testId} onClick={onClick} label={label} ariaLabel={ariaLabel} />;
    }
  };
  return (
    <div id={id} data-testId={testId} className={styles.evrBannerFooterActions}>
      {renderPrimaryActionButton()}
      {renderSecondaryActionButton()}
    </div>
  );
};
```

`banner.tsx`:

```typescript
type TBannerIcon = 'information' | 'alert';
type TBannerIconFill =
  | '--evr-content-status-informative-lowemp'
  | '--evr-content-status-warning-lowemp'
  | '--evr-content-status-error-lowemp';
export type TBannerStatus = 'info' | 'warning' | 'error';
type TStatusStyles = {
  iconFill: TBannerIconFill;
  icon?: TBannerIcon;
};
export interface IBannerAction {
  id: string;
  testId?: string;
  label: string;
  ariaLabel?: string;
  onClick?: (e: React.MouseEvent) => void;
}
export interface IBanner {
  id: string;
  testId?: string;
  status?: TBannerStatus;
  icon?: TIconNames;
  title?: string;
  closeButtonAriaLabel?: string;
  onCloseButtonClick?: () => void;
  action?: IBannerAction;
  secondaryAction?: IBannerAction;
}
export const Banner = () => {
  let headerChild: ReactNode;
  let bodyChild: ReactNode;
  let footerChild: ReactNode;
  React.Children.forEach(children, (child: any) => {
    switch (child.type) {
      case BannerHeader:
        headerChild = child;
        break;
      case BannerBody:
        bodyChild = child;
        break;
      case BannerFooter:
        footerChild = child;
        break;
    }
  });
  return (
    <div id={id} data-testId={testId}>
      <div>{headerChild}</div>
      <div>{bodyChild}</div>
      <div>{footerChild}</div>
    </div>
  );
};
```

Basic usage of `Banner`:

```typescript
<Banner id="banner-example" status="error">
  <BannerHeader
    id="banner-header-id"
    title="Basic Example"
    onCloseButtonClick={() => {}}
    closeButtonAriaLabel="Close banner"
  />
  <BannerBody id="banner-body-id">
    <p>
      Indicate employee with an inaccuracy issue. Indicate how it affects the employee’s pay. Please update the
      employee’s earnings to ensure it is accurate.
    </p>
  </BannerBody>
  <BannerFooter>
    <BannerFooterActions
      id="banner-footer-id"
      action={{
        id: 'primary-action-id',
        testId: 'primary-action-test-id',
        label: 'Filter results',
        ariaLabel: 'filter-results-button',
        onClick: () => {},
      }}
      secondaryAction={{
        id: 'secondary-action-id',
        testId: 'secondary-action-test-id',
        label: 'Delete entry',
        ariaLabel: 'delete-entry-button',
        onClick: () => {},
      }}
    />
  </BannerFooter>
</Banner>
```

Usage with content other than text (links):

```typescript
  const links={[
    {
      text: 'Indicate employee with an inaccuracy issue',
      url: 'https://www.google.com',
    },
    {
      text: 'Microsoft Bing',
      url: 'https://www.bing.com',
    },
  ]};
  <Banner id="banner-links-example" status="error">
    <BannerHeader
      id="banner-header-id"
      title="Example with Links"
      onCloseButtonClick={() => {}}
      closeButtonAriaLabel="Close banner"
    />
    <BannerBody id="banner-body-id">
      <p>Errors were found on this page:</p>
      {links?.map((x) => {
        return (
          <Link href={x.url} target="_blank">{x.text}</Link>
        );
      })}
    </BannerBody>
    <BannerFooter>
      <BannerFooterActions
        id="banner-footer-id"
        action={{
          id: 'primary-action-id',
          testId: 'primary-action-test-id',
          label: 'Filter results',
          ariaLabel: 'filter-results-button',
          onClick: () => {}
        }}
        secondaryAction={{
          id: 'secondary-action-id',
          testId: 'secondary-action-test-id',
          label: 'Delete entry',
          ariaLabel: 'delete-entry-button',
          onClick: () => {}
        }}
      />
    </BannerFooter>
  </Banner>
```

Usage with no content:

```typescript
<Banner id="banner-example" status="error">
  <BannerHeader
    id="banner-header-id"
    title="Example with no content or footer"
    onCloseButtonClick={() => {}}
    closeButtonAriaLabel="Close banner"
  />
</Banner>
```

## Accessibility

https://www.w3.org/WAI/ARIA/apg/patterns/alert/examples/alert/

- At the time of this arch doc creation, Design is still working on adding Banner accessibility.

- Will use WAI-ARIA `role="alert"` and `aria-live="assertive"` because banner contains important information for the user. This info should be announced first thing when the page loads.

## Q&A

**Is banner always dismissible?**
No, banner is not always dismissible.

**Are banners stackable?**
Yes, they are stackable with a max limit of 3. All errors will be grouped into the error banner, warnings into the warning banner, etc.

**What is the focus order in Banner?**
Focus should jump from body links then action buttons then to the close button. It should not jump to the close button first. From the a11y team:

"Banner/problam section content flows with the rest of the page, there's no focus trap. Intent is to get the user to the issue immediately so 'close' as the last focus order would be beneficial. Modal and SidePanel it is like a container so the shell gets the focus, then navigate in order inside the area, and focus traps inside. I believe the intention is similar to this problem section where we want the user to take action and resolve immediately so the order goes to the action button then the close/dismiss (if that shows up)."

## Future Considerations

- Inline banner
- Banner that spans across whole page at top
- Adding toast action for undo

## Other Design Systems

[Ontario](https://designsystem.ontario.ca/components/detail/page-alerts.html#info)

- Called page alert
- Content length should be a max of 280 characters
- No action buttons but there are call-to-action links

[Spectrum](https://spectrum.adobe.com/page/alert-banner/)

- 2 types of alert: alert banner, inline alert
- Alert banner: Has text (not title or content), action button, is dismissible, used for prompt system alerts
- Inline alert: Not always used at top of content, not dismissible, used for form validation

[Material UI](https://v5-0-6.mui.com/components/alert/)

- Called alert
- Handles onClose animation using [react-transition-group](https://github.com/reactjs/react-transition-group) internally
- `action` is a node

[Ant Design](https://ant.design/components/alert)

- Demo: https://codesandbox.io/p/sandbox/custom-action-antd-5-14-1-pr83pd?file=%2Fdemo.tsx%3A38%2C8
- Called alert
- Handles onClose transitions using `CSSMotion from 'rc-motion'`
- `action` is a node

[Polaris](https://polaris.shopify.com/components/feedback-indicators/banner?example=banner-with-footer-call-to-action)

- Called banner
- Can be used inside card or modal
- Good API
- `action` & `secondaryAction` buttons are prop objects with label, url and target props

[Carbon](https://carbondesignsystem.com/components/notification/usage)

- Called notification
- Has actionable (with action button) & inline styles
- Notification has props to be provided to action button: `actionButtonLabel` & `onActionButtonClick`

## Required PBIs

1. [Create Banner component](https://dayforce.atlassian.net/browse/EDS-4057)
1. [Update Link color variants](https://dayforce.atlassian.net/browse/EDS-4107)
1. [Storybook Documentation](https://dayforce.atlassian.net/browse/EDS-4059)
1. [A11y](https://dayforce.atlassian.net/browse/EDS-4058)
1. [Tests](https://dayforce.atlassian.net/browse/EDS-4060)

   - Axe-core
   - Manual (screen reader)
   - Visual
   - Unit
   - Playwright

1. [Add Banner to Ref App](https://dayforce.atlassian.net/browse/EDS-4061)
1. [Push to production](https://dayforce.atlassian.net/browse/EDS-4062)

# Changelog

5/1/24 - [EDS-4301: Rename component to Notification Banner](https://ceridian.atlassian.net/browse/EDS-4301)
5/13/24 - [EDS-4353: Make error banners non-dismissible](https://ceridian.atlassian.net/browse/EDS-4353)
