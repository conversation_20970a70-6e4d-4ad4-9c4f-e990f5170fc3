@use '@ceridianhcm/theme/dist/scss/' as typography;
@use '../../variables.scss';

.evrNotificationBanner {
  min-width: variables.$notificationBannerMinWidth;
  flex-direction: row;
  justify-content: space-between;

  padding-inline: var(--evr-spacing-sm);
  padding-block: var(--evr-spacing-2xs);

  border-radius: var(--evr-radius-sm);
  outline: none; // override default border from ContainerBase

  // status styling
  &.info,
  &.info :is(.evrNotificationBannerHeader, .evrNotificationBannerBody) > * {
    color: var(--evr-content-status-informative-lowemp);
    background: var(--evr-surfaces-status-informative-lowemp);
  }
  &.warning,
  &.warning :is(.evrNotificationBannerHeader, .evrNotificationBannerBody) > * {
    color: var(--evr-content-status-warning-lowemp);
    background: var(--evr-surfaces-status-warning-lowemp);
  }
  &.error,
  &.error :is(.evrNotificationBannerHeader, .evrNotificationBannerBody) > * {
    color: var(--evr-content-status-error-lowemp);
    background: var(--evr-surfaces-status-error-lowemp);
  }

  .icon {
    margin-block-start: var(--evr-spacing-2xs);
    margin-inline-end: var(--evr-spacing-2xs);
  }
  .closeButton {
    margin-block-start: var(--evr-spacing-3xs);
    margin-inline-start: var(--evr-spacing-2xs);
  }
  .childrenContainer {
    width: 100%;
    margin-block: var(--evr-spacing-2xs);
  }

  .evrNotificationBannerHeader {
    + .evrNotificationBannerBody {
      margin-block-start: var(--evr-spacing-3xs);
    }
  }

  .evrNotificationBannerBody {
    .linkList {
      padding: 0;
      list-style-type: none;
      margin-block: var(--evr-spacing-2xs);
    }
    .linkListButton {
      margin-block-start: var(--evr-spacing-sm);
      margin-block-end: var(--evr-spacing-2xs);
    }
  }

  .evrNotificationBannerFooter {
    margin-block-start: var(--evr-spacing-sm);
  }

  .evrNotificationBannerFooterActions {
    display: flex;
    gap: var(--evr-spacing-2xs);
  }

  @media (max-width: variables.$notificationBannerFooterActionsBreakpoint) {
    .evrNotificationBannerFooterActions {
      flex-direction: column;
    }
  }
}
