import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';
import {
  NotificationBanner,
  NotificationBannerBody,
  NotificationBannerFooter,
  NotificationBannerFooterActions,
  NotificationBannerHeader,
  NotificationBannerBodyLinks,
} from '.';
import { Link } from '../link';

<Meta
  title="Testing/Automation Test Cases/NotificationBanner"
  component={NotificationBanner}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: {
        breakpointXs: { disable: true }, // disable default to use smaller size
        mobileMinimum: defaultModes['mobileMinimum'],
      },
    },
  }}
  args={{
    id: 'notification-banner-id',
    testId: 'notification-banner-test-id',
    textMap: {
      closeButtonAriaLabel: 'Close',
    },
    onCloseButtonClick: () => undefined,
  }}
/>

# NotificationBanner

## Live Demo

<Canvas>
  <Story name="Info">
    {({ ...args }) => (
      <NotificationBanner {...args}>
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Info with Action Buttons">
    {({ ...args }) => (
      <NotificationBanner {...args}>
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
        <NotificationBannerFooter id="notification-banner-footer-id">
          <NotificationBannerFooterActions
            id="notification-banner-footer-actions-id"
            primaryAction={{
              id: 'notification-banner-footer-primary-action-id',
              label: 'Label',
              onClick: () => {},
            }}
            secondaryAction={{
              id: 'notification-banner-footer-secondary-action-id',
              label: 'Label',
              onClick: () => {},
            }}
          />
        </NotificationBannerFooter>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Info with Links">
    {({ ...args }) => (
      <NotificationBanner {...args}>
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
          <NotificationBannerBodyLinks
            id="notification-banner-body-links-id"
            maxCollapsedLinks={3}
            expandActionLabel={'Show All'}
            collapseActionLabel={'Collapse'}
          >
            <Link id="link-1" variant={'inherit'} href="https://www.google.com" target="_blank">
              Google
            </Link>
            <Link id="link-2" variant={'inherit'} href="https://www.yahoo.com" target="_blank">
              Yahoo
            </Link>
            <Link id="link-3" variant={'inherit'} href="https://www.microsoft.com" target="_blank">
              Microsoft
            </Link>
            <Link id="link-4" variant={'inherit'} href="https://www.duckduckgo.com" target="_blank">
              DuckDuckGo
            </Link>
            <Link id="link-5" variant={'inherit'} href="https://www.firefox.com" target="_blank">
              Firefox
            </Link>
            <Link id="link-6" variant={'inherit'} href="https://www.apple.com" target="_blank">
              Apple
            </Link>
          </NotificationBannerBodyLinks>
        </NotificationBannerBody>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Warning">
    {({ ...args }) => (
      <NotificationBanner {...args} status="warning">
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Warning with Action Buttons">
    {({ ...args }) => (
      <NotificationBanner {...args} status="warning">
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
        <NotificationBannerFooter id="notification-banner-footer-id">
          <NotificationBannerFooterActions
            id="notification-banner-footer-actions-id"
            primaryAction={{
              id: 'notification-banner-footer-primary-action-id',
              label: 'Label',
              onClick: () => {},
            }}
            secondaryAction={{
              id: 'notification-banner-footer-secondary-action-id',
              label: 'Label',
              onClick: () => {},
            }}
          />
        </NotificationBannerFooter>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Warning with Links">
    {({ ...args }) => (
      <NotificationBanner {...args} status="warning">
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
          <NotificationBannerBodyLinks
            id="notification-banner-body-links-id"
            maxCollapsedLinks={3}
            expandActionLabel={'Show All'}
            collapseActionLabel={'Collapse'}
          >
            <Link id="link-1" variant={'inherit'} href="https://www.google.com" target="_blank">
              Google
            </Link>
            <Link id="link-2" variant={'inherit'} href="https://www.yahoo.com" target="_blank">
              Yahoo
            </Link>
            <Link id="link-3" variant={'inherit'} href="https://www.microsoft.com" target="_blank">
              Microsoft
            </Link>
            <Link id="link-4" variant={'inherit'} href="https://www.duckduckgo.com" target="_blank">
              DuckDuckGo
            </Link>
            <Link id="link-5" variant={'inherit'} href="https://www.firefox.com" target="_blank">
              Firefox
            </Link>
            <Link id="link-6" variant={'inherit'} href="https://www.apple.com" target="_blank">
              Apple
            </Link>
          </NotificationBannerBodyLinks>
        </NotificationBannerBody>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error">
    {({ ...args }) => (
      <NotificationBanner {...args} status="error">
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error with Action Buttons">
    {({ ...args }) => (
      <NotificationBanner {...args} status="error">
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
        </NotificationBannerBody>
        <NotificationBannerFooter id="notification-banner-footer-id">
          <NotificationBannerFooterActions
            id="notification-banner-footer-actions-id"
            primaryAction={{
              id: 'notification-banner-footer-primary-action-id',
              label: 'Label',
              onClick: () => {},
            }}
            secondaryAction={{
              id: 'notification-banner-footer-secondary-action-id',
              label: 'Label',
              onClick: () => {},
            }}
          />
        </NotificationBannerFooter>
      </NotificationBanner>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error with Links">
    {({ ...args }) => (
      <NotificationBanner {...args} status="error">
        <NotificationBannerHeader id="notification-banner-header-id">
          <h4 className="evrHeading4">Heading</h4>
        </NotificationBannerHeader>
        <NotificationBannerBody id="notification-banner-body-id">
          <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
          <NotificationBannerBodyLinks
            id="notification-banner-body-links-id"
            maxCollapsedLinks={3}
            expandActionLabel={'Show All'}
            collapseActionLabel={'Collapse'}
          >
            <Link id="link-1" variant={'inherit'} href="https://www.google.com" target="_blank">
              Google
            </Link>
            <Link id="link-2" variant={'inherit'} href="https://www.yahoo.com" target="_blank">
              Yahoo
            </Link>
            <Link id="link-3" variant={'inherit'} href="https://www.microsoft.com" target="_blank">
              Microsoft
            </Link>
            <Link id="link-4" variant={'inherit'} href="https://www.duckduckgo.com" target="_blank">
              DuckDuckGo
            </Link>
            <Link id="link-5" variant={'inherit'} href="https://www.firefox.com" target="_blank">
              Firefox
            </Link>
            <Link id="link-6" variant={'inherit'} href="https://www.apple.com" target="_blank">
              Apple
            </Link>
          </NotificationBannerBodyLinks>
        </NotificationBannerBody>
      </NotificationBanner>
    )}
  </Story>
</Canvas>
