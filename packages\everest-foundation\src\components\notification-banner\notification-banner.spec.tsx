import React, { useState } from 'react';
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import {
  NotificationBanner,
  NotificationBannerBody,
  NotificationBannerFooter,
  NotificationBannerFooterActions,
  NotificationBannerHeader,
} from '.';

const id = 'notification-banner-id';
const actionBtnId = 'notification-banner-footer-primary-action-id';
const closeBtnId = 'notification-banner-id-close-button';
const testId = 'notification-banner-test-id';
const ariaLabel = 'Example Notification Banner';
const closeButtonAriaLabel = 'Close';
const actionButtonLabel = 'Action Label';
const onCloseButtonClick = jest.fn();
const onActionButtonClick = jest.fn();
const defaultMockProps = {
  id,
  testId,
  textMap: { ariaLabel, closeButtonAriaLabel },
  onCloseButtonClick,
};

const getNotificationBanner = () => screen.getByTestId(testId);
const getActionButton = () => screen.getByTestId(`${testId}-primary-action`);
const getCloseButton = () => screen.getByTestId(`${testId}-close-button`);

const BannerTestComponent = (args?: any | undefined[]) => {
  const [visible, setVisible] = useState(true);
  return !visible ? (
    <></>
  ) : (
    <NotificationBanner
      {...args}
      {...defaultMockProps}
      onCloseButtonClick={() => {
        onCloseButtonClick();
        setVisible(false);
      }}
    >
      <NotificationBannerHeader id="notification-banner-header-id">
        <h4 className="evrHeading4">Heading</h4>
      </NotificationBannerHeader>
      <NotificationBannerBody id="notification-banner-body-id">
        <p className="evrBodyText1">Brief information describing the purpose of this banner</p>
      </NotificationBannerBody>
      <NotificationBannerFooter id="notification-banner-footer-id" testId={testId}>
        <NotificationBannerFooterActions
          id="notification-banner-footer-actions-id"
          primaryAction={{
            id: actionBtnId,
            testId: testId,
            label: actionButtonLabel,
            onClick: onActionButtonClick,
          }}
        />
      </NotificationBannerFooter>
    </NotificationBanner>
  );
};

const renderTestComponent = (props?: any) =>
  act(() => {
    render(<BannerTestComponent {...props} />);
  });

describe('[Notification Banner]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('focuses on action button first then close button', async () => {
    renderTestComponent();
    await userEvent.tab();
    expect(document.activeElement?.getAttribute('id')).toBe(`${actionBtnId}`);
    await userEvent.tab();
    expect(document.activeElement?.getAttribute('id')).toBe(`${closeBtnId}`);
  });

  it('allows focusing on the close button and contain aria-label', () => {
    render(<NotificationBanner {...defaultMockProps} />);
    const closeButton = screen.getByRole('button', { name: 'Close' });
    expect(closeButton).toBeInTheDocument();
  });

  it('should have custom aria-label when provided', () => {
    render(<NotificationBanner {...defaultMockProps} />);
    expect(getNotificationBanner()).toHaveAttribute('aria-label', ariaLabel);
  });

  describe('onActionButtonClick', () => {
    it('triggers onActionButtonClick when action button is mouse clicked', async () => {
      renderTestComponent();
      await userEvent.click(getActionButton());
      expect(onActionButtonClick).toHaveBeenCalledTimes(1);
    });

    it('triggers onActionButtonClick when enter key is pressed', async () => {
      renderTestComponent();
      await userEvent.tab();
      await userEvent.keyboard('[Enter]');
      expect(onActionButtonClick).toHaveBeenCalledTimes(1);
    });

    it('triggers onActionButtonClick when space key is pressed', async () => {
      renderTestComponent();
      await userEvent.tab();
      await userEvent.keyboard('[Space]');
      expect(onActionButtonClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('onCloseButtonClick', () => {
    it('triggers onCloseButtonClick and removes itself when close button is mouse clicked', async () => {
      renderTestComponent();
      await userEvent.click(getCloseButton());
      expect(onCloseButtonClick).toHaveBeenCalledTimes(1);
      expect(document.getElementById(id)).not.toBeInTheDocument();
    });

    it('triggers onCloseButtonClick and removes itself when enter key is pressed', async () => {
      renderTestComponent();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('[Enter]');
      expect(onCloseButtonClick).toHaveBeenCalledTimes(1);
      expect(document.getElementById(id)).not.toBeInTheDocument();
    });

    it('triggers onCloseButtonClick and removes itself when space key is pressed', async () => {
      renderTestComponent();
      await userEvent.tab();
      await userEvent.tab();
      await userEvent.keyboard('[Space]');
      expect(onCloseButtonClick).toHaveBeenCalledTimes(1);
      expect(document.getElementById(id)).not.toBeInTheDocument();
    });
  });
});
