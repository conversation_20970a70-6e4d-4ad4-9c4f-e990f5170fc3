import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import {
  NotificationBanner,
  NotificationBannerBody,
  NotificationBannerFooter,
  NotificationBannerFooterActions,
  NotificationBannerHeader,
} from '.';
import Examples from './notification-banner.examples.mdx';
import { Button } from '../button';
import { action } from '@storybook/addon-actions';

<Meta
  title="Components/NotificationBanner"
  component={NotificationBanner}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/va9QuZPmGm49yBfSlqzbeW/%F0%9F%A7%AA-Notification-Banner?type=design&node-id=3361-2837&mode=design&t=IgRc7Xsxph62wR0J-0',
    },
  }}
  argTypes={{
    onCloseButtonClick: {
      control: 'select',
      options: ['Defined', 'Undefined'],
      mapping: {
        Defined: action('onCloseButtonClick'),
        Undefined: undefined,
      },
    },
  }}
  args={{
    id: 'notification-banner-id',
    testId: 'notification-banner-test-id',
    status: 'info',
    onCloseButtonClick: 'Defined',
    textMap: {
      ariaLabel: 'Invalid Business Number Banner',
      closeButtonAriaLabel: 'Close',
    },
  }}
/>

# NotificationBanner

<Examples />

## Live Demo

<Canvas>
  <Story name="NotificationBanner">
    {(args) => {
      const [visible, setVisible] = React.useState(true);
      return !visible ? (
        <Button id="trigger-button-id" label={'Show banner'} onClick={() => setVisible(true)} />
      ) : (
        <NotificationBanner
          {...args}
          onCloseButtonClick={
            args.onCloseButtonClick
              ? (e) => {
                  setVisible(false);
                  args.onCloseButtonClick(e);
                }
              : undefined
          }
        >
          <NotificationBannerHeader id="notification-banner-header-id">
            <h4 className="evrHeading4">Invalid business number</h4>
          </NotificationBannerHeader>
          <NotificationBannerBody id="notification-banner-body-id">
            <p className="evrBodyText1">
              Please contact your admin to update the business number to ensure employees are paid.
            </p>
          </NotificationBannerBody>
          <NotificationBannerFooter id="notification-banner-footer-id">
            <NotificationBannerFooterActions
              id="notification-banner-footer-actions-id"
              primaryAction={{
                id: 'notification-banner-footer-primary-action-id',
                label: 'Contact Admin',
                onClick: () => {},
              }}
              secondaryAction={{
                id: 'notification-banner-footer-secondary-action-id',
                label: 'Cancel',
                onClick: () => {},
              }}
            />
          </NotificationBannerFooter>
        </NotificationBanner>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="NotificationBanner" />
