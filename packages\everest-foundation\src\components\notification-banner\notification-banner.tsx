import React, { forwardRef, PropsWith<PERSON>hildren, useMemo } from 'react';
import classnames from 'classnames';

import { Icon, TIconName } from '../..';
import { useFirstRender } from '../../utils/use-first-render';
import { ButtonBase } from '../button-base';
import { ContainerBase, IContainerBaseProps } from '../container-base/container-base';

import styles from './notification-banner.module.scss';

type TNotificationBannerIcon = 'information' | 'alert' | 'error' | TIconName;
type TNotificationBannerIconFill =
  | '--evr-content-status-informative-lowemp'
  | '--evr-content-status-warning-lowemp'
  | '--evr-content-status-error-lowemp';
type TNotificationBannerStatusStyles = {
  icon?: TNotificationBannerIcon;
  iconFill: TNotificationBannerIconFill;
};

export type TNotificationBannerStatus = 'info' | 'warning' | 'error';
export interface INotificationBannerTextMap {
  ariaLabel?: string;
  closeButtonAriaLabel?: string;
}
export interface INotificationBannerProps extends Omit<IContainerBaseProps, 'ariaLabel' | 'className' | 'roleType'> {
  /**
   * Sets status of notification banner.
   * @default info
   */
  status?: TNotificationBannerStatus;
  /** Sets aria-labels for notification banner. */
  textMap?: INotificationBannerTextMap;
  /** Callback that runs when close button is clicked. */
  onCloseButtonClick?: (e: React.MouseEvent<HTMLElement>) => void;
}

export const NotificationBanner = forwardRef<HTMLDivElement, PropsWithChildren<INotificationBannerProps>>(
  (props, ref) => {
    const { id, testId, status = 'info', textMap, onCloseButtonClick, children } = props;

    const bannerStatusStyles: TNotificationBannerStatusStyles = useMemo(() => {
      switch (status) {
        case 'warning':
          return {
            icon: 'alert',
            iconFill: '--evr-content-status-warning-lowemp',
          };
        case 'error':
          return {
            icon: 'error',
            iconFill: '--evr-content-status-error-lowemp',
          };
        default:
          return {
            icon: 'information',
            iconFill: '--evr-content-status-informative-lowemp',
          };
      }
    }, [status]);

    const renderStatusIcon = () => {
      return (
        <div className={styles.icon}>
          <Icon name={bannerStatusStyles.icon as TNotificationBannerIcon} fill={bannerStatusStyles.iconFill} />
        </div>
      );
    };

    const renderCloseButton = () => {
      if (textMap?.closeButtonAriaLabel && onCloseButtonClick && status !== 'error') {
        return (
          <div className={styles.closeButton}>
            <ButtonBase
              id={`${id}-close-button`}
              variant="tertiaryNeutral"
              radius={'--evr-radius-circle'}
              uniformSize
              onClick={onCloseButtonClick}
              ariaLabel={textMap?.closeButtonAriaLabel}
              testId={testId ? `${testId}-close-button` : undefined}
            >
              <Icon size="md" name={'x'} fill={bannerStatusStyles.iconFill} />
            </ButtonBase>
          </div>
        );
      }
    };

    // Workaround to consumers having to wrap banner in a div for status types info & warning on their end in order for SR to read content (ex. <div role="status">)
    const firstRender = useFirstRender();

    return (
      <div role={status !== 'error' ? 'status' : undefined}>
        {!firstRender && (
          <ContainerBase
            id={id}
            ref={ref}
            testId={testId}
            className={classnames('evrBodyText1', styles.evrNotificationBanner, styles[status])}
            ariaLabel={textMap?.ariaLabel}
            roleType="region"
          >
            {renderStatusIcon()}
            <div
              className={styles.childrenContainer}
              role={status === 'error' ? 'alert' : undefined} // Firefox doesnt support role='alert', please refer to https://ceridian.atlassian.net/browse/EDS-3662 for more details
              aria-describedby={`${id}-container-content`} // Makes sure SR announces banner content when status is info or warning (aria polite)
            >
              <div id={`${id}-container-content`}>{children}</div>
            </div>
            {renderCloseButton()}
          </ContainerBase>
        )}
      </div>
    );
  }
);

NotificationBanner.displayName = 'NotificationBanner';
