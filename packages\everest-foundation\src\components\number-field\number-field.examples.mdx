import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { NumberField } from './number-field';
import { Button } from '../button';
import { Warning } from '../../../.storybook/docs/shared/status-banner.tsx';

export const scope = { NumberField, Button };

<Warning>
  Use the <LinkTo dayforceStoryPath="docs/components-text-fields-numberfield--docs">globalized NumberField</LinkTo> from
  `@ceridianhcm/everest-dayforce`. NumberField from `@ceridianhcm/components` should not be used.
</Warning>

NumberField is built on top of <LinkTo kind="Components/Text Fields/Text Field">TextField</LinkTo>. Its main purpose is to serve as a base component for our globalized version found under `everest-dayforce`. The dayforce version is wrapped in a `GlobalizationProvider` and uses globalization methods to format input onFocus, onChange, and onBlur.

NumberField is designed to only accept numbers and certain characters, such as the negative sign, thousands separator, and decimal separator.

As the current NumberField doesn’t support onChange, `experimentalFormatOnType` needs to be set to `false` by default.

## Variations

### Default NumberField

In terms of design, NumberField shares the exact same variations, therefore those variations will not be documented here.

By default, NumberField has the `type` property set to **text** and it cannot be changed.

export const defaultNumberFieldCode = `() => {
  const [value, setValue] = React.useState(0);
  const handleChange = (val) => setValue(val);
  const numberWithCommas = (num) => {
    const formatter = new Intl.NumberFormat('en-CA');
    return formatter.format(num);
  };
  return (
    <NumberField
      label="This is a label"
      id="defaultNumberFieldCode"
      onChange={handleChange}
      onConvertToNumber={(val) => Number(val.replaceAll(',', ''))}
      onConvertToFormatString={(num) => numberWithCommas(num)}
      value={value}
    />
  );
}`;

<CodeExample scope={scope} code={defaultNumberFieldCode} />

### ReadOnly NumberField

export const readOnlyNumberFieldCode = `() => {
  const [value, setValue] = React.useState(0);
  const handleChange = (val) => setValue(val);
  const numberWithCommas = (num) => {
    const formatter = new Intl.NumberFormat('en-CA');
    return formatter.format(num);
  };
  return (
    <NumberField
      label="This is a label"
      id="readOnlyNumberFieldCode"
      onChange={handleChange}
      onConvertToNumber={(val) => Number(val.replaceAll(',', ''))}
      onConvertToFormatString={(num) => numberWithCommas(num)}
      value={value}
      readOnly
    />
  );
}`;

<CodeExample scope={scope} code={readOnlyNumberFieldCode} />

### NumberField With Max/Min Value

`maxValue` and `minValue` provide range of numbers that user can input. User can choose to have either one of them or both together.

If user tries to enter the value that exceeds the range, `onChange` will not be triggered, therfore nothing will be typed.

It would be same result for a scenario where initial value exceeds the range.

export const maxMinValueNumberFieldCode = `() => {
  const [value, setValue] = React.useState(0);
  const handleChange = (val) => setValue(val);
  const numberWithCommas = (num) => {
    const formatter = new Intl.NumberFormat('en-CA');
    return formatter.format(num);
  };
  return (
    <NumberField
      label="This is a label"
      id="number-field-maxmin"
      onChange={handleChange}
      onConvertToNumber={(val) => Number(val.replaceAll(',', ''))}
      onConvertToFormatString={(num) => numberWithCommas(num)}
      value={value}
      maxValue={300}
      minValue={-50}
    />
  );
}`;

<CodeExample scope={scope} code={maxMinValueNumberFieldCode} />

### NumberField With Number Type

`numberType` has two values: `integer` and `decimal`. `Integer` allows integer number only such as -3, 5 and no decimal can be typed.

`Decimal` allows user to type in decimal numbers such as 0.44, -3.491. There is no limit on decimal places.

By default, `numberType` is set to `integer`.

export const numberTypeNumberFieldCode = `() => {
  const [value, setValue] = React.useState(0);
  const handleChange = (val) => setValue(val);
  const numberWithCommas = (num) => {
    const formatter = new Intl.NumberFormat('en-CA');
    return formatter.format(num);
  };
  return (
    <NumberField
      label="This is a label"
      id="number-field-number"
      onChange={handleChange}
      onConvertToNumber={(val) => Number(val.replaceAll(',', ''))}
      onConvertToFormatString={(num) => numberWithCommas(num)}
      value={value}
      numberType="decimal"
    />
  );
}`;

<CodeExample scope={scope} code={numberTypeNumberFieldCode} />

### NumberField With Globalized Value | `fr-CA`

Here is an example of a globalized number field. In the `fr-CA` locale, the thousands separator is denoted by a space and the decimal character by a comma. For example, `1 234,56` is equivalent to `1,234.56` in the `en-US` locale.

`onEditFormatString` converts number input into a string onFocus and onChange. This prop is used when the input needs to be formatted differently on edit than it is in `onConvertToFormatString`. In this example, it is used to format the number input into a string without the thousands separator.

Please refer to our `everest-dayforce` NumberField to see how we paired our component with globalization functions.

export const globalizedNumberFieldCode = `() => {
  const [value, setValue] = React.useState(0);
  const handleChange = (num) => {
    setValue(num);
  };
  // 1 234,56 --> 1234.56
  const convertToNumber = (val) => {
    const decimalChar = convertToFormatString(1.1).substring(1, 2);
    const thousandsChar = convertToFormatString(1000).substring(1, 2);
    const numberString = val.replaceAll(thousandsChar, '').replaceAll(decimalChar, '.');
    return Number(numberString);
  };
  // 1234.56 --> 1234,56
  const convertToFormatStringOnEdit = (num) => {
    const options = {
      useGrouping: false,
      maximumFractionDigits: 20,
    };
    const formatter = new Intl.NumberFormat('fr-CA', options);
    return formatter.format(num);
  };
  // 1234.56 --> 1 234,56
  const convertToFormatString = (num) => {
    const formatter = new Intl.NumberFormat('fr-CA');
    return formatter.format(num);
  };
  return (
    <NumberField
      label="This is a label"
      id="number-field-number"
      onChange={handleChange}
      onConvertToNumber={(val) => convertToNumber(val)}
      onEditFormatString={(num) => convertToFormatStringOnEdit(num)}
      onConvertToFormatString={(num) => convertToFormatString(num)}
      value={value}
      numberType="decimal"
    />
  );
}`;

<CodeExample scope={scope} code={globalizedNumberFieldCode} />

### NumberField Allowing Null As Input Value

User can pass in the value `null` and empty value or nothing will be displayed on the input field.

export const allowNullNumberFieldCode = `() => {
  const [value, setValue] = React.useState(null);
  const handleChange = (val) => setValue(val);
  const numberWithCommas = (num) => {
    const formatter = new Intl.NumberFormat('en-CA');
    return formatter.format(num);
  };
  return (
    <div>
      <div style={{ marginBottom: '10px' }}>
        <NumberField
          label="This is a label"
          id="number-field-null"
          onChange={handleChange}
          onConvertToNumber={(val) => Number(val.replaceAll(',', ''))}
          onConvertToFormatString={(num) => numberWithCommas(num)}
          value={value}
        />
      </div>
      <Button
        id="set-input-null-btn"
        label="Click to set the input field value to null"
        onClick={() => handleChange(null)}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={allowNullNumberFieldCode} />

### Accessing NumberField using ref

Click on the Button to access the NumberField, refer to the console for the element details.

export const refCode = `() => {
  const styles = {
    row: {
      display: 'flex',
      justifyContent: 'space-around',
      flexWrap: 'wrap',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      rowGap: '10px',
    },
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  const ref = React.useRef(null);
  const [value, setValue] = React.useState(0);
  const handleChange = (value) => setValue(value);
  return (
    <Column>
      <Row>
        <Button
          id="access-element-btn"
          label="Click to access element"
          onClick={() => {
            console.log(ref.current);
          }}
        />
      </Row>
      <Row>
        <NumberField ref={ref} label="This is a label" id="number-field-ref" onChange={handleChange} value={value} />
      </Row>
    </Column>
  );
}`;

<CodeExample scope={scope} code={refCode} />

## How to Use

NumberField accepts input in the form of an integer or decimal, which can be set using `numberType`. It also can handle min and max values using the respective props, `minValue` and `maxValue`.

Formatting of input can be customized through `onConvertToNumber`, `onConvertToFormatString`, and `onEditFormatString`. These functions accept callbacks and it's up to the consumer to determine how the input should be formatted.

For example, in the `es-ES` locale, we can remove the thousands separator while editing input and leave it in while not editing as shown below:

- `onConvertToNumber` Converts string input into a number

  "1.234,56" --> 1234.56

- `onConvertToFormatString` Converts number input into a string

  1234.56 --> "1.234,56"

- `onEditFormatString` Converts number input into a string on edit

  1234.56 --> "1234,56"

**Note:** `value` is of type `number | null` (not `string`).

## Accessibility

Input fields with `type="number"` face numerous accessibility and browser issues. Therefore, for general numerical input, input fields with attributes like `type="text"`, `inputmode="numerical"` and `pattern="[0-9]*"` are recommended.

When used on mobile devices, the `inputmode="numeric` attribute will display a keyboard with only numeric values. The `pattern` attribute was not included due to its in-browser tooltip not being customizable in design and potentially causing issues on certain browsers.

NumberField currently does not provide feedback when a user enters non-numerical value, which is considered bad accessibility practice. More research needs to be done to add fully accessible validation to our NumberField component.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
