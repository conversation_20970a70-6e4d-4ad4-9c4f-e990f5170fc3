# Number Field

## Summary

Research and document implementations for the Everest Number Field.

- Start Date: 2022-06-03
- Figma link:

## Detailed Design

NumberField is complex component that supports features such as validation, formatting(decimals, currency,...), globalizaton(RTL, international formatting), unique accessibility issues(type="number" vs type="text").

Design of NumberField is identical to TextField, but with added functionalities, therefore NumberField will be built by having HOC wrapper around TextField.

NumberField is be able to receive various formatting methods from Globalization and can be formatted as per their respective culture.

With current design, user will be able to type anything including numbers, letters and special characters during `onChange` phase then parsing/validation will be done during `onBlur`. Eventually, we would like to update this so that parsing/validation will be done during `onChange` and Globalization team is currently conducting research to see if there are potential solution around it. Therefore, expect more enhancement to be done on NumberField component.

## API

1. **name**: string  
   Sets the input name which will be used to deliver data to the back-end when the form is submitted.
1. **label**: string (optional)  
   Sets the label text which gets rendered as part of the text input.
1. **value**: string | number  
   Accepts the initial value of the text input.
1. **onChange**: void  
   Accepts the callback function for the onchange event.
1. **onFocus**: void  
   Accepts the callback function for the onfocus event.
1. **onBlur**: void  
   Accepts the callback function for the onblur event.
1. **disabled**: boolean  
   Sets the **disabled** attribute on the text input.
1. **readonly**: boolean  
   Sets the **readonly** attribute on the text input.
1. **required**: boolean  
   Sets the **required** attribute on the text input. Adds the asterisk to the label.
1. **iconName**: string  
   Accepts the name of the icon and will be used to render an icon at the start of the input.
1. **status**: "success" | "error" | "default"  
   Indicates the state of the input. By default, status is unset.
1. **statusMessage**: string  
   Specifies the status message which is rendered underneath the text input. Will override the **helperText** value. This will be used only when **status** is set to "error" or "success".
1. **statusMessagePrefix**: string
   Sets the prefix for the status message which is rendered in bold.
1. **helperText**: string  
   Specifies the helper message which is rendered underneath the text input.
1. **helperTextPrefix**: string
   Sets the prefix for the help text which is rendered in bold.
1. **id**: string  
   Specifies the id of the text input element.Required for a11y purposes - associate with a label.
1. **ref**: Ref  
   Accepts the Ref which can be used by the other components to target the text input component.
1. **autocomplete**: boolean  
   Sets the **autocomplete** attribute on the text input.
1. **ariaLabel**: string  
   Specifies the **aria-label** attribute for the text input.
1. **ariaLabelledBy**: string  
   Specifies the id of the label to associate the text input with. This should be only used in those cases when there is a standalone label rendered in dom, which needs to be associated with a text input.
1. **ariaDescribedBy**: string  
    Specifies the id of the parapraph (or other similar element) which will be used to provide the description of the text input. This should be only used in those cases when there is a standalone description or helper text rendered in dom, which needs to be associated with a text input.
   ~~dir isn't needed right now, might be added in the future.~~
1. **testId**: string  
   Sets the data-test-id attribute on the text input element.
1. **formatOnType**: boolean
   If set to True, parsing functionality will be done in onChange.
   If False then parsing will be done in onBlur.
1. **onConvertToNumber**: `(value: string) => number;`
   Takes in Globalization format method to convert formatted string to number type.
1. **onConvertToFormattedString**: `(value: number) => string;`
   Takes in Globalization format method to convert number to formatted string.

## Accessibility

- Common sense would indicate that for NumberField, attribute `type="numeric"` should be applied, but it is more complex than it looks.

- As per Cedar Design System, attribute `type="numeric"` on `<input>` field will only accept pure number values as input and rejects all other content, which can cause issues with a numeric identifier that has leading zeroes and may behave differently across browsers and devices. Source: https://rei.github.io/rei-cedar-docs/components/input/

- As per MDN, "The number input type should only be used for incremental numbers, especially when spinbutton incrementing and decrementing are helpful to user experience. The number input type is not appropriate for values that happen to only consist of numbers but aren't strictly speaking a number, such as postal codes in many countries or credit card numbers." Source: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/number#using_number_inputs

- UK government has done intense research on implication of `type="numeric"` and found out assistive technologies such as Dragon Naturally Speaking and NVDA have issues identifying correctly. They have also identified different behaviours displayed on Chrome and Safari.

- As per issue described here https://github.com/w3c/html/issues/1355, input field with `type="numeric"` does not allow user to type in non-numerics such as string. Since browser does not give feedback on typing in "wrong" character in the input to the user, this is considered bad practice in terms of accessibility.

- UK government came up with alternative way to handle number input, which is to apply `type="text"`, `inputmode="numeric"`, and `pattern="[0-9]*"`. Since `inputmode` is supported by all mobile devices, numeric key pad pops up when user trying to type, which force them to type in numbers only.

```
<input type="text" id="206" pattern="[0-9]*" inputmode="numeric" novalidate="novalidate" autocorrect="off" spellcheck="false" autocapitalize="off" class="cdr-input_12-0-0 cdr-input--primary_12-0-0">
```

- It's important to note that regardless of input type `for` attribute for label and `id` attribute for input should match.

```
<label for="ticketNum">Number of tickets you would like to buy:</label>
<input id="ticketNum" type="number" name="ticketNum" value="0">
```

- When using Prefix and Suffix, make sure position prefixes and suffixes so that they’re outside of their input. This is to avoid interfering with some browsers that might insert an icon into the input (for example to show or generate a password). Here is good example: https://design-system.service.gov.uk/components/text-input/input-prefix-suffix/index.html

- When working in production and there is a relevant input purpose, it recommended to use the `autocomplete` attribute to meet WCAG 2.1 Level AA.

```
<div class="govuk-form-group">
  <label class="govuk-label" for="postcode">
    Postcode
  </label>
  <input class="govuk-input govuk-input--width-10" id="postcode" name="postcode" type="text" autocomplete="postal-code">
</div>
```

## Alternatives/Trade-Offs

Will be completed in Part 2

## Q&A

**`type="text"` vs `type="number"` for NumberField**

- Across all design systems, UK governement way is widely accepted practice when it comes to building accessbile NumberField which is to use of following attributes: `type="text"`, `inputmode="numeric"` and `pattern`.

**How many types does this NumberField need to support?**

- Since Dayforce is HR software, it is most likely, it needs to support as many types as possible such as date, postal code, currency, etc. ETrade Design for instance supports 6 different types.

**How will globalization handled?"**

- Globalization will be handled by external "globalization" layer - GlobalizationProvider.

## Future Considerations

Currently, NumberField in Dayforce supports parsing during onBlur/onSubmit, but our ambition is to build NumberField that can parse string/number during onChange and is under consideration.

## Other Design Systems

For example:

**UK Government** - https://design-system.service.gov.uk/components/text-input/

- Has done intense research on number input accessibility.
- Supports `type="text"`, `inputmode="numeric"`, and `pattern="[0-9]*"` as attribute to meet accessbility criteria.

**Cedar Design System** - https://rei.github.io/rei-cedar-docs/components/input/

- Supports two different types of number input components: 1. Numeric Input - does not use `type="number"`. Instead it follows UK government number input format. 2. Number/Quantity Input - use `type="number"`, which only accept pure number values as input and rejects all other content.

**Adobe React Aria** - https://react-spectrum.adobe.com/react-aria/useNumberField.html

- This isn't a design system, but custom hook that assists meeting accessbility.
- Support for internationalized number formatting and parsing including decimals, percentages, currency values, and units.
- Support for the Latin, Arabic, and Han positional decimal numbering systems in over 30 locales.
- Stored different languages in JSON files: https://github.com/adobe/react-spectrum/tree/main/packages/%40react-aria/numberfield/intl

```
{
  "decrease": "{fieldLabel}を縮小",
  "increase": "{fieldLabel}を拡大",
  "numberField": "数値フィールド"
}
```

**Instructure UI** - https://instructure.design/#NumberInput/#guidelines

- Handles arrow buttons, up/down arrow keys, and typing into the input. It also includes an onBlur handler that displays an error message if the input is invalid or missing.

```
   <NumberInput
      renderLabel={`How old are you? (${this.MIN}-${this.MAX})`}
      display={this.state.inline ? 'inline-block' : null}
      messages={this.state.messages}
      onBlur={this.handleBlur}
      onChange={this.handleChange}
      onDecrement={this.handleDecrement}
      onIncrement={this.handleIncrement}
      interaction={this.state.disabled
      ? 'disabled'
      : this.state.readOnly ? 'readonly' : 'enabled'
      }
      isRequired
      showArrows={this.state.showArrows}
      value={this.state.value}
   />
```

**ETrade Design** - http://react.etrade.design/?path=/story/textfield

- Supports 6 different format types(currency, digit, number, date, phone, ssn) through `formatType` prop.

## Required PBIs

- Develop the component and include testing (React + a11y + unit + integration + visual + bug fixes)
