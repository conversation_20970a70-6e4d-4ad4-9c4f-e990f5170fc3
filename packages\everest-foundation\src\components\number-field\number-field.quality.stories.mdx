import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { NumberField } from './number-field';
import { screen, userEvent } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Number Field"
  component={NumberField}
  decorators={[ChromaticDecorators.padStory]}
  parameters={{
    controls: {
      exclude: [
        'value',
        'ref',
        'onChange',
        'onBlur',
        'onFocus',
        'connectedCallback',
        'disconnectedCallback',
        'ariaLabel',
        'ariaLabelledBy',
        'ariaDescribedBy',
        'type',
      ],
    },
    chromatic: Chromatic.ENABLE_CI,
  }}
  argTypes={{
    disabled: {
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    readOnly: {
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    required: {
      type: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    status: {
      type: 'enum',
      control: 'radio',
      options: ['default', 'error', 'success'],
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    autocomplete: {
      type: 'enum',
      control: 'radio',
      options: ['off', 'on'],
      table: {
        defaultValue: { summary: 'off' },
      },
    },
    dir: {
      type: 'enum',
      control: 'radio',
      options: ['ltr', 'rtl'],
      table: {
        defaultValue: { summary: 'ltr' },
      },
    },
  }}
  args={{
    name: 'test-name',
    label: 'This is a label',
    status: 'default',
    autocomplete: 'off',
    dir: 'ltr',
    testId: 'test-id',
    id: 'number-field-id',
  }}
/>

# Number Field

## Live Demo

export const ControlledNumberField = (props) => {
  const [value, setValue] = React.useState(props.value ?? 0);
  const numberWithCommas = (num) => {
    const formatter = new Intl.NumberFormat('en-CA');
    return formatter.format(num);
  };
  return (
    <NumberField
      {...props}
      value={value}
      onChange={(newValue) => setValue(newValue)}
      onConvertToNumber={(val) => Number(val.replaceAll(',', ''))}
      onConvertToFormatString={(num) => numberWithCommas(num)}
    />
  );
};

<Canvas>
  <Story name="Default">{(args) => <ControlledNumberField {...args} />}</Story>
</Canvas>

<Canvas>
  <Story name="Default Value">{(args) => <ControlledNumberField {...args} value="0123456789" />}</Story>
</Canvas>

<Canvas>
  <Story name="Readonly">{(args) => <ControlledNumberField {...args} readOnly={true} />}</Story>
</Canvas>

<Canvas>
  <Story name="Disabled">{(args) => <ControlledNumberField {...args} disabled={true} />}</Story>
</Canvas>

<Canvas>
  <Story name="Required">{(args) => <ControlledNumberField {...args} required={true} />}</Story>
</Canvas>

<Canvas>
  <Story name="Helper Text">
    {(args) => <ControlledNumberField {...args} helperText="This is a helper text" helperTextPrefix="Hint:" />}
  </Story>
</Canvas>

<Canvas>
  <Story name="Success">
    {(args) => (
      <ControlledNumberField
        {...args}
        status="success"
        statusMessage="This is a success message"
        statusMessagePrefix="Success:"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error">
    {(args) => (
      <ControlledNumberField
        {...args}
        status="error"
        statusMessage="This is an error message"
        statusMessagePrefix="Error:"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="User Input"
    play={async () => {
      await userEvent.type(screen.getByRole('textbox'), 'Test text');
    }}
  >
    {(args) => <ControlledNumberField label={args.label} value={args.value} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Tab Focus"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <ControlledNumberField label={args.label} value={args.value} />}
  </Story>
</Canvas>

<ArgsTable story="Number Field" />
