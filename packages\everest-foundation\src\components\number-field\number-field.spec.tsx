import React, { useRef, useState } from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { NumberField } from './number-field';
import { Button } from '../button';

const onChange = jest.fn().mockImplementation((newValue: number | null, setValue) => act(() => setValue(newValue)));

interface IRenderControlledNumberField {
  labelText: string;
  testId: string;
  id: string;
  experimentalFormatOnType?: boolean;
  onConvertToFormatString: (value: number) => string;
  onConvertToNumber: (value: string) => number;
  onEditFormatString?: (value: number) => string;
  numberType?: 'integer' | 'decimal';
  initialValue?: number | null;
  maxValue?: number;
  minValue?: number;
}

const renderControlledNumberField = (numberFieldParameters: IRenderControlledNumberField) => {
  const {
    labelText,
    testId,
    id,
    experimentalFormatOnType,
    onConvertToFormatString,
    onConvertToNumber,
    onEditFormatString,
    numberType,
    initialValue = null,
    maxValue,
    minValue,
  } = numberFieldParameters;
  const NumberFieldWrapper = () => {
    const [value, setValue] = useState<number | null>(initialValue);

    return (
      <NumberField
        label={labelText}
        testId={testId}
        value={value}
        id={id}
        onChange={(newValue) => onChange(newValue, setValue)}
        experimentalFormatOnType={experimentalFormatOnType}
        onConvertToFormatString={onConvertToFormatString}
        onConvertToNumber={onConvertToNumber}
        onEditFormatString={onEditFormatString}
        numberType={numberType}
        maxValue={maxValue}
        minValue={minValue}
      />
    );
  };

  render(<NumberFieldWrapper />);
};

describe('[NumberField]', () => {
  const testId = 'number-field-test-id';
  const buttonTestId = 'button-test-id';
  const labelText = 'This is a label';
  const id = 'number-field-id';
  const onConvertToNumber = (val: string) => {
    return +val.replace(/,/g, '');
  };
  const onConvertToFormatString = (num: number) => {
    return num.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,');
  };
  const user = userEvent.setup({ delay: null });
  const getNumberField = () => screen.getByRole('textbox', { name: labelText });
  const getButton = () => screen.getByRole('button', { name: 'Click Me' });
  enum NUMBER_TYPE {
    DECIMAL = 'decimal',
    INTEGER = 'integer',
  }
  beforeEach(onChange.mockClear);

  // While typing: "" + "1a2b3c" => "123"
  // After blur: => "123"
  it('should not render non-numerical alphanumeric characters', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      initialValue: null,
    });

    await user.type(getNumberField(), '1a2b3c');
    expect(getNumberField()).toHaveValue('123');
  });

  // While typing: "" + "12345" => "12,345"
  // After blur: => "12,345"
  it('should render formatted values as values are entered when formatOnType is set to true and custom functions are provided', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      experimentalFormatOnType: true,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '12345');
    expect(getNumberField()).toHaveValue('12,345'); // not working - keeps thinking the value is '12,354'
  });

  // While typing: "" + "12345,67" => "12345,67"
  // After blur: => "12.345,67"
  it('should render formatted values onChange when onEditFormatString is provided', async () => {
    const onConvertToNumber = (val: string) => {
      return +val.replace('.', '').replace(',', '.');
    };
    const onConvertToFormatString = (num: number) => {
      const formatter = new Intl.NumberFormat('es-ES');
      return formatter.format(num);
    };
    const onEditFormatString = (num: number) => {
      const options = {
        useGrouping: false,
        maximumFractionDigits: 20, // Adding { maximumFractionDigits: 20 } to override default 3 decimal characters
      };
      const formatter = new Intl.NumberFormat('es-ES', options);
      return formatter.format(num);
    };

    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      onEditFormatString: onEditFormatString,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '12345,67');
    expect(getNumberField()).toHaveValue('12345,67');

    await user.tab();
    expect(getNumberField()).toHaveValue('12.345,67');

    // onEditFormatString prop is used to format input on edit differently than onConvertToFormatString would on blur
    // in this case, onEditFormatString should remove the thousands separator on edit
    await user.tab({ shift: true });
    expect(getNumberField()).toHaveValue('12345,67');
  });

  it('should fire onBlur event, when field value is null and remain null value after onBlur event', async () => {
    const onBlur = jest.fn();
    render(
      <>
        <NumberField
          label={labelText}
          id={id}
          value={null}
          testId={testId}
          onBlur={onBlur}
          onConvertToFormatString={onConvertToFormatString}
          onConvertToNumber={onConvertToNumber}
        />
        {/* Button to tab over to focus on which will trigger onBlur from NumberField*/}
        <Button id="button-click-me" label="Click Me" testId={buttonTestId} onClick={() => onChange(null, setValue)} />
      </>
    );

    await user.tab();
    expect(getNumberField()).toHaveFocus();
    await user.tab();
    expect(onBlur).toHaveBeenCalledTimes(1);
    expect(getNumberField()).toHaveValue('');
  });

  // While typing: "" + "12345" => "12345"
  // After blur: => "12,345"
  it('should render formatted values upon on blur when formatOnType is set to false and custom functions are provided then values are entered then user click outside of input field', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '12345');
    await user.tab();
    expect(getNumberField()).toHaveValue('12,345');
  });

  // While typing: "" + "1.23" => "1.23"
  // After blur: => "1.23"
  it('should render decimal number when numberType is set to decimal', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '1.23');
    expect(getNumberField()).toHaveValue('1.23');
  });

  // While typing: "" + "1.23" => "123"
  // After blur: => "123"
  it('should skip decimal and render rest of the integer when numberType is to integer but decimal number is entered', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '1.23');
    expect(getNumberField()).toHaveValue('123');
  });

  // While typing: "-" + "000.0" => "-0000"
  // After blur: => "0"
  it('should skip decimal and render rest of the integer when numberType is to integer but decimal number equal to 0 is entered', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '-000.0');
    expect(getNumberField()).toHaveValue('-0000');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
  });

  // While typing: "" + "309" => "300"
  // After blur: => "300"
  it('should render a max value when the value entered is higher than the maxValue', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      maxValue: 300,
    });

    await user.type(getNumberField(), '309');
    expect(getNumberField()).toHaveValue('300');
  });

  it('should render a minimum value when the value entered is lower than the minValue', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      minValue: 50,
    });

    await user.type(getNumberField(), '2');
    expect(getNumberField()).toHaveValue('50');
  });

  it('should render empty value when a last digit remaining is deleted from input field', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '2');
    await user.type(getNumberField(), '{backspace}');
    expect(getNumberField()).toHaveValue('');
  });

  it('should be able to enter and render the same number that was deleted', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '3');
    await user.type(getNumberField(), '{backspace}');
    expect(getNumberField()).toHaveValue('');

    await user.type(getNumberField(), '3');
    expect(getNumberField()).toHaveValue('3');
  });

  // While typing: "-" + "9" => "-9"
  // After blur: => "-9"
  it('should be able to enter a negative sign before a digit is entered', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '-');
    await user.type(getNumberField(), '9');
    expect(getNumberField()).toHaveValue('-9');

    await user.tab();
    expect(getNumberField()).toHaveValue('-9');
  });

  // While typing: "" + "0.003" => "0.003"
  // After blur: => "0.003"
  it('should be able to enter a decimal with digits other than 0', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '.');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '3');
    expect(getNumberField()).toHaveValue('0.003');

    await user.tab();
    expect(getNumberField()).toHaveValue('0.003');
  });

  // While typing: "-" + "0.003" => "-0.003"
  // After blur: => "-0.003"
  it('should be able to enter a negative sign followed by a decimal with digits other than 0', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '-0');
    await user.type(getNumberField(), '.');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '3');
    expect(getNumberField()).toHaveValue('-0.003');

    await user.tab();
    expect(getNumberField()).toHaveValue('-0.003');
  });

  // While typing: "-" + "000.3" => "-0.3"
  // After blur: => "-0.3"
  it('should be able to enter a negative sign followed by trailing zeroes before a decimal with digit other than 0', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '-');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '.');
    await user.type(getNumberField(), '3');
    expect(getNumberField()).toHaveValue('-0.3');

    await user.tab();
    expect(getNumberField()).toHaveValue('-0.3');
  });

  // While typing: "3" + "-" => "3"
  // After blur: => "3"
  it('should render no change if a digit other than 0 is present then negative sign is typed', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '3');
    await user.type(getNumberField(), '-');
    expect(getNumberField()).toHaveValue('3');
  });

  it('should fire onChange event with max value when typed value is greater than the value from maxValue prop', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      initialValue: 0,
      maxValue: 7,
    });

    await user.type(getNumberField(), '9');
    expect(onChange).toBeCalledTimes(1);
    expect(onChange).toBeCalledWith(7, expect.anything());
  });

  it('should fire onChange event with min value when pasted value is less than the value from minValue prop', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      initialValue: 7,
      minValue: 5,
    });

    await user.type(getNumberField(), '{backspace}');
    await user.paste('1');
    expect(onChange).toBeCalledTimes(2);
    expect(onChange).toBeCalledWith(5, expect.anything());
  });

  // While typing: "" + "000" => "000"
  // After blur: => "0"
  it('should fire onChange event once when 0 is entered consecutively from empty field then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    expect(getNumberField()).toHaveValue('000');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(1);
    expect(onChange).toBeCalledWith(0, expect.anything());
  });

  // While typing: "" + "0.000" => "0.000"
  // After blur: => "0"
  it('should fire onChange event when decimal separator then 0 are entered consecutively from empty field then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '.');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '0');
    expect(getNumberField()).toHaveValue('0.000');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(1);
    expect(onChange).toBeCalledWith(0, expect.anything());
  });

  it('should fire onChange event if max value is equal to new value', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      maxValue: 3,
    });

    await user.type(getNumberField(), '3');
    await user.tab();

    expect(getNumberField()).toHaveValue('3');
    expect(onChange).toBeCalledWith(3, expect.anything());
    expect(onChange).toBeCalledTimes(1);
  });

  it('should fire onChange event if min value is equal to new value', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      minValue: 1,
    });

    await user.type(getNumberField(), '1');
    await user.tab();

    expect(getNumberField()).toHaveValue('1');
    expect(onChange).toBeCalledWith(1, expect.anything());
    expect(onChange).toBeCalledTimes(1);
  });

  // While typing: "0" + ",,," => "0,"
  // After blur: => "0"
  it('should not be able to enter consecutive non-digit characters', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      initialValue: 0,
    });

    await user.type(getNumberField(), ',');
    await user.type(getNumberField(), ',');
    await user.type(getNumberField(), ',');
    expect(getNumberField()).toHaveValue('0,');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(0);
  });

  it('should set the value of NumberField to null when button is clicked', async () => {
    const NumberFieldWrapper = () => {
      const [value, setValue] = useState<number | null>(50);

      return (
        <>
          <NumberField
            label={labelText}
            testId={testId}
            value={value}
            id={id}
            onChange={(newValue) => onChange(newValue, setValue)}
            onConvertToFormatString={onConvertToFormatString}
            onConvertToNumber={onConvertToNumber}
          />
          <Button
            id="button-click-me"
            label="Click Me"
            testId={buttonTestId}
            onClick={() => onChange(null, setValue)}
          />
        </>
      );
    };

    render(<NumberFieldWrapper />);

    await user.click(getButton());
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toBeCalledWith(null, expect.anything());
    expect(getNumberField()).toHaveTextContent('');
  });

  it('should allow pass null to value prop without getting type error and display empty field', () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    expect(getNumberField()).toHaveValue('');
  });

  it('onChange should pass null value, when last character is deleted from input field', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      initialValue: 0,
    });

    await user.type(getNumberField(), '7');
    await user.type(getNumberField(), '{backspace}');
    expect(onChange).toBeCalledTimes(2);
    expect(onChange).toBeCalledWith(null, expect.anything());
  });

  // Negative Number & Decimal Cases
  // While typing: "" + "-" => "-"
  // After blur: => ""
  it('should render correct output when negative sign is typed in empty input field then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '-');
    expect(getNumberField()).toHaveValue('-');
    expect(onChange).toBeCalledTimes(0);

    await user.tab();
    expect(getNumberField()).toHaveValue('');
    expect(onChange).toBeCalledTimes(1);
    expect(onChange).toBeCalledWith(null, expect.anything());
  });

  // While typing: "-" + "0" => "-0"
  // After blur: => "0"
  it('should render correct output when negative sign then 0 are typed in empty input field then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '-');
    await user.type(getNumberField(), '0');
    expect(getNumberField()).toHaveValue('-0');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(1);
  });

  // While typing: "0" + "-" => "-"
  // After blur: => ""
  it('should render correct output when 0 then negative sign are typed in empty input field then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '-');
    expect(getNumberField()).toHaveValue('-');
    expect(onChange).toBeCalledTimes(1);
    expect(onChange).toBeCalledWith(0, expect.anything());

    await user.tab();
    expect(getNumberField()).toHaveValue('');
    expect(onChange).toBeCalledTimes(2);
    expect(onChange).toBeCalledWith(null, expect.anything());
  });

  // While typing: "-" + "0.0" => "-0.0"
  // After blur: => "0"
  it('should render correct output when negative sign then 0.0 are typed in empty input field then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '-');
    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '.');
    await user.type(getNumberField(), '0');
    expect(getNumberField()).toHaveValue('-0.0');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(1);
  });

  // While typing: "" + "0.0" => "0.0"
  // After blur: => "0"
  it('should render correct output when 0.0 is typed in empty input field then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), '.');
    await user.type(getNumberField(), '0');
    expect(getNumberField()).toHaveValue('0.0');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(1);
  });

  // While typing: "" + "0,0" => "0,0"
  // After blur: => "0"
  it('should render 0 if a comma is typed between two zeroes then onBlur', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      numberType: NUMBER_TYPE.DECIMAL,
    });

    await user.type(getNumberField(), '0');
    await user.type(getNumberField(), ',');
    await user.type(getNumberField(), '0');
    expect(getNumberField()).toHaveValue('0,0');

    await user.tab();
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(1);
  });

  // While typing: "0" + ",9" => "9"
  // After blur: => "9"
  it('should render correct value if comma is typed between a zero and non-zero digit', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      initialValue: 0,
    });

    await user.type(getNumberField(), ',');
    expect(getNumberField()).toHaveValue('0,');

    await user.type(getNumberField(), '9');
    expect(onChange).toBeCalledTimes(1);
    expect(getNumberField()).toHaveValue('9');

    await user.tab();
    expect(getNumberField()).toHaveValue('9');
  });

  // When minValue and maxValue are set to 0
  // Typing "-" + "7" => "0"
  // Pressing backspace + "-" + "3" => "0"
  it('should render 0 only when both minValue and maxValue are set to 0', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
      initialValue: 0,
      maxValue: 0,
      minValue: 0,
    });

    await user.type(getNumberField(), '-');
    await user.type(getNumberField(), '7');
    expect(getNumberField()).toHaveValue('0');

    await user.type(getNumberField(), '9');
    expect(getNumberField()).toHaveValue('0');

    await user.type(getNumberField(), '{backspace}');
    await user.type(getNumberField(), '-');
    await user.type(getNumberField(), '3');
    expect(getNumberField()).toHaveValue('0');
  });

  // While typing: "" + "0" => "0" and onChange() will fire
  it('should render 0 and fire onChange when 0 is typed in empty input field', async () => {
    renderControlledNumberField({
      labelText,
      testId,
      id,
      onConvertToFormatString,
      onConvertToNumber,
    });

    await user.type(getNumberField(), '0');
    expect(getNumberField()).toHaveValue('0');
    expect(onChange).toBeCalledTimes(1);
    expect(onChange).toBeCalledWith(0, expect.anything());
  });

  it('should focus number field input after calling focus on ref', async () => {
    const RenderControlledNumberFieldWithFocusRefButton = () => {
      const [value, setValue] = useState<number | null>(1);
      const numberFieldRef = useRef(null);
      const onFocusInput = jest.fn().mockImplementation(() => {
        numberFieldRef.current && (numberFieldRef.current as HTMLInputElement)?.focus();
      });
      return (
        <>
          <Button id="focus-input-button" label="Focus input" onClick={onFocusInput} />
          <NumberField
            ref={numberFieldRef}
            label={labelText}
            testId={testId}
            value={value}
            id={id}
            onChange={(newValue) => onChange(newValue, setValue)}
            onConvertToFormatString={onConvertToFormatString}
            onConvertToNumber={onConvertToNumber}
          />
        </>
      );
    };

    render(<RenderControlledNumberFieldWithFocusRefButton />);

    await waitFor(() => expect(getNumberField()).toHaveValue('1'));
    expect(getNumberField()).not.toHaveFocus();

    const externalFocusButton = screen.getByRole('button', { name: 'Focus input' });
    expect(externalFocusButton).toBeInTheDocument();
    await user.click(externalFocusButton);

    await waitFor(() => expect(getNumberField()).toHaveValue('1'));
    await waitFor(() => expect(getNumberField()).toHaveFocus());
  });
});
