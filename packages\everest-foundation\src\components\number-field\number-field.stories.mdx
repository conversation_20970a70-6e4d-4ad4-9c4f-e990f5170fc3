import { useState } from 'react';
import { <PERSON>a, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { NumberField } from './number-field';
import Examples from './number-field.examples.mdx';
import { action } from '@storybook/addon-actions';
import { useArgs } from '@storybook/client-api';

<Meta
  title="Components/Text Fields/Number Field"
  component={NumberField}
  parameters={{
    // override the default behavior of passing action-props for every prop that starts with 'on'
    // fixes issue with backwards compatibility if no onEditFormatString prop is defined
    actions: {
      argTypesRegex: null,
    },
    status: {
      type: 'ready',
    },
    controls: {
      exclude: ['ref', 'inputMode'],
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: '',
    },
  }}
  argTypes={{
    name: {
      description: 'Sets the name attribute on the number field.',
    },
    label: {
      description: 'Specifies the label rendered in the number field.',
    },
    onConvertToNumber: {
      description: 'Takes the input value of formatted string and returns a number.',
    },
    onConvertToFormatString: {
      description: 'Takes the input value of number and returns a formatted string.',
    },
    onEditFormatString: {
      description: 'Takes the input value of number and returns a separate formatted string for the display value.',
    },
    value: {
      control: '-',
      description: 'Accepts the numeric value of the number field.',
    },
    disabled: {
      type: 'boolean',
      description: 'Sets the disabled attribute on the number field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    readOnly: {
      type: 'boolean',
      description: 'Sets the readonly attribute on the number field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    required: {
      type: 'boolean',
      description: 'Sets the required attribute on the number field.',
      table: {
        defaultValue: { summary: false },
      },
    },
    numberType: {
      type: 'enum',
      control: 'radio',
      options: ['decimal', 'integer'],
      description: 'Sets the numberType of the number.',
      table: {
        defaultValue: { summary: 'integer' },
      },
    },
    experimentalFormatOnType: {
      type: 'boolean',
      description: 'Sets the experimentalFormatOnType prop value. For the time being, value should always be false.',
      table: {
        defaultValue: { summary: false },
      },
    },
    id: {
      description: 'Specifies the id attribute of the number field.',
    },
    status: {
      type: 'enum',
      control: 'radio',
      options: ['default', 'error', 'success'],
      description: 'Sets the status of the number field.',
      table: {
        defaultValue: { summary: 'default' },
      },
    },
    statusMessage: {
      description: 'Sets the status message rendered under the number field.',
    },
    statusMessagePrefix: {
      description: 'Sets the prefix to the status message rendered under the number field.',
    },
    helperText: {
      description: 'Sets the helper text rendered under the number field.',
    },
    helperTextPrefix: {
      description: 'Sets the prefix to the helper text rendered under the number field.',
    },
    autocomplete: {
      type: 'enum',
      control: 'radio',
      options: ['off', 'on'],
      description: 'Sets the autocomplete attribute on the number field.',
      table: {
        defaultValue: { summary: 'off' },
      },
    },
    testId: {
      description: 'An id used for automation testing.',
    },
  }}
  args={{
    name: 'test-name',
    label: 'This is a label',
    numberType: 'integer',
    disabled: false,
    readOnly: false,
    required: true,
    experimentalFormatOnType: false,
    id: 'number-field-id',
    status: 'default',
    statusMessage: 'This is a status message',
    statusMessagePrefix: 'Prefix:',
    helperText: 'This is some helper text',
    helperTextPrefix: 'Hint:',
    autocomplete: 'off',
    testId: 'test-id',
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    onChange: action('onChange'),
  }}
/>

# Number Field

<Examples />

## Live Demo

<Canvas>
  <Story name="Number Field">
    {(args) => {
      const [value, setValue] = useState(null);
      const formatString = (num) => {
        const options = {
          maximumFractionDigits: 20, // Adding { maximumFractionDigits: 20 } to override default 3 decimal characters
        };
        const formatter = new Intl.NumberFormat('en-US', options);
        return formatter.format(num);
      };
      const editFormatString = (num) => {
        const options = {
          useGrouping: false, // remove thousands separator
          maximumFractionDigits: 20, // Adding { maximumFractionDigits: 20 } to override default 3 decimal characters
        };
        const formatter = new Intl.NumberFormat('en-US', options);
        return formatter.format(num);
      };
      return (
        <NumberField
          {...args}
          value={value}
          onChange={(value) => {
            setValue(value);
            args.onChange(value);
          }}
          onConvertToNumber={(val) => Number(val.replaceAll(',', ''))}
          onConvertToFormatString={(num) => formatString(num)}
          onEditFormatString={(num) => editFormatString(num)}
        />
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Number Field" />
