import React, { useEffect, useRef, useState } from 'react';

import { ITextFieldProps, TextField } from '../text-field';

export interface INumberField extends Omit<ITextFieldProps, 'onChange' | 'value' | 'type' | 'maxLength' | 'inputMode'> {
  // Experimental feature, not production ready
  experimentalFormatOnType?: boolean;
  onConvertToNumber: (value: string) => number;
  onConvertToFormatString: (value: number) => string;
  // Used to format display value onChange/onFocus
  onEditFormatString?: (value: number) => string;
  onChange?: (value: number | null) => void;
  value: number | null;
  maxValue?: number;
  minValue?: number;
  numberType?: 'decimal' | 'integer';
}

export const NumberField = React.forwardRef<HTMLInputElement, INumberField>((props, ref) => {
  const {
    value,
    onChange,
    onBlur,
    onFocus,
    experimentalFormatOnType = false,
    onConvertToNumber,
    onConvertToFormatString,
    onEditFormatString,
    autocomplete,
    disabled,
    helperText,
    helperTextPrefix,
    id,
    label,
    name,
    readOnly,
    required,
    iconName,
    status,
    statusMessage,
    statusMessagePrefix,
    testId,
    ariaLabel,
    ariaDescribedBy,
    maxValue,
    minValue,
    numberType = 'integer',
  } = props;
  const [displayValue, setDisplayValue] = useState('');
  const inputActiveRef = useRef(false);
  // Check if the number is 'going' to be an integer
  // If user types just '1', by appending '1' to it, it will be 11, so we know its 'going' to be integer.
  const numberIntValidator = (val: string) => {
    return Number.isInteger(onConvertToNumber(val + '1'));
  };

  // Check if the number is 'going' to be a finite negative number
  // If user type '1.', by appending '1' to it, it will be 1.1, so we know it's 'going' to be a decimal.
  // If user types '-' and appends '1' to it, it will be -1, so we know it's 'going' to be negative.
  const negativeNumberValidator = (val: string) => {
    const convertedVal = onConvertToNumber(val + '1');
    return Number.isFinite(convertedVal) && convertedVal < 0;
  };

  const numberValidator = (val: string, fn: (str: string) => number): [boolean, number] => {
    const convertedValue = fn(val);
    // check if it is a number
    let isValidNumber = typeof convertedValue === 'number' && isFinite(convertedValue);
    // if it is, check if it will be an integer or decimal
    if (isValidNumber) {
      isValidNumber = numberType === 'integer' ? numberIntValidator(val) : isValidNumber;
    }
    return [isValidNumber, convertedValue];
  };

  // Checks min/maxValue bounds
  const numberRuleConversion = (num: number) => {
    let finalNumber = 0;
    finalNumber = numberType === 'integer' ? Math.trunc(num) : num;
    // JavaScript interpret 0 as falsy value, which means when either minValue or maxValue is set to 0
    // result of condition statement below will always return false, since false && true returns false.
    // To handle this edge case, 0 !== undefined was implemented which returns true as long as
    // value is entered including 0.
    if (minValue !== undefined && finalNumber < minValue) finalNumber = minValue;
    if (maxValue !== undefined && finalNumber > maxValue) finalNumber = maxValue;
    // After implementing new condition logic above, without this code, when minValue is set to 0
    // and user types '-' after the default value then type any number,
    // '-' will stay on the field instead of turning to 0.
    // This code will override that and have 0 appear on text field.
    if (minValue === 0 && finalNumber <= 0) setDisplayValue('0');
    return finalNumber;
  };

  // Checks if there are repeating non-digit chars (ex. ,)
  const hasConsecutiveNonDigits = (val: string): boolean => {
    for (let i = 0; i < val.length - 1; i++) {
      if (!/\d/.test(val[i]) && !/\d/.test(val[i + 1])) {
        return true;
      }
    }
    return false;
  };

  const handleValueChange = (val: string) => {
    if (experimentalFormatOnType === false) {
      if (typeof onChange === 'function' && onConvertToNumber) {
        const [isValidNumber, currentValue] = numberValidator(val, onConvertToNumber); // check typed string is valid number
        const previousValue = onConvertToNumber(displayValue);
        // to handle when user deleting very last char.
        // Once deleted, displayValue is set to blank and then onChange will trigger.
        // After that, the useEffect will take over for final step.
        if (val === '') {
          setDisplayValue('');
          value !== null && onChange?.(null);
          return;
        }
        // Replace 0 with - if - is typed afterwards
        if (val === '0-') {
          setDisplayValue('-');
          return;
        }
        // Allow negative sign to be entered in before integer & when value is 0
        if (val === '-') {
          setDisplayValue(val);
          value !== null && onChange?.(null);
          return;
        }
        // type any non-digit => should not repeat, trigger onChange
        // example: 0,,, => 0, => onChange(1 time) bc of 0
        if (hasConsecutiveNonDigits(val)) {
          return;
        }
        // If numberType = integer and value is equal to 0, set current val as displayVal (ex. -0000)
        if (numberIntValidator(val) && negativeNumberValidator(val) && currentValue === 0) {
          setDisplayValue(val);
          if (value && currentValue !== value) onChange?.(currentValue);
          return;
        }
        if (isValidNumber) {
          // previousValue is set to 0 after converting displayValue of '' to a number
          if (previousValue !== currentValue || (displayValue === '' && previousValue === 0)) {
            onChange?.(numberRuleConversion(currentValue)); // handle comma/decimal case & min/max bounds
          } else {
            setDisplayValue(val);
          }
        }
      }
    } else if (typeof onChange === 'function' && onConvertToNumber && onConvertToFormatString) {
      const [isValidNumber, currentValue] = numberValidator(val, onConvertToNumber);
      if (isValidNumber) {
        onChange?.(onConvertToNumber(val));
        setDisplayValue(onConvertToFormatString(currentValue));
      }
    }
  };

  const handleNumberFieldBlur = (e: React.FocusEvent<HTMLElement>) => {
    inputActiveRef.current = false;
    const element = e.target as HTMLInputElement;
    const blurVal = element.value;

    if (experimentalFormatOnType === false) {
      // Check if input when blurred is empty or contains a negative sign
      // Negative sign is assumed to be the same for all locales => -
      if (blurVal === '' || blurVal === '-') {
        setDisplayValue('');
        onChange?.(null);
        onBlur?.(e);
        return;
      }
      const [isNumber, convertedValue] = numberValidator(blurVal, onConvertToNumber);
      // type 0.00 / 0,00 / -0.0 => should be 0 onBlur
      if (isNumber && convertedValue === 0 && value !== convertedValue) {
        setDisplayValue('0');
        onChange?.(0);
        onBlur?.(e);
        return;
      }
      if (isNumber) {
        setDisplayValue(onConvertToFormatString?.(convertedValue));
      }
    }
    onBlur?.(e);
  };

  const handleNumberFieldFocus = (e: React.FocusEvent<HTMLElement>) => {
    inputActiveRef.current = true;
    const element = e.target as HTMLInputElement;
    const focusVal = element.value;

    if (experimentalFormatOnType === false) {
      if (displayValue === '') {
        setDisplayValue('');
      } else if (onEditFormatString) {
        setDisplayValue(onEditFormatString?.(onConvertToNumber?.(focusVal)) || '');
      } else {
        setDisplayValue(onConvertToNumber?.(focusVal).toString());
      }
    }
    onFocus?.(e);
  };

  useEffect(() => {
    const updatedValue = value === null ? null : numberRuleConversion(value);
    let formattedValue = updatedValue?.toString();

    if (inputActiveRef.current) {
      if (onEditFormatString && !!updatedValue) formattedValue = onEditFormatString?.(updatedValue || 0);
      // experimental feature
      if (experimentalFormatOnType) formattedValue = onConvertToFormatString?.(updatedValue || 0);
    } else if (
      // handle the nullable case
      (displayValue === '' && value === null) ||
      // allows user to manually set the value to null through event handler like onClick
      (value === null && updatedValue === null)
    ) {
      formattedValue = '';
    } else {
      const formatString = onConvertToFormatString?.(updatedValue || 0);
      formattedValue = formatString || updatedValue?.toString();
    }
    // set display value
    setDisplayValue(formattedValue ?? '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onConvertToFormatString, onEditFormatString, value]);

  return (
    <TextField
      autocomplete={autocomplete}
      disabled={disabled}
      helperText={helperText}
      helperTextPrefix={helperTextPrefix}
      id={id}
      label={label}
      name={name}
      readOnly={readOnly}
      required={required}
      iconName={iconName}
      status={status}
      statusMessage={statusMessage}
      statusMessagePrefix={statusMessagePrefix}
      testId={testId}
      ariaLabel={label ? undefined : ariaLabel}
      ariaDescribedBy={ariaDescribedBy}
      inputMode="numeric"
      value={displayValue}
      ref={ref}
      onChange={(val: string) => handleValueChange(val)}
      onBlur={(e: React.FocusEvent<HTMLElement>) => handleNumberFieldBlur(e)}
      onFocus={(e: React.FocusEvent<HTMLElement>) => handleNumberFieldFocus(e)}
    />
  );
});

NumberField.displayName = 'NumberField';
