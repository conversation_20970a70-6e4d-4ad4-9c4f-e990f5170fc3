import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { ContributedBanner } from '../../../.storybook/docs/shared/component-status-banner';
import { Omnibar } from './omnibar';
import { Avatar } from '../avatar';
import { <PERSON><PERSON> } from '../button';
import { IconButton } from '../icon-button';
import { PopoverMenu, PopoverMenuItem } from '../popover-menu';
import { SearchField } from '../search-field';
import { HighlightedIcon } from '../highlighted-icon';
import userTaro from '../../../assets/images/user-taro.jpg';
import place from '../../../assets/images/place.jpg';
export const scope = {
  Omnibar,
  Avatar,
  Button,
  IconButton,
  PopoverMenu,
  PopoverMenuItem,
  SearchField,
  userTaro,
  place,
  HighlightedIcon,
};

<ContributedBanner dev="<PERSON>" team="Workforce Management" />

## Omnibar Context

The Omnibar component is designed to provide a consistent banner experience across the application.
It covers a variety of use cases, including navigation, page headers, page-level actions, search, and more.
This flexibility allows users to customize the `startSection` and `endSection` of the Omnibar by passing in the components of their choosing.
The Omnibar will remain fixed at the top of the page, allowing additional content to scroll beneath it. As the page content moves underneath and above the Omnibar, it will be obscured by a blur effect.

## How to Use

The `startSection` will be fixed at the beginning of the Omnibar, while the `endSection` will be fixed at the end.
Customize the `startSection` and `endSection` by supplying the Omnibar with the components of your choosing.

## Responsive

The Omnibar is designed to be fully responsive, automatically expanding to fill the entire width of its parent container.
This ensures that the Omnibar adapts seamlessly to different screen sizes, providing a consistent user experience across various devices.

## Omnibar Examples

#### Omnibar with Back button and PopoverMenu

export const OmnibarExampleOne = `() => {
  const styles = {
    omnibarStartSection: {
      display: 'flex',
      alignItems: 'center',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      flex: '1 1 auto',
    },
    omnibarEndSection: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      marginInlineStart: 'var(--evr-spacing-lg)',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      maxWidth: 'calc(100% - var(--evr-spacing-lg))',
      flex: '1 0 auto',
    },
    omnibarTitleContainer: {
      overflow: 'hidden',
    },
    omnibarTitleText: {
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  };
  return (
    <Omnibar id={'omnibar'} testId={'omnibar-test-id'}>
      <>
        <div style={styles.omnibarStartSection}>
          <IconButton
            id="back-button"
            testId="back-button-test"
            ariaLabel="Back"
            iconName="arrowLeft"
            variant="secondaryNeutral"
            size="large"
            onClick={() => {}}
          />
          <div aria-live="polite" style={styles.omnibarTitleContainer}>
            <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
              Omnibar Title
            </h1>
            <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
              Subtitle
            </p>
          </div>
        </div>
        <div style={styles.omnibarEndSection}>
          <Button
            id="primary-button"
            testId="primary-button-test"
            label="Label"
            size="large"
            variant="primary"
            onClick={() => {}}
          />
          <PopoverMenu
            id="overflow"
            testId="overflow-test"
            buttonAriaLabel="Actions"
            triggerOption="iconButton"
            onChange={() => {}}
            triggerProps={{
              variant: 'tertiaryNeutral',
              startIcon: 'moreVert',
              size: 'large',
            }}
          >
            <PopoverMenuItem id="menu-item-1" testId="menu-item-1-test">
              Menu Item 1
            </PopoverMenuItem>
            <PopoverMenuItem id="menu-item-2" testId="menu-item-2-test">
              Menu Item 2
            </PopoverMenuItem>
            <PopoverMenuItem id="menu-item-3" testId="menu-item-3-test">
              Menu Item 3
            </PopoverMenuItem>
          </PopoverMenu>
        </div>
      </>
    </Omnibar>
  );
}`;

<CodeExample scope={scope} code={OmnibarExampleOne} />

#### Page Icon

export const PageIconExample = `() => {
  const styles = {
    omnibarStartSection: {
      display: 'flex',
      alignItems: 'center',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      flex: '1 1 auto',
    },
    omnibarEndSection: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      marginInlineStart: 'var(--evr-spacing-lg)',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      maxWidth: 'calc(100% - var(--evr-spacing-lg))',
      flex: '1 0 auto',
    },
    omnibarTitleContainer: {
      overflow: 'hidden',
    },
    omnibarTitleText: {
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  };
  return (
    <Omnibar id={'omnibar'} testId={'omnibar-test-id'}>
      <>
        <div style={styles.omnibarStartSection}>
          <HighlightedIcon fill="neutral" id="folder-icon" testId="folder-icon-test" iconName="folder" />
          <div aria-live="polite" style={styles.omnibarTitleContainer}>
            <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
              Omnibar Title
            </h1>
            <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
              Subtitle
            </p>
          </div>
        </div>
        <div style={styles.omnibarEndSection}>
          <Button
            id="secondary-button"
            testId="secondary-button-test"
            label="Secondary"
            size="large"
            variant="secondaryNeutral"
            onClick={() => {}}
          />
          <Button
            id="primary-button"
            testId="primary-button-test"
            label="Primary"
            size="large"
            variant="primary"
            onClick={() => {}}
          />
        </div>
      </>
    </Omnibar>
  );
}`;

<CodeExample scope={scope} code={PageIconExample} />

#### Page content blurs above Omnibar when scrolling

export const OmnibarExampleTwo = `() => {
  const styles = {
    omnibarStartSection: {
      display: 'flex',
      alignItems: 'center',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      flex: '1 1 auto',
    },
    omnibarEndSection: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      marginInlineStart: 'var(--evr-spacing-lg)',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      maxWidth: 'calc(100% - var(--evr-spacing-lg))',
      flex: '1 0 auto',
    },
    omnibarTitleContainer: {
      overflow: 'hidden',
    },
    omnibarTitleText: {
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  };
  return (
    <div style={{ width: '100%', height: '300px', overflow: 'auto' }}>
      <Omnibar id={'omnibar'} testId={'omnibar-test-id'}>
        <>
          <div style={styles.omnibarStartSection}>
            <Avatar id="avatar" testId="avatar-test" size="lg" src={userTaro} />
            <div aria-live="polite" style={styles.omnibarTitleContainer}>
              <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
                Omnibar Title
              </h1>
              <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
                Subtitle
              </p>
            </div>
          </div>
          <div style={styles.omnibarEndSection}>
            <IconButton
              id="refresh-button"
              testId="refresh-button-test"
              ariaLabel="Refresh"
              iconName="refresh"
              variant="tertiaryNeutral"
              size="large"
              onClick={() => {}}
            />
            <IconButton
              id="print-button"
              testId="print-button-test"
              ariaLabel="Print"
              iconName="print"
              variant="tertiaryNeutral"
              size="large"
              onClick={() => {}}
            />
          </div>
        </>
      </Omnibar>
      <div
        style={{
          margin: '56px auto',
          width: '50%',
          height: '600px',
          backgroundImage: \`url('\${place}')\`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />
    </div>
  );
}`;

<CodeExample scope={scope} code={OmnibarExampleTwo} />

## Accessibility

Ensure that the components supplied to the `startSection` and `endSection` of the `Omnibar` include an `id` and `ariaLabel` for an accessible screen reader experience.
