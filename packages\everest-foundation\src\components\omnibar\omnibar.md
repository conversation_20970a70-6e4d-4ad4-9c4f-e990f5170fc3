# Omnibar

- Start Date: Moved from Community in March, 2025
- Figma link: https://www.figma.com/file/f98Ur4FSqNjE2STjlCzB87/Omnibar-(Sub-Header)?node-id=7283-14912&p=f&t=5o8iPIDiVPZ5vpo6-0
- Epic: https://dayforce.atlassian.net/browse/PWEB-18980

## Summary

The Omnibar component is designed to provide a consistent banner experience across the application. It covers a variety of use cases, including navigation, page headers, page-level actions, search, and more. This flexibility allows users to customize the startSection and endSection of the Omnibar by passing in the components of their choosing. The Omnibar will stick to the top of the page while allowing additional page content to scroll underneath.

## API

### IOmnibarProps

1. **id**: `string`  
   Sets `id` attribute.
1. **testId**: `string`  
   Optional. Sets `data-testid` attribute.
1. **startSection**: `ReactNode`  
   Optional. Elements to be rendered at the start of the Omnibar.
1. **endSection**: `ReactNode`  
   Optional. Elements to be rendered at the end of the Omnibar.

### Usage

```jsx
const startSection: (
  <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--evr-spacing-sm)' }}>
    <IconButton
      id="back-button"
      testId="back-button-test"
      ariaLabel="Back"
      iconName="arrowLeft"
      variant="secondaryNeutral"
      size="large"
      onClick={() => {}}
    />
  </div>
);
const endSection: (
  <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--evr-spacing-2xs)' }}>
    <Button
      id="primary-button"
      testId="primary-button-test"
      label="Label"
      ariaLabel="Primary"
      size="large"
      variant="primary"
      disabled={false}
      onClick={() => {}}
    />
  </div>
);
return <Omnibar id={'omnibar'} testId={'omnibar-test-id'} startSection={startSection} endSection={endSection} />;
```

## Changelog

- **03/2025** - Omnibar copied from Community; released with no changes
- **03/20/2025** - Size prop removed in favor of having Omnibar take up 100% of the available width
- **04/17/2025** - Add children to props to be used in favor of start/end sections - `startSection` & `endSection` are still valid props
