@use '../../index.scss' as helper;

.evrOmnibar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: end;
  width: 100%;
  box-sizing: border-box;
  padding: var(--evr-spacing-sm) var(--evr-spacing-sm) 0px var(--evr-spacing-sm); // top right bottom left;
  backdrop-filter: blur(var(--evr-spacing-sm));
  z-index: var(--evr-z-index-inner);
}

.evrOmnibarSectionContainer {
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  height: var(--evr-size-6xl);
  padding: var(--evr-spacing-sm);
  border-radius: var(--evr-spacing-xl);
  border: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
  background: var(--evr-surfaces-primary-default);
  box-shadow: var(--evr-depth-02);
  max-width: 100%;
}
