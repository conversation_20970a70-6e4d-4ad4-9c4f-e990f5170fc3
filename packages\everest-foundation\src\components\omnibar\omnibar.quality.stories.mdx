import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { screen } from '@storybook/test';
import { Avatar } from '../avatar';
import { Button } from '../button';
import { Card } from '../card';
import { IconButton } from '../icon-button';
import { Image } from '../image';
import { Omnibar } from './/omnibar';
import { PopoverMenu, PopoverMenuItem } from '../popover-menu';
import { Tag } from '../tag';
import { TagGroup } from '../tag-group';

import mountain from '../../../assets/images/mountain.jpg';
import place from '../../../assets/images/place.jpg';
import userTaro from '../../../assets/images/user-taro.jpg';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Omnibar"
  component={Omnibar}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'omnibar-id',
    testId: 'test-id',
  }}
/>

export const styles = {
  omnibarStartSection: {
    display: 'flex',
    alignItems: 'center',
    gap: 'var(--evr-spacing-sm)',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    padding: 'calc(var(--evr-focus-ring-border) * 2)',
    flex: '1 1 auto',
  },
  omnibarEndSection: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 'var(--evr-spacing-sm)',
    overflow: 'hidden',
    marginInlineStart: 'var(--evr-spacing-lg)',
    padding: 'calc(var(--evr-focus-ring-border) * 2)',
    maxWidth: 'calc(100% - var(--evr-spacing-lg))',
    flex: '1 0 auto',
  },
  omnibarTitleContainer: {
    overflow: 'hidden',
  },
  omnibarTitleText: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
};

## Live Demo

<Canvas>
  <Story
    name="Omnibar with Back button and PopoverMenu"
    parameters={{ chromatic: { modes: { breakpointLg: defaultModes['breakpointLg'] } } }}
  >
    {(args) => {
      return (
        <Omnibar id={args.id} testId={args.testId}>
          <>
            <div style={styles.omnibarStartSection}>
              <IconButton
                id="back-button"
                testId="back-button-test"
                ariaLabel="Back"
                iconName="arrowLeft"
                variant="secondaryNeutral"
                size="large"
                onClick={() => {}}
              />
              <div aria-live="polite" style={styles.omnibarTitleContainer}>
                <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
                  Omnibar Title
                </h1>
                <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
                  Subtitle
                </p>
              </div>
            </div>
            <div style={styles.omnibarEndSection}>
              <Button
                id="primary-button"
                testId="primary-button-test"
                label="Label"
                size="large"
                variant="primary"
                onClick={() => {}}
              />
              <PopoverMenu
                id="overflow"
                testId="overflow-test"
                buttonAriaLabel="Actions"
                triggerOption="iconButton"
                onChange={() => {}}
                triggerProps={{
                  variant: 'tertiaryNeutral',
                  startIcon: 'moreVert',
                  size: 'large',
                }}
              >
                <PopoverMenuItem id="menu-item-1" testId="menu-item-1-test">
                  Menu Item 1
                </PopoverMenuItem>
                <PopoverMenuItem id="menu-item-2" testId="menu-item-2-test">
                  Menu Item 2
                </PopoverMenuItem>
                <PopoverMenuItem id="menu-item-3" testId="menu-item-3-test">
                  Menu Item 3
                </PopoverMenuItem>
              </PopoverMenu>
            </div>
          </>
        </Omnibar>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Page content blurs above Omnibar when scrolling"
    play={async () => {
      const scrollableElement = screen.getByTestId('example-container-div');
      scrollableElement.scrollBy(0, 200);
    }}
  >
    {(args) => {
      return (
        <div data-testid="example-container-div" style={{ width: '100%', height: '300px', overflow: 'auto' }}>
          <Omnibar id={args.id} testId={args.testId}>
            <>
              <div style={styles.omnibarStartSection}>
                <Avatar id="avatar" testId="avatar-test" size="lg" src={userTaro} />
                <div aria-live="polite" style={styles.omnibarTitleContainer}>
                  <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
                    Omnibar Title
                  </h1>
                  <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
                    Subtitle
                  </p>
                </div>
              </div>
              <div style={styles.omnibarEndSection}>
                <IconButton
                  id="refresh-button"
                  testId="refresh-button-test"
                  ariaLabel="Refresh"
                  iconName="refresh"
                  variant="tertiaryNeutral"
                  size="large"
                  onClick={() => {}}
                />
                <IconButton
                  id="print-button"
                  testId="print-button-test"
                  ariaLabel="Print"
                  iconName="print"
                  variant="tertiaryNeutral"
                  size="large"
                  onClick={() => {}}
                />
              </div>
            </>
          </Omnibar>
          <div
            style={{
              position: 'relative',
              margin: '56px auto',
              width: '50%',
              height: '600px',
              backgroundImage: `url(${place})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          />
        </div>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Omnibar appears over page content"
    play={async () => {
      const scrollableElement = screen.getByTestId('example-container-div');
      scrollableElement.scrollBy(0, 100);
    }}
  >
    {(args) => {
      return (
        <div data-testid="example-container-div" style={{ width: '100%', height: '500px', overflow: 'auto' }}>
          <Omnibar id={args.id} testId={args.testId}>
            <>
              <div style={styles.omnibarStartSection}>
                <Avatar id="avatar" testId="avatar-test" size="lg" src={userTaro} />
                <div aria-live="polite" style={styles.omnibarTitleContainer}>
                  <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
                    Omnibar Title
                  </h1>
                  <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
                    Subtitle
                  </p>
                </div>
              </div>
              <div style={styles.omnibarEndSection}>
                <IconButton
                  id="refresh-button"
                  testId="refresh-button-test"
                  ariaLabel="Refresh"
                  iconName="refresh"
                  variant="tertiaryNeutral"
                  size="large"
                  onClick={() => {}}
                />
                <IconButton
                  id="print-button"
                  testId="print-button-test"
                  ariaLabel="Print"
                  iconName="print"
                  variant="tertiaryNeutral"
                  size="large"
                  onClick={() => {}}
                />
              </div>
            </>
          </Omnibar>
          <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
            <div style={{ width: '18.75rem', margin: '2rem', marginBottom: '500px' }}>
              <Card testId={'card-test-id'} id={'card-id'} ariaLabel={'Mount Everest'}>
                <Card.Media id="card-with-children-media">
                  <Image
                    id="card-with-children-img"
                    alt={'Mount Everest during sunset'}
                    title={'A mountain'}
                    src={'images/image-sample-1.jpg'}
                  />
                </Card.Media>
                <Card.Content id="card-with-children-content">
                  <Card.Header
                    id="card-with-children-header"
                    title="Mount Everest"
                    description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    action={
                      <PopoverMenu id="sunset-menu" buttonAriaLabel="sunset">
                        <PopoverMenuItem id="sunset-new">New</PopoverMenuItem>
                        <PopoverMenuItem id="sunset-edit">Edit</PopoverMenuItem>
                        <PopoverMenuItem id="sunset-delete">Delete</PopoverMenuItem>
                      </PopoverMenu>
                    }
                  />
                  <Card.Metadata id="card-with-children-meta">
                    <TagGroup label="Category">
                      <Tag label="Mountains" />
                      <Tag label="Mount" />
                      <Tag label="Everest" />
                      <Tag label="Beautiful" />
                      <Tag label="Scenic" />
                    </TagGroup>
                  </Card.Metadata>
                  <Card.Body id="card-with-children-body">
                    <Avatar id="card-with-children-avatar" size="md" src={mountain} title="avatar" />
                  </Card.Body>
                  <Card.FooterAction id="card-with-children-footer">
                    <Button id="card-with-children-view-btn" label="View" />
                  </Card.FooterAction>
                </Card.Content>
              </Card>
            </div>
          </div>
        </div>
      );
    }}
  </Story>
</Canvas>
