import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import userEvent from '@testing-library/user-event';

import { Omnibar, IOmnibarProps } from './omnibar';
import { Button } from '../button';

describe('[Omnibar]', () => {
  const getOmnibarWrapper = () => screen.getByTestId('omnibar-test-id-wrapper');

  const mockProps: IOmnibarProps = {
    id: 'omnibar',
    testId: 'omnibar-test-id',
    startSection: (
      <div>
        <Button id="start-button" testId="start-button" label="Start Button" />
        <div>Start 1</div>
        <div>Start 2</div>
      </div>
    ),
    endSection: (
      <div>
        <div>End 1</div>
        <div>End 2</div>
        <Button id="end-button" testId="end-button" label="End Button" />
      </div>
    ),
  };

  it('renders with the correct testId', () => {
    render(<Omnibar {...mockProps} />);
    expect(getOmnibarWrapper()).toBeInTheDocument();

    const section = screen.getByTestId('omnibar-test-id-section-container');
    expect(section).toBeInTheDocument();
  });

  it('renders the startSection elements', () => {
    render(<Omnibar {...mockProps} />);
    expect(screen.getByRole('button', { name: 'Start Button' })).toBeInTheDocument();
    expect(screen.getByText('Start 1')).toBeInTheDocument();
    expect(screen.getByText('Start 2')).toBeInTheDocument();
  });

  it('renders the endSection elements', () => {
    render(<Omnibar {...mockProps} />);
    expect(screen.getByText('End 1')).toBeInTheDocument();
    expect(screen.getByText('End 2')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'End Button' })).toBeInTheDocument();
  });

  it('renders startSection button and triggers click event', async () => {
    const handleClick = jest.fn();
    const propsWithClick = {
      ...mockProps,
      startSection: (
        <div>
          <Button id="start-button" testId="start-button" label="Start Button" onClick={handleClick} />
          <div>Start 1</div>,<div>Start 2</div>
        </div>
      ),
    };
    render(<Omnibar {...propsWithClick} />);
    await userEvent.click(screen.getByRole('button', { name: 'Start Button' }));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
