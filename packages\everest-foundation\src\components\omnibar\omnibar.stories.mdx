import { ArgsT<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/addon-docs';
import { Omnibar } from './omnibar';
import { Button } from '../button';
import { IconButton } from '../icon-button';
import { PopoverMenu, PopoverMenuItem } from '../popover-menu';
import Examples from './omnibar.examples.mdx';
import styles from './omnibar.module.scss';
import place from '../../../assets/images/place.jpg';

<Meta
  title="Patterns/Omnibar"
  component={Omnibar}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/f98Ur4FSqNjE2STjlCzB87/Omnibar-(Sub-Header)?node-id=7283-14912&p=f&t=5o8iPIDiVPZ5vpo6-0',
    },
  }}
  args={{
    id: 'omnibar',
    testId: 'omnibar-test-id',
  }}
/>

# Omnibar

<Examples />

## Live Demo

<Canvas>
  <Story name="Omnibar">
    {(args) => {
      const styles = {
        omnibarStartSection: {
          display: 'flex',
          alignItems: 'center',
          gap: 'var(--evr-spacing-sm)',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          padding: 'calc(var(--evr-focus-ring-border) * 2)',
          flex: '1 1 auto',
        },
        omnibarEndSection: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          gap: 'var(--evr-spacing-sm)',
          overflow: 'hidden',
          marginInlineStart: 'var(--evr-spacing-lg)',
          padding: 'calc(var(--evr-focus-ring-border) * 2)',
          maxWidth: 'calc(100% - var(--evr-spacing-lg))',
          flex: '1 0 auto',
        },
        omnibarTitleContainer: {
          overflow: 'hidden',
        },
        omnibarTitleText: {
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        omnibarUndoRedoContainer: {
          display: 'flex',
          alignItems: 'center',
          gap: 'var(--evr-spacing-2xs)',
        },
        omnibarExampleStyle: {
          height: '300px',
          overflowY: 'auto',
        },
        omnibarExampleImageStyle: {
          margin: '56px auto',
          width: '50%',
          height: '600px',
          backgroundImage: `url(${place})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        },
      };
      return (
        <div style={styles.omnibarExampleStyle}>
          <Omnibar {...args}>
            <div style={styles.omnibarStartSection}>
              <IconButton
                id="back-button"
                testId="back-button-test"
                ariaLabel="Back"
                iconName="arrowLeft"
                variant="secondaryNeutral"
                size="large"
                onClick={() => {}}
              />
              <div aria-live="polite" style={styles.omnibarTitleContainer}>
                <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
                  Edit time
                </h1>
                <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
                  Last Modified 1 day ago
                </p>
              </div>
            </div>
            <div style={styles.omnibarEndSection}>
              <div style={styles.omnibarUndoRedoContainer}>
                <IconButton
                  id="undo-button"
                  testId="undo-button-test"
                  ariaLabel="Undo"
                  iconName="undo"
                  variant="tertiaryNeutral"
                  size="large"
                  onClick={() => {}}
                />
                <IconButton
                  id="redo-button"
                  testId="redo-button-test"
                  ariaLabel="Redo"
                  iconName="redo"
                  variant="tertiaryNeutral"
                  size="large"
                  onClick={() => {}}
                />
              </div>
              <Button
                id="save-button"
                testId="save-button-test"
                label="Save"
                ariaLabel="Save"
                size="large"
                variant="primary"
                onClick={() => {}}
              />
              <PopoverMenu
                id="overflow"
                testId="overflow-test"
                buttonAriaLabel="Actions"
                triggerOption="iconButton"
                triggerProps={{
                  variant: 'tertiaryNeutral',
                  startIcon: 'moreVert',
                  size: 'large',
                }}
              >
                <PopoverMenuItem id="menu-item-1" testId="menu-item-1-test" ariaLabel="Menu Item 1">
                  Menu Item 1
                </PopoverMenuItem>
                <PopoverMenuItem id="menu-item-2" testId="menu-item-2-test" ariaLabel="Menu Item 2">
                  Menu Item 2
                </PopoverMenuItem>
                <PopoverMenuItem id="menu-item-3" testId="menu-item-3-test" ariaLabel="Menu Item 3">
                  Menu Item 3
                </PopoverMenuItem>
              </PopoverMenu>
            </div>
          </Omnibar>
          <div style={styles.omnibarExampleImageStyle}></div>
        </div>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Omnibar" />
