import React, { forwardRef, PropsWithChildren, ReactNode } from 'react';

import styles from './omnibar.module.scss';

export interface IOmnibarProps {
  /**
   * Id for the `Omnibar`.
   */
  id: string;
  /**
   * Sets data-testid.
   */
  testId?: string;
  /**
   * Elements to be rendered at the start of the Omnibar.
   */
  startSection?: ReactNode;
  /**
   * Elements to be rendered at the end of the Omnibar.
   */
  endSection?: ReactNode;
}

export const Omnibar = forwardRef<HTMLDivElement, PropsWithChildren<IOmnibarProps>>((props, ref) => {
  const { id, testId, children, startSection, endSection } = props;

  return (
    <div
      id={`${id}-wrapper`}
      data-testid={testId ? `${testId}-wrapper` : undefined}
      ref={ref}
      className={styles.evrOmnibar}
    >
      <section
        id={`${id}-section-container`}
        data-testid={testId ? `${testId}-section-container` : undefined}
        className={styles.evrOmnibarSectionContainer}
      >
        {startSection}
        {endSection}
        {children}
      </section>
    </div>
  );
});

Omnibar.displayName = 'Omnibar';
