import React, { CSSProperties, MouseEvent, PropsWithChildren, forwardRef, useRef } from 'react';
import classNames from 'classnames';

import styles from './light-box.module.scss';

export type TLightBoxVariant = 'dark' | 'clear';

export interface ILightBox {
  id: string;
  variant?: TLightBoxVariant;
  style?: CSSProperties;
  className?: string;
  onClick?: (e: MouseEvent) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLDivElement>) => void;
  fullscreen?: boolean;
  testId?: string;
}

export const LightBox = forwardRef<HTMLDivElement, PropsWithChildren<ILightBox>>((props, ref): JSX.Element => {
  const { id, variant = 'dark', onClick, onKeyDown, style, className, testId, children, fullscreen } = props;

  const mouseDownTarget = useRef<EventTarget | null>(null);
  const handleClick = (e: MouseEvent) => {
    // Compare mouseDown target with mouseUp target, trigger onClick only if both targets are the same
    if (mouseDownTarget.current === e.target) {
      onClick?.(e);
    }
  };
  const handleMouseDown = (e: MouseEvent) => {
    // Set intial mouse down location
    mouseDownTarget.current = e.target;
  };

  return (
    <div
      id={id}
      data-testid={testId}
      className={classNames([
        styles.evrLightBox,
        {
          [styles.clear]: variant === 'clear',
          [styles.dark]: variant === 'dark',
        },
        className,
      ])}
      style={{ ...style, position: 'fixed' }}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      onKeyDown={onKeyDown}
      ref={ref}
      data-evr-light-box={fullscreen ? 'fullscreen' : undefined}
    >
      {children}
    </div>
  );
});

LightBox.displayName = 'LightBox';
