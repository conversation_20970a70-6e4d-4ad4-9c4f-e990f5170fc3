import React, { useEffect, useState, PropsWithChildren, CSSProperties, useRef, forwardRef } from 'react';
import { Portal } from '@ceridianhcm/everest-cdk';

import { TLightBoxVariant, LightBox } from './light-box';

export interface IOverlayBase {
  id: string;
  open?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  onLightBoxClick?: (e: React.MouseEvent<Element, MouseEvent>) => void;
  onLightBoxKeyDown?: (e: React.KeyboardEvent) => void;
  lightBoxVariant?: TLightBoxVariant;
  fullscreen?: boolean;
  style?: CSSProperties;
  className?: string;
  testId?: string;
}

export const OverlayBase = forwardRef<HTMLDivElement, PropsWithChildren<IOverlayBase>>((props, ref) => {
  const {
    id,
    open,
    onOpen,
    onClose,
    onLightBoxClick,
    onLightBoxKeyDown,
    lightBoxVariant,
    style,
    fullscreen,
    testId,
    children,
    className,
  } = props;
  const [isVisible, setIsVisible] = useState(false);
  const fullscreenSizePosition = useRef({
    top: '0',
    bottom: '0',
    left: '0',
    right: '0',
  });
  useEffect(() => {
    if (typeof open === 'boolean') {
      if (open && !isVisible) {
        setIsVisible(true);
      } else if (!open && isVisible) {
        onClose?.();
        setIsVisible(false);
      } else if (open && isVisible) {
        onOpen?.();
      }
    }
  }, [open, isVisible, onOpen, onClose]);

  return (
    <>
      {isVisible && (
        <Portal id={id} portalContainerId={`${id}-portal-container`} zIndexCategory="modal">
          <LightBox
            id={`${id}-light-box`}
            variant={lightBoxVariant}
            className={className}
            style={{
              ...style,
              ...(fullscreen ? fullscreenSizePosition.current : {}),
            }}
            onClick={onLightBoxClick}
            onKeyDown={onLightBoxKeyDown}
            testId={testId ? `${testId}-light-box` : undefined}
            ref={ref}
            fullscreen={fullscreen}
          >
            {children}
          </LightBox>
        </Portal>
      )}
    </>
  );
});

OverlayBase.displayName = 'OverlayBase';
