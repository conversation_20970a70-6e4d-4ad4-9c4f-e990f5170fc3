import React from 'react';
import { render } from '@testing-library/react';

import { Button } from '../button';

import { OverlayBody } from '.';

const modalBodyTestId = 'modal-body-test-id';
const buttonTestId = 'button-test-id';

describe('OverlayBody', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('can render any child component, for example Button', () => {
    const { getByTestId } = render(
      <OverlayBody id="modal-body-id" testId={modalBodyTestId}>
        <Button id="button-id" testId={buttonTestId} label="button-label"></Button>
      </OverlayBody>
    );
    expect(getByTestId(modalBodyTestId)).toBeInTheDocument();
    expect(getByTestId(buttonTestId)).toBeInTheDocument();
  });
});
