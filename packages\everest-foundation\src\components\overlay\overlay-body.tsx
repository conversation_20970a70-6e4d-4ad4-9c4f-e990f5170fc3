import React, { PropsWithChildren } from 'react';

import styles from './overlay.module.scss';

export interface IOverlayBody {
  id: string;
  testId?: string;
}

export const OverlayBody = React.forwardRef<HTMLElement, PropsWithChildren<IOverlayBody>>((props, ref): JSX.Element => {
  const { id, testId, children } = props;

  return (
    <section id={id} ref={ref} data-testid={testId} className={styles.evrOverlayBody}>
      {children}
    </section>
  );
});

OverlayBody.displayName = 'OverlayBody';
