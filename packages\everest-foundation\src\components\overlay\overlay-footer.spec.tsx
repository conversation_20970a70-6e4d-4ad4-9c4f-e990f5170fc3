import React from 'react';
import { render } from '@testing-library/react';

import { OverlayFooter } from '.';

const overlayFooterTestId = 'overlay-footer-test-id';

describe('OverlayFooter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render any child comopnent, example <p>', () => {
    const { getByTestId } = render(
      <OverlayFooter id="modal-footer-id" testId={overlayFooterTestId}>
        <p>child component</p>
      </OverlayFooter>
    );
    expect(getByTestId(overlayFooterTestId).firstChild).toHaveTextContent('child component');
  });
});
