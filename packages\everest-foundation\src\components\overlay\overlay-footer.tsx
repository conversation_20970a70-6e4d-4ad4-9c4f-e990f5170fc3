import React, { PropsWithChildren } from 'react';

import styles from './overlay.module.scss';

export interface IOverlayFooter {
  id: string;
  testId?: string;
}

export const OverlayFooter = React.forwardRef<HTMLElement, PropsWithChildren<IOverlayFooter>>(
  (props, ref): JSX.Element => {
    const { id, testId, children } = props;

    return (
      <section id={id} ref={ref} data-testid={testId} className={styles.evrOverlayFooter}>
        {children}
      </section>
    );
  }
);

OverlayFooter.displayName = 'OverlayFooter';
