import React, { PropsWithChildren } from 'react';

import { mergeRefs, useCreateTestId } from '../../utils';
import { IconButton } from '../icon-button';

import styles from './overlay.module.scss';

export interface IOverlayHeader {
  id: string;
  testId?: string;
  onCloseButtonClick?: (e: React.MouseEvent<HTMLElement>) => void;
  closeButtonAriaLabel?: string;
}

export const OverlayHeader = React.forwardRef<HTMLElement, PropsWithChildren<IOverlayHeader>>((props, ref) => {
  const { id, testId, closeButtonAriaLabel, onCloseButtonClick, children } = props;
  const dataRef = useCreateTestId(testId);

  const handleCloseButtonKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      dataRef.current.children[1] && dataRef.current.children[1].click();
    }
  };

  const handleCloseButtonClick = (e: React.MouseEvent<HTMLElement>) => {
    e.preventDefault();
    onCloseButtonClick?.(e);
  };

  return (
    <section id={id} ref={mergeRefs([dataRef, ref])} className={styles.evrOverlayHeader}>
      <div id={`${id}-header-title`} data-testid={testId && `${testId}-header-title`} className={styles.title}>
        {children}
      </div>
      {onCloseButtonClick && closeButtonAriaLabel && (
        <IconButton
          id={`${id}-header-close-button`}
          testId={testId && `${testId}-header-close-button`}
          onKeyDown={handleCloseButtonKeyDown}
          onClick={handleCloseButtonClick}
          iconName="x"
          variant="tertiaryNeutral"
          ariaLabel={closeButtonAriaLabel}
        />
      )}
    </section>
  );
});

OverlayHeader.displayName = 'OverlayHeader';
