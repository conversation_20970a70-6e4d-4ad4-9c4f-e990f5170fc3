import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { Button } from '../button';
import { Overlay } from './overlay';

export const scope = {
  Button,
  Overlay,
};

The `Overlay` is a positionable container expected to be used when building dialog components such as `Modal` or `Toast`.

The intended audience is Everest developers and not feature teams or other consumers.

## Using Overlay

Wrap the contents of the dialog with the `Overlay` and open or close it using the `open` prop.

The `Overlay` uses fixed positioning with dimensions and offset defined by the `style` prop.

A full screen toggle can also be applied with the `fullscreen` prop, and makes the light box occupy the entire viewport.

You may want to consider using <LinkTo kind="Toolbox/Focus Trap">Focus Trap</LinkTo> in combination with `Overlay`.

### Example

Click the below button to make a full screen modal-type dialog appear.

export const defaultOverlayCode = `
  () => {
    const [open, setOpen] = React.useState(false);
    const hideScrollBar = (isHidden) => {
      document.documentElement.style.overflow = isHidden ? 'hidden' : 'auto';
      document.documentElement.style.paddingRight = isHidden
        ? '15px'
        : '0';
    }
    return (
      <>
        <Button
          id='clickp-to-open-btn'
          label="Click to open"
          onClick={(e) => {
            setOpen((prev) => !prev);
          }}
        />
        <Overlay
          open={open}
          id="overlay-example-modal"          
          onClose={() => {
            hideScrollBar(false);
          }}
          onLightBoxClick={(e) => {
            setOpen(false);
          }}
          onOpen={() => {            
            hideScrollBar(true);
          }}
          style={{ overflow: 'hidden' }}
          fullscreen
        >
          <div
            style={{
              position: 'relative',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%,-50%)',
              height: '5rem',
              width: '20rem',
              backgroundColor: 'white',
              borderRadius: '0.75rem',    
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onClick={(e) => {
              e.stopPropagation();
              setOpen(false);
            }}
            className="evrBodyText"
          >
            Click anywhere to close
          </div>
        </Overlay>
      </>
    );
  }
`;

<CodeExample scope={scope} code={defaultOverlayCode} />
