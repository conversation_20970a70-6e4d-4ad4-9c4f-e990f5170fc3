# Overlay

## Summary

Research and document implementations for the Everest Overlay.

- Start Date: 2022-11-11
- Figma link: https://www.figma.com/file/hs65bgSZxdhJvnhm09AmJT/%F0%9F%A7%AA-Overlays?node-id=44%3A50819 (WIP at time of writing)

## Detailed Design

The Overlay is intened to provide a positioned container intended to be populated with components such as Modal or Toast.

The intended audience for this component is Everest developers.

## API

1. **id**: `string`  
   Sets the `id` attribute on `Overlay` html elements. The portal container is assigned id `${id}-portal-container`, the light box is assigned id `${id}-light-box`.
1. **open?**: `boolean`  
   Optional. Opens/closes the region.
1. **onOpen?**: `() => void`  
   Optional. Callback that runs when the region opens.
1. **onClose?**: `() => void`  
   Optional. Callback that runs when the region closes.
1. **onLightBoxClick?**: `(e: MouseEvent) => void`
   Optional. Callback when the light box is clicked.
1. **lightBoxVariant?** : `TLightBoxVariant`
   Optional. Styling variants for the light box. Default is a `dark` shade of grey, `clear` provides a transparent light box.
1. **testId?**: `string`  
   Optional. Sets `data-testid` attribute on the `LightBox` to `${testId}-light-box`.
1. **fullscreen?**: `boolean`  
   Optional. Enable or disable a fullscreen break.
1. **style?**: `CSSProperties`  
   Optional. Styling to position and size the overlay.

## Usage

A simple example is as follows.

```
  const [open, setOpen] = useState(true);
  //
  return (
    <>
      <Button
        id='click-to-open-close-btn'
        label="Click to open/close"
        onClick={(e) => {
          action('onClickButton')(e);
          setOpen((prev) => !prev);
        }}
      />
      <Overlay
        open={open}
        id="overlay-example"
        style={{
          width: '300px',
          top: '3rem',
          left: 'calc(100% - 300px)',
        }}
        lightBoxVariant="clear"
        onClose={() => {
          console.log('onClose');
        }}
        onLightBoxClick={(e) => {
          action('onLightBoxClick')(e);
          setOpen(false);
        }}
        onOpen={() => {
          console.log('onOpen');
        }}
        // fullscreen
      >
        <div
          style={{
            backgroundColor: 'white',
            margin: '1rem',
            padding: '0.5rem',
            height: '4rem',
            borderRadius: '0.75rem',
            boxShadow: '5px 5px 5px 5px rgba(85,85,85,0.2)',
          }}
          onClick={(e) => {
            e.stopPropagation();
            action('onClickChild')(e);
            setOpen(false);
          }}
          className="evrBodyText1"
        >
          Greetings Humans
        </div>
      </Overlay>
    </>
  );
```

## Accessibility

Accessibility concerns will be addressed in those components built upon `Overlay`.

## Other Design Systems

Below are examples of components that could use this component in their implementation.

**Material UI**

- https://mui.com/material-ui/react-dialog/#optional-sizes

- https://mui.com/material-ui/react-snackbar/#simple-snackbars

**Ant Design**

- https://ant.design/components/modal/

**Polaris**

- https://polaris.shopify.com/components/modal

- https://polaris.shopify.com/components/toast

**Carbon**

- https://react.carbondesignsystem.com/?path=/docs/components-modal--default

## Required PBIs

- Refine component, tests and story

## Changelog

10/11/2023: EDS-3564 - added a condition to render the close IconButton only when 'onCloseButtonClick' and 'closeButtonAriaLabel' are passed. Also, created a new custom event 'IDialogCloseEvent' which returns 'TDialogCloseEventReason' to handle the escapeKeyDown.
