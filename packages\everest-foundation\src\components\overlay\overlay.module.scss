@use '../../variables.scss';

.evrOverlayHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    margin-inline-end: var(--evr-spacing-2xs);
    width: 100%;
  }
}

.evrOverlayBody {
  flex: 0 1 auto;
}

.evrOverlayHeader,
.evrOverlayFooter {
  align-items: center;
  justify-content: space-between;
  flex: 0 1 auto;
}

.evrOverlayFooter {
  position: relative;
}
