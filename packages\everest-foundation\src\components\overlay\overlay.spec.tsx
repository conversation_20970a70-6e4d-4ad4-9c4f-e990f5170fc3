import React, { CSSProperties } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { TLightBoxVariant } from './light-box';
import { Overlay, IOverlay } from './overlay';

const testId = 'overlay-test-id';
const lightBoxTestId = `${testId}-light-box`;
const id = 'overlay-id';
const onOpen = jest.fn();
const onClose = jest.fn();
const onLightBoxClick = jest.fn();
const onLightBoxKeyDown = jest.fn();
const lightBoxVariant = 'clear' as TLightBoxVariant;
const testText = 'Test text';

const propsMock: IOverlay = {
  id,
  testId,
  open: true,
  onOpen,
  onClose,
  onLightBoxClick,
  onLightBoxKeyDown,
  lightBoxVariant,
  fullscreen: false,
  style: {
    height: '100px',
    width: '100px',
    top: '10px',
    left: '10px',
  } as CSSProperties,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const TestComponent = (props: any) => (
  <Overlay {...propsMock} {...(props ? props : {})}>
    <div tabIndex={-1}>{testText}</div>
  </Overlay>
);

describe('[Overlay]', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('should render when open is true', () => {
    render(<TestComponent />);
    screen.getByText(testText);
  });

  it('should not render when open is false or undefined', () => {
    const { rerender } = render(<TestComponent open={false} />);
    expect(screen.queryByText(testText)).toBeNull();

    rerender(<TestComponent open={undefined} />);
    expect(screen.queryByText(testText)).toBeNull();
  });

  it('should call onOpen when region is opened', () => {
    render(<TestComponent />);
    expect(propsMock.onOpen).toHaveBeenCalledTimes(1);
  });

  it('should call onClose when region is closed', () => {
    const { rerender } = render(<TestComponent />);
    rerender(<TestComponent open={false} />);
    expect(propsMock.onClose).toHaveBeenCalledTimes(1);
  });

  it('should call onLightBoxClick when light box is clicked', async () => {
    render(<Overlay {...propsMock} />);

    await userEvent.click(screen.getByTestId(lightBoxTestId));
    expect(propsMock.onLightBoxClick).toHaveBeenCalledTimes(1);
  });

  it('should call onLightBoxKeyDown when the content is focused and a key is pressed', async () => {
    render(<TestComponent />);
    const content = screen.getByText(testText);
    content.focus();

    await userEvent.keyboard(' ');
    expect(propsMock.onLightBoxKeyDown).toHaveBeenCalledTimes(1);
  });

  it('should have light box with specified style', () => {
    // verifying that an arbitrary style when passed is inline
    render(<TestComponent />);

    const lightBox = screen.getByTestId(lightBoxTestId);
    expect(lightBox.style.height).toBe('100px');
    expect(lightBox.style.width).toBe('100px');
    expect(lightBox.style.top).toBe('10px');
    expect(lightBox.style.left).toBe('10px');
  });

  it('should be fullscreen when specified', () => {
    render(<TestComponent style={{ top: '20px', left: '20px' }} fullscreen={true} />);

    // verifying that relative offsets and height/width are inline
    const lightBox = screen.getByTestId(lightBoxTestId);
    expect(lightBox.style.top).toBe('0px');
    expect(lightBox.style.bottom).toBe('0px');
    expect(lightBox.style.left).toBe('0px');
    expect(lightBox.style.right).toBe('0px');
  });

  it('should have light box variant "dark" by default', () => {
    render(<TestComponent lightBoxVariant={undefined} />);
    const lightBox = screen.getByTestId(lightBoxTestId);
    expect(lightBox.classList.contains('dark')).toBe(true);
  });

  it('should have light box variant "clear" when specified', () => {
    render(<TestComponent lightBoxVariant={'clear'} />);
    const lightBox = screen.getByTestId(lightBoxTestId);
    expect(lightBox.classList.contains('clear')).toBe(true);
  });
});
