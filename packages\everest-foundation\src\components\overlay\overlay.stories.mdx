import { useState } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Overlay } from './overlay';
import { Button } from '../button';
import { action } from '@storybook/addon-actions';
import Examples from './overlay.examples.mdx';
import { useArgs } from '@storybook/client-api';

<Meta
  title="Toolbox/Overlay"
  component={Overlay}
  parameters={{
    controls: {
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description:
        'Sets the `id` attribute on `Overlay` html elements. The portal container is assigned id `${id}-portal-container`, and the light box is assigned id `${id}-light-box`.',
    },
    open: {
      description: 'Opens or closes the overlay.',
      table: {
        defaultValue: { summary: false },
      },
    },
    onOpen: {
      control: '-',
      description: 'Callback that runs when the overlay opens.',
    },
    onClose: {
      control: '-',
      description: 'Callback that runs when the overlay closes.',
    },
    onLightBoxClick: {
      control: '-',
      description: 'Callback when the light box is clicked.',
    },
    onLightBoxKeyDown: {
      control: '-',
      description: 'Callback when a key is pressed that bubbles up to the light box.',
    },
    lightBoxVariant: {
      type: 'enum',
      control: 'radio',
      options: ['clear', 'dark'],
      description:
        'Styling variants for the light box. Default is a `dark` shade of grey, `clear` provides a transparent light box.',
      table: {
        defaultValue: { summary: 'dark' },
      },
    },
    testId: {
      control: 'text',
      description: 'Sets `data-testid` attribute on the light box.',
    },
    fullscreen: {
      description: 'Enable or disable a fullscreen break. When enabled, the light box fills the entire viewport.',
      table: {
        defaultValue: { summary: false },
      },
    },
    style: {
      description: 'Styling to position and size the light box.',
    },
  }}
  args={{
    id: 'overlay-example-floating',
    open: false,
    lightBoxVariant: 'clear',
    style: {
      width: '18.75rem',
      top: 'calc(50% - 4rem)',
      left: 'calc(100% - 18.75rem)',
    },
    fullscreen: false,
  }}
/>

# Overlay

<Examples />

## Live Demo

<Canvas>
  <Story name="Overlay">
    {(args) => {
      const [open, setOpen] = useArgs();
      const hideScrollBar = (isHidden) => {
        document.documentElement.style.overflow = isHidden ? 'hidden' : 'auto';
        document.documentElement.style.paddingRight = isHidden && args.lightBoxVariant === 'dark' ? '15px' : '0';
      };
      return (
        <div style={{ display: 'flex', justifyContent: 'center ' }}>
          <Button
            id="click-open-close-btn"
            label="Click to open or close"
            onClick={(e) => {
              action('onClickButton')(e);
              setOpen({ open: !args.open });
            }}
          />
          <Overlay
            open={args.open}
            id={args.id}
            style={{
              ...args.style,
              ...(args.fullscreen
                ? {
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: undefined,
                  }
                : {}),
            }}
            lightBoxVariant={args.lightBoxVariant}
            onClose={() => {
              action('onClose');
              hideScrollBar(false);
            }}
            onLightBoxClick={(e) => {
              action('onLightBoxClick')(e);
            }}
            onLightBoxKeyDown={(e) => {
              action('onLightBoxKeyDown')(e);
            }}
            onOpen={() => {
              action('onOpen');
              hideScrollBar(args.fullscreen && args.lightBoxVariant === 'dark' ? true : false);
            }}
            fullscreen={args.fullscreen}
          >
            <div
              style={{
                backgroundColor: 'white',
                margin: '1rem',
                padding: '0.5rem',
                height: '4rem',
                borderRadius: '0.75rem',
                border: 'solid #0087FD 0.125rem',
                boxShadow: '5px 5px 5px 5px rgba(85,85,85,0.2)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                textAlign: 'center',
                minWidth: '5rem',
              }}
              onClick={(e) => {
                e.stopPropagation();
                action('onClickChild')(e);
                setOpen({ open: false });
              }}
              className="evrBodyText"
              tabIndex={0}
            >
              Click me
              {args.fullscreen ? (
                ' to close'
              ) : (
                <>
                  {' '}
                  <br />
                  or the button to close
                </>
              )}
            </div>
          </Overlay>
        </div>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Overlay" />
