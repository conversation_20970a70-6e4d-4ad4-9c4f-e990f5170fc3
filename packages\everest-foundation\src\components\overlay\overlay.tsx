import React, { PropsWithChildren, forwardRef } from 'react';

import { IOverlayBase, OverlayBase } from './overlay-base';

type OverlayBaseOmitted = 'windowSize';
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IOverlay extends Omit<IOverlayBase, OverlayBaseOmitted> {}

export const Overlay = forwardRef<HTMLDivElement, PropsWithChildren<IOverlay>>((props, ref) => {
  const {
    id,
    open,
    onOpen,
    onClose,
    onLightBoxClick,
    onLightBoxKeyDown,
    lightBoxVariant,
    style,
    fullscreen,
    testId,
    children,
    className,
  } = props;

  const overlayProps = {
    id,
    open,
    onOpen,
    onClose,
    onLightBoxClick,
    onLightBoxKeyDown,
    lightBoxVariant,
    style,
    fullscreen,
    testId,
    children,
    className,
  };
  return <OverlayBase {...overlayProps} ref={ref} />;
});

Overlay.displayName = 'Overlay';
