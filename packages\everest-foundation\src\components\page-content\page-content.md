# PageContent

- Start Date: 2025-04-16
- Figma Link: https://www.figma.com/file/GlFJ5NBAdplnrLXTKtLHCI/Page-Templates?node-id=3922-8993&m=dev
- Epic: https://dayforce.atlassian.net/browse/PWEB-20165

## Summary

PageContent is a foundational layout component that provides consistent spacing and structure for page content. It handles responsive padding, maximum width constraints, and proper overflow behavior.

## Detailed Design

PageContent is a single container component that:

- Handles content overflow with automatic scrolling
- Provides responsive padding based on viewport width
- Enforces maximum width constraints for optimal readability
- Supports custom class names for additional styling

## API

### IPageContentProps

1. **id**: `string`
   Sets `id` attribute.
2. **testId**: `string`
   Optional. Sets `data-testid` attribute.
3. **className**: `string`
   Optional. Sets custom class name to allow for additional styling.
4. **size**: `TPageContentSize`
   Sets the size of the component.

```typescript
export type TPageContentSize = 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
```

## Implementation

```tsx
export interface IPageContentProps {
  id: string;
  testId?: string;
  className?: string;
  size: TPageContentSize;
}

export const PageContent = forwardRef<HTMLDivElement, PropsWithChildren<IPageContentProps>>((props, ref) => {
  const { id, testId, className, children, size } = props;

  return (
    <div
      ref={ref}
      id={id}
      className={(classnamesstyles.evrPageContent, styles[`evrPageContent${size.toUpperCase()}`], className)}
      data-testid={testId}
    >
      {children}
    </div>
  );
});
```

## Usage

```tsx
import { PageContent } from '@ceridianhcm/components';

const { breakpoint } = useEverestContext();
const [size, setSize] = useState('md');
useEffect(() => {
  switch (breakpoint) {
    case 'sm':
      setSize('sm');
      break;
    case 'md':
      setSize('md');
      break;
    case 'lg':
      setSize('lg');
      break;
    case 'xl':
      setSize('xl');
      break;
    case 'xxl':
      setSize('xxl');
      break;
    default:
      setSize('md');
  }
}, [breakpoint]);

const MyPage = () => (
  <PageContent id="my-page-id" size={size}>
    <h1 className="evrHeading1">Page Title</h1>
    <p className="evrBodyText">Page content goes here...</p>
  </PageContent>
);
```

## Styling

The component uses responsive breakpoints for proper spacing:

- Default padding: var(--evr-spacing-md)
- Small screens (>=430px): var(--evr-spacing-lg)
- Medium screens (>=768px): var(--evr-spacing-3xl)
- Large screens (>=1280px and <1440px): Fluid padding calculation between var(--evr-spacing-3xl) to var(--evr-spacing-5xl)
- Extra large screens (>=1440px and <1920px): Fluid padding calculation between var(--evr-spacing-5xl) to var(--evr-spacing-10xl)
- XXL screens (>=1920px): Fixed padding to var(--evr-spacing-10xl)

## Accessibility

- PageContent is a generic container and does not require specific ARIA roles.
- Ensure that any child components within PageContent are accessible and follow best practices.
- Use semantic HTML elements (e.g., headings, paragraphs) for better screen reader support.
- Ensure that the component is keyboard navigable and focusable if it contains interactive elements.
- Test with screen readers to ensure proper reading order and context.
- Ensure that the component is responsive and maintains accessibility on different screen sizes.

## Future Considerations

- Evaluate need for custom scroll behavior options
- Might need to evaluate for different content width constraints

## Q&A

Q: Why use fluid padding for larger screens?
A: Fluid padding is padding that scales proportionally with the size of the viewport. It provides a smoother transition between breakpoints and maintains proportional spacing as the viewport grows.

Q: Why set a maximum width?
A: Maximum width ensures optimal readability and consistent layout across large screens while preventing content from becoming too stretched.

Q: Does the PageContent include top and bottom padding?
A: No, it only provides side paddings.

Q. In the Figma designs, Omnibar is shown above the page content. Is it part of the PageContent?
A: No, Omnibar is not included in PageContent.
