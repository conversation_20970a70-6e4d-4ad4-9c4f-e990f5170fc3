import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/addon-docs';
import Examples from './record-list.examples.mdx';
import { SAMPLE_DATA } from './table_demo_data';
import { Button } from '../button';
import { Checkbox } from '../checkbox';
import { Link } from '../link';
import { PopoverMenu, PopoverMenuItem } from '../popover-menu';
import { Popover, PopoverBody, PopoverFooter } from '../popover';
import { SearchField } from '../search-field';
import { Table, TableToolbar, TableBody, TablePagination, TableCellText } from '../table';
import { useEverestContext } from '../everest-provider';
import { useState, useRef, useMemo, useEffect } from 'react';

export const scope = {
  Button,
  Checkbox,
  Link,
  Popover,
  PopoverMenuItem,
  PopoverMenu,
  PopoverBody,
  PopoverFooter,
  SearchField,
  Table,
  TableToolbar,
  TableBody,
  TablePagination,
  TableCellText,
  useEverestContext,
  SAMPLE_DATA,
};

<Meta title="Layout/Page Layout/Record List" />

# Record List

<Examples />

export const RecordList = () =>{
    const styles = {
        headerBar: {
            display: 'flex',
            padding: 'var(--evr-spacing-md) var(--evr-spacing-md) var(--evr-spacing-md) 0px',
            alignItems: 'center',
            gap: 'var(--evr-spacing-sm)',
            borderTop: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
            borderBottom: 'var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp)',
            background: 'var(--evr-surfaces-primary-hovered)',
            justifyContent: 'space-between',
        },
        headerBarText: {
            paddingInlineStart: 'var(--evr-spacing-md)',
        },
        body: {
            display: 'flex',
            padding: '0px var(--evr-spacing-md) 0px var(--evr-spacing-md)',
            flexDirection: 'column',
            alignItems: 'flex-start',
            gap: 'var(--evr-spacing-md)',
            alignSelf: 'stretch',
        },
        filterBlock: {
            display: 'flex',
            paddingBlockStart: 'var(--evr-spacing-md)',
            justifyContent: 'space-between',
            alignItems: 'center',
            alignSelf: 'stretch',
        },
        filterBlockMobile: {
            paddingBlockStart: 'var(--evr-spacing-md)',
            width: '100%',
        },
        searchFieldContainer: {
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'flex-start',
            gap: 'var(--evr-spacing-md)',
        },
        buttonGroup: {
            display: 'flex',
            gap: 'var(--evr-spacing-md)',
            alignItems: 'center',
        },
        buttonGroupMobile: {
            paddingBlockStart: 'var(--evr-spacing-md)',
        },
        checkboxContainer: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            gap: 'var(--evr-spacing-2xs)',
            alignSelf: 'stretch',
            minWidth: '240px',
            maxWidth: '480px',
            paddingBlockStart: 'var(--evr-spacing-sm)',
            paddingBlockEnd: 'var(--evr-spacing-3xs)',
        },
        popoverButtonGroupContainer: {
            display: 'flex',
            justifyContent: 'end',
            gap: 'var(--evr-spacing-2xs)',
        },
    };

    const { breakpoint } = useEverestContext();
    const isMobile = breakpoint === 'xs' || breakpoint === 'sm' || breakpoint === 'md';

    const PageOptionsPopoverMenu= () => {
        const initialState = '';
        const [selectedMenuItemId, setSelectedMenuItemId] = useState(initialState);

        function reducer(action) {
            switch (action) {
                case initialState:
                    return;
                case 'option-1':
                    console.log('You selected Option 1!');
                    break;
                case 'option-2':
                    console.log('You selected Option 2!');
                    break;
                default:
                    break;
            }
            setSelectedMenuItemId(initialState);
        }

        useEffect(() => {
            reducer(selectedMenuItemId);
        }, [selectedMenuItemId]);

        return (
            <PopoverMenu
                id="page-header-menu"
                buttonAriaLabel="Page header menu"
                onChange={({ id }) => setSelectedMenuItemId(id)}
                triggerOption="iconButton"
                triggerProps={{ variant: "tertiaryNeutral" }}
            >
                <PopoverMenuItem id="option-1">Option 1</PopoverMenuItem>
                <PopoverMenuItem id="option-2">Option 2</PopoverMenuItem>
            </PopoverMenu>
        );
    };

    const PageSearchField= () => {
        const [searchFieldValue, setSearchFieldValue] = useState('');

        return (
            <SearchField
                placeholder="Search"
                ariaLabel="Search Field"
                id="search-field"
                clearButtonAriaLabel="Clear search field value"
                onClear={() => setSearchFieldValue('')}
                value={searchFieldValue}
                onChange={(value) => setSearchFieldValue(value)}
            />
        );
    };


    const CustomizeDisplayPopover = () => {
        const triggerRefPopover = useRef(null);
        const [openPopover, setOpenPopover] = useState(false);

        const checkboxKeys = [
            'Page Name',
            'Type',
            'Status',
            'Topic',
            'Last Modified',
            'Last Modified By',
            'Reference Code',
        ];

        const initialCheckboxStates = checkboxKeys.reduce((acc, key) => {
            acc[key] = 'unchecked';
            return acc;
        }, {});

        const [checkboxStates, setCheckboxStates] = useState(initialCheckboxStates);

        const handleCheckboxChange = (label) => {
            setCheckboxStates((prevState) => ({
                ...prevState,
                [label]: prevState[label] === 'unchecked' ? 'checked' : 'unchecked',
            }));
        };

        const checkbox = (label) => {
            return (
                <Checkbox
                    id={`checkbox-${label.replace(/\\s+/g, '-').toLowerCase()}`}
                    checkedState={checkboxStates[label]}
                    label={label}
                    onChange={() => handleCheckboxChange(label)}
                />
            );
        };

        return (
            <>
                <Button
                    id="trigger-button-popover"
                    label="Customize Display"
                    ref={triggerRefPopover}
                    onClick={() => setOpenPopover(!openPopover)}
                    variant="secondaryNeutral"
                    startIcon="column"
                    size="large"
                    fullWidth={isMobile}
                />
                <Popover
                    id="popover-checkbox"
                    open={openPopover}
                    triggerRef={triggerRefPopover}
                    onClose={() => {
                        setOpenPopover(false);
                        triggerRefPopover && triggerRefPopover.current.focus();
                    }}
                    ariaLabelledBy="popover-custom-header"
                    ariaDescribedBy="popover-custom-body"
                >
                    <PopoverBody id="popover-custom-body">
                        <div style={styles.checkboxContainer}>
                            <p className="evrBodyText1 evrBold">Columns & filters to display</p>
                            {checkboxKeys.map((key) => (
                                <div key={key}>
                                    {checkbox(key)}
                                </div>
                            ))}
                        </div>
                    </PopoverBody>
                    <PopoverFooter id="popover-footer">
                        <div style={{ display: 'flex', justifyContent: 'end', gap: '8px' }}>
                            <Button id="clear-button" label="Clear" variant="tertiaryNeutral" onClick={() => setCheckboxStates(initialCheckboxStates)}></Button>
                            <Button
                                id="save-button"
                                label="Save"
                                onClick={() => {
                                    alert("Save!");
                                    setOpenPopover(false);
                                }}
                            />
                        </div>
                    </PopoverFooter>
                </Popover>
            </>
        );
    };

    const RecordsTable = () => {
        const originalData = [...SAMPLE_DATA]; // use this to "reset" the table. Do not modify
        const [sortedData, setSortedData] = useState([...originalData]); // holds sorted/unsorted data. Use this with pagination
        const [pageIndex, setPageIndex] = useState(0);
        const rowsPerPage = 25;
        // holds only the visible portion of the sortedData. It's what we feed to the table.
        const [visibleData, setVisibleData] = useState([...sortedData].slice(0, rowsPerPage));
        const [sortState, setSortState] = useState(undefined);
        const tableBodyRef = useRef(null);

        const onPageChangeHandler = (index) => {
            setPageIndex(index);
            setVisibleData([...sortedData.slice(index * rowsPerPage, index * rowsPerPage + rowsPerPage)]);
            if (tableBodyRef.current) {
                // on page change always scroll to the top
                tableBodyRef.current.scrollToRow(0);
            }
        };

        const onSortChangeHandler = (field) => {
            const options = ['asc', 'desc', undefined];
            let nextOptionIndex = 0;
            // if it's a consecutive click on a header of an already sorted column then loop through the sorting options
            // otherwise it's always going to be asc sort. It's the first option
            if (sortState && field === sortState.field && sortState.sortDirection) {
                const currentOptionIndex = options.indexOf(sortState.sortDirection);
                nextOptionIndex = currentOptionIndex + (1 % 3);
            }
            setSortState({ field: field, sortDirection: options[nextOptionIndex] });
            // if sorting is set then actually sort the data otherwise return the unsorted "slice".
            // Also take into account the active page index and return the correct "slice".
            if (options[nextOptionIndex]) {
                setSortedData(() => {
                    const sorted = sortData(field, options[nextOptionIndex]);
                    setVisibleData([...sorted.slice(pageIndex * rowsPerPage, pageIndex * rowsPerPage + rowsPerPage)]);
                    return sorted;
                });
            } else {
                setSortedData([...originalData]);
                setVisibleData([...originalData.slice(pageIndex * rowsPerPage, pageIndex * rowsPerPage + rowsPerPage)]);
            }
        };

        const sortData = (field, direction) => {
            return [...originalData].sort((a, b) => {
                if (direction === 'asc') {
                    return a[field] > b[field] ? 1 : -1;
                } else {
                    return a[field] < b[field] ? 1 : -1;
                }
            });
        };

        const columnDefinitions = useMemo(
            () => [
                {
                    field: 'page_name',
                    label: 'Page Name',
                    sortable: true,
                    minWidth: 70,
                    cellTemplate: (cellContext) => (
                        <Link id={`${cellContext.rowIndex}-page_name`} href="https://www.dayforce.com" target="_blank" >
                            {cellContext.rowData.page_name}
                        </Link>
                    ),
                },
                {
                    field: 'type',
                    label: 'Type',
                    sortable: true,
                    minWidth: 150,
                    cellTemplate: (cellContext) => <TableCellText id={`${cellContext.rowIndex}-${cellContext.getValue()}`}>{cellContext.getValue()}</TableCellText>,
                },
                {
                    field: 'status',
                    label: 'Status',
                    sortable: true,
                    minWidth: 150,
                    cellTemplate: (cellContext) => <TableCellText id={`${cellContext.rowIndex}-${cellContext.getValue()}`}>{cellContext.getValue()}</TableCellText>,
                },
                {
                    field: 'topic',
                    label: 'Topic',
                    sortable: true,
                    minWidth: 170,
                    cellTemplate: (cellContext) => <TableCellText id={`${cellContext.rowIndex}-${cellContext.getValue()}`}>{cellContext.getValue()}</TableCellText>,
                },
                {
                    field: 'last_modified',
                    label: 'Last modified',
                    sortable: true,
                    cellTemplate: (cellContext) => <TableCellText id={`${cellContext.rowIndex}-${cellContext.getValue()}`}>{cellContext.getValue()}</TableCellText>,
                },
                {
                    field: 'last_modified_by',
                    label: 'Last modified by',
                    sortable: true,
                    minWidth: 70,
                    cellTemplate: (cellContext) => <TableCellText id={`${cellContext.rowIndex}-${cellContext.getValue()}`}>{cellContext.getValue()}</TableCellText>,
                },
                {
                    field: 'action',
                    label: 'Actions',
                    minWidth: 70,
                    meta: {
                        isActionColumn: isMobile,
                    },
                    cellTemplate: (cellContext) => (
                        <PopoverMenu
                            triggerOption="iconButton"
                            triggerProps={{
                                variant: 'tertiaryNeutral',
                                iconName: 'moreVert',
                            }}
                            id="page-menu"
                            buttonAriaLabel="Contextual Menu"
                            onChange={console.log}
                            buttonAriaDescribedBy={`page-table-body-page_name ${cellContext.rowIndex}-page_name`}
                        >
                            <PopoverMenuItem id="option-1">Option 1</PopoverMenuItem>
                            <PopoverMenuItem id="option-2">Option 2</PopoverMenuItem>
                        </PopoverMenu>
                    ),
                },
            ],
            []
        );

        return (
            <div style={{ height: '660px', width: '100%' }}>
                <Table id="page-table" testId="table-test-id" mobile={isMobile}>
                    <TableToolbar
                        id="table-toolbar"
                        textMap={{
                            pageNumberX: 'Page {0}',
                            showingXtoYfromZentries: 'Showing {0} to {1} from {2} entries',
                        }}
                    />
                    <TableBody
                        id="page-table-body"
                        testId="table-body-test-id"
                        data={visibleData}
                        columnDefs={columnDefinitions}
                        sorting={sortState}
                        onSortChange={onSortChangeHandler}
                        alternatingRowColors
                        ariaLabel="Page table. Sortable columns can be sorted by activating the column header buttons."
                        textMap={{
                            ascending: 'Ascending',
                            descending: 'Descending',
                            unsorted: 'Unsorted',
                        }}
                        ref={tableBodyRef}
                    ></TableBody>
                    <TablePagination
                        id="page-table-pagination"
                        testId="table-pagination-test-id"
                        currentPageIndex={pageIndex}
                        onPageIndexChange={onPageChangeHandler}
                        currentRowsPerPage={rowsPerPage}
                        totalRowsCount={originalData.length}
                        textMap={{
                            nextPageAriaLabel: 'Next Page',
                            previousPageAriaLabel: 'Previous Page',
                            pageNumberDropdownAriaLabel: 'Results Page Number',
                            page: 'Page',
                        }}
                    ></TablePagination>
                </Table>
            </div>
        );
    };

    return (
        <div style={{ width: '100%' }}>
            <div style={styles.headerBar}>
                <div style={styles.headerBarText}>
                    <h2 className="evrHeading2">Page</h2>
                </div>
                <div>
                    <PageOptionsPopoverMenu />
                </div>
            </div>
            <div style={styles.body}>
                <div style={isMobile? styles.filterBlockMobile : styles.filterBlock}>
                    <div style={isMobile ? {} : styles.searchFieldContainer}>
                        <PageSearchField />
                    </div>
                    <div style={isMobile ? { ...styles.buttonGroup, ...styles.buttonGroupMobile } : styles.buttonGroup}>
                        <CustomizeDisplayPopover />
                        <Button id="new-page-button" label="New Page" startIcon="add" onClick={() => { alert("New Page!"); }} size="large" fullWidth={isMobile}/>
                    </div>
                </div>
                {/* <div></div> This is a placeholder for SimpleFilter*/}
                <div style={{ width: '100%' }}>
                    <RecordsTable />
                </div>
            </div>
        </div>
    );

};

## Live Demo

<Canvas>
  <Story name="Record List">
    {() => {
      return (
        <div>
          <RecordList />
        </div>
      );
    }}
  </Story>
</Canvas>
