/* eslint-disable @typescript-eslint/naming-convention */

export interface IClient {
  page_name: string;
  type: string;
  status: string;
  topic: string;
  last_modified: string;
  last_modified_by: string;
}

export const SAMPLE_DATA = [
  {
    page_name: 'Address',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: '<PERSON>',
  },
  {
    page_name: 'New Hire Form',
    type: 'Form',
    status: 'Draft',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: '<PERSON>',
  },
  {
    page_name: 'Direct Deposit',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: '<PERSON>',
  },
  {
    page_name: 'Car Detail',
    type: 'Form',
    status: 'Draft',
    topic: 'Car',
    last_modified: '05/24/2024',
    last_modified_by: '<PERSON>',
  },
  {
    page_name: 'Job Assignment Change',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: '<PERSON>',
  },
  {
    page_name: 'Country Tax',
    type: 'Form',
    status: 'Archived',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Manesh Patel',
  },
  {
    page_name: 'Employment Status',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: '<PERSON> Tan',
  },
  {
    page_name: 'Name and Marital Status',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Emergency Contact',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
  {
    page_name: 'Employee Car',
    type: 'Form',
    status: 'Active',
    topic: 'Car',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
  {
    page_name: 'Address',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Aaron Banks',
  },
  {
    page_name: 'New Hire Form',
    type: 'Form',
    status: 'Draft',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Direct Deposit',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Car Detail',
    type: 'Form',
    status: 'Draft',
    topic: 'Car',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Job Assignment Change',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Aaron Banks',
  },
  {
    page_name: 'Country Tax',
    type: 'Form',
    status: 'Archived',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Manesh Patel',
  },
  {
    page_name: 'Employment Status',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
  {
    page_name: 'Name and Marital Status',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Emergency Contact',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
  {
    page_name: 'Employee Car',
    type: 'Form',
    status: 'Active',
    topic: 'Car',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
  {
    page_name: 'Address',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Aaron Banks',
  },
  {
    page_name: 'New Hire Form',
    type: 'Form',
    status: 'Draft',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Direct Deposit',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Car Detail',
    type: 'Form',
    status: 'Draft',
    topic: 'Car',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Job Assignment Change',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Aaron Banks',
  },
  {
    page_name: 'Country Tax',
    type: 'Form',
    status: 'Archived',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Manesh Patel',
  },
  {
    page_name: 'Employment Status',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
  {
    page_name: 'Name and Marital Status',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Daisy Herrera',
  },
  {
    page_name: 'Emergency Contact',
    type: 'Form',
    status: 'Active',
    topic: 'Employee',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
  {
    page_name: 'Employee Car',
    type: 'Form',
    status: 'Active',
    topic: 'Car',
    last_modified: '05/24/2024',
    last_modified_by: 'Sue Tan',
  },
];
