import React, { PropsWith<PERSON>hildren, Children, ReactNode, forwardRef, ReactElement } from 'react';

import { PageShellOmnibar } from './page-shell-omnibar';

import styles from './page-shell.module.scss';

export interface IPageShellContent {
  /** Sets the `id` attribute */
  id: string;
  /** Sets `data-testid` attribute */
  testId?: string;
}

export const PageShellContent = forwardRef<HTMLDivElement, PropsWithChildren<IPageShellContent>>(
  (props, ref): JSX.Element => {
    const { id, children, testId } = props;

    let pageShellOmnibarChild: ReactElement | undefined;
    const contentChildren: ReactNode[] = [];

    Children.forEach(children, (child) => {
      if (React.isValidElement(child)) {
        if (child.type === PageShellOmnibar) {
          pageShellOmnibarChild = child;
        } else {
          contentChildren.push(child);
        }
      }
    });

    return (
      <div ref={ref} id={id} data-testid={testId} className={styles.pageShellContent}>
        {pageShellOmnibarChild}
        {contentChildren}
      </div>
    );
  }
);

PageShellContent.displayName = 'PageShellContent';
