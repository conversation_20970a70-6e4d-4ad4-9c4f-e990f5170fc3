import React, { forwardRef, PropsWithChildren } from 'react';

export interface IPageShellLeftPanel {
  /** Sets the `id` attribute */
  id: string;
  /** Sets `data-testid` attribute */
  testId?: string;
}

export const PageShellLeftPanel = forwardRef<HTMLDivElement, PropsWithChildren<IPageShellLeftPanel>>((props, ref) => {
  const { id, testId, children } = props;

  return (
    <div id={id} data-testid={testId} ref={ref}>
      {children}
    </div>
  );
});

PageShellLeftPanel.displayName = 'PageShellLeftPanel';
