import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { PageShell, PageShellLeftPanel, PageShellContent, PageShellOmnibar, usePageShell } from '.';
import { <PERSON><PERSON> } from '../button';
import { IconButton } from '../icon-button';
import { Avatar } from '../avatar';
import { Omnibar } from '../omnibar';
import { ToastProvider, useToastContext } from '../toast-provider';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '../side-panel';
import { SideNav, SideNavActions } from '../side-nav';
import { Drawer } from '../drawer';
import userTaro from '../../../assets/images/user-taro.jpg';

export const scope = {
  PageShell,
  PageShellLeftPanel,
  PageShellContent,
  PageShellOmnibar,
  Button,
  IconButton,
  Avatar,
  ToastProvider,
  useToastContext,
  Omnibar,
  usePageShell,
  SidePanel,
  SidePanelBody,
  SidePanelHeader,
  SidePanelFooter,
  Drawer,
  SideNav,
  SideNavActions,
  userTaro,
};

## Overview

The PageShell component handles the layout and structure of the `LeftPanel`, <LinkTo kind="Patterns/Omnibar">Omnibar</LinkTo>, <LinkTo kind="Components/SidePanel">SidePanel</LinkTo>, <LinkTo kind="Components/Toasts/Toast">Toast</LinkTo> and the content area of the page. It is responsible for managing the behavior and interactions among these components, ensuring seamless functionality across various screen sizes and breakpoints.

## usePageShell

`usePageShell` hook is used to manage the states of individual components and returns the following values:

- `breakpoint` : The `PageShellLeftPanel` is configured to behave as an overlay when the viewport size is extra small (`xs`). This behavior is controlled using the `breakpoint` value returned by the hook.
- `leftPanelCollapsed` and `setLeftPanelCollapsed` : These values allow the hook to manage whether the `PageShellLeftPanel` is expanded or collapsed, depending on the viewport size.
- `sidePanelSize` : The hook dynamically adjusts the size of the `SidePanel` based on the current breakpoint using the `sidePanelSize` value.

## Usage

export const pageShellExample = `() => {
  const OmnibarComponent = () => {
    const styles = {
      omnibarStartSection: {
        display: 'flex',
        alignItems: 'center',
        gap: 'var(--evr-spacing-sm)',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        padding: 'calc(var(--evr-focus-ring-border) * 2)',
        flex: '1 1 auto',
      },
      omnibarEndSection: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: 'var(--evr-spacing-sm)',
        overflow: 'hidden',
        marginInlineStart: 'var(--evr-spacing-lg)',
        padding: 'calc(var(--evr-focus-ring-border) * 2)',
        maxWidth: 'calc(100% - var(--evr-spacing-lg))',
        flex: '1 0 auto',
      },
      omnibarTitleContainer: {
        overflow: 'hidden',
      },
      omnibarTitleText: {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
      },
    };
    return (
      <Omnibar id={'omnibar'} testId={'omnibar-test-id'}>
        <>
          <div style={styles.omnibarStartSection}>
            <Avatar id="avatar" testId="avatar-test" size="lg" src={userTaro} />
            <div aria-live="polite" style={styles.omnibarTitleContainer}>
              <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
                Omnibar Title
              </h1>
              <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
                Subtitle
              </p>
            </div>
          </div>
          <div style={styles.omnibarEndSection}>
            <IconButton
              id="refresh-button"
              testId="refresh-button-test"
              ariaLabel="Refresh"
              iconName="refresh"
              variant="tertiaryNeutral"
              size="large"
              onClick={() => {}}
            />
            <IconButton
              id="print-button"
              testId="print-button-test"
              ariaLabel="Print"
              iconName="print"
              variant="tertiaryNeutral"
              size="large"
              onClick={() => {}}
            />
          </div>
        </>
      </Omnibar>
    );
  };
  const ToastComponent = () => {
    const gridItem = {
      padding: '1rem 3.5rem',
      display: 'flex',
      gap: '1rem',
    };
    const { queueToast, dequeueToast } = useToastContext();
    const [nextId, setNextId] = React.useState(1);
    const templateToast = (id, micro) => ({
      id: \`\${id}\`,
      testId: \`\${id}\`,
      micro,
      closeButtonAriaLabel: 'Close Button',
      title: \`Heading \${id}\`,
      content: \`\${new Date().toLocaleString('en-US')}\`,
      icon: 'rocketship',
      action: { id: 'action-button', label: 'Action' },
      open: false,
    });
    return (
      <div style={gridItem}>
        <Button
          id="trigger-toast-btn-id"
          label="Trigger Toast"
          onClick={() => {
            queueToast(templateToast(nextId, false));
            setNextId((nextId) => nextId + 1);
          }}
        />
        <Button
          id="close-toasts-btn-id"
          testId="close-toasts-btn-test-id"
          label="Close toasts"
          onClick={() => {
            for (let i = 1; i < nextId; i++) {
              dequeueToast(templateToast(i, false));
            }
            setNextId(1);
          }}
        />
      </div>
    );
  };
  const SidePanelComponent = ({ size }) => {
    const [open, setOpen] = useState(false);
    const triggerBtnSidePanelRef = useRef(null);
    const SidePanelStyle = {
      padding: '0 3.5rem 1rem',
    };
    return (
      <div style={SidePanelStyle}>
        <Button
          id="trigger-side-panel-button-id"
          label="Open SidePanel"
          ref={triggerBtnSidePanelRef}
          onClick={() => setOpen(!open)}
        />
        <div>
          <SidePanel
            size={size}
            id="side-panel-id"
            open={open}
            ariaLabelledBy="side-panel-header-id"
            ariaDescribedBy="side-panel-body-id"
            anchor="right"
            onOpen={() => {
              setOpen(true);
            }}
            onClose={(e) => {
              setOpen(false);
              triggerBtnSidePanelRef && triggerBtnSidePanelRef.current.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(!open);
              }}
              id="side-panel-header-id"
            >
              <h3 id="header-id-default" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody id="side-panel-body-id">
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
            <SidePanelFooter id="side-panel-footer-id">
              <p className="evrBodyText">Footer</p>
            </SidePanelFooter>
          </SidePanel>
        </div>
      </div>
    );
  };
  // This style is used to set the height to support the storybook example. This is not required in the actual implementation.
  const pageShellExampleStyle = {
    width: '100%',
    height: '32rem',
    position: 'relative',
    border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
  };
  // This style is not required in the actual implementation once the left side panel is implemented.
  const leftSidePanelStyle = {
    height: '100%',
    borderRight: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
  };
  const pageContentStyle = {
    padding: '0 3.5rem 1rem',
    textAlign: 'justify',
  };
  {
    /** The size of the SidePanel is determined by the sidePanelSize value returned from the usePageShell hook, which adjusts based on the viewport. */
  }
  const { sidePanelSize } = usePageShell();
  return (
    <div style={pageShellExampleStyle}>
      <PageShell
        id="page-shell-id"
        testId="page-shell-test-id"
        toastProviderProps={{ ariaLabel: 'Page Shell Toast Provider' }}
      >
        <PageShellLeftPanel id="page-shell-left-panel-id" testId="page-shell-left-panel-test-id">
          <div style={leftSidePanelStyle}>LeftSidePanel</div>
        </PageShellLeftPanel>
        <PageShellContent id="page-shell-content-id" testId="page-shell-content-test-id">
          <PageShellOmnibar>
            <OmnibarComponent />
          </PageShellOmnibar>
          <ToastComponent />
          <SidePanelComponent size={sidePanelSize} />
          <div style={pageContentStyle}>
            <p className="evrBodyText">
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into
              electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release
              of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software
              like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing
              and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,
              when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum
            </p>
          </div>
        </PageShellContent>
      </PageShell>
    </div>
  );
}`;

<CodeExample scope={scope} code={pageShellExample} />

## PageShell with Side nav

export const pageShellWithSideNav = `() => {
    const OmnibarComponent = () => {
        const omnibarProps = {
            id: 'omnibar',
            testId: 'omnibar-test',
            startSection: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--evr-spacing-sm)' }}>
                    <Avatar id="avatar" testId="avatar-test" size="lg" title="Avatar" ariaLabel="Avatar" />
                    <div key="title-section">
                    <h4 key="title" id="title" className="evrHeading4" aria-live="polite" aria-label="Title">
                        Omnibar Title
                    </h4>
                    <h4
                        key="subtitle"
                        id="subtitle"
                        className="evrBodyText2"
                        style={{ color: 'var(--evr-content-primary-lowemp)' }}
                        aria-live="polite"
                        aria-label="Subtitle"
                    >
                        Subtitle
                    </h4>
                    </div>
                </div>
            ),
            endSection: (
                <div style={{ display: 'flex', alignItems: 'center', gap: '0px' }}>
                    <IconButton
                    key="refresh-button"
                    id="refresh-button"
                    testId="refresh-button-test"
                    ariaLabel="Refresh"
                    iconName="refresh"
                    variant="tertiaryNeutral"
                    size="large"
                    onClick={() => {}}
                    />
                    <IconButton
                    key="print-button"
                    id="print-button"
                    testId="print-button-test"
                    ariaLabel="Print"
                    iconName="print"
                    variant="tertiaryNeutral"
                    size="large"
                    onClick={() => {}}
                    />
                </div>
            ),
        };
        return <Omnibar {...omnibarProps} />;
    };

    const SideNavComponent = () => {
        const [expandedItems, setExpandedItems] = React.useState([]);
        const handleExpand = (id) => {
        setExpandedItems((prev) => {
            if (prev.includes(id)) {
            return prev.filter((item) => item !== id);
            } else {
            return [...prev, id];
            }
        });
        };
        const data = [
          {
            id: 'personal',
            iconName: 'person',
            href: '/Personal',
            label: 'Personal',
            active: true,
            expanded: expandedItems.includes('personal'),
            onClick: (id) => handleExpand(id),
            childItems: [
              {
                id: 'about-me',
                label: 'About Me',
                href: '/about-me',
                active: true,
                onClick: () => console.log('clicked'),
              },
              {
                id: 'letters',
                label: 'Letters',
                href: '/letters',
                onClick: () => console.log('clicked'),
              },
            ],
          },
          {
            id: 'career',
            iconName: 'briefcase',
            label: 'Career',
            href: '/Career',
            onClick: () => console.log('clicked'),
          },
          {
            id: 'forms',
            iconName: 'form',
            label: 'Forms',
            href: '/Forms',
            onClick: () => console.log('clicked'),
          },
          {
            id: 'settings',
            iconName: 'settings',
            label: 'Settings',
            href: '/Settings',
            onClick: () => console.log('clicked'),
          },
        ];

        // create a expanded state to pass to the side nav and drawer
        const [expanded, setExpanded] = React.useState(false);
        const handleToggle = () => {
        setExpanded(!expanded);
        };


        return (
        <Drawer expanded={expanded}>
            <SideNav id="hi" data={data} expanded={expanded} >
            <SideNavActions onToggle={handleToggle}>
                <div>SideNav Actions</div>
            </SideNavActions>
            </SideNav>
        </Drawer>
        );
    }

    // This style is used to set the height to support the storybook example. This is not required in the actual implementation.
    const pageShellExampleStyle = {
        width:'100%',
        height:'32rem',
        position:'relative',
        border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
    };
    const pageContentStyle = {
        padding: '0 3.5rem 1rem',
        textAlign: 'justify',
    };
    {/** The size of the SidePanel is determined by the sidePanelSize value returned from the usePageShell hook, which adjusts based on the viewport. */}
    const { sidePanelSize } = usePageShell();
    return (
        <div style={pageShellExampleStyle}>
            <PageShell id="page-shell-id" testId="page-shell-test-id" toastProviderProps={{ ariaLabel: 'Page Shell Toast Provider' }}>
                <PageShellLeftPanel id="page-shell-left-panel-id" testId="page-shell-left-panel-test-id">
                    <SideNavComponent />
                </PageShellLeftPanel>
                <PageShellContent id="page-shell-content-id" testId="page-shell-content-test-id">
                    <PageShellOmnibar>
                        <OmnibarComponent />
                    </PageShellOmnibar>
                    <div style={pageContentStyle}>
                        <p className="evrBodyText">
                            Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                            industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                            scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                            into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                            release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                            software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
                            printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since
                            the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.
                            It has survived not only five centuries, but also the leap into electronic typesetting, remaining
                            essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing
                            Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including
                            versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem
                            Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a
                            galley of type and scrambled it to make a type specimen book. It has survived not only five centuries,
                            but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in
                            the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with
                            desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply
                            dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy
                            text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type
                            specimen book. It has survived not only five centuries, but also the leap into electronic typesetting,
                            remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets
                            containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker
                            including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting
                            industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown
                            printer took a galley of type and scrambled it to make a type specimen book. It has survived not only
                            five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was
                            popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more
                            recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem
                            Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's
                            standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it
                            to make a type specimen book. It has survived not only five centuries, but also the leap into electronic
                            typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of
                            Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like
                            Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and
                            typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when
                            an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived
                            not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged.
                            It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and
                            more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum
                            Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                            industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
                            scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap
                            into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the
                            release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
                            software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
                            printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since
                            the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.
                            It has survived not only five centuries, but also the leap into electronic typesetting, remaining
                            essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing
                            Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including
                            versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem
                            Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a
                            galley of type and scrambled it to make a type specimen book. It has survived not only five centuries,
                            but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in
                            the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with
                            desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply
                            dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy
                            text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type
                            specimen book. It has survived not only five centuries, but also the leap into electronic typesetting,
                            remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets
                            containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker
                            including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting
                            industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown
                            printer took a galley of type and scrambled it to make a type specimen book. It has survived not only
                            five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was
                            popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more
                            recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum Lorem
                            Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's
                            standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it
                            to make a type specimen book. It has survived not only five centuries, but also the leap into electronic
                            typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of
                            Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like
                            Aldus PageMaker including versions of Lorem Ipsum
                        </p>
                    </div>
                </PageShellContent>
            </PageShell>
        </div>
    );

}`;

<CodeExample scope={scope} code={pageShellWithSideNav} />

## How to Use

`LeftPanel`, `Omnibar`, `SidePanel`, `Toast` are the main components of the PageShell.

- `LeftPanel` is wrapped in the `PageShellLeftPanel` wrapper component.
- `Omnibar` is wrapped in the `PageShellOmnibar` wrapper component.
- `PageShellOmnibar` and content area are wrapped in the `PageShellContent` wrapper component.

`PageShellLeftPanel` and `PageShellContent` wrapper components are passed as children to the `PageShell`. `PageShellOmnibar` and content area are passed as children to the `PageShellContent`.

The wrapper components `PageShellLeftPanel`, `PageShellOmnibar`, and `PageShellContent` are provided by the PageShell and are required for ensuring that the components are positioned correctly within the layout. Without these wrappers, the layout and behavior of the PageShell will not work as expected.
