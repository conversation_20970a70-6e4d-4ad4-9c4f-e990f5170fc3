# PageShell

- Start Date: 2025-03-06
- Figma link: https://www.figma.com/file/GlFJ5NBAdplnrLXTKtLHCI/Page-Templates?node-id=5001-6&p=f&t=cQbZcYSpAsRuwoxz-0
- Epic: https://dayforce.atlassian.net/browse/PWEB-19068

## Summary

The PageShell component handles the layout and structure of the PageShellLeftPanel, Omnibar, SidePanel, and the content area of the page. It is responsible for managing the behavior and interactions among these components, ensuring seamless functionality across various screen sizes and breakpoints.

## Detailed Design

PageShell employs a specific child components approach (similar to `Cards`, `NotificationBanner`, and `Accordion`) rather than a render props approach (similar to `Dropdown`, `Tree`, and `Table`). This method enhances readability and maintainability.

PageShell utilizes a hook - `usePageShell` - to manage the states of individual components.

- When the viewport is extra small (`xs`), the `PageShellLeftPanel` is configured to behave as an overlay. This is achieved using the `breakpoint` return value.
- The hook provides `leftPanelCollapsed` and `setLeftPanelCollapsed` return values to control whether the `PageShellLeftPanel` is collapsed or expanded based on the viewport size.
- Additionally, the hook determines the size of the `SidePanel` using the `sidePanelSize` return value, which dynamically adjusts according to the breakpoint.

Each of the main components within PageShell is wrapped in a specific wrapper component making it easier to read and maintain. For example, refer to the Usage section.

## API

1.  **id**: `string`  
    Sets the id attribute on the PageShell.
2.  **testId**: `string | undefined`  
    Optional. Sets **data-testid** attribute on the PageShell.
3.  **children**: `ReactNode`  
    Optional. Sets the contents of the PageShell.
4.  **toastProviderProps**: `TPageShellToastProvider`
    Optional. Sets the props for the `ToastProvider` and enables the `ToastProvider` if provided

```typescript
export type TPageShellToastProvider = Omit<IToastProvider, 'id' | 'testId'>;
```

### Usage

```jsx
const {breakpoint, leftPanelCollapsed, setLeftPanelCollapsed, sidePanelSize} = usePageShell;

<PageShell id="page-shell-id" testId="page-shell-test-id">
  <PageShellLeftPanel>
    <NavSidePanel {...navSidePanelProps} collapse={leftPanelCollapsed} onExpandButtonClick={()=>{setLeftPanelCollapsed(!leftPanelCollapsed)}}/> <!-- This is not an Everest component -->
  </PageShellLeftPanel>

  <PageShellContent>
    <PageShellOmnibar> <!-- This is a fragment included for future-proofing, allowing for additional styling if needed -->
      <Omnibar id={'omnibar'} testId={'omnibar-test-id'} />
    </PageShellOmnibar>
    <div></div> <!-- This is a placeholder for the PageContent component, which is not part of the current PageShell development -->
    <SidePanel size={sidePanelSize} />
  </PageShellContent>
</PageShell>
```

## Accessibility

TBD

Most of the accessibility features will be managed by the individual components. However, the role of the page content needs to be determined.

## Required PBIs

1. [Architecture](https://dayforce.atlassian.net/browse/PWEB-19068)
2. [Create PageShell component and usePageShell](https://dayforce.atlassian.net/browse/PWEB-19423)
3. [Storybook Documentation](https://dayforce.atlassian.net/browse/PWEB-19424)
4. [A11y](https://dayforce.atlassian.net/browse/PWEB-19425)
5. [Tests: P11y, Manual, Visual tests, Unit tests, playwright tests](https://dayforce.atlassian.net/browse/PWEB-19427)
6. [Add to Ref app](https://dayforce.atlassian.net/browse/PWEB-19429)
7. [Push Component to Production](https://dayforce.atlassian.net/browse/PWEB-19428)

## Q&A

**Why is PageShell implemented using the specific child components approach rather than the render props approach that we have followed in the Dropdown?**
PageShell is implemented using the specific child components approach to enhance readability and maintainability. Also, this approach makes it easy to pass props to child components.

**What component is passed as a child to the PageShellLeftPanel?**
Currently, there is no component available that meets our requirement. The development of this component is in progress.

**Why can't our Everest NavSidePanel be used for PageShellLeftPanel?**
NavSidePanel is an overlay, whereas we need the PageShellLeftPanel to push the page content.

**How can each of the individual components interact with each other and what is the need?**
There is no need for individual components within the `PageShell` to directly interact with each other. The behavior and state of each component are independently managed using the `usePageShell` based on the viewport.

**Does the PageShell accept any child component?**  
Currently, only the children shown in the usage example are accepted. If new templates are introduced in the future, the PageShell will be refactored to support them.

**Should the children be passed in order or will we handle the order?**  
The order will be handled internally, so the feature team does not need to pass the children in a specific order.

**Why is the `size` prop not required despite different sizes being shown in Figma based on breakpoints?**
The `usePageShell` manages the behavior of individual components based on the defined breakpoints. This includes handling actions such as expanding or collapsing the `NavSidePanel` within the `PageShellLeftPanel` and adjusting the size of the `SidePanel`.

**How is padding inside the `PageShellContent` handled?**
The `PageContent` component, which is not part of the `PageShell` development, handles the padding. While padding could be implemented directly in the `PageShellContent`, it is managed in the `PageContent` component to allow flexibility for future page templates that may require different padding.

**Why don't we need a `PageShellRightPanel` similar to `PageShellLeftPanel`?**
The `SidePanel` component creates its own portal, which allows it to render independently without requiring a wrapper like `PageShellRightPanel`.
