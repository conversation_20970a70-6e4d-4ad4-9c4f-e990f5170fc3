import React, { ReactElement } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Avatar } from '../avatar/avatar';
import { Button } from '../button';
import { TIconName } from '../icon';
import { Omnibar } from '../omnibar/omnibar';
import { TToastPosition } from '../toast-overlay';
import { useToastContext } from '../toast-provider';

import { PageShell, PageShellLeftPanel, PageShellContent, PageShellOmnibar, usePageShell } from '.';

const pageShellId = 'page-shell-id';
const pageShellTestId = 'page-shell-test-id';
const pageShellContentId = 'page-shell-content-id';
const pageShellContentTestId = 'page-shell-content-test-id';
const pageShellLeftPanelId = 'page-shell-left-panel-id';
const pageShellLeftPanelTestId = 'page-shell-left-panel-test-id';
const omnibarId = 'omnibar-id';
const omnibarTestId = 'omnibar-test-id';
const toastId = 'toast-id';
const toastTriggerBtnTestId = 'toast-trigger-btn-test-id';

const getPageShell = () => screen.getByTestId(pageShellTestId);
const getPageShellContent = () => screen.getByTestId(pageShellContentTestId);
const getPageShellLeftPanel = () => screen.getByTestId(pageShellLeftPanelTestId);
const getOmnibar = () => screen.getByTestId(`${omnibarTestId}-wrapper`);
const getPageContent = () => screen.queryByText('PageContent');
const getToast = () => screen.queryByText(`Heading ${toastId}`);
const getToastTriggerButton = () => screen.getByRole('button');

let sidePanelSizeValue: string;

// Mock the useEverestContext hook
jest.mock('../everest-provider/everest-provider', () => ({
  useEverestContext: jest.fn(() => ({
    breakpoint: 'xxl',
  })),
}));

const OmnibarComponent = () => {
  return (
    <Omnibar id={omnibarId} testId={omnibarTestId}>
      <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--evr-spacing-sm)' }}>
        <Avatar id="avatar" testId="avatar-test" size="lg" title="Avatar" ariaLabel="Avatar" />
        <div aria-live="polite">
          <h1 id="title" className="evrHeading4">
            Omnibar Title
          </h1>
          <p id="subtitle" className="evrBodyText" style={{ color: 'var(--evr-content-primary-lowemp)' }}>
            Subtitle
          </p>
        </div>
      </div>
    </Omnibar>
  );
};

const ToastComponent = () => {
  const { queueToast } = useToastContext();
  const templateToast = (id: string, micro: boolean) => ({
    id: `${id}`,
    testId: `${id}`,
    micro,
    closeButtonAriaLabel: 'Close Button',
    title: `Heading ${id}`,
    content: `${new Date().toLocaleString('en-US')}`,
    icon: 'rocketship' as TIconName,
    action: { id: 'action-button', label: 'Action' },
    open: false,
  });
  return (
    <Button
      id="toast-trigger-btn"
      testId={toastTriggerBtnTestId}
      label="Default Toast"
      onClick={() => {
        queueToast(templateToast(toastId, false));
      }}
    />
  );
};

export const RenderPageShell = ({
  toastProviderProps,
}: {
  toastProviderProps?: { position: TToastPosition; ariaLabel: string };
}): ReactElement => {
  return <PageShellWrapper toastProviderProps={toastProviderProps} />;
};

const PageShellWrapper = ({
  toastProviderProps,
}: {
  toastProviderProps?: { position: TToastPosition; ariaLabel: string };
}): ReactElement => {
  const { sidePanelSize } = usePageShell();
  sidePanelSizeValue = sidePanelSize;
  return (
    <PageShell id={pageShellId} testId={pageShellTestId} toastProviderProps={toastProviderProps}>
      <PageShellContent id={pageShellContentId} testId={pageShellContentTestId}>
        <ToastComponent />
        <div>PageContent</div>
        <PageShellOmnibar>
          <OmnibarComponent />
        </PageShellOmnibar>
      </PageShellContent>
      <PageShellLeftPanel id={pageShellLeftPanelId} testId={pageShellLeftPanelTestId}>
        LeftSidePanel
      </PageShellLeftPanel>
    </PageShell>
  );
};

describe('[PageShell]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render PageShell and its children with correct id and testId', () => {
    render(RenderPageShell({}));
    expect(getPageShell()).toHaveAttribute('id', pageShellId);
    expect(getPageShell()).toHaveAttribute('data-testid', pageShellTestId);

    expect(getPageShellContent()).toHaveAttribute('id', pageShellContentId);
    expect(getPageShellContent()).toHaveAttribute('data-testid', pageShellContentTestId);

    expect(getPageShellLeftPanel()).toHaveAttribute('id', pageShellLeftPanelId);
    expect(getPageShellLeftPanel()).toHaveAttribute('data-testid', pageShellLeftPanelTestId);
  });

  it('should render PageShell children in the correct order', async () => {
    render(RenderPageShell({}));
    expect(getPageShell()).toBeInTheDocument();

    expect(getPageShell().firstChild).toBe(getPageShellLeftPanel());
    expect(getPageShell().lastChild).toBe(getPageShellContent());
  });

  it('should render PageShellContent children in the correct order', async () => {
    render(RenderPageShell({}));

    expect(getOmnibar()).toBeInTheDocument();
    expect(getPageShellContent()).toBeInTheDocument();

    expect(getPageShellContent().firstChild).toBe(getOmnibar());
    expect(getPageShellContent().lastChild).toBe(getPageContent());
  });

  it('toastProviderProps enables the ToastProvider', async () => {
    const { unmount } = render(RenderPageShell({}));
    await userEvent.click(getToastTriggerButton());
    expect(getToast()).not.toBeInTheDocument();

    unmount();

    render(RenderPageShell({ toastProviderProps: { position: 'bottomCenter', ariaLabel: 'ToastProvider' } }));
    await userEvent.click(getToastTriggerButton());
    expect(getToast()).toBeInTheDocument();
  });

  it('usePageShell hook should return correct sidePanelSize value based on viewport', () => {
    render(RenderPageShell({ toastProviderProps: { position: 'bottomCenter', ariaLabel: 'ToastProvider' } }));

    expect(sidePanelSizeValue).toBe('xl');
  });
});
