import { useState, useRef } from 'react';
import { ArgsTable, <PERSON>vas, <PERSON>a, Story } from '@storybook/addon-docs';
import Examples from './page-shell.examples.mdx';
import { PageShell, PageShellLeftPanel, PageShellContent, PageShellOmnibar, usePageShell } from '.';
import { Button } from '../button';
import { IconButton } from '../icon-button';
import { Avatar } from '../avatar';
import { Omnibar } from '../omnibar';
import { ToastProvider, useToastContext } from '../toast-provider';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '../side-panel';
import userTaro from '../../../assets/images/user-taro.jpg';

<Meta
  title="Patterns/PageShell"
  component={PageShell}
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/GlFJ5NBAdplnrLXTKtLHCI/Page-Templates?node-id=5001-6&p=f&t=cQbZcYSpAsRuwoxz-0',
    },
  }}
  args={{
    id: 'page-shell-id',
    testId: 'page-shell-test-id',
    toastProviderProps: {
      position: 'bottomRight',
      delay: 5000,
      ariaLabel: 'Notifications',
    },
  }}
/>

# PageShell

<Examples />

## Live Demo

export const OmnibarComponent = () => {
  const styles = {
    omnibarStartSection: {
      display: 'flex',
      alignItems: 'center',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      flex: '1 1 auto',
    },
    omnibarEndSection: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: 'var(--evr-spacing-sm)',
      overflow: 'hidden',
      marginInlineStart: 'var(--evr-spacing-lg)',
      padding: 'calc(var(--evr-focus-ring-border) * 2)',
      maxWidth: 'calc(100% - var(--evr-spacing-lg))',
      flex: '1 0 auto',
    },
    omnibarTitleContainer: {
      overflow: 'hidden',
    },
    omnibarTitleText: {
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  };
  return (
    <Omnibar id={'omnibar'} testId={'omnibar-test-id'}>
      <>
        <div style={styles.omnibarStartSection}>
          <Avatar id="avatar" testId="avatar-test" size="lg" src={userTaro} />
          <div aria-live="polite" style={styles.omnibarTitleContainer}>
            <h1 id="title" className="evrHeading4" style={styles.omnibarTitleText}>
              Omnibar Title
            </h1>
            <p id="subtitle" className="evrBodyText" style={styles.omnibarTitleText}>
              Subtitle
            </p>
          </div>
        </div>
        <div style={styles.omnibarEndSection}>
          <IconButton
            id="refresh-button"
            testId="refresh-button-test"
            ariaLabel="Refresh"
            iconName="refresh"
            variant="tertiaryNeutral"
            size="large"
            onClick={() => {}}
          />
          <IconButton
            id="print-button"
            testId="print-button-test"
            ariaLabel="Print"
            iconName="print"
            variant="tertiaryNeutral"
            size="large"
            onClick={() => {}}
          />
        </div>
      </>
    </Omnibar>
  );
};

export const ToastComponent = () => {
  const gridItem = {
    padding: '1rem 3.5rem',
    display: 'flex',
    gap: '1rem',
  };
  const { queueToast, dequeueToast } = useToastContext();
  const [nextId, setNextId] = React.useState(1);
  const templateToast = (id, micro) => ({
    id: `${id}`,
    testId: `${id}`,
    micro,
    closeButtonAriaLabel: 'Close Button',
    title: `Heading ${id}`,
    content: `${new Date().toLocaleString('en-US')}`,
    icon: 'rocketship',
    action: { id: 'action-button', label: 'Action' },
    open: false,
  });
  return (
    <div style={gridItem}>
      <Button
        id="trigger-toast-btn-id"
        label="Trigger Toast"
        onClick={() => {
          queueToast(templateToast(nextId, false));
          setNextId((nextId) => nextId + 1);
        }}
      />
      <Button
        id="close-toasts-btn-id"
        testId="close-toasts-btn-test-id"
        label="Close toasts"
        onClick={() => {
          for (let i = 1; i < nextId; i++) {
            dequeueToast(templateToast(i, false));
          }
          setNextId(1);
        }}
      />
    </div>
  );
};

export const SidePanelComponent = ({ size }) => {
  const [open, setOpen] = useState(false);
  const triggerBtnSidePanelRef = useRef(null);
  const SidePanelStyle = {
    padding: '0 3.5rem 1rem',
  };
  return (
    <div style={SidePanelStyle}>
      <Button
        id="trigger-side-panel-button-id"
        label="Open SidePanel"
        ref={triggerBtnSidePanelRef}
        onClick={() => setOpen(!open)}
      />
      <div>
        <SidePanel
          size={size}
          id="side-panel-id"
          open={open}
          ariaLabelledBy="side-panel-header-id"
          ariaDescribedBy="side-panel-body-id"
          anchor="right"
          onOpen={() => {
            setOpen(true);
          }}
          onClose={(e) => {
            setOpen(false);
            triggerBtnSidePanelRef && triggerBtnSidePanelRef.current.focus();
          }}
        >
          <SidePanelHeader
            closeButtonAriaLabel={'close side panel'}
            onCloseButtonClick={(e) => {
              setOpen(!open);
            }}
            id="side-panel-header-id"
          >
            <h3 id="header-id-default" className="evrHeading3">
              Heading
            </h3>
          </SidePanelHeader>
          <SidePanelBody id="side-panel-body-id">
            <p className="evrBodyText">Hello world!</p>
          </SidePanelBody>
          <SidePanelFooter id="side-panel-footer-id">
            <p className="evrBodyText">Footer</p>
          </SidePanelFooter>
        </SidePanel>
      </div>
    </div>
  );
};

export const PageShellComponent = ({ args }) => {
  // This style is used to set the height to support the storybook example. This is not required in the actual implementation.
  const pageShellExampleStyle = {
    width: '100%',
    height: '32rem',
    position: 'relative',
    border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
  };
  // This style is not required in the actual implementation once the left side panel is implemented.
  const leftSidePanelStyle = {
    height: '100%',
    borderRight: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
  };
  const pageContentStyle = {
    padding: '0 3.5rem 1rem',
    textAlign: 'justify',
  };
  /** The size of the SidePanel is determined by the sidePanelSize value returned from the usePageShell hook, which adjusts based on the viewport. */
  const { sidePanelSize } = usePageShell();
  return (
    <div style={pageShellExampleStyle}>
      <PageShell {...args}>
        <PageShellLeftPanel id="page-shell-left-panel-id" testId="page-shell-left-panel-test-id">
          <div style={leftSidePanelStyle}>LeftSidePanel</div>
        </PageShellLeftPanel>
        <PageShellContent id="page-shell-content-id" testId="page-shell-content-test-id">
          <PageShellOmnibar>
            <OmnibarComponent />
          </PageShellOmnibar>
          <ToastComponent />
          <SidePanelComponent size={sidePanelSize} />
          <div style={pageContentStyle}>
            <p className="evrBodyText">
              Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and
              scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into
              electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release
              of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software
              like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the printing
              and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,
              when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum Lorem Ipsum is simply dummy text of the
              printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the
              1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has
              survived not only five centuries, but also the leap into electronic typesetting, remaining essentially
              unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum
              passages, and more recently with desktop publishing software like Aldus PageMaker including versions of
              Lorem Ipsum Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
              been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of
              type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the
              leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with
              the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing
              software like Aldus PageMaker including versions of Lorem Ipsum
            </p>
          </div>
        </PageShellContent>
      </PageShell>
    </div>
  );
};

{/* PageShellComponent is a workaround for storybook bug of not re-rendering the component when the internal state changes. Because of this bug, the usePageShell hooks is not returning the breakpoint based on the viewport. */}

<Canvas>
  <Story name="PageShell">
    {(args) => {
      return <PageShellComponent args={args} />;
    }}
  </Story>
</Canvas>

<ArgsTable story="PageShell" />
