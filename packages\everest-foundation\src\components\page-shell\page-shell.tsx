import React, { forwardRef, PropsWith<PERSON>hildren, Children, ReactElement } from 'react';

import { PageShellContent } from './page-shell-content';
import { PageShellLeftPanel } from './page-shell-left-panel';
import { ToastProvider, IToastProvider } from '../toast-provider';

import styles from './page-shell.module.scss';

export type TPageShellToastProviderProps = Omit<IToastProvider, 'id' | 'testId'>;

export interface IPageShell {
  /** Sets the `id` attribute */
  id: string;
  /** Sets `data-testid` attribute */
  testId?: string;
  /** Sets the props for the `ToastProvider` and enables the `ToastProvider` if provided */
  toastProviderProps?: TPageShellToastProviderProps;
}

export const PageShell = forwardRef<HTMLDivElement, PropsWithChildren<IPageShell>>((props, ref) => {
  const { id, testId, children, toastProviderProps } = props;

  let pageShellLeftPanelChild: ReactElement | undefined;
  let pageShellContentChild: ReactElement | undefined;

  Children.forEach(children, (child) => {
    if (React.isValidElement(child)) {
      switch (child.type) {
        case PageShellLeftPanel:
          pageShellLeftPanelChild = child;
          break;
        case PageShellContent:
          pageShellContentChild = child;
          break;
      }
    }
  });

  const renderPageShell = (
    <div id={id} data-testid={testId} ref={ref} className={styles.evrPageShell}>
      {pageShellLeftPanelChild}
      {pageShellContentChild}
    </div>
  );

  return toastProviderProps ? (
    <ToastProvider
      {...toastProviderProps}
      id={`${id}-toast-provider`}
      testId={testId ? `${testId}-toast-provider` : undefined}
    >
      {renderPageShell}
    </ToastProvider>
  ) : (
    renderPageShell
  );
});

PageShell.displayName = 'PageShell';
