import React, { useState } from 'react';

import { useEverestContext } from '../everest-provider/everest-provider';
import { TSidePanelSize } from '../side-panel-base';

export const usePageShell = (): {
  breakpoint: string;
  leftPanelCollapsed: boolean;
  setLeftPanelCollapsed: React.Dispatch<React.SetStateAction<boolean>>;
  sidePanelSize: TSidePanelSize;
} => {
  const { breakpoint } = useEverestContext();

  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState<boolean>(false);
  const [sidePanelSize, setSidePanelSize] = useState<TSidePanelSize>('md');

  React.useEffect(() => {
    const breakpointConfig: Record<string, { sidePanelSize: TSidePanelSize; leftPanelCollapsed: boolean }> = {
      xs: { sidePanelSize: 'fullscreen', leftPanelCollapsed: true },
      sm: { sidePanelSize: 'fullscreen', leftPanelCollapsed: true },
      md: { sidePanelSize: 'md', leftPanelCollapsed: true },
      lg: { sidePanelSize: 'lg', leftPanelCollapsed: false },
      xl: { sidePanelSize: 'xl', leftPanelCollapsed: false },
      xxl: { sidePanelSize: 'xl', leftPanelCollapsed: false },
    };

    const config = breakpointConfig[breakpoint] || { sidePanelSize: 'md', leftPanelCollapsed: false };

    setSidePanelSize(config.sidePanelSize);
    setLeftPanelCollapsed(config.leftPanelCollapsed);
  }, [breakpoint]);

  return {
    breakpoint,
    leftPanelCollapsed,
    setLeftPanelCollapsed,
    sidePanelSize,
  };
};
