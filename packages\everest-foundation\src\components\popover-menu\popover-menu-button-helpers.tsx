import { KeyboardEvent } from 'react';

import { TMenuListInitialPosition } from '../menu-list';

export function handleOnKeyDown(
  e: KeyboardEvent<HTMLButtonElement>,
  disabled: boolean,
  setInitialPosition: (value: TMenuListInitialPosition) => void,
  visible?: boolean,
  setVisible?: (value: boolean) => void
): void {
  if (disabled || e.key === 'Tab') return;
  if (!['ArrowUp', 'ArrowDown', 'Enter', ' '].some((value) => value === e.key)) return;
  e.stopPropagation();
  e.preventDefault();
  setVisible?.(!visible);
  if (e.key === 'ArrowUp') {
    setInitialPosition('end' as TMenuListInitialPosition);
    return;
  }
  setInitialPosition('start' as TMenuListInitialPosition);
}
