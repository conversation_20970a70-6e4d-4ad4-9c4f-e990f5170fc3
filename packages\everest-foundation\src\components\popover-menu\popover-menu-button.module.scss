@use '@ceridianhcm/theme/dist/scss/' as typography;
@use '../segmented-button/segmented-button.module.scss' as segmentedButtonOverrides;

// these styles override the styling of the ButtonBase to meet the one-off buttons designs for the toolbar
.toolbarButtonOverrides {
  @include typography.body2Regular;
  padding-inline: calc(var(--evr-spacing-sm) - var(--evr-spacing-3xs));
  height: var(--evr-size-xl);

  &:not([disabled]):not(.inverse) {
    &:hover {
      background-color: var(--evr-surfaces-primary-hovered);
    }
    &:active {
      background-color: var(--evr-surfaces-secondary-hovered);
    }
  }
}

// these styles override the styling of the ButtonBase to meet the one-off buttons designs for segmented button
.informationalSegmentedButton {
  @include segmentedButtonOverrides.evrSegmentedButton;

  color: var(--evr-content-status-informative-lowemp);
  fill: var(--evr-content-status-informative-lowemp);
  background-color: var(--evr-surfaces-status-informative-lowemp);
  border-color: var(--evr-interactive-primary-default);

  // needed to increase specificity
  &:active:not([disabled]),
  &:hover:not([disabled]) {
    color: var(--evr-content-status-informative-lowemp);
    fill: var(--evr-content-status-informative-lowemp);
    background-color: var(--evr-surfaces-status-informative-lowemp);
    border-color: var(--evr-interactive-primary-default);
  }
}

.neutralSegmentedButton {
  @include segmentedButtonOverrides.evrSegmentedButton;

  color: var(--evr-content-primary-highemp);
  fill: var(--evr-content-primary-highemp);
  background-color: var(--evr-surfaces-primary-selected);
  border-color: var(--evr-borders-primary-default);

  // needed to increase specificity
  &:active:not([disabled]),
  &:hover:not([disabled]) {
    color: var(--evr-content-primary-highemp);
    fill: var(--evr-content-primary-highemp);
    background-color: var(--evr-surfaces-primary-selected);
    border-color: var(--evr-borders-primary-default);
  }
}

.fullWidth {
  width: 100%;
}
