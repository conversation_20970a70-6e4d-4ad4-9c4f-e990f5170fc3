import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { PopoverMenuButton } from './popover-menu-button.tsx';
import { useState, useRef } from 'react';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Popover Menu Button"
  component={PopoverMenuButton}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'popover-menu-button',
    testId: 'button-test',
    ariaControls: 'dummy-control',
    buttonAriaLabel: 'Popover Menu Button',
    buttonLabel: 'Button',
  }}
/>

# Popover Menu Button

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Default">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          disabled
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Button">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="button"
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Button Disabled">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="button"
          disabled
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Button with triggerProps">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="button"
          triggerProps={{
            variant: 'secondaryNeutral',
            endIcon: 'chevronDownSmall',
          }}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Button with triggerProps & fullWidth">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="button"
          triggerProps={{
            variant: 'secondaryNeutral',
            startIcon: 'file',
            fullWidth: true,
          }}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="IconButton with triggerProps">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="iconButton"
          triggerProps={{
            variant: 'tertiary',
            iconName: 'edit',
            square: true,
          }}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="iconButton with triggerProps including size large">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="iconButton"
          triggerProps={{
            variant: 'tertiary',
            iconName: 'edit',
            square: false,
            size: 'large',
          }}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="ToolbarButton">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="toolbarButton"
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="ToolbarButton with triggerProps">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="toolbarButton"
          triggerProps={{
            variant: 'tertiaryNeutral',
            startIcon: 'menu',
          }}
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="ToolbarButton with smart chevron icon">
    {(args) => {
      const [visible, setVisible] = useState(false);
      const [activeDescendantId, setActiveDescendantId] = useState('');
      const focusRingRef = useRef(null);
      return (
        <PopoverMenuButton
          {...args}
          setVisible={setVisible}
          setActiveDescendantId={setActiveDescendantId}
          focusRingRef={focusRingRef}
          triggerOption="toolbarButton"
          triggerProps={{
            variant: 'tertiaryNeutral',
          }}
          showChevron
        />
      );
    }}
  </Story>
</Canvas>
