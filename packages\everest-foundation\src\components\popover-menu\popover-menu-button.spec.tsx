import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { PopoverMenuButton } from './popover-menu-button';

describe('[Popover Menu Trigger Button]', () => {
  const id = 'trigger-button';
  const ariaControls = 'test-menu';
  const disabled = false;
  const buttonAriaLabel = 'Trigger button';
  const buttonAriaDescribedBy = 'element1 element2';
  const testId = 'test';
  const visible = false;
  const tabIndex = 0 as 0 | -1 | undefined;
  const setVisible = jest.fn();
  const setInitialPosition = jest.fn();

  const propsMock = {
    id,
    visible,
    setVisible,
    ariaControls,
    disabled,
    buttonAriaLabel,
    buttonAriaDescribedBy,
    testId,
    setInitialPosition,
    tabIndex,
  };

  const getPopoverMenuButton = () => screen.getByTestId(`${testId}-popover-menu-button`);

  beforeEach(() => {
    jest.clearAllMocks();
  });

  [
    {
      name: 'Icon Button',
      jsx: (props = propsMock) => <PopoverMenuButton {...props} />,
    },
    {
      name: 'Icon Button with triggerProps',
      jsx: (props = propsMock) => (
        <PopoverMenuButton {...props} triggerProps={{ variant: 'primary', iconName: 'rocketship', square: true }} />
      ),
    },
    {
      name: 'Button',
      jsx: (props = propsMock) => {
        return <PopoverMenuButton {...props} triggerOption="button" buttonLabel="Button" />;
      },
    },
    {
      name: 'Button with triggerProps',
      jsx: (props = propsMock) => {
        return (
          <PopoverMenuButton
            {...props}
            triggerOption="button"
            triggerProps={{ variant: 'tertiaryNeutral', size: 'small', startIcon: 'view' }}
            buttonLabel="Button"
          />
        );
      },
    },
    {
      name: 'Toolbar Button',
      jsx: (props = propsMock) => {
        return <PopoverMenuButton {...props} triggerOption="toolbarButton" buttonLabel="Toolbar Button" />;
      },
    },
    {
      name: 'Toolbar Button with triggerProps',
      jsx: (props = propsMock) => {
        return (
          <PopoverMenuButton
            {...props}
            triggerOption="toolbarButton"
            triggerProps={{ variant: 'tertiaryNeutral', startIcon: 'menu' }}
            buttonLabel="Toolbar Button"
          />
        );
      },
    },
    {
      name: 'Toolbar Button with smart chevron icon',
      jsx: (props = propsMock) => {
        return <PopoverMenuButton {...props} triggerOption="toolbarButton" buttonLabel="Toolbar Button" showChevron />;
      },
    },
  ].forEach(function (item) {
    it(`${item.name} should render without issue`, () => {
      render(item.jsx());
      expect(getPopoverMenuButton()).toBeInTheDocument();
    });

    it(`${item.name} should have aria-controls attribute`, () => {
      render(item.jsx());
      expect(getPopoverMenuButton()).toHaveAttribute('aria-controls', ariaControls);
    });

    it(`${item.name} should have aria-expanded initially set to false`, () => {
      render(item.jsx());
      expect(getPopoverMenuButton()).toHaveAttribute('aria-expanded', 'false');
    });

    it(`${item.name} should have an aria-label`, () => {
      render(item.jsx());
      expect(getPopoverMenuButton()).toHaveAttribute('aria-label', buttonAriaLabel);
    });
    it(`${item.name} should have an aria-describedby`, () => {
      render(item.jsx());
      expect(getPopoverMenuButton()).toHaveAttribute('aria-describedby', buttonAriaDescribedBy);
    });

    it(`${item.name} should have aria-haspopup`, () => {
      render(item.jsx());
      expect(getPopoverMenuButton()).toHaveAttribute('aria-haspopup', 'true');
    });

    it(`${item.name} should have disabled attribute when disabled`, () => {
      propsMock.disabled = true;
      render(item.jsx());
      expect(getPopoverMenuButton()).toHaveAttribute('disabled');
      propsMock.disabled = false;
    });

    describe('Mouse', () => {
      beforeEach(() => {
        propsMock.visible = false;
        // Need to use this instance of setVisible (not const instance declared above) to test aria-expanded when clicked
        propsMock.setVisible = jest.fn(() => {
          propsMock.visible = true;
        });
      });

      it(`${item.name} should call setVisible when clicked`, () => {
        render(item.jsx());
        getPopoverMenuButton().click();
        expect(propsMock.setVisible).toHaveBeenCalledTimes(1);
      });

      it(`${item.name} should not call setVisible when clicked and disabled`, () => {
        propsMock.disabled = true;
        render(item.jsx());
        getPopoverMenuButton().click();
        expect(propsMock.setVisible).toHaveBeenCalledTimes(0);
        propsMock.disabled = false;
      });

      it(`${item.name} should set aria-expanded to true when clicked`, () => {
        const { rerender } = render(item.jsx());
        getPopoverMenuButton().click();
        rerender(item.jsx());
        expect(propsMock.setVisible).toHaveBeenCalledTimes(1);
        expect(getPopoverMenuButton()).toHaveAttribute('aria-expanded', 'true');
      });
    });

    describe('Keyboard', () => {
      beforeEach(() => {
        propsMock.visible = false;
      });

      function setPropsMockVisible() {
        propsMock.visible = true;
      }

      async function keyboardActionTestFocus(key: string) {
        render(item.jsx());
        getPopoverMenuButton().focus();
        await userEvent.keyboard(key);
        expect(propsMock.setVisible).toHaveBeenCalledTimes(1);
        expect(propsMock.setVisible).toHaveBeenCalledWith(true);
      }

      it(`${item.name} should set the menu to be visible when the enter key is pressed`, async () => {
        await keyboardActionTestFocus('{enter}');
        expect(propsMock.setInitialPosition).toHaveBeenCalledWith('start');
      });

      it(`${item.name} should set the menu to be visible when the space key is pressed`, async () => {
        await keyboardActionTestFocus(' ');
      });

      it(`${item.name} should set the menu to be visible when the arrow down key is pressed`, async () => {
        await keyboardActionTestFocus('{ArrowDown}');
      });

      it(`${item.name} should set the menu to be visible and  when the up arrow key is pressed`, async () => {
        await keyboardActionTestFocus('{ArrowUp}');
        expect(propsMock.setInitialPosition).toHaveBeenCalledTimes(1);
        expect(propsMock.setInitialPosition).toHaveBeenCalledWith('end');
      });

      async function keyboardActionTestAriaExpanded(key: string) {
        propsMock.setVisible = jest.fn(setPropsMockVisible);
        const { rerender } = render(item.jsx());

        getPopoverMenuButton().focus();
        await userEvent.keyboard(key);
        expect(propsMock.setVisible).toHaveBeenCalledTimes(1);
        rerender(item.jsx());
        expect(getPopoverMenuButton()).toHaveAttribute('aria-expanded', 'true');
      }

      it(`${item.name} should set aria-expanded equal to true when enter key is pressed`, async () => {
        await keyboardActionTestAriaExpanded('{enter}');
      });

      it(`${item.name} should set aria-expanded equal to true when space key is pressed`, async () => {
        await keyboardActionTestAriaExpanded(' ');
      });

      async function keyboardActionTestDisabledVisbility(key: string) {
        propsMock.setVisible = jest.fn(setPropsMockVisible);
        propsMock.disabled = true;
        const { rerender } = render(item.jsx());
        getPopoverMenuButton().focus();
        await userEvent.keyboard(key);
        expect(propsMock.setVisible).toHaveBeenCalledTimes(0);
        expect(propsMock.visible).toEqual(false);
        rerender(item.jsx());
        expect(getPopoverMenuButton()).toHaveAttribute('aria-expanded', 'false');
      }

      it(`${item.name} should not set visible to true and not set aria-expanded to true when disabled and enter key is pressed`, async () => {
        await keyboardActionTestDisabledVisbility('{enter}');
      });

      it(`${item.name} should not set visible to true and not set aria-expanded to true when disabled and space key is pressed`, async () => {
        await keyboardActionTestDisabledVisbility(' ');
      });

      it(`${item.name} should not set visible to true and not set aria-expanded to true when disabled and down arrow key is pressed`, async () => {
        await keyboardActionTestDisabledVisbility('{ArrowDown}');
      });

      it(`${item.name} should not set visible to true and not set aria-expanded to true when disabled and up arrow key is pressed'`, async () => {
        await keyboardActionTestDisabledVisbility('{ArrowUp}');
      });
    });
  });
});
