import React, { RefObject, forwardRef, useContext } from 'react';
import classNames from 'classnames';

import { handleOnKeyDown } from './popover-menu-button-helpers';
import { PopoverMenuContext } from './popover-menu-context';
import { IPopoverMenuTriggerButtonProps, IPopoverMenuTriggerIconButtonProps } from './popover-menu-provider';
import { mergeRefs, useCreateTestId } from '../../utils';
import { resolvePropsContext } from '../../utils/resolve-props-context';
import { ButtonBase } from '../button-base';
import { Icon } from '../icon';
import { IIconButtonProps, IconButton } from '../icon-button';
import { TMenuListInitialPosition } from '../menu-list';

import styles from './popover-menu-button.module.scss';

export type ITriggerOption = 'iconButton' | 'button' | 'toolbarButton' | 'segmentedNeutral' | 'segmentedInformational';

export interface IPopoverMenuButton {
  id: string;
  visible?: boolean;
  setVisible?: (value: boolean) => void;
  disabled?: boolean;
  showChevron?: boolean;
  buttonLabel?: string;
  buttonAriaDescribedBy?: string;
  buttonAriaLabel: string;
  ariaControls?: string;
  tabIndex?: -1 | 0;
  testId?: string;
  setInitialPosition?: (value: TMenuListInitialPosition) => void;
  triggerOption?: ITriggerOption;
  triggerRef?: RefObject<HTMLElement>;
  ref?: React.ForwardedRef<HTMLElement>;
  // this must be refactored
  triggerProps?: IPopoverMenuTriggerIconButtonProps | IPopoverMenuTriggerButtonProps;
}

export const PopoverMenuButton = forwardRef<HTMLElement, IPopoverMenuButton>((props, ref) => {
  const popoverMenuContext = useContext(PopoverMenuContext);
  const {
    id,
    disabled = false,
    triggerOption = 'iconButton',
    buttonLabel = '',
    buttonAriaLabel,
    buttonAriaDescribedBy,
    ariaControls,
    testId,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    setInitialPosition = (value: TMenuListInitialPosition) => undefined,
    visible,
    setVisible,
    showChevron = false,
    triggerRef,
    triggerProps,
    tabIndex,
  } = resolvePropsContext(props, popoverMenuContext);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleOnClick(e: React.MouseEvent<HTMLButtonElement>) {
    if (disabled) return;
    setVisible?.(!visible);
  }

  const testIdRef = useCreateTestId(testId ? `${testId}-popover-menu-button` : undefined);
  const refs = mergeRefs([triggerRef as RefObject<HTMLDivElement>, testIdRef, ref]);

  // Using type IconButtonProps since Button's & IconButton's props overlap (excluding buttonLabel)
  const defaultProps = {
    id,
    disabled,
    onClick: handleOnClick,
    onKeyDown: (e: React.KeyboardEvent<HTMLButtonElement>) =>
      handleOnKeyDown(e, disabled, setInitialPosition, visible, setVisible),
    ariaExpanded: visible,
    ariaHasPopup: 'true',
    ariaControls,
    ariaDescribedBy: buttonAriaDescribedBy,
    ariaLabel: buttonAriaLabel,
  } as IIconButtonProps;

  // the button should be decoupled in future
  if (triggerOption === 'iconButton') {
    const { variant, iconName, square, size } = (triggerProps as IPopoverMenuTriggerIconButtonProps) || {};
    const destructuredProps = {
      variant: variant || 'tertiary',
      iconName: iconName || 'moreVert',
      square: square || false,
      size: size || 'small',
    };
    return <IconButton {...destructuredProps} {...defaultProps} ref={refs} type="button" />;
  } else {
    const { variant, size, startIcon, endIcon, fullWidth } = (triggerProps as IPopoverMenuTriggerButtonProps) || {};
    const destructuredProps = {
      variant: variant || 'secondary',
      size: size || 'medium',
      startIcon: startIcon,
      endIcon: endIcon,
      fullWidth,
    };

    const getEndIcon = () => {
      // has regular endIcon
      if (endIcon && endIcon !== startIcon) {
        return <Icon name={endIcon} size="md" />;
      }

      // smart chevron icon
      if (showChevron) {
        return <Icon name={visible ? 'chevronUpSmall' : 'chevronDownSmall'} size="md" />;
      }
    };

    return (
      <ButtonBase
        type="button"
        ref={refs}
        {...destructuredProps}
        {...defaultProps}
        tabIndex={tabIndex}
        radius={
          ['button', 'segmentedNeutral', 'segmentedInformational'].includes(triggerOption)
            ? '--evr-radius-md'
            : '--evr-radius-3xs'
        }
        className={classNames({
          [styles.toolbarButtonOverrides]: triggerOption === 'toolbarButton',
          [styles.informationalSegmentedButton]: triggerOption === 'segmentedInformational',
          [styles.neutralSegmentedButton]: triggerOption === 'segmentedNeutral',
          [styles.fullWidth]: destructuredProps.fullWidth,
        })}
        callToAction={triggerOption === 'button'}
      >
        {startIcon && <Icon name={startIcon} size="md" />}
        <span>{buttonLabel}</span>
        {getEndIcon()}
      </ButtonBase>
    );
  }
});

if (process.env.NODE_ENV !== 'production') {
  PopoverMenuButton.displayName = 'PopoverMenuButton';
}
