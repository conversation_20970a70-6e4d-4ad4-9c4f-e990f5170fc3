import React from 'react';
import { TPlacement } from '@ceridianhcm/everest-cdk/dist';

import { IPopoverMenuProvider } from './popover-menu-provider';
import { TMenuListInitialPosition, TMenuListItemValue } from '../menu-list';

export interface IPopoverMenuContext extends IPopoverMenuProvider {
  visible: boolean;
  setVisible: (value: boolean) => void;
  handleClickOutside: (e: Event) => void;
  onClose: () => void;
  onOpen: () => void;
  fitToScreen: boolean; //fitToScreen doesnt effect the output as this is disabled in anchoredOverlay
  setInitialPosition: (value: TMenuListInitialPosition) => void;
  intialPosition: TMenuListInitialPosition;
  checkedIds?: string[];
}

export const defaultContext = {
  visible: false,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setVisible: (value: boolean): void => undefined,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleClickOutside: (e: Event): void => undefined,
  onClose: (): void => undefined,
  onOpen: (): void => undefined,
  intialPosition: 'start' as TMenuListInitialPosition,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setInitialPosition: (value: TMenuListInitialPosition): void => undefined,
  //
  id: '',
  // this is deprecated an no longer used
  selectedMenuId: '',
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChange: (value: TMenuListItemValue): void => undefined,
  buttonAriaLabel: '',
  placement: 'bottom' as TPlacement,
  disabled: false,
  testId: '',
  maxItems: 7,
  ariaLabelledBy: '',
  fitToScreen: true,
  checkedIds: [],
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const PopoverMenuContext = React.createContext<IPopoverMenuContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  PopoverMenuContext.displayName = 'PopoverMenuContext';
}
