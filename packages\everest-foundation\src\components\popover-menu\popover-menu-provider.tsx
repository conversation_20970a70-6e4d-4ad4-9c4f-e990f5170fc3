import React, { PropsWithChildren, useEffect, useMemo, useState } from 'react';
import { IAnchorOrigin, ITransformOrigin, TPlacement } from '@ceridianhcm/everest-cdk/dist';

import { ITriggerOption } from './popover-menu-button';
import { PopoverMenuContext, defaultContext } from './popover-menu-context';
import { TButtonSize, TButtonVariant } from '../..';
import { TIconName } from '../icon';
import { TMenuListInitialPosition, TMenuListItemValue } from '../menu-list';

export interface IPopoverMenuTriggerIconButtonProps {
  variant?: TButtonVariant;
  iconName?: TIconName;
  square?: boolean;
  size?: TButtonSize;
}

export interface IPopoverMenuTriggerButtonProps {
  variant?: TButtonVariant;
  size?: TButtonSize;
  startIcon?: TIconName;
  endIcon?: TIconName;
  fullWidth?: boolean;
}

export interface IPopoverMenuProvider {
  id: string;
  /**
   * @deprecated `selectedMenuId` is no longer used.
   */
  selectedMenuId?: string;
  onChange: (value: TMenuListItemValue) => void;
  buttonAriaLabel: string;
  menuRef?: React.RefObject<HTMLUListElement>;
  triggerRef?: React.RefObject<HTMLElement>;
  buttonAriaDescribedBy?: string;
  buttonLabel?: string;
  triggerOption?: ITriggerOption;
  triggerProps?: IPopoverMenuTriggerIconButtonProps | IPopoverMenuTriggerButtonProps;
  placement?: TPlacement;
  anchorOrigin?: IAnchorOrigin;
  transformOrigin?: ITransformOrigin;
  disabled?: boolean;
  testId?: string;
  maxItems?: number;
  showChevron?: boolean;
}

export const PopoverMenuProvider = (props: PropsWithChildren<IPopoverMenuProvider>): JSX.Element => {
  const {
    id,
    onChange,
    placement,
    anchorOrigin,
    transformOrigin,
    disabled = false,
    children,
    testId,
    buttonAriaLabel,
    buttonAriaDescribedBy,
    buttonLabel,
    triggerOption = 'iconButton',
    maxItems,
    triggerProps,
    showChevron = false,
  } = props;
  const [visible, setVisible] = useState(false);
  const [initialPosition, setInitialPosition] = useState('start' as TMenuListInitialPosition);
  const triggerRef = React.createRef<HTMLElement>();
  const menuRef = React.createRef<HTMLUListElement>();
  const context = useMemo(
    () => ({
      ...defaultContext,
      setVisible,
      visible,
      handleClickOutside,
      initialPosition,
      setInitialPosition,
      id,
      onChange,
      placement,
      anchorOrigin,
      transformOrigin,
      disabled,
      testId,
      buttonAriaLabel,
      buttonAriaDescribedBy,
      buttonLabel,
      triggerOption,
      maxItems,
      showChevron,
      onClose: () => triggerRef?.current?.focus({ preventScroll: true }),
      onOpen: () => menuRef?.current?.focus({ preventScroll: true }),
      triggerProps,
      triggerRef,
      menuRef,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      visible,
      onChange,
      placement,
      anchorOrigin,
      transformOrigin,
      disabled,
      testId,
      buttonAriaLabel,
      buttonAriaDescribedBy,
      buttonLabel,
      triggerOption,
      triggerProps,
      maxItems,
      showChevron,
    ]
  );

  // Replace this logic with useComponentFocused
  function handleClickOutside(e: Event) {
    // inspired by https://stackoverflow.com/questions/32553158/detect-click-outside-react-component
    if (
      // menuRef's parentElement is the whole contextual menu div not just the ul inside the div
      menuRef?.current?.parentElement &&
      !menuRef.current.parentElement.contains(e.target as HTMLElement) &&
      triggerRef?.current &&
      !triggerRef.current.contains(e.target as HTMLElement)
    ) {
      setVisible?.(false);
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [menuRef, triggerRef]);

  return <PopoverMenuContext.Provider value={context}>{children}</PopoverMenuContext.Provider>;
};

PopoverMenuProvider.displayName = 'PopoverMenuProvider';
