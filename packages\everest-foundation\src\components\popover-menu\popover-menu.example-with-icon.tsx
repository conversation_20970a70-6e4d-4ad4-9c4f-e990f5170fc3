export const popoverMenuAndItemsExampleWithIcons = `
  () => {
      const styleWrapper = (child) => 
        <div style={{display: 'flex', justifyContent: 'center', paddingRight: '10px'}}>
          {child}
        </div>;
      const [selectedMenuItemId, setSelectedMenuItemId] = React.useState('');      
      return (          
        <PopoverMenu
          id="my-file-menu"
          buttonAriaLabel="File"
          setSelectedMenuId={setSelectedMenuItemId}
          selectedMenuId={selectedMenuItemId}
        >
          <PopoverMenuItem id="file-item-1">            
            {styleWrapper(
              <Icon
                name="draft"
                fill={'--evr-content-primary-default'}
              />
            )}
            New File
          </PopoverMenuItem>
          <PopoverMenuItem id="file-item-2" divider>
            {styleWrapper(
              <Icon
                name="maximize"
                fill={'--evr-content-primary-default'}
              />
            )}
            New Window</PopoverMenuItem>
          <PopoverMenuItem id="file-item-3">          
            {styleWrapper(
              <Icon
                name="file"
                fill={'--evr-content-primary-default'}
              />
            )}
            Open...</PopoverMenuItem>
          <PopoverMenuItem id="file-item-4" divider>
            {styleWrapper(
              <Icon
                name="folder"
                fill={'--evr-content-primary-default'}
              />
            )}
            Open Folder...
          </PopoverMenuItem>
          <PopoverMenuItem id="file-item-5" disabled>
            {styleWrapper(
              <Icon
                name="save"
                fill={'--evr-inactive-content'}
              />
            )}
            Save As...
          </PopoverMenuItem>
        </PopoverMenu>        
      )
    }
`;
