import { useEffect, useRef, useState } from 'react';
import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { PopoverMenu, PopoverMenuItem, PopoverMenuItemHeading } from './index';
import { Icon } from '../icon';
import { Button } from '../button';

export const scope = {
  PopoverMenu,
  PopoverMenuItem,
  PopoverMenuItemHeading,
  Icon,
  Button,
  useEffect,
  useRef,
  useState,
};

The `PopoverMenu` is a list of choices that temporarily appears when a user interacts with a button.

## Variations

### Default PopoverMenu

There is one default presentation for the `PopoverMenu` consisting of a button with vertical ellipses, a menu, and menu items.

The menu items use the `PopoverMenuItem` component, which is an alias of the <LinkTo kind="Toolbox/Menu List Item/Menu List Item">MenuListItem</LinkTo> component.

The `PopoverMenu` is a controlled component, and the choice of menu item id is specified in state.

export const defaultCode = `() => {
  const initialState = '';
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(initialState);
  function reducer(action) {
    switch (action) {
      case initialState:
        return;
      case 'user-profile':
        console.log('You selected Profile!');
        break;
      case 'user-prefs':
        console.log('You selected Preferences!');
        break;
      case 'user-logout':
        console.log('You selected Logout!');
        break;
      default:
        break;
    }
    setSelectedMenuItemId(initialState);
  }
  useEffect(() => {
    reducer(selectedMenuItemId);
  }, [selectedMenuItemId]);
  return (
    <PopoverMenu id="my-user-menu" buttonAriaLabel="User" onChange={({ id }) => setSelectedMenuItemId(id)}>
      <PopoverMenuItem id="user-profile">Profile</PopoverMenuItem>
      <PopoverMenuItem id="user-prefs">Preferences</PopoverMenuItem>
      <PopoverMenuItem id="user-logout">Logout</PopoverMenuItem>
    </PopoverMenu>
  );
}`;

<CodeExample scope={scope} code={defaultCode} />

### Checkable Menu items

The `checkedIds` prop represents an array of items that should be checked. Set the individual menu item to have role `menuitemcheckbox` when multiple items may be checked or `menuitemradio` when only a single item may be checked.

export const checkableCode = `() => {
  const [checkedIds, setCheckedIds] = useState(['menu-item-1', 'menu-item-3']);
  const [lastSelectedId, setLastSelectedId] = useState('');
  return (
    <PopoverMenu
      id="my-menu"
      testId="my-menu-test-id"
      buttonAriaLabel="View options"
      triggerOption="iconButton"
      onChange={({ id, role }) => {
        // set last selected menu item, not stricly required
        setLastSelectedId(id);
        // menu items with role 'menuitem'
        if (role === 'menuitem') {
          // ...
          return;
        }
        // menu items with role 'menuitemcheckbox'
        if (role === 'menuitemcheckbox') {
          // ...
          if (checkedIds.includes(id)) {
            const index = checkedIds.indexOf(id);
            const checkedIdsCopy = [...checkedIds];
            checkedIdsCopy.splice(index, 1);
            setCheckedIds(checkedIdsCopy);
            return;
          }
          setCheckedIds((prev) => [...prev, id]);
        }
      }}
      checkedIds={checkedIds}
    >
      <PopoverMenuItem id="menu-item-1" role="menuitemcheckbox">
        Always Show Bookmarks Bar
      </PopoverMenuItem>
      <PopoverMenuItem id="menu-item-2" disabled role="menuitemcheckbox">
        Always Show Toolbar in Full Screen
      </PopoverMenuItem>
      <PopoverMenuItem id="menu-item-3" role="menuitemcheckbox" divider>
        Always Show Full URLs
      </PopoverMenuItem>
      <PopoverMenuItem id="menu-item-6">Zoom In</PopoverMenuItem>
      <PopoverMenuItem id="menu-item-7">Zoom Out</PopoverMenuItem>
    </PopoverMenu>
  );
}`;

<CodeExample scope={scope} code={checkableCode} />

### Triggered Element

The `PopoverMenu` can be triggered by either a `button` or an `iconButton`.

#### Button Trigger Element

For a labelled `button`, set `triggerOption` to `button` and specify the `triggerProps` object that satisfies the below interface:

```typescript
interface IPopoverMenuTriggerButtonProps {
  variant?: TButtonVariant;
  size?: TButtonSize;
  startIcon?: TIconName;
  endIcon?: TIconName;
  fullWidth?: boolean;
}
```

If the `triggerOption` is set to `button` but no `triggerProps` are provided, the default `triggerProps` object is as follows:

```typescript
{
  variant: 'secondary',
  size: 'medium',
}
```

Here is an example.

export const buttonCode = `() => {
  const initialState = '';
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(initialState);
  function reducer(action) {
    switch (action) {
      case initialState:
        return;
      case 'export-pdf':
        console.log('You selected PDF!');
        break;
      case 'export-excel':
        console.log('You selected Excel!');
        break;
      case 'export-binary':
        console.log('You selected Binary!');
        break;
      default:
        break;
    }
    setSelectedMenuItemId(initialState);
  }
  useEffect(() => {
    reducer(selectedMenuItemId);
  }, [selectedMenuItemId]);
  return (
    <PopoverMenu
      id="my-export-menu"
      buttonAriaLabel="Export"
      triggerOption="button"
      triggerProps={{
        variant: 'secondaryNeutral',
        startIcon: 'file',
      }}
      buttonLabel="Export"
      onChange={({ id }) => setSelectedMenuItemId(id)}
    >
      <PopoverMenuItem id="export-pdf">PDF</PopoverMenuItem>
      <PopoverMenuItem id="export-excel">Excel</PopoverMenuItem>
      <PopoverMenuItem id="export-binary">Binary</PopoverMenuItem>
    </PopoverMenu>
  );
}`;

<CodeExample scope={scope} code={buttonCode} />

#### ToolbarButton Trigger Element

When `PopoverMenu` is meant to be used within a toolbar, set the `triggerOption` to `toolbarButton` to match the UI specifications.

#### IconButton Trigger Element

For an `iconButton`, set `triggerOption` to `iconButton` and specify a `triggerProps` object that satisfies the below interface:

```typescript
export interface IPopoverMenuTriggerIconButtonProps {
  variant?: TButtonVariant;
  iconName?: TIconName;
  square?: boolean;
  size?: TButtonSize;
}
```

If the `triggerOption` is set to `iconButton` but no `triggerProps` are provided, the default `triggerProps` object is as follows:

```typescript
{
  variant: 'tertiary',
  iconName: 'moreVert',
  square: false,
  size: 'small'
}
```

See the below example with `square: true` and `size: large`.

export const iconButtonCode = `() => {
  const initialState = '';
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(initialState);
  function reducer(action) {
    switch (action) {
      case initialState:
        return;
      case 'pencil-edit-undo':
        console.log('You selected Undo!');
        break;
      case 'pencil-edit-redo':
        console.log('You selected Redo!');
        break;
      case 'pencil-edit-copy':
        console.log('You selected Copy!');
        break;
      default:
        break;
    }
    setSelectedMenuItemId(initialState);
  }
  useEffect(() => {
    reducer(selectedMenuItemId);
  }, [selectedMenuItemId]);
  return (
    <PopoverMenu
      id="my-pencil-edit-menu"
      triggerOption="iconButton"
      triggerProps={{
        variant: 'tertiary',
        iconName: 'edit',
        square: 'true',
        size: 'large'
      }}
      buttonAriaLabel="Edit"
      onChange={({ id }) => setSelectedMenuItemId(id)}
    >
      <PopoverMenuItem id="pencil-edit-undo">Undo</PopoverMenuItem>
      <PopoverMenuItem id="pencil-edit-redo" divider>
        Redo
      </PopoverMenuItem>
      <PopoverMenuItem id="pencil-edit-copy">Copy</PopoverMenuItem>
    </PopoverMenu>
  );
}`;

<CodeExample scope={scope} code={iconButtonCode} />

### Menu Placement and Disable

The menu and button can be disabled with the `disabled` attribute.

The position of the menu on mount can be set with the `placement` prop. The default value is `bottom`. If there isn't enough space on screen for the menu's placement on mount, it will attempt the opposite placement. For example, if there is not enough space at the `bottom`, the component will attempt to place the menu at the `top`. To learn more about positioning see the <LinkTo kind="Toolbox/AnchoredOverlay">AnchoredOverlay</LinkTo> component.

export const popoverMenuPlacementDisabledCode = `() => {
  const styles = {
    row: {
      display: 'flex',
      justifyContent: 'space-evenly',
      flexWrap: 'wrap',
      columnGap: '20px',
    },
  };
  const [selectedMenuFileItemId, setSelectedMenuFileItemId] = useState('');
  const [selectedMenuEditItemId, setSelectedMenuEditItemId] = useState('');
  const [selectedMenuOSItemId, setSelectedMenuOSItemId] = useState('');
  return (
    <div>
      <div style={styles.row}>
        <PopoverMenu id="my-file-menu" buttonAriaLabel="File" onChange={({ id }) => setSelectedMenuFileItemId(id)}>
          <PopoverMenuItem id="file-new">New File</PopoverMenuItem>
          <PopoverMenuItem id="file-open">Open...</PopoverMenuItem>
          <PopoverMenuItem id="file-disabled" disabled>
            Save As...
          </PopoverMenuItem>
        </PopoverMenu>
        <PopoverMenu
          id="my-edit-menu"
          buttonAriaLabel="Edit"
          onChange={({ id }) => setSelectedMenuEditItemId(id)}
          placement="right"
        >
          <PopoverMenuItem id="edit-undo">Undo</PopoverMenuItem>
          <PopoverMenuItem id="edit-redo" divider>
            Redo
          </PopoverMenuItem>
          <PopoverMenuItem id="edit-copy">Copy</PopoverMenuItem>
        </PopoverMenu>
        <PopoverMenu
          id="my-os-menu"
          buttonAriaLabel="Operating System"
          onChange={({ id }) => setSelectedMenuOSItemId(id)}
          placement="top"
          disabled
        >
          <PopoverMenuItem id="os-mac">MacOS</PopoverMenuItem>
          <PopoverMenuItem id="os-linux">Linux</PopoverMenuItem>
          <PopoverMenuItem id="os-win">Windows</PopoverMenuItem>
        </PopoverMenu>
        <PopoverMenu
          id="my-os-menu"
          buttonAriaLabel="Operating System"
          onChange={({ id }) => setSelectedMenuOSItemId(id)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <PopoverMenuItem id="os-mac">MacOS</PopoverMenuItem>
          <PopoverMenuItem id="os-linux">Linux</PopoverMenuItem>
          <PopoverMenuItem id="os-win">Windows</PopoverMenuItem>
        </PopoverMenu>
      </div>
      <br />
      <div style={styles.row}>
        <PopoverMenu
          id="my-file-menu"
          buttonAriaLabel="File"
          buttonLabel="File"
          triggerOption="button"
          triggerProps={{
            variant: 'secondary',
            endIcon: 'chevronDownSmall',
          }}
          onChange={({ id }) => setSelectedMenuFileItemId(id)}
        >
          <PopoverMenuItem id="file-new">New File</PopoverMenuItem>
          <PopoverMenuItem id="file-open">Open...</PopoverMenuItem>
          <PopoverMenuItem id="file-disabled" disabled>
            Save As...
          </PopoverMenuItem>
        </PopoverMenu>
        <PopoverMenu
          id="my-edit-menu"
          buttonAriaLabel="Edit"
          buttonLabel="Edit"
          triggerOption="button"
          triggerProps={{
            variant: 'secondary',
            endIcon: 'chevronDownSmall',
          }}
          onChange={({ id }) => setSelectedMenuEditItemId(id)}
          placement="right"
        >
          <PopoverMenuItem id="edit-undo">Undo</PopoverMenuItem>
          <PopoverMenuItem id="edit-redo" divider>
            Redo
          </PopoverMenuItem>
          <PopoverMenuItem id="edit-copy">Copy</PopoverMenuItem>
        </PopoverMenu>
        <PopoverMenu
          id="my-os-menu"
          buttonAriaLabel="Operating System"
          buttonLabel="Operating System"
          triggerOption="button"
          triggerProps={{
            variant: 'secondary',
            endIcon: 'chevronDownSmall',
          }}
          onChange={({ id }) => setSelectedMenuOSItemId(id)}
          placement="top"
          disabled
        >
          <PopoverMenuItem id="os-mac">MacOS</PopoverMenuItem>
          <PopoverMenuItem id="os-linux">Linux</PopoverMenuItem>
          <PopoverMenuItem id="os-win">Windows</PopoverMenuItem>
        </PopoverMenu>
        <PopoverMenu
          id="my-os-menu"
          buttonAriaLabel="Operating System"
          buttonLabel="Operating System"
          triggerOption="button"
          triggerProps={{
            variant: 'secondary',
            endIcon: 'chevronDownSmall',
          }}
          onChange={({ id }) => setSelectedMenuOSItemId(id)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <PopoverMenuItem id="os-mac">MacOS</PopoverMenuItem>
          <PopoverMenuItem id="os-linux">Linux</PopoverMenuItem>
          <PopoverMenuItem id="os-win">Windows</PopoverMenuItem>
        </PopoverMenu>
      </div>
    </div>
  );
}`;

<CodeExample scope={scope} code={popoverMenuPlacementDisabledCode} />

## Accessing PopoverMenu using ref

Click on the Button to access the PopoverMenu, refer to the console for the element details.

export const refCode = `() => {
  const styles = {
    row: {
      display: 'flex',
      justifyContent: 'space-around',
      flexWrap: 'wrap',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      rowGap: '0.625rem',
    },
  };
  const Row = ({ children }) => <div style={styles.row}>{children}</div>;
  const Column = ({ children }) => <div style={styles.column}>{children}</div>;
  const ref = useRef(null);
  const initialState = '';
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(initialState);
  function reducer(action) {
    switch (action) {
      case initialState:
        return;
      case 'user-profile':
        console.log('You selected Profile!');
        break;
      case 'user-prefs':
        console.log('You selected Preferences!');
        break;
      case 'user-logout':
        console.log('You selected Logout!');
        break;
      default:
        break;
    }
    setSelectedMenuItemId(initialState);
  }
  useEffect(() => {
    reducer(selectedMenuItemId);
  }, [selectedMenuItemId]);
  return (
    <Column>
      <Row>
        <Button
          id="access-element-btn"
          label="Click to access element"
          onClick={() => {
            console.log(ref.current);
          }}
        />
      </Row>
      <Row>
        <PopoverMenu
          id="my-user-menu"
          buttonAriaLabel="User"
          onChange={({ id }) => setSelectedMenuItemId(id)}
          ref={ref}
        >
          <PopoverMenuItem id="user-profile">Profile</PopoverMenuItem>
          <PopoverMenuItem id="user-prefs">Preferences</PopoverMenuItem>
          <PopoverMenuItem id="user-logout">Logout</PopoverMenuItem>
        </PopoverMenu>
      </Row>
    </Column>
  );
}`;

<CodeExample scope={scope} code={refCode} />

#### PopoverMenu with SubMenu **(Beta)**

Submenus are menu sections that expand from a main menu, offering more choices related to a specific topic or category. In order to utilize SubMenu's within PopoverMenu, `PopoverMenuItem` can now include `PopoverMenuItemHeading` and `PopoverMenuItem` as children to achieve this.

The main `PopoverMenuItem` should include the `ref` property to make sure all accessibility and keyboard navigations work as expected.

export const subMenuPopoverCode = `() => {
  const initialState = '';
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(initialState);
  const subMenuRef = useRef(null);
  return (
    <PopoverMenu
      id="my-sub-menu-example"
      buttonAriaLabel="Menu"
      triggerOption="button"
      triggerProps={{
        variant: 'secondary',
        endIcon: 'chevronDownSmall',
      }}
      buttonLabel="Menu"
      onChange={({ id }) => setSelectedMenuItemId(id)}
    >
      <PopoverMenuItem id="submenu-item-1" role="menuitemcheckbox">
        Menu item 1
      </PopoverMenuItem>
      <PopoverMenuItem id="submenu-item-2" role="menuitemcheckbox">
        Menu item 2
      </PopoverMenuItem>
      <PopoverMenuItem id="submenu-item-3" role="menuitemcheckbox" divider>
        Menu item 3
      </PopoverMenuItem>
      <PopoverMenuItem id="menu-item-submenu" ref={subMenuRef}>
        <PopoverMenuItemHeading>SubMenu Heading</PopoverMenuItemHeading>
        <PopoverMenuItem id="sub-list-item-1">SubItem 1</PopoverMenuItem>
        <PopoverMenuItem id="sub-list-item-2">SubItem 2</PopoverMenuItem>
      </PopoverMenuItem>
    </PopoverMenu>
  );
}`;

<CodeExample scope={scope} code={subMenuPopoverCode} />

## How to Use

The default and suggested maximum number of menu items is `7`. This can be adjusted with the `maxItems` prop on `PopoverMenu`.

Related menu items can be visually grouped using the `divider` prop on the `PopoverMenuItem` component.

Menu items should have succinct descriptions.

## Accessibility

The `PopoverMenu` and `PopoverMenuItem`s require distinct ids to function correctly and facilitate assistive technologies.

The menu can be navigated with a keyboard using the `Arrow`, `Home` and `End` keys. Selections are made with the `Enter` and `Space` keys. Jump to a menu item by pressing the first letter of a menu item's text.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
