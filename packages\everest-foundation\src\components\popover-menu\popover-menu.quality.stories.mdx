import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { PopoverMenu, PopoverMenuItem } from './index';
import { useState } from 'react';
import { userEvent } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Popover Menu"
  component={PopoverMenu}
  decorators={[ChromaticDecorators.padStory, ChromaticDecorators.setHeightTo100vh]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    id: 'transport',
    testId: 'transport-menu-test',
    buttonAriaLabel: 'Transport Button Menu',
    buttonLabel: 'Button',
  }}
/>

# Popover Menu

## Live Demo

<Canvas>
  <Story name="Default">
    {(args) => {
      const [selectedMenuItemId, setSelectedMenuItemId] = useState('');
      return (
        <PopoverMenu {...args} onChange={({ id }) => setSelectedMenuItemId(id)}>
          <PopoverMenuItem id="planes-item" testId="planes-item">
            Planes
          </PopoverMenuItem>
          <PopoverMenuItem id="steamboats-item" testId="steamboats-item" disabled>
            Steamboats
          </PopoverMenuItem>
          <PopoverMenuItem id="autogyros-item" testId="autogyros-item">
            Autogyros
          </PopoverMenuItem>
          <PopoverMenuItem id="helicopters-item" testId="helicopters-item">
            Helicopters
          </PopoverMenuItem>
          <PopoverMenuItem id="automobiles-item" testId="automobiles-item">
            Automobiles
          </PopoverMenuItem>
          <PopoverMenuItem id="submarines-item" testId="submarines-item">
            Submarines
          </PopoverMenuItem>
        </PopoverMenu>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default with checked ids"
    play={async () => {
      await userEvent.click(document.getElementById('transport-popover-menu-button'));
    }}
  >
    {(args) => {
      const [selectedMenuItemId, setSelectedMenuItemId] = useState('');
      return (
        <PopoverMenu
          {...args}
          onChange={({ id }) => setSelectedMenuItemId(id)}
          checkedIds={['planes-item', 'helicopters-item']}
        >
          <PopoverMenuItem id="planes-item" testId="planes-item" role="menuitemcheckbox">
            Planes
          </PopoverMenuItem>
          <PopoverMenuItem id="steamboats-item" testId="steamboats-item" disabled role="menuitemcheckbox">
            Steamboats
          </PopoverMenuItem>
          <PopoverMenuItem id="autogyros-item" testId="autogyros-item" role="menuitemcheckbox">
            Autogyros
          </PopoverMenuItem>
          <PopoverMenuItem id="helicopters-item" testId="helicopters-item" role="menuitemcheckbox">
            Helicopters
          </PopoverMenuItem>
          <PopoverMenuItem id="automobiles-item" testId="automobiles-item" role="menuitemcheckbox">
            Automobiles
          </PopoverMenuItem>
          <PopoverMenuItem id="submarines-item" testId="submarines-item" role="menuitemcheckbox">
            Submarines
          </PopoverMenuItem>
        </PopoverMenu>
      );
    }}
  </Story>
</Canvas>

export const maxItems = 10;

<Canvas>
  <Story
    name="Default with overflowing options"
    play={async () => {
      await userEvent.click(document.getElementById('transport-popover-menu-button'));
      for (let i = 0; i < maxItems - 1; i++) {
        await userEvent.keyboard('{ArrowDown}');
      }
    }}
  >
    {(args) => {
      const [selectedMenuItemId, setSelectedMenuItemId] = useState('');
      return (
        <PopoverMenu {...args} onChange={({ id }) => setSelectedMenuItemId(id)} maxItems={maxItems}>
          <PopoverMenuItem id="planes-item" testId="planes-item" role="menuitemcheckbox">
            Planes
          </PopoverMenuItem>
          <PopoverMenuItem id="steamboats-item" testId="steamboats-item" role="menuitemcheckbox">
            Steamboats
          </PopoverMenuItem>
          <PopoverMenuItem id="autogyros-item" testId="autogyros-item" role="menuitemcheckbox">
            Autogyros
          </PopoverMenuItem>
          <PopoverMenuItem id="helicopters-item" testId="helicopters-item" role="menuitemcheckbox">
            Helicopters
          </PopoverMenuItem>
          <PopoverMenuItem id="automobiles-item" testId="automobiles-item" role="menuitemcheckbox">
            Automobiles
          </PopoverMenuItem>
          <PopoverMenuItem id="submarines-item" testId="submarines-item" role="menuitemcheckbox">
            Submarines
          </PopoverMenuItem>
          <PopoverMenuItem id="canoes-item" testId="canoes-item" role="menuitemcheckbox">
            Canoes
          </PopoverMenuItem>
          <PopoverMenuItem id="jetskis-item" testId="jetskis-item" role="menuitemcheckbox">
            Jetskis
          </PopoverMenuItem>
          <PopoverMenuItem id="yachts-item" testId="yachts-item" role="menuitemcheckbox">
            Yachts
          </PopoverMenuItem>
          <PopoverMenuItem id="cars-item" testId="cars-item" role="menuitemcheckbox">
            Cars
          </PopoverMenuItem>
          <PopoverMenuItem id="tractors-item" testId="tractors-item" role="menuitemcheckbox">
            Tractors
          </PopoverMenuItem>
          <PopoverMenuItem id="motorcycles-item" testId="motorcycles-item" role="menuitemcheckbox">
            Motorcycles
          </PopoverMenuItem>
          <PopoverMenuItem id="atvs-item" testId="atvs-item" role="menuitemcheckbox">
            ATVs
          </PopoverMenuItem>
          <PopoverMenuItem id="bicycles-item" testId="bicycles-item" role="menuitemcheckbox">
            Bicycles
          </PopoverMenuItem>
          <PopoverMenuItem id="tricycles-item" testId="tricycles-item" role="menuitemcheckbox">
            Tricycles
          </PopoverMenuItem>
          <PopoverMenuItem id="tanks-item" testId="tanks-item" role="menuitemcheckbox">
            Tanks
          </PopoverMenuItem>
          <PopoverMenuItem id="scooters-item" testId="scooters-item" role="menuitemcheckbox">
            Scooters
          </PopoverMenuItem>
          <PopoverMenuItem id="mopeds-item" testId="mopeds-item" role="menuitemcheckbox">
            Mopeds
          </PopoverMenuItem>
          <PopoverMenuItem id="sailboats-item" testId="sailboats-item" role="menuitemcheckbox">
            Sailboats
          </PopoverMenuItem>
          <PopoverMenuItem id="hoverboards-item" testId="hoverboards-item" role="menuitemcheckbox">
            Hoverboards
          </PopoverMenuItem>
        </PopoverMenu>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Default">
    {(args) => {
      const [selectedMenuItemId, setSelectedMenuItemId] = useState('');
      return (
        <PopoverMenu {...args} onChange={({ id }) => setSelectedMenuItemId(id)} id="transport-disabled" disabled>
          <PopoverMenuItem id="planes-item" testId="planes-item">
            Planes
          </PopoverMenuItem>
          <PopoverMenuItem id="steamboats-item" testId="steamboats-item">
            Steamboats
          </PopoverMenuItem>
        </PopoverMenu>
      );
    }}
  </Story>
</Canvas>
