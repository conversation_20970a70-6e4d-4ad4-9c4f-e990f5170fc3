import React from 'react';
import { TPlacement } from '@ceridianhcm/everest-cdk/dist';
import { act } from '@testing-library/react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { PopoverMenu, PopoverMenuItem, PopoverMenuItemHeading } from './index';

describe('[PopoverMenu]', () => {
  beforeEach(() => {
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  afterEach(() => {
    jest.resetAllMocks();
  });

  const id = 'testing';
  const onChange = jest.fn();
  const buttonAriaLabel = 'Button Label';
  const placement = 'bottom' as TPlacement;
  const disabled = false;
  const testId = 'test-id';
  const maxItems = 7;

  const propsMock = {
    id,
    onChange,
    buttonAriaLabel,
    placement,
    disabled,
    testId,
    maxItems,
  };

  const getButton = () => screen.getByTestId(`${testId}-popover-menu-button`);
  const getMenu = () => screen.getByTestId(`${testId}`);
  const queryMenu = () => screen.queryByTestId(`${testId}`);
  const getMenuItem = (value: string) => screen.getByTestId(`item-${value}`);

  const TestSubPopoverMenu = () => {
    const subMenuRef = React.useRef(null);
    return (
      <PopoverMenu {...propsMock}>
        <PopoverMenuItem id="item-one" testId="item-one">
          One
        </PopoverMenuItem>
        <PopoverMenuItem id="item-two" testId="item-two" ref={subMenuRef}>
          <PopoverMenuItemHeading>SubMenu Heading</PopoverMenuItemHeading>
          <PopoverMenuItem id="sub-list-item-1">SubItem 1</PopoverMenuItem>
          <PopoverMenuItem id="sub-list-item-2">SubItem 2</PopoverMenuItem>
        </PopoverMenuItem>
      </PopoverMenu>
    );
  };

  [
    {
      name: 'Icon Button',
      jsx: (props = propsMock) => {
        return (
          <PopoverMenu {...props}>
            <PopoverMenuItem id="item-one" testId="item-one">
              One
            </PopoverMenuItem>
            <PopoverMenuItem id="item-two" testId="item-two">
              Two
            </PopoverMenuItem>
          </PopoverMenu>
        );
      },
    },
    {
      name: 'Icon Button with triggerProps',
      jsx: (props = propsMock) => (
        <PopoverMenu
          {...props}
          triggerOption="iconButton"
          triggerProps={{ variant: 'primary', iconName: 'rocketship', square: true }}
        >
          <PopoverMenuItem id="item-one" testId="item-one">
            One
          </PopoverMenuItem>
          <PopoverMenuItem id="item-two" testId="item-two">
            Two
          </PopoverMenuItem>
        </PopoverMenu>
      ),
    },
    {
      name: 'Button',
      jsx: (props = propsMock) => {
        return (
          <PopoverMenu {...props} triggerOption="button" buttonLabel="Button">
            <PopoverMenuItem id="item-one" testId="item-one">
              One
            </PopoverMenuItem>
            <PopoverMenuItem id="item-two" testId="item-two">
              Two
            </PopoverMenuItem>
          </PopoverMenu>
        );
      },
    },
    {
      name: 'Button with triggerProps',
      jsx: (props = propsMock) => {
        return (
          <PopoverMenu
            {...props}
            triggerOption="button"
            triggerProps={{ variant: 'tertiaryNeutral', size: 'small', startIcon: 'view' }}
            buttonLabel="Button"
          >
            <PopoverMenuItem id="item-one" testId="item-one">
              One
            </PopoverMenuItem>
            <PopoverMenuItem id="item-two" testId="item-two">
              Two
            </PopoverMenuItem>
          </PopoverMenu>
        );
      },
    },
  ].forEach(function (item) {
    it(`${item.name} should render the button without issue`, () => {
      render(item.jsx());
      expect(getButton()).toBeInTheDocument();
    });

    it(`${item.name} should render the menu when the button is clicked without issue`, async () => {
      render(item.jsx());
      await userEvent.click(getButton());
      expect(getMenu()).toBeInTheDocument();
      expect(getMenuItem('one')).toBeInTheDocument();
      expect(getMenuItem('two')).toBeInTheDocument();
    });

    it(`${item.name} should not display the menu when disabled`, async () => {
      propsMock.disabled = true;
      render(item.jsx());

      await userEvent.click(getButton());
      expect(queryMenu()).toBeNull();
      getButton().focus();
      await userEvent.keyboard(' ');
      expect(queryMenu()).toBeNull();
      getButton().focus();
      await userEvent.keyboard('{enter}');
      expect(queryMenu()).toBeNull();
      propsMock.disabled = false;
    });

    async function keyboardActionTestRender(key: string) {
      render(item.jsx());
      await userEvent.tab();
      await userEvent.keyboard(key);
      expect(getMenu()).toBeInTheDocument();
      expect(getMenuItem('one')).toBeInTheDocument();
      expect(getMenuItem('two')).toBeInTheDocument();
    }

    it(`${item.name} should render the menu and set aria-activedescendant to the first menu item when the enter key is pressed without issue`, async () => {
      await keyboardActionTestRender('{enter}');
      expect(getMenu()).toHaveAttribute('aria-activedescendant', 'item-one');
    });

    it(`${item.name} should render the menu and set aria-activedescendant to the first menu item when the space key is pressed without issue`, async () => {
      await keyboardActionTestRender(' ');
      expect(getMenu()).toHaveAttribute('aria-activedescendant', 'item-one');
    });

    it(`${item.name} should render the menu and set aria-activedescendant to the first menu item when the down arrow key is pressed without issue`, async () => {
      await keyboardActionTestRender('{ArrowDown}');
      expect(getMenu()).toHaveAttribute('aria-activedescendant', 'item-one');
    });

    it(`${item.name} should render the menu when the up arrow key is pressed and set aria-activedescendant to the last menu item without issue`, async () => {
      await keyboardActionTestRender('{ArrowUp}');
      expect(getMenu()).toHaveAttribute('aria-activedescendant', 'item-two');
    });

    it(`${item.name} should call onChange with the appropriate id when a menu item is clicked`, async () => {
      render(item.jsx());
      await userEvent.click(getButton());
      await waitFor(() => {
        getMenuItem('two').click();
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenCalledWith({ id: 'item-two', role: 'menuitem' });
      });
    });

    it(`${item.name} should call onChange with the appropriate id when a menu item is keyed`, async () => {
      render(item.jsx());
      getButton().focus();
      await userEvent.keyboard('{ArrowDown}');
      getMenu().focus();
      await userEvent.keyboard('{ArrowDown}');
      getMenu().focus();
      await userEvent.keyboard('{enter}');
      expect(onChange).toHaveBeenCalledWith({ id: 'item-two', role: 'menuitem' });
      expect(onChange).toHaveBeenCalledTimes(1);
    });
  });

  it('should provide different roles in the onChange argument when menu items are clicked', async () => {
    render(
      <PopoverMenu {...propsMock}>
        <PopoverMenuItem id="item-one" testId="item-one">
          One
        </PopoverMenuItem>
        <PopoverMenuItem id="item-two" testId="item-two" role="menuitemcheckbox">
          Two
        </PopoverMenuItem>
        <PopoverMenuItem id="item-three" testId="item-three" role="menuitemradio">
          Three
        </PopoverMenuItem>
      </PopoverMenu>
    );

    await userEvent.click(getButton());
    await userEvent.click(getMenuItem('three'));
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-three', role: 'menuitemradio' });
    await userEvent.click(getButton());
    await userEvent.click(getMenuItem('two'));
    expect(onChange).toHaveBeenCalledTimes(2);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-two', role: 'menuitemcheckbox' });
    await userEvent.click(getButton());
    await userEvent.click(getMenuItem('one'));
    expect(onChange).toHaveBeenCalledTimes(3);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-one', role: 'menuitem' });
  });

  it('should provide different roles in the onChange argument when menu items are selected by keyboard', async () => {
    const clickButtonAndWaitForMenu = async () => {
      await userEvent.click(getButton());
      expect(getMenu()).toBeInTheDocument();
    };
    const waitForMenuToVanish = () => expect(screen.queryByTestId(`${testId}`)).not.toBeInTheDocument();

    render(
      <PopoverMenu {...propsMock}>
        <PopoverMenuItem id="item-one" testId="item-one">
          One
        </PopoverMenuItem>
        <PopoverMenuItem id="item-two" testId="item-two" role="menuitemcheckbox">
          Two
        </PopoverMenuItem>
        <PopoverMenuItem id="item-three" testId="item-three" role="menuitemradio">
          Three
        </PopoverMenuItem>
      </PopoverMenu>
    );
    // try space key with menuitemradio
    await clickButtonAndWaitForMenu();
    await userEvent.keyboard('{ArrowDown}');
    await userEvent.keyboard('{ArrowDown}');
    await userEvent.keyboard(' ');
    waitForMenuToVanish();
    expect(onChange).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-three', role: 'menuitemradio' });
    // try enter key with menuitemradio
    await clickButtonAndWaitForMenu();
    await userEvent.keyboard('{ArrowDown}');
    await userEvent.keyboard('{ArrowDown}');
    await userEvent.keyboard('{enter}');
    expect(onChange).toHaveBeenCalledTimes(2);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-three', role: 'menuitemradio' });
    waitForMenuToVanish();
    // try space key with menuitemcheckbox
    await clickButtonAndWaitForMenu();
    await userEvent.keyboard('{ArrowDown}');
    await userEvent.keyboard(' ');
    waitForMenuToVanish();
    expect(onChange).toHaveBeenCalledTimes(3);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-two', role: 'menuitemcheckbox' });
    // try enter key with menuitemcheckbox
    await clickButtonAndWaitForMenu();
    await userEvent.keyboard('{ArrowDown}');
    await userEvent.keyboard('{enter}');
    expect(onChange).toHaveBeenCalledTimes(4);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-two', role: 'menuitemcheckbox' });
    waitForMenuToVanish();
    // try space key with menuitem
    await clickButtonAndWaitForMenu();
    await userEvent.keyboard(' ');
    expect(onChange).toHaveBeenCalledTimes(5);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-one', role: 'menuitem' });
    waitForMenuToVanish();
    // try enter key with menuitem
    await clickButtonAndWaitForMenu();
    await userEvent.keyboard('{enter}');
    expect(onChange).toHaveBeenCalledTimes(6);
    expect(onChange).toHaveBeenCalledWith({ id: 'item-one', role: 'menuitem' });
  });

  it('should close submenu with escape key', async () => {
    const clickButtonAndWaitForMenu = async () => {
      await userEvent.click(getButton());
      expect(getMenu()).toBeInTheDocument();
    };

    render(<TestSubPopoverMenu />);

    await clickButtonAndWaitForMenu();
    expect(getMenuItem('two')).toHaveAttribute('aria-expanded', 'false');
    await userEvent.keyboard('{ArrowDown}');
    await act(async () => {
      await userEvent.keyboard(' ');
    });
    expect(getMenuItem('two')).toHaveAttribute('aria-expanded', 'true');
    await act(async () => {
      await userEvent.keyboard('{escape}');
    });
    expect(getMenuItem('two')).toHaveAttribute('aria-expanded', 'false');
  });
});
