import { <PERSON><PERSON>, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { PopoverMenu, PopoverMenuItem } from './index';
import { useState } from 'react';
import Examples from './popover-menu.examples.mdx';

<Meta
  title="Components/Popover Menu"
  component={PopoverMenu}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/C5r0pQ9nTQiump044rnI8s/%F0%9F%A7%AAMenus?node-id=2%3A349',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description:
        'Set the `id` attribute on html elements of the `PopoverMenu`. The button is assigned id `${id}-popover-menu-button` and the menu is given id `${id}-popover-menu`.',
    },
    selectedMenuId: {
      control: '-',
      description: 'Deprecated. React state value holding the id of the selected menu item.',
    },
    onChange: {
      control: '-',
      description:
        'Callback that runs when a menu selection is made and should update React state with the menu item id.',
    },
    buttonLabel: {
      control: 'text',
      description: 'Sets label on button if `triggerOption` is set to `"button"` or `"toolbarButton"`.',
    },
    buttonAriaLabel: {
      control: 'text',
      description: 'Aria label to describe the `PopoverMenu` button.',
    },
    buttonAriaDescribedBy: {
      type: 'string',
      control: 'text',
      description:
        'Specifies one or more ids of elements with the text that provides additional context for the screen reader users.',
    },
    triggerOption: {
      type: 'enum',
      control: 'radio',
      description: 'Sets button trigger type.',
      table: {
        defaultValue: { summary: 'iconButton' },
      },
    },
    triggerProps: {
      type: 'object',
      control: 'object',
      description: 'Specifies the presentation of the trigger element.',
    },
    placement: {
      type: 'enum',
      control: 'select',
      options: ['top', 'bottom', 'right', 'left'],
      description: 'Placement of the menu.',
      table: {
        defaultValue: { summary: 'bottom' },
      },
    },
    anchorOrigin: {
      type: 'object',
      control: 'object',
      description: 'Sets the point on the trigger elements anchor where overlay will attach to.',
    },
    transformOrigin: {
      type: 'object',
      control: 'object',
      description: 'Sets the point on the overlay which will attach to the trigger elements anchor.',
    },
    maxItems: {
      type: 'number',
      control: 'number',
      description: 'Max number of items displayed in overlay.',
      table: {
        defaultValue: { summary: 7 },
      },
    },
    showChevron: {
      description:
        'Shows `chevronDownSmall` icon when overlay is closed and `chevronUpSmall` icon when overlay is open. Applies only when `triggerOption` is set to `"button"` or `"toolbarButton"` and `endIcon` is not provided as part of `triggerProps`.',
      table: {
        defaultValue: { summary: false },
      },
    },
    tabIndex: {
      description: 'Manually set the `tabIndex`.',
      table: {
        defaultValue: { summary: 0 },
      },
    },
    disabled: {
      description: 'Disable the button and menu.',
      table: {
        defaultValue: { summary: false },
      },
    },
    testId: {
      control: 'text',
      description:
        'Sets `data-testid` attribute on the html elements. The button is assigned test id `${testId}-popover-menu-button` and the menu is assigned test id `${testId}-contextual-menu`.',
    },
    checkedIds: {
      control: '-',
      description: 'Specifies ids of menu items with role `menuitemcheckbox` that should be checked.',
    },
  }}
  args={{
    id: 'my-menu',
    testId: 'my-menu-test-id',
    buttonLabel: 'Button',
    buttonAriaLabel: 'More options',
    triggerOption: 'iconButton',
    disabled: false,
    showChevron: false,
  }}
/>

# Popover Menu

<Examples />

## Live Demo

<Canvas>
  <Story name="Popover Menu">
    {(args) => {
      const [checkedIds, setCheckedIds] = useState(['menu-item-1', 'menu-item-3']);
      const [lastSelectedId, setLastSelectedId] = useState('');
      return (
        <PopoverMenu
          {...args}
          onChange={({ id, role }) => {
            // last selected menu item set here, not stricly required
            setLastSelectedId(id);
            // handle menu items with role `menuitem`
            if (role === 'menuitem') {
              // ...
              return;
            }
            // handle menu items with role `menuitemcheckbox`
            if (role === 'menuitemcheckbox') {
              // ...
              if (checkedIds.includes(id)) {
                const index = checkedIds.indexOf(id);
                const checkedIdsCopy = [...checkedIds];
                checkedIdsCopy.splice(index, 1);
                setCheckedIds(checkedIdsCopy);
                return;
              }
              setCheckedIds((prev) => [...prev, id]);
            }
          }}
          checkedIds={checkedIds}
        >
          <PopoverMenuItem id="menu-item-1" role="menuitemcheckbox">
            Always Show Bookmarks Bar
          </PopoverMenuItem>
          <PopoverMenuItem id="menu-item-2" disabled role="menuitemcheckbox">
            Always Show Toolbar in Full Screen
          </PopoverMenuItem>
          <PopoverMenuItem id="menu-item-3" role="menuitemcheckbox" divider>
            Always Show Full URLs
          </PopoverMenuItem>
          <PopoverMenuItem id="menu-item-6">Zoom In</PopoverMenuItem>
          <PopoverMenuItem id="menu-item-7">Zoom Out</PopoverMenuItem>
        </PopoverMenu>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Popover Menu" />
