import React, { PropsWithChildren } from 'react';

import { PopoverMenuButton } from './popover-menu-button';
import { IPopoverMenuProvider, PopoverMenuProvider } from './popover-menu-provider';
import { ContextualMenuWrapper } from '../contextual-menu-wrapper';

export interface IPopoverMenu extends IPopoverMenuProvider {
  checkedIds?: string[];
  tabIndex?: 0 | -1;
}

export const PopoverMenu = React.forwardRef<HTMLElement, PropsWithChildren<IPopoverMenu>>((props, ref): JSX.Element => {
  const { id, buttonAriaLabel, triggerProps, triggerOption, children, checkedIds, tabIndex = 0, maxItems = 7 } = props;
  const popoverMenuButtonId = `${id}-popover-menu-button`;
  const popoverMenuId = `${id}-popover-menu`;

  return (
    <PopoverMenuProvider {...props}>
      <PopoverMenuButton
        ref={ref}
        id={popoverMenuButtonId}
        buttonAriaLabel={buttonAriaLabel}
        triggerProps={triggerProps}
        triggerOption={triggerOption}
        tabIndex={tabIndex}
        // Reverting ariaOwns back to ariaControls, because it makes mobile screen readers unable to navigate PopoverMenu options.
        // AriaOwns was initially introduced to prevent NVDA from reading the Table's aria-label when focus returns to the PopoverMenu trigger button after navigating
        // from menu options located in a portal outside the Table. So as of now we're picking the better of the two bad states.
        // This decision was confirmed with the A11Y team via Teams on Aug 14th 2024
        ariaControls={popoverMenuId}
      />
      <ContextualMenuWrapper
        id={popoverMenuId}
        ariaLabelledBy={popoverMenuButtonId}
        checkedIds={checkedIds}
        maxItems={maxItems}
      >
        {children}
      </ContextualMenuWrapper>
    </PopoverMenuProvider>
  );
});

PopoverMenu.displayName = 'PopoverMenu';
