import { TPlacement } from '@ceridianhcm/everest-cdk';

export const getPlacementOrigins = (
  placement: TPlacement
): {
  anchorOrigin: { vertical: string; horizontal: string };
  transformOrigin: { vertical: string; horizontal: string };
} => {
  switch (placement) {
    case 'bottom':
      return {
        anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      };
    case 'bottomLeft':
      return {
        anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
        transformOrigin: { vertical: 'top', horizontal: 'right' },
      };
    case 'bottomCenter':
      return {
        anchorOrigin: { vertical: 'bottom', horizontal: 'center' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      };
    case 'bottomRight':
      return {
        anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      };
    case 'top':
      return {
        anchorOrigin: { vertical: 'top', horizontal: 'left' },
        transformOrigin: { vertical: 'bottom', horizontal: 'left' },
      };
    case 'topLeft':
      return {
        anchorOrigin: { vertical: 'top', horizontal: 'left' },
        transformOrigin: { vertical: 'bottom', horizontal: 'right' },
      };
    case 'topCenter':
      return {
        anchorOrigin: { vertical: 'top', horizontal: 'center' },
        transformOrigin: { vertical: 'bottom', horizontal: 'left' },
      };
    case 'topRight':
      return {
        anchorOrigin: { vertical: 'top', horizontal: 'right' },
        transformOrigin: { vertical: 'bottom', horizontal: 'left' },
      };
    case 'left':
      return {
        anchorOrigin: { vertical: 'top', horizontal: 'left' },
        transformOrigin: { vertical: 'top', horizontal: 'right' },
      };
    case 'leftCenter':
      return {
        anchorOrigin: { vertical: 'center', horizontal: 'left' },
        transformOrigin: { vertical: 'top', horizontal: 'right' },
      };
    case 'right':
      return {
        anchorOrigin: { vertical: 'top', horizontal: 'right' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      };
    case 'rightCenter':
      return {
        anchorOrigin: { vertical: 'center', horizontal: 'right' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      };
  }
};
