import { <PERSON><PERSON>xample } from '../../../.storybook/doc-blocks/example/example';
import { AlphaBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';
import { <PERSON><PERSON> } from '../button';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, PopoverFooter, PopoverBody } from '.';
import { RadioButtonGroup } from '../radio-button-group';
import { RadioButton } from '../radio-button';
import { Form } from '../../mocks/form/form.tsx';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { useEverestContext } from '../everest-provider';

<AlphaBanner />

export const scope = {
  Popover,
  Button,
  PopoverHeader,
  PopoverBody,
  PopoverFooter,
  RadioButtonGroup,
  RadioButton,
  Form,
  useEverestContext,
};

## How to Use

The Popover component must include `PopoverHeader` and `PopoverBody` as its children. <br /><br />
The visibility of the Popover is controlled by the `open` prop: setting it to true opens the Popover and triggers the `onOpen` callback, while setting it to false closes the Popover and activates the `onClose` callback. <br /><br />
The `onOpen` and `onClose` callback functions are used to perform operations when the Popover opens and closes respectively. <br />
The Popover can be closed by pressing the <kbd>Escape</kbd> keyboard button or by clicking outside the popover element. Closing by <kbd>Escape</kbd> keyboard button can be controlled by a return reason of the `onClose` callback. See [Prevent rendering of Close IconButton and disable Popover closing on Escape key](#prevent-rendering-of-close-iconbutton-and-disable-popover-closing-on-escape-key) for example. <br /><br />
The Close IconButton of the `PopoverHeader` will be rendered only when the `onCloseButtonClick` callback and `closeButtonAriaLabel` prop are passed. <br />

## Responsive

A responsive Popover can be achieved through use of the `useEverestContext` hook provided by <LinkTo kind="Foundations/Everest Provider">EverestProvider</LinkTo>. The following breakpoint ranges should be referenced to determine which value to supply to `size`. This is demonstrated in the [Use useEverestContext to dynamically size the Popover](#use-useeverestcontext-to-dynamically-size-the-popover) example below.

| Breakpoints                 | Size         |
| --------------------------- | ------------ |
| &leq; 767 (xs, sm)          | `fullscreen` |
| &geq; 768 (md, lg, xl, xxl) | custom size  |

The default width and height are set to `auto`, but can be customized using the `size` prop.

```
type TPopoverSize = 'fullscreen' | { width: string; height: string };
```

## Accessibility

`ariaLabelledBy` should be set to `PopoverHeader`'s id and `ariaDescribedBy` should be set to `PopoverBody`'s id or items inside the `PopoverBody` for the screen reader to announce the Popover's children when the popover opens.

## Variations

### Popover with radio buttons

export const popoverWithRadioButtons = `() => {
      const triggerRef = React.useRef(null);
      const [open, setOpen] = React.useState(false);
      const [valueRow, setValueRow] = React.useState('one');
      return (
        <>
          <Button
            id="trigger-button-with-radio-id"
            label="Popover"
            ref={triggerRef}
            onClick={() => setOpen(!open)}
          />
          <Popover
            id="popover-radio"
            open={open}
            triggerRef={triggerRef}
            onClose={(e) => {
              setOpen(false);
              triggerRef && triggerRef.current.focus();
            }}
            ariaLabelledBy= 'popover-custom-header-id'
            ariaDescribedBy= 'popover-custom-body-id'
          >
            <PopoverHeader
              closeButtonAriaLabel={'Close popover'}
              onCloseButtonClick={(e) => {
                setOpen(!open);
              }}
              id="popover-custom-header-id"
            >
              <h3 className="evrHeading3">Custom heading</h3>
            </PopoverHeader>
            <PopoverBody id="popover-custom-body-id">
              <h4 className="evrHeading4">Radio Button Group inside popover</h4>
              <div style={{paddingBottom: '16px'}}>
                <RadioButtonGroup
                    id="radio-button-group"
                    onChange={value => setValueRow(value)}
                    groupName="RowGroup"
                    label="Group One"
                >
                    <RadioButton
                    id="radio-button-1"
                    value="one"
                    checked={'one'=== valueRow }
                    label="Option One" />
                    <RadioButton
                    id="radio-button-2"
                    value="two"
                    checked={'two'=== valueRow }
                    label="Option Two" />
                </RadioButtonGroup>
              </div>
            </PopoverBody>
            <PopoverFooter id="popover-footer-id">
              <div style={{display: 'flex', justifyContent:'end'}}>
                <Button
                  id="save-button-id"
                  label="Save"
                  onClick={() => {
                    alert("Save!")
                  }}
                />
              </div>
            </PopoverFooter>
          </Popover>
        </>
      );
}`;

<CodeExample scope={scope} code={popoverWithRadioButtons} />

### Popover Placement

The placement prop determines the popover's initial position, defaulting to `bottom`. For custom positioning, combine `anchorOrigin` and `transformOrigin` with an `offset`.

The following example has set the `placement` to `leftCenter`.

export const popoverPlacement = `() => {
      const triggerRef = React.useRef(null);
      const [open, setOpen] = React.useState(false);
      const [valueRow, setValueRow] = React.useState('one');
      return (
        <>
          <Button
            id="trigger-button-placement-id"
            label="Popover"
            ref={triggerRef}            
            onClick={() => setOpen(!open)}
          />
          <Popover
            id="popover-placement"
            open={open}
            triggerRef={triggerRef}
            onClose={(e) => {
              setOpen(false);
              triggerRef && triggerRef.current.focus();
            }}
            ariaLabelledBy= 'popover-placement-header-id'
            ariaDescribedBy= 'popover-placement-body-id'
            placement="leftCenter"
          >
            <PopoverHeader
              closeButtonAriaLabel={'Close popover'}
              onCloseButtonClick={(e) => {
                setOpen(!open);
              }}
              id="popover-placement-header-id"
            >
              <h3 className="evrHeading3">Custom heading</h3>
            </PopoverHeader>
            <PopoverBody id="popover-placement-body-id">
              <h4 className="evrHeading4">Radio Button Group inside popover</h4>
              <div style={{paddingBottom: '16px'}}>
                <RadioButtonGroup
                    id="radio-button-group"
                    onChange={value => setValueRow(value)}
                    groupName="RowGroup"
                    label="Group One"
                >
                    <RadioButton
                    id="radio-button-1"
                    value="one"
                    checked={'one'=== valueRow }
                    label="Option One" />
                    <RadioButton
                    id="radio-button-2"
                    value="two"
                    checked={'two'=== valueRow }
                    label="Option Two" />
                </RadioButtonGroup>
              </div>
            </PopoverBody>
            <PopoverFooter id="popover-placement-footer-id">
              <div style={{display: 'flex', justifyContent:'end'}}>
                <Button
                  id="save-button-id"
                  label="Save"
                  onClick={() => {
                    alert("Save!")
                  }}
                />
              </div>
            </PopoverFooter>
          </Popover>
        </>
      );
}`;

<CodeExample scope={scope} code={popoverPlacement} />

### Popover with a form

export const popoverWithAForm = `() => {
      const triggerRef = React.useRef(null);
      const [open, setOpen] = React.useState(false);
      return (
        <>
          <Button
            id="trigger-button-with-form-id"
            label="Popover"
            ref={triggerRef}
            onClick={() => setOpen(!open)}
          />
          <Popover
            id="popover-form"
            open={open}
            triggerRef={triggerRef}
            onClose={(e) => {
              setOpen(false);
              triggerRef && triggerRef.current.focus();

            }}
            ariaLabelledBy='popover-custom-header-id-form'
            ariaDescribedBy='popover-custom-body-id-form'
          >
            <PopoverHeader
              closeButtonAriaLabel={'Close popover'}
              onCloseButtonClick={(e) => {
                setOpen(!open);
              }}
              id="popover-custom-header-id-form"
            >
              <h3 className="evrHeading3">Custom heading</h3>
            </PopoverHeader>
            <PopoverBody id="popover-custom-body-id-form">
              <Form/>
            </PopoverBody>
            <PopoverFooter id="popover-footer-id-form">
              <div style={{display: 'flex', justifyContent:'end'}}>
                <Button
                  id="save-button-id-form"
                  label="Save"
                  onClick={() => {
                    alert("Save!")
                  }}
                />
              </div>
            </PopoverFooter>
          </Popover>
        </>
      );

}`;

<CodeExample scope={scope} code={popoverWithAForm} />

### Prevent rendering of Close IconButton and disable Popover closing on Escape key

The Close IconButton of the Popover will only be rendered when the `onCloseButtonClick` callback and `closeButtonAriaLabel` prop are passed. The Popover closing by pressing the <kbd>Escape</kbd> keyboard button can be prevented by the return reason of the `onClose` i.e. `onClose` return a reason `escapeKeyDown`.

export const closeButtonCode = `() => {
      const triggerRef = React.useRef(null);
      const [open, setOpen] = React.useState(false);
      const [valueRow, setValueRow] = React.useState('one');
      return (
        <>
          <Button
            id="trigger-button-render-closebutton-id"
            label="Popover"
            ref={triggerRef}
            onClick={() => setOpen(!open)}
          />
          <Popover
            id="popover-radio"
            open={open}
            triggerRef={triggerRef}
            onClose={(e) => {
              if (e && e.reason!=='escapeKeyDown'){ 
                setOpen(false);
                triggerRef && triggerRef.current.focus();
              }
            }}
            ariaLabelledBy= 'popover-custom-header-id'
            ariaDescribedBy= 'popover-custom-body-id'
          >
            <PopoverHeader
              id="popover-custom-header-id"
            >
              <h3 className="evrHeading3">Custom heading</h3>
            </PopoverHeader>
            <PopoverBody id="popover-custom-body-id">
              <h4 className="evrHeading4">Radio Button Group inside popover</h4>
              <div style={{paddingBottom: '16px'}}>
                <RadioButtonGroup
                  id="radio-button-group-2"
                    onChange={value => setValueRow(value)}
                    groupName="RowGroup"
                    label="Group One"
                >
                    <RadioButton
                    id="radio-button-3"
                    value="one"
                    checked={'one'=== valueRow }
                    label="Option One" />
                    <RadioButton
                    id="radio-button-4"
                    value="two"
                    checked={'two'=== valueRow }
                    label="Option Two" />
                </RadioButtonGroup>
              </div>
            </PopoverBody>
            <PopoverFooter id="popover-footer-id">
              <div style={{display: 'flex', justifyContent:'end'}}>
                <Button
                  id="save-button-id"
                  label="Save"
                  onClick={() => {
                    alert("Save!")
                  }}
                />
              </div>
            </PopoverFooter>
          </Popover>
        </>
      );
}`;

<CodeExample scope={scope} code={closeButtonCode} />

### Use `useEverestContext` to dynamically size the Popover

This example demonstrates a responsive Popover by dynamically updating the `size` prop using Everest Provider's `breakpoint` property from the `useEverestContext` hook.

export const responsivePopover = `() => {
        const { breakpoint } = useEverestContext();
        const [open, setOpen] = React.useState(false);
        const [size, setSize] = React.useState(undefined);
        const triggerBtnUseEverestContext = React.useRef(null);
        React.useEffect(() => {
          switch (breakpoint) {
            case 'xs':
            case 'sm':
              setSize('fullscreen');
              break;
            case 'md':
              setSize({width: '300px', height: '200px'});
              break;
            case 'lg':
              setSize({width: '400px', height: '300px'});
              break;
            case 'xl':
            case 'xxl':
              setSize({width: '500px', height: '400px'});
              break;
            default:
              setSize({width: '300px', height: '200px'});
          }
        }, [breakpoint]);

        const getBreakpointAndSizeLabel = () => {
          let sizeLabel = size;
          if (typeof size === 'object') {
            sizeLabel = 'Width:' + size.width + ' & Height: ' + size.height
          }
          return '(Breakpoint: ' + breakpoint + ') (Size: ' + sizeLabel + ')';
        }

        return (
          <>
            <Button
              id="trigger-button-use-everest-context"
              label={'Responsive Example ' + getBreakpointAndSizeLabel()}
              ref={triggerBtnUseEverestContext}
              onClick={() => setOpen(!open)}
            />
            <Popover
              id="popover-use-everest-context-example"
              open={open}
              triggerRef={triggerBtnUseEverestContext}
              ariaLabelledBy="popover-heading-id-use-everest-context"
              ariaDescribedBy="popover-body-id-use-everest-context"
              size={size}
              onClose={() => {
                setOpen(false);
                triggerBtnUseEverestContext && triggerBtnUseEverestContext.current.focus();
              }}
            >
              <PopoverHeader
                closeButtonAriaLabel={'close popover'}
                onCloseButtonClick={(e) => {
                  setOpen(false);
                  triggerBtnUseEverestContext && triggerBtnUseEverestContext.current.focus();
                }}
                id="popover-header-id-use-everest-context"
              >
                <h3 id="header-id" className="evrHeading3">
                  Heading
                </h3>
              </PopoverHeader>
              <PopoverBody id="popover-body-id-use-everest-context">
                <p className="evrBodyText">{getBreakpointAndSizeLabel()}</p>
              </PopoverBody>
              <PopoverFooter id="popover-footer-id-use-everest-context">
                <p className="evrBodyText">Footer</p>
              </PopoverFooter>
            </Popover>
          </>
        );

}`;

<CodeExample scope={scope} code={responsivePopover} />
