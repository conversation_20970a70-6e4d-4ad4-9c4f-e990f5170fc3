# Popover

## Summary

Research and document implementations for the Everest Popover.

- Start Date: 2023-04-04
- Figma link: https://www.figma.com/file/A38hY1rPdCEZNVe7fZjQfY/%F0%9F%A7%AAModal?node-id=4524-20999&t=9EhYGcbM4DMJi57K-0

## Detailed Design

Popover anatomy consists of the following and is divided into sections:

- **Popover Base (mandatory):** - The parent container which sets the height and width of the Popover.
- **Header (mandatory):** - Heading of the Popover and a mandatory close button.
- **Body (mandatory):** - Takes different components to be displayed within the popover.
- **Footer (optional):** - The footer of the popover can include buttons or tags

## API

### Popover

1.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the popover.
2.  **id**: `string`
    Mandatory. Sets the id of the popover.
3.  **children**: `ReactNode | undefined`
    Optional. Sets the section contents of the popover.
4.  **open**: `boolean`
    Open the Popover when it is set to true. Close the Popover when it is set to false.
5.  **placement**: `string`
    Optional. Placement of the popover. Default is "bottom". All options are "top", "topLeft", "topRight", "left", "right", "bottom", "bottomLeft" and "bottomRight"
6.  **ariaLabelledBy**: `string | undefined`
    Optional. Sets the value for the **aria-labelledby** attribute of the popover component. Recommneded data should be popover header id or title
7.  **ariaDescribedBy**: `string | undefined`
    Optional. Sets the value for the **aria-describedby** attribute of the popover component. Recommneded data should be popover body id or title
8.  **onOpen**: `() => void | undefined`
    Optional. Sets a callback function that is executed when Popover is opened.
9.  **onClose**: `() => void | undefined`
    Optional. Sets a callback function that is executed when Popover is closed.

### PopoverHeader

1.  **id**: `string`
    Mandatory. Sets the id of the header.
2.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the header.
3.  **title**: `string`
    Mandatory. Sets the heading.
4.  **closeButtonAriaLabel**: string
    Mandatory. Sets the aria label on the close button
5.  **onCloseButtonClick**: `() => void | undefined`
    Optional. Sets a callback function that is executed on click of the close button.

### PopoverBody

1.  **id**: `string`
    Mandatory. Sets the id of the body.
2.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the body.
3.  **children**: `ReactNode | undefined`
    Optional. Sets the content of the popover.

### PopoverFooter

1.  **id**: `string`
    Mandatory. Sets the id of the footer.
2.  **testId**: `string | undefined`
    Optional. Sets **data-test-id** attribute on the footer.
3.  **children**: `ReactNode | undefined`
    Optional. Sets the footer content of the popover.

## Usage

For the Popover base component we will utilize AnchoredOverlay component

```
()=>{
    const [open, setOpen] = useState(false);
    return (
        <Popover id="popover_id"  onClose={} onOpen={} open={open}>
            <PopoverHeader id="header_id" title="Heading" onCloseButtonClick={()=>{setOpen(false)}} />
            <PopoverBody>
                 <div>Hello World!</div>
            </PopoverBody>
            <PopoverFooter id="footer_id">
                <button>Click me!</button>
            </PopoverFooter>
        </Popover>
    )
}

```

PopoverHeader will also utilize the internal OverlayHeader component like ModalHeader does

```
const PopoverHeader = ({ content } ) => {
    return (
        <OverlayHeader id="overlay-header-id" title="Heading" onCloseButtonClick={()=>{setOpen(false)}} />
    )
}

```

PopoverBody will utilize the internal OverlayBody component, as part of this we will refactor ModalBody to use OverlayBody component as well

```
const PopoverBody = ({ content } ) => {
    return (
        <OverlayBody id="overlay-body-id"  />
    )
}
```

PopoverFooter will utilize the internal OverlayFooter component, as part of this we will refactor ModalFooter to use OverlayFooter component as well

```
const PopoverFooter = ({ content } ) => {
    return (
        <OverlayFooter id="overlay-footer-id"  />
    )
}
```

## Accessibility

- Screenreaders by default will announce the heading and content text when focused.
- <ESC> keyboard button is set to close the popover.
- FocusTrap will be set inside the popover until an action is performed.

## Q&A

**Should popover close when clicked outside the popover area**

For the first iteration, we will be implementing click away functionality like popover menu does.

**Should popover always be placed at the bottom of the trigger element**
The default position will be bottom. The position can be overwritten by the placement prop anytime.

## Future Considerations

As popover component matures overtime we can add PopoverBody templates that can be added to assist in faster developement for our users.

## Other Design Systems

### Material UI - https://mui.com/material-ui/react-popover/

- Supports mouser over and click away interactions

### Shopify Polaris - https://polaris.shopify.com/components/overlays/popover

- Gives more controls to the widths and heights of the popover relative to the anchor with props like: fluidContent, fullWidth

- In terms of accessibility like how it allows users to illustrate the type of popover by using:
  ariaHaspopup? boolean | "false" | "true" | "dialog" | "grid" | "listbox" | "menu" | "tree"

### NextUI - https://nextui.org/docs/components/popover

- Contains the trigger inside the popover component

## Required PBIs

###EAI-461 - Popover architecture
###EAI-462 - Create Popover component

## Acceptance Criteria

- Component to be named `<Popover>`
- Build a component and setup in Storybook (Components, Foundations, Automation)
- Styles are on par with Design specs in Figma
- Verify the following:
  - API works as intended
    - testId
    - children
    - title
    - onCloseButtonClick
    - open
    - onOpen
    - onClose
  - Verify popover can take in custom child components
  - Accessibility (aria attributes, tab order, screen reader callouts, mouse, and keyboard interactivity)
- Unit and integration tests implemented

## Changelog

10/17/2023: EDS-3564 - updated onClick callback to return a reason 'escapeKeyDown' when escape button is pressed and 'lightBoxClick' when clicked outside.
