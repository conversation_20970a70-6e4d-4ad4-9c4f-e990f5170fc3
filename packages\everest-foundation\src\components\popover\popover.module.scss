@use '../../variables.scss';
@use '../../mixins.scss' as mixins;

.evrPopover {
  background-color: var(--evr-surfaces-primary-default);
  border: var(--evr-border-width-thin-px);
  box-shadow: var(--evr-depth-06);
  isolation: isolate;
  outline: none;
  display: flex;
  flex-direction: column;

  &.fullscreen {
    @include mixins.fullscreen;
  }

  & .containerHeader {
    display: flex;
    padding: var(--evr-spacing-sm);
  }

  & .containerBody {
    display: flex;
    padding: 0 var(--evr-spacing-sm);
    overflow-y: auto;
  }

  & .containerFooter {
    display: flex;
    padding: var(--evr-spacing-sm);
    padding-bottom: var(--evr-spacing-md);
  }

  @media (min-width: variables.$overlayMobileScreenWidth) {
    & .containerFooter {
      padding: var(--evr-spacing-sm);
    }
  }
}

.evrPopoverBackgroundOverlay {
  @include mixins.fullscreen;
}
