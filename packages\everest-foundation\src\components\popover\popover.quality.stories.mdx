import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { <PERSON>over, PopoverHeader, PopoverFooter, PopoverBody } from '.';
import { Button } from '../button';
import { useEffect, useState } from 'react';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Popover"
  component={Popover}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: {
        breakpointXs: { disable: true }, // disable mobile default
        breakpointLg: defaultModes['breakpointLg'],
      },
    },
  }}
  args={{
    id: 'popover',
    testId: 'popover-test-id',
    placement: 'bottom',
  }}
/>

export const PopoverWrapper = ({ defaultOpen = true, placement = 'bottom', borderRadius = '', size = '', offset }) => {
  const triggerRef = React.useRef(null);
  const [open, setOpen] = React.useState(defaultOpen);
  return (
    <>
      <Button id="trigger-button-with-id" label="Popover" ref={triggerRef} onClick={() => setOpen(!open)} />
      <Popover
        testId="popover-test-id"
        open={open}
        triggerRef={triggerRef}
        ariaLabelledBy="popover-custom-header-id"
        ariaDescribedBy="popover-custom-body-id"
        placement={placement}
        borderRadius={borderRadius}
        size={size}
        offset={offset}
        onClose={(e) => {
          setOpen(false);
          triggerRef && triggerRef.current.focus();
        }}
      >
        <PopoverHeader
          id="popover-custom-header-id"
          closeButtonAriaLabel={'Close popover'}
          onCloseButtonClick={(e) => {
            setOpen(!open);
          }}
        >
          <h3 className="evrHeading3">Popover Custom heading</h3>
        </PopoverHeader>
        <PopoverBody id="popover-custom-body-id">
          <div className="evrBodyText">This is the contents inside PopoverBody</div>
        </PopoverBody>
        <PopoverFooter id="popover-footer-id">
          <div className="evrBodyText" style={{ display: 'flex', justifyContent: 'end' }}>
            <p className="evrBodyText"> Popover Custom Footer </p>
          </div>
        </PopoverFooter>
      </Popover>
    </>
  );
};

# Popover

## Live Demo

<Canvas>
  <Story name="Default">
    <PopoverWrapper />
  </Story>
</Canvas>

<Canvas>
  <Story name="Placement">
    <PopoverWrapper placement="right" />
  </Story>
</Canvas>

<Canvas>
  <Story name="BorderRadius">
    <PopoverWrapper borderRadius="--evr-radius-xs" />
  </Story>
</Canvas>

<Canvas>
  <Story name="Size">
    <PopoverWrapper size={{ height: '350px', width: '800px' }} />
  </Story>
</Canvas>

<Canvas>
  <Story name="Offset">
    <PopoverWrapper offset={{ vertical: '50px', horizontal: '50px' }} />
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    <PopoverWrapper defaultOpen={false} />
    <Button label="dummy focusable button" />
  </Story>
</Canvas>
