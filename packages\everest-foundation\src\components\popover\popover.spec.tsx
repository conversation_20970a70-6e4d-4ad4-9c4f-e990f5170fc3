import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { IPopover, Popover, PopoverBody, PopoverFooter, PopoverHeader } from '.';

const popoverId = 'popover-id';
const popoverHeaderId = 'overlay-header-id';
const popoverBodyId = 'overlay-body-id';
const popoverFooterId = 'overlay-footer-id';

const popoverTestid = 'test';
const popoverLightboxTestid = `${popoverTestid}-popover-lightbox`;
const popoverHeaderTestid = 'test-overlay-header-id';
const popoverBodyTestid = 'test-overlay-body-id';
const popoverFooterTestid = 'test-overlay-footer-id';
const onOpen = jest.fn();
const onClose = jest.fn();
const mockTrigger = document.createElement('button');
mockTrigger.setAttribute('id', 'trigger-button');
const mockTriggerRef = { current: mockTrigger };

const mockProps: IPopover = {
  id: popoverId,
  testId: popoverTestid,
  open: true,
  onOpen: onOpen,
  onClose: onClose,
  ariaLabelledBy: popoverHeaderId,
  ariaDescribedBy: popoverBodyId,
  triggerRef: mockTriggerRef,
  placement: 'top',
};
const renderPopover = (open: boolean) => {
  return (
    <Popover {...mockProps} open={open}>
      <PopoverHeader
        id={popoverHeaderId}
        testId={popoverHeaderTestid}
        closeButtonAriaLabel={''}
        onCloseButtonClick={() => null}
      >
        <h3>Heading</h3>
      </PopoverHeader>
      <PopoverBody id={popoverBodyId} testId={popoverBodyTestid}>
        <div>Hello world!</div>
      </PopoverBody>
      <PopoverFooter id={popoverFooterId} testId={popoverFooterTestid}>
        <div>Footer</div>
      </PopoverFooter>
    </Popover>
  );
};

describe('Popover', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('should open or close as expected', () => {
    it('should dispatch onClose when popover is open then closes', async () => {
      const { rerender } = render(renderPopover(true));
      await waitFor(() => expect(onOpen).not.toHaveBeenCalled());
      rerender(renderPopover(false));
      await waitFor(() => expect(onClose).toHaveBeenCalled());
    });

    it('should dispatch onOpen when popover is closed then opens', async () => {
      const { rerender } = render(renderPopover(false));
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderPopover(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
    });

    it('should run onOpen once and onClose once when starting closed, then opened, then closed ', async () => {
      const { rerender } = render(renderPopover(false));
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderPopover(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalledTimes(1));
      rerender(renderPopover(false));
      await waitFor(() => expect(onClose).toHaveBeenCalledTimes(1));
    });

    it('should run onOpen once and onClose once when starting open, then closed ', async () => {
      const { rerender } = render(renderPopover(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderPopover(false));
      await waitFor(() => {
        expect(onClose).toHaveBeenCalledTimes(1);
        expect(onOpen).toHaveBeenCalledTimes(1);
      });
    });

    it('onClose should return a reason escapeKeyDown when Escape keyboard button is pressed', async () => {
      render(renderPopover(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
      await userEvent.keyboard('{Escape}');
      expect(onClose).toHaveBeenCalledWith({ reason: 'escapeKeyDown' });
    });
  });

  describe('click outside', () => {
    it('should call onClose function on popover when clicked outside', async () => {
      render(renderPopover(true));
      await userEvent.click(screen.getByTestId(popoverTestid));
      expect(onClose).toHaveBeenCalledTimes(0);
      const lightboxEl = screen.getByTestId(popoverLightboxTestid);

      await userEvent.click(lightboxEl);
      expect(onClose).toHaveBeenCalledWith({ reason: 'lightBoxClick' });
    });
  });
});
