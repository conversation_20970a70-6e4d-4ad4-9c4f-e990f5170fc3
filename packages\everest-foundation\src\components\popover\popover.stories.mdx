import { <PERSON><PERSON>, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { useState, useEffect, useRef } from 'react';
import { Popover, PopoverHeader, PopoverBody, PopoverFooter } from '.';
import { Button } from '../button';
import Examples from './popover.examples.mdx';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';

<Meta
  title="Everest Labs/Components/Popover"
  component={Popover}
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: '',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Sets the `id` attribute on the popover',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets data-testid',
    },
    open: {
      description: 'Opens or closes the popover.',
      table: {
        defaultValue: { summary: false },
      },
    },
    onOpen: {
      control: '-',
      description: 'Callback that runs when the popover opens.',
    },
    onClose: {
      control: '-',
      description:
        "Callback that runs when the popover closes. Returns an object {reason: 'escapeKeyDown'} when the escape key is pressed and {reason: 'lightBoxClick'} when LightBox is clicked.",
    },
    ariaLabelledBy: {
      type: 'string',
      control: '-',
      description: 'Id for either the PopoverHeader or its internal content.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: '-',
      description: 'Id of the PopoverBody.',
    },
    triggerRef: {
      control: '-',
      description: 'Set reference to the trigger element.',
    },
    placement: {
      type: 'enum',
      control: 'select',
      options: [
        'bottom',
        'bottomCenter',
        'bottomLeft',
        'bottomRight',
        'left',
        'leftCenter',
        'right',
        'rightCenter',
        'top',
        'topCenter',
        'topLeft',
        'topRight',
      ],
      description: "Specify the popover's position in relation to the trigger element.",
      table: {
        defaultValue: { summary: 'bottomCenter' },
      },
    },
    anchorOrigin: {
      control: '-',
      description:
        "Specify the point on the trigger element's anchor for the popover's attachment. Combine it with `transformOrigin` to customize the `placement`.",
    },
    transformOrigin: {
      control: '-',
      description:
        "Specify the point on the popover that attaches to the trigger element's anchor. Combine it with `anchorOrigin` to customize the `placement`.",
    },
    offset: {
      type: 'string',
      control: '-',
      description:
        "Specifies the margin between the trigger element and the popover, applicable to both vertical and horizontal orientations. Supports values in 'rem' and 'px' units.",
    },
    borderRadius: {
      control: 'select',
      options: ['Undefined', '--evr-radius-xs'],
      mapping: {
        Undefined: undefined,
        '--evr-radius-xs': '--evr-radius-xs',
      },
      description: 'Sets the border-radius.',
    },
    size: {
      type: 'object',
      control: 'object',
      description:
        "Allows setting the popover's size, supporting either a fullscreen mode or customized width and height dimensions.",
    },

}}
args={{
    id: 'example-id',
    testId: 'example-test-id',
    open: false,
    ariaLabelledBy: 'popoverHeader-id',
    ariaDescribedBy: 'popoverBody-id',
    placement: 'bottom',
    size: {
      height: 'auto',
      width: 'auto',
    },
  }}
/>

# Popover

<Examples />

## Live Demo

<Canvas>
  <Story name="Popover">
    {(args) => {
      const triggerRef = useRef(null);
      const [{ open }, updateArgs] = useArgs();
      return (
        <div>
          <Button id="trigger-button-id" label="Popover" ref={triggerRef} onClick={() => updateArgs({ open: !open })} />
          <Popover
            {...args}
            open={open}
            triggerRef={triggerRef}
            onOpen={(e) => {
              action('onOpen')(e);
            }}
            onClose={(e) => {
              action('onClose')(e);
              updateArgs({ open: false });
              triggerRef && triggerRef.current.focus();
            }}
          >
            <PopoverHeader
              closeButtonAriaLabel={'close popover'}
              onCloseButtonClick={(e) => {
                action('onCloseButtonClick')(e);
                updateArgs({ open: false });
              }}
              id="popoverHeader-id"
            >
              <h3 id="header-id" className="evrHeading3">
                Heading
              </h3>
            </PopoverHeader>
            <PopoverBody id="popoverBody-id">
              <p className="evrBodyText">Hello world!</p>
            </PopoverBody>
            <PopoverFooter id="popoverFooter-id">
              <p className="evrBodyText">Footer</p>
            </PopoverFooter>
          </Popover>
        </div>
      );
    }}
  </Story>
</Canvas>

## Props

### Popover Props

<ArgsTable story="Popover" />

### PopoverHeader Props

<ArgsTable of={PopoverHeader} />

### PopoverBody Props

<ArgsTable of={PopoverBody} />

### PopoverFooter Props

<ArgsTable of={PopoverFooter} />
