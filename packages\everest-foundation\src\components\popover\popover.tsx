import React, { PropsWithChildren, ReactNode, RefObject, useCallback, useEffect, useRef } from 'react';
import { AnchoredOverlay, IAnchorOrigin, ITransformOrigin, IOffset, TPlacement } from '@ceridianhcm/everest-cdk';
import classNames from 'classnames';

import { getPlacementOrigins } from './popover-helper';
import { FocusTrap, mergeRefs } from '../../utils';
import {
  LightBox,
  IDialogBase,
  Overlay,
  OverlayBody,
  OverlayFooter,
  OverlayHeader,
  TDialogCloseEventReason,
} from '../overlay';

import styles from './popover.module.scss';

export type TPopoverPlacement = TPlacement;

type TPopoverSize = 'fullscreen' | { width: string; height: string };
export interface IPopover extends IDialogBase {
  triggerRef: RefObject<HTMLElement>;
  placement?: TPopoverPlacement;
  anchorOrigin?: IAnchorOrigin;
  transformOrigin?: ITransformOrigin;
  offset?: IOffset;
  size?: TPopoverSize;
  borderRadius?: '--evr-radius-xs';
}

export const Popover = React.forwardRef<HTMLDivElement, PropsWithChildren<IPopover>>((props, ref): JSX.Element => {
  const {
    id,
    testId,
    children,
    open,
    onOpen,
    onClose,
    ariaLabelledBy,
    ariaDescribedBy,
    triggerRef,
    placement = 'bottom',
    anchorOrigin,
    transformOrigin,
    offset,
    size = { height: 'auto', width: 'auto' },
    borderRadius,
  } = props;

  const contentContainerRef = useRef<HTMLDivElement>(null);
  const lightBoxRef = useRef<HTMLDivElement>(null);
  const reasonRef = useRef<TDialogCloseEventReason | null>(null);
  const onOpenPrevRef = useRef(open ? null : false);

  const { anchorOrigin: getAnchorOriginFromPlacement, transformOrigin: getTransformOriginFromPlacement } =
    getPlacementOrigins(placement);

  const getAnchorOrigin = () =>
    anchorOrigin || (transformOrigin ? { vertical: 'bottom', horizontal: 'left' } : getAnchorOriginFromPlacement);

  const getTransformOrigin = () =>
    transformOrigin || (anchorOrigin ? { vertical: 'top', horizontal: 'left' } : getTransformOriginFromPlacement);

  const getAnchorOverlayHeight = useCallback(() => {
    if (typeof size === 'string') {
      return size;
    }
    return size.height;
  }, [size]);

  const getAnchorOverlayWidth = useCallback(() => {
    if (typeof size === 'string') {
      return size;
    }
    return size.width;
  }, [size]);

  let headerChild: ReactNode;
  let bodyChild: ReactNode;
  let footerChild: ReactNode;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  React.Children.forEach(children, (child: any) => {
    switch (child.type) {
      case OverlayHeader:
        headerChild = child;
        break;
      case OverlayFooter:
        footerChild = child;
        break;
      case OverlayBody:
        bodyChild = child;
        break;
    }
  });

  const handleEscKey = useCallback(
    (e: React.KeyboardEvent) => {
      e.stopPropagation();
      if (e.key === 'Escape' && open) {
        reasonRef.current = 'escapeKeyDown';
        setTimeout(() => onClose?.({ reason: reasonRef.current as TDialogCloseEventReason }));
      }
    },
    [open, onClose]
  );

  const handleLightBoxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const el = e.target;
    if (
      el &&
      lightBoxRef?.current?.contains(el as HTMLElement) &&
      !contentContainerRef?.current?.contains(el as HTMLElement)
    ) {
      reasonRef.current = 'lightBoxClick';
      onClose?.({ reason: reasonRef.current });
    }
  };

  useEffect(() => {
    if (open && !onOpenPrevRef.current) {
      setTimeout(() => {
        contentContainerRef && contentContainerRef.current?.focus();
      });
      onOpen &&
        setTimeout(() => {
          onOpen();
        });
    } else if (!open && onOpenPrevRef.current) {
      if (reasonRef.current === null) {
        onClose?.();
      } else {
        reasonRef.current = null;
      }
    }
    onOpenPrevRef.current = open;
  }, [open, onOpenPrevRef, onOpen, onClose]);

  const contentFocusTrapWrapped = () => (
    // eslint-disable-next-line jsx-a11y/no-autofocus
    <FocusTrap autoFocus={false}>
      <div
        id={id}
        className={classNames(styles.evrPopover, { [styles.fullscreen]: size === 'fullscreen' })}
        role="dialog"
        aria-labelledby={ariaLabelledBy}
        aria-describedby={ariaDescribedBy}
        data-testid={testId}
        ref={mergeRefs([ref, contentContainerRef])}
        tabIndex={-1}
        aria-modal={true}
        style={{ borderRadius: borderRadius ? `var(${borderRadius})` : undefined }}
      >
        {headerChild && <div className={styles.containerHeader}>{headerChild}</div>}
        {bodyChild && <div className={styles.containerBody}>{bodyChild}</div>}
        {footerChild && <div className={styles.containerFooter}>{footerChild}</div>}
      </div>
    </FocusTrap>
  );

  const renderFullscreenPopover = () => {
    return (
      <Overlay
        id={`${id}-popover-overlay-wrapper`}
        fullscreen
        open={open}
        testId={testId ? `${testId}-popover-overlay-wrapper` : undefined}
        onLightBoxClick={handleLightBoxClick}
        onLightBoxKeyDown={handleEscKey}
        ref={lightBoxRef}
      >
        {contentFocusTrapWrapped()}
      </Overlay>
    );
  };

  const renderFloatingPopover = () => {
    return open ? (
      <LightBox
        id={`${id}-popover-lightbox`}
        testId={testId ? `${testId}-popover-lightbox` : undefined}
        onClick={handleLightBoxClick}
        className={styles.evrPopoverBackgroundOverlay}
        variant="clear"
        onKeyDown={handleEscKey}
        ref={lightBoxRef}
      >
        <AnchoredOverlay
          id={`${id}-popover-overlay-wrapper`}
          visible={open}
          triggerRef={triggerRef}
          anchorOrigin={getAnchorOrigin()}
          transformOrigin={getTransformOrigin()}
          offset={offset}
          height={getAnchorOverlayHeight()}
          width={getAnchorOverlayWidth()}
        >
          {contentFocusTrapWrapped()}
        </AnchoredOverlay>
      </LightBox>
    ) : (
      <></>
    );
  };

  return size === 'fullscreen' ? renderFullscreenPopover() : renderFloatingPopover();
});

Popover.displayName = 'Popover';
