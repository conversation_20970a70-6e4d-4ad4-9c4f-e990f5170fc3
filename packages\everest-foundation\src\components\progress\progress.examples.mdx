import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { Progress } from './progress';
import { Button } from '../button';

export const scope = { Progress, Button };

Progress bars display status relative to a percent limit or a task. Mainly used within system processes such as downloading, uploading, etc. When using a progress bar, it will give the user feedback on progress however the bar is not interactable.

## Variations

### Determinate - Default

This is the default state of the progress bars. Used when progress can be calculated. (i.e., downloading a file, uploading a file, etc.)

export const determinateCode = `() => {
    const styles = {
        row: {
            width:'25rem',
        },
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
          <Progress value={30} />
        </Row>
    );
}`;

<CodeExample scope={scope} code={determinateCode} />

### Indeterminate - Default

This state is used when there is no calculable outcome. Indicates work is occurring. (i.e., reconnecting to the Internet.) Indeterminate has an optional label text above the track.

export const indeterminateCode = `() => {
    const styles = {
        row: {
            width:'25rem',
        },
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
          <Progress indeterminate />
        </Row>
    );
}`;

<CodeExample scope={scope} code={indeterminateCode} />

### Indeterminate with Label

An indeterminate label bar has label text located above the track.

export const indeterminateLabelCode = `() => {
    const styles = {
        row: {
            width:'25rem',
        },
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
          <Progress indeterminate label="Label" />
        </Row>
    );
}`;

<CodeExample scope={scope} code={indeterminateLabelCode} />

### Determinate - Contextual

A contextual determinate is a default bar with caption text and a percent to completion under the track.

export const determinateCaptionCode = `() => {
    const styles = {
        row: {
            width:'25rem',
        },
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
          <Progress value={30} caption="Caption" formatValue={(max, value, roundedValue) => roundedValue + '%'}/>
        </Row>
    );
}`;

<CodeExample scope={scope} code={determinateCaptionCode} />

### Determinate - Contextual with Label

A contextual determinate with a label has a default bar with contextual text including label text that is shown above the track.

export const determinateCaptionLabelCode = `() => {
    const styles = {
        row: {
            width:'25rem',
        },
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    return (
        <Row>
          <Progress value={30} caption="Caption" label="Label" formatValue={(max, value, roundedValue) => roundedValue + '%'}/>
        </Row>
    );
}`;

<CodeExample scope={scope} code={determinateCaptionLabelCode} />

### Determinate/Indeterminate with an error message

When an error occurs, the validation text will be shown under the track of the progress bar. In the situation of having a contextual text area on a determinate state, this area will turn from caption text into validation text while preserving the percentage to complete.

export const determinateError = `() => {
    const styles = {
        row: {
            width:'25rem',
            display:'flex',
            flexWrap:'wrap',
            gap: '1.25rem'
        },
    }
    const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    
    return (
        <Row>
            <Progress value={30} caption="Caption" errorMessage="Caption" errorMessagePrefix="Error:" label="Determinate" formatValue={(max, value, roundedValue) => roundedValue + '%'}/>
            <Progress indeterminate errorMessage="Caption" errorMessagePrefix="Error:" label="Indeterminate" />
        </Row>
    );
}`;

<CodeExample scope={scope} code={determinateError} />

### Determinate with inline value

A determinate with inline value has a default bar with the percentage completion shown to the right of the track and no other text.
When `showProgressValueInline` is set to `true` any defined caption, label and error message text will be hidden.

export const inlineValueCode = `() => (
  <div style={{ width: '25rem' }}>
    <Progress
      value={45}
      formatValue={(max, value, roundedValue) => roundedValue + '%'}
      showProgressValueInline={true}
    />
  </div>
);`;

<CodeExample scope={scope} code={inlineValueCode} />

## Accessing Progress using ref

Click on the Button to access the Progress, refer to the console for the element details.

export const refCode = `()=>{
     const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            width:'25rem'
        },
         column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '10px'
        }
    }
     const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
      <Column>
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
        </Row>
        <Row>
            <Progress ref={ref} value={30} />
        </Row>
      </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Use a determinate progress bar when the progression of a task can be calculated. Include contextual information to keep users informed and set their expectations accurately.

Ensure labels are clear and concise, ideally limited to a single line of text.

Add an ellipsis ("...") to the end of the caption text to show that the action is in progress.

Progress is full width of the parent container.

If percentage is not shown then show the remaining size.

Labels inform users of the information the progress bar is processing.

## Accessibility

When an error occurs, the validation text will be shown under the track of the progress bar in an indeterminate/determinate state. In the situation of having a contextual text area on a determinate state, this area will turn from caption text into validation text while preserving the percent to complete.

This component is not interactable.

The label/caption will announce once. The percentage will be announce in 10% intervals until completion.

The following table include the accessible attributes in the `Progress` component, including their descriptions and example values.

| Label                | Description                                                                                                                                                                                                  | <div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div> |
| -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------- |
| `ariaLabel`          | Aria label for Progress. Used as an additional text which is announced by the screen reader along with the `label` and `caption`.                                                                            | "Progress bar"                                            |
| `ariaLabelledBy`     | ID of an element that labels the Progress                                                                                                                                                                    | "progress-label"                                          |
| `ariaValueText`      | Human readable text description of the current value. More information on proper usage can be found [here (MDN)](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-valuetext). | "4 of 5 items completed", "60 percent complete"           |
| `label`              | Label for the Progress                                                                                                                                                                                       | "Upload File"                                             |
| `caption`            | Additional text providing context or status                                                                                                                                                                  | "In progress"                                             |
| `errorMessage`       | Message displayed when an error occurs                                                                                                                                                                       | "File failed to upload"                                   |
| `errorMessagePrefix` | Prefix added to the error message                                                                                                                                                                            | "Error:"                                                  |
| `value`              | Current progress value                                                                                                                                                                                       | 50                                                        |

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
