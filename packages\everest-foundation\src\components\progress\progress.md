# Progress Bar

## Summary

Research and document implementations for the Everest Progress Bar.

- Start Date: 2022-05-24
- Figma link: https://www.figma.com/file/YQpfQSKCqvp9Ea6vk7oWQt/%F0%9F%A7%AAProgress?node-id=338%3A13923

## Detailed Design

This component has 2 states:

1. **Determinate** - This state shows the percent to completion of the task.
2. **Indeterminate** - This state shows that the time to task completion is ongoing.

We are using the existing Caption and Label components for the below types of each of the states.

1. **Determinate - Default** - This is shown as a bar. The caption is hidden.
2. **Determinate - Contextual** - This is a default bar with a Caption and a percent to completion under the bar.
3. **Determinate - Contextual with Label** - This is a contextual determinate with a Label that is shown above the bar.
4. **Indeterminate - Default** - This is shown as a bar and is animated to proceed indefinitely until the task is done.
5. **Indeterminate - Label** - This has a Label that is shown above the bar.

## API

1. **testId**: `string`  
   Sets **data-test-id** attribute on the html element.
2. **caption**: `string`  
   Sets caption text for **Determinate - Contextual** and **Determinate - Contextual with Label**.
3. **label**: `string`  
   Sets label text for **Determinate - Contextual with Label** and **Indeterminate - Label**.
4. **value**: `number`
   Sets a percent(aria-valuenow) to completion for **Determinate**.
5. **max**: `number`
   Sets the maximum value(aria-valuemax) for defining the percentage. Default it to 100.
6. **indeterminate**: `boolean`  
   Sets the variant **Indeterminate**.
7. **errorMessage**: `string`  
   Specifies the error message which is rendered under the bar.
8. **errorMessagePrefix**: `string`  
   Specifies the prefix for the error message.
9. **ariaLabel**: `string`  
   Sets the additional text to be announced by the screen reader along with the `label` and `caption`
10. **formatValue** : (max: number, value:number) => string
    This is a callback function used to calculate the percentage value and then set the value to **aria-valuenow** with desired symbol, for example 10%.
11. **id**: `undefined | string`  
    Optional

## usage

const percentageFormat = (max: number, value: number) => {
const formatValue = (value / max) \* 100 + '%';
return formatValue;
};

<ProgressBar value={percentage} max="100" formatValue={percentageFormat}>
<ProgressBar indeterminate>

## Accessibility

**Touch Target**  
The entire progress bar is not interactable.

**Layout & Magnification**  
Font size will scale with the screen's viewport. `caption` will overflow to the next line.

**Keyboard Interaction**  
This component is not interactable.

**Loading error handling**  
`errorMessage` will be rendered under the bar for **Determinate** and **Indeterminate**. For **Determinate**, `caption` should be replaced with `errorMessage`.

**Page Interaction**
The user should be informed of the interval of the progress bar while interacting with the page and its elements.

**Screen Readers**
a. **Determinate**  
 The label/caption will announce once. The percentage will be announced in % until completion.
b. **Indeterminate**  
 The label/ ariaLabel will announce once. The screen reader must announce the semantic meaning "progress bar", a state "busy" and a purpose.

## Q&A

**What happens if the caption is set for Indeterminate?**  
Hide caption for Indeterminate. A Caption should be shown only for Determinate.

**How to handle Caption if the text is too long?**  
If there is not enough horizontal space within the container, the text should move vertically to another line. The maximum number of lines depends on the parent container.

**What should be the gap between the caption container and `percentage`?**  
4px

**What should the max and min-width of the ProgressBar?**  
The minimal width is 100px while the max is the full width of the parent container.

**What should be the animation for the Indeterminate?**  
Here is the reference,
https://www.figma.com/proto/YQpfQSKCqvp9Ea6vk7oWQt/branch/vITemgiPAysbJyny3Y1yiV/%F0%9F%A7%AAProgress?page-id=854%3A11426&node-id=854%3A11465&viewport=-622%2C627%2C2.5&scaling=min-zoom&starting-point-node-id=854%3A11456

**Should there be any animation for the Determinate when `percentage` is changed?**  
checking with designers.

**should progressbar should always be in percentage?**  
Yes

**Why aria-valuemax default to 100?**
As we are dealing with percentage, we are defaulting the max range value i.e. 100. We are defining aria-valuemax considering the future consideration i.e. to show the progress in numbers.

**Why aria-valuemin is not defined?**
Percentage calculation is defined based on the aria-valuemin and aria-valuemax. As we are dealing with percentages, the range will be 0 and 100 for the calculation. If aria-valuemin is not defined, then it defaults to 0.

**Should we append symbol '%' for the number in the code?**
This will be handled using **format** callback function.

## Other Design Systems

For example:

**Material UI** - https://mui.com/material-ui/api/linear-progress/

- Supports different colors and buffer.
- Supports Determinate and Indeterminate variants.

**Polaris (Shopify)** - https://polaris.shopify.com/components/feedback-indicators/progress-bar#navigation

- Supports different sizes and colors.
- Supports ProgessBar with animation and without animation.

**Carbon (IBM)** - https://react.carbondesignsystem.com/?path=/docs/components-progressbar--example

- Supports different sizes.
- Handles max value i.e. scaling is handled.
- Supports Determinate and Indeterminate variants.

**Fluent (Microsoft)** - https://developer.microsoft.com/en-us/fluentui#/controls/web/progressindicator

- Supports Determinate and Indeterminate variants.
- Supports different bar heights. It uses `barHeight` to set the value.

## Acceptance Criteria

1. The name of a component is ProgressBar
2. Types:
   a. Determinate - Default
   b. Determinate - Contextual
   c. Determinate - Contextual with Label
   d. Indeterminate - Default
   e. Indeterminate - Label
3. Component is not focusable/clickable
4. APIs:
   a.testId
   b.caption
   c.label
   d.value
   e.max
   f.indeterminate
   g.errorMessage
   h.errorMessagePrefix
   i.ariaLabel
   j.formatValue
   k.id
5. Shows correct animation in Determinate and Indetermiante state.

# Changelog

04/25/2025 - Add customization to allow value to be displayed to the right of the bar ([**PWEB-20262**](https://dayforce.atlassian.net/browse/PWEB-20262))
