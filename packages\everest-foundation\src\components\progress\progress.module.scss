@use '../../index.scss' as helper;
@use '@ceridianhcm/theme/dist/scss/' as typography;

.evrProgress {
  &.container {
    min-width: 100px;
    width: 100%;
  }

  &.label {
    margin-block-end: var(--evr-spacing-3xs);
  }

  &.progressContainer {
    position: relative;
    overflow: hidden;
    display: block;
    width: 100%;
    height: var(--evr-size-xs);
    background-color: var(--evr-surfaces-tertiary-default);
  }

  & .trackContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--evr-spacing-3xs);
  }

  &.progress {
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    background-color: var(--evr-interactive-primary-default);
    border-radius: var(--evr-radius-xs);

    &.determinate {
      width: 100%;
      transition: transform 2.4s cubic-bezier(0.8, 0.3, 0.3, 0.8);
      transform-origin: left center;
    }

    &.indeterminate {
      animation: animation-progress 2.4s cubic-bezier(0.8, 0.3, 0.3, 0.8) infinite;
    }
  }

  @keyframes animation-progress {
    0% {
      left: -33.3%;
      right: 100%;
    }
    100% {
      left: 100%;
      right: -33.3%;
    }
  }

  &.captionContainer {
    margin-block-start: var(--evr-spacing-3xs);
    display: flex;
    gap: var(--evr-spacing-3xs);
    cursor: default;
    position: relative;
    justify-content: space-between;
  }

  &.caption {
    overflow: hidden;
    width: auto;
    overflow-wrap: break-word;
  }

  .captionText {
    @include typography.captionRegular;
    color: var(--evr-content-primary-default);
  }

  &.formatValue {
    position: static;
    right: 0;
    float: right;
    width: fit-content;
  }

  &.hidden {
    @include helper.visuallyHidden;
  }

  &.progressContainer.rounded {
    border-radius: var(--evr-radius-2xs);
  }
}
