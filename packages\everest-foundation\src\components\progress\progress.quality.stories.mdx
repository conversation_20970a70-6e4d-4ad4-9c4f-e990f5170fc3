import { Meta, <PERSON>, Canvas } from '@storybook/addon-docs';
import { Progress } from './progress';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Progress"
  component={Progress}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    max: 100,
    value: 30,
    testId: 'progress-id',
    ariaLabel: 'Progress Bar',
    ariaValueText: '30 percent complete',
  }}
/>

# Progress

## Live Demo

<Canvas>
  <Story name="Indeterminate - Default">{(args) => <Progress indeterminate {...args}></Progress>}</Story>
</Canvas>

<Canvas>
  <Story name="Indeterminate - Label">{(args) => <Progress indeterminate label="Label" {...args}></Progress>}</Story>
</Canvas>

<Canvas>
  <Story name="Determinate - Default">{(args) => <Progress {...args}></Progress>}</Story>
</Canvas>

<Canvas>
  <Story name="Determinate - Contextual">
    {(args) => (
      <Progress caption="Caption" formatValue={(max, value, roundedValue) => roundedValue + '%'} {...args}></Progress>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Determinate - Contextual with Label">
    {(args) => (
      <Progress
        caption="Caption"
        label="Label"
        formatValue={(max, value, roundedValue) => roundedValue + '%'}
        {...args}
      ></Progress>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Determinate - Error">
    {(args) => (
      <Progress
        caption="Caption"
        label="Label"
        errorMessage="Caption"
        errorMessagePrefix="Error:"
        formatValue={(max, value, roundedValue) => roundedValue + '%'}
        {...args}
      ></Progress>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Determinate - Long Caption">
    {(args) => (
      <Progress
        caption="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna ali. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum"
        formatValue={(max, value, roundedValue) => roundedValue + '%'}
        {...args}
      ></Progress>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Determinate - with inline value">
    {(args) => (
      <Progress
        showProgressValueInline={true}
        formatValue={(max, value, roundedValue) => roundedValue + '%'}
        {...args}
      ></Progress>
    )}
  </Story>
</Canvas>
