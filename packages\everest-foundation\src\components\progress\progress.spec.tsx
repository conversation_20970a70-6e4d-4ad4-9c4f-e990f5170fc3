import React from 'react';
import { render, screen } from '@testing-library/react';

import '@testing-library/jest-dom';
import { Progress } from './progress';

const formatValue = (max: number, value: number, percentage: number) => `${percentage}% completed`;

describe('[Progress]', () => {
  describe('Indeterminate', () => {
    it('renders an indeterminate progress', () => {
      render(<Progress indeterminate />);
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-busy', 'true');
      expect(progressBar).not.toHaveAttribute('aria-valuenow');
    });

    it('renders an indeterminate progress with a label', () => {
      render(<Progress indeterminate label="Progress" />);
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('Progress')).toBeInTheDocument();
    });

    it('renders an indeterminate progress with an error message', () => {
      render(<Progress indeterminate errorMessage="There was an error" errorMessagePrefix="Error:" />);
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('Error:')).toBeInTheDocument();
      expect(screen.getByText('There was an error')).toBeInTheDocument();
    });
  });

  describe('Determinate', () => {
    it('renders a determinate progress', () => {
      render(<Progress value={30} formatValue={formatValue} />);
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-valuenow', '30');
    });

    it('renders a contextual determinate progress', () => {
      render(<Progress value={30} caption="Caption" formatValue={formatValue} />);
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-valuenow', '30');
      expect(screen.getByText('Caption')).toBeInTheDocument();
    });

    it('renders a contextual determinate progress with a label', () => {
      render(<Progress value={30} caption="Caption" label="Label" formatValue={formatValue} />);
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-valuenow', '30');
      expect(screen.getByText('Caption')).toBeInTheDocument();
      expect(screen.getByText('Label')).toBeInTheDocument();
    });

    it('renders a contextual determinate progress with the value displayed inline next to the track', () => {
      render(<Progress value={30} showProgressValueInline={true} formatValue={formatValue} />);
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-valuenow', '30');
    });
  });

  it('applies rounded corners when _roundedCorners is true', () => {
    render(<Progress _roundedCorners />);
    const progressBarContainer = screen.getByRole('progressbar');
    expect(progressBarContainer).toHaveClass('rounded');
  });
});
