import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { Progress } from './progress';
import Examples from './progress.examples.mdx';

<Meta
  title="Components/Progress"
  component={Progress}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      exclude: ['_roundedCorners'],
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9858&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  argTypes={{
    formatValue: {
      control: '-',
    },
  }}
  args={{
    id: 'progress-1',
    testId: 'progress-test-id',
    label: 'Upload file',
    value: 30,
    max: 100,
    indeterminate: false,
    caption: 'Still processing',
  }}
/>

# Progress

<Examples />

## Live Demo

<Canvas>
  <Story name="Progress">
    {(args) => <Progress {...args} formatValue={(max, value, roundedValue) => roundedValue + '%'} />}
  </Story>
</Canvas>

<ArgsTable story="Progress" />
