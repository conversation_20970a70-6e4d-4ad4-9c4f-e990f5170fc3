import React from 'react';

import { LabelText } from '../../typography/components/label-text';
import { mergeRefs, useCreateTestId } from '../../utils';
import { StatusMessage } from '../status-message';

import styles from './progress.module.scss';
export interface IProgress {
  /**
   * ID of an element that labels the Progress
   */
  id?: string;
  /**
   * Optional. An ID used for automation testing. Sets the `data-testid` attribute on the Progress.
   */
  testId?: string;
  /**
   * Optional. User-provided caption. If none is given, no caption text is rendered.
   */
  caption?: string;
  /**
   * Optional. User-provided label. If none is given, no label text is rendered.
   */
  label?: string;
  /**
   * @default 0
   * Optional. A percentage value for a determinate progress bar.
   */
  value?: number;
  /**
   * @default 100
   * Sets the maximum value for a determinate progress. Used for defining the percentage.
   */
  max?: number;
  /**
   * If true, the Progress is indeterminate. If false or absent, the Progress is determinate.
   */
  indeterminate?: boolean;
  /**
   * Optional. Renders the error message, overrides the caption text.
   */
  errorMessage?: string;
  /**
   * Optional. Renders the prefix before the error message.
   */
  errorMessagePrefix?: string;
  /**
   * Optional. Used for accessibility. Communicates the purpose of the Progress.
   */
  ariaLabel?: string;
  /**
   * Optional. Used for accessibility. An ID for a label for the Progress.
   */
  ariaLabelledBy?: string;
  /**
   * Optional. Human-readable text for the value of the Progress.
   */
  ariaValueText?: string;
  /**
   * Optional. If true, the Progress value is displayed inline next to the track and all other text is hidden.
   * If false or absent, the progress value is displayed below the progress track and any other configured text is shown.
   * @default false
   */
  showProgressValueInline?: boolean;
  /**
   * Optional. Callback function to determine the format of the percentage
   * @param max The maximum value of the progress
   * @param value The current value of the progress
   * @param percentageValue The percentage value of the progress
   * @returns A string representing the formatted value
   */
  formatValue?: (max: number, value: number, percentageValue: number) => string;
  /**
   * Optional. A reference to the element with the `progressbar` role.
   */
  ref?: React.ForwardedRef<HTMLDivElement>;
  /**
   * @deprecated This property should not be used.
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  _roundedCorners?: boolean;
}

export const Progress = React.forwardRef<HTMLDivElement, IProgress>((props: IProgress, ref) => {
  const {
    id,
    testId,
    caption,
    label,
    value = 0,
    max = 100,
    indeterminate,
    errorMessage,
    errorMessagePrefix,
    ariaLabel,
    ariaLabelledBy,
    ariaValueText,
    formatValue,
    showProgressValueInline = false,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    _roundedCorners = true,
  } = props;

  const dataRef = useCreateTestId(testId);
  const dataRefCaption = useCreateTestId(`${testId}-caption`);
  const isCaptionOrError = errorMessage || errorMessagePrefix || caption;
  const labelTextTestId = testId ? `${testId}-label` : undefined;
  const getLabel = () => {
    if (isCaptionOrError || indeterminate) return <LabelText testId={labelTextTestId}>{label}</LabelText>;
  };
  const labelStyle = isCaptionOrError || indeterminate ? styles.label : undefined;

  const progressVariant = () => {
    if (!indeterminate)
      return (
        <span
          style={{ transform: `translateX(${percentageScale() - 100}%)` }}
          className={`${styles.evrProgress} ${styles.progress} ${styles.determinate}`}
        ></span>
      );
    return <span className={`${styles.evrProgress} ${styles.progress} ${styles.indeterminate}`}></span>;
  };

  const percentageScale = () => {
    return Math.floor((value / max) * 100);
  };

  const formattedPercentage = formatValue?.(max, value, percentageScale());

  const captionText = () => {
    if (!indeterminate || ((errorMessage || errorMessagePrefix) && indeterminate)) {
      if (isCaptionOrError && !formatValue) console.error('formatValue callback function is not called');
      if (errorMessage || errorMessagePrefix)
        return (
          <div className={`${styles.evrProgress} ${styles.caption}`}>
            <StatusMessage
              visible
              variant={'error'}
              statusMessagePrefix={errorMessagePrefix}
              statusMessage={errorMessage}
            />
          </div>
        );
      if (caption && !indeterminate)
        return (
          <div className={`${styles.evrProgress} ${styles.caption}`}>
            <p className={styles.captionText}>{caption}</p>
          </div>
        );
    }
  };

  return (
    <div className={`${styles.evrProgress} ${styles.container}`} id={id} ref={mergeRefs([ref, dataRef])}>
      {label && !showProgressValueInline && <div className={`${styles.evrProgress} ${labelStyle}`}>{getLabel()}</div>}
      <div className={styles.trackContainer}>
        <span
          className={`${styles.evrProgress} ${styles.progressContainer} ${_roundedCorners && styles.rounded}`}
          role="progressbar"
          aria-label={`${ariaLabel ?? ''} ${label ?? ''} ${caption ?? ''} `}
          aria-valuenow={!indeterminate ? percentageScale() : undefined}
          aria-busy={indeterminate ? 'true' : 'false'}
          aria-labelledby={ariaLabelledBy}
          aria-valuetext={ariaValueText}
        >
          {progressVariant()}
        </span>
        {!indeterminate && showProgressValueInline && formattedPercentage && (
          <p className={`${styles.captionText} ${styles.evrProgress}`} aria-live="assertive">
            {formattedPercentage}
          </p>
        )}
      </div>
      {!showProgressValueInline && (
        <div
          className={`${styles.evrProgress} ${
            (isCaptionOrError && !indeterminate) || (indeterminate && (errorMessage || errorMessagePrefix))
              ? styles.captionContainer
              : ''
          }`}
          ref={dataRefCaption}
        >
          {captionText()}
          {!indeterminate && (
            <p
              className={`${styles.captionText} ${styles.evrProgress} ${
                isCaptionOrError && formattedPercentage ? '' : styles.hidden
              }`}
              aria-live="assertive"
            >
              {formattedPercentage}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

Progress.displayName = 'Progress';
