import React from 'react';

export interface IRadioButtonGroupContext {
  groupName: string;
  onChange: (value: string) => void;
  required?: boolean;
}

const defaultContext = {
  groupName: '',
  onChange: () => undefined,
  required: false,
};

// esLint doesn't like this context object capitalized -- ignoring rule
// eslint-disable-next-line @typescript-eslint/naming-convention
export const RadioButtonGroupContext = React.createContext<IRadioButtonGroupContext>(defaultContext);

if (process.env.NODE_ENV !== 'production') {
  RadioButtonGroupContext.displayName = 'RadioButtonGroupContext';
}
