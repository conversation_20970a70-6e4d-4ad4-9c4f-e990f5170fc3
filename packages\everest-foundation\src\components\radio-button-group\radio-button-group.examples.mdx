import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { RadioButton } from '../radio-button/radio-button';
import { RadioButtonGroup } from './radio-button-group';

export const scope = {
  RadioButton,
  RadioButtonGroup,
};

A radio button group is a collection of <LinkTo kind="Components/Radio Buttons/Radio Button">RadioButtons</LinkTo>. Only one radio button can be chosen from the group at a time.

## Variations

### RadioButtonGroup Layout

A `RadioButtonGroup` can be presented in a horizontal or vertical layout. The default is horizontal.

export const controlledLayoutCode = `() => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            gap: '0.5rem 1rem'
        }
    };
    const [valueRow, setValueRow] = React.useState('one');
    const [valueCol, setValueCol] = React.useState('three');
    const [valueDisabled, setValueDisabled] = React.useState('six');
    return (
      <div style={styles.row}>
        <RadioButtonGroup
            onChange={value => setValueRow(value)}
            groupName="RowGroup"
            label="Group One"
            id="radio-group-id"
          >
            <RadioButton
              id="radio-group-button-id-1"
              value="one"
              checked={'one'=== valueRow }
              label="Option One" />
            <RadioButton
              id="radio-group-button-id-2"
              value="two"
              checked={'two'=== valueRow }
              label="Option Two" />
        </RadioButtonGroup>
        <RadioButtonGroup
            onChange={value => setValueCol(value)}
            groupName="ColumnGroup"
            vertical={true}
            label="Group Two"
            id="radio-group-2-id"
        >
            <RadioButton
              id="radio-group-2-button-id-1"
              value="three"
              checked={'three'=== valueCol }
              label="Option Three" />
            <RadioButton
              id="radio-group-2-button-id-2"
              value="four"
              checked={'four'=== valueCol }
              label="Option Four" />
        </RadioButtonGroup>
        <RadioButtonGroup
            id="radio-group-3-id"
            onChange={value => setValueDisabled(value)}
            groupName="ColumnDisabled"
            vertical={true}
            label="Group Three"
        >
            <RadioButton
              id="radio-group-3-button-id-1"
              value="five"
              checked={'five'=== valueDisabled }
              label="Option Five"
              disabled />
            <RadioButton
              id="radio-group-3-button-id-2"
              value="six"
              checked={'six'=== valueDisabled }
              label="Option Six"
              disabled />
        </RadioButtonGroup>
      </div>
    )
  }
`;

<CodeExample scope={scope} code={controlledLayoutCode} />

### RadioButtonGroup with an error message

`errorMessage` prop is used to show the validation error message. `errorMessagePrefix` is used to set the prefix to the error message

export const errorMessageCode = `
  () => {
    const styles = {
        row: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            gap: '0.5rem 1rem'
        }
    };
    return (
      <div style={styles.row}>
      <RadioButtonGroup
        id="radio-group-4-id"
        groupName="errorExampleGroup"
        vertical={true}
        label="Radio Button Group"
        errorMessage="This is an error message"
        errorMessagePrefix="Error:"        
      >
      <RadioButton
        id="radio-group-4-button-id-1"
        checked={"checked"}
        status={'error'}
        label="Option One" />
      <RadioButton
        id="radio-group-4-button-id-2"
        label="Option Two" />
      </RadioButtonGroup>
       <RadioButtonGroup
        id="radio-group-5-id"
        groupName="errorExampleGroup"
        vertical={true}
        label="Radio Button Group"
        errorMessage="This is an error message"
        errorMessagePrefix="Error:"        
      >
        <RadioButton
          id="radio-group-5-button-id-1"
          status={'error'}
          label="Option One" />
        <RadioButton
          id="radio-group-5-button-id-2"
          status={'error'}
          label="Option Two" />
      </RadioButtonGroup>
      </div>
    )
  }
`;

<CodeExample scope={scope} code={errorMessageCode} />

### Handling State and Events

`RadioButtonGroup` passes props such as `groupName` and `onChange` to its child <LinkTo kind="Components/Radio Buttons/Radio Button">RadioButtons</LinkTo>, which are fully controlled components.
These props can be overwritten by props on the individual radio buttons.

export const controlledCode = `
  () => {
    const [value, setValue] = React.useState('1');
    const [counter, setCounter] = React.useState('3')
    function handleChange(value){ setValue(value); };
    return (
      <RadioButtonGroup
        id="radio-group-6-id"
        onChange={handleChange}
        groupName="stateExampleGroup"
        vertical={true}
        label="Radio Button Group"
      >
        <RadioButton
          id="radio-group-6-button-id-1"
          value='1'
          checked={'1'=== value }
          label="Option 1" />
        <RadioButton
          id="radio-group-6-button-id-2"
          value='2'
          checked={'2' === value }
          label={"Option 2"}
          disabled={true} />
        <RadioButton
          id="radio-group-6-button-id-3"
          value={counter}
          checked={counter === value }
          onChange={ value => {
            let newValue = (Number(counter)+1).toString();
            setValue(newValue);
            setCounter(newValue);
          }}
          label={"Option " + counter.toString()} />
      </RadioButtonGroup>
    )
  }
`;

<CodeExample scope={scope} code={controlledCode} />

### RadioButtonGroup with prefix text and multiline label

Multiline label radio button behave the same way when wrapped inside of `RadioButtonGroup` as single line radio button, except
width of container can dictate number of lines of label to be wrapped.

export const multilineLabelCodes = `
  () => {
    const styles = {
      container: {
          width: '200px'
      }
    };
    const [value, setValue] = React.useState('one');
    return (
      <div style={styles.container}>
        <RadioButtonGroup
          id="radio-group-7-id"
          groupName="multilineLabelRadioButton"
          onChange={newValue => setValue(newValue)}
          vertical={true}
          label="Multiline Radio Button Group"      
        >
          <RadioButton
            id="radio-group-7-button-id-1"
            value="one"
            checked={'one' === value}
            multiline={true}
            prefixText="Prefix Text"
            label="This is a multi-line label that is stacked under prefix text." />
          <RadioButton
            id="radio-group-7-button-id-2"
            value="two"
            checked={'two' === value}
            multiline={true}
            prefixText="Prefix Text"
            label="This is also a multi-line label that is stacked." />
        </RadioButtonGroup>
      </div>
    )
  }
`;

<CodeExample scope={scope} code={multilineLabelCodes} />

## How to Use

Related <LinkTo kind="Components/Radio Buttons/Radio Button">RadioButtons</LinkTo> should be grouped in a `RadioButtonGroup` to properly navigate the buttons with assistive technologies, such as keyboards or screen readers.

## Accessibility

Use your keyboard to tab into a radio button group and navigate with the arrow keys. The space bar can be pressed to make a selection if needed.

A unique `id` is generated if none is provided to help assistive technologies.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
