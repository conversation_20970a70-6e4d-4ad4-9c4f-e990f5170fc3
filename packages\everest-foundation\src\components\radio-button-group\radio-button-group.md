# Radio Button Group

## Summary

Research and document implementations for the Everest Radio Button Group.

- Start Date: 2022-03-01
- Figma link: https://www.figma.com/file/6MRT3O96cACFwhFpRH6T1r/%F0%9F%A7%AASelection-Controls?node-id=536%3A10959

## Detailed Design

The radio button group is intended to be a simple grouping for `RadioButton` components with limited layout options. The group will use context to pass props to its children. Children will use context when the button group exists (changes to RadioButton will be needed).

## API

1. **groupName**: `string`  
   Sets the group `name` the radio buttons belong to. This is passed to its children.
1. **onChange**: `(value: string) => undefined`  
   User supplied function that runs when a button group member is checked. Usually this would include updating React state. This is passed to its children.
1. **vertical**: `undefined | boolean`  
   Optional. Parameter indicating the layout direction of the radio button. Default will be false/horizontal for now.
1. **label**: `string | undefined`  
   Optional user provided label. If none is given, no label text is rendered.
1. **required**: `undefined | boolean`  
   Optional. Sets the button group as required.
1. **testId**: `string | undefined`  
   Optional. Sets `data-testid` attribute on the containing html element.
1. **id**: `string | undefined`  
   Required id for the radio button group.
1. **errorMessage**: `string`  
   Specifies the error message which is rendered at the bottom of the radio button group.
1. **errorMessagePrefix**: `string`  
   Specifies the prefix for the error message.

## Accessibility

- The component will not be interactive and used only for layout and accessibility needs.
- A guide to accessible radio buttons and groups can be found at https://www.w3.org/TR/wai-aria-practices/#radiobutton. The two approaches on radio button groups are (i) roving tab index with focus and (ii) use of `aria-activedescendant`. The more reliable method appears to be tab indexes with focus (e.g. https://zellwk.com/blog/element-focus-vs-aria-activedescendant/).

## Q&A

**QA: Will the group have a label/legend? How do we indicate a group as required?**

A: A label will always be provided. If no visual presentation then an aria label will be specified, e.g. 'Choose an option.'

**QA: Will there be a validation requirement in the future? How will that work?**

A: The consumer will have the option to supply error feedback. How that feedback is determined is up to the consumer (e.g., if there is error text, render it--otherwise don't show it.)

**QA: What will be the default layout? Is it row?**

A: Yes, default is horizontal/row.

**QA: Is there a specific HTML tag required for this component?**

A: None.

## Future Considerations

Suggest validation be addressed later.

## Other Design Systems

**Carbon**

- https://www.carbondesignsystem.com/components/radio-button/usage/
- Uses radio button group with legend, and uses a fieldset

**Polaris**

- https://polaris.shopify.com/components/forms/radio-button#navigation
- Does not have an explicit radio grouping component (layout arranged with `<Stack>` component)

**Material UI**

- https://mui.com/components/radio-buttons/
- Compositional with radio grouping component and radio controls that are wrapped with form labels
- A custom hook is offered to facilitate getting props from a provider for the parent grouping element. Using context to pass props to children looks promising.

**Grommet**

- Radio button: https://v2.grommet.io/radiobutton
- Radio button group: https://v2.grommet.io/radiobuttongroup
- Radio groups can be passed arrays can be passed to represent buttons and their labels

**React Bootstrap**

- https://react-bootstrap.github.io/components/buttons/#checkbox--radio
- `<ButtonGroup>` component has role `group` and appears to only control presentation

## Required PBIs

1. Develop the component (React + a11y + manual dev testing)
1. Component testing (unit + integration + visual)
1. Documentation preparation

## Acceptance Criteria

1.  - Component to be named `<RadioGroup>`
    - Build component and setup in Storybook (Components, Foundations &rarr; Automation)
    - Manually verify
      - API works as expected (within the context of a singular button)
      - a11y (e.g. presence of aria attributes, screen reader narrative, keyboard and pointer functionality)
1.  - Build unit tests (RTL) to confirm
      - component renders, API and functionality work as expected (e.g. keyboard interactions)
    - Build integration tests to confirm
      - functionality (checked, unchecked, disabled)
      - visual tests
1.  - Build validation logic, styling, and testing, as needed
