import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { RadioButtonGroup } from './radio-button-group';
import { RadioButton } from '../radio-button';
import { Button } from '../button';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Radio Button Group"
  component={RadioButtonGroup}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    onChange: () => undefined,
    testId: 'radio-button-group-test-id',
  }}
/>

# Radio Button Group

## Live Demo

<Canvas>
  <Story name="Horizontal Layout">
    {(args) => (
      <RadioButtonGroup {...args} groupName="groupNameOne" label="Horizontal Group" id="radio-form-group-id">
        <RadioButton
          id="radio-button-id"
          value="one"
          checked={false}
          label="Option One"
          testId={`${args.testId}-btn-1`}
        />
        <RadioButton
          id="radio-button-id-2"
          value="two"
          checked={true}
          label="Option Two"
          testId={`${args.testId}-btn-2`}
        />
      </RadioButtonGroup>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Vertical Layout">
    {(args) => (
      <RadioButtonGroup
        {...args}
        vertical={true}
        groupName="groupNameTwo"
        label="Vertical Group"
        id="radio-form-group-id-2"
      >
        <RadioButton
          id="radio-button-id-3"
          value="one"
          checked={false}
          label="Option One"
          testId={`${args.testId}-btn-1`}
        />
        <RadioButton
          id="radio-button-id-4"
          value="two"
          checked={true}
          label="Option Two"
          testId={`${args.testId}-btn-2`}
        />
      </RadioButtonGroup>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Message">
    {(args) => (
      <RadioButtonGroup
        {...args}
        groupName="groupNameTwo"
        label="Group with an error"
        errorMessage="This is an error message"
        errorMessagePrefix="Error:"
        id="radio-form-group-id-3"
      >
        <RadioButton
          id="radio-button-id-5"
          value="one"
          checked={false}
          label="Option One"
          testId={`${args.testId}-btn-1`}
        />
        <RadioButton
          id="radio-button-id-6"
          value="two"
          checked={true}
          label="Option Two"
          testId={`${args.testId}-btn-2`}
        />
      </RadioButtonGroup>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {() => {
      const [value, setValue] = React.useState('');
      return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <RadioButtonGroup
            id="no-default-id"
            testId="no-default-test-id"
            groupName="no default"
            label="No default value"
            onChange={(value) => setValue(value)}
          >
            <RadioButton
              id="no-default-1-id"
              testId="no-default-1-test-id"
              label="No default one"
              value="one"
              checked={'one' === value}
            />
            <RadioButton
              id="no-default-2-id"
              testId="no-default-2-test-id"
              label="No default two"
              value="two"
              checked={'two' === value}
              disabled
            />
            <RadioButton
              id="no-default-3-id"
              testId="no-default-3-test-id"
              label="No default three"
              value="three"
              checked={'three' === value}
            />
          </RadioButtonGroup>
          <RadioButtonGroup
            id="with-default-id"
            testId="with-default-test-id"
            groupName="with default"
            label="With default value"
          >
            <RadioButton id="with-default-1" testId="with-default-1-test-id" label="With default one" value="one" />
            <RadioButton
              id="with-default-2"
              testId="with-default-2-test-id"
              label="With default two"
              value="two"
              checked
            />
          </RadioButtonGroup>
          <RadioButtonGroup
            id="disabled-radio-group-id"
            testId="disabled-radio-group-test-id"
            groupName="disabled"
            label="Disabled"
          >
            <RadioButton
              id="disabled-1-id"
              testId="disabled-1-test-id"
              label="Disabled One"
              value="one"
              checked
              disabled
            />
            <RadioButton id="disabled-2-id" testId="disabled-2-test-id" label="Disabled Two" value="two" disabled />
          </RadioButtonGroup>
          <Button id="external-id" testId="external-test-id" label="External Button" />
        </div>
      );
    }}
  </Story>
</Canvas>
