import * as React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { RadioButtonGroup } from './radio-button-group';
import { RadioButton } from '../radio-button';

describe('[RadioButtonGroup]', () => {
  const groupName = 'groupNameTest';
  const onChange = jest.fn();
  const testId = 'radio-button-group-test-group-id';
  const label = 'Label';
  const errorMessage = 'This is an error message';
  const getRadioButtonGroup = () => screen.getByTestId(testId);
  const getRadioButtonOneLabel = () => screen.getByTestId(`${testId}-btn-1-label`);
  const getRadioButtonOneInput = () => screen.getByTestId(`${testId}-btn-1-radio`);
  const getRadioButtonTwoLabel = () => screen.getByTestId(`${testId}-btn-2-label`);
  const getRadioButtonTwoInput = () => screen.getByTestId(`${testId}-btn-2-radio`);
  const radioGroupId = 'radio-group-id';
  const radioButtonId = 'radio-button-id';
  const radioButtonId2 = 'radio-button-id-2';

  const radioButtonGrouping = (required = false, label?: string, errorMessage?: string) => (
    <RadioButtonGroup
      groupName={groupName}
      onChange={onChange}
      testId={testId}
      required={required}
      errorMessage={errorMessage}
      label={label}
      id={radioGroupId}
    >
      <RadioButton id={radioButtonId} value="one" checked={true} label="btnOne" testId={`${testId}-btn-1`} />
      <RadioButton id={radioButtonId2} value="two" checked={false} label="btnTwo" testId={`${testId}-btn-2`} />
    </RadioButtonGroup>
  );

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without issue', () => {
    render(radioButtonGrouping());
    expect(getRadioButtonGroup()).toBeInTheDocument();
    expect(getRadioButtonOneLabel()).toBeInTheDocument();
    expect(getRadioButtonOneInput()).toBeInTheDocument();
    expect(getRadioButtonTwoLabel()).toBeInTheDocument();
    expect(getRadioButtonTwoInput()).toBeInTheDocument();
  });

  [
    {
      name: 'Radio Button Group',
      jsx: radioButtonGrouping(),
    },
    {
      name: 'Radio Button Group Disabled',
      jsx: radioButtonGrouping(false),
    },
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getRadioButtonGroup()).toBeInTheDocument();
        expect(getRadioButtonOneInput()).toBeInTheDocument();
        expect(getRadioButtonTwoInput()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  it('should call onChange when child radio button is clicked', async () => {
    const { getByLabelText } = render(radioButtonGrouping());
    await userEvent.click(getByLabelText('btnTwo'));
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it('should call onChange as defined on button instead of group', async () => {
    const onChangeBtnOne = jest.fn();
    const radioButtonGroupingTest = () => (
      <RadioButtonGroup id={radioGroupId} groupName={groupName} onChange={onChange} testId={testId}>
        <RadioButton id={radioButtonId} value="one" checked={false} onChange={onChangeBtnOne} label="btnOne" />
      </RadioButtonGroup>
    );
    const { getByLabelText } = render(radioButtonGroupingTest());
    await userEvent.click(getByLabelText('btnOne'));
    expect(onChangeBtnOne).toHaveBeenCalledTimes(1);
    expect(onChange).toHaveBeenCalledTimes(0);
  });

  it('should set children as required when group is required', () => {
    render(radioButtonGrouping(true));
    expect(getRadioButtonOneInput()).toHaveAttribute('required');
    expect(getRadioButtonTwoInput()).toHaveAttribute('required');
  });

  it('should render a label when provided', () => {
    render(radioButtonGrouping(true, label));

    expect(screen.getByText(label)).toBeInTheDocument();
  });

  it('should render error message when provided', () => {
    render(radioButtonGrouping(true, label, errorMessage));

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('should have an id when given', () => {
    const onChangeBtnOne = jest.fn();
    render(
      <RadioButtonGroup groupName={groupName} onChange={onChange} testId={testId} id={radioGroupId}>
        <RadioButton id={radioButtonId} value="one" checked={false} onChange={onChangeBtnOne} label="btnOne" />
      </RadioButtonGroup>
    );
    expect(getRadioButtonGroup()).toHaveAttribute('id', 'radio-group-id');
  });
});
