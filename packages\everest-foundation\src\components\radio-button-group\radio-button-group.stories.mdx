import { <PERSON><PERSON>, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { RadioButtonGroup } from './radio-button-group';
import { action } from '@storybook/addon-actions';
import { RadioButton } from '../radio-button';
import Examples from './radio-button-group.examples.mdx';
import { useArgs } from '@storybook/client-api';

<Meta
  title="Components/Radio Buttons/Radio Button Group"
  component={RadioButtonGroup}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9809&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  argTypes={{
    groupName: {
      type: 'string',
      control: 'text',
      description: 'Sets the radio button group name.',
    },
    onChange: {
      control: '-',
      description:
        'User supplied function that runs when a radio button is checked. Usually this includes updating React state.',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Optional. Sets `data-testid` attribute on the radio button group.',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'Required id for the radio button group.',
    },
    required: {
      type: 'boolean',
      control: 'boolean',
      description: 'Optional. Sets the button group as required.',
    },
    vertical: {
      type: 'boolean',
      control: 'boolean',
      description: 'Optional. Sets the layout as vertical. Default is false or horizontal.',
    },
    label: {
      description: 'Optional. Renders the label at the top of the radio button group.',
    },
    errorMessage: {
      description: 'Optional. Renders the error message at the bottom of the radio button group.',
    },
    errorMessagePrefix: {
      description: 'Optional. Renders the prefix before the error message.',
    },
    value: {
      table: { disable: true },
    },
  }}
  args={{
    groupName: 'radio-button-group',
    required: false,
    vertical: false,
    label: 'This is a label',
    errorMessage: 'This is an error message',
    errorMessagePrefix: 'Error:',
    id: 'radio-button-group-id',
    testId: 'radio-button-group-test-id',
    value: 'one',
  }}
/>

# Radio Button Group

<Examples />

## Live Demo

{/*
  Looks like inputs losing focus in Docs with useArgs is a known issue
  https://github.com/storybookjs/storybook/issues/11657
*/}

<Canvas>
  <Story name="Radio Button Group">
    {(args) => {
      const [{ value }, updateArgs] = useArgs();
      return (
        <RadioButtonGroup
          {...args}
          onChange={(value) => {
            action('onChange')(value);
            updateArgs({ value });
          }}
        >
          <RadioButton id="option-1" value="one" checked={'one' === value} label="Option One" />
          <RadioButton id="option-2" value="two" checked={'two' === value} label="Option Two" />
          <RadioButton id="option-3" value="three" checked={'three' === value} label="Option Three" />
        </RadioButtonGroup>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Radio Button Group" />
