import React, { Children, PropsWithChildren, ReactNode, useMemo } from 'react';
import classnames from 'classnames';

import { RadioButtonGroupContext, IRadioButtonGroupContext } from './radio-button-group-context';
import { useCreateTestId } from '../../utils';
import { Label } from '../label';
import { StatusMessage } from '../status-message';

import styles from './radio-button-group.module.scss';

export interface IRadioButtonGroup extends IRadioButtonGroupContext {
  vertical?: boolean;
  testId?: string;
  id: string;
  label?: string;
  errorMessage?: string;
  errorMessagePrefix?: string;
}

export const RadioButtonGroup = ({
  groupName,
  onChange,
  required,
  errorMessage,
  errorMessagePrefix,
  label,
  vertical = false,
  testId,
  id,
  children,
}: PropsWithChildren<IRadioButtonGroup>): JSX.Element => {
  const context = useMemo(
    () => ({
      groupName,
      onChange,
      required,
    }),
    [groupName, onChange, required]
  );

  const dataRefId = useCreateTestId(testId);
  const radioButtonGroupLabelId = id ? `${id}-radio-button-group-label` : undefined;

  const wrapper = (radioGroupChildren: ReactNode) => (
    <>
      {Children.map(radioGroupChildren, (child) => {
        return !vertical ? child : <div className={styles.child}>{child}</div>;
      })}
    </>
  );

  return (
    <RadioButtonGroupContext.Provider value={context}>
      <div className={styles.evrRadioButtonGroup}>
        {label && (
          <Label required={required} id={radioButtonGroupLabelId}>
            {label}
          </Label>
        )}
        <div
          role="radiogroup"
          className={classnames(styles.wrapperInner, {
            [styles.col]: vertical,
            [styles.hasErrorMessage]: !!errorMessage,
          })}
          id={id}
          ref={dataRefId}
          aria-labelledby={radioButtonGroupLabelId}
        >
          {wrapper(children)}
        </div>
        <StatusMessage
          visible
          variant={'error'}
          statusMessage={errorMessage}
          statusMessagePrefix={errorMessagePrefix}
        />
      </div>
    </RadioButtonGroupContext.Provider>
  );
};
