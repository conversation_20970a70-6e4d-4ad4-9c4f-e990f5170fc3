import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { RadioButton } from './radio-button';
import { Button } from '../button';

export const scope = { RadioButton, Button };

Radio buttons are used when there are two or more mutually exclusive options and only one of them can be chosen.

This component is a singular radio button.

## Variations

### Default Status

A `RadioButton` in `default` status can be checked or unchecked. In either state it can disabled or enabled.

export const defaultCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            justifyContent: 'space-around',
            columnGap: '0.625rem'
        },
        grid: {
            display: 'grid',
            gap: '0.5rem 0.5rem',
            grid: 'auto auto / auto auto'
        },
        quadrantOne: {
            gridColumnStart: '1',
            gridColumnEnd: '2'
        },
        quadrantTwo: {
            gridColumnStart: '2',
            gridColumnEnd: '3'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row} role="radiogroup">{children}</div>
    );
    const Grid = ({ children }) => (
        <div style={styles.grid} role="radiogroup">{children}</div>
    );
    const QuadrantOne = ({ children }) => (
        <div style={styles.quadrantOne} role="radiogroup">{children}</div>
    );
    const QuadrantTwo = ({ children }) => (
        <div style={styles.quadrantTwo} role="radiogroup">{children}</div>
    );
    const QuadrantThree = QuadrantOne;
    const QuadrantFour = QuadrantTwo;
    return (
        <Grid>    
            <QuadrantOne>
            <RadioButton
                id="radio-id-groupOne" 
                groupName="groupOne" 
                value="checked" 
                checked={true} 
                onChange={()=>{}} 
                label="Checked" />    
            </QuadrantOne>
            <QuadrantTwo>
            <RadioButton
                id="radio-id-groupTwo" 
                groupName="groupTwo"
                value="unchecked"
                checked={false}
                onChange={()=>{}}
                label="Unchecked"/>
            </QuadrantTwo>          
            <QuadrantThree>
            <RadioButton
                id="radio-id-groupThree" 
                groupName="groupThree"
                value="checked"
                checked={true}
                onChange={()=>{}}
                label="Disabled-Checked"
                disabled={true}/>
            </QuadrantThree>
            <QuadrantFour>
            <RadioButton
                id="radio-id-groupFour" 
                groupName="groupFour"
                value="unchecked"
                checked={false}
                onChange={()=>{}}
                label="Disabled-Unchecked"
                disabled={true}/>      
            </QuadrantFour>
        </Grid>
    );
}`;

<CodeExample scope={scope} code={defaultCode} />

### Error Status

A `RadioButton` in `error` status can be checked or unchecked. In either state it can disabled or enabled.

export const errorCode = `() => {
    const styles = {
        row: {
            display: 'inline-flex',
            justifyContent: 'space-around',
            columnGap: '0.625rem'
        },
        grid: {
            display: 'grid',
            gap: '0.5rem 0.5rem',
            grid: 'auto auto / auto auto'
        },
        quadrantOne: {
            gridColumnStart: '1',
            gridColumnEnd: '2'
        },
        quadrantTwo: {
            gridColumnStart: '2',
            gridColumnEnd: '3'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row} role="radiogroup">{children}</div>
    );
    const Grid = ({ children }) => (
        <div style={styles.grid} role="radiogroup">{children}</div>
    );
    const QuadrantOne = ({ children }) => (
        <div style={styles.quadrantOne} role="radiogroup">{children}</div>
    );
    const QuadrantTwo = ({ children }) => (
        <div style={styles.quadrantTwo} role="radiogroup">{children}</div>
    );
    const QuadrantThree = QuadrantOne;
    const QuadrantFour = QuadrantTwo;
    return (
        <Grid>    
            <QuadrantOne>
            <RadioButton
                id="radio-id-groupOne-2" 
                groupName="groupOne" 
                value="checked" 
                checked={true} 
                status="error"
                onChange={()=>{}} 
                label="Checked" />    
            </QuadrantOne>
            <QuadrantTwo>
            <RadioButton
                id="radio-id-groupTwo-2" 
                groupName="groupTwo"
                value="unchecked"
                checked={false}
                status="error"
                onChange={()=>{}}
                label="Unchecked"/>
            </QuadrantTwo>          
            <QuadrantThree>
            <RadioButton
                id="radio-id-groupThree-2" 
                groupName="groupThree"
                value="checked"
                checked={true}
                status="error"
                onChange={()=>{}}
                label="Disabled-Checked"
                disabled={true}/>
            </QuadrantThree>
            <QuadrantFour>
            <RadioButton
                id="radio-id-groupFour-2" 
                groupName="groupFour"
                value="unchecked"
                checked={false}
                status="error"
                onChange={()=>{}}
                label="Disabled-Unchecked"
                disabled={true}/>      
            </QuadrantFour>
        </Grid>
    );
}`;

<CodeExample scope={scope} code={errorCode} />

### Handling State and Events

Radio buttons are controlled components. The `checked` prop controls the checked state. <br/>
To update state and perform other side effects, an `onChange` function must be provided. <br/>
Other props like `groupName`, `value`, and `label` must be given.

export const controlledCode = `() => {  
    const styles = {
        row: {
            display: 'inline-flex',
            justifyContent: 'space-around',
            columnGap: '0.625rem'
        }
    };
    const Row = ({ children }) => (
        <div style={styles.row} role="radiogroup">{children}</div>
    );
    const [value, setValue] = React.useState('one');
    function handleChange(value){ setValue(value); };
    return (
      <Row>
        <RadioButton 
          id="radio-id-exampleGroup" 
          groupName="stateExampleGroup" 
          value="one"
          checked={'one'=== value } 
          onChange={handleChange} 
          label="Option One" />
        <RadioButton 
          id="radio-id-exampleGroup-2" 
          groupName="stateExampleGroup" 
          value="two"
          checked={'two'=== value } 
          onChange={handleChange} 
          label="Option Two" />
      </Row>
    )  
  }
`;

<CodeExample scope={scope} code={controlledCode} />

### Radio button with multi-line and prefix text

When `prefixText` is provided and `multiline` prop is set to true, radio button will be aligned with prefix text and label
can wrapped in multiple lines underneath. <br/>  
If `prefixText` is provided, but `multiline` is set to false then both prefix text and label will be on the same line. <br/>

To meet accessibility standard, entire label and radio button are focusable and interactive, meaning anywhere inside of blue focus outline can be clicked to toggle radio button.

export const prefixTextandMultilineCode = `() => {
    const styles = {
        column: {
            display: 'flex',
            flexDirection: 'column',
            width: '200px',
            gap: '1rem'
        }
    };
    const Column = ({ children }) => (
        <div style={styles.column} role="radiogroup">{children}</div>
    );
    const [value, setValue] = React.useState('one');
    function handleChange(value){ setValue(value); };
    return (
        <Column>
            <RadioButton id="radio-id-prefix-text" prefixText='Prefix Text' multiline={true} label="This is a multi-line label that is stacked under prefix text." checked={"one" === value} value="one" onChange={handleChange} />
            <RadioButton id="radio-id-prefix-text-2" prefixText='Prefix Text' multiline={false} label="Single line." checked={"two" === value} value="two" onChange={handleChange} />
        </Column>
    );
}`;

<CodeExample scope={scope} code={prefixTextandMultilineCode} />

## Accessing RadioButton using ref

Click on the Button to access the RadioButton, refer to the console for the element details.

export const refCode = `()=>{
     const styles = {
        row: {
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
        },
         column: {
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            flexDirection: 'column',
            width: '100%',
            rowGap: '10px'
        }
    }
     const Row = ({ children }) => (
        <div style={styles.row}>{children}</div>
    );
    const Column = ({ children }) => (
        <div style={styles.column}>{children}</div>
    );
    const ref=React.useRef(null);
    return (
      <Column>
        <Row>
            <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
        </Row>
        <Row>
            <RadioButton 
                id="radio-id-click" 
                ref={ref}
                value="checked" 
                checked={true} 
                label="Checked" /> 
        </Row>
      </Column>
    );
}   
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Use radio buttons when there are two or more mutually exclusive options. If there is only one option, consider
a different component, like a <LinkTo kind="Components/Checkbox">Checkbox</LinkTo>.

Related radio buttons should be grouped in a <LinkTo kind="Components/Radio Buttons/Radio Button Group">RadioButtonGroup</LinkTo> to properly navigate the buttons with assistive technologies, such as keyboards or screen readers.

## Accessibility

A unique `id` is generated if none is provided to help assistive technologies.

Although this component does not require a label to be set, one must be provided in your app to satsify accessibility requirements.

All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
