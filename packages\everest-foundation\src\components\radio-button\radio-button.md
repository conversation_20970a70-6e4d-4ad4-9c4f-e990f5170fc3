# Radio Button

## Summary

Research and document implementations for the Everest Radio Button.

- Start Date: 2022-02-08
- Figma link: https://www.figma.com/file/6MRT3O96cACFwhFpRH6T1r/%F0%9F%A7%AASelection-Controls?node-id=536%3A10959

## Detailed Design

The radio button is intended to be a child of a controlled radio button group component. The group component will follow in another PBI.

## API

1. **value**: `string`  
   The value of the radio button.
1. **checked**: `boolean`  
   Indicates if the radio button is checked. Default is false.
1. **status**: `string`
   Indicates status state of radio button. Default is default.
1. **groupName**: `string | undefined`  
   Optional. Sets the group the radio button belongs to. This should be set on either the radio button or the radio button group. If none is given, a random name is generated.
1. **onChange**: `undefined | (value: string) => undefined`  
   Optional. User supplied function that runs when the button is checked. Usually this would include updating React state. This should be set on either the radio button or the radio button group.
1. **dir**: `undefined | 'ltr' | 'rtl'`  
   Optional. Parameter indicating the direction of the radio button.
1. **label**: `string | undefined`  
   Optional user provided label. If none is given, no label text is rendered.
1. **disabled**: `boolean | undefined`  
   Optional. Sets the `disabled` attribute on the radio button.
1. **required**: `boolean | undefined`  
   Optional. Sets the button group as required or optional.
1. **testId**: `string | undefined`  
   Optional. Sets `data-testid` attribute on the containing html element.
1. **id**: `string | undefined`  
   Required id for the radio button label.
1. **onBlur**: `undefined | React.FocusEventHandler<HTMLInputElement>`  
   Optional callback on blur.
1. **onFocus**: `undefined | React.FocusEventHandler<HTMLInputElement>`  
   Optional callback on focus.

## Accessibility

- When focused the component will present either a css outline or use the focus-ring component as a visual indicator. When the prop `disabled` is passed, the radio button will not be able to be focused.
- A guide to accessible radio buttons and groups can be found at https://www.w3.org/TR/wai-aria-practices/#radiobutton. The two approaches on radio button groups are (i) roving tab index with focus and (ii) use of `aria-activedescendant`. The more reliable method appears to be tab indexes with focus (e.g. https://zellwk.com/blog/element-focus-vs-aria-activedescendant/).

## Q&A

**Q: Are we planning to build a radio group with radio buttons as children of the group?**

A: The ask for now is only the button element itself, not necessarily grouping.

**Q: Are radio buttons going to have different placements for the label?**

A: The button position and label are as pictured in figma, for now.

**Q: What is the use-case for no label?**

A: There is no specific use-case. Design wants to allow for flexibility and follow an atomic structure. The label should be optional.

**Q: How do we represent keyboard focus?**

A: Keyboard focus is added to figma document, and consists of outlining the container on focus.

**Q: Do we plan on other color scheme variants?**

A: Not currently.

**Q: When using the keyboard, can I tab onto a disabled radio button?**

A: The component will only be semantically disabled using `aria-disabled`. This is consistent with other components like checkbox and button.

**Q: How do we style Label when disabled?**

A: A prop will be used to inform Label's presentation. This will follow.

## Future Considerations

- A parent component with role `radiogroup` should exist and have an appropriate label. Radio button props including `groupName`, `onChange`, `disabled`, etc, could be provided to a radio button by the parent grouping component.

## Other Design Systems

**Carbon**

- https://www.carbondesignsystem.com/components/radio-button/usage/
- API resembles MDN html spec (https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/radio)
- Compositional approach that distinguishes between groups and child buttons
- Appears to use uncontrolled components

**Polaris**

- https://polaris.shopify.com/components/forms/radio-button#navigation
- Does not have an explicit radio grouping component (layout arranged with `<Stack>` component), but prop `name` appears to render as the html `name`
- Has additional help text options

**Material UI**

- https://mui.com/components/radio-buttons/
- Compositional with radio grouping component and radio controls that are wrapped with form labels
- A custom hook is offered to facilitate getting props from a provider for the parent grouping element.

**Grommet**

- Radio button: https://v2.grommet.io/radiobutton
- Radio button group: https://v2.grommet.io/radiobuttongroup
- Individual radio buttons can be rendered, however for radio groups arrays can be passed to represent buttons and their labels

**React Bootstrap**

- https://react-bootstrap.github.io/components/buttons/#checkbox--radio
- Features an abstracted `<ToggleButton>` component that can be typed as radio, and groups buttons using a `<ButtonGroup>` component
- Offers controlled and uncontrolled component versions

## Known Issues

**Storybook zoom in issue with Safari** Storybook zoom in is not working as expected, it increases the border thickness. Safari browser zoom doesn't have any issue.

## Required PBIs

1. Develop the component (React + a11y + manual dev testing)
1. Component testing (unit + integration + visual)
1. Create or implement a random id generator.
1. Plan and create a grouping component, possibly separating logical and presentational concerns.
1. Revise/refactor as required after the creation of a radio/button grouping component
1. Documentation preparation

## Acceptance Criteria

1.  - Component to be named `<RadioButton>`
    - Build component and setup in Storybook (Components, Foundations &rarr; Automation)
    - Manually verify
      - API works as expected (within the context of a singular button)
      - a11y (e.g. presence of aria attributes, screen reader narrative, keyboard and pointer functionality)
      - different states and visual styles match figma. Confirm and QA with designers.
1.  - Build unit tests (RTL) to confirm
      - component renders, API and functionality work as expected (e.g. keyboard interactions)
    - Build integration tests to confirm
      - functionality (checked, unchecked, disabled)
      - visual tests

Acceptance criteria for PBIs 3 - 5 will be determined later.
