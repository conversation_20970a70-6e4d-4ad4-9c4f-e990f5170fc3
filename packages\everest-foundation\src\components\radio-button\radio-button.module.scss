.evrRadioButton {
  &.input {
    position: absolute;
    overflow: hidden;
    padding: 0;
    border: 0;
    margin: -1px;
    block-size: 1px;
    clip: rect(0, 0, 0, 0);
    inline-size: 1px;
    visibility: inherit;
    white-space: nowrap;
  }

  //Firefox doesnt automatically adjust the margins after hiding the default radio in &.input
  &.input:-moz-any(input) {
    margin: -2px;
  }

  &.containerSvg {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 0 0 var(--evr-size-sm);
    width: calc(var(--evr-size-sm) + var(--evr-border-width-thick-rem));
    height: calc(var(--evr-size-sm) + var(--evr-border-width-thick-rem));
    margin: var(--evr-spacing-3xs) 0;
    padding: 0;
    border-radius: 100%;
    box-sizing: border-box;
    outline: none;
    cursor: pointer;
  }

  &.border {
    fill: var(--evr-surfaces-primary-default);
    width: calc(var(--evr-size-sm) + var(--evr-border-width-thick-rem));
    height: calc(var(--evr-size-sm) + var(--evr-border-width-thick-rem));
    &.borderCircle {
      stroke: var(--evr-interactive-primary-default);
      stroke-width: var(--evr-border-width-thin-px); //using px as safar is having issues with zoom with rem
    }
    &.centerDot {
      fill: var(--evr-interactive-primary-default);
    }
  }

  &.borderUnchecked {
    width: calc(var(--evr-size-sm) + var(--evr-border-width-thick-rem));
    height: calc(var(--evr-size-sm) + var(--evr-border-width-thick-rem));
    stroke: var(--evr-borders-primary-default);
    stroke-width: var(--evr-border-width-thin-px); //using px as safar is having issues with zoom with rem
  }

  &:hover {
    .evrRadioButton {
      &.border {
        &.borderCircle {
          stroke: var(--evr-interactive-primary-hovered);
        }
        &.centerDot {
          fill: var(--evr-interactive-primary-hovered);
        }
      }
      &.borderUnchecked {
        stroke: var(--evr-borders-primary-hovered);
      }
    }
  }

  &:active {
    .evrRadioButton {
      &.border {
        &.borderCircle {
          stroke: var(--evr-interactive-primary-hovered);
        }
        &.centerDot {
          fill: var(--evr-interactive-primary-hovered);
        }
      }
    }
  }

  &.error {
    .evrRadioButton {
      width: calc(var(--evr-size-sm) + var(--evr-size-2xs)); // 20px
      height: calc(var(--evr-size-sm) + var(--evr-size-2xs)); // 20px
      &.border {
        &.borderCircle {
          stroke: var(--evr-interactive-status-error-pressed);
          stroke-width: var(--evr-border-width-thick-px);
        }
        &.centerDot {
          fill: var(--evr-interactive-status-error-default);
        }
      }
      &.borderUnchecked {
        stroke: var(--evr-interactive-status-error-pressed);
        stroke-width: var(--evr-border-width-thick-px);
      }
    }
    &.containerSvg {
      width: calc(var(--evr-size-sm) + var(--evr-size-2xs)); // 20px
      height: calc(var(--evr-size-sm) + var(--evr-size-2xs)); // 20px
    }
  }

  &.disabled {
    cursor: not-allowed;
    .evrRadioButton {
      &.border {
        fill: var(--evr-inactive-surfaces);
        &.borderCircle {
          stroke: var(--evr-inactive-border);
        }
        &.centerDot {
          fill: var(--evr-inactive-content);
        }
      }
      &.borderUnchecked {
        stroke: var(--evr-inactive-border);
      }
    }
  }

  &.textDisabled {
    color: var(--evr-inactive-content);
    cursor: not-allowed;
  }

  &.multiLineLabelTextWithPrefixText {
    display: flex;
    justify-items: center;
    align-items: center;
    padding-inline-start: calc(var(--evr-size-lg) - var(--evr-border-width-thick-rem));
    &.prefixTextPadding {
      padding-inline-start: var(--evr-spacing-3xs);
      padding-inline-end: var(--evr-spacing-3xs);
    }
  }

  &.multiLineLabelTextSpacingError {
    padding-inline-start: var(--evr-spacing-lg);
  }

  &.multiLineDisplay {
    display: inline-flex;
    gap: var(--evr-size-xs);
    align-items: flex-start;
  }

  &.createSpace {
    white-space: pre-wrap;
  }

  &.labelContainer {
    display: flex;
    flex-direction: column;
    cursor: pointer;
  }
}

.evrRadioButtonContainer {
  display: inline-flex;

  label {
    gap: var(--evr-size-xs);
  }
}

@media (forced-colors: active) {
  .evrRadioButton {
    &.border {
      border-color: ButtonText;
      svg {
        fill: WindowText;
      }
    }
  }

  .evrRadioButton.disabled,
  .evrRadioButton.disabled:checked {
    color: GrayText;
    border-color: GrayText;
    svg {
      fill: GrayText;
    }
  }
}
