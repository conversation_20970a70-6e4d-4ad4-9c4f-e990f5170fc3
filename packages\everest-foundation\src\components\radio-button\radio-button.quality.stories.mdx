import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { userEvent } from '@storybook/test';

import { RadioButton } from './radio-button';
import { Chromatic } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Radio Button"
  component={RadioButton}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    value: 'value',
    onChange: () => undefined,
    label: 'label',
    testId: 'radio-button-test-id',
  }}
/>

# Radio Button

## Live Demo

<Canvas>
  <Story name="Default Checked">
    {(args) => (
      <RadioButton {...args} id="radio-button-checked-id" checked={true} groupName={'groupName1'}></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Default Checked with Focus Ring"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => (
      <RadioButton {...args} id="radio-button-checked-focus-id" checked={true} groupName={'groupName1'}></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Unchecked">
    {(args) => (
      <RadioButton {...args} id="radio-button-unchecked-id" groupName={'groupName1'} checked={false}></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Checked">
    {(args) => (
      <RadioButton
        {...args}
        id="radio-button-error-checked-id"
        checked={true}
        groupName={'groupName2'}
        status="error"
      ></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Unchecked">
    {(args) => (
      <RadioButton
        {...args}
        id="radio-button-error-unchecked-id"
        groupName={'groupName2'}
        checked={false}
        status="error"
      ></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Checked">
    {(args) => (
      <RadioButton
        {...args}
        id="radio-button-disabled-checked-id"
        checked={true}
        disabled={true}
        groupName={'groupName3'}
      ></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Disabled Unchecked">
    {(args) => (
      <RadioButton
        {...args}
        id="radio-button-disabled-unchecked-id"
        disabled={true}
        groupName={'groupName3'}
        checked={false}
      ></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Single Line Prefix Multi Line Label">
    {(args) => (
      <div style={{ width: '200px' }}>
        <RadioButton
          {...args}
          id="radio-button-prefix-single-id"
          groupName={'groupName4'}
          prefixText="Prefix Text"
          label="This is a very long multi line label"
          checked={false}
          multiline={true}
        ></RadioButton>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Multi Line Prefix Multi Line Label">
    {(args) => (
      <div style={{ width: '200px' }}>
        <RadioButton
          {...args}
          id="radio-button-prefix-multi-id"
          groupName={'groupName4'}
          prefixText="This is a very long prefix text"
          label="This is a very long multi line label"
          checked={false}
          multiline={true}
        ></RadioButton>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Single Line Prefix Single Line Label">
    {(args) => (
      <RadioButton
        {...args}
        id="radio-button-single-prefix-single-id"
        groupName={'groupName4'}
        prefixText="Prefix Text"
        label="This is a label"
        checked={false}
      ></RadioButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Single Line Prefix Multi Line Label">
    {(args) => (
      <div style={{ width: '200px' }}>
        <RadioButton
          {...args}
          id="radio-button-error-single-prefix-id"
          groupName={'groupName5'}
          prefixText="Prefix Text"
          label="This is a very long multi line label"
          checked={false}
          multiline={true}
          status="error"
        ></RadioButton>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Multi Line Prefix Multi Line Label">
    {(args) => (
      <div style={{ width: '200px' }}>
        <RadioButton
          {...args}
          id="radio-button-error-multi-id"
          groupName={'groupName5'}
          prefixText="This is a very long prefix text"
          label="This is a very long multi line label"
          checked={false}
          multiline={true}
          status="error"
        ></RadioButton>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Error Single Line Prefix Single Line Label">
    {(args) => (
      <RadioButton
        {...args}
        id="radio-button-single-error-single-id"
        groupName={'groupName5'}
        prefixText="Prefix Text"
        label="This is a label"
        checked={false}
        status="error"
      ></RadioButton>
    )}
  </Story>
</Canvas>
