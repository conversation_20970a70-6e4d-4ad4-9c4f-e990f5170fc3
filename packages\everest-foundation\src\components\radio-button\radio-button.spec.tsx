import * as React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { RadioButton } from './radio-button';

describe('[RadioButton]', () => {
  const groupName = 'groupNameTest';
  const value = 'valueTest';
  const onChange = jest.fn();
  const label = 'labelTest';
  const prefixText = 'Prefix Text';
  const testId = 'radio-button-test-id';
  const onFocus = jest.fn();
  const onBlur = jest.fn();
  const getRadioButtonLabel = () => screen.getByTestId(`${testId}-label`);
  const getRadioButtonInput = () => screen.getByTestId(`${testId}-radio`);
  const radioLabelId = 'radio-label-id';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  [
    {
      name: 'Default Checked RadioButton with label',
      jsx: (
        <RadioButton
          id={radioLabelId}
          groupName={groupName}
          value={value}
          checked={true}
          onChange={onChange}
          label={label}
          testId={testId}
        />
      ),
    },
    {
      name: 'Default UnChecked RadioButton with label',
      jsx: (
        <RadioButton
          id={radioLabelId}
          groupName={groupName}
          value={value}
          checked={false}
          onChange={onChange}
          label={label}
          testId={testId}
        />
      ),
    },
    {
      name: 'Error Checked RadioButton with label',
      jsx: (
        <RadioButton
          id={radioLabelId}
          groupName={groupName}
          value={value}
          checked={true}
          status={'error'}
          onChange={onChange}
          label={label}
          testId={testId}
        />
      ),
    },
    {
      name: 'Error UnChecked RadioButton with label',
      jsx: (
        <RadioButton
          id={radioLabelId}
          groupName={groupName}
          value={value}
          checked={false}
          status={'error'}
          onChange={onChange}
          label={label}
          testId={testId}
        />
      ),
    },
    {
      name: 'Disabled Checked RadioButton with label',
      jsx: (
        <RadioButton
          id={radioLabelId}
          groupName={groupName}
          value={value}
          checked={true}
          onChange={onChange}
          disabled={true}
          label={label}
          testId={testId}
        />
      ),
    },
    {
      name: 'Disabled UnChecked RadioButton with label',
      jsx: (
        <RadioButton
          id={radioLabelId}
          groupName={groupName}
          value={value}
          checked={false}
          onChange={onChange}
          disabled={true}
          label={label}
          testId={testId}
        />
      ),
    },
    // If there is no label text then it is an a11y violation
  ].forEach(function (item) {
    it(`${item.name} does not have accessibility violations`, async () => {
      const { container } = render(item.jsx);
      await waitFor(() => {
        expect(getRadioButtonLabel()).toBeInTheDocument();
        expect(getRadioButtonInput()).toBeInTheDocument();
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  it('should render without issue', () => {
    render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    expect(getRadioButtonLabel()).toBeInTheDocument();
    expect(getRadioButtonInput()).toBeInTheDocument();
  });

  it('should have name attribute', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    expect(getByLabelText(label)).toBeInTheDocument();
    expect(getByLabelText(label)).toHaveAttribute('name', groupName);
  });

  it('should have value attribute', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    expect(getByLabelText(label)).toBeInTheDocument();
    expect(getByLabelText(label)).toHaveAttribute('value', value);
  });

  it('should display checked attribute when checked is true', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    expect(getByLabelText(label)).toBeInTheDocument();
    expect(getByLabelText(label)).toHaveAttribute('checked');
  });

  it('should not have checked attribute when checked is false', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    expect(getByLabelText(label)).toBeInTheDocument();
    expect(getByLabelText(label)).not.toHaveAttribute('checked');
  });

  it('should display error state when status is set to error', () => {
    const { container } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        status={'error'}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    expect(container.getElementsByClassName('error').length).toBe(1);
  });

  it('should have label text when given', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    expect(getByLabelText(label)).toBeInTheDocument();
  });

  it('should call onChange when clicked and checked is false', async () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    await userEvent.click(getByLabelText(label));
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it('should not call onChange when clicked and checked is true', async () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    await userEvent.click(getByLabelText(label));
    expect(onChange).toHaveBeenCalledTimes(0);
  });

  it('should call onChange when the keyboard space key is pressed on label', async () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
      />
    );
    getByLabelText(label).focus();
    await userEvent.keyboard(' ');
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it('should not have label text when not provided', () => {
    const { container } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        testId={testId}
      />
    );
    expect(container.classList.contains('labelText')).toBe(false);
  });

  it('should call onFocus when label is focused', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        testId={testId}
        label={label}
      />
    );
    getByLabelText(label).focus();
    expect(onFocus).toHaveBeenCalledTimes(1);
  });

  it('should call onBlur when label is blurred', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        testId={testId}
        label={label}
      />
    );
    getByLabelText(label).focus();
    getByLabelText(label).blur();
    expect(onBlur).toHaveBeenCalledTimes(1);
  });

  it('should have disabled attribute when disabled is true', () => {
    render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
        disabled={true}
      />
    );

    const btnInput = getRadioButtonInput();
    expect(btnInput).toHaveAttribute('disabled');
    expect(btnInput).not.toHaveAttribute('aria-disabled');
  });

  it('should not call onChange when the keyboard space key is pressed on label and disabled is true', async () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        onChange={onChange}
        label={label}
        testId={testId}
        disabled={true}
      />
    );
    getByLabelText(label).focus();
    await userEvent.keyboard(' ');
    expect(onChange).toHaveBeenCalledTimes(0);
  });

  it('should have required attribute when required prop is true', () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        label={label}
        required={true}
        testId={testId}
      />
    );
    expect(getByLabelText(label)).toHaveAttribute('required');
  });

  it('label should have specified id attribute when given', () => {
    render(
      <RadioButton
        groupName={groupName}
        value={value}
        checked={true}
        onChange={onChange}
        label={label}
        testId={testId}
        id={radioLabelId}
      />
    );
    expect(getRadioButtonLabel()).toHaveAttribute('id', radioLabelId);
  });

  it('should have a groupName if none is given', () => {
    const { getByLabelText } = render(
      <RadioButton id={radioLabelId} value={value} checked={true} onChange={onChange} label={label} testId={testId} />
    );
    expect(getByLabelText(label)).toHaveAttribute('name');
    expect(getByLabelText(label)).not.toHaveAttribute('name', '');
  });

  it('should have a label id when given', () => {
    render(
      <RadioButton value={value} checked={true} onChange={onChange} label={label} testId={testId} id={radioLabelId} />
    );
    expect(getRadioButtonLabel()).toHaveAttribute('id', 'radio-label-id');
  });

  it('should not call onChange if none is given', async () => {
    const { getByLabelText } = render(
      <RadioButton
        id={radioLabelId}
        groupName={groupName}
        value={value}
        checked={false}
        label={label}
        testId={testId}
      />
    );
    await userEvent.click(getByLabelText(label));
    expect(onChange).toHaveBeenCalledTimes(0);
  });

  it('prefix text should render when provided', () => {
    render(
      <RadioButton
        value={value}
        checked={true}
        onChange={onChange}
        label={label}
        testId={testId}
        prefixText={prefixText}
        id={radioLabelId}
      />
    );
    screen.getByText(prefixText);
  });
});
