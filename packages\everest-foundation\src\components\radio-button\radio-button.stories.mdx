import { Meta, Story, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { RadioButton } from './radio-button';
import { action } from '@storybook/addon-actions';
import Examples from './radio-button.examples.mdx';

<Meta
  title="Components/Radio Buttons/Radio Button"
  component={RadioButton}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/2ohImRKxDbNG714LklG2gY/Everest-Web?node-id=3157%3A9809&t=HEIyoS0N6cPMADjO-0',
    },
  }}
  argTypes={{
    groupName: {
      type: 'string | undefined',
      control: 'text',
      description:
        'Optional. Sets the group name the radio button belongs to. If none is given, a random `groupName` is generated. This should be set on either the `RadioButton` or `RadioButtonGroup`.',
    },
    value: {
      type: 'string',
      control: 'text',
      description: 'The value of the radio button.',
    },
    checked: {
      type: 'boolean',
      control: 'boolean',
      description: 'Indicates if the radio button is checked.',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    status: {
      control: 'radio',
      options: ['default', 'error'],
      description: 'Sets color status of radio button',
    },
    onChange: {
      control: '-',
      description:
        'Optional. User supplied function that runs when the button is checked. Usually this would include updating React state. This should be set on either the `RadioButton` or the `RadioButtonGroup`.',
    },
    dir: {
      type: 'enum',
      control: 'select',
      options: ['ltr', 'rtl'],
      description: 'Optional. Parameter indicating the direction of the radio button. Not currently implemented.',
      table: { disable: true },
    },
    label: {
      control: 'text',
      type: 'string',
      description: 'Optional user provided label. If none is given, no label text is rendered.',
    },
    disabled: {
      control: 'boolean',
      description: 'Optional. Sets `disabled` attribute on the radio button.',
    },
    testId: {
      type: 'string',
      control: 'text',
      description:
        'Optional. Sets `data-testid` attributes on the html elements. The label receives `data-testid={{testId}}-label` and the radio input receives `data-testid={{testId}}-radio`',
    },
    id: {
      type: 'string',
      control: 'text',
      description: 'Required id for the radio button label.',
    },
    onBlur: {
      control: '-',
      description: 'Optional callback on blur.',
    },
    onFocus: {
      control: '-',
      description: 'Optional callback on focus.',
    },
    required: {
      type: 'boolean',
      control: 'boolean',
      description: 'Optional. Sets the button group as required.',
    },
    prefixText: {
      control: 'text',
      type: 'string',
      description: 'Optional user provided prefix text. If none is given, no prefix text is rendered.',
    },
    multiline: {
      control: 'boolean',
      description: 'Optional. Sets multi-line text format for the label.',
    },
  }}
  args={{
    groupName: 'groupName',
    value: 'value',
    checked: false,
    status: 'default',
    onChange: action('onChange'),
    dir: 'ltr',
    label: 'This is a label',
    disabled: false,
    testId: 'radio-button-test-id',
    id: 'radio-button-id',
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    required: false,
    prefixText: 'Prefix Text',
    multiline: true,
  }}
/>

# Radio Button

<Examples />

## Live Demo

<Canvas>
  <Story name="Radio Button">
    {(args) => {
      const [{ checked }, updateArgs] = useArgs();
      const handleChange = (e) => {
        updateArgs({ checked: !checked });
        action('onChange')(e);
      };
      return <RadioButton {...args} checked={checked} onChange={handleChange} />;
    }}
  </Story>
</Canvas>

<ArgsTable story="Radio Button" />
