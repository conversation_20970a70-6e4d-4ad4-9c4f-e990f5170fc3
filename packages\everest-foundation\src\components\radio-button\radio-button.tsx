import React from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { generateId, mergeRefs, useCreateTestId } from '../../utils';
import { resolvePropsContext } from '../../utils/resolve-props-context';
import { Label } from '../label';
import { RadioButtonGroupContext, IRadioButtonGroupContext } from '../radio-button-group/radio-button-group-context';

import styles from './radio-button.module.scss';

export type RadioButtonStatus = 'default' | 'error';

export interface IRadioButton {
  value: string;
  checked: boolean;
  status?: RadioButtonStatus;
  groupName?: string;
  onChange?: (value: string) => void;
  dir?: 'ltr' | 'rtl';
  label?: string;
  testId?: string;
  id: string;
  onFocus?: React.FocusEventHandler<HTMLInputElement>;
  onBlur?: React.FocusEventHandler<HTMLInputElement>;
  disabled?: boolean;
  required?: boolean;
  prefixText?: string;
  multiline?: boolean;
  ref?: React.ForwardedRef<HTMLInputElement>;
}

export const RadioButton = React.forwardRef<HTMLInputElement, IRadioButton>((props: IRadioButton, ref) => {
  const context = React.useContext(RadioButtonGroupContext);
  const {
    groupName,
    onChange,
    disabled,
    required,
    value,
    checked,
    status,
    label,
    testId,
    id,
    onFocus,
    onBlur,
    prefixText = '',
    multiline = false,
    // dir,
  } = resolvePropsContext<IRadioButton, IRadioButtonGroupContext>(props, context);
  const groupNameRef = React.useRef(generateId());
  const dataRefRadio = useCreateTestId(testId ? `${testId}-radio` : undefined);
  const dataRefLabel = useCreateTestId(testId ? `${testId}-label` : undefined);
  const errorState = status === 'error' && !disabled;

  function handleChange(btnValue: string) {
    if (onChange && !checked && (disabled === false || typeof disabled === 'undefined')) {
      onChange(btnValue);
    }
  }
  function handleFocus(e: React.FocusEvent<HTMLInputElement>) {
    !disabled && onFocus && onFocus(e);
  }
  function handleBlur(e: React.FocusEvent<HTMLInputElement>) {
    !disabled && onBlur && onBlur(e);
  }

  const renderInputElement = (
    <>
      <input
        className={classnames(styles.evrRadioButton, styles.input)}
        type="radio"
        name={groupName || groupNameRef.current} // groupName is a required string from context, so need || instead of ??
        value={value}
        checked={checked}
        onChange={() => handleChange(value)}
        onFocus={handleFocus}
        onBlur={handleBlur}
        required={required}
        disabled={disabled}
        ref={mergeRefs([ref, dataRefRadio])}
        aria-describedby={`${multiline ? groupName || groupNameRef.current : ''}`}
      />
      <span
        className={classnames(styles.evrRadioButton, styles.containerSvg, {
          [styles.error]: errorState,
          [styles.disabled]: disabled,
        })}
        aria-hidden={'true'}
      >
        <svg className={classnames(styles.evrRadioButton, styles.border)}>
          <circle
            cx="50%"
            cy="50%"
            r={`${errorState ? '45%' : '47%'}`}
            className={classnames(
              styles.evrRadioButton,
              styles.borderCircle,
              { [styles.border]: checked },
              { [styles.borderUnchecked]: !checked }
            )}
          ></circle>
          {checked ? (
            <circle
              cx="50%"
              cy="50%"
              r={`${errorState ? '20%' : '22%'}`}
              className={classnames(styles.evrRadioButton, styles.border, styles.centerDot)}
            ></circle>
          ) : null}
        </svg>
      </span>
    </>
  );

  return (
    <FocusRing variant="focusring">
      {multiline ? (
        <label id={id} ref={dataRefLabel} className={classnames(styles.evrRadioButton, styles.labelContainer)}>
          <div className={classnames(styles.evrRadioButton, styles.multiLineDisplay)}>
            {renderInputElement}
            <div
              className={classnames(
                styles.evrRadioButton,
                styles.multiLineLabelTextWithPrefixText,
                styles.prefixTextPadding,
                { [styles.textDisabled]: disabled },
                'evrBodyText2',
                'evrBold'
              )}
            >
              {prefixText}
            </div>
          </div>
          <div
            className={classnames(
              styles.evrRadioButton,
              styles.multiLineLabelTextWithPrefixText,
              {
                [styles.multiLineLabelTextSpacingError]: errorState,
              },
              { [styles.textDisabled]: disabled },
              'evrBodyText2'
            )}
            id={groupName || groupNameRef.current}
            aria-hidden={true}
          >
            {label}
          </div>
        </label>
      ) : (
        <div className={styles.evrRadioButtonContainer}>
          <Label disabled={disabled} id={id} testId={testId ? `${testId}-label` : undefined} textPadding>
            {renderInputElement}
            <div className={classnames(styles.evrRadioButton, { [styles.textDisabled]: disabled }, 'evrBodyText2')}>
              {prefixText && prefixText.length > 0 && (
                <span className={classnames(styles.evrRadioButton, styles.createSpace, 'evrBold')}>{prefixText} </span>
              )}
              {label}
            </div>
          </Label>
        </div>
      )}
    </FocusRing>
  );
});

RadioButton.displayName = 'RadioButton';
