import React, { forwardRef } from 'react';

import { Button } from '../button';
import { Divider } from '../divider';

import styles from './search-field.module.scss';
import listBoxFooterItemStyles from '../list-box-footer-item/list-box-footer-item.module.scss';

export interface ISearchFieldOverlayFooter {
  id: string;
  testId?: string;
  label: string;
  onClick: (e: React.MouseEvent<HTMLButtonElement> | React.KeyboardEvent<HTMLButtonElement>) => void;
}

export const SearchFieldOverlayFooter = forwardRef<HTMLDivElement, ISearchFieldOverlayFooter>((props, ref) => {
  const { id, testId, label, onClick } = props;

  return (
    <>
      <div className={listBoxFooterItemStyles.evrListBoxFooterDivider}>
        <Divider />
      </div>
      <div className={styles.evrSearchFieldListBoxFooter} id={id} data-testid={testId} ref={ref}>
        <Button
          id={`${id}-see-all-results-button`}
          testId={testId ? `${testId}-see-all-results-button` : undefined}
          variant="tertiaryNeutral"
          label={label}
          onClick={onClick}
          onKeyDown={(e) => {
            if (e.key === 'Enter') onClick?.(e);
          }}
        />
      </div>
    </>
  );
});

SearchFieldOverlayFooter.displayName = 'SearchFieldOverlayFooter';
