import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { SearchField } from './search-field';
import { SearchFieldOverlayFooter } from './search-field-overlay-footer';
import { Button } from '../button';
import { Icon } from '../icon';
import { Warning } from '../../../.storybook/docs/shared/status-banner';
import { FunctionUtil } from '@platform/core';
import { Markdown } from '@storybook/blocks';
import IDataItem from '../list-item/i-data-item.md?raw';

export const scope = { SearchField, Button, Icon, FunctionUtil, SearchFieldOverlayFooter };

## Overview

SearchField can be used to handle two different types of search: basic and with suggestions. Details for both modes are below.

## Usage

### Basic SearchField

SearchField is a controlled component so it doesn't store or update its states. There are props required to control the state of the component to create a functional SearchField.

- `inputValue` - controls the `SearchField` value
- `onInputValueChange` - callback when `SearchField` value has updated

We follow a **controlled** component approach to SearchField. Instead of having state determined internally/by the DOM, SearchField reacts to state that is passed in. `onInputValueChange` behaviour must also be determined by a change handler set by the consumer, and will be where
its state can be changed when a user types.

export const defaultCode = `() => {
  const [inputValue, setInputValue] = React.useState('');
  const handleInputValueChange = (inputValue) => setInputValue(inputValue);
  const handleClear = () => setInputValue('');
  const textMap = {
    ariaLabel: 'Search box',
    clearButtonAriaLabel: 'Clear input'
  };
  return (
    <SearchField
      placeholder="Search for department"
      id="search-field-1"
      onClear={handleClear}
      inputValue={inputValue}
      textMap={textMap}
      onInputValueChange={handleInputValueChange}
    />
  );
};
`;

<CodeExample scope={scope} code={defaultCode} />

### Suggestions SearchField

SearchField can also optionally display a list of suggestions. There are additional props required to fully utilize this feature.

- `options` - a list of options to provide in order to show the list of suggestions
- `onChange` - callback when one of the options has been selected
- `value` - sets the selected option

Note that there is no selected indicator in the dropdown, as both `onChange` and `value` props just provides finer controls for consumers to use.

When no results are found and `noResultsText` in the `textMap` object prop is provided, the SearchField will display the `noResultsText` accordingly.

The option is of type `IDataItem` which is defined as the following:

<Markdown>{IDataItem}</Markdown>

`title` is required and used as a display value for each option.

export const suggestionsCode = `() => {
  const options = [
    { id:'id-0', title: 'Sales'},
    { id:'id-1', title: 'Marketing'},
    { id:'id-2', title: 'Support'},
    { id:'id-3', title: 'Human Resources'},
    { id:'id-4', title: 'Product & Technology'},
    { id:'id-5', title: 'Services'},
    { id:'id-6', title: 'Operations'},
    { id:'id-7', title: 'Customer Experience'},
    { id:'id-8', title: 'Finance'},
    { id:'id-9', title: 'Legal'},
  ];
  const textMap = {
    ariaLabel: 'Search box',
    clearButtonAriaLabel: 'Clear input',
    noResultsText: 'No results matching "{0}"',
    selectedItem: '{0} selected',
  };
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState(options);
  React.useEffect(() => {
    if (inputValue.length === 0) {
      setFilteredOptions(options);
      setValue(undefined);
    }
  }, [inputValue]);
  const handleInputValueChange = (inputValue) => {
    setInputValue(inputValue);
    const filtered = options.filter((option) =>
      option.title.toLowerCase().includes(inputValue.toLowerCase())
    );
    setFilteredOptions(filtered);
  };
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
  };
  const handleBlur = () => {
    if (value) {
      setInputValue(value.title);
    } else {
      setInputValue('');
    }
  };
  const handleClear = () => {
    setValue(undefined);
    setInputValue('');
  };
  return (
    <SearchField
      id="search-field-2"
      placeholder="Search for department"
      inputValue={inputValue}
      onInputValueChange={handleInputValueChange}
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      onClear={handleClear}
      textMap={textMap}
      options={filteredOptions}
    />
  );
};
`;

<CodeExample scope={scope} code={suggestionsCode} />

#### SearchField with Grouped Items

The `options` data can contain grouped items. (See the [How To Use](#how-to-use) section for more details)

export const suggestionsWithGroupingCode = `() => {
  const options = [
    {
      title: 'Group 1',
      id: 'group1',
      items: [
        { title: 'First Option', id: 'item1' },
        { title: 'Second Option', id: 'item2' },
        { title: 'Third Option', id: 'item3' },
      ],
    },
    {
      title: 'Group 2',
      id: 'group2',
      items: [
        { title: 'Fourth Option', id: 'item4' },
        { title: 'Fifth Option', id: 'item5' },
      ],
    },
    {
      title: 'Group 3',
      id: 'group3',
      items: [
        { title: 'Sixth Option', id: 'item6' },
        { title: 'Seventh Option', id: 'item7' },
        { title: 'Eighth Option', id: 'item8' },
        { title: 'Ninth Option', id: 'item9' },
        { title: 'Tenth Option', id: 'item10' },
      ],
    },
  ];
  const filterByTitle = (options, input) => {
    return options.reduce((acc, option) => {
      if (option.items?.length) {
        const filteredItems = filterByTitle(option.items || [], input);
        if (filteredItems.length > 0) {
          acc.push({ ...option, items: filteredItems });
        }
      } else if (option.title.toLowerCase().includes(input.toLowerCase())) {
        acc.push(option);
      }
      return acc;
    }, []);
  };
  const textMap = {
      ariaLabel: 'Search box',
      clearButtonAriaLabel: 'Clear input',
      noResultsText: 'No results matching "{0}"',
      selectedItem: '{0} selected',
  };
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState(options);
  React.useEffect(() => {
      if (inputValue.length === 0) {
          setFilteredOptions(options);
          setValue(undefined);
      }
  }, [inputValue]);
  const handleInputValueChange = (newInputValue) => {
      setInputValue(newInputValue);
      const filtered = filterByTitle(options, newInputValue);
      setFilteredOptions(filtered);
  };
  const handleChange = (item) => {
      setValue(item);
      item ? setInputValue(item.title) : setInputValue('');
  };
  const handleBlur = () => {
      if (value) {
          setInputValue(value.title);
      } else {
          setInputValue('');
      }
  };
  const handleClear = () => {
      setValue(undefined);
      setInputValue('');
  };
  return (
    <SearchField
      id="search-field-2"
      placeholder="Search for department"
      inputValue={inputValue}
      onInputValueChange={handleInputValueChange}
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      onClear={handleClear}
      textMap={textMap}
      options={filteredOptions}
    />
  );
};
`;

<CodeExample scope={scope} code={suggestionsWithGroupingCode} />

#### More Results Footer

SearchField also features an optional `overlayFooter` prop, which accepts a component to be used as a footer within the overlay. The example below contains our recommended component for this case.

For accessibility requirements to be met the footer component must be provided with an `id`. This will allow the footer to be announced by a screen reader upon reaching the last option within the overlay.

<Warning>Implementations of the footer outside of 'More Results' scenario are **not supported** by Everest.</Warning>

export const moreResultsCode = `() => {
  const options = [
    {
      title: 'Group 1',
      id: 'group1',
      items: [
        { title: 'First Option', id: 'item1' },
        { title: 'Second Option', id: 'item2' },
        { title: 'Third Option', id: 'item3' },
      ],
    },
    {
      title: 'Group 2',
      id: 'group2',
      items: [
        { title: 'Fourth Option', id: 'item4' },
        { title: 'Fifth Option', id: 'item5' },
      ],
    },
    {
      title: 'Group 3',
      id: 'group3',
      items: [
        { title: 'Sixth Option', id: 'item6' },
        { title: 'Seventh Option', id: 'item7' },
        { title: 'Eighth Option', id: 'item8' },
        { title: 'Ninth Option', id: 'item9' },
        { title: 'Tenth Option', id: 'item10' },
      ],
    },
  ];
  const MAX_RESULTS_DISPLAYED = 5;
  // helper function to get the count of selectable options in an options collection
  const getCount = (options) => {
    return options.reduce((acc, option) => {
      if (option.items?.length) {
        return acc + getCount(option.items);
      } else {
        return acc + 1;
      }
    }, 0);
  };
  // helper function to filter an options collection by option title
  const filterByTitle = (options, input) => {
    return options.reduce((acc, option) => {
      if (option.items?.length) {
        const filteredItems = filterByTitle(option.items || [], input);
        if (filteredItems.length > 0) {
          acc.push({ ...option, items: filteredItems });
        }
      } else if (option.title.toLowerCase().includes(input.toLowerCase())) {
        acc.push(option);
      }
      return acc;
    }, []);
  };
  // helper function to limit an options collection by specified limit
  const filterByLimit = (options, limit, counter = { value: 0 }) => {
    return options.reduce((acc, option) => {
      if (counter.value >= limit) {
        return acc;
      }
      if (option.items?.length) {
        const filteredItems = filterByLimit(option.items, limit, counter);
        if (filteredItems.length > 0) {
          acc.push({ ...option, items: filteredItems });
        }
      } else {
        counter.value++;
        acc.push(option);
      }
      return acc;
    }, []);
  };
  const textMap = {
    ariaLabel: 'Search box',
    clearButtonAriaLabel: 'Clear input',
    noResultsText: 'No results matching "{0}"',
    selectedItem: '{0} selected',
  };
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState(options);
  const [overlayFooterVisible, setOverlayFooterVisible] = React.useState(false);
  React.useEffect(() => {
    if (inputValue.length === 0) {
      setValue(undefined);
      setFilteredOptions(filterByLimit(options, MAX_RESULTS_DISPLAYED));
      setOverlayFooterVisible(getCount(options) > MAX_RESULTS_DISPLAYED);
    }
  }, [inputValue]);
  const handleInputValueChange = (newInputValue) => {
    setInputValue(newInputValue);
    const allMatching = filterByTitle(options, newInputValue);
    const allMatchingCount = getCount(allMatching);
    setFilteredOptions(filterByLimit(allMatching, MAX_RESULTS_DISPLAYED));
    setOverlayFooterVisible(allMatchingCount > MAX_RESULTS_DISPLAYED);
  };
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
  };
  const handleBlur = () => {
    if (value) {
      setInputValue(value.title);
    } else {
      setInputValue('');
    }
  };
  const handleClear = () => {
    setValue(undefined);
    setInputValue('');
  };
  return (
    <SearchField
      id="search-field-3"
      placeholder="Search for department"
      inputValue={inputValue}
      onInputValueChange={handleInputValueChange}
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      onClear={handleClear}
      textMap={textMap}
      options={filteredOptions}
      overlayFooter={
        overlayFooterVisible && (
          <SearchFieldOverlayFooter
            id="search-field-overlay-footer-id"
            testId="search-overlay-footer-testId"
            label="See all results"
            onClick={() => { alert('Redirect users to a full page search results screen'); }}
          />
        )
      }
    />
  );
};
`;

<CodeExample scope={scope} code={moreResultsCode} />

#### SearchField with Data Loading

Use the `loading` prop in combination with `loadingText` to indicate to the user when you are fetching data. The below example shows how data may be loaded when typing.

export const dataLoadCode = `() => {
  const options = Array.from({ length: 100 }, (v, i) => ({
    id: \`id-\${i}\`,
    title: \`Suggestion \${i + 1}\`
  }));
  const textMap = {
    ariaLabel: 'Search box',
    clearButtonAriaLabel: 'Clear input',
    loadingText: 'Loading',
    noResultsText: 'No results matching "{0}"',
    selectedItem: '{0} selected',
  };
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [overlayFooterVisible, setOverlayFooterVisible] = React.useState(false);
  React.useEffect(() => {
    if (inputValue.length === 0) {
      setFilteredOptions([]);
    }
  }, [inputValue]);
  React.useEffect(() => {
    setOverlayFooterVisible(true);
    setLoading(false);
  }, [filteredOptions]);
  const simulatedAPIDelay = 1000;
  const typingDebounce = 500;
  const handleInputValueChange = (inputValue) => {
    setInputValue(inputValue);
    setLoading(true);
    setOverlayFooterVisible(false);
    fetchAndFilter(inputValue);
  }
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
  }
  const handleFocus = () => {
    if (value === undefined) {
      setLoading(true);
      fetchAndFilter('');
    }
  }
  const handleBlur = () => {
    if (value) {
      setInputValue(value.title);
      setFilteredOptions([value]);
    } else {
      setInputValue('');
      setFilteredOptions([]);
    }
    setOverlayFooterVisible(false);
  }
  const handleClear = () => {
    setValue(undefined);
    setInputValue('');
    setFilteredOptions([]);
    setLoading(true);
    setOverlayFooterVisible(false);
  }
  const fetchAndFilter = React.useCallback(
    // FunctionUtil is imported from @platform/core
    FunctionUtil.debounce((title) => {
      // simulated API call
      setTimeout(() => {
        if (title.trim().length > 0) {
          setFilteredOptions(options.filter((option) =>
            option.title.toLowerCase().includes(title.trim().toLowerCase())
          ));
        } else {
          setFilteredOptions(options);
        }
      }, simulatedAPIDelay);
    }, typingDebounce),
    [options, value]
  );
  return (
    <SearchField
      id="search-field-4"
      placeholder="Search for department"
      inputValue={inputValue}
      onInputValueChange={handleInputValueChange}
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      onClear={handleClear}
      textMap={textMap}
      options={filteredOptions}
      loading={loading}
      overlayFooter={
        overlayFooterVisible && (
          <SearchFieldOverlayFooter
            id="search-field-overlay-footer-id"
            testId="search-overlay-footer-testId"
            label="See all results"
            onClick={console.log}
          />
        )
      }
    />
  );
};
`;

<CodeExample scope={scope} code={dataLoadCode} />

#### SearchField with Custom List Items

Here's an example demonstrating the utilization of `itemRenderer` to craft custom list items for the SearchField.<br /><br />
Customized list items should adhere to the following guidelines:

- Users are expected to handle any potential issues, particularly regarding accessibility.
- They should be read-only.
- Avoid heavy integration with other components.

export const customListItemsCode = `() => {
  const exampleProps = {
    id: 'search-field-with-custom-list-example',
    testId: 'search-field-with-custom-list-example-test-id',
    textMap: {
      selectedItem: '{0} selected',
      ariaLabel: 'Custom List SearchField',
      clearButtonAriaLabel: 'Clear input',
      noResultsText: 'No results matching "{0}"',
    },
  };
  const options = [
    { id: 'Group1', title: 'Accessibility Team', iconName: 'accessibility',
      items: [
        { id: 'id-0', title: 'Andy Brown', additionalData: { description: 'Accessibility Specialist' } },
        { id: 'id-1', title: 'Andrea Johnson', additionalData: { description: 'Accessibility QA' } },
        { id: 'id-2', title: 'Andrew Williams', additionalData: { description: 'Accessibility QA' } },
      ] 
    },
    { id: 'Group2', title: 'Engineering Team', iconName: 'rocketship',
      items: [
        { id: 'id-3', title: 'Andra Garcia', additionalData: { description: 'AI Engineer' } },
        { id: 'id-4', title: 'Anders Lee', additionalData: { description: 'Lead Developer' } },
        { id: 'id-5', title: 'Anderson McCoy', additionalData: { description: 'Senior Developer' } },
        { id: 'id-6', title: 'Darlene Robertson', additionalData: { description: 'Intern Developer' } },
      ]
    },
    { id: 'id-7', title: 'Jane Cooper', iconName: 'palmTree', additionalData: { description: 'Product Designer' } },
    { id: 'id-8', title: 'Theresa Web', iconName: 'palmTree', additionalData: { description: 'HR' } },
  ];
  const [inputValue, setInputValue] = React.useState('');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState(options);
  const handleInputValueChange = (value) => {
    setInputValue(value);
    const filtered = options.filter((option) => option.title.toLowerCase().includes(value.toLowerCase()));
    setFilteredOptions(filtered);
  };
  const handleChange = (item) => {
    setValue(item);
    item ? setInputValue(item.title) : setInputValue('');
    setFilteredOptions(options);
  };
  const handleBlur = (e) => {
    if (value) {
      setInputValue(value.title);
    } else {
      setInputValue('');
    }
    setFilteredOptions(options);
  };
  const handleClear = () => {
    setInputValue('');
    setValue(undefined);
  };
  React.useEffect(() => {
    setInputValue('');
    setValue(undefined);
    setFilteredOptions(options);
  }, []);
  const itemRenderer = (dataItem, selected) => {
    const containerStyle = {
      display: 'flex',
      overflow: 'hidden',
    };
    const leftSideStyle = {
      display: 'flex',
      alignItems: 'center',
      marginLeft: '-0.1rem',
      marginRight: '0.5rem',
    };
    const rightSideStyle = {
      overflow: 'hidden',
      marginRight: '0.75rem',
    };
    const textResultStyle = {
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    };
    return (
      <div style={containerStyle}>
        {dataItem.iconName && <div style={leftSideStyle}><Icon name={dataItem.iconName} /></div>}
        <div style={rightSideStyle}>
          <p style={textResultStyle} className={selected ? '_evrBodyText1 evrBold' : '_evrBodyText1'}>{dataItem.title}</p>
          <p style={textResultStyle} className='_evrBodyText1'>{dataItem?.additionalData?.description}</p>
        </div>
      </div>
    );
  };
  return (
    <SearchField
      {...exampleProps}
      inputValue={inputValue}
      options={filteredOptions}
      onInputValueChange={handleInputValueChange}
      placeholder="Search for employee"
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      onClear={handleClear}
      itemRenderer={itemRenderer}
    />
  );
}`;

<CodeExample scope={scope} code={customListItemsCode} />

## Accessing SearchField using ref

Click on the Button to access the SearchField, refer to the console for the element details.

export const refCode = `()=>{
  const styles = {
    row: {
      display: 'flex',
      justifyContent: 'space-around',
      flexWrap: 'wrap',
    },
    column: {
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      rowGap: '10px'
    }
  }
  const ref=React.useRef(null);
  const [value, setValue] = React.useState(undefined);
  const handleChange = (value) => setValue(value);
  return (
    <div style={styles.column}>
      <div style={styles.row}>
        <Button id='access-element-btn' label="Click to access element" onClick={()=>{console.log(ref.current)}}/>
      </div>
      <div style={styles.row}>
        <SearchField
          id="search-field-ref"
          ariaLabel="Search for department"
          ref={ref}
          placeholder="Search for department"
          onInputValueChange={handleChange}
        />
      </div>
    </div>
  );
};
`;

<CodeExample scope={scope} code={refCode} />

## How to Use

Use the `<SearchField />` component when user needs to provide a certain search text value.

The `options` prop now supports _grouped_ items. When a top-level option item contains an `items` array, it will be rendered as a group.
Currently we only support **one** level of grouping but in the future we will support nested groups at multiple levels.

Below is an example of grouped item:

```js
[
  {
    id: 'item-0',
    title: 'Ungrouped item',
  },
  {
    id: 'group-0',
    title: 'Grouped items',
    items: [
      { id: 'item-1', title: 'grouped item 1' },
      { id: 'item-2', title: 'grouped item 2' },
    ],
  },
];
```

## Accessibility

There should always be a clear `ariaLabel` provided since label is not used in SearchField. Screen readers will announce the `ariaLabel` value once the SearchField is focused.

As outlined in the API table below, aria-labels should be specified. `loadingText` and `noResultsText` are optional and be used when utilizing its associated features.

| Label                | Description                                                                                                                                      | <div style={{whiteSpace: 'nowrap'}}><div style={{whiteSpace: 'nowrap'}}>Example (en-US)</div></div> |
| -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------- |
| ariaLabel            | Label for SearchField                                                                                                                            | "Sample SearchField"                                                                                |
| clearButtonAriaLabel | Button to clear SearchField                                                                                                                      | "Clear input"                                                                                       |
| selectedItem         | Selection announcement text for selected item. The component will internally replace the placeholder `{0}` with the item's title as appropriate. | "\{0\} selected"                                                                                    |
| loadingText          | Label for loading state                                                                                                                          | "Loading"                                                                                           |
| noResultsText        | Label when no results are found                                                                                                                  | "No results matching \{0\}"                                                                         |

An announcement should be made when there are results for the provided search term. For example, the announcement could say "There are X icons matching your search". This is to be implemented by the feature teams. Similarly, `noResultsText` should be used to announce there are no results for the provided search term.

**Note:** All user-facing text and accessible technology narratives (e.g. screen readers) should be suitably translated to match the user's locale.
