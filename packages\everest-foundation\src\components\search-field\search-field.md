# SearchField Architecture

## Summary

- Start Date: 2022-12-15
- Figma link: https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/Mx4cgl0JabnJpYEon5Oie2/Everest-Web?node-id=3157%3A9740&t=36qQALNhJB7zNyE0-0

## Design

Please refer to Text Field's V2 and TextFieldBase arch docs for more detailed documentation.

SearchField is a wrapper class for TextFieldBase in which `type` is set to "search" and the `iconName` is set to "search". Label and its required prop will be removed.

## API

1. **type**: text | password | search
   Removed. Default value: **text**
1. **label**: string (optional)  
   Removed. Sets the label text which gets rendered as part of the search input.
1. **required**: boolean  
   Removed. Sets the **required** attribute on the text input. Adds the asterisk to the label / placeholder.
1. **iconName**: string  
   Removed. Accepts the name of the icon and will be used to render an icon at the start of the search input.
1. **dir**: "ltr" | "rtl"  
   Removed. Specifies the direction of the text in the search input.
1. **ariaLabel**: string  
   Required. Specifies the **aria-label** attribute for the search input.

## Q&A

1. Why aren't we using `inputMode = 'search'` for SearchField?
   The `inputMode` prop in `TextFieldBase` will determine what keyboard a mobile device will display. For example, if `inputMode` is set to `numeric`, then a numberic keypad will show up instead of a regular keyboard. The only difference in using `inputMode="search"` is that the Enter/Return button will be affected.

   When `type="search"`, Android devices will display a magnifying glass and iOS devices will show "return" key. In order for iOS devices to show "search" key instead of "return", feature teams must wrap the input within a <form action="">.

   When `inputMode="search"`, Android devics will show the enter symbol and and iOS devices will show 'go'.

   According to Mozilla, "inputs that require a search query should typically use <input type="search"> instead." This suits our purpose for SearchField so we will stick with `type="search"`.

   References:

   - https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/inputmode
   - https://css-tricks.com/everything-you-ever-wanted-to-know-about-inputmode/
   - https://stackoverflow.com/questions/4864167/show-search-button-in-iphone-ipad-safari-keyboard

## SearchField Update - with Suggestions Box (Sept 2024)

- Start Date: 2024-08-26
- Figma links: https://www.figma.com/design/jMzOdXgPrIVZOnwXi06oaF/%F0%9F%A7%AA-Search?node-id=1-4&node-type=CANVAS&t=8Kv11vttTGw1XuRw-0

### Detailed Design

The Search component displays a dropdown of search results when filling the search field, to which the user can select.

Requirements:

- Grouping/categorization of list items (templating)
- Skeleton loaders (future phase, low priority)
- Search fetch and filter should be controlled by consumer (controlled component)
- Use of clickable footer to provide additional information as a temporary measure to reduce data load (virtualization is a future phase)

### Pre-requisites/Other Components

Re-use existing components:

- FormFieldContainerContext
- FormFieldContainer
- TriggerAreaStyledOverlay
- SelectContainer
- ListBox
- ListItemContext

### API

1.  **textMap?**: `ISearchTextMap`
    Localized text for decorative elements and other labels. Interface should look as follows:
    ```typescript
    export interface ISearchTextMap {
      loadingText?: `string`;
      noResultsText?: `string`;
      ariaLabel?: `string`;
      clearButtonAriaLabel?: `string`;
    }
    ```
1.  **id**: `string`
    Sets the `id` attribute on the Search component.
1.  **inputValue?**: `string`
    Sets the input value.
1.  **itemRenderer?**: `(dataItem: IDataItem, selected: boolean) => ReactNode`
    Sets a custom item template.
1.  **label?**: `string`
    Sets the label rendered on the Search component.
1.  **loading?**: `boolean`
    Sets the search state to loading.
1.  **maxItems?**: `number`
    Sets the max number of items displayed on the overlay.
1.  **onBlur?**: `(e: React.FocusEvent) => void`
    Optional callback on blur.
1.  **onChange?**: `(dataItem?: IDataItem, index?: number) => void`
    Callback when Search selected value change.
1.  **onClear?**: `() => void`
    Callback when clear button is clicked/entered
1.  **onFocus?**: `(e: React.FocusEvent) => void`
    Optional callback on focus.
1.  **onInputValueChange?**: `(value: string) => void`
    Callback when Search input value changes.
1.  **options**: `IDataItem[]`
    Search options as:
    ```typescript
    export interface IDataItem {
      id: string;
      title: string;
      value?: string;
      iconName?: TIconName;
      additionalData?: unknown;
    }
    ```
1.  **overlayFooter?**: `ReactElement`
    Displays the overlay footer for Search. An `id` is required on the overlayFooter element to meet accessibility requirements.
1.  **placeholder?**: `string`
    Sets the placeholder text for the Search component.
1.  **testId?**: `string`
    Sets the `data-testid` attribute on the Search component.
1.  **value?**: `IDataItem`
    Sets the search value.

Note: As long as `loading` is true or `options` prop is provided, it will utilize all of the necessary props needed for the suggestion box overlay, otherwise will revert to its original functionality.

### Usage

Simple usage of the Search, which is very similar to our other existing dropdown-like components such as Combobox and MultiSelect:

```
() => {
    const args = {
        id: 'search-example',
        label: 'This is a search label',
        testId: 'search-test-id',
        textMap: {
            loadingText: 'Loading text'
            ariaLabel: 'This is a search aria-label',
            noResultsText: 'No results',
            clearButtonAriaLabel: 'Clear button'
        },
        maxItems: 10,
    }
    const options = [
        { id: '1-id', title: 'First Option' },
        { id: '2-id', title: 'Second Option' },
        { id: '3-id', title: 'Third Option' },
        { id: '4-id', title: 'Fourth Option' },
        { id: '5-id', title: 'Fifth Option' },
        { id: '6-id', title: 'Sixth Option' },
    ];
    const [inputValue, setInputValue] = useState('');
    const [value, setValue] = useState(null);
    const [filteredOptions, setFilteredOptions] = useState(options);
    const handleInputValueChange = (value) => {
        setInputValue(value);
    };
    const handleChange = (value, index) => {
        setValue(value);
    };
    const handleClear = () => {
        setValue(null);
        setInputValue('');
    };
    const handleBlur = () => {
        setInputValue('');
    };
    useEffect(() => {
        if (!inputValue) {
        setFilterOptions(options);
    } else
      setFilterOptions(
        options.filter((option) =>
          option.title.toUpperCase().includes(inputValue.toUpperCase())
        )
      );
    }, [inputValue]);
    return (
        <SearchField
            {...args}
            onBlur={handleBlur}
            inputValue={inputValue}
            options={filteredOptions}
            onInputValueChange={handleInputValueChange}
            value={value}
            onChange={handleChange}
            onClear={handleClear}
        />
    );
}
```

Debouncing examples will be added to guide consumers on reducing search calls.

### ItemRenderer Templates and Grouping

Similar to Combobox examples, we will provide various examples on Storybook to showcase some of the supported templates shown on Figma.

Most will be dependent on the structure provided by consumer, leveraging `additionalData` property in `IDataItem`.

Grouping functionality is not just limited to Search. As per EDS-4234; ItemRenderer templates will change once grouping functionality is available across some of our Everest components.

### Design Q&A

Q: Do we expect consumers to allow putting images for the no results state?
A: No. The image for no search results in Figma should be made available and placed in our platform-df-asset repo for our component to consume.

Q: We do not have certain status, helper texts or status messages in the design spec. Do we foresee having these to be supported in the near future?
A: No, those will not be implemented in the near future as there is no use case for it in Search at this time.

Q: Are we supporting dynamic onChange when the value on the search field is changed. For example, should onChange trigger after a delay when backspacing or adding a letter to the search field?
A: No, currently it is not supported. Regardless if there is a suggestions box displayed or not, 'Enter' key will be used to trigger onChange. When suggestions box is displayed and the focus is on the search field, 'Enter' will trigger onChange against the search field value. When soft selecting on a list item and pressing 'Enter', it will trigger onChange against the selected list item.

### Alternatives

- Some other DSM uses `groupBy` prop to allow consumer to put in a filter and let the Search component categorize into groups via the filter

### Future Considerations

- Bold text highlighting on Search - up to consumers or readily implemented?

### Other Design Systems

**Carbon**

- https://carbondesignsystem.com/patterns/search-pattern/
- https://react.carbondesignsystem.com/?path=/story/components-containedlist--with-persistent-search

**Polaris**

- https://polaris.shopify.com/components/selection-and-input/autocomplete

**MUI**

- https://mui.com/material-ui/react-autocomplete/#search-input

### Required PBI

1. [Search][Development] Create component (https://dayforce.atlassian.net/browse/EDS-4749)
1. [Search][Development] Tests: Pa11y, Manual, Visual, Unit, and Playwright Tests (https://dayforce.atlassian.net/browse/EDS-4755)
1. [Search][Development] A11y (https://dayforce.atlassian.net/browse/EDS-4756)
1. [Search][Development] Storybook documentation (https://dayforce.atlassian.net/browse/EDS-4757)
1. [Search][Development] Add to Ref App (https://dayforce.atlassian.net/browse/EDS-4758)
1. [Search][Development] Grouping architecture (https://dayforce.atlassian.net/browse/EDS-4812)
1. [Search][Development] Implement Search List Item Templates/Groupings (https://dayforce.atlassian.net/browse/EDS-4750)
1. [Search][Development] Implement Skeleton Loaders (https://dayforce.atlassian.net/browse/EDS-4761)
1. [Search][Development] Push component to Beta (https://dayforce.atlassian.net/browse/EDS-4759)
1. [Search][Development] Push component to Production (https://dayforce.atlassian.net/browse/EDS-4760)
