@use '../../mixins.scss' as mixins;
@use '../../index.scss' as helper;

.evrSearchFieldVisuallyHidden {
  @include helper.visuallyHidden;
}

.evrSearchFieldNoResult {
  @include mixins.listItemNoResults;

  & {
    padding-block: var(--evr-spacing-2xl);
    padding-inline: var(--evr-spacing-md);
    word-break: break-word;
  }
}

.evrSearchFieldListBoxFooter {
  padding: var(--evr-spacing-2xs) var(--evr-spacing-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  cursor: default;
}

.evrOverlayContainer {
  margin-block-start: calc(var(--evr-border-width-thin-px) * -1); // prevents text shifting 1px due to borders
  border-radius: var(--evr-radius-2xs);
}
