import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { SearchField } from './search-field';
import { SearchFieldOverlayFooter } from './search-field-overlay-footer';
import { expect, screen, userEvent, waitFor } from '@storybook/test';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';

export const testId = 'search-field-test-id';

<Meta
  title="Testing/Automation Test Cases/SearchField"
  component={SearchField}
  decorators={[ChromaticDecorators.padStory, ChromaticDecorators.setHeightTo100vh]}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    name: 'test-name',
    placeholder: 'This is a placeholder',
    status: 'default',
    autocomplete: 'off',
    testId,
    id: 'search-field-id',
    textMap: {
      ariaLabel: 'search-field-aria-label',
      loadingText: 'Loading',
      clearButtonAriaLabel: 'Clear search field value',
    },
  }}
/>

# SearchField

## Live Demo

export const ControlledSearchField = (props) => {
  const [inputValue, setInputValue] = React.useState(props.inputValue ?? '');
  const handleInputValueChange = (inputValue) => setInputValue(inputValue);
  const handleClear = () => setInputValue('');
  const textMap = {
    ariaLabel: 'Aria Label for SearchField Basic',
    clearButtonAriaLabel: 'Aria Label for Clear Button Basic',
  };
  return (
    <SearchField
      {...props}
      inputValue={inputValue}
      onInputValueChange={handleInputValueChange}
      textMap={textMap}
      onClear={handleClear}
    />
  );
};

export const ControlledSearchFieldWithOverlay = (props) => {
  const options = [
    { id:'id-0', title: 'Sales'},
    { id:'id-1', title: 'Marketing'},
    { id:'id-2', title: 'Support'},
    { id:'id-3', title: 'Human Resources'},
    { id:'id-4', title: 'Product & Technology'},
    { id:'id-5', title: 'Services'},
    { id:'id-6', title: 'Operations'},
    { id:'id-7', title: 'Customer Experience'},
    { id:'id-8', title: 'Finance'},
    { id:'id-9', title: 'Legal'},
  ];
  const opts = props.options || options;
  const textMap = {
    ariaLabel: "Aria Label for SearchField With Overlay",
    clearButtonAriaLabel: "Aria Label for Clear Button Overlay",
    noResultsText: 'No results matching "{0}"',
    loadingText: 'Loading...',
  };
  const [inputValue, setInputValue] = React.useState(props.inputValue ?? '');
  const [value, setValue] = React.useState(undefined);
  const [filteredOptions, setFilteredOptions] = React.useState(opts);
  React.useEffect(() => {
    if (inputValue.length === 0) {
      setFilteredOptions(opts);
      setValue(undefined);
    }
  }, [inputValue]);
  const handleInputValueChange = (inputValue) => {
      setInputValue(inputValue);
      const filtered = opts.filter((option) =>
          option.title.toLowerCase().includes(inputValue.toLowerCase())
      );
      setFilteredOptions(filtered);
  }
  const handleChange = (item) => {
      setValue(item);
      item ? setInputValue(item.title) : setInputValue('');
  }
  const handleBlur = () => {
      if (value) {
          setInputValue(value.title);
      } else {
          setInputValue('');
      }
  }
  const handleClear = () => {
      setValue(undefined);
      setInputValue('');
  }
  return (
    <SearchField
      {...props}
      inputValue={inputValue}
      onInputValueChange={handleInputValueChange}
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      onClear={handleClear}
      textMap={textMap}
      options={filteredOptions}
      overlayFooter={props.overlayFooter}
    />
  );

};

<Canvas>
  <Story name="Default">{(args) => <ControlledSearchField {...args} />}</Story>
</Canvas>

<Canvas>
  <Story name="Long Placeholder">
    {(args) => (
      <ControlledSearchField
        {...args}
        placeholder="This is a looooooong placeholder. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Default Value">{(args) => <ControlledSearchField inputValue="Default Value" {...args} />}</Story>
</Canvas>

<Canvas>
  <Story name="Long Value">
    {(args) => (
      <ControlledSearchField
        {...args}
        inputValue="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum"
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="User Input"
    play={async () => {
      await userEvent.click(screen.getByRole('searchbox'));
      await userEvent.type(screen.getByRole('searchbox'), 'Test text');
    }}
  >
    {(args) => <ControlledSearchField {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Mouse Hover"
    play={async () => {
      await userEvent.hover(screen.getByTestId(testId));
    }}
  >
    {(args) => <ControlledSearchField {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Tab Focus"
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => <ControlledSearchField {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Overlay, focus ring and scroll bar"
    play={async () => {
      await userEvent.click(screen.getByRole('combobox'));
    }}
  >
    {(args) => <ControlledSearchFieldWithOverlay {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Overlay with grouping"
    play={async () => {
      await userEvent.click(screen.getByRole('combobox'));
    }}
  >
    {(args) => (
      <ControlledSearchFieldWithOverlay
        {...args}
        options={[
          {
            id: 'id-0',
            title: 'Sales',
            items: [
              { id: 'id-0-0', title: 'Sales 1' },
              { id: 'id-0-1', title: 'Sales 2' },
            ],
          },
          {
            id: 'id-1',
            title: 'Marketing',
            items: [
              { id: 'id-1-0', title: 'Marketing 1' },
              { id: 'id-1-1', title: 'Marketing 2' },
            ],
          },
          {
            id: 'id-2',
            title: 'Support',
            items: [
              { id: 'id-2-0', title: 'Support 1' },
              { id: 'id-2-1', title: 'Support 2' },
            ],
          },
          { id: 'id-3', title: 'Human Resources' },
          { id: 'id-4', title: 'Product & Technology' },
        ]}
      />
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Overlay with value"
    play={async () => {
      await userEvent.click(screen.getByRole('combobox'));
      await userEvent.type(screen.getAllByRole('combobox')[0], 'Legal');
    }}
  >
    {(args) => <ControlledSearchFieldWithOverlay {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Overlay with no results"
    play={async () => {
      await userEvent.click(screen.getByRole('combobox'));
      await userEvent.type(screen.getAllByRole('combobox')[0], 'Foo');
    }}
  >
    {(args) => {
      return (
        <ControlledSearchFieldWithOverlay
          {...args}
          options={[]}
        />
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story
    name="Overlay with more results footer"
    play={async () => {
      await userEvent.click(screen.getByRole('combobox'));
      await userEvent.type(screen.getAllByRole('combobox')[0], 'S');
    }}
  >
    {(args) => {
      return (
        <ControlledSearchFieldWithOverlay
          {...args}
          overlayFooter={
            <SearchFieldOverlayFooter
              id="search-field-overlay-footer-id"
              testId="search-overlay-footer-testId"
              label="See all results"
              onClick={console.log}
            />
          }
        />
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Overlay with focus on clear button"
    play={async () => {
      await userEvent.tab();
      await userEvent.type(screen.getByRole('combobox'), 'S');
      await userEvent.tab();
      await waitFor(() => expect(screen.getByTestId('search-field-test-id-overlay-clear-button')).toHaveFocus());
    }}
  >
    {(args) => <ControlledSearchFieldWithOverlay {...args} />}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Overlay with loading"
    play={async () => {
      await userEvent.click(screen.getByRole('combobox'));
    }}
  >
    {(args) => <ControlledSearchFieldWithOverlay {...args} loading={true} />}
  </Story>
</Canvas>

## [Playwright] - User Interactions

<Canvas>
  <Story
    name="[Playwright] - Interactions"
    parameters={{ chromatic: Chromatic.DISABLE }}
  >
    {(args) => {
      const overlayFooter = (
        <SearchFieldOverlayFooter
          id="search-field-overlay-footer-id"
          testId="search-overlay-footer-testId"
          label="See all results"
          onClick={console.log}
        />
      )
      return (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-around',
            gap: '1rem',
          }}
        >
          <ControlledSearchField {...args} id="search-field-basic" placeholder="basic search" />
          <ControlledSearchFieldWithOverlay {...args} id="search-field-suggestions" placeholder="suggestions (simple)" />
          <ControlledSearchFieldWithOverlay
            {...args}
            id="search-field-suggestions-with-grouping"
            placeholder="suggestions (with grouping)"
            options={[
              {
                id: 'id-0',
                title: 'Sales',
                items: [
                  { id: 'id-0-0', title: 'Sales 1' },
                  { id: 'id-0-1', title: 'Sales 2' },
                ],
              },
              {
                id: 'id-1',
                title: 'Marketing',
                items: [
                  { id: 'id-1-0', title: 'Marketing 1' },
                  { id: 'id-1-1', title: 'Marketing 2' },
                ],
              },
              {
                id: 'id-2',
                title: 'Support',
                items: [
                  { id: 'id-2-0', title: 'Support 1' },
                  { id: 'id-2-1', title: 'Support 2' },
                ],
              },
              { id: 'id-3', title: 'Human Resources' },
              { id: 'id-4', title: 'Product & Technology' },
            ]}
          />
          <ControlledSearchFieldWithOverlay {...args} id="search-field-footer" placeholder="suggestions (with footer)" overlayFooter={overlayFooter}
          />
        </div>
      );
    }}

  </Story>
</Canvas>
