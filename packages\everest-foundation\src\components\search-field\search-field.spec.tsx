import React, { useState } from 'react';
import { render, screen, within } from '@testing-library/react';
import { act } from '@testing-library/react-hooks';
import userEvent from '@testing-library/user-event';

import { ISearchFieldTextMap, SearchField } from './search-field';
import { SearchFieldOverlayFooter } from './search-field-overlay-footer';
import { mockResizeObserver } from '../../test-utils';
import { IDataItem } from '../list-item';

const onInputValueChange = jest.fn().mockImplementation((newInputValue: string, setInputValue) => {
  act(() => setInputValue(newInputValue));
});

interface IRenderControlledSearchField {
  id: string;
  testId?: string;
  inputValue?: string;
  options?: IDataItem[];
  loading?: boolean;
  maxLength?: number;
  textMap?: ISearchFieldTextMap;
  overlayFooter?: React.ReactElement;
}

const renderControlledSearchField = (searchFieldParameters: IRenderControlledSearchField) => {
  const { id, textMap, testId, maxLength, loading, overlayFooter, options } = searchFieldParameters;

  const SearchFieldWrapper = () => {
    const [inputValue, setInputValue] = useState('');

    return (
      <SearchField
        id={id}
        textMap={textMap}
        testId={testId}
        inputValue={inputValue}
        maxLength={maxLength}
        loading={loading}
        options={options}
        onInputValueChange={(newInputValue: string) => onInputValueChange(newInputValue, setInputValue)}
        overlayFooter={overlayFooter}
        placeholder="This is a placeholder"
      />
    );
  };

  render(<SearchFieldWrapper />);
};

describe('[SearchField]', () => {
  const id = 'search-field-1';
  const testId = 'search-field-test-id';
  const ariaLabel = 'This is an aria label';
  const placeholderText = 'This is a placeholder';
  const clearButtonAriaLabel = 'This is a clear button Aria Label';
  const options = [
    { title: 'First Suggestion', id: 'item1' },
    { title: 'Second Suggestion', id: 'item2' },
    { title: 'Third Suggestion', id: 'item3' },
  ];
  const loadingText = 'Loading';
  const noResultsText = 'No results';
  const textMap = {
    loadingText: loadingText,
    noResultsText: noResultsText,
    ariaLabel: ariaLabel,
    clearButtonAriaLabel: clearButtonAriaLabel,
  };
  const maxLength = 3;
  const onSubmit = jest.fn();
  const onClick = jest.fn();

  const getBasicSearchField = () => screen.getAllByRole('searchbox')[0];
  const getSearchField = () => screen.getAllByRole('combobox')[0];
  const getClearButton = () => screen.getAllByRole('button', { name: clearButtonAriaLabel })[0];
  const getListItems = () => screen.getAllByRole('option');

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { ResizeObserver } = window;

  beforeEach(() => {
    mockResizeObserver();
  });

  afterEach(() => {
    window.ResizeObserver = ResizeObserver;
    jest.restoreAllMocks();
  });

  const defaultInput = 'test';

  const props = {
    id,
    testId,
    placeholder: placeholderText,
    textMap,
  };

  it('should have search input type', () => {
    render(<SearchField {...props} />);
    expect(getBasicSearchField()).toHaveAttribute('type', 'search');
  });

  it('should always render search icon', () => {
    render(<SearchField {...props} />);
    expect(screen.getByTestId(`${testId}-prefix-icon`)).toBeInTheDocument();
    expect(screen.getByTestId(`${testId}-prefix-icon`)).toHaveAttribute('data-evr-name', 'search');
  });

  it('should render initial value when inputValue is provided and clear button', () => {
    render(<SearchField {...props} inputValue={defaultInput} />);
    expect(getBasicSearchField()).toHaveValue(defaultInput);
    expect(getClearButton()).toBeInTheDocument();
  });

  it('should be accepting a value change', async () => {
    renderControlledSearchField({ id, textMap });
    await userEvent.type(getBasicSearchField(), defaultInput);
    expect(getBasicSearchField()).toHaveValue(defaultInput);
  });

  describe('autocomplete', () => {
    it('should render autocomplete attribute when autocomplete prop is set to "off" (basic search only)', () => {
      render(<SearchField {...props} autocomplete="off" />);
      expect(getBasicSearchField()).toHaveAttribute('autocomplete', 'off');
    });

    it('should not render autocomplete attribute when autocomplete prop is omitted (basic search only)', () => {
      render(<SearchField {...props} />);
      expect(getBasicSearchField()).not.toHaveAttribute('autocomplete');
    });

    it('should not render autocomplete attribute when autocomplete prop is set to "on" (basic search only)', () => {
      render(<SearchField {...props} autocomplete="on" />);
      expect(getBasicSearchField()).not.toHaveAttribute('autocomplete');
    });

    it('should set autocomplete attribute to "off" when search suggestion options are set', () => {
      render(<SearchField {...props} autocomplete="on" options={options} />);
      expect(getSearchField()).toHaveAttribute('autocomplete', 'off');
    });
  });

  it('should be accepting a value change respecting maxLength', async () => {
    renderControlledSearchField({ id, textMap, maxLength: maxLength });
    await userEvent.type(getBasicSearchField(), defaultInput);
    expect(getBasicSearchField()).toHaveValue(defaultInput.slice(0, maxLength));
  });

  it('should fire onInputValueChange event', async () => {
    expect.assertions(5);
    const events: React.FormEvent<HTMLInputElement>[] = [];
    const onInputValueChange = jest.fn().mockImplementation((e) => {
      events.push(e);
    });

    render(<SearchField {...props} onInputValueChange={onInputValueChange} />);

    await userEvent.type(getBasicSearchField(), defaultInput);
    expect(onInputValueChange).toBeCalledTimes(4);
    events.forEach((e) => {
      expect(onInputValueChange).toHaveBeenCalledWith(e);
    });
  });

  it('should fire onChange with pasted value respecting maxLength', async () => {
    const onInputValueChange = jest.fn();
    render(<SearchField {...props} onInputValueChange={onInputValueChange} maxLength={maxLength} />);
    await userEvent.click(getBasicSearchField());
    await userEvent.paste(defaultInput);
    expect(onInputValueChange).toBeCalledWith(defaultInput.slice(0, maxLength));
  });

  it('should fire onFocus event', async () => {
    expect.assertions(2);
    const onFocus = jest.fn();
    render(<SearchField {...props} onFocus={onFocus} />);

    await userEvent.tab();
    expect(getBasicSearchField()).toHaveFocus();
    expect(onFocus).toHaveBeenCalledTimes(1);
  });

  it('should fire onBlur event', async () => {
    expect.assertions(2);
    const onBlur = jest.fn();
    render(<SearchField {...props} onBlur={onBlur} />);

    await userEvent.tab();
    expect(getBasicSearchField()).toHaveFocus();

    await userEvent.tab();
    expect(onBlur).toHaveBeenCalledTimes(1);
  });

  it('should fire onClear event', async () => {
    const onClear = jest.fn();
    render(<SearchField {...props} onClear={onClear} inputValue="text" />);

    expect(getClearButton()).toBeInTheDocument();

    await userEvent.click(getClearButton());
    expect(onClear).toHaveBeenCalledTimes(1);
  });

  it('parent form gets submitted on enter key press when the search field is focused', async () => {
    render(
      <form onSubmit={onSubmit}>
        <SearchField {...props} />
      </form>
    );

    await userEvent.type(getBasicSearchField(), '{enter}');
    expect(onSubmit).toHaveBeenCalledTimes(1);
  });

  it('should have an id when given', () => {
    render(<SearchField {...props} id={id} />);
    expect(getBasicSearchField()).toHaveAttribute('id', id);
  });

  it('should render ariaLabel', () => {
    render(<SearchField {...props} />);
    expect(getBasicSearchField()).toHaveAttribute('aria-label', ariaLabel);
  });

  it.each(['role', 'aria-expanded', 'aria-controls'])(
    'should not render attribute "%s" for Basic search field',
    (attr) => {
      render(<SearchField {...props} />);
      expect(getBasicSearchField()).not.toHaveAttribute(attr);
    }
  );

  describe('overlay interactions', () => {
    it('should render all list items when options are provided', async () => {
      renderControlledSearchField({ id, textMap, options });
      await userEvent.click(getSearchField());
      await userEvent.type(getSearchField(), 'S');
      expect(getListItems()[0]).toBeInTheDocument();
      expect(getListItems().length).toBe(3);
    });

    it('should show loading stub when options is provided and loading is true, with an announcement', async () => {
      renderControlledSearchField({ id, textMap, options, loading: true, testId });
      await userEvent.click(getSearchField());
      await userEvent.type(getSearchField(), 'S');

      const el = screen.getByTestId(`${testId}-list-box-stub`);
      expect(within(el).getByText(loadingText)).toBeInTheDocument();
      expect(screen.getByRole('alert').textContent).toBe(loadingText);
    });

    it('should not show loading stub when options is not provided', async () => {
      render(<SearchField {...props} loading={true} />);
      await userEvent.click(getBasicSearchField());
      expect(screen.queryByText(loadingText)).not.toBeInTheDocument();
    });

    it('should not show loading stub when loading is false', async () => {
      render(<SearchField {...props} options={options} loading={false} />);

      await userEvent.click(getSearchField());
      expect(screen.queryByText(loadingText)).not.toBeInTheDocument();
    });

    it('should show no results when options are empty and noResultsText in textMap is provided, with an announcement', async () => {
      renderControlledSearchField({ id, textMap, options: [] });
      await userEvent.click(getSearchField());
      await userEvent.type(getSearchField(), 'F');
      expect(screen.getByText(noResultsText)).toBeInTheDocument();
      expect(screen.getByRole('alert').textContent).toBe(noResultsText);
    });

    it('should not show no results when options are not provided', async () => {
      render(<SearchField {...props} />);
      await userEvent.click(getBasicSearchField());
      expect(screen.queryByText(noResultsText)).not.toBeInTheDocument();
    });

    it('should not show no results when options are not empty', async () => {
      render(<SearchField {...props} options={options} />);

      await userEvent.click(getSearchField());
      expect(screen.queryByText(noResultsText)).not.toBeInTheDocument();
    });

    it('should show overlayFooter when overlayFooter prop is passed', async () => {
      const overlayFooter = (
        <SearchFieldOverlayFooter
          id="search-field-overlay-footer-id"
          testId="search-field-overlay-footer-test-id"
          label="See all results"
          onClick={onClick}
        />
      );
      renderControlledSearchField({ id, textMap, options, overlayFooter });
      await userEvent.click(getSearchField());
      await userEvent.type(getSearchField(), 'S');
      expect(screen.getByTestId('search-field-overlay-footer-test-id')).toBeInTheDocument();
    });

    it('should not show overlayFooter when overlayFooter prop is not passed', async () => {
      renderControlledSearchField({ id, textMap, options });
      await userEvent.click(getSearchField());
      expect(screen.queryByTestId('search-field-overlay-footer-test-id')).not.toBeInTheDocument();
    });
  });

  describe('keyboardHandler', () => {
    it('should select the next option on ArrowDown key press', async () => {
      renderControlledSearchField({ id, textMap, options });
      await userEvent.type(getSearchField(), 'S');
      await userEvent.keyboard('{ArrowDown}');
      expect(getListItems()[1]).toHaveClass('softSelected');
    });

    it('should select the previous option on ArrowUp key press', async () => {
      renderControlledSearchField({ id, textMap, options });
      await userEvent.type(getSearchField(), 'S');
      await userEvent.keyboard('{ArrowDown}{ArrowDown}{ArrowUp}');
      expect(getListItems()[1]).toHaveClass('softSelected');
    });

    it('should select the first option on Home key press', async () => {
      renderControlledSearchField({ id, textMap, options });

      await userEvent.type(getSearchField(), 'S');
      expect(screen.queryByRole('listbox')).toBeInTheDocument();

      await userEvent.keyboard('{ArrowDown}{ArrowDown}{Home}');
      expect(getListItems()[0]).toHaveClass('softSelected');
    });

    it('should select the last option on End key press', async () => {
      renderControlledSearchField({ id, textMap, options });

      await userEvent.type(getSearchField(), 'S');
      expect(screen.queryByRole('listbox')).toBeInTheDocument();

      await userEvent.keyboard('{End}');
      expect(getListItems()[2]).toHaveClass('softSelected');
    });

    it('should close the overlay on Escape key press', async () => {
      renderControlledSearchField({ id, textMap, options });

      await userEvent.type(getSearchField(), 'S');
      expect(screen.queryByRole('listbox')).toBeInTheDocument();

      await userEvent.keyboard('{Escape}');
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });

    it('should select the soft-selected option and close the overlay on Enter key press', async () => {
      renderControlledSearchField({ id, textMap, options });

      await userEvent.type(getSearchField(), 'S');
      expect(screen.queryByRole('listbox')).toBeInTheDocument();

      await userEvent.keyboard('{ArrowDown}{Enter}');
      await userEvent.keyboard('S{ArrowDown}{Enter}');
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
      expect(getSearchField()).toHaveValue(options[1].title);
    });

    it('should open the overlay and soft-select the first option on Enter key press', async () => {
      renderControlledSearchField({ id, textMap, options });
      await userEvent.tab();
      await userEvent.keyboard('{Enter}');
      expect(screen.queryByRole('listbox')).toBeInTheDocument();
    });
  });

  describe('mouseDownHandler', () => {
    it('should collapse overlay when mousedown interaction is within the ListBox', async () => {
      renderControlledSearchField({ id, textMap, options });
      await userEvent.click(getSearchField());
      const searchField = getSearchField();
      await userEvent.click(searchField);
      const listBox = screen.getByRole('listbox');
      const event = new MouseEvent('mousedown', { bubbles: true, cancelable: true });
      listBox.dispatchEvent(event);
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });
  });
});
