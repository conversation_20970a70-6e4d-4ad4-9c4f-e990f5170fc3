import { useState, useEffect } from 'react';
import { <PERSON>a, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { SearchField } from './search-field';
import Examples from './search-field.examples.mdx';
import { action } from '@storybook/addon-actions';

<Meta
  title="Components/Text Fields/SearchField"
  component={SearchField}
  parameters={{
    status: {
      type: 'ready',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/Bkay9m2xXxUIc6BDMi4tOr/branch/Mx4cgl0JabnJpYEon5Oie2/Everest-Web?node-id=3157%3A9740&t=YuPkvhXbnycbZkCx-0',
    },
    controls: {
      exclude: ['ref'],
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    name: {
      description: 'Sets the name attribute on the search field.',
    },
    placeholder: {
      description: 'Sets the placeholder text of the search field.',
    },
    value: {
      control: '-',
    },
    id: {
      description: 'Specifies the id attribute of the search field.',
    },
    inputMode: {
      description: 'Sets the data type hint for the search field.',
    },
    inputValue: {
      control: '-',
    },
    itemRenderer: {
      control: '-',
    },
    maxLength: {
      description: 'Specifies the maximum value length to be allowed in the input.',
    },
    onChange: {
      control: '-',
      description: 'Accepts the callback function for the `onchange` event.',
    },
    onFocus: {
      control: '-',
      description: 'Accepts the callback function for the `onfocus` event.',
    },
    onBlur: {
      control: '-',
      description: 'Accepts the callback function for the `onblur` event.',
    },
    onClear: {
      control: '-',
      description: 'Callback when clear button is clicked.',
    },
    onInputValueChange: {
      control: '-',
    },
    overlayFooter: {
      control: '-',
    },
    autocomplete: {
      type: 'enum',
      control: 'radio',
      options: ['off', 'on'],
      description:
        'Sets the autocomplete attribute on the search field when in basic mode. Always off when there are search suggestions.',
    },
    ariaDescribedBy: {
      description:
        'Specifies the id of the paragraph (or other similar element) which will be used to provide the description of the search field.',
    },
    testId: {
      description: 'An id used for automation testing.',
    },
    textMap: {
      type: 'object',
      control: 'object',
      description: 'Object containing localized text for various elements.',
    },
  }}
  args={{
    name: 'test-name',
    id: 'searchfield-1',
    placeholder: 'Search for department',
    autocomplete: 'off',
    loading: false,
    maxItems: 5,
    testId: 'search-field-test-id',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onBlur: action('onBlur'),
    onClear: action('onClear'),
    options: [
      {
        title: 'Group 1',
        id: 'group1',
        items: [
          { title: 'First Option', id: 'item1' },
          { title: 'Second Option', id: 'item2' },
          { title: 'Third Option', id: 'item3' },
        ],
      },
      {
        title: 'Group 2',
        id: 'group2',
        items: [
          { title: 'Fourth Option', id: 'item4' },
          { title: 'Fifth Option', id: 'item5' },
        ],
      },
      {
        title: 'Group 3',
        id: 'group3',
        items: [
          { title: 'Sixth Option', id: 'item6' },
          { title: 'Seventh Option', id: 'item7' },
          { title: 'Eighth Option', id: 'item8' },
          { title: 'Ninth Option', id: 'item9' },
          { title: 'Tenth Option', id: 'item10' },
        ],
      },
    ],
    textMap: {
      ariaLabel: 'Search box',
      clearButtonAriaLabel: 'Clear input',
      loadingText: 'Loading',
      noResultsText: 'No results matching "{0}"',
      selectedItem: '{0} selected',
    },
  }}
/>

# SearchField

<Examples />

## Live Demo

<Canvas>
  <Story name="SearchField">
    {({options, ...args}) => {
      /**
       * Note: intentionally opted not to use useArgs() here as it was negatively 
       * affecting the component functionality, in particular when selecting a 
       * suggestion via keyboard or mouse the overlay would not close
       */

      const filterOptions = (options, input) => {
        return options.reduce((acc, option) => {
          if (option.items?.length) {
            const filteredItems = filterOptions(option.items || [], input);
            if (filteredItems.length > 0) {
              acc.push({ ...option, items: filteredItems });
            }
          } else if (option.title.toLowerCase().includes(input.toLowerCase())) {
            acc.push(option);
          }
          return acc;
        }, []);
      };

      const [inputValue, setInputValue] = useState('');
      const [value, setValue] = useState(undefined);
      const [filteredOptions, setFilteredOptions] = useState(options);
      useEffect(() => {
        if (inputValue?.length === 0) {
            setFilteredOptions(options);
            setValue(undefined);
        }
      }, [inputValue]);
      const handleInputValueChange = (newInputValue) => {
        setInputValue(newInputValue);
        const filtered = filterOptions(options, newInputValue);
        setFilteredOptions(filtered);
        action('onInputValueChange')(newInputValue);
      };
      const handleChange = (item) => {
        action('onChange')(item);
        setValue(item);
        item ? setInputValue(item.title) : setInputValue('');
      };
      const handleBlur = (e) => {
        action('onBlur')(e);
        if (value) {
          setInputValue(value.title);
        } else {
          setInputValue('');
        }
      };
      const handleClear = () => {
        action('onClear')('');
        setValue(undefined);
        setInputValue('');
      };

      return (
        <SearchField
          {...args}
          inputValue={inputValue}
          options={filteredOptions}
          onInputValueChange={handleInputValueChange}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          onClear={handleClear}
        />
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="SearchField" />
