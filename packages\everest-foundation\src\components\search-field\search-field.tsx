import React, {
  FocusEvent,
  useCallback,
  useMemo,
  useLayoutEffect,
  useEffect,
  useRef,
  useState,
  MouseEvent,
  ReactElement,
} from 'react';
import { getRuntimeEnvironmentInfo } from '@platform/core';

import { mergeRefs, announce, clearAnnouncer, templateReplacer } from '../../utils';
import { getAriaAttributes } from '../../utils/get-aria-attributes';
import { fileSearchImage } from '../assets/file-search-image';
import { FormFieldContainerContext } from '../form-field-container';
import { IListBoxMethods, IListBoxTextMap, ListBox } from '../list-box';
import { IDataItem } from '../list-item';
import { IListItemContext, ListItemContext } from '../list-item/list-item-context';
import { getSelectableOptions } from '../list-item/list-item-helper';
import { ITextFieldBaseProps, TextFieldBase } from '../text-field-base';
import { TriggerAreaStyledOverlay } from '../trigger-area-styled-overlay';

import styles from './search-field.module.scss';

export interface ISearchFieldProps
  extends Omit<
    ITextFieldBaseProps,
    | 'ariaActiveDescendant'
    | 'ariaControls'
    | 'ariaExpanded'
    | 'ariaLabel'
    | 'bottomBorder'
    | 'borderContainerRef'
    | 'characterCounter'
    | 'clearButtonAriaLabel'
    | 'clearButtonRef'
    | 'disabled'
    | 'helperText'
    | 'helperTextPrefix'
    | 'hideFocusRing'
    | 'iconName'
    | 'label'
    | 'onChange'
    | 'onKeyDown'
    | 'onMouseDown'
    | 'onMouseEnter'
    | 'onMouseOut'
    | 'readOnly'
    | 'required'
    | 'role'
    | 'showClearButton'
    | 'status'
    | 'statusMessage'
    | 'statusMessagePrefix'
    | 'type'
    | 'value'
  > {
  /** Custom renderer for the ListItem and ListItemGroup items; the function receives the dataItem and selected state and should return a ReactNode */
  itemRenderer?: (dataItem: IDataItem, selected: boolean) => React.ReactNode;
  /** Toggles the loading presentation. */
  loading?: boolean;
  /** Max number of items displayed in overlay.
   * @default 5
   */
  maxItems?: number;
  /** A callback for when the search field input field changes. */
  onInputValueChange?: (value: string) => void;
  /** Array of search item objects of type `IDataItem`. */
  options?: IDataItem[];
  /** Custom footer for the overlay */
  overlayFooter?: ReactElement;
  /** Object containing localized text for various elements. */
  textMap?: ISearchFieldTextMap;
  /** Sets the input value of the search field. */
  inputValue?: string;
  /** Callback for when the search field is changed. */
  onChange?: (dataItem?: IDataItem) => void;
  /** The current value of the search field. */
  value?: IDataItem;
}

export interface ISearchFieldTextMap extends IListBoxTextMap {
  loadingText?: string;
  noResultsText?: string;
  ariaLabel?: string;
  clearButtonAriaLabel?: string;
  selectedItem?: string;
}

export const SearchField = React.forwardRef<HTMLInputElement, ISearchFieldProps>((props, ref) => {
  const {
    id,
    inputMode,
    inputValue,
    itemRenderer,
    loading,
    maxItems = 5,
    maxLength,
    name,
    onBlur: consumerProvidedOnBlur,
    onChange: consumerProvidedOnChange,
    onClear: consumerProvidedOnClear,
    onFocus: consumerProvidedOnFocus,
    onInputValueChange: consumerProvidedOnInputValueChange,
    options,
    placeholder,
    testId,
    textMap,
    overlayFooter,
    value: initialValue,
    autocomplete,
  } = props;

  const runtimeInfo = getRuntimeEnvironmentInfo();
  const listboxId = `${id}-list-box`;
  const noResultsMessageId = `${id}-no-results-message`;

  const sharedClearButtonProps = {
    clearButtonAriaLabel: textMap?.clearButtonAriaLabel,
  };

  const overlayTriggerRef = useRef<HTMLElement>(null);
  const overlayWrapperRef = useRef<HTMLDivElement>(null);
  const clearButtonRef = useRef<HTMLDivElement>(null);
  const clearButtonRefOverlay = useRef<HTMLDivElement>(null);
  const listBoxForwardRef = useRef<IListBoxMethods>(null);
  const listBoxRef = useRef<HTMLUListElement>(null);
  const searchFieldRef = useRef<HTMLInputElement>(null);
  const searchFieldRefOverlay = useRef<HTMLInputElement>(null);

  // the actual item that was activated by the user by click or Enter
  // no actual indicator is needed, this is present to reduce onChange call when the same item is selected
  const [selectedOption, setSelectedOption] = useState<IDataItem | undefined>(undefined);

  const [softSelectedOption, setSoftSelectedOption] = useState<IDataItem | undefined>(undefined);
  const [overlayVisible, setOverlayVisible] = useState(false);
  const [overlayContentHeight, setOverlayContentHeight] = useState(0);
  const [listBoxHeight, setListBoxHeight] = useState(0);
  const [selectableOptions, setSelectableOptions] = useState<IDataItem[]>([]);
  const totalOptionsCount = useRef(0);
  const isClosingOverlayFromKeyboardRef = useRef(false);
  const isClosingOverlayFromMouseDownRef = useRef(false);
  const [isFocusWithinComponent, setIsFocusWithinComponent] = useState(false);
  const [isListItemFocusVisible, setIsListItemFocusVisible] = useState(false);
  const showListBox = useMemo(() => options?.length || loading, [options?.length, loading]);

  const value = useMemo(
    () => selectableOptions.find((option) => option.id === initialValue?.id),
    [selectableOptions, initialValue]
  );

  const ariaAttributes = getAriaAttributes(SearchField.displayName ?? 'SearchField', {
    ...props,
    listboxId,
    noResultsMessageId,
    overlayVisible,
    activeDescendantId: !showListBox || loading ? undefined : softSelectedOption?.id,
  });

  const loadingStub = useMemo(
    () => (loading ? { title: textMap?.loadingText, id: `${id}-search-field-stub`, type: 'loading' } : undefined),
    [loading, textMap?.loadingText, id]
  );

  useEffect(() => {
    // get flattened 1-dimentional array of all items and nested groups
    const resp = getSelectableOptions(options);

    setSelectableOptions(resp.result);
    totalOptionsCount.current = resp.totalOptionsCount;
  }, [options]);

  // #region contexts

  const listItemContext: IListItemContext = {
    stopMouseOverSoftSelection: true,
    isFocusVisible: isListItemFocusVisible,
    itemRenderer,
    onMouseMove: (e: MouseEvent) => {
      const liElement = (e.target as HTMLElement).closest('li');
      if (liElement?.id !== softSelectedOption?.id) {
        setSoftSelectedOption(selectableOptions.find((item) => item.id == liElement?.id));
      }
    },
  };

  const formFieldContainerContext = useMemo(
    () => ({
      label: undefined,
      id,
    }),
    [id]
  );

  // #endregion contexts

  // #region helpers

  const onInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    setIsFocusWithinComponent(true);
    keyboardHandler(e);
  };

  const optionToSetAsSoftSelected = useCallback(() => {
    if (!selectableOptions.length) return undefined;

    return selectedOption && selectableOptions.findIndex((item) => item.id === selectedOption.id) > -1
      ? selectedOption
      : selectableOptions[0];
  }, [selectableOptions, selectedOption]);

  // Checks whether the provided element is inside/a part of the SearchField.
  const isElementInside = (element: HTMLElement | Node): boolean => {
    return (
      // Background
      searchFieldRef.current === element ||
      (clearButtonRef.current && clearButtonRef.current === element) || // There isn't always a clear button => need null check
      // Foreground/overlay
      overlayWrapperRef.current?.contains(element) ||
      false
    );
  };

  // Analyzes the focus event and determines:
  //    - if focus was brought in from another element
  //    - if it "goes to" another element
  // It will check if the relevant element exists inside the SearchField.
  const isInternalFocusChange = (e: FocusEvent<HTMLElement>): boolean => {
    if (e.type === 'focus') {
      const isTargetInside = isElementInside(e.target);
      const isRelatedTargetOutside = e.relatedTarget !== null ? !isElementInside(e.relatedTarget as HTMLElement) : true;

      return !(isTargetInside && isRelatedTargetOutside);
    }

    return (
      (e.relatedTarget &&
        (overlayTriggerRef.current?.contains(e.relatedTarget as Node) ||
          overlayWrapperRef.current?.contains(e.relatedTarget as Node))) ||
      false
    );
  };

  const getOverlayHeight = useCallback(() => {
    if (!overlayContentHeight) return '';
    return `calc(${overlayContentHeight}px + (var(--evr-border-width-thin-px) * 3))`;
  }, [overlayContentHeight]);

  const closeOverlayFromKeyboard = () => {
    isClosingOverlayFromKeyboardRef.current = true;
    setSoftSelectedOption(undefined);
    setOverlayVisible(false);
  };

  // #endregion helpers

  // #region handlers

  const focusEventHandler = (e: React.FocusEvent<HTMLElement>) => {
    if (!isInternalFocusChange(e)) {
      if (e.type === 'focus') {
        consumerProvidedOnFocus?.(e);
      } else if (e.type === 'blur') {
        setIsFocusWithinComponent(false);
        setIsListItemFocusVisible(false);
        consumerProvidedOnBlur?.(e);
      }
    } else {
      setIsFocusWithinComponent(true);
      setIsListItemFocusVisible(e.target === searchFieldRefOverlay.current);
    }
  };

  const mouseDownHandler = (e: React.MouseEvent) => {
    const isClearButton = clearButtonRef.current?.contains(e.target as HTMLElement);

    if (isClearButton) return;

    setIsFocusWithinComponent(true);

    if (overlayVisible) {
      e.preventDefault();
    }

    if (selectableOptions.length || loading) {
      setOverlayVisible(() => {
        // collapse overlay when mousedown interaction is within the ListBox and not on the overlay footer
        if (listBoxRef.current?.contains(e.target as HTMLElement)) {
          isClosingOverlayFromMouseDownRef.current = true;
          return !overlayVisible;
        }
        return true;
      });
    }
  };

  const keyboardHandler = (e: React.KeyboardEvent<HTMLInputElement>) => {
    let closeOverlay = false;
    setIsListItemFocusVisible(true);
    if (selectableOptions.length) {
      switch (e.key) {
        case 'ArrowDown': {
          // prevent moving text input cursor. We want to use up/down to manage soft selection. To move input cursor user can use left and right arrow
          e.preventDefault();
          if (loading) return;
          const nextInd = selectableOptions.findIndex((el) => el.id === softSelectedOption?.id) + 1;
          if (nextInd < selectableOptions.length) {
            setSoftSelectedOption(selectableOptions[nextInd]);
          }
          break;
        }

        case 'ArrowUp': {
          e.preventDefault(); // prevent input cursor from moving
          if (e.altKey) {
            closeOverlay = true;
            break;
          }

          if (loading) return;
          const prevInd = selectableOptions.findIndex((el) => el.id === softSelectedOption?.id) - 1;

          if (prevInd >= 0) {
            setSoftSelectedOption(selectableOptions[prevInd]);
          }
          break;
        }

        case 'Home':
          e.preventDefault(); // prevent input cursor from moving
          if (loading) return;
          setSoftSelectedOption(selectableOptions[0]);
          break;

        case 'End':
          e.preventDefault(); // prevent input cursor from moving
          if (loading) return;
          setSoftSelectedOption(selectableOptions[selectableOptions.length - 1]);
          break;

        case 'Tab':
          if (e.shiftKey && overlayVisible) {
            // Prevent focusing next element when overlay is visible and just hide the overlay first.
            // This is what Material UI does and it also helps us.
            // Only need to cover the Shift+Tab out of component here.
            // Tabbing out of the overlay is handled in the overlay keydown handler.
            e.preventDefault();
            closeOverlay = true;
          }
          break;

        case 'Escape':
          closeOverlay = true;
          if (overlayVisible) e.stopPropagation();
          break;

        case 'Enter': {
          if (loading) {
            closeOverlay = true;
            break;
          }
          if (overlayVisible) {
            if (softSelectedOption && selectableOptions.includes(softSelectedOption)) {
              itemSelectionHandler(softSelectedOption);
            }
            closeOverlay = true;
          } else {
            setSoftSelectedOption(optionToSetAsSoftSelected());
            setOverlayVisible(true);
          }
          break;
        }

        default:
          break;
      }
    }

    if (closeOverlay) {
      closeOverlayFromKeyboard();
    }
  };

  const itemSelectionHandler = (item: IDataItem) => {
    if (loading) return;

    const selectionTemplate = textMap?.selectedItem ?? '';
    const listItemTitle = item.title || '';
    clearAnnouncer('assertive');
    announce(templateReplacer(selectionTemplate, [listItemTitle]), 'assertive');

    if (inputValue !== item?.title) {
      consumerProvidedOnInputValueChange?.(item?.title ?? '');
    }

    if (!selectedOption || selectedOption.id != item.id) {
      consumerProvidedOnChange?.(item);
    }
  };

  // #endregion Handlers

  // #region effects

  useEffect(() => {
    setSelectedOption(value);
  }, [value]);

  useEffect(() => {
    if (
      !isClosingOverlayFromKeyboardRef.current &&
      !isClosingOverlayFromMouseDownRef.current &&
      inputValue &&
      inputValue?.length > 0 &&
      (options?.length || loading || textMap?.noResultsText)
    ) {
      setOverlayVisible(true);
    }
    isClosingOverlayFromKeyboardRef.current = false;
    isClosingOverlayFromMouseDownRef.current = false;
  }, [inputValue, options, loading, textMap]);

  // reset the soft selected option when overlay is closed
  useEffect(() => {
    if (overlayVisible) {
      setSoftSelectedOption(optionToSetAsSoftSelected());
    } else {
      setSoftSelectedOption(undefined);
    }
  }, [overlayVisible, selectableOptions, optionToSetAsSoftSelected]);

  // set the cursor in the input text to the click position
  useEffect(() => {
    if (overlayVisible && searchFieldRefOverlay.current && searchFieldRef.current) {
      searchFieldRefOverlay.current.selectionStart = searchFieldRef.current?.selectionStart;
      searchFieldRefOverlay.current.selectionEnd = searchFieldRef.current?.selectionEnd;
    }
  }, [searchFieldRef, searchFieldRefOverlay, overlayVisible]);

  // manages focus in inputs and soft selection when overlay visibility changes
  useLayoutEffect(() => {
    if (overlayVisible && options) {
      // this will set soft selection in case the keydown button didn't set it (for example if overlay was triggered by Spacebar or Enter)

      const listItem = (listBoxRef?.current as HTMLElement)?.querySelector('[role="option"]');
      const listItemHeight = Math.ceil(listItem?.getBoundingClientRect().height ?? 0); // ceil is used here to get correct height when zoomed in
      const overlayFooterHeight = overlayFooter ? listItemHeight : 0;
      // Use maxItems provided by consumer unless the options contain even fewer items.
      // If this evaluates to 0 there will be no height restriction
      const maxListBoxHeight = loadingStub
        ? listItemHeight
        : Math.min(maxItems, totalOptionsCount.current) * listItemHeight;

      setListBoxHeight(maxListBoxHeight);
      setOverlayContentHeight(
        searchFieldRefOverlay.current?.clientHeight && maxListBoxHeight
          ? searchFieldRefOverlay.current?.clientHeight + maxListBoxHeight - 1 + overlayFooterHeight // -1 is to remove the gap between the list item and the container
          : 0
      );

      setTimeout(() => {
        (searchFieldRefOverlay?.current as HTMLElement)?.focus();
        listBoxForwardRef.current?.scrollListItemIntoView(softSelectedOption);
      }, 0);
    } else if (isFocusWithinComponent) {
      setSoftSelectedOption(undefined);
      (searchFieldRef?.current as HTMLElement).focus();
    }
  }, [
    softSelectedOption,
    options,
    setSoftSelectedOption,
    searchFieldRefOverlay,
    searchFieldRef,
    overlayVisible,
    maxItems,
    isFocusWithinComponent,
    loadingStub,
    overlayFooter,
  ]);

  // #endregion effects

  // #region renderers

  const renderListResults = () => {
    if (showListBox && options) {
      const scopedOptions = loading ? ([loadingStub] as IDataItem[]) : options;
      return (
        <ListItemContext.Provider value={listItemContext}>
          <ListBox
            id={listboxId}
            height={listBoxHeight ? `${listBoxHeight}px` : ''}
            ref={listBoxForwardRef}
            options={scopedOptions}
            selectableOptions={selectableOptions}
            listBoxRef={listBoxRef}
            softSelectedId={softSelectedOption?.id}
            focusOnSoftSelected={false}
            onSelection={itemSelectionHandler}
            onListItemBlur={(e) => {
              // technically list items are not supposed to ever be focused but voiceover on iOS actually fires focus events when
              // list items get the accessibility focus. We need to prevent those events propagating, because they break stuff
              e.stopPropagation();
              if (!isInternalFocusChange(e as FocusEvent<HTMLElement>)) setOverlayVisible(false);
            }}
            onListItemFocus={(e) => {
              e.stopPropagation();
            }}
            testId={testId ? `${testId}-list-box` : undefined}
            textMap={textMap}
            footer={overlayFooter}
          />
          <div
            className={styles.evrSearchFieldVisuallyHidden}
            role="alert"
            tabIndex={runtimeInfo.os === 'android' || runtimeInfo.os === 'ios' ? undefined : -1}
          >
            {loading && textMap?.loadingText}
          </div>
        </ListItemContext.Provider>
      );
    }
    return (
      textMap?.noResultsText && (
        <div className={styles.evrSearchFieldNoResult} id={noResultsMessageId} role="alert">
          <p className="evrBodyText1">{templateReplacer(textMap?.noResultsText, [inputValue || ''])}</p>
          {fileSearchImage}
        </div>
      )
    );
  };

  const renderSearchField = (isOverlay: boolean) => {
    return (
      <TextFieldBase
        {...sharedClearButtonProps}
        {...ariaAttributes.textFieldBase}
        iconName={'search'}
        id={isOverlay ? `${id}-overlay` : id}
        inputMode={inputMode}
        maxLength={maxLength}
        name={name}
        onBlur={(e) => {
          if (!isInternalFocusChange(e)) {
            setOverlayVisible(false); // collapses the overlay if input loses focus
          }
        }}
        onClear={() => {
          setIsFocusWithinComponent(true);
          setOverlayVisible(false);
          consumerProvidedOnClear?.();
        }}
        onChange={consumerProvidedOnInputValueChange}
        onMouseDown={(e) => {
          e.stopPropagation();
          mouseDownHandler(e);
        }}
        onKeyDown={onInputKeyDown}
        placeholder={placeholder}
        ref={isOverlay ? mergeRefs([ref, searchFieldRefOverlay]) : mergeRefs([ref, searchFieldRef])}
        required={undefined}
        showClearButton
        testId={isOverlay ? `${testId}-overlay` : testId}
        type="search"
        value={inputValue}
        bottomBorder={isOverlay}
        borderContainerRef={isOverlay ? undefined : overlayTriggerRef}
        clearButtonRef={isOverlay ? clearButtonRefOverlay : clearButtonRef}
        hideFocusRing={isOverlay}
        autocomplete={options ? 'off' : autocomplete}
      />
    );
  };

  // #endregion renderers

  return (
    <FormFieldContainerContext.Provider value={formFieldContainerContext}>
      <div onBlur={focusEventHandler} onFocus={focusEventHandler}>
        <TriggerAreaStyledOverlay
          id={`${id}-trigger-area-overlay`}
          triggerRef={overlayTriggerRef}
          overlayVisible={overlayVisible}
          overlayHeight={getOverlayHeight()}
          overrideFocusRingDefaultBehavior={!loading && (isFocusWithinComponent || isListItemFocusVisible)}
          renderTriggerAreaContent={() => renderSearchField(false)}
          renderOverlayContent={() => (
            <div
              className={styles.evrOverlayContainer}
              ref={overlayWrapperRef}
              onMouseDown={mouseDownHandler}
              onKeyDown={(e: React.KeyboardEvent<HTMLDivElement>) => {
                // If tabbing out of the footer, prevent focusing next element
                // and just hide the overlay first. This is similar to when
                // you tab out of the Dropdown/Combo from the clear button,
                // which is what material UI does and it also helps us.
                if (
                  e.key === 'Tab' &&
                  !e.shiftKey &&
                  overlayWrapperRef.current?.contains(e.target as Node) &&
                  searchFieldRefOverlay.current !== e.target &&
                  clearButtonRefOverlay.current !== e.target
                ) {
                  e.preventDefault();
                  closeOverlayFromKeyboard();
                }
              }}
            >
              {renderSearchField(true)}
              {renderListResults()}
            </div>
          )}
        />
      </div>
    </FormFieldContainerContext.Provider>
  );
});

SearchField.displayName = 'SearchField';
