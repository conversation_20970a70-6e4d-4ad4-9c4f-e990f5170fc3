import React from 'react';
import { act, render, screen } from '@testing-library/react';

import { Card } from '../card';

import { SectionPanel, SectionPanelHeader, SectionPanelRow } from '.';

const sectionPanelId = 'section-panel-id';
const sectionPanelHeaderId = 'section-panel-header-id';
const sectionPanelRowId = 'section-panel-row-id';

const cardId = 'card-id';
const cardHeaderId = 'card-header-id';
const cardContentId = 'card-content-id';

const cardTitle = 'Widget';
const cardDescription = 'Hello World';

const SectionPanelHeaderTestComponent = () => {
  return (
    <SectionPanel id={sectionPanelId}>
      <SectionPanelHeader id={sectionPanelHeaderId}>
        <h2 className="evrHeading2">Section Panel Header</h2>
      </SectionPanelHeader>
      <SectionPanelRow id={sectionPanelRowId}>
        <div>
          <Card id={cardId}>
            <Card.Content id={cardContentId}>
              <Card.Header title={cardTitle} id={cardHeaderId} description={cardDescription} />
            </Card.Content>
          </Card>
        </div>
      </SectionPanelRow>
    </SectionPanel>
  );
};

const renderTestComponent = () =>
  act(() => {
    render(<SectionPanelHeaderTestComponent />);
  });

describe('SectionPanelHeader Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render header', () => {
    renderTestComponent();
    expect(screen.getByText('Section Panel Header')).toBeInTheDocument();
  });

  it('should apply correct heading level', () => {
    renderTestComponent();
    const header = screen.getByRole('heading', { level: 2 });
    expect(header).toBeInTheDocument();
  });
});
