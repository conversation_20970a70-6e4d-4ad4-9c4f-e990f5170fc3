import React, { PropsWithChildren } from 'react';

import styles from './section-panel-header.module.scss';
interface ISectionPanelHeaderProps {
  /** Sets the `id` attribute on the section panel header */
  id: string;
  /** Sets `data-testid` attribute */
  testId?: string;
}

export const SectionPanelHeader = (props: PropsWithChildren<ISectionPanelHeaderProps>): JSX.Element => {
  const { id, testId, children } = props;

  return (
    <div id={id} data-testid={testId} className={styles.evrSectionPanelHeader}>
      {children}
    </div>
  );
};

SectionPanelHeader.displayName = 'SectionPanelHeader';
