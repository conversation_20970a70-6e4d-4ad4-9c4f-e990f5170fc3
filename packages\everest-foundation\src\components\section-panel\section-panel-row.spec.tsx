import React from 'react';
import { act, render, screen } from '@testing-library/react';

import { Card } from '../card';

import { SectionPanel, SectionPanelRow } from '.';

const sectionPanelId = 'section-panel-id';
const sectionPanelRowIdOne = 'section-panel-row-id-1';
const sectionPanelRowIdTwo = 'section-panel-row-id-2';

const sectionPanelTestId = 'section-panel-test-id';
const sectionPanelRowTestIdOne = 'section-panel-row-test-id-1';
const sectionPanelRowTestIdTwo = 'section-panel-row-test-id-2';

const cardIdOne = 'card-id-1';
const cardHeaderIdOne = 'card-header-id-1';
const cardContentIdOne = 'card-content-id-1';

const cardIdTwo = 'card-id-2';
const cardHeaderIdTwo = 'card-header-id-2';
const cardContentIdTwo = 'card-content-id-2';

const cardIdThree = 'card-id-3';
const cardHeaderIdThree = 'card-header-id-3';
const cardContentIdThree = 'card-content-id-3';

const cardTestIdOne = 'card-test-id-1';
const cardHeaderTestIdOne = 'card-header-test-id-1';
const cardContentTestIdOne = 'card-content-test-id-1';

const cardTestIdTwo = 'card-test-id-2';
const cardHeaderTestIdTwo = 'card-header-test-id-2';
const cardContentTestIdTwo = 'card-content-test-id-2';

const cardTestIdThree = 'card-test-id-3';
const cardHeaderTestIdThree = 'card-header-test-id-3';
const cardContentTestIdThree = 'card-content-test-id-3';

const cardTitle = 'Widget';
const cardDescription = 'Hello World';

const SectionPanelRowTestComponent = () => {
  return (
    <SectionPanel id={sectionPanelId} testId={sectionPanelTestId}>
      <SectionPanelRow id={sectionPanelRowIdOne} testId={sectionPanelRowTestIdOne}>
        <div>
          <Card id={cardIdOne} testId={cardTestIdOne}>
            <Card.Content id={cardContentIdOne} testId={cardContentTestIdOne}>
              <Card.Header
                title={cardTitle}
                id={cardHeaderIdOne}
                testId={cardHeaderTestIdOne}
                description={cardDescription}
              />
            </Card.Content>
          </Card>
        </div>
        <div>
          <Card id={cardIdTwo} testId={cardTestIdTwo}>
            <Card.Content id={cardContentIdTwo} testId={cardContentTestIdTwo}>
              <Card.Header
                title={cardTitle}
                id={cardHeaderIdTwo}
                testId={cardHeaderTestIdTwo}
                description={cardDescription}
              />
            </Card.Content>
          </Card>
        </div>
      </SectionPanelRow>
      <SectionPanelRow id={sectionPanelRowIdTwo} testId={sectionPanelRowTestIdTwo}>
        <div>
          <Card id={cardIdThree} testId={cardTestIdThree}>
            <Card.Content id={cardContentIdThree} testId={cardContentTestIdThree}>
              <Card.Header
                title={cardTitle}
                id={cardHeaderIdThree}
                testId={cardHeaderTestIdThree}
                description={cardDescription}
              />
            </Card.Content>
          </Card>
        </div>
      </SectionPanelRow>
    </SectionPanel>
  );
};

const renderTestComponent = () =>
  act(() => {
    render(<SectionPanelRowTestComponent />);
  });

describe('SectionPanelRow Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all the children in the section panel row', () => {
    renderTestComponent();
    expect(screen.getByTestId(sectionPanelRowTestIdOne)).toBeInTheDocument();
    expect(screen.getByTestId(sectionPanelRowTestIdTwo)).toBeInTheDocument();
    expect(screen.getByTestId(cardTestIdOne)).toBeInTheDocument();
    expect(screen.getByTestId(cardHeaderTestIdOne)).toBeInTheDocument();
    expect(screen.getByTestId(cardContentTestIdOne)).toBeInTheDocument();
    expect(screen.getByTestId(cardTestIdTwo)).toBeInTheDocument();
    expect(screen.getByTestId(cardHeaderTestIdTwo)).toBeInTheDocument();
    expect(screen.getByTestId(cardContentTestIdTwo)).toBeInTheDocument();
    expect(screen.getByTestId(cardTestIdThree)).toBeInTheDocument();
    expect(screen.getByTestId(cardHeaderTestIdThree)).toBeInTheDocument();
    expect(screen.getByTestId(cardContentTestIdThree)).toBeInTheDocument();
  });
});
