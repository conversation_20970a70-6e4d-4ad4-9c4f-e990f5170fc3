import React, { Children, forwardRef, PropsWithChildren } from 'react';

import styles from './section-panel-row.module.scss';

interface ISectionPanelRowProps {
  /** Sets the `id` attribute on the section panel row */
  id: string;
  /** Sets `data-testid` attribute */
  testId?: string;
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const SectionPanelRow = forwardRef<HTMLDivElement, PropsWithChildren<ISectionPanelRowProps>>((props, ref) => {
  const { id, children, testId } = props;

  return (
    <div id={id} data-testid={testId} className={styles.evrSectionPanelRow}>
      {Children.map(children, (child) => (
        <div className={`${styles.evrSectionPanelRow} ${styles.item}`}>{child}</div>
      ))}
    </div>
  );
});

SectionPanelRow.displayName = 'SectionPanelRow';
