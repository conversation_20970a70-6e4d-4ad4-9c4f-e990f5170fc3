import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { ContributedBanner } from '../../../.storybook/docs/shared/component-status-banner';
import { SectionPanel, SectionPanelHeader, SectionPanelRow } from '.';
import { Card } from '../card';

export const scope = {
  SectionPanel,
  SectionPanelHeader,
  SectionPanelRow,
  Card,
};

<ContributedBanner dev="Alexander Yang" team="UI Modernization" />

## Overview

The SectionPanel ensures consistent spacing and layout for the components it wraps. 
It is designed to be used in conjunction with the SectionPanelHeader and SectionPanelRow components. 
The SectionPanelHeader component is used to provide a title for the section, while the SectionPanelRow component is used to create rows of content within the section.

## How to Use

The SectionPanel component consists of three main components:

- `SectionPanel` - The outer container that provides consistent spacing.
- `SectionPanelHeader` - The header component for the section title. This one is optional.
- `SectionPanelRow` - Container for content rows within the section.

### Basic Structure

```jsx
<SectionPanel>
  <SectionPanelHeader>{/* Place title here */}</SectionPanelHeader>
  <SectionPanelRow>
    {/* Place content here */}
  </SectionPanelRow>
</SectionPanel>
```

### Best Practices

- Use `SectionPanelHeader` to provide clear, descriptive titles.
- Use `SectionPanelRow` to group related content. 
It works best when you pass multiple elements directly inside it, like individual `<div>`s — each will be wrapped and styled automatically. 
However, if you wrap multiple elements inside a single container (like a `<div>` or `<ul>`), 
SectionPanelRow treats that entire container as one item and only wraps that.

**Don't do this:❌**
```jsx
<SectionPanelRow>
  <div>
    <Card />
    <Card />
  </div>
</SectionPanelRow>
```

**Do this:✅**
```jsx
<SectionPanelRow>
  <div>
    <Card />
  </div>
  <div>
    <Card />
  </div>
</SectionPanelRow>
```
### Features

- Multiple rows can be used to organize different groups of content.
- Rows automatically handle responsive layout and gap of 24px for their children.
- Rows distribute space equally among their children. For example, if you pass 2 components into a `SectionPanelRow`, each will take up 50% of the width; with 3 components, each gets roughly 33%, and so on.
- Compatible with other Everest components like Card.
- SectionPanel doesn't control the width and height of its children. It's up to consumer to decide how to size them. 

## SectionPanel Example

### SectionPanel with SectionPanelHeader

export const SectionPanelWithHeaderExample = `() => {
    const sectionPanelExampleStyle = {
      padding: '32px 20px',
      border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
    };

    return (
      // This style is a temporary workaround to add padding to the SectionPanel component.
      // This won't be necessary when used with Page Content pattern component as it provides the necessary spacing to fit into the page.
      <div style={sectionPanelExampleStyle}>
        <SectionPanel id="section-panel-id" testId="section-panel-test-id">
            <SectionPanelHeader id="section-panel-header-id" testId="section-panel-header-test-id"><h2 className="evrHeading2">Section Panel Header</h2></SectionPanelHeader>
            <SectionPanelRow id="section-panel-row-id-1" testId="section-panel-row-test-id-1">
                <div>
                  <Card id="card-with-header-1">
                    <Card.Content id="card-with-header-content-id-1">
                      <Card.Header
                        title="Widget"
                        id="card-with-header-header-id-1"
                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                      />
                    </Card.Content>
                  </Card>
                </div>
                <div>
                  <Card id="card-with-header-2">
                    <Card.Content id="card-with-header-content-id-2">
                      <Card.Header
                        title="Widget"
                        id="card-with-header-header-id-2"
                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                      />
                    </Card.Content>
                  </Card>
                </div>
            </SectionPanelRow>
            <SectionPanelRow id="section-panel-row-id-2" testId="section-panel-row-test-id-2">
                <div>
                  <Card id="card-with-header-3">
                    <Card.Content id="card-with-header-content-id-3">
                      <Card.Header
                        title="Widget"
                        id="card-with-header-header-id-3"
                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                      />
                    </Card.Content>
                  </Card>
                </div>
                <div>
                  <Card id="card-with-header-4">
                    <Card.Content id="card-with-header-content-id-4">
                      <Card.Header
                        title="Widget"
                        id="card-with-header-header-id-4"
                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                      />
                    </Card.Content>
                  </Card>
                </div>
                <div>
                  <Card id="card-with-header-5">
                    <Card.Content id="card-with-header-content-id-5">
                      <Card.Header
                        title="Widget"
                        id="card-with-header-header-id-5"
                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                      />
                    </Card.Content>
                  </Card>
                </div>
            </SectionPanelRow>
            <SectionPanelRow id="section-panel-row-id-3" testId="section-panel-row-test-id-3">
                <div>
                  <Card id="card-with-header-6">
                    <Card.Content id="card-with-header-content-id-6">
                      <Card.Header
                        title="Widget"
                        id="card-with-header-header-id-6"
                        description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                      />
                    </Card.Content>
                  </Card>
                </div>
            </SectionPanelRow>
        </SectionPanel>
      </div>
    );
}`;

<CodeExample scope={scope} code={SectionPanelWithHeaderExample} />

## Accessibility

Components wrapped inside the SectionPanel are individually responsible for handling their own accessibility.
The SectionPanel component does not provide any specific accessibility features, but it is designed to work well with other components that do. 
If you want to use a specific HTML structure like a `<ul>` with `<li>` items, it's better not to use SectionPanelRow. 
That's because SectionPanelRow adds a `<div>` around each child to apply styling, which can break the intended HTML structure.