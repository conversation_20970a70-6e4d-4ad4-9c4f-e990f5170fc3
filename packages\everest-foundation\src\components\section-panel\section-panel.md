# SectionPanel

- Start Date: 2025-04-11
- Figma link: https://www.figma.com/design/09HYRnHQSZZax5GoVNEZ9S/Worker-Insights?node-id=7725-82618&m=dev
- Epic: N/A

## Summary

SectionPanel wraps widget-like components and displays them in a grid layout using Flexbox. It ensures that widgets are evenly spaced and properly aligned.

## Detailed Design

SectionPanel consists of an optional header, but mandatory row which accepts mainly but not limited to widget.

- **SectionPanelHeader**: Accepts text as its content. 
- **SectionPanelRow**: Can include a widget or card. Anything that is container based pretty much.
- **SectionPanel**: Can include Header and Row.

## API

### ISectionPanelHeaderProps

1. **id**: `string`
   Sets `id` attribute.
2. **testId**: `string`
   Optional. Sets `data-testid` attribute.
3. **children**: `React.ReactNode`
   Sets the content for the header.

### ISectionPanelRowProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId**: `string`
    Optional. Sets `data-testid` attribute.
3.  **children**: `React.ReactNode`
    Sets the content for the row of the section panel.

### ISectionPanelProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId**: `string`
    Optional. Sets `data-testid` attribute.
3.  **children**: `React.ReactNode`
    Sets the content for the section panel.

### Usage

```jsx
return (
  <SectionPanel id="section-panel-id">
    <SectionPanelHeader id="section-panel-header"><h2 className="evrHeading2">Overview</h2></SectionPanelHeader>
    <SectionPanelRow>
      <div className="section-height">
        <Card id="card-with-header">
          <Card.Content id="card-with-header-content-id">
            <Card.Header
              title="Calendar"
              id="card-with-header-header-id"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
            />
          </Card.Content>
        </Card>
      </div>
    </SectionPanelRow>
  </SectionPanel>
);
```

## Accessibility

Components wrapped inside the SectionPanel are individually responsible for handling their own accessibility.

## Future Considerations

- Currently, all containers within the SectionPanel share the same width. However, future requirements may involve supporting variable widths. For example, allocating 60% of the space to one container and 40% to another. One proposed solution is to leverage the Stack component from Everest to handle layout flexibility, rather than requiring consumers to manually define exact widths.

## Q&A

**Q: Does SectionPanel only accepts Widget component?**
A: No, SectionPanel can take in any container based component such as Card.

**Q: How is responsiveness handled?**
A: SecitonPanel uses Flexbox, which is inherently designed to handle responsiveness. When a container's minimum width is exceeded, the containers automatically wrap and stack vertically.
