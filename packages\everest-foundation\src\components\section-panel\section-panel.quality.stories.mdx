import { useState, useEffect, useRef } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { SectionPanel, SectionPanelHeader, SectionPanelRow } from '.';
import { Card } from '../card';
import { Button } from '../button';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';
import { userEvent, within } from '@storybook/test';

<Meta
  title="Testing/Automation Test Cases/Section Panel"
  component={SectionPanel}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: {
        breakpointXxl: defaultModes['breakpointXxl'],
      },
    },
  }}
  args={{
    id: 'section-panel-id',
    testId: 'section-panel-test-id',
  }}
/>

# Section Panel

## Live Demo

<Canvas>
  <Story name="With Header">
    {(args) => {
      return (
        <div
          style={{
            padding: '32px 20px',
            border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
          }}
        >
          <SectionPanel {...args}>
            <SectionPanelHeader id="section-panel-header-id">
              <h2 className="evrHeading2">Section Panel Header</h2>
            </SectionPanelHeader>
            <SectionPanelRow id="section-panel-row-id-1">
              <div>
                <Card id="card-with-header-1">
                  <Card.Content id="card-with-header-content-id-1">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-1"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
              <div>
                <Card id="card-with-header-2">
                  <Card.Content id="card-with-header-content-id-2">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-2"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
            </SectionPanelRow>
          </SectionPanel>
        </div>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="Without Header">
    {(args) => {
      return (
        <div
          style={{
            padding: '32px 20px',
            border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
          }}
        >
          <SectionPanel {...args}>
            <SectionPanelRow id="section-panel-row-id-1">
              <div>
                <Card id="card-with-header-1">
                  <Card.Content id="card-with-header-content-id-1">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-1"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
              <div>
                <Card id="card-with-header-2">
                  <Card.Content id="card-with-header-content-id-2">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-2"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
            </SectionPanelRow>
          </SectionPanel>
        </div>
      );
    }}
  </Story>
</Canvas>

<Canvas>
  <Story name="With Multiple Rows With Each Row Containing Multiple Elements">
    {(args) => {
      return (
        <div
          style={{
            padding: '32px 20px',
            border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
          }}
        >
          <SectionPanel {...args}>
            <SectionPanelHeader id="section-panel-header-id" testId="section-panel-header-test-id">
              <h2 className="evrHeading2">Section Panel Header</h2>
            </SectionPanelHeader>
            <SectionPanelRow id="section-panel-row-id-1" testId="section-panel-row-test-id-1">
              <div>
                <Card id="card-with-header-1">
                  <Card.Content id="card-with-header-content-id-1">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-1"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
              <div>
                <Card id="card-with-header-2">
                  <Card.Content id="card-with-header-content-id-2">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-2"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
            </SectionPanelRow>
            <SectionPanelRow id="section-panel-row-id-2" testId="section-panel-row-test-id-2">
              <div>
                <Card id="card-with-header-3">
                  <Card.Content id="card-with-header-content-id-3">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-3"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
              <div>
                <Card id="card-with-header-4">
                  <Card.Content id="card-with-header-content-id-4">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-4"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
              <div>
                <Card id="card-with-header-5">
                  <Card.Content id="card-with-header-content-id-5">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-5"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
            </SectionPanelRow>
            <SectionPanelRow id="section-panel-row-id-3" testId="section-panel-row-test-id-3">
              <div>
                <Card id="card-with-header-6">
                  <Card.Content id="card-with-header-content-id-6">
                    <Card.Header
                      title="Widget"
                      id="card-with-header-header-id-6"
                      description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                    />
                  </Card.Content>
                </Card>
              </div>
            </SectionPanelRow>
          </SectionPanel>
        </div>
      );
    }}
  </Story>
</Canvas>
