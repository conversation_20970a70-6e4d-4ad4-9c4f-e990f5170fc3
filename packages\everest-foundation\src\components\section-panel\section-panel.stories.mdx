import { ArgsTable, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/addon-docs';
import { SectionPanel, SectionPanelHeader, SectionPanelRow } from '.';
import { Card } from '../card';
import { Image } from '../image';
import Examples from './section-panel.examples.mdx';

<Meta
  title="Patterns/SectionPanel"
  component={SectionPanel}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/09HYRnHQSZZax5GoVNEZ9S/Worker-Insights?node-id=7530-65868&p=f&m=dev',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
    },
    testId: {
      type: 'string',
      control: 'text',
    },
  }}
  args={{
    id: 'section-panel-id',
    testId: 'section-panel-test-id',
  }}
/>

# SectionPanel

<Examples />

## Live Demo

<Canvas>
  <Story name="SectionPanel">
    {(args) => {
      const sectionPanelExampleStyle = {
        padding: '32px 20px',
        border: 'var(--evr-border-width-thin-px) solid var(--evr-borders-primary-default)',
      };
      return (
        <div style={sectionPanelExampleStyle}>
          <SectionPanel {...args}>
              <SectionPanelHeader id="section-panel-header-id"><h2 className="evrHeading2">Section Panel Header</h2></SectionPanelHeader>
              <SectionPanelRow id="section-panel-row-id-1">
                  <div>
                    <Card id="card-with-header-1">
                      <Card.Content id="card-with-header-content-id-1">
                        <Card.Header
                          title="Widget"
                          id="card-with-header-header-id-1"
                          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        />
                      </Card.Content>
                    </Card>
                  </div>
                  <div>
                    <Card id="card-with-header-2">
                      <Card.Content id="card-with-header-content-id-2">
                        <Card.Header
                          title="Widget"
                          id="card-with-header-header-id-2"
                          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        />
                      </Card.Content>
                    </Card>
                  </div>
              </SectionPanelRow>
              <SectionPanelRow id="section-panel-row-id-2">
                  <div>
                    <Card id="card-with-header-3">
                      <Card.Content id="card-with-header-content-id-3">
                        <Card.Header
                          title="Widget"
                          id="card-with-header-header-id-3"
                          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        />
                      </Card.Content>
                    </Card>
                  </div>
                  <div>
                    <Card id="card-with-header-4">
                      <Card.Content id="card-with-header-content-id-4">
                        <Card.Header
                          title="Widget"
                          id="card-with-header-header-id-4"
                          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        />
                      </Card.Content>
                    </Card>
                  </div>
                  <div>
                    <Card id="card-with-header-5">
                      <Card.Content id="card-with-header-content-id-5">
                        <Card.Header
                          title="Widget"
                          id="card-with-header-header-id-5"
                          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        />
                      </Card.Content>
                    </Card>
                  </div>
              </SectionPanelRow>
              <SectionPanelRow id="section-panel-row-id-3">
                  <div>
                    <Card id="card-with-header-6">
                      <Card.Content id="card-with-header-content-id-6">
                        <Card.Header
                          title="Widget"
                          id="card-with-header-header-id-6"
                          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed dos eiusmod tempor incididunt ut labore et dolore magna aliqua."
                        />
                      </Card.Content>
                    </Card>
                  </div>
              </SectionPanelRow>
          </SectionPanel>
        </div>
      );
    }}
  </Story>
</Canvas>

## Props

### SectionPanel Props
<ArgsTable story="SectionPanel" />

### SectionPanelHeader Props
<ArgsTable of={SectionPanelHeader} />

### SectionPanelRow Props
<ArgsTable of={SectionPanelRow} />