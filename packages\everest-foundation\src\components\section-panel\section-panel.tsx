import React, { Children, forwardRef, ReactElement, PropsWithChildren } from 'react';

import { SectionPanelHeader } from './section-panel-header';

import styles from './section-panel.module.scss';

interface ISectionPanelProps {
  /** Sets the `id` attribute on the section panel */
  id: string;
  /** Sets `data-testid` attribute */
  testId?: string;
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const SectionPanel = forwardRef<HTMLDivElement, PropsWithChildren<ISectionPanelProps>>((props, ref) => {
  const { id, children, testId } = props;

  let headerElement: ReactElement | null = null;
  const filteredChildren = Children.toArray(children).filter((child) => {
    if (React.isValidElement(child) && child.type === SectionPanelHeader) {
      headerElement = child;
      return false;
    }
    return true;
  });

  return (
    <>
      {headerElement}
      <div id={id} data-testid={testId} className={styles.evrSectionPanel}>
        {filteredChildren}
      </div>
    </>
  );
});

SectionPanel.displayName = 'SectionPanel';
