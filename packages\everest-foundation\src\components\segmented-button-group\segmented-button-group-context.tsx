import { createContext, useContext } from 'react';

export type TSegmentedButtonVariant = 'informational' | 'neutral';

interface ISegmentedButtonContext {
  selectedOption: string;
  onChange: (id: string) => void;
  mobile?: boolean;
  iconOnly?: boolean;
  variant: TSegmentedButtonVariant;
}

const defaultContext = {
  selectedOption: '',
  onChange: () => undefined,
  mobile: false,
  iconOnly: false,
  variant: 'neutral' as TSegmentedButtonVariant,
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const SegmentedButtonContext = createContext<ISegmentedButtonContext>(defaultContext);

export const useSegmentedButtonContext = (): ISegmentedButtonContext => {
  const context = useContext(SegmentedButtonContext);
  if (!context) {
    throw new Error('useSegmentedButtonContext must be used within a SegmentedButtonProvider');
  }
  return context;
};
