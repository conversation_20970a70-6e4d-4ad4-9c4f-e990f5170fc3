import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { SegmentedButton } from '../segmented-button/segmented-button';
import { SegmentedButtonGroup } from './segmented-button-group';
import { Button } from '../button';

export const scope = {
  SegmentedButtonGroup,
  SegmentedButton,
  Button,
};

`SegmentedButtonGroup` is designed to group related actions or options, allowing users to toggle between them.

It exclusively accepts `SegmentedButton` components as its children to ensure consistent functionality and styling.

## Variations

### Default

The default `SegmentedButtonGroup` displays the provided labels from the `SegmentedButton`s.

The `variant` prop customizes the appearance of the selected `SegmentedButton`. There are two styles: `informational` and `neutral`.

export const segmentedButtonGroupCodeDefault = `
  () => {
    const [selectedOptionGroup1, setSelectedOptionGroup1] = useState('option-1-default');
    const [selectedOptionGroup2, setSelectedOptionGroup2] = useState('option-1-info');

    const handleOnChangeGroup1 = (selectedId) => {
      console.log('Selected option (Group 1):', selectedId);
      setSelectedOptionGroup1(selectedId);
    };

    const handleOnChangeGroup2 = (selectedId) => {
      console.log('Selected option (Group 2):', selectedId);
      setSelectedOptionGroup2(selectedId);
    };

    return (
      <div>
        <h1 style={{ fontSize: '16px' }}>Neutral</h1>
        <SegmentedButtonGroup
          id="example-1"
          ariaLabel="View"
          onChange={handleOnChangeGroup1}
          selectedOption={selectedOptionGroup1}
        >
          <SegmentedButton id="option-1-default" label="Hourly" />
          <SegmentedButton id="option-2-default" label="Salary" />
          <SegmentedButton id="option-3-default" label="Contract" />
        </SegmentedButtonGroup>

        <h1 style={{ fontSize: '16px', marginTop: '16px' }}>Informational</h1>
        <SegmentedButtonGroup
          variant="informational"
          id="example-1-info"
          ariaLabel="View"
          onChange={handleOnChangeGroup2}
          selectedOption={selectedOptionGroup2}
        >
          <SegmentedButton id="option-1-info" label="Hourly" />
          <SegmentedButton id="option-2-info" label="Salary" />
          <SegmentedButton id="option-3-info" label="Contract" />
        </SegmentedButtonGroup>
      </div>
    );

}
`;

<CodeExample scope={scope} code={segmentedButtonGroupCodeDefault} />

### Default with Icons

The default `SegmentedButtonGroup` can also display both the label and any provided icons.

export const segmentedButtonGroupCodeDefaultWithIcons = `
  () => {
    const [selectedOption, setSelectedOption] = useState('option-1-default-icons');
    const handleOnChange = (selectedId) => {
      console.log('Selected option:', selectedId);
      setSelectedOption(selectedId);
    };
    return (
      <SegmentedButtonGroup id="example-2" ariaLabel="View" selectedOption={selectedOption} onChange={handleOnChange}>
        <SegmentedButton id="option-1-default-icons" icon="time" label="Hourly" />
        <SegmentedButton id="option-2-default-icons" icon="currency" label="Salary" />
        <SegmentedButton id="option-3-default-icons" icon="file" label="Contract" />
      </SegmentedButtonGroup>
    );
}
`;

<CodeExample scope={scope} code={segmentedButtonGroupCodeDefaultWithIcons} />

### Icon Only

The Icon Only variation displays the icons, with their respective label as a `ToolTip`.

export const segmentedButtonGroupCodeIconOnly = `
  () => {
    const [selectedOption, setSelectedOption] = useState('option-1-icon');
    const handleOnChange = (selectedId) => {
      console.log('Selected option:', selectedId);
      setSelectedOption(selectedId);
    };
    return (
      <SegmentedButtonGroup id="example-3" iconOnly ariaLabel="View" selectedOption={selectedOption} onChange={handleOnChange}>
        <SegmentedButton tooltipPlacement="topCenter" id="option-1-icon" label="List" icon="list"/>
        <SegmentedButton tooltipPlacement="topCenter" id="option-2-icon" label="Grid" icon="grid"/>
        <SegmentedButton tooltipPlacement="topCenter" id="option-3-icon" label="Graph" icon="barChart"/>
        <SegmentedButton tooltipPlacement="topCenter" id="option-4-icon" label="Calendar" icon="calendar"/>

      </SegmentedButtonGroup>
    );

}
`;

<CodeExample scope={scope} code={segmentedButtonGroupCodeIconOnly} />

### Mobile

The Mobile variation functions as a dropdown, displaying the currently selected item. When opened, it shows the remaining options for selection.

export const segmentedButtonGroupCodeMobile = `
  () => {
    const [selectedOption, setSelectedOption] = useState('option-1-mobile');
    const handleOnChange = (selectedId) => {
      console.log('Selected option:', selectedId);
      setSelectedOption(selectedId);
    };

    return (
      <SegmentedButtonGroup id="example-4" mobile ariaLabel="Employee Status" selectedOption={selectedOption} onChange={handleOnChange}>
        <SegmentedButton id="option-1-mobile" label="Active" icon="faceHappy"/>
        <SegmentedButton id="option-2-mobile" label="Probation" icon="hourglass"/>
        <SegmentedButton id="option-3-mobile" label="Terminated" icon="flag"/>
      </SegmentedButtonGroup>
    );

}
`;

<CodeExample scope={scope} code={segmentedButtonGroupCodeMobile} />

## Accessing Segmented Button Group using ref

Click on the Button to access the group, refer to the console for the element details.

export const segmentedButtonGroupRefCode = `
() => {
    const ref = React.useRef(null);
      const [selectedOption, setSelectedOption] = useState('option-1-ref');
    const handleOnChange = (selectedId) => {
      console.log('Selected option:', selectedId);
      setSelectedOption(selectedId);
    };

    return (
      <div style={{display: 'flex', flexDirection: 'column', gap: 'var(--evr-spacing-sm)', alignItems: 'center'}}>
        <Button
          id="access-segmented-button-group-btn"
          label="Click to access Segmented Button Group"
          onClick={() => {
            console.log(ref.current);
          }}
        />

        <SegmentedButtonGroup ref={ref} ariaLabel="View" selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="option-1-ref" label="List" icon="list"/>
          <SegmentedButton id="option-2-ref" label="Grid" icon="grid"/>
          <SegmentedButton id="option-3-ref" label="Graph" icon="barChart"/>
        </SegmentedButtonGroup>
      </div>
    );

}
`;

<CodeExample scope={scope} code={segmentedButtonGroupRefCode} />

## Usage Guidelines

- Include up to 5 `SegmentedButton` children under `SegmentedButtonGroup`.

- Use `SegmentedButtonGroup` when there is toggling between settings or options for viewing a set of data.

- Either all `SegmentedButtons` includes an `Icon` or none of them do. Do not mix and match.

## Accessibility

Users will be able to navigate default or icon only variations of `SegmentedButtonGroup` with <kbd>Tab</kbd> keys. To activate a button, keyboard users will use the <kbd>Space</kbd> or <kbd>Enter</kbd> keys.

For the mobile variation of `SegmentedButtonGroup`, keyboard navigation will be similar to that of `PopoverMenu`.
The menu can be navigated with a keyboard using the Arrow, <kbd>Home</kbd> and <kbd>End</kbd> keys.
Selections are made with the <kbd>Enter</kbd> and <kbd>Space</kbd> keys. Jump to a menu item by pressing the first letter of a menu item's text.

An aria-label is required for `SegmentedButtonGroup`. This should be a short description of the purpose of the controls.
