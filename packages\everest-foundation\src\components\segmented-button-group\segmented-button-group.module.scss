@use '../../index.scss' as helper;

.evrSegmentedButtonGroup {
  &.container {
    display: inline-flex;
    border: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-default);
    border-radius: var(--evr-radius-md);
    background-color: var(--evr-surfaces-primary-default);
    gap: var(--evr-spacing-3xs);
    min-height: var(--evr-spacing-lg);

    align-items: center;
    box-sizing: border-box;
  }
  &.mobileIconWrapper {
    display: inline-flex;
    padding-inline-end: var(--evr-spacing-xs);
  }
}

.visuallyHidden {
  @include helper.visuallyHidden();
}
