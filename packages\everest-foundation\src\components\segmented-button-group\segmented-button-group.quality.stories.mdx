import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { userEvent, screen } from '@storybook/test';
import { action } from '@storybook/addon-actions';
import { SegmentedButtonGroup } from './segmented-button-group';
import { SegmentedButton } from '../segmented-button';
import { Chromatic, ChromaticDecorators } from '../../../chromatic';
import { useState } from 'react';

<Meta
  title="Testing/Automation Test Cases/Segmented Button Group"
  component={SegmentedButtonGroup}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    onChange: () => undefined,
    label: 'label',
    testId: 'segmented-button-group-test-id',
  }}
/>

# Segmented Button

## Live Demo

<Canvas>
  <Story name="Default Icons Neutral">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex1-option-1-default-icons');
      
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup {...args} id="example-1" ariaLabel="View" selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex1-option-1-default-icons" icon="time" label="Hourly" />
          <SegmentedButton id="ex1-option-2-default-icons" icon="currency" label="Salary" />
          <SegmentedButton id="ex1-option-3-default-icons" icon="file" label="Contract" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>
<Canvas>
  <Story name="Default Label Only Neutral">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex2-option-1');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-2" {...args} selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex2-option-1" label="All" />
          <SegmentedButton id="ex2-option-2" label="Favorited" />
          <SegmentedButton id="ex2-option-3" label="Unfavorited" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Icon Only Neutral">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex3-option-1-icon');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-3" {...args} iconOnly selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton tooltipPlacement="bottomCenter" id="ex3-option-1-icon" label="All" icon="employees" />
          <SegmentedButton tooltipPlacement="bottomCenter" id="ex3-option-2-icon" label="Favorited" icon="favoriteFilled" />
          <SegmentedButton tooltipPlacement="bottomCenter" id="ex3-option-3-icon" label="Unfavorited" icon="favoriteOutline" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Mobile Label and Icon Neutral">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex4-option-1');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-4" {...args} mobile selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex4-option-1" label="All" icon="employees" />
          <SegmentedButton id="ex4-option-2" label="Favorited" icon="favoriteFilled" />
          <SegmentedButton id="ex4-option-3" label="Unfavorited" icon="favoriteOutline" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Mobile Label Only Neutral">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex5-option-1');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-5" {...args} mobile selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex5-option-1" label="All" />
          <SegmentedButton id="ex5-option-2" label="Favorited" />
          <SegmentedButton id="ex5-option-3" label="Unfavorited" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Default Icons Informational">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex6-option-1');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-6" {...args} variant="informational" selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex6-option-1" label="All" icon="employees" />
          <SegmentedButton id="ex6-option-2" label="Favorited" icon="favoriteFilled" />
          <SegmentedButton id="ex6-option-3" label="Unfavorited" icon="favoriteOutline" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Default Label Only Informational">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex7-option-1');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-7" {...args} variant="informational" selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex7-option-1" label="All" />
          <SegmentedButton id="ex7-option-2" label="Favorited" />
          <SegmentedButton id="ex7-option-3" label="Unfavorited" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Icon Only Informational">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex8-option-1-icon');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-8" {...args} iconOnly variant="informational" selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton tooltipPlacement="bottomCenter" id="ex8-option-1-icon" label="All" icon="employees" />
          <SegmentedButton tooltipPlacement="bottomCenter" id="ex8-option-2-icon" label="Favorited" icon="favoriteFilled" />
          <SegmentedButton tooltipPlacement="bottomCenter" id="ex8-option-3-icon" label="Unfavorited" icon="favoriteOutline" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Mobile Label and Icon Informational">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex9-option-1');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-9" {...args} mobile variant="informational" selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex9-option-1" label="All" icon="employees" />
          <SegmentedButton id="ex9-option-2" label="Favorited" icon="favoriteFilled" />
          <SegmentedButton id="ex9-option-3" label="Unfavorited" icon="favoriteOutline" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="Mobile Label Only Informational">
    {(args) => {
      const [selectedOption, setSelectedOption] = useState('ex10-option-1');
      const handleOnChange = (selectedId) => {
        console.log('Selected option:', selectedId);
        setSelectedOption(selectedId);
      };

      return (
        <SegmentedButtonGroup id="example-10" {...args} mobile variant="informational" selectedOption={selectedOption} onChange={handleOnChange}>
          <SegmentedButton id="ex10-option-1" label="All" />
          <SegmentedButton id="ex10-option-2" label="Favorited" />
          <SegmentedButton id="ex10-option-3" label="Unfavorited" />
        </SegmentedButtonGroup>
      );
    }}

  </Story>
</Canvas>

<Canvas>
  <Story
    name="Mobile Open"
    play={async ({}) => {
      userEvent.click(screen.getByRole('button'));
    }}
  >
    {(args) => (
      <SegmentedButtonGroup
        id="example-11"
        {...args}
        mobile
        selectedOption="ex11-option-1"
        onChange={action('onChange')}
      >
        <SegmentedButton testId="test-option-1" id="ex11-option-1" label="All" />
        <SegmentedButton id="ex11-option-2" label="Favorited" />
        <SegmentedButton id="ex11-option-3" label="Unfavorited" />
      </SegmentedButtonGroup>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Icon Only Activated"
    decorators={[ChromaticDecorators.padStory]}
    play={async () => {
      await userEvent.tab();
    }}
  >
    {(args) => (
      <div style={{ height: '500px'}}>
        <SegmentedButtonGroup
          id="example-12"
          {...args}
          iconOnly
          selectedOption="ex12-option-1-icon"
          onChange={action('onChange')}
        >
          <SegmentedButton
            testId="test-icon-1"
            tooltipPlacement="bottomCenter"
            id="ex12-option-1-icon"
            label="All"
            icon="employees"
          />
          <SegmentedButton
            tooltipPlacement="bottomCenter"
            id="ex12-option-2-icon"
            label="Favorited"
            icon="favoriteFilled"
          />
          <SegmentedButton
            tooltipPlacement="bottomCenter"
            id="ex12option-3-icon"
            label="Unfavorited"
            icon="favoriteOutline"
          />
        </SegmentedButtonGroup>
      </div>
    )}
  </Story>
</Canvas>
<Canvas>
  <Story
    name="[Playwright] - Interactions"
    parameters={{ chromatic: Chromatic.DISABLE }}
  >
    {(args) => {
      const [selectedOptions, setSelectedOptions] = useState({
        informational: 'option-1',
        mobile: 'mobile-1',
        iconOnly: 'icon-1',
      });

      const handleOnChange = (group, selectedId) => {
        console.log(`Selected option for ${group}:`, selectedId);
        setSelectedOptions((prev) => ({
          ...prev,
          [group]: selectedId,
        }));
      };

      return (
        <>
          <SegmentedButtonGroup
            {...args}
            variant="informational"
            selectedOption={selectedOptions.informational}
            onChange={(selectedId) => handleOnChange('informational', selectedId)}
          >
            <SegmentedButton id="option-1" label="All" />
            <SegmentedButton id="option-2" label="None" />
            <SegmentedButton id="option-3" label="Favorited" />
          </SegmentedButtonGroup>

          <SegmentedButtonGroup
            {...args}
            mobile
            variant="informational"
            selectedOption={selectedOptions.mobile}
            onChange={(selectedId) => handleOnChange('mobile', selectedId)}
          >
            <SegmentedButton id="mobile-1" label="Tape" />
            <SegmentedButton id="mobile-2" label="Rug" />
            <SegmentedButton id="mobile-3" label="Mop" />
          </SegmentedButtonGroup>

          <SegmentedButtonGroup
            {...args}
            iconOnly
            variant="informational"
            selectedOption={selectedOptions.iconOnly}
            onChange={(selectedId) => handleOnChange('iconOnly', selectedId)}
          >
            <SegmentedButton tooltipPlacement="bottomCenter" id="icon-1" icon="rings" label="Shoe" />
            <SegmentedButton tooltipPlacement="bottomCenter" id="icon-2" icon="rings" label="Table" />
            <SegmentedButton tooltipPlacement="bottomCenter" id="icon-3" icon="rings" label="Chair" />
          </SegmentedButtonGroup>
        </>
      );
    }}

  </Story>
</Canvas>
