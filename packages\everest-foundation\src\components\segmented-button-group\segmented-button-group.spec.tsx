import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { SegmentedButtonGroup } from './segmented-button-group';
import { SegmentedButton } from '../segmented-button/segmented-button';

describe('[SegmentedButtonGroup]', () => {
  const ariaLabel = 'Segmented Button Group';
  const onChange = jest.fn();
  beforeEach(() => {
    // scrollIntoView is not a function in jest
    // https://github.com/jsdom/jsdom/issues/1695#issuecomment-449931788
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });
  it('should render all segmented buttons in the group', () => {
    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel={ariaLabel}
        selectedOption="option-2"
        onChange={() => undefined}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(3);
    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('Favorited')).toBeInTheDocument();
    expect(screen.getByText('Unfavorited')).toBeInTheDocument();
  });

  it('should trigger "onChange" when a button is selected', async () => {
    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        selectedOption={'option-2'}
        onChange={onChange}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    const button = screen.getByText('Unfavorited');
    await userEvent.click(button);

    await waitFor(() => {
      expect(onChange).toHaveBeenCalledTimes(1);
    });
  });

  it('should correctly mark the selected option', () => {
    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        selectedOption="option-2"
        onChange={onChange}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    const selectedButton = screen.getByRole('button', { name: 'Favorited' });
    expect(selectedButton).toBeInTheDocument();

    expect(selectedButton).toHaveAttribute('aria-pressed', 'true');
  });

  it('should render buttons with icons when iconOnly is true', () => {
    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        iconOnly={true}
        selectedOption={'option-2'}
        onChange={() => undefined}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    const buttons = screen.getAllByRole('button');
    expect(buttons[0]).toHaveAttribute('aria-label', 'All');
    expect(buttons[1]).toHaveAttribute('aria-label', 'Favorited');
    expect(buttons[2]).toHaveAttribute('aria-label', 'Unfavorited');
  });

  it('should render the group in a dropdown when mobile is true', async () => {
    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        mobile={true}
        selectedOption={'option-1'}
        onChange={() => undefined}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    const dropdownTrigger = screen.getByRole('button');
    await userEvent.click(dropdownTrigger);

    await waitFor(() => screen.getByRole('menu'));
    const dropdownMenu = screen.getByRole('menu');

    expect(dropdownMenu).toBeInTheDocument();
  });

  it('should render provided icons with labels in the dropdown when mobile is true', async () => {
    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        mobile={true}
        selectedOption={'option-1'}
        onChange={() => undefined}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    // Click dropdown trigger
    const dropdownTrigger = screen.getByRole('button');
    await userEvent.click(dropdownTrigger);

    await waitFor(() => screen.getByRole('menu'));

    const dropdownItems = screen.getAllByRole('menuitem');

    // Check for the labels
    expect(dropdownItems[0]).toHaveTextContent('Favorited');
    expect(dropdownItems[1]).toHaveTextContent('Unfavorited');

    // Check for the icons
    expect(dropdownItems[0].querySelector('svg[data-evr-name="favoriteFilled"]')).toBeInTheDocument();
    expect(dropdownItems[1].querySelector('svg[data-evr-name="favoriteOutline"]')).toBeInTheDocument();
  });

  it('should set class to "segmentedNeutral" when variant is "neutral"', () => {
    const variant = 'neutral';
    const testId = 'test-id';

    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        selectedOption={'option-1'}
        variant={variant}
        onChange={() => undefined}
        mobile
        testId={testId}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    expect(screen.getByTestId(`${testId}-popover-menu-button`)).toHaveClass('neutralSegmentedButton');
  });

  it('should set class to "segmentedInformational" when variant is "informational"', () => {
    const variant = 'informational';
    const testId = 'test-id';

    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        selectedOption={'option-1'}
        variant={variant}
        onChange={() => undefined}
        mobile
        testId={testId}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );

    expect(screen.getByTestId(`${testId}-popover-menu-button`)).toHaveClass('informationalSegmentedButton');
  });

  it('should set aria-label when provided to group', () => {
    const testId = 'test-id';

    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        selectedOption={'option-1'}
        onChange={() => undefined}
        testId={testId}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );
    const buttonGroup = screen.getByTestId(testId);
    expect(buttonGroup).toHaveAttribute('aria-labelledby', 'segmented-button-group-id-aria-label');

    const label = screen.getByText(ariaLabel);
    expect(label).toBeInTheDocument();
  });

  it('should set aria-label to trigger button label for mobile group', () => {
    const testId = 'test-id';

    render(
      <SegmentedButtonGroup
        id="segmented-button-group-id"
        ariaLabel="Segmented Button Group"
        selectedOption={'option-1'}
        onChange={() => undefined}
        mobile
        testId={testId}
      >
        <SegmentedButton id="option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );
    const triggerButton = screen.getByRole('button', { name: 'All' });
    expect(triggerButton).toHaveAttribute('aria-label', 'All');
  });
});
