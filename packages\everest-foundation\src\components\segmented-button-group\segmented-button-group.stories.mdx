import { Meta, <PERSON>, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { action } from '@storybook/addon-actions';
import { SegmentedButtonGroup } from './segmented-button-group';
import { SegmentedButton } from '../segmented-button/segmented-button';
import Examples from './segmented-button-group.examples.mdx';
import { useArgs } from '@storybook/client-api';

<Meta
  title="Components/Segmented Button/Segmented Button Group"
  component={SegmentedButtonGroup}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/ixHav72aJwOo4s5I7kqk6d/%F0%9F%A7%AA-Segmented-Button?node-id=4362-37314&t=u5G2EjLoCbFmM0tT-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Sets the `id` attribute.',
      type: { required: true },
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets data-test-id attribute on the HTML element.',
      type: { required: false },
    },
    ariaLabel: {
      type: 'string',
      control: 'text',
      description:
        'Associates the button group with a descriptive label, ensuring that assistive technologies describe the behavior of the controls.',
      type: { required: true },
    },
    onChange: {
      action: 'clicked',
      description:
        'Callback fired when a button item is selected. One parameter for the `id` of the selected `SegmentedButton`.',
      table: {
        type: { summary: '(id: string) => void' },
      },
    },
    selectedOption: {
      type: 'string',
      control: 'text',
      description:
        'The selected option in the `SegmentedButtonGroup`. This value is the `id` of a `SegmentedButton`. If none is specified, the first button item is selected.',
      type: { required: false },
    },
    iconOnly: {
      description: 'Hides the labels and displays buttons as icons only. The labels are shown in tooltips.',
      control: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    mobile: {
      description: 'Displays the group in a dropdown.',
      control: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    variant: {
      description: 'Changes the color of the selected button.',
      control: 'radio',
      options: ['neutral', 'informational'],
      table: {
        defaultValue: { summary: 'neutral' },
      },
    },
  }}
  args={{
    id: 'segmented-button-group-id',
    iconOnly: false,
    mobile: false,
    ariaLabel: 'Segmented Button Group',
    onChange: action('onChange'),
    variant: 'neutral',
    selectedOption: 'option-2',
  }}
/>

# Segmented Button Group

<Examples />

## Live Demo

<Canvas>
  <Story name="Segmented Button Group">
    {(args) => {
    const [, updateArgs] = useArgs();
    const handleOnChange = (selectedId) => {
      action('onChange')(selectedId);
      updateArgs({ selectedOption: selectedId });
    };
 
    return (
      <SegmentedButtonGroup {...args} onChange={handleOnChange}>
        <SegmentedButton tooltipPlacement="bottomCenter" id="option-1" label="All" icon="employees" />
        <SegmentedButton tooltipPlacement="bottomCenter" id="option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton tooltipPlacement="bottomCenter" id="option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );
    }}

  </Story>
</Canvas>

<ArgsTable story="Segmented Button Group" />
