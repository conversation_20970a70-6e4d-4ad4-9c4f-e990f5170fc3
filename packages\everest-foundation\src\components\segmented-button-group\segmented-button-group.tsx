import React, { Children, forwardRef, PropsWithChildren, useMemo } from 'react';
import classnames from 'classnames';

import { SegmentedButtonContext, TSegmentedButtonVariant } from './segmented-button-group-context';
import { Icon, TIconName } from '../icon';
import { PopoverMenu, PopoverMenuItem } from '../popover-menu';

import styles from './segmented-button-group.module.scss';

interface ISegmentedButtonGroupProps {
  id: string;
  testId?: string;
  mobile?: boolean;
  iconOnly?: boolean;
  selectedOption?: string;
  onChange: (id: string) => void;
  ariaLabel: string;
  variant?: TSegmentedButtonVariant;
  ref?: React.ForwardedRef<HTMLDivElement> | React.ForwardedRef<HTMLLIElement>;
}

export const SegmentedButtonGroup = forwardRef<
  HTMLDivElement | HTMLLIElement,
  PropsWithChildren<ISegmentedButtonGroupProps>
>((props, ref) => {
  const {
    id,
    testId,
    children,
    mobile = false,
    iconOnly = false,
    selectedOption = '',
    onChange,
    ariaLabel,
    variant = 'neutral',
  } = props;
  const buttonsDict = useMemo(() => {
    if (!children) {
      return {};
    }

    const dict: Record<string, { testId: string; label: string; icon: TIconName }> = {};
    Children.forEach(children, (child) => {
      if (React.isValidElement(child)) {
        dict[child.props.id] = {
          testId: child.props.testId,
          label: child.props.label,
          icon: child.props.icon,
        };
      }
    });
    return dict;
  }, [children]);

  const unselectedChildrenIds = Object.keys(buttonsDict).filter((id) => id !== selectedOption);

  const contextValue = useMemo(
    () => ({
      selectedOption,
      onChange,
      mobile,
      iconOnly,
      variant,
    }),
    [selectedOption, onChange, mobile, iconOnly, variant]
  );

  const popoverMenuItems = useMemo(() => {
    return unselectedChildrenIds.map((childId) => {
      const { label, icon } = buttonsDict[childId];
      return (
        <PopoverMenuItem key={childId} id={childId} testId={buttonsDict[childId]?.testId}>
          {icon && (
            <span className={classnames(styles.evrSegmentedButtonGroup, styles.mobileIconWrapper)}>
              <Icon name={icon} />
            </span>
          )}
          {label}
        </PopoverMenuItem>
      );
    });
  }, [unselectedChildrenIds, buttonsDict]);

  return (
    <SegmentedButtonContext.Provider value={contextValue}>
      <span className={styles.visuallyHidden} id={`${id}-aria-label`}>
        {ariaLabel}
      </span>
      {mobile ? (
        <PopoverMenu
          buttonAriaLabel={buttonsDict[selectedOption]?.label}
          buttonLabel={buttonsDict[selectedOption]?.label}
          id={id}
          ref={ref}
          showChevron
          testId={testId}
          triggerOption={variant === 'neutral' ? 'segmentedNeutral' : 'segmentedInformational'}
          triggerProps={{
            startIcon: buttonsDict[selectedOption]?.icon,
          }}
          onChange={(value) => onChange(value.id)}
        >
          {popoverMenuItems}
        </PopoverMenu>
      ) : (
        <div
          className={classnames(styles.evrSegmentedButtonGroup, styles.container)}
          role="group"
          aria-labelledby={`${id}-aria-label`}
          id={id}
          data-testid={testId}
          ref={ref as React.Ref<HTMLDivElement>}
        >
          {children}
        </div>
      )}
    </SegmentedButtonContext.Provider>
  );
});

SegmentedButtonGroup.displayName = 'SegmentedButtonGroup';
