import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { SegmentedButton } from './segmented-button';
import { SegmentedButtonGroup } from '../segmented-button-group';
import { Button } from '../button';

export const scope = {
  SegmentedButtonGroup,
  SegmentedButton,
  Button,
};

`SegmentedButton` is designed as an individual toggle component, specifically used within a `SegmentedButtonGroup`.

## Usage

The default `SegmentedButton` displays a label and an optional icon. It should only be used as the child of `SegmentedButtonGroup`.

export const segmentedButtonCodeDefault = `
  () => {
    return (
      <SegmentedButton
        tooltipPlacement="bottomCenter"
        id="button-1"
        label="Default Button"
      />
    );
}
`;

<CodeExample scope={scope} code={segmentedButtonCodeDefault} />

### Using a Ref

You can use a `ref` to directly access the `SegmentedButton` instance.

export const segmentedButtonRefCode = `
  () => {
    const ref = React.useRef(null);

    return (
      <div style={{ display: 'flex', flexDirection: 'row', gap: 'var(--evr-spacing-sm)'}}>
        <Button
          id="access-segmented-button-btn"
          label="Click to access Segmented Button"
          onClick={() => {
            console.log(ref.current);
          }}
        />
        <SegmentedButton
          ref={ref}
          id="button-3"
          label="Button with Ref"
       />
      </div>
    );

}
`;

<CodeExample scope={scope} code={segmentedButtonRefCode} />

---

## How to Use

Use `SegmentedButton` as the children within a `SegmentedButtonGroup`.

## Accessibility

When selected in a `SegmentedButtonGroup`, the `SegmentedButton` will have `aria-pressed=true`.
In `iconOnly` mode, the `aria-label` of the individual buttons are set to their respective `labels`.
