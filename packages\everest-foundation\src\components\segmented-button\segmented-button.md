# Segmented Button Component

## Summary

Segmented buttons allow users to toggle between settings or options for viewing a set of data. They can be used in any data rich content layout including pages, dashboards, tables, cards or widgets.

Research and document the finding of Everest Segmented Button Component regarding its design and architecture overview. It is based on the Figma file: https://www.figma.com/design/ixHav72aJwOo4s5I7kqk6d/%F0%9F%A7%AA-Segmented-Button?node-id=0-

**Designer Q&A**

- What is the max number of buttons?
  - **Answer** : Five.
- Does any animation take place when toggling between buttons?
  - **Answer**: No.
- Can some segmented buttons have start icons, while others don't in the same group?
  - **Answer**: No, it should be consistent. Where all the buttons have start icons, or all of them do not.
- Are end icons supported?
  - **Answer**: No, only start icons are supported.
- Is it accessible to have a tooltip hold the label of an icon button?
  - **Answer**: Yes, provided that other requirements are met (screen reader, etc.)
- Can only one button be selected at a time?
  - **Answer**: Yes.
- Can segmented buttons have default selections?
  - **Answer**: Yes they can.
- Can segmented buttons be disabled?
  - No.

## API

### ISegmentedButtonGroup (Container)

1. **id**: `string`
   Required. `id` for the `SegmentedButtonGroup`.
1. **selectedOption**: `string | undefined`
   Optional. The default selected option in the segmented button. If none is specified, the first button item is selected.
1. **ariaLabel**: `string`
   Required. Associates the button group with a descriptive label, ensuring that assistive technologies describe the behaviour of the controls.
1. **testId**: `string | undefined`  
   Optional. Sets **data-test-id** attribute on the html element.
1. **onChange**: `(SegmentedButton.id) => void`
   Required. Callback fired when the button item is clicked.
1. **mobile**: `boolean | undefined`
   Optional. Default value is `false`. Displays the group in a dropdown.
1. **iconOnly**: `boolean | undefined`
   Optional. Default value is `false`. Hides the labels and displays buttons as icons only. The labels are shown in tooltips.

### ISegmentedButton (Each Item)

1. **id**: `string`
   Mandatory. `id` for the `SegmentedButton`.
1. **testId**: `string | undefined`
   Optional. `testId` for the `SegmentedButton`.
1. **label**: `string`
   Required. Visible label for the button item. However, if `iconOnly` is set to `true`, this value becaomes the `ariaLabel` and `tooltip` value for the `Segmented Button`.
1. **icon**: `TIconName`
   Optional. Start icon for the button item.

## Accessibility

The `SegmentedButtonGroup` component has a role of `group`, providing a clear semantic structure for related buttons.The `ariaLabel` attribute associates the button group with a descriptive label, ensuring that assistive technologies can convey the purpose of the group to users.

Each `SegmentedButton` includes the `aria-pressed` attribute, which indicates the current pressed state of the button, helping users understand which option is selected. When `iconOnly` is set to `true`, the labels are hidden and instead provided as tooltips, becoming the `aria-label` for each button. This ensures that all options remain discoverable.

Users will be able to navigate `SegmentedButtonGroup` with tab keys. To activate a button, KB users will use the space or enter keys.

## Usage

```typescript
() => {
  const [selectedOption, setSelectedOption] = useState();

  return (
    <SegmentedButtonGroup
      id="group-id"
      onChange={(id) => setSelectedOption(id)}
      selectedOption={selectedOption}
      ariaLabel="View"
    >
      <SegmentedButton id="item-id-0" label="Grid" icon="placeHolderWireframe" />
      <SegmentedButton id="item-id-1" label="Table" icon="placeHolderWireframe" />
      <SegmentedButton id="item-id-2" label="List" icon="placeHolderWireframe" />
    </SegmentedButtonGroup>
  );
};
```

## Future Considerations

Future iterations may require multi-select functionality. Consider changing `selectedOption` to an array `selectedOptions` in `ISegmentedButtonGroup` to accommodate multiple selections. Modify internal logic to add or remove buttons from the selection when clicked.

## Other Design Systems

**MUI**: https://m3.material.io/components/segmented-buttons/overview

- Provides a SegmentedButtonSet component that acts as a container for individual SegmentedButton components. This is a similar implementation to the outlined one in this document.

**Ant Design**: https://ant-design.antgroup.com/components/segmented

- The Segmented component features a straightforward API that allows developers to implement segmented controls quickly. With just a few props—like `options`, `value`, and `onChange`.

## Unresolved questions

## Required PBIs

### PWEB-16434: Architecture - https://dayforce.atlassian.net/browse/PWEB-16434

### PWEB-16435: Create Component Under Labs - https://dayforce.atlassian.net/browse/PWEB-16435

### PWEB-16437: A11y - https://dayforce.atlassian.net/browse/PWEB-16437

### PWEB-16836: Storybook Documentation - https://dayforce.atlassian.net/browse/PWEB-16436

### PWEB-16823: Testing - https://dayforce.atlassian.net/browse/PWEB-16823

### PWEB-16824: Push Component to Prod - https://dayforce.atlassian.net/browse/PWEB-16824

### PWEB-16826: Add to Ref App - https://dayforce.atlassian.net/browse/PWEB-16826

## Changelog

- 04/11/2024: Added `style` prop to allow consumers to change color of activated button (`informational` and `neutral`)
- 14/11/2024: Added `tooltipPlacement` prop to allow consumers to specify the placement of the `tooltip` in `iconOnly` mode.
