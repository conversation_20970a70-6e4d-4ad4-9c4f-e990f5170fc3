@use '../../variables.scss' as variables;
@use '@ceridianhcm/theme/dist/scss/' as typography;

.evrSegmentedButton {
  display: flex;
  align-items: center;
  margin: 0;
  cursor: pointer;
  border-radius: var(--evr-radius-md);
  padding-inline: var(--evr-spacing-sm);
  // account for extra height from box-shadow (1px on top and bottom)
  min-height: calc(var(--evr-spacing-lg) - 2 * var(--evr-border-width-thin-px));
  gap: var(--evr-spacing-3xs);
  box-sizing: border-box;

  font-family: 'Inter', sans-serif;
  font-size: var(--evr-font-size-md);
  font-weight: var(--evr-bold-weight);

  &.active {
    &.informational {
      color: var(--evr-content-status-informative-lowemp);
      fill: var(--evr-content-status-informative-lowemp);
      background-color: var(--evr-surfaces-status-informative-lowemp);
      box-shadow: variables.$segmentedButtonInformationalBoxShadow;
    }

    &.neutral {
      color: var(--evr-content-primary-highemp);
      fill: var(--evr-content-primary-highemp);
      background-color: var(--evr-surfaces-primary-selected);
      box-shadow: variables.$segmentedButtonNeutralBoxShadow;
    }
  }

  &.inactive {
    color: var(--evr-content-primary-lowemp);
    fill: var(--evr-content-primary-lowemp);
    background-color: transparent;
    box-shadow: variables.$segmentedButtonInactiveBoxShadow;

    &:hover {
      background-color: var(--evr-surfaces-primary-hovered);
    }

    &:active {
      background-color: var(--evr-surfaces-primary-selected);
    }
  }
}

@mixin evrSegmentedButton {
  @include typography.body2Bold;
  height: var(--evr-spacing-lg);
  padding-inline: var(--evr-spacing-sm) var(--evr-spacing-2xs);
}
