import React, { useState } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { SegmentedButton } from '../segmented-button/segmented-button';
import { SegmentedButtonGroup } from '../segmented-button-group';

describe('[SegmentedButton]', () => {
  const defaultProps = {
    id: 'group-1',
    tooltipPlacement: 'topCenter',
    ariaLabel: 'ariaLabel',
  };

  const SegmentedButtonGroupWithState = ({ iconOnly }: { iconOnly: boolean }) => {
    const [selectedOption, setSelectedOption] = useState('option-1');

    const handleChange = (id: string) => {
      setSelectedOption(id);
    };

    return (
      <SegmentedButtonGroup
        {...defaultProps}
        selectedOption={selectedOption}
        onChange={handleChange}
        iconOnly={iconOnly}
      >
        <SegmentedButton id="option-1" testId="test-option-1" label="All" icon="employees" />
        <SegmentedButton id="option-2" testId="test-option-2" label="Favorited" icon="favoriteFilled" />
        <SegmentedButton id="option-3" testId="test-option-3" label="Unfavorited" icon="favoriteOutline" />
      </SegmentedButtonGroup>
    );
  };

  it('should render the button with correct label and icon', () => {
    render(<SegmentedButtonGroupWithState iconOnly={false} />);

    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByTestId('test-option-1')).toBeInTheDocument();
  });

  it('should apply class based on the selected state', () => {
    render(<SegmentedButtonGroupWithState iconOnly={false} />);

    const button = screen.getByRole('button', { name: 'All' });
    expect(button).toHaveClass('active');

    const inactiveButton = screen.getByRole('button', { name: 'Favorited' });
    expect(inactiveButton).toHaveClass('inactive');
  });

  it('should update the selected state on click', async () => {
    render(<SegmentedButtonGroupWithState iconOnly={false} />);

    // 'All' button is selected initially
    expect(screen.getByRole('button', { name: 'All' })).toHaveClass('active');
    expect(screen.getByRole('button', { name: 'Favorited' })).not.toHaveClass('active');

    await userEvent.click(screen.getByText('Favorited'));

    // After click, 'Favorited' should be selected
    expect(screen.getByRole('button', { name: 'Favorited' })).toHaveClass('active');
    expect(screen.getByRole('button', { name: 'All' })).not.toHaveClass('active');
  });

  it('should render icon-only button with tooltip when iconOnly is true', () => {
    render(<SegmentedButtonGroupWithState iconOnly={true} />);

    // No labels should be visible
    expect(screen.queryByText('All')).not.toBeInTheDocument();
    expect(screen.getByTestId('test-option-1')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'All' })).toBeInTheDocument();
  });

  it('should log error when icon is missing in icon-only mode', () => {
    const consoleErrorMock = jest.spyOn(console, 'error').mockImplementation();

    render(
      <SegmentedButtonGroup {...defaultProps} selectedOption="option-1" onChange={() => undefined} iconOnly={true}>
        <SegmentedButton id="option-1" label="All" icon={undefined} />
      </SegmentedButtonGroup>
    );

    expect(consoleErrorMock).toHaveBeenCalledWith(
      `Icon is required for icon-only mode but is missing for button with label "All" and id "option-1".`
    );

    consoleErrorMock.mockRestore();
  });

  it('should set aria-label when iconOnly is true', () => {
    render(<SegmentedButtonGroupWithState iconOnly={true} />);

    const button = screen.getByRole('button', { name: 'All' });
    expect(button).toHaveAttribute('aria-label', `All`);
  });

  it('should apply focus ring style correctly when focused', async () => {
    render(<SegmentedButtonGroupWithState iconOnly={false} />);

    const button = screen.getByRole('button', { name: 'All' });

    await userEvent.tab();

    expect(button).toHaveStyle('outline-offset: calc(2 * var(--evr-focus-ring-outline-offset))');
  });
});
