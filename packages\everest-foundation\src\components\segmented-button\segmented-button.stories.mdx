import { <PERSON>a, <PERSON>, <PERSON><PERSON>, ArgsTable } from '@storybook/addon-docs';
import { action } from '@storybook/addon-actions';
import { SegmentedButtonGroup } from '../segmented-button-group';
import { SegmentedButton } from './segmented-button';
import Examples from './segmented-button.examples.mdx';
import { ICON_NAMES } from '../icon';

<Meta
  title="Components/Segmented Button/Segmented Button"
  component={SegmentedButton}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/ixHav72aJwOo4s5I7kqk6d/%F0%9F%A7%AA-Segmented-Button?node-id=4362-37314&t=u5G2EjLoCbFmM0tT-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Sets the `id` attribute.',
    },
    testId: {
      type: 'string',
      control: 'text',
      description: 'Sets data-test-id attribute on the HTML element.',
      type: { required: false },
    },
    label: {
      type: 'string',
      control: 'text',
      description: 'Sets visible label.',
    },
    icon: {
      type: 'enum',
      control: 'select',
      options: [...ICON_NAMES.sort(), null],
      description: 'Changes the icon used.',
    },
    tooltipPlacement: {
      type: 'enum',
      control: 'select',
      options: [
        'Undefined',
        'topLeft',
        'topCenter',
        'topRight',
        'rightCenter',
        'bottomRight',
        'bottomCenter',
        'bottomLeft',
        'leftCenter',
      ],
      description:
        'The initial placement of the `Tooltip` relative to the segmented button. Applicable only when inside an `iconOnly` `SegmentedButtonGroup',
      table: {
        defaultValue: { summary: 'bottomCenter' },
      },
    },
  }}
  args={{
    id: 'segmented-button-id',
    testId: 'segmented-button-test-id',
    label: 'Segment',
    icon: undefined,
    tooltipPlacement: 'bottomCenter',
  }}
/>

# Segmented Button

<Examples />

## Live Demo

<Canvas>
  <Story name="Segmented Button">
    {(args) => {
      const iconOnly = args.icon && args.tooltipPlacement !== 'Undefined';
      return (
        <SegmentedButtonGroup
          id="segmented-button-group-id"
          iconOnly={iconOnly}
          onChange={() => undefined}
          ariaLabel="Segmented Button Group"
        >
          <SegmentedButton {...args} tooltipPlacement={iconOnly ? args.tooltipPlacement : undefined} />
        </SegmentedButtonGroup>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="Segmented Button" />
