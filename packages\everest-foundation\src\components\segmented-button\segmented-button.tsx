import React, { PropsWithChildren } from 'react';
import { TPlacement } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { ButtonBase } from '../button-base';
import { Icon, TIconName } from '../icon';
import { useSegmentedButtonContext } from '../segmented-button-group/segmented-button-group-context';
import { Tooltip } from '../tool-tip/tool-tip';

import styles from './segmented-button.module.scss';

export interface ISegmentedButtonProps {
  id: string;
  testId?: string;
  label: string;
  icon?: TIconName;
  tooltipPlacement?: TPlacement;
}

export const SegmentedButton = React.forwardRef<HTMLButtonElement, PropsWithChildren<ISegmentedButtonProps>>(
  (props, ref) => {
    const { id, testId, label, icon, tooltipPlacement } = props;
    const { selectedOption, onChange, iconOnly, variant } = useSegmentedButtonContext();
    const isSelected = selectedOption === id;

    const handleSegmentedButtonFocusRingStyleTransform = (focusRingStyles: React.CSSProperties) => {
      return {
        ...focusRingStyles,
        outlineOffset: 'calc(2 * var(--evr-focus-ring-outline-offset))',
      };
    };

    const renderSegmentedButton = () => (
      <ButtonBase
        className={classnames(styles.evrSegmentedButton, {
          [styles.active]: isSelected,
          [styles.inactive]: !isSelected,
          [styles.informational]: variant === 'informational',
          [styles.neutral]: variant != 'informational',
        })}
        id={id}
        testId={testId}
        onClick={() => {
          onChange(id);
        }}
        ariaPressed={isSelected}
        ref={ref}
        ariaLabel={iconOnly ? label : undefined}
        focusRingStyleTransform={handleSegmentedButtonFocusRingStyleTransform}
      >
        {icon && <Icon name={icon} id={`${id}-icon`} testId={testId ? `${testId}-icon` : undefined} />}
        {!iconOnly && <span className={styles.label}>{label}</span>}
      </ButtonBase>
    );

    // Return null if icon is missing
    if (process.env.NODE_ENV !== 'production' && iconOnly && !icon) {
      console.error(
        `Icon is required for icon-only mode but is missing for button with label "${label}" and id "${id}".`
      );
      return null;
    }

    return iconOnly ? (
      <Tooltip id={`tooltip-${id}`} title={label} placement={tooltipPlacement || 'topCenter'}>
        {renderSegmentedButton()}
      </Tooltip>
    ) : (
      renderSegmentedButton()
    );
  }
);

SegmentedButton.displayName = 'SegmentedButton';
