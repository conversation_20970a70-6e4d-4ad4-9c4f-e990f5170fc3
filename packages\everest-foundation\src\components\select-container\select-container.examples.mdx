import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { SelectContainer } from './select-container';
import { FormFieldContainer } from '../form-field-container';
import { Warning } from '../../../.storybook/docs/shared/status-banner.tsx';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { AlphaBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';

export const scope = {
  SelectContainer,
  FormFieldContainer,
};

# Select Container

Select Container is a low-level building block that can be used along Form Field Container to build selection components such as Dropdown, Comboboxes, or MultiSelect that have shared requirements:

- Chevron icon
- Clear button

Select Container is meant to wrap content and optionally add clear button and chevron icon

## Examples

### Basic Usage

In order to display a Select Container, use it with the Form Field Container component:

export const basicExample = `() => {
  const style = {
    height: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    paddingInlineStart: "16px",
  }
  const renderSelectContainer = () => {
    return (
      <SelectContainer renderContent={() => <p className={'evrBodyText1'} style={style}>Select Container</p>}/>
    );
  };
  return (
    <div style={{ width: '50%' }} >
        <FormFieldContainer
        renderContent={renderSelectContainer}
        />
    </div>
  )
}`;

<CodeExample scope={scope} code={basicExample} />

### Advanced Usage

To see more advanced usages of the Select Container component take a look at the <LinkTo kind="Components/Dropdown">Dropdown</LinkTo> and <LinkTo kind="Components/Combobox">Combobox</LinkTo> components.
