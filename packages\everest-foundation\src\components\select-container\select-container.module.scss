@use '../../variables.scss';

.evrSelectContainer {
  width: 100%;
  display: flex;
  position: relative;
  background: transparent;
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
  }

  &.readOnly {
    cursor: text;
  }

  .innerWrapper {
    width: 100%;
    height: 100%;
    background-color: var(--evr-surfaces-primary-default);
    border: 0;
    border-radius: var(--evr-radius-2xs);

    &.readOnly {
      background-color: var(--evr-inactive-surfaces);
    }
  }

  .clearButtonWrapper {
    position: absolute;
    display: flex;
    align-items: center;
    height: 100%;
    inset-inline-end: calc(
      var(--evr-spacing-md) + var(--evr-spacing-2xs) + var(--evr-spacing-xs) - var(--evr-border-width-thin-px)
    ); // icons width (24px) + icon padding (8px) + end padding (12px) - border

    .clearButton {
      display: flex;
      margin-inline-end: var(--evr-spacing-2xs);
      outline: none;
      &:hover {
        cursor: pointer;
        background-color: var(--evr-surfaces-secondary-hovered);
        border-radius: var(--evr-radius-circle);
      }
    }
  }

  .divider {
    display: flex;
    padding-block-start: var(--evr-size-3xs);
    height: var(--evr-size-sm);
  }
}
