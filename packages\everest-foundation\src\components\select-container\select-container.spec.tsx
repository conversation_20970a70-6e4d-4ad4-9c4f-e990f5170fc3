import * as React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { ISelectContainer, SelectContainer } from './select-container';

const defaultProps: ISelectContainer = {
  id: 'base-id',
  testId: 'test-id',
  disabled: false,
  showClearButton: true,
  chevronIconOpen: true,
  clearButtonFocusable: true,
  clearButtonAriaLabel: 'Clear Button',
  renderContent: () => <h2>Hello World</h2>,
};

const getRenderContent = () => screen.getByText('Hello World');
const getClearButton = () => screen.getByRole('button', { name: '<PERSON> Button' });
const getHiddenClearButton = () => document.getElementById('base-id-clear-button');
const getChevronIcon = () => screen.getByTestId(`${defaultProps.testId}-icon-wrapper-icon`);

describe('[SelectContainer]', () => {
  afterEach(jest.clearAllMocks);

  it('renders the content', () => {
    render(<SelectContainer {...defaultProps} />);
    expect(getRenderContent()).toBeInTheDocument();
  });

  it('flips the chevron when "chevronIconOpen" is true', () => {
    render(<SelectContainer {...defaultProps} />);
    expect(getChevronIcon()).toHaveAttribute('data-evr-name', 'chevronUpSmall');
  });

  it('allows focusing clear button when "clearButtonFocusable" is true', async () => {
    render(<SelectContainer {...defaultProps} />);

    getClearButton().focus();
    await waitFor(() => expect(getClearButton()).toHaveFocus());

    expect(getClearButton()).toHaveAttribute('tabIndex', '0');
    expect(getClearButton()).toHaveAttribute('aria-hidden', 'false');
  });

  it('does not allow focusing clear button when "clearButtonFocusable" is false', () => {
    render(<SelectContainer {...defaultProps} clearButtonFocusable={false} />);
    expect(getHiddenClearButton()).toHaveAttribute('tabIndex', '-1');
    expect(getHiddenClearButton()).toHaveAttribute('aria-hidden', 'true');
  });

  it('sets the aria-label correctly', () => {
    render(<SelectContainer {...defaultProps} />);
    expect(getClearButton()).toHaveAttribute('aria-label', 'Clear Button');
  });

  it('calls "onClear" when the button is activated', async () => {
    const onClear = jest.fn();
    render(<SelectContainer {...defaultProps} onClear={onClear} />);

    await userEvent.click(getClearButton());
    expect(onClear).toBeCalledTimes(1);
  });

  it('calls "onClearBlur" when the button is blurred', () => {
    const onClearButtonBlur = jest.fn();
    render(<SelectContainer {...defaultProps} onClearButtonBlur={onClearButtonBlur} />);

    getClearButton().focus();
    getClearButton().blur();
    expect(onClearButtonBlur).toBeCalledTimes(1);
  });

  it('calls "onClearKeyUp" when clear button is clicked', async () => {
    const onClearButtonKeyUp = jest.fn();
    render(<SelectContainer {...defaultProps} onClearButtonKeyUp={onClearButtonKeyUp} />);

    getClearButton().focus();
    await waitFor(() => expect(getClearButton()).toHaveFocus());

    await userEvent.keyboard('{enter}');
    expect(onClearButtonKeyUp).toBeCalledTimes(1);
  });

  it('calls "onClearKeyDown" when a key is pressed', async () => {
    const onClearButtonKeyDown = jest.fn();
    render(<SelectContainer {...defaultProps} onClearButtonKeyDown={onClearButtonKeyDown} />);

    getClearButton().focus();
    await waitFor(() => expect(getClearButton()).toHaveFocus());

    await userEvent.keyboard('{Tab}');
    expect(onClearButtonKeyDown).toBeCalledTimes(1);
  });

  it('calls "onChevronIconClick" when chevronIcon is clicked', async () => {
    const onChevronIconClick = jest.fn();
    render(<SelectContainer {...defaultProps} onChevronIconClick={onChevronIconClick} />);

    await userEvent.click(getChevronIcon());
    expect(onChevronIconClick).toBeCalledTimes(1);
  });

  it('calls "onMouseEnter"  when mouse enters', async () => {
    const onMouseEnter = jest.fn();
    render(<SelectContainer {...defaultProps} onMouseEnter={onMouseEnter} />);

    await userEvent.hover(getClearButton());
    expect(onMouseEnter).toHaveBeenCalledTimes(1);
  });

  it('calls "onMouseOut"  when mouse leaves', async () => {
    const onMouseOut = jest.fn();
    render(<SelectContainer {...defaultProps} onMouseOut={onMouseOut} />);

    await userEvent.unhover(getClearButton());
    expect(onMouseOut).toHaveBeenCalledTimes(1);
  });

  it('calls "onClearFocus" when the button is focused', () => {
    const onClearButtonFocus = jest.fn();
    render(<SelectContainer {...defaultProps} onClearButtonFocus={onClearButtonFocus} />);

    getClearButton().focus();
    getClearButton().blur();
    expect(onClearButtonFocus).toBeCalledTimes(1);
  });

  it('hides clear button when "showClearButton" is false', () => {
    render(<SelectContainer {...defaultProps} showClearButton={false} />);
    expect(getHiddenClearButton()).not.toBeInTheDocument();
  });
});
