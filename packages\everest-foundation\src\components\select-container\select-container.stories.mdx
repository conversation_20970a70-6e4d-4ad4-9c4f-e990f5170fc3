import { <PERSON>a, Story, Can<PERSON>, ArgsTable } from '@storybook/addon-docs';
import { SelectContainer } from './select-container';
import { FormFieldContainer } from '../form-field-container';
import Examples from './select-container.examples.mdx';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';
import { useCreateTestId } from '../../utils/use-create-testid.tsx';
import { mergeRefs } from '../../utils/merge-refs.ts';

<Meta
  title="Toolbox/Select Container"
  component={SelectContainer}
  parameters={{
    controls: {
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    testId: {
      type: 'text',
      control: 'text',
      description:
        'Sets a value for `data-testid` attribute on the select container element. Used for automation testing.',
    },
    disabled: {
      type: 'boolean',
      control: 'boolean',
      description: 'Sets the disabled attribute on the select container.',
    },
    readOnly: {
      type: 'boolean',
      control: 'boolean',
      description: 'Sets the readOnly attribute on the select container.',
    },
    showClearButton: {
      type: 'boolean',
      control: 'boolean',
      description: 'Controls visibility of the clear button',
    },
    chevronIconOpen: {
      type: 'boolean',
      control: 'boolean',
      description: 'Controls visibility of the overlay',
    },
    clearButtonAriaLabel: {
      description: 'Aria-label for the clear button.',
    },
    onClear: {
      description: 'Takes a function that should fire when the clear button is triggered.',
    },
    onClearButtonKeyUp: {
      description: 'Takes a function that should fire when key is released from clear button.',
    },
    onClearButtonKeyDown: {
      description: 'Takes a function that should fire when a key is pressed down on the clear button.',
    },
    onClearButtonBlur: {
      description: 'Takes a function that should fire when the clear button losses focus.',
    },
    onClearButtonFocus: {
      description: 'Takes a function that should fire when the clear button receives focus.',
    },
    onChevronIconClick: {
      description: 'Takes a function that should fire when the ChevronIcon is clicked.',
    },
    onMouseEnter: {
      description: 'Takes a function that should fire when the mouse enters.',
    },
    onMouseOut: {
      description: 'Takes a function that should fire when the mouse leaves.',
    },
    clearButtonRef: {
      control: '-',
      description: 'Reference to the clear button.',
    },
    renderContent: {
      description: 'Set the content that should be displayed in the select container element.',
    },
  }}
  args={{
    testId: 'select-container-test-id',
    disabled: false,
    readOnly: false,
    showClearButton: false,
    chevronIconOpen: false,
    clearButtonAriaLabel: 'Clear selection',
    onClear: action('onClear'),
    onClearButtonKeyUp: action('onClearButtonKeyUp'),
    onClearButtonKeyDown: action('onClearButtonKeyDown'),
    onClearButtonBlur: action('onClearButtonBlur'),
    onClearButtonFocus: action('onClearButtonFocus'),
    onMouseEnter: action('onMouseEnter'),
    onMouseOut: action('onMouseOut'),
  }}
/>

<Examples />

<Canvas>
  <Story name="Select Container">
    {(args) => {
      const renderSelectContainer = () => {
        return <SelectContainer {...args} />;
      };
      return <FormFieldContainer renderContent={renderSelectContainer} />;
    }}
  </Story>
</Canvas>

### Select Container props

<ArgsTable story="Select Container" />
