import React, { RefObject, useRef } from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { Divider } from '../divider';
import { FormFieldContainerIconWrapper, TFormFieldContainerIconWrapperVerticalAlign } from '../form-field-container';
import { Icon } from '../icon';
import { IListBoxTextMap } from '../list-box';

import styles from './select-container.module.scss';

export type TSelectContainerTriggerAreaStatus = 'default' | 'error' | 'success';

export interface ISelectContainerTriggerAreaAltText extends IListBoxTextMap {
  clearButton?: string;
  selectedItem?: string;
  unselectedItem?: string;
}

export interface ISelectContainerAltText {
  clearButton?: string;
  selectedItem?: string;
  unselectedItem?: string;
}

export interface ISelectContainer {
  id: string;
  testId?: string;
  disabled?: boolean;
  readOnly?: boolean;
  showClearButton?: boolean;
  showChevronIcon?: boolean;
  chevronIconOpen?: boolean;
  chevronIconVerticalAlign?: TFormFieldContainerIconWrapperVerticalAlign;
  clearButtonFocusable?: boolean;
  clearButtonAriaLabel?: string;
  onClear?: (triggeredByMouse: boolean) => void;
  onClearButtonKeyUp?: (e: React.KeyboardEvent) => void;
  onClearButtonKeyDown?: (e: React.KeyboardEvent) => void;
  onClearButtonBlur?: (e: React.FocusEvent) => void;
  onClearButtonFocus?: (e: React.FocusEvent) => void;
  onChevronIconClick?: (e: React.MouseEvent) => void;
  onMouseEnter?: (e: React.MouseEvent) => void;
  onMouseOut?: (e: React.MouseEvent) => void;
  clearButtonRef?: RefObject<HTMLDivElement>;
  renderContent?: () => React.ReactNode;
  clickInsteadOfMouseDownOverride?: boolean;
}

/**
 * SelectContainer is an internal component for Selection components (dropdown, multiSelect, comboBox)
 * It handles Chevron button and Clear button
 */
export const SelectContainer = (props: ISelectContainer): JSX.Element => {
  const {
    id,
    testId,
    disabled,
    readOnly,
    showClearButton = false,
    showChevronIcon = true,
    chevronIconOpen = false,
    chevronIconVerticalAlign = 'middle',
    clearButtonFocusable = true,
    clearButtonAriaLabel,
    onClear,
    onClearButtonKeyUp,
    onClearButtonKeyDown,
    onClearButtonBlur,
    onClearButtonFocus,
    onChevronIconClick,
    clearButtonRef,
    renderContent,
    onMouseEnter,
    onMouseOut,
    clickInsteadOfMouseDownOverride = false, // TODO -- review override wrt onClick/MouseDown
  } = props;

  const handleClearButtonFocusRingStyleTransform = (defaultStyles: React.CSSProperties) => {
    return {
      ...defaultStyles,
      borderRadius: 'var(--evr-radius-circle)',
    };
  };

  const handleClearButtonMouseDown = () => {
    if (disabled || readOnly) return;
    onClear?.(true);
  };

  const handleClearButtonKeyDown = (e: React.KeyboardEvent) => {
    if (disabled || readOnly) return;
    if (e.key === 'Enter' || e.key === ' ') {
      onClear?.(false);
    }
    onClearButtonKeyDown?.(e);
  };

  const handleClearButtonKeyUp = (e: React.KeyboardEvent) => {
    if (disabled || readOnly) return;
    onClearButtonKeyUp?.(e);
  };
  const containerRef = useRef(null);
  const handleMouseEnter = (e: React.MouseEvent) => {
    if (disabled || readOnly) return;
    onMouseEnter?.(e);
  };
  const handleMouseOut = (e: React.MouseEvent) => {
    if (
      disabled ||
      readOnly ||
      (containerRef.current && (containerRef.current as HTMLElement).contains(e.relatedTarget as HTMLElement))
    )
      return;
    onMouseOut?.(e);
  };

  return (
    <div
      ref={containerRef}
      id={id}
      className={classnames(styles.evrSelectContainer, {
        [styles.disabled]: disabled,
        [styles.readOnly]: readOnly,
      })}
      onMouseEnter={handleMouseEnter}
      // eslint-disable-next-line jsx-a11y/mouse-events-have-key-events
      onMouseOut={handleMouseOut}
    >
      <div
        className={classnames(styles.innerWrapper, {
          [styles.readOnly]: readOnly,
        })}
      >
        {renderContent?.()}
      </div>
      {showClearButton && !disabled && !readOnly && (
        <div className={styles.clearButtonWrapper}>
          {/**
           * Should be using iconButton, but iconButton doesn't have the variant to support it yet
           * Discussion with Nick, the IconButton new variant will not be created at this point.
           * but if we are having to duplicate the same work again, should this be a iconButton
           * new iconButton variant is required but that's not yet ready for Design
           * */}
          <FocusRing target="firstchild" styleTransform={handleClearButtonFocusRingStyleTransform}>
            <div
              id={`${id}-clear-button`}
              ref={clearButtonRef}
              className={styles.clearButton}
              onClick={clickInsteadOfMouseDownOverride ? handleClearButtonMouseDown : undefined}
              onMouseDown={!clickInsteadOfMouseDownOverride ? handleClearButtonMouseDown : undefined}
              onKeyDown={handleClearButtonKeyDown}
              onKeyUp={handleClearButtonKeyUp}
              onBlur={onClearButtonBlur}
              onFocus={onClearButtonFocus}
              tabIndex={disabled || !clearButtonFocusable ? -1 : 0}
              role="button"
              aria-hidden={!clearButtonFocusable}
              aria-label={clearButtonAriaLabel}
              data-testid={testId ? `${testId}-clear-button` : undefined}
            >
              <Icon
                name="xSmall"
                fill={!disabled ? '--evr-content-primary-default' : '--evr-inactive-content'}
                id={`${id}-clear-button-icon`}
                testId={testId ? `${testId}-clear-button-icon` : undefined}
              />
            </div>
          </FocusRing>
          <div className={styles.divider}>
            <Divider vertical />
          </div>
        </div>
      )}
      {showChevronIcon && (
        <FormFieldContainerIconWrapper
          id={`${id}-chevron-icon`}
          iconName={chevronIconOpen ? 'chevronUpSmall' : 'chevronDownSmall'}
          disabled={disabled}
          readOnly={readOnly}
          position={'end'}
          verticalAlign={chevronIconVerticalAlign}
          onClick={onChevronIconClick}
          testId={testId ? `${testId}-icon-wrapper` : undefined}
        />
      )}
    </div>
  );
};

SelectContainer.displayName = 'SelectContainer';
