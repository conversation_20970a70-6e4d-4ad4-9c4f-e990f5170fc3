# Showcase Banner

- Start Date: 2025-04-23
- Figma link: https://www.figma.com/file/m4UAMZ6FrQovftLRPed38k/Showcase-Banner?node-id=2001-60599&m=dev

## Summary

The Showcase Banner is used to communicate message to the users with an illustration. Banner is usually located at the top of the page.

## Detailed Design

The Showcase Banner consists of banner header, illustration, close button, action button and banner description.
The structure of the Showcase Banner is very similar to Notification Banner and it will also use Container Base to build, but there are no status states. Banner will have only one background color: black.

Here are the summary of Showcase Banner templates:

- ShowcaseBannerHeader: Includes title(mandatory)
- ShowcaseBannerBody: Includes description(mandatory)
- ShowcaseBannerFooter: Includes footer(optional)
- ShowcaseBannerFooterActionButton: Includes call-to-action button. Only one button allowed(optional)

Illustration is mandatory for Showcase Banner while close button is optional. Those two won't be included in the template, but rather placed outside of template to be rendered just like how Notification Banner with rendering icon and close button.

🚨**NOTE:**
At the time of writing, <PERSON> is developing Spot Illustration component, but Showcase Banner requires Hero Illustration.
Until Hero Illustration component is developed, Showcase Banner will accept any form of illustration as a workaround.

## API

### IShowcaseBannerProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId?**: `string`
    Optional. Sets `data-testid` attribute.
3.  **illustration**: `React.ReactNode`
    Renders an illustration. Consumers must ensure it aligns in size with the other elements.
4.  **onCloseButtonClick?**: `() => void`
    Optional. Sets a callback function that is executed when banner is dismissed using the close button.
5.  **closeButtonAriaLabel?**: `string`
    Optional. Sets value of `aria-label` of close button.
6.  **children**: `React.ReactNode`
    Sets the content for the showcase banner.
7. **backgroundColor?**: `string`
    Optional. Sets the color of the banner background. Default color is black.
8. **size?**: `large | small | mobile`
    Optional. Sets the size of the banner. Default size is large.

### IShowcaseBannerHeaderProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId?**: `string`
    Optional. Sets **data-test-id** attribute.
3.  **children**: `React.ReactNode`
    Sets content of header.

### IShowcaseBannerBodyProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId?**: `string`
    Optional. Sets **data-test-id** attribute.
3.  **children**: `React.ReactNode`
    Sets content of banner description.

### IShowcaseBannerFooterProps

1.  **id**: `string`
    Sets `id` attribute.
2.  **testId?**: `string`
    Optional. Sets **data-test-id** attribute.
3.  **children?**: `React.ReactNode`
    Optional. Sets content of banner footer.

### IShowcaseBannerFooterActions

1.  **id**: `string`
    Sets `id` attribute.
2.  **action?**: `IButtonProps`
    Optional. Sets the props for the action button.
3.  **testId?**: `string`
    Optional. Sets **data-test-id** attribute.

## Usage

```typescript
<ShowcaseBanner
  id="showcase-banner-example"
  illustration={<HeroIllustration name="yoga" size="large"/>}
  onCloseButtonClick={() => {}}
  closeButtonAriaLabel="close banner"
>
  <ShowcaseBannerHeader id="showcase-banner-header-id">
    <h2 className="evrHeading4">Showcase Banner</h2>
  </ShowcaseBannerHeader>
  <ShowcaseBannerBody id="showcase-banner-body-id">
    <p>Hello World</p>
  </ShowcaseBannerBody>
  <ShowcaseBannerFooter>
    <ShowcaseBannerFooterActions
      id="showcase-banner-footer-id"
      action={{
        label="Footer Action Button",
        ariaLabel="footer action button",
        onClick=() => {},
      }}
    />
  </ShowcaseBannerFooter>
</ShowcaseBanner>
```

## Accessibility

- At the time of this arch doc creation, Design is still working on adding Showcase Banner accessibility.
- Unlike Notification Banner, accessibility should be more simpler. Perhaps role="region" with aria-label should be sufficient.
- Unlike the Notification Banner, the Showcase Banner should follow a top-to-bottom focus order similar to a Modal, since it doesn't convey urgency or alertness.

## Q/A

**Is banner always dismissible?**
No, banner is not always dismissible.

**What's the difference between Showcase Banner and Notification Banner?**
Biggest difference is that the Showcase Banner always comes with graphical illustration and will be always placed at the top of page. Another difference is that the Showcase Banner doesn't have status states.

**Will the hero illustration provided by Everest?**
Eventually yes. Showcase Banner uses Hero Illustration but at the moment Everest is developing Spot Illustration. Once Everest develops Hero Illustration then Showcase Banner will be updated to use Hero Illustration. Until Everest develops this component, the consumer is allowed to pass in any illustration. An illustration is required for Showcase Banner.

**How does component achieve responsiveness such as decreasing size of the font as screen becomes smaller when component is allowing custom template?**
As long as consumer use Everest tokens, responsiveness can be achieved as responsiveness are built in within the font. However, if consumers use custom token other than Everest then they are responsible for responsiveness. 

**Is background color for the banner always black?**
As per design, the background color for the banner will always be black(--evr-surfaces-primary-inverse-default) for now.

## Required PBIs

1. [Create Showcase Banner component and storybook doc](https://dayforce.atlassian.net/browse/WFM-84235)
1. [Create Test Case](https://dayforce.atlassian.net/browse/WFM-84236)
1. [Create Qualities](https://dayforce.atlassian.net/browse/WFM-84237)
1. [A11y](https://dayforce.atlassian.net/browse/WFM-84237)
1. [Push to Production](https://dayforce.atlassian.net/browse/WFM-87320)
1. [Add to Ref App](https://dayforce.atlassian.net/browse/WFM-87321)