import { useEffect } from 'react';
import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { SideMenu } from './side-menu';
import { Button } from '../button';
import { Icon } from '../icon';
import { AlphaBanner, ContributedBanner } from '../../../.storybook/docs/shared/component-status-banner.tsx';

<ContributedBanner dev="Mark <PERSON>" team="Platform Web Framework" />

<AlphaBanner />

export const scope = {
  Button,
  SideMenu,
  useEffect,
};

A side menu is a visual representation that organizes content into a hierarchy, making it easier for users to view large amounts of information. This component consists of nested heading levels with branch nodes that can be expanded or collapsed, allowing users to reveal or hide child nodes as needed. Additionally, the tree can include leaf nodes, which are the endpoints in the hierarchy and do not have any child nodes.

An interactive tree node functions like a folder; it can be selected or clicked to display the information contained within it.

## Variations

### Basic Side Menu

export const basicSideMenu = `() => {
    const [expandedIds, setExpandedIds] = useState(new Set(['basic-side-menu-1']));
    const onNodeToggle = (nodeContext) => {
        const newExpandedIds = new Set(expandedIds);
        newExpandedIds.has(nodeContext.id) ? newExpandedIds.delete(nodeContext.id) : newExpandedIds.add(nodeContext.id);

        setExpandedIds(newExpandedIds);
    };
    const complexTemplate = (nodeContext) => {
        return (
            <div className="evrBodyText">
              <div>{nodeContext.label}</div>
            </div>
        );
    }
    const data = [
        {
            id: 'basic-side-menu-1',
            label: 'North America',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            children: [
                {
                    id: 'basic-side-menu-2',
                    label: 'Canada',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
                {
                    id: 'basic-side-menu-3',
                    label: 'United States',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
                {
                    id: 'basic-side-menu-4',
                    label: 'Mexico',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
            ],
        },
        {
            id: 'basic-side-menu-5',
            label: 'Europe',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            children: [
                {
                    id: 'basic-side-menu-6',
                    label: 'Germany',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-7',
                    label: 'United Kingdom',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-8',
                    label: 'Spain',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-9',
                    label: 'France',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
            ],
        },
    ];

    return(
        <div style={{ height: "375px" }}>
            <SideMenu
                expandedIds={expandedIds}
                id="basic-side-menu"
                testId="basic-side-menu-test"
                data={data}
                height={"375px"}
                open
                onNodeToggle={onNodeToggle}
            />
        </div>
    );

}`;

<CodeExample scope={scope} code={basicSideMenu} />

### With Open/Close Control

export const withOpenCloseControl = `() => {
    const [open, setOpen] = useState(false);
    const [expandedIds, setExpandedIds] = useState(new Set(['basic-side-menu-1']));
    const onNodeToggle = (nodeContext) => {
        const newExpandedIds = new Set(expandedIds);
        newExpandedIds.has(nodeContext.id) ? newExpandedIds.delete(nodeContext.id) : newExpandedIds.add(nodeContext.id);

        setExpandedIds(newExpandedIds);
    };
    const complexTemplate = (nodeContext) => {
        return (
            <div className="evrBodyText">
              <div>{nodeContext.label}</div>
            </div>
        );
    }
    const data = [
        {
            id: 'basic-side-menu-1',
            label: 'North America',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            children: [
                {
                    id: 'basic-side-menu-2',
                    label: 'Canada',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
                {
                    id: 'basic-side-menu-3',
                    label: 'United States',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-4',
                    label: 'Mexico',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
            ],
        },
        {
            id: 'basic-side-menu-5',
            label: 'Europe',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            children: [
                {
                    id: 'basic-side-menu-6',
                    label: 'Germany',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-7',
                    label: 'United Kingdom',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-8',
                    label: 'Spain',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-9',
                    label: 'France',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
            ],
        },
    ];

    return(
        <div style={{display: 'flex', flexDirection: 'column', overflow: 'hidden', position: 'relative', width: '100%'}}>
            <div style={{ height: "375px" }}>
                <SideMenu
                    expandedIds={expandedIds}
                    id="basic-side-menu"
                    testId="basic-side-menu-test"
                    data={data}
                    height={"375px"}
                    open={open}
                    onNodeToggle={onNodeToggle}
                />
            </div>
            <div style={{paddingTop: '20px', paddingBottom: '20px', display: 'flex', justifyContent: 'space-around'}}>
                <Button onClick={()=>{setOpen(!open)}} label="Toggle Open/Close" />
            </div>
        </div>
    );

}`;

<CodeExample scope={scope} code={withOpenCloseControl} />

### With Custom Title Component

export const withCustomTitleControl = `() => {
    const [expandedIds, setExpandedIds] = useState(new Set(['basic-side-menu-1']));
    const onNodeToggle = (nodeContext) => {
        const newExpandedIds = new Set(expandedIds);
        newExpandedIds.has(nodeContext.id) ? newExpandedIds.delete(nodeContext.id) : newExpandedIds.add(nodeContext.id);

        setExpandedIds(newExpandedIds);
    };
    const complexTemplate = (nodeContext) => {
        return (
            <div className="evrBodyText">
              <div>{nodeContext.label}</div>
            </div>
        );
    }
    const TitleComponent = () => {
        return (
            <div style={{padding: '10px 6px'}}>
                <span className="evrBodyText evrBold">Custom Title</span>
            </div>
        );
    }
    const data = [
        {
            id: 'basic-side-menu-1',
            label: 'North America',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            children: [
                {
                    id: 'basic-side-menu-2',
                    label: 'Canada',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
                {
                    id: 'basic-side-menu-3',
                    label: 'United States',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
                {
                    id: 'basic-side-menu-4',
                    label: 'Mexico',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                },
            ],
        },
        {
            id: 'basic-side-menu-5',
            label: 'Europe',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            children: [
                {
                    id: 'basic-side-menu-6',
                    label: 'Germany',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-7',
                    label: 'United Kingdom',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-8',
                    label: 'Spain',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
                {
                    id: 'basic-side-menu-9',
                    label: 'France',
                    nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                },
            ],
        },
    ];

    return(
        <div style={{ height: "375px" }}>
            <SideMenu
                expandedIds={expandedIds}
                id="basic-side-menu"
                testId="basic-side-menu-test"
                title={<TitleComponent />}
                data={data}
                height={"375px"}
                open
                onNodeToggle={onNodeToggle}
            />
        </div>
    );

}`;

<CodeExample scope={scope} code={withCustomTitleControl} />

## How to Use

### Data

Each data point passed to the component can accept the following properties:

```typescript
export interface ISideMenuItem {
  id: string;
  testId?: string;
  children?: ISideMenuItem[];
  nodeRenderer: (node) => JSX.Element;
  [key: string]: unknown; // allows passing any additional props they may have/need
}
```

- `id` is the unique identifier for the node
- `testId` is a unique identifier to be used in unit tests
- `children` is an array of child nodes which render in the hierarchy
- `nodeRenderer` is a function that returns a custom template for the node

Other data points can be added ad hoc (ie, icon names) and then referenced in the `nodeRenderer` using `(node as ISideMenuNode).data`:

```javascript
const complexTemplate = (nodeContext as ISideMenuNode) => {
  return (
    <div className="evrBodyText">
      <div>{nodeContext.label}</div>
    </div>
  );
};
```

### Node Rendering

Each data point passed to the `SideMenu` should include its own `nodeRenderer` property. This property is a function that returns a functional React component, with the `nodeContext` passed as a prop. The `nodeContext` contains data specific to the node data which is passed in via the data prop. The `nodeContext` provides additional properties that can assist with node styling and behavior.

#### Expand/Collapse

The `expandedIds` prop is used to expand/collapse nodes. The `onNodeToggle` handler passed as a prop and will trigger when the state of a node is toggled.

## Accessibility

**React Arborist** ([GitHub Link](https://github.com/brimdata/react-arborist)) is accessible by default. One modification is made to the row's ARIA roles: the `aria-selected` property is excluded since the `SideMenu` does not support it.

- The component is fully keyboard-navigable.
- The default roles are set to `tree` and `treeitem`.
- Developers can add a `navigation` role to the parent element if needed. Additionally, they can specify custom accessibility roles for the nodes in the render context function.

### Keyboard Navigation

`SideMenu` supports the following keyboard navigation:

#### <kbd>Enter</kbd> / <kbd>Space</kbd>

**Function**: Activates the focused node.onNodeToggle<br />
**Behavior**:
If the focus is on an interactive element within the node, it activates the interactive element.
If the focus is on the node container, it activates the node and triggers `onNodeToggle`.

#### <kbd>Arrow Down</kbd>

**Function**: Moves the focus to the next node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling.
Focuses on the next node in the tree.

#### <kbd>Arrow Up</kbd>

**Function**: Moves the focus to the previous node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling.
Focuses on the previous node in the tree.

#### <kbd>Arrow Right</kbd>

**Function**: Expands the focused node or moves the focus to the next interactive element within the opened list<br />
**Behavior**:
If the node is a parent and expanded, it shifts focus to the next interactive element.
If the node is a parent and not expanded, it toggles the node to expand it.

#### <kbd>Arrow Left</kbd>

**Function**: Collapses the focused node or moves the focus to the parent element.<br />
**Behavior**:
If the node is a child, it shifts focus to the parent element.
If the node is a parent and expanded, it toggles the node to collapse it.

#### <kbd>Home</kbd>

**Function**: Moves the focus to the first node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling to the top.
Focuses on the first node in the tree.

#### <kbd>End</kbd>

**Function**: Moves the focus to the last node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling to the bottom.
Focuses on the last node in the tree
