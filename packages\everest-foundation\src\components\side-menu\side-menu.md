# Side Menu

## Summary

The SideMenu component will be used as a collapsible navigation tree with nodes and child controls. Each control will have unique render capabilities based on implementation. This component is being built with the global feature navigation in mind but should not be limited to that.

- Start Date: 2025-02-05
- Figma link: https://www.figma.com/design/Oid7J4sQABjNdm4jqg0KW2/Everest-Tiger-Team---UI?node-id=1745-137292&t=4wHzQGKQ95HSBqFZ-0

## Detailed Design

- React Arborist will be leveraged for this component.
- Side panel will be custom built for this component's needs. This may change in the future.
  - The side panel should be controlled externally.
- Each node should be customizable with it's render.
- The expand/collapse of each node will be handled externally.
- Component should be navigable with the keyboard based on w3 standards.
- `ButtonBase` was used for the node component.

## API

### SideMenu props

1. **id**: string  
   Sets **id** attribute on the html element.
2. **testId**: string  
   Sets **data-test-id** attribute on the html element.
3. **className**: string  
   Sets a classname on the parent element in order to include styles specific per implementation.
4. **data**: ISideMenuItem[]  
   Array of simple JS objects which build out the arborist tree. See [below](#ISideMenuItem) for more details on `ISideMenuItem`
5. **expandedIds**: Set&lt;string&gt;  
   Open/Close the branch nodes in the tree with a Set of strings that correspond with the `ISideMenuItem` ids.
6. **open**: boolean  
   **default**: `false`  
   Opens/Closes the drawer of the of the component
7. **height**: string  
   Set the height of the container element. The side menu will always expand to this height.
8. **rowHeight**: number  
   **default**: `40`  
   Set the `react-arborist` row height based on that component's props. React Arborist sets their row heights internally based on this value.
9. **onNodeFocus**: (node: `ISideMenuNode`) => void;  
   Handler that runs when each node's `focus` event is triggered. The argument passed is the `data` attribute of that node's `ISideMenuItem`
10. **onNodeToggle**: (node: `ISideMenuNode`) => void;  
    Handler that runs when each node's `toggle` event is triggered. The argument passed is the `data` attribute of that node's `ISideMenuItem`
11. **role**: string  
    Aria role applied to the container element for various use cases

<h3 id="ISideMenuItem">ISideMenuItem item</h3>

The `data` prop above accepts an array of the following to render it's tree.

1. **id**: string  
   The node id applied to each control. Can be used to identify each node individually.
2. **testId**: string  
   The test id applied to each branch control. Can be used to hook into unit tests.
3. **children**: ISideMenuItem[]  
   Each node can have possible sub nodes which can have the same properties as their parent. This is considered a "branch".
4. **nodeRenderer**: (node: ISideMenuRendererNode) => JSX.Element;  
   Function which returns a JSX Element to render custom controls and branches. Custom styles can be applied here.
5. **[key: string]**: unknown  
   Other properties can be included on each node and referenced in `nodeRenderer` as needed.

## Usage

The following demonstrates a basic example of the `SideMenu` component. Each supported prop is present with a simple implementation within a consuming component.

```jsx
const [expandedIds, setExpandedIds] = useState(new Set(['basic-side-menu-1']));
const [open, setOpen] = useState(false);
const complexTemplate = (nodeContext) => {
   return (
      <div className="evrBodyText">
         <div>{nodeContext.label}</div>
      </div>
   );
};
const data = [
   {
      id: 'basic-side-menu-1',
      label: 'North America',
      nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
      children: [
         {
            id: 'basic-side-menu-2',
            label: 'Canada',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
         },
         {
            id: 'basic-side-menu-3',
            label: 'United States',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            children: [
            {
               id: 'basic-side-menu-10',
               label: 'Pennsylvania',
               nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            {
               id: 'basic-side-menu-11',
               label: 'New Jersey',
               nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            {
               id: 'basic-side-menu-12',
               label: 'New York',
               nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            ],
         },
         {
            id: 'basic-side-menu-4',
            label: 'Mexico',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
         },
      ],
   },
   {
      id: 'basic-side-menu-5',
      label: 'Europe',
      nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
      children: [
         {
            id: 'basic-side-menu-6',
            label: 'Germany',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
         },
         {
            id: 'basic-side-menu-7',
            label: 'United Kingdom',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
         },
         {
            id: 'basic-side-menu-8',
            label: 'Spain',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
         },
         {
            id: 'basic-side-menu-9',
            label: 'France',
            nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
         },
      ],
   },
];

return (
   <>
      <SideMenu
         id="basic-side-menu"
         testId="basic-side-menu-test"
         className="df-menu-class-name-here"
         data={data}
         expandedIds={expandedIds}
         height="375px"
         rowHeight={50}
         open={open}
         onNodeFocus={(node) => {
            // Do something on focus.  For instance: set styles or class names
         }}
         onNodeToggle={(node) => {
            const nextSet = new Set(expandedIds);
            if (expandedIds.has(node.data.id)) {
               nextSet.delete(node.data.id);
               setExpandedIds(nextSet);
            } else {
               nextSet.add(node.data.id);
               setExpandedIds(nextSet);
            }
         }}
         role="navigation"
      />

      <Button onClick={()=>{
         setOpen(!open)
      }}>
   </>
)
```

## Prerequisite/Other Components

No sub menu components will be created for this component but it will make use of the Button component.

## Accessibility

React arborist comes with accessibility out of the box. This was refactored a bit to suit our needs.

### Keyboard navigation

`SideMenu` supports the following keyboard navigation:

#### Enter / Space

**Function**: Activates the focused node.onNodeToggle<br />
**Behavior**:
If the focus is on an interactive element within the node, it activates the interactive element.
If the focus is on the node container, it activates the node and triggers `onNodeToggle`.

#### Arrow Down

**Function**: Moves the focus to the next node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling.
Focuses on the next node in the tree.

#### Arrow Up

**Function**: Moves the focus to the previous node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling.
Focuses on the previous node in the tree.

#### Arrow Right

**Function**: Expands the focused node or moves the focus to the next interactive element within the opened list<br />
**Behavior**:
If the node is a parent and expanded, it shifts focus to the next interactive element.
If the node is a parent and not expanded, it toggles the node to expand it.

#### Arrow Left

**Function**: Collapses the focused node or moves the focus to the parent element.<br />
**Behavior**:
If the node is a child, it shifts focus to the parent element.
If the node is a parent and expanded, it toggles the node to collapse it.

#### Home

**Function**: Moves the focus to the first node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling to the top.
Focuses on the first node in the tree.

#### End

**Function**: Moves the focus to the last node in the tree.<br />
**Behavior**:
Stops the default behavior and prevents the page from scrolling to the bottom.
Focuses on the last node in the tree

### ARIA attributes

Custom:

- **container role**: devs can include a role prop to render their component specific to their needs. For example, when using the side menu as a navigation component, `role="navigation"` is appropriate.
- **aria-expanded**: whether the tree is expanded or collapsed.
- **aria-hidden**: whether the tree is hidden or displayed.

From React Arborist:

- **role**: `tree` for the treeview component.
- **role**: `treeitem` for each node
- **aria-level**: each node's hierarchical level
- **aria-expanded**: whether each node is expanded or not.

## Alternatives/Trade-Offs

We had considered using `Treeview` for this component, but that included a few features which made it difficult to refactor. It was decided that a wholly new component would be built instead.

- Treeview has it's chevron control positioned to the left. SideMenu needs it positioned on the right
- Treeview only uses the chevron to control the expanded/collapsed state of the node as a clickable element. SideMenu uses the entire node.

## Q&A

**Q: Why Everest?**  
We decided on an everest component as we felt this could be reused in other applications. A side menu component is fairly common in other design libraries.

**Q: Why React Arborist?**  
React arborist was already selected for use in another Everest component (Treeview) and satisfied all of the requirements for the SideMenu

**Q: Does clicking anywhere on the node expand/collapse it?**  
Yes, clicking anywhere on the node can expand/collapse it

**Q: Are there any other handlers accounted for?**  
Yes, we also account for the focus event.

**Q: What about other Arborist supported events?**  
React arborist can support a few other events but some of them are left out because our component does not support that feature (ie. you cannot "select" a node in the side menu). If there is an event we would like to support, we can add as needed.

**Q: Can this support custom controls?**  
Yes, you need to include the appropriate node renderer in the passed data set. With this context, you can adjust your control's display based on the node's data.

## Other Design Systems

- React Arborist: https://github.com/brimdata/react-arborist

## Required PBIs

[PWEB-15750](https://dayforce.atlassian.net/browse/PWEB-17570)

## Acceptance Criteria

From the story:

> AppShell navigation follows Dayforce legacy look for the nav rendering layer.
