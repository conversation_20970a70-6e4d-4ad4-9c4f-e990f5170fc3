@use '../../variables.scss';

.evrSideMenu {
  max-width: variables.$sideMenuMaxWidth;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  transition: transform linear 0.1s, box-shadow linear 0.1s;
  background: var(--evr-surfaces-secondary-default);
  overflow-y: auto;
  box-shadow: 0.5rem 0.5rem 0.5rem -0.5rem rgba(0, 0, 0, 0.16);

  &.evrSideMenuClosed {
    transform: translateX(-100%);
    box-shadow: none;
  }

  [role='tree'] {
    &:focus-visible {
      outline: none;
    }
  }
}

.evrSideMenuRow {
  display: flex;
  width: calc(100% - (var(--evr-spacing-2xs) - var(--evr-border-width-thick-px))) !important;
  padding-inline-start: calc(var(--evr-spacing-2xs) - var(--evr-border-width-thick-px));
  align-items: stretch;
  transition: background-color 0.3s;

  &:hover {
    background-color: var(--evr-surfaces-secondary-hovered);
  }
}

.evrSideMenuItems {
  &::-webkit-scrollbar {
    width: var(--evr-spacing-3xs);
    height: var(--evr-spacing-3xs);
  }

  &::-webkit-scrollbar-track {
    background: var(--evr-surfaces-primary-default);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--evr-surfaces-tertiary-hovered);
    max-height: var(--evr-spacing-3xs);
  }

  [role='treeitem'] {
    &:has(.parentActivateNode),
    &:has(.node)[aria-selected='true'] {
      border-radius: var(--evr-radius-2xs);
      background: var(--evr-interactive-primary-decorative);
    }

    &:focus,
    &:focus-visible {
      outline: var(--evr-interactive-primary-focus) solid var(--evr-border-width-thick-px);
      outline-offset: calc(-1 * var(--evr-border-width-thick-px));
      border-radius: var(--evr-radius-2xs);
      z-index: var(
        --evr-z-index-inner
      ); // z-index needed to account for the focus ring to take priority over the highlighted node
    }
  }
}

.evrSideMenuItem {
  display: flex;
  flex: 1 1 100%;
  cursor: pointer;
  background-color: transparent;
  align-items: center;
}

.evrSideMenuLeaf,
.evrSideMenuControl {
  display: flex;
  flex: 1 0 100%;
  justify-content: start;
  align-items: center;
}

.evrSideMenuControl {
  border: 0;
  margin: 0;
  padding: 0;
  background: none;
  cursor: pointer;
  justify-content: space-between;
  text-align: left;

  &:hover {
    background-color: transparent;
  }
}
