import { SideMenu } from './side-menu';
import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { Chromatic } from '../../../chromatic';
import { useState } from 'react';

<Meta
  title="Testing/Automation Test Cases/SideMenu"
  component={SideMenu}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    open: true,
    testId: 'side-menu-test-id',
    height: '400px',
    id: 'storybook-side-menu',
    data: (function () {
      const complexTemplate = (nodeContext) => {
        return (
          <div className="evrBodyText">
            <div>{nodeContext.label}</div>
          </div>
        );
      };

      return [
        {
          id: 'basic-side-menu-1',
          testId: 'basic-side-menu-1',
          label: 'North America',
          nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
          children: [
            {
              id: 'basic-side-menu-2',
              label: 'Canada',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            {
              id: 'basic-side-menu-3',
              label: 'United States',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            {
              id: 'basic-side-menu-4',
              label: 'Mexico',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
          ],
        },
        {
          id: 'basic-side-menu-5',
          label: 'Europe',
          nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
          children: [
            {
              id: 'basic-side-menu-6',
              label: 'Germany',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            {
              id: 'basic-side-menu-7',
              label: 'United Kingdom',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            {
              id: 'basic-side-menu-8',
              label: 'Spain',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
            {
              id: 'basic-side-menu-9',
              label: 'France',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
            },
          ],
        },
      ];
    })(),

}}
/>

export const SideMenuWrapper = ({expandedIds: expandedIdsProp, ...props}) => {
  const [expandedIds, setExpandedIds] = useState(expandedIdsProp ?? new Set([]));
  const onNodeToggle = (nodeContext) => {
    const newExpandedIds = new Set(expandedIds);
    newExpandedIds.has(nodeContext.id) ? newExpandedIds.delete(nodeContext.id) : newExpandedIds.add(nodeContext.id);

    setExpandedIds(newExpandedIds);

};

return <SideMenu {...props} expandedIds={expandedIds} onNodeToggle={onNodeToggle}/>;
};

<Canvas>
  <Story name="Opened">{(args) => <SideMenuWrapper {...args} />}</Story>
</Canvas>

<Canvas>
  <Story name="Closed">{(args) => <SideMenuWrapper {...args} open={false} />}</Story>
</Canvas>

<Canvas>
  <Story name="IDs are open">
    {(args) => <SideMenuWrapper {...args} expandedIds={new Set(['basic-side-menu-1'])} />}
  </Story>
</Canvas>

<Canvas>
  <Story name="With Title Component">
    {(args) => (
      <div style={{ height: '400px' }}>
        <SideMenu
          {...args}
          title={
            <div className="evrBodyText evrBold" style={{ padding: '10px 6px' }}>
              Side Menu Title
            </div>
          }
          expandedIds={new Set(['basic-side-menu-1', 'basic-side-menu-5'])}
        />
      </div>
    )}

  </Story>

</Canvas>
