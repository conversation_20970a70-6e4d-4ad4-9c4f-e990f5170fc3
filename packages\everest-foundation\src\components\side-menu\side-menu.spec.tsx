import React, { useState } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { mockResizeObserver } from '../../test-utils';
import { ISideMenuItem, ISideMenuProps, SideMenu } from '../side-menu';

const complexTemplate = (nodeData: ISideMenuItem) => {
  return <div data-testid={nodeData.testId}>{nodeData.label}</div>;
};

const SideMenuWithToggle = ({ onNodeToggle: onNodeToggleProp, ...props }: ISideMenuProps) => {
  const [expandedIds, setExpandedIds] = useState(new Set<string>([]));
  const onNodeToggle = (nodeContext: ISideMenuItem) => {
    const newExpandedIds = new Set(expandedIds);

    if (newExpandedIds.has(nodeContext.id)) {
      newExpandedIds.delete(nodeContext.id);
    } else {
      newExpandedIds.add(nodeContext.id);
    }

    onNodeToggleProp?.(nodeContext);
    setExpandedIds(newExpandedIds);
  };

  return <SideMenu expandedIds={expandedIds} onNodeToggle={onNodeToggle} {...props} />;
};

const data = [
  {
    id: 'test-id-1',
    testId: 'test-id-1',
    label: 'North America',
    nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
    children: [
      {
        id: 'test-id-1-1',
        testId: 'test-id-1-1',
        label: 'USA',
        nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
        children: [
          {
            id: 'test-id-1-1-1',
            testId: 'test-id-1-1-1',
            label: 'California',
            nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
          },
          {
            id: 'test-id-1-1-2',
            testId: 'test-id-1-1-2',
            label: 'Texas',
            nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
          },
          {
            id: 'test-id-1-1-3',
            testId: 'test-id-1-1-3',
            label: 'New York',
            nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
          },
        ],
      },
      {
        id: 'test-id-1-2',
        testId: 'test-id-1-2',
        label: 'Canada',
        nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
      },
      {
        id: 'test-id-1-3',
        testId: 'test-id-1-3',
        label: 'Mexico',
        nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
      },
    ],
  },
  {
    id: 'test-id-2',
    testId: 'test-id-2',
    label: 'Europe',
    nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
    children: [
      {
        id: 'test-id-2-1',
        testId: 'test-id-2-1',
        label: 'Germany',
        nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
      },
      {
        id: 'test-id-2-2',
        testId: 'test-id-2-2',
        label: 'France',
        nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
      },
      {
        id: 'test-id-2-3',
        testId: 'test-id-2-3',
        label: 'Italy',
        nodeRenderer: (nodeData: ISideMenuItem) => complexTemplate(nodeData),
      },
    ],
  },
];

const sideMenuId = 'df-side-menu-test';

describe('[SideMenu]', () => {
  /**
   * Fix TypeError: window.ResizeObserver is not a constructor issue
   * mock out the basic API (observe, unobserve, disconnect) and
   * use jest.fn() to return particular mock entries in the test.
   * https://github.com/maslianok/react-resize-detector/issues/145
   * https://github.com/que-etc/resize-observer-polyfill/issues/50
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { ResizeObserver } = window;

  beforeEach(() => {
    mockResizeObserver();
  });

  afterEach(() => {
    window.ResizeObserver = ResizeObserver;
    jest.restoreAllMocks();
  });

  it('should render successfully', () => {
    render(<SideMenu id={sideMenuId} data={data} open />);
    expect(screen.getByTestId('test-id-1')).toBeInTheDocument();
  });

  it('should open and close the component', () => {
    const { rerender } = render(<SideMenu id={sideMenuId} data={data} testId="side-menu" open />);

    const sideMenu = screen.getByTestId('side-menu');

    expect(sideMenu).toHaveAttribute('aria-hidden', 'false');

    rerender(<SideMenu id={sideMenuId} data={data} testId="side-menu" open={false} />);
    const rerenderedSideMenu = screen.getByTestId('side-menu');

    expect(rerenderedSideMenu).toHaveAttribute('aria-hidden', 'true');
  });

  it('should toggle each node', async () => {
    const nodeToggle = jest.fn();

    render(
      <SideMenu
        id={sideMenuId}
        data={data}
        onNodeToggle={(nodeData) => {
          nodeToggle(nodeData.id);
        }}
        open
      />
    );

    const button = screen.getByTestId('test-id-1');

    expect(button).toBeInTheDocument();

    await userEvent.click(button);

    expect(nodeToggle).toHaveBeenCalledWith('test-id-1');
  });

  it('should focus each node', async () => {
    const nodeFocus = jest.fn();

    render(
      <SideMenu
        id={sideMenuId}
        data={data}
        onNodeFocus={(nodeData) => {
          nodeFocus(nodeData.id);
        }}
        open
      />
    );

    const button = screen.getByTestId('test-id-1');

    expect(button).toBeInTheDocument();

    await userEvent.tab();

    expect(nodeFocus).toHaveBeenCalledWith('test-id-1');
  });

  it('should toggle the node with keyboard control', async () => {
    const nodeToggle = jest.fn();

    render(
      <SideMenu
        id={sideMenuId}
        data={data}
        onNodeToggle={(nodeData) => {
          nodeToggle(nodeData.label);
        }}
        open
      />
    );

    await userEvent.keyboard('{Enter}');

    expect(nodeToggle).toHaveBeenCalledWith('North America');
  });

  it('should toggle the unopened branch with the arrow keys', async () => {
    const nodeToggle = jest.fn();

    render(
      <SideMenu
        id={sideMenuId}
        data={data}
        onNodeToggle={(nodeData) => {
          nodeToggle(nodeData.label);
        }}
        open
      />
    );

    await userEvent.keyboard('{arrowright}');

    expect(nodeToggle).toHaveBeenCalledWith('North America');
    expect(nodeToggle).toHaveBeenCalledTimes(1);
  });

  it('should not open the branch with right arrow if it is already opened', async () => {
    const nodeToggle = jest.fn();

    render(
      <SideMenuWithToggle
        id={sideMenuId}
        data={data}
        onNodeToggle={(data) => {
          nodeToggle(data.label);
        }}
        open
      />
    );

    // // Open the branch
    await userEvent.keyboard('{enter}');
    // // Do not toggle if we are already open
    await userEvent.keyboard('{arrowright}');

    expect(nodeToggle).toHaveBeenCalledTimes(1);

    // Toggle when closing the branch
    await userEvent.keyboard('{arrowup}');
    await userEvent.keyboard('{arrowleft}');

    expect(nodeToggle).toHaveBeenCalledTimes(2);
  });

  it('should expand the supplied ids', () => {
    const expandedIds = new Set(['test-id-1']);

    render(<SideMenu id={sideMenuId} data={data} expandedIds={expandedIds} open />);

    const expandedRow = screen.getByRole('treeitem', { name: /North America/g });
    const collapsedRow = screen.queryByRole('treeitem', { name: /Europe/g });

    expect(expandedRow).toHaveAttribute('aria-expanded', 'true');
    expect(collapsedRow).toHaveAttribute('aria-expanded', 'false');
  });

  it('should be controllable via te expandedId prop', () => {
    const expandedIds = new Set(['test-id-1']);

    const { rerender } = render(<SideMenu id={sideMenuId} data={data} expandedIds={expandedIds} open />);

    const expandedRow = screen.getByRole('treeitem', { name: /North America/g });
    const collapsedRow = screen.queryByRole('treeitem', { name: /Europe/g });

    expect(expandedRow).toHaveAttribute('aria-expanded', 'true');
    expect(collapsedRow).toHaveAttribute('aria-expanded', 'false');

    rerender(<SideMenu id={sideMenuId} data={data} expandedIds={new Set(['test-id-1', 'test-id-2'])} open />);

    const newlyExpandedRow = screen.queryByRole('treeitem', { name: /Europe/g });

    expect(expandedRow).toHaveAttribute('aria-expanded', 'true');
    expect(newlyExpandedRow).toHaveAttribute('aria-expanded', 'true');
  });

  it('should focus on open and refocus on reopen', async () => {
    const expandedIds = new Set(['test-id-1']);

    const { rerender } = render(<SideMenu id={sideMenuId} data={data} expandedIds={expandedIds} open />);

    expect(
      screen.getByRole('treeitem', {
        name: /North America/g,
      })
    ).toHaveFocus();

    await userEvent.keyboard('{arrowdown}');

    expect(
      screen.getByRole('treeitem', {
        name: /USA/g,
      })
    ).toHaveFocus();

    await userEvent.click(document.body);
    rerender(<SideMenu id={sideMenuId} data={data} expandedIds={expandedIds} open={false} />);

    expect(
      screen.getByRole('treeitem', {
        name: /USA/g,
        hidden: true,
      })
    ).not.toHaveFocus();

    rerender(<SideMenu id={sideMenuId} data={data} expandedIds={expandedIds} open />);

    expect(
      screen.getByRole('treeitem', {
        name: /USA/g,
      })
    ).toHaveFocus();
  });

  it('should render a side-menu title component', () => {
    render(
      <SideMenu
        id={sideMenuId}
        data={data}
        open
        title={<div data-testid="side-menu-title">Side Menu Title</div>}
        testId="df-side-menu-test"
      />
    );

    expect(screen.getByTestId('side-menu-title')).toBeInTheDocument();
  });
});
