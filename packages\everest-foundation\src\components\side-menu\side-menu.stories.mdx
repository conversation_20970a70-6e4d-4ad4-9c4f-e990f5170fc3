import { useState } from 'react';
import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { SideMenu } from './side-menu';
import Examples from './side-menu.examples.mdx';

<Meta
  title="Everest Labs/Components/SideMenu"
  component={SideMenu}
  parameters={{
    status: {
      type: 'alpha',
      contributed: true,
    },
    controls: {
      sort: 'requiredFirst',
    },
  }}
  argTypes={{
    data: {
      control: '-',
      type: 'array',
    },
    open: {
      type: 'boolean',
      control: 'boolean',
      table: {
        defaultValue: { summary: false },
      },
    },
    title: {
      control: '-',
    },
    expandedIds: {
      control: 'array',
      type: 'array',
    },
    rowHeight: {
      control: '-',
    },
  }}
  args={{
    open: true,
    expandedIds: new Set(['basic-side-menu-1', 'basic-side-menu-5']),
    height: '375px',
  }}
/>

# SideMenu

<Examples />

## Live Demo

<Canvas>
  <Story name="SideMenu">
    {({expandedIds: extendedIdsProp, ...args}) => {
      const [expandedIds, setExpandedIds] = useState(extendedIdsProp);
      const complexTemplate = (nodeContext) => {
        const open = expandedIds.has(nodeContext.id);
        return (
          <div className={`evrBodyText${open ? ' evrBold' : ''}`} style={{ display: 'flex' }}>
            {nodeContext.label}
          </div>
        );
      };

      const onNodeToggle = (nodeContext) => {
        const newExpandedIds = new Set(expandedIds);
        newExpandedIds.has(nodeContext.id) ? newExpandedIds.delete(nodeContext.id) : newExpandedIds.add(nodeContext.id);

        setExpandedIds(newExpandedIds);
      };

      const data = [
          {
              id: 'basic-side-menu-1',
              label: 'North America',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
              children: [
                  {
                      id: 'basic-side-menu-2',
                      label: 'Canada',
                      nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                  },
                  {
                      id: 'basic-side-menu-3',
                      label: 'United States',
                      nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                      children: [
                          {
                              id: 'basic-side-menu-10',
                              label: 'California',
                              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                          },
                          {
                              id: 'basic-side-menu-11',
                              label: 'New York',
                              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                          },
                          {
                              id: 'basic-side-menu-12',
                              label: 'Texas',
                              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                          },
                      ]
                  },
                  {
                      id: 'basic-side-menu-4',
                      label: 'Mexico',
                      nodeRenderer: (nodeContext) => complexTemplate(nodeContext)
                  },
              ],
          },
          {
              id: 'basic-side-menu-5',
              label: 'Europe',
              nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
              children: [
                  {
                      id: 'basic-side-menu-6',
                      label: 'Germany',
                      nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                  },
                  {
                      id: 'basic-side-menu-7',
                      label: 'United Kingdom',
                      nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                  },
                  {
                      id: 'basic-side-menu-8',
                      label: 'Spain',
                      nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                  },
                  {
                      id: 'basic-side-menu-9',
                      label: 'France',
                      nodeRenderer: (nodeContext) => complexTemplate(nodeContext),
                  },
              ],
          },
      ];

      return (
        <div style={{height: `calc(${args.height} + 20px)`, position: 'relative', overflow: 'hidden'}}>
          <SideMenu {...args} onNodeToggle={onNodeToggle} data={data} expandedIds={expandedIds}></SideMenu>
        </div>
      )
    }}

  </Story>
</Canvas>

<ArgsTable story="SideMenu" />
