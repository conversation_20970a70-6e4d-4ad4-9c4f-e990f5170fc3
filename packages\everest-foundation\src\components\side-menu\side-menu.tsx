import React, { useCallback, useEffect, useRef, useState } from 'react';
import classnames from 'classnames';
import { NodeRendererProps, RowRendererProps, Tree, TreeApi, NodeApi } from 'react-arborist';

import { useResizeDetector } from '../../utils';
import { ButtonBase } from '../button-base';
import { Icon } from '../icon';

import styles from './side-menu.module.scss';

interface ISideMenuRendererNode extends ISideMenuItem {
  open: boolean;
}
export interface ISideMenuItem {
  id: string;
  testId?: string;
  children?: ISideMenuItem[];
  nodeRenderer: (node: ISideMenuRendererNode) => JSX.Element;
  [key: string]: unknown; // allows passing any additional props they may have/need
}

export interface ISideMenuTree extends TreeApi<ISideMenuItem> {
  onNodeToggle?: (node: ISideMenuItem) => void;
  onNodeFocus?: (node: ISideMenuItem) => void;
}

export interface ISideMenuProps {
  /** Can be passed to the node to set your own specific styles. */
  className?: string;
  /** Build out the node tree. */
  data: ISideMenuItem[];
  /** Tells the component which branches should be open. */
  expandedIds?: Set<string>;
  /** The title component that renders above the tree. */
  title?: React.ReactNode;
  /** Sets the inline height of the entire tree. */
  height?: string;
  /** Sets the id attribute of the container element. */
  id: string;
  /** Determines if the side menus is in it's open state or not. */
  open?: boolean;
  /**
   * Sets the height of each tree node.
   * @default 40
   */
  rowHeight?: number;
  /** Can be used for unit tests that consume the SideMenu. */
  testId?: string;
  /** Runs when any node gains focus. */
  onNodeFocus?: (node: ISideMenuItem) => void;
  /** Runs when any branch toggles. */
  onNodeToggle?: (node: ISideMenuItem) => void;
  /** The aria role of the containing element. */
  role?: string;
}

interface SideMenuRowProps<T> extends RowRendererProps<T> {
  setMaintainFocusNode: (node: NodeApi<ISideMenuItem> | undefined) => void;
}

const SideMenuRow = ({ node, children, innerRef, attrs, setMaintainFocusNode }: SideMenuRowProps<ISideMenuItem>) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { ['aria-selected']: ariaSelected, ...rowAttrs } = attrs;
  const { onNodeFocus, onNodeToggle } = node.tree as ISideMenuTree;

  return (
    <div
      {...rowAttrs}
      aria-expanded={node.isOpen}
      className={classnames(styles.evrSideMenuRow)}
      onFocus={() => {
        onNodeFocus?.(node.data);
      }}
      onClick={() => {
        setMaintainFocusNode(node);
      }}
      onKeyDown={
        !node.isLeaf
          ? (e: React.KeyboardEvent) => {
              if (
                e.key === 'Enter' ||
                (!node.isOpen && e.key === 'ArrowRight') ||
                (node.isOpen && e.key === 'ArrowLeft')
              ) {
                e.preventDefault();
                onNodeToggle?.(node.data);
                setMaintainFocusNode(node);
              }

              if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                setMaintainFocusNode(undefined);
              }
            }
          : undefined
      }
      ref={innerRef}
    >
      {children}
    </div>
  );
};

export const SideMenu = (props: ISideMenuProps): JSX.Element => {
  const {
    className,
    data = [],
    expandedIds,
    title,
    height,
    id,
    open,
    onNodeFocus,
    onNodeToggle,
    role,
    rowHeight = 40,
    testId,
  } = props;
  const parentRef = useRef<HTMLDivElement>(null);
  const treeRef = useRef<ISideMenuTree>();
  const lastFocusedRef = useRef<string | null>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const [maintainFocusNode, setMaintainFocusNode] = useState<NodeApi<ISideMenuItem> | undefined>(undefined);
  const [treeHeight, setTreeHeight] = useState<number | undefined>(undefined);

  const { height: parentHeight } = useResizeDetector({
    handleHeight: true,
    handleWidth: false,
    observerOptions: { box: 'border-box' },
    targetRef: parentRef,
  });

  const { height: titleHeight } = useResizeDetector({
    handleHeight: true,
    handleWidth: false,
    observerOptions: { box: 'border-box' },
    targetRef: titleRef,
  });

  useEffect(() => {
    if (titleHeight && parentHeight) {
      const newHeight = parentHeight >= titleHeight ? parentHeight - titleHeight : 0;
      setTreeHeight(newHeight);
    } else {
      setTreeHeight(parentHeight);
    }
  }, [titleHeight, parentHeight]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const nodeItem = ({ node, style }: NodeRendererProps<any>) => {
    const branchControl = (
      <ButtonBase
        className={classnames(styles.evrSideMenuControl, node.isOpen && styles.evrSideMenuControlOpen)}
        id={`${node.data.id}-icon-button`}
        testId={node.data.testId ? `${node.data.testId}-icon-button` : undefined}
        onClick={() => {
          onNodeToggle?.(node.data);
        }}
        disabled={!open}
        // disable focus ring styles as focus styles are handled in the SideMenu CSS
        focusRingStyleTransform={() => ({})}
      >
        {node.data.nodeRenderer && node.data.nodeRenderer(node.data)}
        <Icon
          fill="--evr-content-primary-highemp"
          name={node.isOpen ? 'chevronUpSmall' : 'chevronDownSmall'}
          size="md"
        />
      </ButtonBase>
    );

    const leafControl = (
      <div
        className={classnames(styles.evrSideMenuLeaf)}
        data-testid={node.data.testId ? `${node.data.testId}-leaf-control` : undefined}
      >
        {node.data.nodeRenderer && node.data.nodeRenderer(node.data)}
      </div>
    );

    return (
      <div className={classnames(styles.evrSideMenuItem)} id={`${node.data.id}-node-item`} style={{ ...style }}>
        {node.isLeaf ? leafControl : branchControl}
      </div>
    );
  };

  const renderRow = useCallback(
    (props) => <SideMenuRow setMaintainFocusNode={setMaintainFocusNode} {...props} />,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [maintainFocusNode]
  );

  // Bind handlers to the TreeRef so they can be used in the SideMenuRow component
  useEffect(() => {
    if (treeRef.current) {
      treeRef.current.onNodeFocus = (...args) => {
        onNodeFocus?.(...args);
        lastFocusedRef.current = args[0].id;
      };
      treeRef.current.onNodeToggle = onNodeToggle;
    }
  }, [onNodeFocus, onNodeToggle]);

  useEffect(() => {
    if (expandedIds) {
      treeRef.current?.closeAll();

      expandedIds.forEach((id) => {
        treeRef.current?.open(id);
      });
    }
  }, [expandedIds]);

  // For accessibility, we need to set the tree to be focusable when it's open and not when it's closed
  useEffect(() => {
    const tree = parentRef.current?.querySelectorAll('[role="tree"]')[0];
    const tabIndexValue = open ? '0' : '-1';
    const target = lastFocusedRef.current || data?.[0]?.id;

    tree?.setAttribute('tabindex', tabIndexValue);

    if (open && target) {
      treeRef.current?.focus(target, { scroll: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  useEffect(() => {
    // focus the last focused element when the tree is re-rendered, this happens b/c the props change when a checkbox is selected or a node is toggled/activated
    if (maintainFocusNode) {
      treeRef.current?.focus(maintainFocusNode, { scroll: true });
    }
  });

  return (
    <div
      className={classnames(styles.evrSideMenu, !open && styles.evrSideMenuClosed, className)}
      data-testid={testId}
      id={id}
      style={{ height }}
      ref={parentRef}
      role={role}
      aria-hidden={!open}
    >
      {title && <div ref={titleRef}>{title}</div>}
      <Tree
        className={styles.evrSideMenuItems}
        data={data}
        height={treeHeight}
        renderRow={renderRow}
        rowHeight={rowHeight}
        openByDefault={false}
        ref={treeRef}
        width="100%"
      >
        {nodeItem}
      </Tree>
    </div>
  );
};

SideMenu.displayName = 'SideMenu';
