import React, { forwardRef, PropsWithChildren, useContext } from 'react';
import classnames from 'classnames';

import { IconButton } from '../icon-button';
import { SideNavContext } from './side-nav-context';

import styles from './side-nav.module.scss';

export interface ISideNavActions {
  id: string;
  testId?: string;
  ariaLabel: string;
  onToggle?: () => void;
}

export const SideNavActions = forwardRef<HTMLDivElement, PropsWithChildren<ISideNavActions>>(
  (props, ref): JSX.Element => {
    const { id, testId, ariaLabel, children, onToggle } = props;

    const { expanded } = useContext(SideNavContext);

    return (
      <div id={id} data-testid={testId} ref={ref} className={styles.evrSideNavActions}>
        {expanded && children}
        <div
          className={classnames({
            [styles.evrShowCollapsedIcon]: expanded,
          })}
        >
          <IconButton
            id={`${id}-expand-collapse`}
            testId={`${testId}-expand-collapse`}
            iconName="contentExpandCollapse"
            variant="tertiaryNeutral"
            onClick={onToggle}
            ariaLabel={ariaLabel}
            size="medium"
          />
        </div>
      </div>
    );
  }
);

SideNavActions.displayName = 'SideNavActions';
