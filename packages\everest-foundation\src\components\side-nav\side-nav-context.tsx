import React from 'react';

export interface ISideNavContext {
  expanded: boolean;
}

export const defaultSideNavContext: ISideNavContext = {
  expanded: false,
};

// esLint doesn't like this context object capitalized -- ignoring rule
// eslint-disable-next-line @typescript-eslint/naming-convention
export const SideNavContext = React.createContext<ISideNavContext>(defaultSideNavContext);

if (process.env.NODE_ENV !== 'production') {
  SideNavContext.displayName = 'SideNavContext';
}
