import React, { forwardRef, PropsWithChildren } from 'react';

export interface ISideNavHeader {
  id: string;
  testId?: string;
}

export const SideNavHeader = forwardRef<HTMLDivElement, PropsWithChildren<ISideNavHeader>>(
  (props, ref): JSX.Element => {
    const { id, testId, children } = props;

    return (
      <div id={id} data-testid={testId} ref={ref}>
        {children}
      </div>
    );
  }
);

SideNavHeader.displayName = 'SideNavHeader';
