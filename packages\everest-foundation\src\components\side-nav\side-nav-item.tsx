import React, { useContext } from 'react';
import { forwardRef, PropsWithChildren } from 'react';
import { FocusRing } from '@ceridianhcm/everest-cdk';
import classnames from 'classnames';

import { Icon, TIconName } from '../icon';
import { ISideNavDataItem as ISideNavItem } from './side-nav';
import { SideNavContext } from './side-nav-context';

import styles from './side-nav.module.scss';

export const SideNavItem = forwardRef<HTMLLIElement, PropsWithChildren<ISideNavItem>>((props, ref): JSX.Element => {
  const {
    id,
    testId,
    iconName,
    label,
    href,
    level = 0,
    childItems = [],
    expanded = false,
    active = false,
    onClick,
  } = props;

  const { expanded: drawerExpanded } = useContext(SideNavContext);

  // Values we will use to calculate the left padding and width of the side nav item, which changes based on the level of the item
  const evrSideNavItemLeftPaddingDefault = 'var(--evr-spacing-sm)'; // 1 rem
  const evrSideNavItemWidthDefault = 'var(--evr-spacing-lg)'; // 2 rem
  const evrSideNavItemChildrenMultiplier = 'var(--evr-spacing-xl)'; // 2.5rem

  const calculatePadding = () =>
    `calc(${evrSideNavItemLeftPaddingDefault} + (${evrSideNavItemChildrenMultiplier} * ${level}))`;
  const calculateWidth = () =>
    `calc(100% - (${evrSideNavItemWidthDefault} + (${evrSideNavItemChildrenMultiplier} * ${level})))`;

  const renderIcon = (name: TIconName) => (
    <Icon name={name} fill={active ? '--evr-interactive-primary-default' : '--evr-content-primary-highemp'} size="md" />
  );

  const handleSummaryFocusRingStyleTransform = (focusRingStyles: React.CSSProperties) => {
    return {
      ...focusRingStyles,
      borderRadius: 'var(--evr-radius-sm)',
    };
  };

  const renderAnchorElement = () => {
    return (
      <a
        id={`${id}-anchor`}
        data-testid={`${testId}-anchor`}
        href={href ?? '#'} // href is required for the link to be focusable, so we set it to # if not provided
        style={
          drawerExpanded
            ? {
                paddingLeft: calculatePadding(),
                width: calculateWidth(),
              }
            : undefined
        }
        onClick={(e) => {
          !href && e.preventDefault();
          onClick?.(id);
        }}
        className={classnames(styles.evrSideNav, styles.evrSideNavExpanded, {
          [styles.evrSideNavActive]: active,
        })}
      >
        {iconName && renderIcon(iconName as TIconName)}
        <span>{drawerExpanded && label}</span>
      </a>
    );
  };

  const renderLinkButton = () => {
    return (
      <FocusRing canFocusFromMouse styleTransform={handleSummaryFocusRingStyleTransform}>
        <button
          id={`${id}-button`}
          data-testid={`${testId}-button`}
          type="button"
          onClick={onClick ? () => onClick(id) : undefined}
          aria-expanded={expanded}
          className={classnames(styles.evrSideNav, styles.evrSideNavExpanded, {
            [styles.evrSideNavActive]: active,
            [styles.evrSideNavActiveParentExpanded]: expanded && active,
          })}
          style={
            drawerExpanded
              ? {
                  paddingLeft: calculatePadding(),
                  width: calculateWidth(),
                }
              : undefined
          }
        >
          <div className={classnames(styles.evrSideNav, styles.evrSideNavExpanded, styles.evrSideNavButton)}>
            {iconName && renderIcon(iconName as TIconName)}
            <span>{drawerExpanded && label}</span>
          </div>
          {expanded ? renderIcon('chevronUpSmall') : renderIcon('chevronDownSmall')}
        </button>
      </FocusRing>
    );
  };

  const renderExpandedItem = () => {
    return (
      <li ref={ref} id={id} data-testid={testId}>
        {childItems.length === 0 && (
          <FocusRing canFocusFromMouse styleTransform={handleSummaryFocusRingStyleTransform}>
            {renderAnchorElement()}
          </FocusRing>
        )}
        {childItems.length > 0 && (
          <>
            {renderLinkButton()}
            {expanded && (
              <ul>
                {childItems.map((child) => {
                  return <SideNavItem key={child.id} {...child} level={level + 1} />;
                })}
              </ul>
            )}
          </>
        )}
      </li>
    );
  };

  const renderCollapsedItem = () => {
    // TODO: in a11y ticket, we will add the Tooltips to the collapsed items
    return (
      <FocusRing canFocusFromMouse styleTransform={handleSummaryFocusRingStyleTransform}>
        <li
          ref={ref}
          id={id}
          data-testid={testId}
          className={classnames(styles.evrSideNav, styles.evrSideNavCollapsed, {
            [styles.evrSideNavActive]: active,
          })}
        >
          {renderAnchorElement()}
        </li>
      </FocusRing>
    );
  };

  return drawerExpanded ? renderExpandedItem() : renderCollapsedItem();
});

SideNavItem.displayName = 'SideNavItem';
