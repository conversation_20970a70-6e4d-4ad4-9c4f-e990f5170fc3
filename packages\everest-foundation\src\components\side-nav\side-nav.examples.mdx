import { CodeExample } from '../../../.storybook/doc-blocks/example/example';
import { useEverestContext } from '../everest-provider';
import { Drawer } from '../drawer';
import { SideNav, SideNavActions } from '.';

export const scope = {
  SideNav,
  SideNavActions,
  Drawer,
  useEverestContext,
};

### Side Nav example with breakpoint integration

For screen sizes smaller than `lg`, the side nav will be displayed as a Side Panel in expanded mode.

export const sideNavWithBreakpointIntegration = `() => {
      // create a state to manage the expanded items in the side nav
      const [expandedItems, setExpandedItems] = React.useState([]);
      const handleExpand = (id) => {
        setExpandedItems((prev) => {
            if (prev.includes(id)) {
              return prev.filter((item) => item !== id);
            } else {
             return [...prev, id];
            }
        });
      };
      const data = [
        {
            id: 'personal',
            iconName: 'person',
            href: 'javascript:void(0);',
            label: 'Personal',
            active: true,
            expanded: expandedItems.includes('personal'),
            onClick: (id) => handleExpand(id),
            childItems: [
              {
                id: 'about-me',
                label: 'About Me',
                href: 'javascript:void(0);',
                active: true,
                onClick: () => console.log('about-me'),
              },
              {
                id: 'letters',
                label: 'Letters',
                href: 'javascript:void(0);',
                onClick: () => console.log('letters'),
              },
            ],
        },
        {
          id: 'career',
          iconName: 'briefcase',
          label: 'Career',
          href: 'javascript:void(0);',
          onClick: () => console.log('career'),
        },
        {
          id: 'forms',
          iconName: 'form',
          label: 'Forms',
          href: 'javascript:void(0);',
          onClick: () => console.log('forms'),
        },
        {
          id: 'settings',
          iconName: 'settings',
          label: 'Settings',
          href: 'javascript:void(0);',
          onClick: () => console.log('settings'),
        },
      ];

      // create a expanded state to pass to the side nav and drawer
      const [expanded, setExpanded] = React.useState(false);
      const handleToggle = () => {
        setExpanded(!expanded);
      };

      const { breakpoint } = useEverestContext();
      const isSidePanel = breakpoint === 'xs' || breakpoint === 'sm' || breakpoint === 'md';

      return (
        <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%'}}>
        <div className="evrBodyText">Current breakpoint: {breakpoint}</div>
        <div style={{ height: '100%', display: 'flex', justifyContent: 'flex-start', width: '100%' }}>
          <Drawer expanded={expanded} persistent={!isSidePanel}>
            <SideNav id="side-nav" data={data} expanded={expanded} >
              <SideNavActions onToggle={handleToggle}>
                <div className='evrBodyText2'>Profile</div>
              </SideNavActions>
            </SideNav>
          </Drawer>
        </div>
        </div>
      );
    }`

<CodeExample scope={scope} code={sideNavWithBreakpointIntegration} />
