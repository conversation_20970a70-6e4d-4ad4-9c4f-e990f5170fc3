# Side Nav

## Summary

Research and document implementations for the Everest Side Nav.

- Start Date: 2024-03-27
- Figma link: https://www.figma.com/design/yzd3fHCowfWDL4iTBAoJNv/Component-Documentation-for-Designers?node-id=16207-35499&p=f&m=dev

The side nav, also known as the sub-nav, is anchored to the left to divide related content into separate pages where the content within the pages themselves may possibly be further divided into subsections.

## Detailed Design

The Side Nav is used to provide navigation within a specific product area. It helps users access related pages or features without leaving the current context. This component is strictly for product-level navigation and should not be used for global navigation or embedded anchor navigation within a page.

## API

### ISideNavDataItem alias of INavDataItem

1. **id**: `string`  
   A unique identifier for the side nav item.
2. **testId**: `string`  
   Optional. Sets **data-testid** attribute on the html element.
3. **iconName**: `string`  
   Specifies the name of the icon to display alongside the side nav item.
4. **label**: `string`  
   The text label for the side nav item.
5. **href**: `string`  
   Optional. URL to navigate to when the side nav item is clicked. If href is not defined then we will apply a # string and have the prevent default functionality
6. **children**: `ISideNavDataItem[]`  
   Optional. A list of child items for the side nav item. Each child item should conform to the `INavItemData` interface. Defaults to an empty array.
7. **expanded**: `boolean`  
   Optional. Indicates whether the side nav item is expanded to show its children. This prop only works if the side nav item has children. Defaults to `false`.
8. **active**: `boolean`  
   Optional. Indicates whether the side nav item is currently active. Defaults to `false`.
9. **onClick**: `() => void`  
   Optional. Callback function triggered when the side nav item is clicked.
10. **notificationCount**: `number`  
    Optional. Displays a tag indicating the number of notifications for the side nav item. Ideally this will part of phase 2 since we do not have this color in the tag currently

### ISideNav

1. **id**: `string`  
   `id` for the side nav.
2. **testId**: `string`  
   Optional. Sets **data-testid** attribute on the html element.
3. **expanded**: `boolean`  
   Optional. Determines whether the side nav is expanded or collapsed. Defaults to `false`.
4. **data**: `ISideNavDataItem[]`  
   Optional. An array of side nav items that define the structure and content of the side navigation. Each item in the array should conform to the `ISideNavDataItem` interface.
5. **ariaLabel**: `string`  
   Provides an accessible label for the side nav to improve screen reader support.
6. **fullscreen**: `boolean`  
   Optional. Enables fullscreen mode for the side nav. This will be part of phase 2

### IDrawer

1. **id**: `string`  
   A unique identifier for the drawer.
2. **testId**: `string`  
   Optional. Sets **data-testid** attribute on the HTML element for testing purposes.
3. **expanded**: `boolean`  
   Indicates whether the drawer is expanded or collapsed.
4. **persistent**: `boolean`  
   Optional. Indicates whether the side nav is a push vs side panel. Defaults to `true`.

### ISideNavActions

1. **id**: `string`  
   A unique identifier for the sub-nav actions.
2. **testId**: `string`  
   Optional. Sets **data-testid** attribute on the HTML element for testing purposes.
3. **ariaLabel**: `string`  
   Provides an accessible label for the icon button that collapses and expands the drawer component, improving screen reader support.
4. **onToggle**: `() => void`  
   Optional. Callback function triggered when the icon button responsible for expanding and collapsing the side nav is clicked. This allows external components to hook into the toggle behavior.

### ISideNavHeader

1. **id**: `string`  
   A unique identifier for the sub-nav header.
2. **testId**: `string`  
   Optional. Sets **data-testid** attribute on the HTML element for testing purposes.

## User Implementation

```jsx
const [expanded, setExpanded] = React.useState(true);

const handleToggle = () => {
  setExpanded((prevExpanded) => !prevExpanded);
};

const data = () => {
  return ([
    {
      id="dashboard",
      iconName="placeHolder",
      label="Dashboard",
      href="/Dashboard",
      onClick={() => console.log('clicked')}
    },
    {
      id="preview",
      iconName="placeHolder",
      label="Preview",
      href="/Preview",
      active={true},
      onClick={() => console.log('clicked')}
    }
    {
      id="hcm",
      iconName="placeHolder",
      label="HCM",
      expanded={false}
      children= [
        {
          id: "employees",
          iconName: "placeHolder",
          label: "Employees",
          href: "/Employees",
          onClick: () => console.log('clicked'),
        },
        {
          id: "timesheets",
          iconName: "placeHolder",
          label: "TimeSheets",
          href: "/Timesheets",
          onClick: () => console.log('clicked'),
        }
      ]
    }
  ]);
}

return (
  <SideNav
    id="side-nav"
    ariaLabel="Side Navigation"
    expanded={expanded}
    data={data}
    onToggle={handleToggle}
  >
    <SideNavActions>
       <div style={{ height: 'min-content' }}>
        // ToggleTip would be added here as well
        <IconButton
          id="trigger-toggletip-btn"
          iconName="information"
          ariaLabel="Information"
          onClick={onClick}
          onKeyDown={onKeyDown}
          ref={triggerRef}
          ariaExpanded={open}
          ariaOwns="default-toggletip"
          ariaHasPopup="dialog"
          variant="tertiaryNeutral"
        />
        // expanded icon button
        <IconButton
          id="trigger-toggletip-btn"
          iconName={expanded ? 'ContentExpandCollapse' : 'ArrowLeftSmall'}
          ariaLabel="ContentExpandCollapse"
          onClick={onClick}
          onKeyDown={onKeyDown}
          ref={triggerRef}
          ariaExpanded={open}
          ariaOwns="default-toggletip"
          ariaHasPopup="dialog"
          variant="tertiaryNeutral"
        />
      </div>
    </SideNavActions>
    <SideNavHeader>
      <div>US STORES</div>
    </SideNavHeader>
  </SideNav>
);
```

## Dev Implementation

We will not be using Treeview for the side nav, as those are better suited for folders, organizations, and so on. Also, the following items are represented as a list of links rather than a tree of tree items.

For quicker development we will be create a NavItem which will be a new component the encompass the behavior of the nav item.

In the scenario where an item needs to have children, we will use the children prop to indicate that a button with a `<ul>` element should be created to display the children

In the scenario where an item does not have children, we will be displaying the side nav item with an `a` tag

### Example HTML Structure for Side Nav when expanded

```html
<nav aria-label="Side Navigation">
  <ul>
    <li>
      <a href="/section1">Section 1</a>
    </li>
    <li>
      <button aria-expanded="true">Section 2</button>
      <ul>
        <li>
          <a href="/section2/subsection1">Subsection 1</a>
        </li>
        <li>
          <a href="/section2/subsection2">Subsection 2</a>
        </li>
      </ul>
    </li>
    <li>
      <a href="/section3">Section 3</a>
    </li>
  </ul>
</nav>
```

For breakpoints below 1280px:  
We will be switching from a push panel to an overlay panel (SidePanel). This is to ensure that at smaller screen sizes, the side nav is still accessible to the users. We will be utilizing the size prop to handle this change

### Example Structure for Side Nav for breakpoints below 1280px

```jsx
<SidePanel>
  <SidePanelBody>
    <ul>
      <li>
        <a href="/section1">Section 1</a>
      </li>
      <li>
        <button aria-expanded="true">Section 2</button>
        <ul>
          <li>
            <a href="/section2/subsection1">Subsection 1</a>
          </li>
          <li>
            <a href="/section2/subsection2">Subsection 2</a>
          </li>
        </ul>
      </li>
      <li>
        <a href="/section3">Section 3</a>
      </li>
    </ul>
  </SidePanelBody>
</SidePanel>
```

### Example HTML Structure for Side Nav when collapsed

```jsx
<nav>
  <ul>
    <li>
      <a href="/section1">
        <Tooltip title={label}>
          <Icon name="sparkle" />
        </Tooltip>
      </a>
    </li>
    <li>
      <a href="/section2">
        <Tooltip title={label}>
          <Icon name="folder" />
        </Tooltip>
      </a>
    </li>
    <li>
      <a href="/section3">
        <Tooltip title={label}>
          <Icon name="chart" />
        </Tooltip>
      </a>
    </li>
    <li>
      <a href="/section4">
        <Tooltip title={label}>
          <Icon name="settings" />
        </Tooltip>
      </a>
    </li>
  </ul>
</nav>
```

When the side nav is in collapsed state, we will pass the `expanded` prop from `SideNav` using react context to the `NavItem`. In collapsed state we remove the label text and move that to the Tooltip to be utilized. We will be using the **NavItem** to showcase the navigation items rather than `IconButton` as per the a11y requirements.

- When the Side Navigation is collapsed:
  - Labels for navigation items are hidden to save space.
  - Tooltips are used to describe the icons, ensuring accessibility and providing additional context.
  - Hovering over an icon displays a Tooltip with the corresponding label, helping users understand the purpose of each navigation item without expanding the menu.

**Note: When the side nav is in a collapsed state, we will only showcase the parent nodes at the highest level as per the design decision.**

## Accessibility

### Keyboard Navigation

Users should be able to navigate through the list of links using the `Tab` key rather than arrow keys. Each link or button in the side nav should be focusable, allowing users to tab through the navigation items sequentially. This ensures consistency with standard web navigation patterns and improves accessibility for keyboard users.

### ARIA Attributes

- Parent `NavItem` will have `aria-expanded` to describe when the item has been expanded.
- All interactive elements, such as links and buttons, that are not visible should include `aria-label` or `aria-labelledby` attributes to provide descriptive labels for screen readers. This ensures that users with assistive technologies can understand the purpose of each navigation item.
- For notifications (see Future Considerations sections), we will be using `aria-describedby` to ensure that they are connected to the right `NavItem`.
- We will be using Tooltips to showcase what the icons describe when in a collapsed state.

### Future Considerations

- Handling of notification tags for side nav items will be addressed in **Phase 2** of the implementation.
- Handling of mobile screens and it sizing will be done in **Phase 2** of the implementation.

## Q&A

### Questions

1. **What is the expected behavior for nested navigation items when the parent item is collapsed?**  
   Child items should be completely hidden, and the user should be able to click on the parent element.

2. **Are there any disabled or read-only states for each item?**  
   None are present in the current designs.

3. **How will users know what the icons describe in the collapsed state?**  
   We will be using tooltips to provide descriptions for the icons when the side nav is collapsed.

4. **Should the collapsed state use buttons or links?**  
   In the collapsed state, navigation items should remain as links containing icons. This ensures accessibility and consistency with standard web practices. Each link will include an `aria-label` to describe its purpose, and tooltips will provide additional context when users hover over the icons.

## Other Design Systems

1. Dell design system: https://www.delldesignsystem.com/components/side-navigation/?tab=Overview
   Similar design and same layout as we need. They use `ItemGroup` and `Item` as components rather than just one component.

2. Carbon design system: https://react.carbondesignsystem.com/?path=/story/components-ui-shell-sidenav--fixed-side-nav  
   Same layout but does not have a collapsed state that stays to the side at all times.

## Stories

- [PWEB-19971](https://dayforce.atlassian.net/browse/PWEB-19971): [Side Nav] [Development] Create React Component -Labs
- [PWEB-19972](https://dayforce.atlassian.net/browse/PWEB-19972): [Side Nav] [Development] A11y
- [PWEB-19973](https://dayforce.atlassian.net/browse/PWEB-19973): [Side Nav] [Development] Storybook Documentation
- [PWEB-19974](https://dayforce.atlassian.net/browse/PWEB-19974): [Side Nav] [Development] Tests: P11y, Manual, Visual tests, Unit tests, playwright tests
- [PWEB-20124](https://dayforce.atlassian.net/browse/PWEB-20124): [Side Nav] [Development] Add to Ref App
- [PWEB-20145](https://dayforce.atlassian.net/browse/PWEB-20145): [SideNav][Development] Handle mobile screen sizing

## Future Stories for NavItem

- [PWEB-20098](https://dayforce.atlassian.net/browse/PWEB-20098): [NavItem] [Development] - Architecture
- [PWEB-20099](https://dayforce.atlassian.net/browse/PWEB-20099): [NavItem] [Development] - Create React Component - Labs
- [PWEB-20101](https://dayforce.atlassian.net/browse/PWEB-20101): [NavItem] [Development] A11y
- [PWEB-20100](https://dayforce.atlassian.net/browse/PWEB-20100): [NavItem] [Development] Tests: P11y, Manual, Visual tests, Unit tests, playwright tests
- [PWEB-20102)](https://dayforce.atlassian.net/browse/PWEB-20102): [NavItem] [Development] Storybook Documentation
- [PWEB-20125](https://dayforce.atlassian.net/browse/PWEB-20125): [NavItem] [Development] Add to Ref App

## Change Log
