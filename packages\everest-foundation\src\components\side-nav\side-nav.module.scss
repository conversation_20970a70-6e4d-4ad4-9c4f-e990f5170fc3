@use '../../variables.scss';
@use '../../mixins.scss' as mixins;

@mixin sideNavHoverStyling {
  border-radius: var(--evr-radius-sm);
  background: var(--evr-surfaces-primary-hovered);
}

@mixin sideNavActiveStyling {
  border-radius: var(--evr-radius-sm);
  background: var(--evr-interactive-primary-decorative);

  & span {
    color: var(--evr-interactive-primary-default);
    font-weight: var(--evr-demi-bold-weight);
  }
}

@mixin sideNavRemoveListStyle {
  list-style: none;
  padding: 0;
  margin: 0;
}

.evrSideNav {
  display: flex;
  flex-direction: column;

  .evrSideNavActions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: var(--evr-spacing-sm);

    .evrShowCollapsedIcon {
      & svg {
        transform: rotate(180deg);
      }
    }
  }

  &.evrSideNavCollapsed {
    align-items: center;

    & ul {
      @include sideNavRemoveListStyle;

      & .evrSideNavActive {
        @include sideNavActiveStyling;
      }

      & li {
        padding: var(--evr-spacing-xs);

        &:hover {
          @include sideNavHoverStyling;
        }

        // remove default focus outline so we can use the FocusRing
        & a {
          &:focus-visible {
            outline: none;
          }
        }
      }
    }
  }

  &.evrSideNavExpanded {
    & ul {
      @include sideNavRemoveListStyle;

      & button {
        all: unset;
        padding: var(--evr-spacing-xs) var(--evr-spacing-sm);
        cursor: pointer;

        // flexbox
        display: flex;
        align-items: center;
        justify-content: space-between;

        // font
        @include mixins.clarikageo;
        font-weight: var(--evr-medium-weight);
        font-size: var(--evr-size-sm);
        color: var(--evr-content-primary-highemp);

        height: var(--evr-spacing-md);

        &.evrSideNavActive {
          @include sideNavActiveStyling;

          &.evrSideNavActiveParentExpanded {
            background: none;
          }
        }

        &:hover {
          @include sideNavHoverStyling;
        }

        & .evrSideNavButton {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: var(--evr-spacing-sm);
        }
      }

      & a {
        padding: var(--evr-spacing-xs) var(--evr-spacing-sm);
        gap: var(--evr-spacing-sm);

        // flexbox
        display: flex;
        flex-direction: row;
        align-items: center;

        // font
        @include mixins.clarikageo;
        text-decoration: none;
        font-weight: var(--evr-medium-weight);
        font-size: var(--evr-size-sm);
        color: var(--evr-content-primary-highemp);

        height: var(--evr-spacing-md);

        &.evrSideNavActive {
          @include sideNavActiveStyling;
        }

        &:focus-visible {
          outline: none;
        }

        &:hover {
          @include sideNavHoverStyling;
        }
      }
    }
  }
}
