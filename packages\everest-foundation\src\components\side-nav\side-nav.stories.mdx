import { <PERSON>a, Story, Preview } from '@storybook/addon-docs';
import { Drawer } from '../drawer';
import { SideNav, SideNavActions } from '.';
import Examples from './side-nav.examples.mdx';

<Meta
  title="Everest Labs/Components/SideNav"
  component={SideNav}
  parameters={{
    status: {
      type: 'alpha',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/yzd3fHCowfWDL4iTBAoJNv/Everest-Documentation-for-Designers?node-id=16207-35499&p=f&t=IszOMSFrPixPk6VR-0',
    },
  }}
/>

# SideNav

The `SideNav` component is used for navigation within an application.

<Examples />

## Live Demo

<Story name="SideNav">
  {(args) => {
    // create a state to manage the expanded items in the side nav
    const [expandedItems, setExpandedItems] = React.useState([]);
    const handleExpand = (id) => {
      setExpandedItems((prev) => {
        if (prev.includes(id)) {
          return prev.filter((item) => item !== id);
        } else {
          return [...prev, id];
        }
      });
    };
    const data = [
      {
        id: 'personal',
        iconName: 'person',
        href: 'javascript:void(0);',
        label: 'Personal',
        active: true,
        expanded: expandedItems.includes('personal'),
        onClick: (id) => handleExpand(id),
        childItems: [
          {
            id: 'about-me',
            label: 'About Me',
            href: 'javascript:void(0);',
            active: true,
            onClick: () => console.log('about me'),
          },
          {
            id: 'letters',
            label: 'Letters',
            href: 'javascript:void(0);',
            onClick: () => console.log('letters'),
          },
        ],
      },
      {
        id: 'career',
        iconName: 'briefcase',
        label: 'Career', // example without href, should apply a # string plus have e.preventDefault()
        onClick: () => console.log('career'),
      },
      {
        id: 'forms',
        iconName: 'form',
        label: 'Forms',
        href: 'javascript:void(0);',
        onClick: () => console.log('form'),
      },
      {
        id: 'settings',
        iconName: 'settings',
        label: 'Settings',
        href: 'javascript:void(0);',
        onClick: () => console.log('settings'),
      },
    ];

    // create a expanded state to pass to the side nav and drawer
    const [expanded, setExpanded] = React.useState(false);
    const handleToggle = () => {
      setExpanded(!expanded);
    };


    return (
      <div style={{ height: '100vh' }}>
        <Drawer expanded={expanded}>
          <SideNav id="side-nav" data={data} expanded={expanded} >
            <SideNavActions onToggle={handleToggle}>
              <div className='evrBodyText2'>Profile</div>
            </SideNavActions>
          </SideNav>
        </Drawer>
      </div>
    );

}}

</Story>
