import { forwardRef, PropsWith<PERSON><PERSON>dren, useMemo } from 'react';
import React from 'react';
import classnames from 'classnames';

import { defaultSideNavContext, ISideNavContext, SideNavContext } from './side-nav-context';
import { SideNavItem } from './side-nav-item';
import { resolvePropsContext } from '../../utils';

import styles from './side-nav.module.scss';
export interface ISideNavDataItem {
  /** A unique identifier for the side nav item. */
  id: string;

  /** Sets **data-testid** attribute on the html element. */
  testId?: string;

  /** Specifies the name of the icon to display alongside the side nav item. */
  iconName: string;

  /** The text label for the side nav item. */
  label: string;

  /** URL to navigate to when the side nav item is clicked. */
  href?: string;

  /**
   * A list of child items for the side nav item.
   * Each child item should conform to the `ISideNavDataItem` interface. Defaults to an empty array.
   */
  childItems?: ISideNavDataItem[];

  /**
   * Indicates whether the side nav item is expanded to show its children.
   * This prop only works if the side nav item has children. Defaults to `false`.
   */
  expanded?: boolean;

  /** Indicates whether the side nav item is currently active. Defaults to `false`. */
  active?: boolean;

  /** Optional. Callback function triggered when the side nav item is clicked. */
  onClick?: (id: string) => void;

  /** Optional. Used internally to apply the correct padding */
  level?: number;
}

export interface ISideNav {
  /** A unique identifier for the side nav. */
  id: string;

  /** Optional. Sets **data-testid** attribute on the html element. */
  testId?: string;

  /** Optional. An array of side nav items that define the structure and content of the side navigation. */
  data?: ISideNavDataItem[];

  /** Provides an accessible label for the side nav to improve screen reader support. */
  ariaLabel: string;

  /** A boolean value that indicates whether the side nav is expanded or collapsed mode. */
  expanded: boolean;
}

export const SideNav = forwardRef<HTMLDivElement, PropsWithChildren<ISideNav>>((props, ref): JSX.Element => {
  const {
    id,
    testId,
    data,
    ariaLabel,
    expanded: drawerExpanded,
  } = resolvePropsContext<ISideNav, ISideNavContext>(props, defaultSideNavContext);

  const context = useMemo(
    () => ({
      expanded: drawerExpanded,
    }),
    [drawerExpanded]
  );

  // go through the data and create the side nav items
  const createSideNavItems = useMemo(() => {
    if (!data) return null;

    return data.map((item) => (
      <SideNavItem
        key={item.id}
        {...item}
        childItems={drawerExpanded ? item.childItems : []} // Only show child items if the side nav is expanded
      />
    ));
  }, [data, drawerExpanded]);

  return (
    <SideNavContext.Provider value={context}>
      <nav
        id={id}
        data-testid={testId}
        ref={ref}
        aria-label={ariaLabel}
        className={classnames(styles.evrSideNav, {
          [styles.evrSideNavCollapsed]: !drawerExpanded,
          [styles.evrSideNavExpanded]: drawerExpanded,
        })}
      >
        {props.children}
        <ul>{createSideNavItems}</ul>
      </nav>
    </SideNavContext.Provider>
  );
});

SideNav.displayName = 'SideNav';
