@use '../../variables.scss';

.evrSidePanel {
  height: 100vh; // fallback for dvh
  height: 100dvh;
  position: fixed;
  top: 0;
  background-color: var(--evr-surfaces-primary-default);
  box-shadow: var(--evr-depth-06);
  display: flex;
  flex-direction: column;
  outline: none;
  &.anchorLeft {
    animation: variables.$slideFromLeftAnimation;
    left: 0;
  }

  &.anchorRight {
    animation: variables.$slideFromRightAnimation;
    right: 0;
  }
  &Fullscreen {
    max-width: 100%;
    min-width: 100%;
  }

  &XL {
    max-width: 960px;
    min-width: 768px;
  }

  &LG {
    max-width: 720px;
    min-width: 600px;
  }

  &MD {
    max-width: 640px;
    min-width: 512px;
  }

  &SM {
    width: 320px;
  }
}
