import React, { PropsWithChildren, useCallback, useEffect, useMemo, useRef } from 'react';

import { mergeRefs } from '../../utils';
import { FocusTrap } from '../../utils/focus-trap';
import { IDialogBase, Overlay, TDialogCloseEventReason } from '../overlay';
import { TLightBoxVariant } from '../overlay/light-box';
import { SidePanelContext } from '../side-panel/side-panel-context';

import styles from './side-panel-base.module.scss';

export type TSidePanelAnchor = 'left' | 'right';

export type TSidePanelSize = 'fullscreen' | 'sm' | 'md' | 'lg' | 'xl';

export interface ISidePanelBase extends IDialogBase {
  anchor?: TSidePanelAnchor;
  ariaLabel?: string;
  size: TSidePanelSize;
  // lightBoxVariant is a stop-gap measure when combining multiple components
  // with dark overlays
  lightBoxVariant?: TLightBoxVariant;
}

export const SidePanelBase = React.forwardRef<HTMLDivElement, PropsWithChildren<ISidePanelBase>>(
  (props, ref): JSX.Element => {
    const {
      id,
      testId,
      children,
      open,
      onOpen,
      onClose,
      ariaLabelledBy,
      ariaDescribedBy,
      anchor = 'right',
      ariaLabel,
      size,
      lightBoxVariant = 'dark',
    } = props;

    const contentContainerRef = useRef<HTMLDivElement>(null);
    const lightBoxRef = useRef<HTMLDivElement>(null);
    const onOpenPrevRef = useRef(open ? null : false);
    const reasonRef = useRef<TDialogCloseEventReason | null>(null);

    const handleEscKey = useCallback(
      (event: React.KeyboardEvent) => {
        if (event.key === 'Escape' && open) {
          reasonRef.current = 'escapeKeyDown';
          setTimeout(() => onClose?.({ reason: reasonRef.current as TDialogCloseEventReason }));
        }
      },
      [onClose, open]
    );

    useEffect(() => {
      if (open && !onOpenPrevRef.current) {
        setTimeout(() => {
          contentContainerRef && contentContainerRef.current?.focus();
        });
        onOpen &&
          setTimeout(() => {
            onOpen();
          });
      } else if (!open && onOpenPrevRef.current) {
        if (reasonRef.current === null) {
          onClose?.();
        } else {
          reasonRef.current = null;
        }
      }
      onOpenPrevRef.current = open;
    }, [open, onOpenPrevRef, onOpen, onClose]);

    const renderSizeClass = (size: TSidePanelSize) => {
      switch (size) {
        case 'fullscreen':
          return styles.evrSidePanelFullscreen;
        case 'xl':
          return styles.evrSidePanelXL;
        case 'lg':
          return styles.evrSidePanelLG;
        case 'md':
          return styles.evrSidePanelMD;
        case 'sm':
          return styles.evrSidePanelSM;
      }
    };

    const sidePanelContext = useMemo(() => ({ isFullscreen: size === 'fullscreen' }), [size]);

    const result = open ? (
      <SidePanelContext.Provider value={sidePanelContext}>
        <Overlay
          id={`${id}-overlay`}
          ref={lightBoxRef}
          testId={`${testId}-overlay`}
          open={open}
          fullscreen
          lightBoxVariant={lightBoxVariant}
          onLightBoxClick={(e) => {
            const el = e.target;
            if (
              el &&
              lightBoxRef?.current?.contains(el as HTMLElement) &&
              !contentContainerRef?.current?.contains(el as HTMLElement)
            ) {
              reasonRef.current = 'lightBoxClick';

              setTimeout(() => {
                onClose?.({ reason: reasonRef.current as TDialogCloseEventReason });
              });
            }
          }}
          onLightBoxKeyDown={handleEscKey}
        >
          {/* eslint-disable-next-line jsx-a11y/no-autofocus */}
          <FocusTrap autoFocus={false}>
            <div
              id={id}
              aria-label={ariaLabel}
              aria-labelledby={ariaLabelledBy}
              aria-describedby={ariaDescribedBy}
              data-testid={testId}
              className={`${styles.evrSidePanel} ${
                anchor === 'right' ? styles.anchorRight : styles.anchorLeft
              } ${renderSizeClass(size)}`}
              tabIndex={-1}
              ref={mergeRefs([ref, contentContainerRef])}
              role="dialog"
              aria-modal={true}
            >
              {children}
            </div>
          </FocusTrap>
        </Overlay>
      </SidePanelContext.Provider>
    ) : (
      <></>
    );

    return result;
  }
);

SidePanelBase.displayName = 'SidePanelBase';
