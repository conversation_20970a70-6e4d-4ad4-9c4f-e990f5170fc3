import React, { PropsWithChildren, useContext } from 'react';
import classnames from 'classnames';

import { SidePanelContext } from './side-panel-context';
import { IOverlayBody, OverlayBody } from '../overlay';

import styles from './side-panel.module.scss';

export const SidePanelBody = React.forwardRef<HTMLDivElement, PropsWithChildren<IOverlayBody>>(
  (props, ref): JSX.Element => {
    const { children, id, testId } = props;

    const context = useContext(SidePanelContext);
    const { isFullscreen } = context;

    return (
      <div
        className={classnames(styles.evrSidePanelBody, {
          [styles.evrSidePanelBodyPadding]: !isFullscreen,
        })}
      >
        <OverlayBody id={id} testId={testId} ref={ref}>
          {children}
        </OverlayBody>
      </div>
    );
  }
);

SidePanelBody.displayName = 'SidePanelBody';
