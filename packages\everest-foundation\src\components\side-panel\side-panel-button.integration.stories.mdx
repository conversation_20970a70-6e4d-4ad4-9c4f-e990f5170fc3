import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '.';
import { Button } from '../button';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Integration"
  component={SidePanel}
  parameters={{
    chromatic: Chromatic.DISABLE,
  }}
/>

# Open Side Panel from Button

## Live Demo

## Playwright User Interactions

<Canvas>
  <Story name="Open SidePanel from Button">
    {() => {
      const [open, setOpen] = React.useState(false);
      const triggerBtnMDRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-md"
            label="Open Side Panel"
            ref={triggerBtnMDRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-md"
            testId="side-panel-light-box"
            open={open}
            size="md"
            onClose={(e) => {
              setOpen(false);
              triggerBtnMDRef && triggerBtnMDRef.current?.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'Close Side Panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id" className="evrHeading3">
                Side Panel Heading
              </h3>
            </SidePanelHeader>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>
