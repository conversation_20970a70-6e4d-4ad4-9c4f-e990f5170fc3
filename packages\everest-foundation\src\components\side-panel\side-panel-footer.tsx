import React, { PropsWithChildren, useContext } from 'react';
import classnames from 'classnames';

import { SidePanelContext } from './side-panel-context';
import { IOverlayFooter, OverlayFooter } from '../overlay';

import styles from './side-panel.module.scss';

export const SidePanelFooter = React.forwardRef<HTMLDivElement, PropsWithChildren<IOverlayFooter>>(
  (props, ref): JSX.Element => {
    const { children, id, testId } = props;

    const context = useContext(SidePanelContext);
    const { isFullscreen } = context;

    return (
      <div className={classnames(styles.evrSidePanelFooter, { [styles.evrSidePanelFooterPadding]: !isFullscreen })}>
        <OverlayFooter id={id} testId={testId} ref={ref}>
          {children}
        </OverlayFooter>
      </div>
    );
  }
);

SidePanelFooter.displayName = 'SidePanelFooter';
