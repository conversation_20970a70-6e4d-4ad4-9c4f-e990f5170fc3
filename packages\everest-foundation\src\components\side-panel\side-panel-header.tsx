import React, { PropsWithChildren, useContext } from 'react';
import classnames from 'classnames';

import { SidePanelContext } from './side-panel-context';
import { IOverlayHeader, OverlayHeader } from '../overlay';

import styles from './side-panel.module.scss';

export interface ISidePanelHeader extends IOverlayHeader {
  addBorder?: boolean;
}

export const SidePanelHeader = React.forwardRef<HTMLDivElement, PropsWithChildren<ISidePanelHeader>>(
  (props, ref): JSX.Element => {
    const { addBorder = true, children, id, testId, closeButtonAriaLabel, onCloseButtonClick } = props;

    const context = useContext(SidePanelContext);
    const { isFullscreen } = context;

    return (
      <div
        className={classnames(styles.evrSidePanelHeader, {
          [styles.evrSidePanelHeaderBorder]: addBorder,
          [styles.evrSidePanelHeaderPadding]: !isFullscreen,
        })}
      >
        <OverlayHeader
          id={id}
          testId={testId}
          onCloseButtonClick={onCloseButtonClick}
          closeButtonAriaLabel={closeButtonAriaLabel}
          ref={ref}
        >
          {children}
        </OverlayHeader>
      </div>
    );
  }
);

SidePanelHeader.displayName = 'SidePanelHeader';
