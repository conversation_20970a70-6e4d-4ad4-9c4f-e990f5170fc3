import { useRef } from 'react';
import { <PERSON>a, <PERSON>, Canvas } from '@storybook/addon-docs';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, ModalBody, ModalContentText, ModalFooter, ModalFooterActions } from '../modal';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '.';
import { Button } from '../button';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Integration"
  component={SidePanel}
  parameters={{ chromatic: Chromatic.DISABLE }}
/>

# Open Modal from SidePanel

## Live Demo

<Canvas>
  <Story name="Open Modal from SidePanel">
    {() => {
      const [openSidePanel, setOpenSidePanel] = React.useState(false);
      const [openModal, setOpenModal] = React.useState(false);
      const [lightBoxVariantSidePanel, setLightBoxVariantSidePanel] = React.useState('dark');
      const modalTriggerButtonRef = React.useRef(null);
      React.useEffect(() => {
        // delay switching the lightbox of the modal from dark
        // to clear to prevent flicker
        if (openModal && openSidePanel) {
          setTimeout(() => setLightBoxVariantSidePanel('clear'));
        } else {
          setLightBoxVariantSidePanel('dark');
        }
      }, [openModal, openSidePanel]);
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Button id="trigger-button" label="Open Modal from SidePanel" onClick={() => setOpenSidePanel(true)} />
          <div>
            <SidePanel
              id="side-panel-modal"
              open={openSidePanel}
              ariaLabelledBy="side-panel-modal-heading"
              ariaDescribedBy="side-panel-modal-body"
              anchor="right"
              onClose={(e) => {
                // only run when the modal is closed
                if (!openModal) {
                  setOpenSidePanel(false);
                  const triggerBtn = document.getElementById('trigger-button');
                  triggerBtn && triggerBtn.focus();
                }
              }}
              lightBoxVariant={lightBoxVariantSidePanel}
            >
              <SidePanelHeader
                closeButtonAriaLabel={'close side panel'}
                onCloseButtonClick={(e) => {
                  setOpenSidePanel(false);
                }}
                id="side-panel-modal-header"
                testId="side-panel-header-test"
              >
                <h3 id="side-panel-modal-heading" className="evrHeading3">
                  SidePanel Heading
                </h3>
              </SidePanelHeader>
              <SidePanelBody id="side-panel-modal-body">
                <div style={{ margin: '0.5rem 0' }}>
                  <Button
                    id="modal-button"
                    label="Open modal"
                    onClick={() => setOpenModal(true)}
                    ref={modalTriggerButtonRef}
                  />
                </div>
              </SidePanelBody>
              <SidePanelFooter id="side-panel-footer">
                <p className="evrBodyText">Footer</p>
              </SidePanelFooter>
            </SidePanel>
          </div>
          <Modal
            open={openModal}
            size="md"
            id="modal"
            onClose={(e) => {
              openModal && setOpenModal(false);
              modalTriggerButtonRef && modalTriggerButtonRef.current.focus();
            }}
            ariaLabelledBy="modal-header"
            ariaDescribedBy="modal-body"
          >
            <ModalHeader
              title="Modal Heading"
              onCloseButtonClick={(e) => {
                if (openModal) setOpenModal(false);
              }}
              id="modal-header-id"
              closeButtonAriaLabel="Close Modal"
            />
            <ModalBody id="modal-body">
              <ModalContentText id="modal-body-content" content="Content" />
            </ModalBody>
            <ModalFooter id="modal-footer">
              <ModalFooterActions
                id="modal-action-buttons"
                primaryAction={{
                  id: 'primary-button',
                  label: 'Continue',
                  onClick: () => alert('Continue'),
                }}
                secondaryAction={{
                  id: 'close-button',
                  label: 'Close',
                  onClick: () => {
                    setOpenModal(false);
                    const modalTriggerBtn = document.getElementById('modal-button');
                    modalTriggerBtn && modalTriggerBtn.focus();
                  },
                }}
              />
            </ModalFooter>
          </Modal>
        </div>
      );
    }}
  </Story>
</Canvas>
