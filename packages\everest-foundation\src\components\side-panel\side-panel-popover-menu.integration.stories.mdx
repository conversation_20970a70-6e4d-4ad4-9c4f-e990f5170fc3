import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { PopoverMenu, PopoverMenuItem, PopoverMenuItemHeading } from '../popover-menu';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '.';
import { Button } from '../button';
import { Chromatic, defaultModes } from '../../../chromatic';

<Meta
  title="Testing/Automation Test Cases/Integration"
  component={SidePanel}
  parameters={{ chromatic: Chromatic.DISABLE }}
/>

# Open Modal from SidePanel

## Live Demo

<Canvas>
  <Story name="Open Popover Menu from SidePanel">
    {() => {
      const [openSidePanel, setOpenSidePanel] = React.useState(false);
      const [openModal, setOpenModal] = React.useState(false);
      const [lightBoxVariantSidePanel, setLightBoxVariantSidePanel] = React.useState('dark');
      const modalRef = React.useRef(null);
      const modalTriggerButtonRef = React.useRef(null);
      const initialState = '';
      const [selectedMenuItemId, setSelectedMenuItemId] = React.useState(initialState);
      const subMenuRef = React.useRef(null);
      React.useEffect(() => {
        // delay switching the lightbox of the modal from dark
        // to clear to prevent flicker
        if (openModal && openSidePanel) {
          setTimeout(() => setLightBoxVariantSidePanel('clear'));
        } else {
          setLightBoxVariantSidePanel('dark');
        }
      }, [openModal, openSidePanel]);
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Button id="trigger-button" label="Open Popover Menu from SidePanel" onClick={() => setOpenSidePanel(true)} />
          <div>
            <SidePanel
              id="side-panel-modal"
              open={openSidePanel}
              ariaLabelledBy="side-panel-modal-heading"
              ariaDescribedBy="side-panel-modal-body"
              anchor="right"
              onClose={(e) => {
                // only run when the modal is closed
                if (!openModal) {
                  setOpenSidePanel(false);
                  const triggerBtn = document.getElementById('trigger-button');
                  triggerBtn && triggerBtn.focus();
                }
              }}
              lightBoxVariant={lightBoxVariantSidePanel}
            >
              <SidePanelHeader
                closeButtonAriaLabel={'close side panel'}
                onCloseButtonClick={(e) => {
                  setOpenSidePanel(false);
                }}
                id="side-panel-modal-header"
                testId="side-panel-header-test"
              >
                <h3 id="side-panel-modal-heading" className="evrHeading3">
                  SidePanel Heading
                </h3>
              </SidePanelHeader>
              <SidePanelBody id="side-panel-modal-body">
                <div style={{ margin: '0.5rem 0' }}>
                  <PopoverMenu
                    id="my-sub-menu-example"
                    buttonAriaLabel="Popover Menu"
                    triggerOption="button"
                    triggerProps={{
                      variant: 'secondary',
                      endIcon: 'chevronDownSmall',
                    }}
                    buttonLabel="Popover Menu"
                    onChange={({ id }) => setSelectedMenuItemId(id)}
                  >
                    <PopoverMenuItem id="submenu-item-1" role="menuitemcheckbox">
                      Menu item 1
                    </PopoverMenuItem>
                    <PopoverMenuItem id="submenu-item-2" role="menuitemcheckbox">
                      Menu item 2
                    </PopoverMenuItem>
                    <PopoverMenuItem id="submenu-item-3" role="menuitemcheckbox" divider>
                      Menu item 3
                    </PopoverMenuItem>
                    <PopoverMenuItem id="menu-item-submenu" ref={subMenuRef}>
                      <PopoverMenuItemHeading>SubMenu Heading</PopoverMenuItemHeading>
                      <PopoverMenuItem id="sub-list-item-1">SubItem 1</PopoverMenuItem>
                      <PopoverMenuItem id="sub-list-item-2">SubItem 2</PopoverMenuItem>
                    </PopoverMenuItem>
                  </PopoverMenu>
                </div>
              </SidePanelBody>
              <SidePanelFooter id="side-panel-footer">
                <p className="evrBodyText">Footer</p>
              </SidePanelFooter>
            </SidePanel>
          </div>
        </div>
      );
    }}
  </Story>
</Canvas>
