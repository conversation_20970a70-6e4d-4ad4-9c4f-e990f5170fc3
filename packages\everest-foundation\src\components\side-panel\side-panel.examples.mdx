import { <PERSON>Example } from '../../../.storybook/doc-blocks/example/example';
import { useEffect, useRef, useState } from 'react';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';
import { But<PERSON> } from '../button';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '.';
import { RadioButtonGroup } from '../radio-button-group';
import { RadioButton } from '../radio-button';
import { Combobox } from '../combobox';
import { DatePicker } from '../date-time/date-picker';
import { Dropdown } from '../dropdown';
import { MultiSelect } from '../multiselect';
import { TextArea } from '../text-area';
import { TextField } from '../text-field';
import { Modal, ModalHeader, ModalBody, ModalFooter, ModalFooterActions, ModalContentText } from '../modal';
import { useEverestContext } from '../everest-provider';

import { Form } from '../../mocks/form/form';
import { TabGroup } from '../tab-group';
import { Tab } from '../tab';

export const scope = {
  Button,
  Combobox,
  DatePicker,
  Dropdown,
  MultiSelect,
  SidePanel,
  SidePanelHeader,
  SidePanelBody,
  SidePanelFooter,
  RadioButtonGroup,
  RadioButton,
  TabGroup,
  Tab,
  TextArea,
  TextField,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalFooterActions,
  ModalContentText,
  useEffect,
  useRef,
  useState,
  useEverestContext,
};

A SidePanel performs a small set of quick actions related to content on the main screen.

## How to Use

- SidePanel can render any child component, including `SidePanelHeader`, `SidePanelBody` and `SidePanelFooter`. <br />

`open` is used to toggle the visibility of the SidePanel. When `open` is set to true, the SidePanel opens, and it triggers the `onOpen` callback function. SidePanel can be closed by setting `open` to false and when it is set to false, it triggers the `onClose` callback function. <br /><br />
The `onOpen` and `onClose` callback functions are used to perform operations when the SidePanel opens and closes respectively. <br /><br />
The SidePanel can be closed by pressing the <kbd>Escape</kbd> keyboard button or by clicking outside the side panel element. Close on click outside functionality can be controlled by a return reason of the `onClose` callback. See [Prevent outside clicking from closing the SidePanel](#prevent-outside-clicking-from-closing-the-sidepanel) example

Similarly closing by <kbd>Escape</kbd> keyboard button can be controlled by a return reason of the `onClose` callback. <br /><br />
The Close IconButton of the `SidePanelHeader` will be rendered only when the `onCloseButtonClick` callback and `closeButtonAriaLabel` prop are passed. <br />

Please note that if `onCloseButtonClick` toggles `open` to `false`, the `onClose` function will run. This can be seen in the examples to follow.

## Basic Usage

SidePanel anatomy is made up of the following parts:

- <b>Header:</b> Contains the title of the SidePanel.
- <b>Body:</b> The main section of the SidePanel, typically used for editing specific content within the page.
- <b>Footer:</b> Includes primary, secondary, and tertiary actions related to the content being edited.

Please refer to the [SidePanel with a Form](#sidepanel-with-a-form) for a detailed example.

## Content Guidelines

### Title (Header)

- Clearly describe the purpose of the SidePanel.
- Title should be concise and descriptive.

### Content (Body)

- Content can vary depending on the use case.
- Use the SidePanel to allow users to edit or interact with specific content within the page.
- Ensure that the content is clear and easy to understand.
- Keep content minimal.
- Try to limit the number of Tabs (if used) to a maximum of 5.

### Actions (Footer)

- Contains primary, secondary, and tertiary actions for the content being edited through the Side Panel.
- Typical actions for a Side Panel are Save (Primary), Cancel (Secondary), and Reset (Tertiary).

## Responsive

A responsive SidePanel can be achieved through use of the `useEverestContext` hook provided by <LinkTo kind="Foundations/Everest Provider">EverestProvider</LinkTo>. The following breakpoint ranges should be referenced to determine which value to supply to `size`. This is demonstrated in the [Use useEverestContext to dynamically size the SidePanel](#use-useeverestcontext-to-dynamically-size-the-sidepanel) example below.

| Breakpoints          | Size       |
| -------------------- | ---------- |
| &leq; 767 (xs, sm)   | fullscreen |
| 768 - 1279 (md)      | md         |
| 1280 - 1439 (lg)     | lg         |
| &geq; 1440 (xl, xxl) | xl         |

The `sm` size is also available for use, should any team require it for their implementation. The `sm` size is 320px wide.

## Accessibility

`ariaLabelledBy` should be set to id of heading element `<h3>` from `SidePanelHeader` and `ariaDescribedBy` should be set to `SidePanelBody`'s id or items inside the `SidePanelBody` for the screen reader to announce the SidePanel's children when the side panel opens.

Either `ariaLabelledBy` or `ariaLabel` should be provided to the SidePanel component as having both attributes present simultaneously can create ambiguity about which label should be used by assistive technologies. It's important to provide clear and consistent labeling to ensure accessibility.

Since Everest SidePanel mimics the behaviour of the Modal, its accessibility is handled in the same way.

SidePanel implements `role=dialog` and `aria-modal=true` to indicate that it is both a dialog and a modal.

When tested with NVDA, the role "dialog" is not announced when the SidePanel opens. This issue arises because NVDA encounters a problem when the entire dialog element receives focus. This issue is documented in detail on the NVDA GitHub repository here: https://github.com/nvaccess/nvda/issues/8620.

In the case where double scroll bars can be displayed, please refer to the [Prevent body from scrolling when SidePanel is open](#prevent-body-from-scrolling-when-sidepanel-is-open) example.

## Variations

### SidePanel with Tab Group

export const sidePanelTabGroup = `() => {
    const [open, setOpen] = useState(false);
    const [activeId, setActiveId] = useState('tab-label-id-1');
    const triggerBtnTabGroupRef = useRef(null);
    const handleChange = (newlySelectedTabId) => {
      newlySelectedTabId && setActiveId(newlySelectedTabId);
    };
    const textMap = {
      viewAllLabel: 'View All',
      viewAllAriaLabel: 'View All Tabs',
      overflowButtonPrevAriaLabel: 'Scroll to previous',
      overflowButtonNextAriaLabel: 'Scroll to next',
    };
    return (
        <>
          <Button
            id="trigger-button-with-tab-groups"
            label="Example with Tab Groups"
            ref={triggerBtnTabGroupRef}
            onClick={() => setOpen(!open)}
          />
          <SidePanel
            id='side-panel-tab-group-example'
            open={open}
            ariaLabelledBy="side-panel-heading-id-tab-group"
            ariaDescribedBy="side-panel-body-id-tab-group"
            size="md"
            onClose={(e) => {
                setOpen(false);
                triggerBtnTabGroupRef && triggerBtnTabGroupRef.current.focus()
            }}
          >
            <SidePanelHeader
                addBorder={false}
                closeButtonAriaLabel={'close side panel'}
                onCloseButtonClick={(e) => {
                  setOpen(false);
                }}
                id="side-panel-header-id-tab-group"
              >
                <h3 id="side-panel-heading-id-tab-group" className="evrHeading3">
                  Sample Page 
                </h3>
              </SidePanelHeader>
              <SidePanelBody id="side-panel-body-id-tab-group">
                <TabGroup id="tab-group-sidepanel-example" activeId={activeId} onActiveIdChange={handleChange} textMap={textMap}>
                    <Tab id='tab-label-id-1' label='Tab Label 1'>
                        Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
                    </Tab>
                    <Tab id='tab-label-id-2' label='Tab Label 2'>
                        Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
                    </Tab>
                </TabGroup>
              </SidePanelBody>
          </SidePanel>
        </>
    )
}`;

<CodeExample scope={scope} code={sidePanelTabGroup} />

### SidePanel with a Form

export const sidePanelWithAForm = `() => {
    const options = [
      { id: '1', title: 'Canada' },
      { id: '2', title: 'China' },
      { id: '3', title: 'Great Britain' },
      { id: '4', title: 'Italy' },
      { id: '5', title: 'Mexico' },
      { id: '6', title: 'United States' },
    ];
    const [open, setOpen] = useState(false);
    const phoneNumberInputRef = useRef(null);
    const [phoneNumber, setPhoneNumber] = useState('');
    const [searchValue, setSearchValue] = useState('');
    const [numberError, setNumberError] = useState('');
    const [dropdownCountryError, setDropdownCountryError] = useState('');
    const [dropdownCountryValue, setDropdownCountryValue] = useState(undefined);
    const [comboboxInputValue, setComboboxInputValue] = useState('');
    const [comboboxCountryValue, setComboboxCountryValue] = useState('');
    const [filteredOptions, setFilteredOptions] = useState(options);
    const [multiSelectCountryValue, setMultiSelectCountryValue] = useState([]);
    const [multiSelectInputValue, setMultiSelectInputValue] = useState('');
    const [textAreaValue, setTextAreaValue] = useState('');
    const countryDropdownRef = useRef(null);
    const triggerBtnFormRef = useRef(null);
    const onSearchValueChange = (value) => {
      setSearchValue(value);
    };
    const onPhoneNumberChange = (value) => {
      setPhoneNumber(value);
    };
    const focusInputUponVerificationError = (error, ref) => {
      if (error) {
        if (ref.current) {
          ref.current.focus();
        }
      }
    };
    useEffect(() => {
      if (dropdownCountryError) {
        focusInputUponVerificationError(dropdownCountryError, countryDropdownRef);
      }
    }, [dropdownCountryError]);
    const handleClick = () => {
      setDropdownCountryError('error');
      phoneNumber.length === 0 ? setNumberError('error') : null;
    };
    const handleDropdownCountryChange = (value) => {
      value && setDropdownCountryValue(value);
    };
    const handleDropdownClearCountry = () => {
      setDropdownCountryValue(undefined);
    };
    const handleComboboxCountryChange = (item) => {
      setComboboxCountryValue(item);
      item ? setComboboxInputValue(item.title) : setComboboxInputValue('');
      setFilteredOptions(options);
    };
    const handleComboboxClearCountry = () => {
      setComboboxInputValue("");
    };
    const handleComboboxInputValueChange = (value) => {
      setComboboxInputValue(value);
      const filtered = options.filter((option) =>
        option.title.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredOptions(filtered);
    };
    const handleComboxCountryBlur = () => {
      if (comboboxCountryValue) {
        setComboboxInputValue(comboboxCountryValue.title)
      } else {
        setComboboxInputValue('');
      }
      // workaround to fix combobox focused issue on sidePanel
      // Further investigation is required (Ticket Created)
      // It seems like a timing issue on the FocusLock library when setState with an Array list would stop focus from getting to the next focusable item.  
      setTimeout(() => {
        setFilteredOptions(options);
      })      
    }
    const handleMultiSelectCountryChange = (values) => {
      values && setMultiSelectCountryValue(values);
    };
    const handleMultiSelectClearCountry = () => {
      setMultiSelectCountryValue([]);
      setMultiSelectInputValue('');
    };
    const handleMultiSelectInputValueChange = (value) => {
      setMultiSelectInputValue(value);
    };
    const onTextAreaChange = (value) => {
      setTextAreaValue(value);
    };
    const locale = 'en-US';
    const [dateValue, setDateValue] = useState(undefined);
    const [dateFieldStatus, setDateFieldStatus] = useState('default');
    const [hasDateFieldDisplayValue, setHasDateFieldDisplayValue] = useState(false);
    const [dateFieldErrorMessage, setDateFieldErrorMessage] = useState('');
    const dateSegmentPlaceholder = { 
      day: 'dd',
      month: 'mm',
      year: 'yyyy'
    };
    const getDateTimeParts = () => {
      const dateTimeParts = [];
      const parts = new Intl.DateTimeFormat(locale).formatToParts(new Date());
      for (let i = 0; i < parts.length; i++) {
        dateTimeParts.push({ ...parts[i], id: parts[i].type + i });
      }
      return dateTimeParts;
    };
    const weekdays = [
      { short: 'Su', long: 'Sunday' },
      { short: 'Mo', long: 'Monday' },
      { short: 'Tu', long: 'Tuesday' },
      { short: 'We', long: 'Wednesday' },
      { short: 'Th', long: 'Thursday' },
      { short: 'Fr', long: 'Friday' },
      { short: 'Sa', long: 'Saturday' }
    ];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const handleDatePickerChange = (displayValue, newValue) => {
      setHasDateFieldDisplayValue(!!displayValue);
      if (newValue) {
        const newDate = new Date(newValue);
        newDate.setFullYear(newValue.getFullYear());
        setDateValue(newDate);
      } else {
        setDateValue(undefined);
      }
    };
    const handleDateFieldBlur = () => {
      if (!dateValue && hasDateFieldDisplayValue) {
        setDateFieldStatus('error');
        setDateFieldErrorMessage('Invalid Date');
      } else {
        setDateFieldStatus('default');
      }
    };
    const formatDateForSR = (view, value) => {
      switch (view) {
        case 'year':
          return value.toLocaleDateString('en-US', { year: 'numeric' });
        case 'month':
          return value.toLocaleDateString('en-US', { year: 'numeric', month: 'long'});
        case 'day':
          return new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(value);
      }
    };
    const datePickerTextMap = { 
      clearButtonAriaLabel: 'Clear input',
      calendarAriaLabel: 'choose date',
      dayLabel: 'day',
      monthLabel: 'month',
      yearLabel: 'year',
      formatLabel: 'format',
      expectedDateFormatLabel: 'Expected Date Format:',
      requiredLabel: 'required',
      blank: 'blank',
      invalidEntry: 'invalid entry',
      invalidDay: 'invalid day',
      invalidMonth: 'invalid month',
      invalidYear: 'invalid year',
      nextMonthButtonLabel: 'next month',
      previousMonthButtonLabel: 'previous month',
      previousViewIsYearLabel: 'go to year view',
      previousViewIsMonthLabel: 'go to month view',
    };
    const multiSelectTextMap = {
        clearButton: 'Clear All',
        removeTagButtonAriaLabel: 'Remove {0}',
        selectedItem: '{0} selected',
        unselectedItem: '{0} not selected',
        moreSelectedOptionsLabel: '+{0} more'
    };
    return (
        <>
          <Button
            id="trigger-button-with-form"
            label="Example with Form"
            ref={triggerBtnFormRef}
            onClick={() => setOpen(!open)}
          />
          <SidePanel
            id='side-panel-form-example'
            open={open}
            ariaLabelledBy="side-panel-heading-id-form"
            ariaDescribedBy="side-panel-body-id-form"
            size="md"
            onClose={(e) => {
                setOpen(false);
                triggerBtnFormRef && triggerBtnFormRef.current.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
              id="side-panel-header-id-form"
            >
              <h3 id="side-panel-heading-id-form" className="evrHeading3">
                Sample form in SidePanel
              </h3>
            </SidePanelHeader>
            <SidePanelBody id="side-panel-body-id-form">
              <div style={{padding: '5px', width: '512px'}}>
                <TextField
                  id="input-primary-search"
                  testId="input-primary-search"
                  label="Search"
                  type="search"
                  value={searchValue ? searchValue : ''}
                  placeholder=""
                  onChange={onSearchValueChange}
                  status={numberError ? 'error' : 'default'}
                  statusMessage="Not a valid phone number. Check the number and try again."
                  required={true}
                ></TextField>
                <br />
                <TextField
                  id="input-primary-phone-number"
                  testId="input-primary-phone-number"
                  ref={phoneNumberInputRef}
                  label="Phone Number"
                  type="text"
                  value={phoneNumber ? phoneNumber : ''}
                  placeholder=""
                  onChange={onPhoneNumberChange}
                  status={numberError ? 'error' : 'default'}
                  statusMessage="Not a valid phone number. Check the number and try again."
                  required={true}
                ></TextField>
                <br />
                <Dropdown
                  id="dropdown-country"
                  testId="dropdown-country"
                  value={dropdownCountryValue}
                  onChange={handleDropdownCountryChange}
                  onClear={handleDropdownClearCountry}
                  options={options}
                  ref={countryDropdownRef}
                  label="Country 1"
                  status={dropdownCountryError ? 'error' : 'default'}
                  statusMessage="Not a valid Country, select different country."
                  required={true}
                ></Dropdown>
                <br />
                <Combobox
                  id="combobox-country"
                  label="Country 2"
                  inputValue={comboboxInputValue}
                  options={filteredOptions}
                  onInputValueChange={handleComboboxInputValueChange}
                  value={comboboxCountryValue}
                  onChange={handleComboboxCountryChange}
                  onClear={handleComboboxClearCountry}
                  onBlur={handleComboxCountryBlur}
                />
                <br />
                <DatePicker
                  id="datepicker-side-panel"
                  label="Appointment date"
                  dateSegmentPlaceholder={dateSegmentPlaceholder}
                  dateTimeParts={getDateTimeParts()}
                  weekdays={weekdays}
                  months={months}
                  value={dateValue}
                  onChange={handleDatePickerChange}
                  onBlur={handleDateFieldBlur}
                  helperTextPrefix="Recommended:"
                  helperText="within 60 days from today"
                  status={dateFieldStatus}
                  statusMessage={dateFieldErrorMessage}
                  formatDateForSR={formatDateForSR}
                  textMap={datePickerTextMap}
                />
                <br />
                <MultiSelect
                  id="multi-select-country"
                  testId="multi-select-country"
                  selectedOptions={multiSelectCountryValue}
                  inputValue={multiSelectInputValue}
                  onInputValueChange={handleMultiSelectInputValueChange}
                  onChange={handleMultiSelectCountryChange}
                  onClear={handleMultiSelectClearCountry}
                  options={options}
                  label="Country3 "
                  statusMessage="Not a valid Country, select different country."
                  required={true}
                  noResultsText={''}
                  ariaInputLabel={''}
                  ariaListboxResult={''}
                  textMap={multiSelectTextMap}
                ></MultiSelect>
                <br />
                <TextArea
                  id="text-area-primary-phone-number"
                  testId="text-area-primary-phone-number"
                  label="Comments"
                  value={textAreaValue ? textAreaValue : ''}
                  placeholder=""
                  onChange={onTextAreaChange}
                  statusMessage="Not a valid phone number. Check the number and try again."
                  required={true}
                ></TextArea>
              </div>
            </SidePanelBody>
            <SidePanelFooter id="side-panel-footer-id-form">
              <div style={{display: 'flex', justifyContent: 'space-between'}}>
                <Button
                  id="Reset-button-id-form"
                  variant='tertiary'
                  label="Reset"
                  onClick={() => {
                    alert("Reset!")
                  }}
                />
                <div style={{display: 'flex', justifyContent:'end'}}>
                  <div style={{paddingRight: '8px'}}>
                    <Button
                      id="close-button-id-form"
                      variant='secondary'
                      label="Close"
                      onClick={() => {
                        setOpen(false);
                        triggerBtnFormRef && triggerBtnFormRef.current.focus();
                      }}
                    />
                  </div>
                  <Button
                    id="continue-button-id-form"
                    label="Continue"
                    onClick={handleClick}
                  />
                </div>
              </div>
            </SidePanelFooter>
          </SidePanel>
        </>
    )}`;

<CodeExample scope={scope} code={sidePanelWithAForm} />

### Prevent `<body>` from scrolling when SidePanel is open

The feature team should prevent the document/page from scrolling when the SidePanel is open.

In this example we have targeted the `<body>`, but the feature team should target what's appropriate for them, most likely their MFE's `<main>` instead of `<body>`

export const sidePanelNoScroll = `() => {
    const [open, setOpen] = useState(false);
    const triggerBtnNoScrollRef = useRef(null);
    return (
        <>
          <Button
            id="trigger-button-with-no-scroll"
            label="Example with No background scroll"
            ref={triggerBtnNoScrollRef}
            onClick={() => setOpen(!open)}
          />
          <SidePanel
            id='side-panel-no-body-scroll-example'
            open={open}
            ariaLabelledBy="side-panel-heading-id-no-scroll"
            ariaDescribedBy="side-panel-body-id-no-scroll"
            size="md"
            onOpen={(e) => {
              //we have targeted the <body>, but the feature team should target what's appropriate for them, most likely their MFE's <main> instead of <body>
              document.body.style.overflowY = 'hidden';
            }}
            onClose={(e) => {
              //reset the scrolling effect
              document.body.style.overflowY = 'auto';
              setOpen(false);                
              triggerBtnNoScrollRef && triggerBtnNoScrollRef.current.focus();
            }}
          >
            <SidePanelHeader
                closeButtonAriaLabel={'close side panel'}
                onCloseButtonClick={(e) => {
                  setOpen(false);
                }}
                id="side-panel-header-id-no-scroll"
              >
                <h3 id="side-panel-heading-id-no-scroll" className="evrHeading3">
                  Heading
                </h3>
              </SidePanelHeader>
              <SidePanelBody id="side-panel-body-id-no-scroll">
                <p className="evrBodyText">Hello world!</p>
              </SidePanelBody>
          </SidePanel>
        </>
    )
}`;

<CodeExample scope={scope} code={sidePanelNoScroll} />

### Open Modal from SidePanel

See the below example of how one might open the <LinkTo kind="Components/Modal">Modal</LinkTo> from the `SidePanel`.

export const sidePanelWithModal = `() => {
  const [openSidePanel, setOpenSidePanel] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [lightBoxVariantSidePanel, setLightBoxVariantSidePanel] = useState('dark');
  const modalRef = useRef(null);
  const modalTriggerButtonRef = useRef(null);
  const triggerBtnRef = useRef(null);
  useEffect(() => {
    // delay switching the lightbox of the modal from dark
    // to clear to prevent flicker
    if (openModal && openSidePanel) {
      setTimeout(() => setLightBoxVariantSidePanel('clear'));
    } else {
      setLightBoxVariantSidePanel('dark');
    }
  }, [openModal, openSidePanel]);
  return (
    <>
      <Button
        id='trigger-button'
        label='Open Modal from SidePanel'
        ref={triggerBtnRef}
        onClick={() => setOpenSidePanel(true)}
      />
      <div>
        <SidePanel
          id='side-panel-modal'
          open={openSidePanel}
          ariaLabelledBy='side-panel-modal-heading'
          ariaDescribedBy='side-panel-modal-body'
          anchor='right'
          open={openSidePanel}
          size="md"
          onClose={(e) => {
            // only run when the modal is closed
            if (!openModal) {
              setOpenSidePanel(false);
              triggerBtnRef && triggerBtnRef.current.focus();
            }
          }}
          lightBoxVariant={lightBoxVariantSidePanel}
        >
          <SidePanelHeader
            closeButtonAriaLabel={'close side panel'}
            onCloseButtonClick={(e) => {
              setOpenSidePanel(false);
            }}
            id='side-panel-modal-header'
          >
            <h3 id="side-panel-modal-heading" className="evrHeading3">
              Heading
            </h3>
          </SidePanelHeader>
          <SidePanelBody id="side-panel-modal-body">
            <div style={{ margin: '0.5rem 0' }}>
              <Button 
                id="modal-button" 
                label="Open modal" 
                onClick={() => setOpenModal(true)} 
                ref={modalTriggerButtonRef}
              />
            </div>
          </SidePanelBody>
          <SidePanelFooter id="side-panel-footer">
            <p className="evrBodyText">Footer</p>
          </SidePanelFooter>
        </SidePanel>
      </div>
      <Modal
        open={openModal}
        size='md'
        id='modal'
        onClose={(e) => {
          openModal && setOpenModal(false);
          modalTriggerButtonRef && modalTriggerButtonRef.current.focus();
        }}
        ariaLabelledBy="modal-header"
        ariaDescribedBy="modal-body"
        ref={modalRef}
      >
        <ModalHeader
          title="Heading"
          onCloseButtonClick={(e) => {
            if (openModal) setOpenModal(false);
          }}
          id="modal-header-id"
          closeButtonAriaLabel="Close Modal"
        />
        <ModalBody id="modal-body">
          <ModalContentText id="modal-body-content" content="Content" />
        </ModalBody>
        <ModalFooter id="modal-footer">
          <ModalFooterActions
            id="modal-action-buttons"
            primaryAction={{
              id: 'primary-button',
              label: 'Continue',
              onClick: () => alert('Continue'),
            }}
            secondaryAction={{
              id: 'close-button',
              label: 'Close',
              onClick: () => {
                setOpenModal(false);
                const modalTriggerBtn = document.getElementById('modal-button');
                modalTriggerBtn && modalTriggerBtn.focus();                
            }}}
          />
        </ModalFooter>
      </Modal>
    </>
  );
}`;

<CodeExample scope={scope} code={sidePanelWithModal} />

### Prevent outside clicking from closing the SidePanel

export const preventOutsideClickCode = `() => {
  const [open, setOpen] = useState(false);
    const triggerBtnOutside = useRef(null);
    return (
        <>
          <Button
            id="trigger-button-prevent-outside-click"
            label="Prevent outside click to close SidePanel"
            ref={triggerBtnOutside}
            onClick={() => setOpen(!open)}
          />
          <SidePanel
            id='side-panel-prevent-outside-click-example'
            open={open}
            ariaLabelledBy="side-panel-heading-id-prevent-outside-click"
            ariaDescribedBy="side-panel-body-id-prevent-outside-click"
            size="md"
            onClose={(e) => {
              if (e && e.reason !== 'lightBoxClick'){
                setOpen(false);
                triggerBtnOutside && triggerBtnOutside.current.focus();
              }
            }}
          >
            <SidePanelHeader
                closeButtonAriaLabel={'close side panel'}
                onCloseButtonClick={(e) => {
                  setOpen(false);
                  setTimeout(()=> {
                    triggerBtnOutside && triggerBtnOutside.current.focus();
                  });
                }}
                id="side-panel-header-id-prevent-outside-click"
              >
                <h3 id="side-panel-heading-id-prevent-outside-click" className="evrHeading3">
                  Heading
                </h3>
              </SidePanelHeader>
              <SidePanelBody id="side-panel-body-id-prevent-outside-click">
                <p className="evrBodyText">Hello world!</p>
              </SidePanelBody>
          </SidePanel>
        </>
    )
}`;

<CodeExample scope={scope} code={preventOutsideClickCode} />

### Use `useEverestContext` to dynamically size the SidePanel

This example demonstrates a responsive SidePanel by dynamically updating the `size` prop using Everest Provider's `breakpoint` property from the `useEverestContext` hook.

export const responsiveSidePanel = `() => {
  const { breakpoint } = useEverestContext();
  const [open, setOpen] = useState(false);
  const [size, setSize] = useState('md');
  const triggerBtnUseEverestContext = useRef(null);
  useEffect(() => {
    switch (breakpoint) {
      case 'xs':
      case 'sm':
        setSize('fullscreen');
        break;
      case 'md':
        setSize('md');
        break;
      case 'lg':
        setSize('lg');
        break;
      case 'xl':
      case 'xxl':
        setSize('xl');
        break;
      default:
        setSize('md');
    }
  }, [breakpoint]);
  return (
    <>
      <Button
        id='trigger-button-use-everest-context'
        label={'Responsive Example (Breakpoint: ' + breakpoint + ') (Size: ' + size + ')'}
        ref={triggerBtnUseEverestContext}
        onClick={() => setOpen(!open)}
      />
      <SidePanel
        id='side-panel-use-everest-context-example'
        open={open}
        ariaLabelledBy='side-panel-heading-id-use-everest-context'
        ariaDescribedBy='side-panel-body-id-use-everest-context'
        size={size}
        onClose={() => {
          setOpen(false);
          triggerBtnUseEverestContext && triggerBtnUseEverestContext.current.focus();
        }}
      >
        <SidePanelHeader
          closeButtonAriaLabel={'close side panel'}
          onCloseButtonClick={() => {
            setOpen(false);
          }}
          id='side-panel-header-id-use-everest-context'
        >
          <h3 id="side-panel-heading-id-use-everest-context" className='evrHeading3'>Breakpoint: {breakpoint} <br /> Size: {size}</h3>
        </SidePanelHeader>
        <SidePanelBody id='side-panel-body-id-use-everest-context'>
          <p className="evrBodyText">Altering the size of the window will dynamically update the breakpoint value and the size of the SidePanel.</p>
        </SidePanelBody>
      </SidePanel>
    </>
  );
}`;

<CodeExample scope={scope} code={responsiveSidePanel} />
