# Side Panel

## Summary

Research and document implementations for the Everest Side Panel.

- Start Date: 2023-05-10
- Figma link: https://www.figma.com/file/yR0VRua16iizwZrN1SOiUH/%F0%9F%A7%AASide-Panel?type=design&node-id=5627-37591&t=wbq61u5p5rBD1Nfl-0

## Detailed Design

Side Panel anatomy consists of the following and is divided into sections:

- **Side Panel Container (mandatory):** - The parent container which sets the height and width of the Side Panel.
- **Header:** - Heading of the Side Panel and a mandatory close button.
- **Body:** - Takes different components to be displayed within the Side Panel.
- **Footer:** - The footer of the Side Panel can include buttons or tags

## API

### Side Panel

1.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the side panel .
2.  **id**: `string`
    Mandatory. Sets the id of the side panel .
3.  **children**: `ReactNode | undefined`
    Optional. Sets the section contents of the side panel .
4.  **open**: `boolean`
    Open the Side panel when it is set to true. Close the Side panel when it is set to false.
5.  **anchor**: `left | right`(Tanchor)
    Optional. Placement of the side panel . Default is "right". Other option is "left"
6.  **ariaLabelledBy**: `string | undefined`
    Optional. Sets the value for the **aria-labelledby** attribute of the side panel component. Recommneded data should be side panel header id or title
7.  **ariaDescribedBy**: `string | undefined`
    Optional. Sets the value for the **aria-describedby** attribute of the side panel component. Recommneded data should be side panel body id or title
8.  **onOpen**: `() => void | undefined`
    Optional. Sets a callback function that is executed when Side panel is opened.
9.  **onClose**: `() => void | undefined`
    Optional. Sets a callback function that is executed when Side panel is closed.
10. **disableCloseOnClickOutside**: `boolean`
    Optional. Default value is "false". if set to True will close the side panel when the user clicks outside the side panel component

### SidePanelHeader(Required)

1.  **id**: `string`
    Mandatory. Sets the id of the header.
2.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the header.
3.  **title**: `string`
    Mandatory. Sets the heading.
4.  **closeButtonAriaLabel**: string
    Mandatory. Sets the aria label on the close button
5.  **onCloseButtonClick**: `() => void | undefined`
    Optional. Sets a callback function that is executed on click of the close button.

### SidePanelBody(Required)

1.  **id**: `string`
    Mandatory. Sets the id of the body.
2.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the body.
3.  **children**: `ReactNode | undefined`
    Optional. Sets the content of the side panel .

### SidePanelFooter(Optional)

1.  **id**: `string`
    Mandatory. Sets the id of the footer.
2.  **testId**: `string | undefined`
    Optional. Sets **data-testid** attribute on the footer.
3.  **children**: `ReactNode | undefined`
    Optional. Sets the footer content of the side panel .

## Usage

For the Side panel component we will utilize the overlay component to be able to take up a certain section of the screen. Updating the styling in Overlay should be sufficient, see image below for more

![alt text](https://dev.azure.com/Ceridian/c0ecfdbf-2a9e-45cd-83a3-4f46b4e5f961/_apis/git/repositories/964723ec-efcb-4bb2-82bd-5796ae2c3db2/pullRequests/311516/attachments/image.png)

### disableCloseOnClickOutside field

Regarding `disableCloseOnClickOutside ` we will use the `onLightBoxClick` functionality that will allow us to close the side panel when it is clicked outside the side panel.

### anchor field

We will maintain consistency with other DS systems like MUI and semanticUI by ignoring global attributes `dir ='rtl'` and providing an `anchor` prop with "left" and "right" options

### Animations and sizing

For animations we will utilize the Toast animations currently and once design finalizes the animations we will update them accordingly.

One thing to watch out for is the sizing for the side panel, eventually we will have a size for each breakpoint as design decides, for now we will keep it at 20-25% of the screen for all screens except mobile. **On mobile it will take up the whole screen.**

### Overlay props

As per the designs we will use `lightBoxVariant="dark"`, it is still TBD whether we will need to add a prop to change the variants

### SidePanel dev implementation

```
()=>{
    const [open, setOpen] = useState(false);
    return (
        <Overlay id="side_panel_id"  onClose={} onOpen={} open={open} onLightBoxClick={setOpen(false)}>
            .......
        </Overlay >
    )
}

```

Currently there are many different proposed header styling, in order to accommodate all different header styling. Currently as part of Rapid development, we will export `OverlayHeader` as `SidePanelHeader`. This follows the pattern both Modal and Popover use currently

we will use the OverlayBody and OverlayFooter as we do for Modal and Popover components, for the body and footer related components

### SidePanel User implementation

```
()=>{
    const [open, setOpen] = useState(false);
    return (
        <SidePanel id="side_panel_id"  onClose={} onOpen={} open={open} disableCloseOnClickOutside={true}>
           <SidePanelHeader id="header_id" title="Heading" onCloseButtonClick={()=>{setOpen(false)}} />
            <SidePanelBody>
                 <div>Hello World!</div>
            </SidePanelBody>
            <SidePanelFooter id="footer_id">
                <button>Click me!</button>
            </SidePanelFooter>
        </SidePanel >
    )
}

```

## Accessibility

- Screenreaders by default will announce the heading and content text when focused.
- Side Panel will use `aria-labelledBy` and `aria-described` on the container
- <ESC> keyboard button is set to close the side panel .
- FocusTrap will be set inside the side panel until an action is performed.
- Users will have the option to click anywhere outside to close the side panel.
- if users want a navigation role, the side panel should be wrapped in a <nav> element for the screen reader to read out the correct role

## Q&A

**Should side panel have dimmable feature that dims the rest of the screen?**
The Side Panel could have an overlay covering the rest of the screen, not sure if the level of 'dim' should be adjustable or toggled on and off within the side panel itself. TBD on this by design

**Do we need to push content in any scenario rather than overlay the side panel?**
Right now based on the designs, we will have a floating component

**Will the side panel only open left/right ? Could it open Top/bottom as well?**
No top/bottom, those would be called a drawer and would be considered as a diff component

## Other Design Systems

### Material UI - https://mui.com/material-ui/react-drawer/

- Different sizes available
- Swippable functionality on Mobile

### Semantic UI - https://react.semantic-ui.com/modules/sidebar/

- Adds dimmable functionality to the page when the side bar is open
- Also handles pushable functionality which pushes the content rather than overlay

## Required Stories

### EAI-494 - Side Panel architecture

### EAI-501 - Create Side Panel component

### EAI-502 - Add to Ref app (as part of this we will ensure how this looks like in DayForce and z-indexes are correct)

## Change Log

10/17/2023: EDS-3564 - updated onClick callback to return a reason 'escapeKeyDown' when escape button is pressed and 'lightBoxClick' when clicked outside. Deprecated the `disableCloseOnClickOutside` prop.

10/19/2023: EDS-3336: Remove triggerRef prop from SidePanel as it is not used in the code due to an approach change.

11/7/2023: EDS-3672 - Removed the `disableCloseOnClickOutside` prop from SidePanel

02/06/2024: EDS-3338 - Added `addHeaderBorder` prop to SidePanelHeader to add a border to the header

02/20/2024: EDS-3339 - Added `size` and `addBorder` prop to SidePanelHeader to ensure the padding is correct for the header. Removed `addHeaderBorder` from base

08/22/2024: EDS-4191 - Removed the `headerChild` and `bodyChild` restriction, allowing any children to be accepted.

12/19/2024: PWEB-17672 - Add setTimeout inside SidePanelBase to allow focus back to previous element.
