@use '../../variables.scss';

.evrSidePanelHeaderBorder {
  border-bottom: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
}

.evrSidePanelHeader {
  padding: var(--evr-spacing-sm);
}

.evrSidePanelBody {
  padding: var(--evr-spacing-md) var(--evr-spacing-sm) 0 var(--evr-spacing-sm);
  overflow-y: auto;
  flex-grow: 1;
}

.evrSidePanelFooter {
  border-top: var(--evr-border-width-thin-px) solid var(--evr-borders-decorative-lowemp);
  padding: var(--evr-spacing-sm) var(--evr-spacing-sm) var(--evr-spacing-md) var(--evr-spacing-sm);
}

@media (min-width: variables.$overlayMobileScreenWidth) {
  .evrSidePanelFooter {
    padding: var(--evr-spacing-sm);
  }
}

//These padding classes are separate since they are only applied when not in fullscreen
.evrSidePanelHeaderPadding {
  padding-left: var(--evr-spacing-md);
  padding-right: var(--evr-spacing-md);
}

.evrSidePanelBodyPadding {
  padding-left: var(--evr-spacing-md);
  padding-right: var(--evr-spacing-md);
}

.evrSidePanelFooterPadding {
  padding: var(--evr-spacing-sm) var(--evr-spacing-md) var(--evr-spacing-md);
}

@media (min-width: variables.$overlayMobileScreenWidth) {
  .evrSidePanelFooterPadding {
    padding: var(--evr-spacing-sm) var(--evr-spacing-md);
  }
}
