import { <PERSON><PERSON>, <PERSON>, Canvas } from '@storybook/addon-docs';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '.';
import { Button } from '../button';
import { Chromatic, ChromaticDecorators, defaultModes } from '../../../chromatic';
import { Accordion, AccordionContent, AccordionDetail, AccordionSummary } from '../accordion';

<Meta
  title="Testing/Automation Test Cases/SidePanel"
  component={SidePanel}
  decorators={[ChromaticDecorators.setHeightTo100vh, ChromaticDecorators.setWidthTo100vw]}
  parameters={{
    chromatic: {
      ...Chromatic.ENABLE_CI,
      modes: {
        breakpointXs: { disable: true }, // turn off default and specify per story
      },
    },
  }}
/>

# Fullscreen Size

## Live Demo

<Canvas>
  <Story
    name="Fullscreen Size"
    parameters={{
      chromatic: { modes: { breakpointXs: defaultModes['breakpointXs'], breakpointSm: defaultModes['breakpointSm'] } },
    }}
  >
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnFullscreenRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-fullscreen"
            label="Open Panel"
            ref={triggerBtnFullscreenRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-fullscreen"
            open={open}
            size="fullscreen"
            onClose={(e) => {
              setOpen(false);
              triggerBtnFullscreenRef && triggerBtnFullscreenRef.current?.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
            <SidePanelFooter>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <Button
                  id="close-button-id-fullscreen"
                  variant="secondary"
                  label="Close"
                  onClick={() => {
                    setOpen(false);
                    triggerBtnFullscreenRef && triggerBtnFullscreenRef.current?.focus();
                  }}
                />
              </div>
            </SidePanelFooter>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# XL Size

## Live Demo

<Canvas>
  <Story name="XL Size" parameters={{ chromatic: { modes: { breakpointXl: defaultModes['breakpointXl'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnXLRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-xl"
            label="Open Panel"
            ref={triggerBtnXLRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-xl"
            open={open}
            size="xl"
            onClose={(e) => {
              setOpen(false);
              triggerBtnXLRef && triggerBtnXLRef.current?.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
            <SidePanelFooter>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <Button
                  id="close-button-id-xl"
                  variant="secondary"
                  label="Close"
                  onClick={() => {
                    setOpen(false);
                    triggerBtnXLRef && triggerBtnXLRef.current?.focus();
                  }}
                />
              </div>
            </SidePanelFooter>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# LG Size

## Live Demo

<Canvas>
  <Story name="LG Size" parameters={{ chromatic: { modes: { breakpointLg: defaultModes['breakpointLg'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnLGRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-lg"
            label="Open Panel"
            ref={triggerBtnLGRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-lg"
            open={open}
            size="lg"
            onClose={(e) => {
              setOpen(false);
              triggerBtnLGRef && triggerBtnLGRef.current?.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
            <SidePanelFooter>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <Button
                  id="close-button-id-lg"
                  variant="secondary"
                  label="Close"
                  onClick={() => {
                    setOpen(false);
                    triggerBtnLGRef && triggerBtnLGRef.current?.focus();
                  }}
                />
              </div>
            </SidePanelFooter>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# MD Size

## Live Demo

<Canvas>
  <Story name="MD Size" parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnMDRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-md"
            label="Open Panel"
            ref={triggerBtnMDRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-md"
            open={open}
            size="md"
            onClose={(e) => {
              setOpen(false);
              triggerBtnMDRef && triggerBtnMDRef.current?.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
            <SidePanelFooter>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <Button
                  id="close-button-id-md"
                  variant="secondary"
                  label="Close"
                  onClick={() => {
                    setOpen(false);
                    triggerBtnMDRef && triggerBtnMDRef.current?.focus();
                  }}
                />
              </div>
            </SidePanelFooter>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# SM Size

## Live Demo

<Canvas>
  <Story name="SM Size" parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointSm'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnSMRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-sm"
            label="Open Panel"
            ref={triggerBtnSMRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-sm"
            open={open}
            size="sm"
            onClose={(e) => {
              setOpen(false);
              triggerBtnSMRef && triggerBtnSMRef.current?.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
            <SidePanelFooter>
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <Button
                  id="close-button-id-sm"
                  variant="secondary"
                  label="Close"
                  onClick={() => {
                    setOpen(false);
                    triggerBtnMDRef && triggerBtnMDRef.current?.focus();
                  }}
                />
              </div>
            </SidePanelFooter>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# Left anchor

## Live Demo

<Canvas>
  <Story name="Left Anchor" parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}>
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnLeftAnchorRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-left-anchor"
            label="Open Panel from Left side"
            ref={triggerBtnLeftAnchorRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-left-anchor"
            open={open}
            size="md"
            anchor="left"
            onClose={(e) => {
              setOpen(false);
              triggerBtnLeftAnchorRef && triggerBtnLeftAnchorRef.current?.focus();
            }}
          >
            <SidePanelHeader
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world from the West Coast!</p>
            </SidePanelBody>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# Header Without Border

## Live Demo

<Canvas>
  <Story
    name="Header without border"
    parameters={{ chromatic: { modes: { breakpointMd: defaultModes['breakpointMd'] } } }}
  >
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnMDRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-without-border"
            label="Open Panel"
            ref={triggerBtnMDRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-without-border"
            open={open}
            size="md"
            onClose={(e) => {
              setOpen(false);
              triggerBtnMDRef && triggerBtnMDRef.current?.focus();
            }}
          >
            <SidePanelHeader
              addBorder={false}
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <h3 id="header-id-without-border" className="evrHeading3">
                Heading
              </h3>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# Header With buttons

## Live Demo

<Canvas>
  <Story
    name="Header With buttons"
    parameters={{ chromatic: { modes: { breakpointXl: defaultModes['breakpointXl'] } } }}
  >
    {() => {
      const [open, setOpen] = React.useState(true);
      const triggerBtnMDRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button-with-buttons"
            label="Open Panel"
            ref={triggerBtnMDRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <SidePanel
            id="side-panel-with-buttons"
            open={open}
            size="xl"
            onClose={(e) => {
              setOpen(false);
              triggerBtnMDRef && triggerBtnMDRef.current?.focus();
            }}
          >
            <SidePanelHeader
              addBorder={false}
              closeButtonAriaLabel={'close side panel'}
              onCloseButtonClick={(e) => {
                setOpen(false);
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex' }}>
                  <Button id="start-icon-btn" startIcon="chevronLeft" label="Starting" variant="tertiaryNeutral" />
                  <Button id="end-icon-btn" endIcon="chevronRight" label="Ending" variant="tertiaryNeutral" />
                </div>
                <div style={{ display: 'flex' }}>
                  <Button id="start-icon-btn" startIcon="chevronLeft" label="Starting" variant="tertiaryNeutral" />
                  <Button id="end-icon-btn" endIcon="chevronRight" label="Ending" variant="tertiaryNeutral" />
                  <Button id="start-icon-btn" startIcon="chevronLeft" label="Starting" variant="tertiaryNeutral" />
                </div>
              </div>
            </SidePanelHeader>
            <SidePanelBody>
              <p className="evrBodyText">Hello world!</p>
            </SidePanelBody>
          </SidePanel>
        </>
      );
    }}
  </Story>
</Canvas>

# Can render any Child

## Live Demo

<Canvas>
  <Story
    name="Can render any Child"
    parameters={{ chromatic: { modes: { breakpointXl: defaultModes['breakpointXl'] } } }}
  >
    {() => {
      const [open, setOpen] = React.useState(true);
      const [openDetailId, setOpenDetailId] = React.useState('accordion-stacked-card-1');
      const triggerBtnRef = React.useRef(null);
      return (
        <>
          <Button
            id="trigger-button"
            label="Open Panel"
            ref={triggerBtnRef}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <div>
            <SidePanel
              id="side-panel-any-child"
              open={open}
              anchor="right"
              size="md"
              onClose={(e) => {
                setOpen(false);
                triggerBtnRef && triggerBtnRef.current.focus();
              }}
              lightBoxVariant="dark"
            >
              <div style={{ width: '320px', alignSelf: 'center', marginTop: '1rem' }}>
                <Accordion
                  id="accordion-stacked-card"
                  openIconName="chevronUp"
                  closeIconName="chevronDown"
                  onToggle={(value) => {
                    if (value === openDetailId) {
                      setOpenDetailId('');
                    } else {
                      setOpenDetailId(value);
                    }
                  }}
                >
                  <AccordionDetail id="accordion-stacked-card-1" open={'accordion-stacked-card-1' === openDetailId}>
                    <AccordionSummary>
                      <h3 className="evrHeading3">Heading 1</h3>
                    </AccordionSummary>
                    <AccordionContent>
                      <p className="evrBodyText">
                        Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been
                        the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley
                        of type and scrambled it to make a type specimen book. It has survived not only five centuries,
                        but also the leap into electronic typesetting, remaining essentially unchanged. It was
                        popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages,
                        and more recently with desktop publishing software like Aldus PageMaker including versions of
                        Lorem Ipsum.
                      </p>
                    </AccordionContent>
                  </AccordionDetail>
                </Accordion>
              </div>
            </SidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>
