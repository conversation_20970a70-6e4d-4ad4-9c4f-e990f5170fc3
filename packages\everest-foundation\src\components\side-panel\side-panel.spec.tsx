import React, { useRef } from 'react';
import { render, waitFor, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { TextField } from '../text-field';

import { SidePanel, ISidePanel, SidePanelBody, SidePanelFooter, SidePanelHeader } from '.';

const sidepanelId = 'side-panel-id';
const sidePanelHeaderId = 'overlay-header-id';
const sidePanelHeadingId = 'overlay-heading-id';
const sidePanelBodyId = 'overlay-body-id';
const sidePanelFooterId = 'overlay-footer-id';

const sidePanelTestid = 'test-side-panel-id';
const sidePanelHeaderTestid = 'test-overlay-header-id';
const sidePanelBodyTestid = 'test-overlay-body-id';
const sidePanelFooterTestid = 'test-overlay-footer-id';
const onOpen = jest.fn();
const onClose = jest.fn();

const mockProps: ISidePanel = {
  id: sidepanelId,
  testId: sidePanelTestid,
  open: true,
  onOpen: onOpen,
  onClose: onClose,
  ariaLabelledBy: sidePanelHeadingId,
  ariaDescribedBy: sidePanelBodyId,
  anchor: 'right',
  ariaLabel: 'Side Panel test aria label',
  size: 'fullscreen',
};
const renderSidePanel = (open: boolean) => {
  return (
    <SidePanel {...mockProps} open={open}>
      <SidePanelHeader
        id={sidePanelHeaderId}
        testId={sidePanelHeaderTestid}
        closeButtonAriaLabel={''}
        onCloseButtonClick={() => null}
      >
        <h3 id={sidePanelHeadingId}>Heading</h3>
      </SidePanelHeader>
      <SidePanelBody id={sidePanelBodyId} testId={sidePanelBodyTestid}>
        <div>Hello world!</div>
      </SidePanelBody>
      <SidePanelFooter id={sidePanelFooterId} testId={sidePanelFooterTestid}>
        <div>Footer</div>
      </SidePanelFooter>
    </SidePanel>
  );
};

const RenderSidePanelWithTextField = () => {
  const textFieldRef = useRef<HTMLInputElement>(null);
  return (
    <SidePanel
      {...mockProps}
      open={true}
      onOpen={() => {
        setTimeout(() => {
          textFieldRef && textFieldRef.current?.focus();
        });
      }}
    >
      <SidePanelHeader
        id={sidePanelHeaderId}
        testId={sidePanelHeaderTestid}
        closeButtonAriaLabel={''}
        onCloseButtonClick={() => null}
      >
        <h3 id={sidePanelHeadingId}>Heading</h3>
      </SidePanelHeader>
      <SidePanelBody id={sidePanelBodyId} testId={sidePanelBodyTestid}>
        <div>Hello world!</div>
        <TextField ref={textFieldRef} label="This is a label" id="text-field-id" value="Test value" />
      </SidePanelBody>
      <SidePanelFooter id={sidePanelFooterId} testId={sidePanelFooterTestid}>
        <div>Footer</div>
      </SidePanelFooter>
    </SidePanel>
  );
};

const renderSidePanelNoHeaderNoBody = (open: boolean) => {
  return (
    <SidePanel {...mockProps} open={open}>
      <TextField label="This is a label" id="text-field-id" value="Test value" />
    </SidePanel>
  );
};

describe('Side Panel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  describe('should open or close as expected', () => {
    it('should render all the children when side panel is visible', () => {
      const { getByTestId } = render(renderSidePanel(true));
      expect(getByTestId(sidePanelTestid)).toBeInTheDocument();
      expect(getByTestId(sidePanelHeaderTestid)).toBeInTheDocument();
      expect(getByTestId(sidePanelBodyTestid)).toBeInTheDocument();
      expect(getByTestId(sidePanelFooterTestid)).toBeInTheDocument();
    });

    it('should dispatch onClose when side panel is open then closes', async () => {
      const { rerender } = render(renderSidePanel(true));
      await waitFor(() => expect(onOpen).not.toHaveBeenCalled());
      rerender(renderSidePanel(false));
      await waitFor(() => expect(onClose).toHaveBeenCalled());
    });

    it('should dispatch onOpen when side panel is closed then opens', async () => {
      const { rerender } = render(renderSidePanel(false));
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderSidePanel(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
    });

    it('should run onOpen once and onClose once when starting closed, then opened, then closed ', async () => {
      const { rerender } = render(renderSidePanel(false));
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderSidePanel(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalledTimes(1));
      rerender(renderSidePanel(false));
      await waitFor(() => expect(onClose).toHaveBeenCalledTimes(1));
    });

    it('should run onOpen once and onClose once when starting open, then closed ', async () => {
      const { rerender } = render(renderSidePanel(true));
      await waitFor(() => expect(onOpen).toHaveBeenCalled());
      expect(onClose).not.toHaveBeenCalled();
      rerender(renderSidePanel(false));
      await waitFor(() => {
        expect(onClose).toHaveBeenCalledTimes(1);
        expect(onOpen).toHaveBeenCalledTimes(1);
      });
    });

    it('onClose should return a reason escapeKeyDown when Escape keyboard button is pressed', async () => {
      render(renderSidePanel(true));
      await waitFor(() => expect(onOpen).not.toHaveBeenCalled());
      // we need to focus the side panel
      screen.getByTestId(sidePanelTestid).focus();
      await userEvent.keyboard('{Escape}');
      await waitFor(() => expect(onClose).toHaveBeenCalledWith({ reason: 'escapeKeyDown' }));
    });

    it('can render any child', async () => {
      render(renderSidePanelNoHeaderNoBody(true));
      await waitFor(() => expect(onOpen).not.toHaveBeenCalled());
      await waitFor(() => expect(screen.getByRole('textbox')).toBeInTheDocument());
    });
  });

  describe('click outside', () => {
    const lightBoxTestId = `${sidePanelTestid}-overlay-light-box`;

    it('should call onClose function on side panel when clicked outside', async () => {
      render(renderSidePanel(true));
      // clicking on the side panel itself should not close it
      await userEvent.click(screen.getByTestId(sidePanelTestid));
      expect(onClose).not.toHaveBeenCalled();
      await userEvent.click(screen.getByTestId(lightBoxTestId));
      await waitFor(() => expect(onClose).toHaveBeenCalledWith({ reason: 'lightBoxClick' }));
    });
  });
  // With this custom focus functionality, screen reader fails to read the whole content of the dialog when dialog opens,
  // but in terms of UI, it works as expected
  describe('custom focus functionality', () => {
    it('should focus on Text Field when SidePanel opens and focus is intentionally set on the text field', async () => {
      render(<RenderSidePanelWithTextField />);
      await waitFor(() => expect(screen.getByRole('textbox')).toHaveFocus());
    });
  });
});
