import { <PERSON>a, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useRef, useEffect } from 'react';
import { SidePanel, SidePanelHeader, SidePanelBody, SidePanelFooter } from '.';
import { Button } from '../button';
import Examples from './side-panel.examples.mdx';
import { useArgs } from '@storybook/client-api';
import { action } from '@storybook/addon-actions';
import { LinkTo } from '../../../.storybook/docs/shared/link-to';

<Meta
  title="Components/SidePanel"
  component={SidePanel}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/yR0VRua16iizwZrN1SOiUH/%F0%9F%A7%AASide-Panel?type=design&node-id=5520-32197&t=xkaz3RHvJij0p6YA-0',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      control: 'text',
      description: 'Sets the `id` attribute on the side panel',
    },
    open: {
      description: 'Opens or closes the side panel.',
    },
    onClose: {
      control: '-',
      description:
        "Callback that runs when the side panel closes. Returns an object {reason: 'escapeKeyDown'} when the escape key is pressed and {reason: 'lightBoxClick'} when LightBox is clicked.",
    },
    onOpen: {
      control: '-',
      description: 'Callback that runs when the side panel opens.',
    },
    anchor: {
      type: 'enum',
      control: 'inline-radio',
      options: ['left', 'right'],
      description: 'The placement of the side panel relative to the screen',
      table: {
        defaultValue: { summary: 'right' },
      },
    },
    ariaLabelledBy: {
      type: 'string',
      control: '-',
      description: 'Id of the content inside SidePanelHeader.',
    },
    ariaDescribedBy: {
      type: 'string',
      control: '-',
      description: 'Id of the SidePanelBody.',
    },
    ariaLabel: {
      type: 'string',
      description: 'Sets the value for **aria-label** attribute on the side panel.',
    },
    size: {
      type: 'enum',
      control: 'radio',
      options: ['fullscreen', 'sm', 'md', 'lg', 'xl'],
      description: 'Sets the width of the side panel.',
    },
    lightBoxVariant: {
      control: 'inline-radio',
      options: ['dark', 'clear'],
      description: 'Define the lightbox variant. **This is an experimental feature and may be removed at any time.**',
      table: {
        defaultValue: { summary: 'dark' },
      },
    },
  }}
  args={{
    id: 'side-panel-id',
    open: false,
    ariaLabelledBy: 'side-panel-heading-id',
    ariaDescribedBy: 'side-panel-body-id',
    anchor: 'right',
    size: 'lg',
    lightBoxVariant: 'dark',
  }}
/>

# SidePanel

<Examples />

## Live Demo

<Canvas>
  <Story name="SidePanel">
    {(args) => {
      const [{ open }, updateArgs] = useArgs();
      const triggerBtnRef = useRef(null);
      return (
        <>
          <Button
            id="trigger-button-id"
            label="SidePanel"
            ref={triggerBtnRef}
            onClick={() => updateArgs({ open: !open })}
          />
          <div>
            <SidePanel
              {...args}
              onOpen={(e) => {
                action('onOpen')(e);
              }}
              onClose={(e) => {
                action('onClose')(e);
                updateArgs({ open: false });
                triggerBtnRef.current?.focus();
              }}
              lightBoxVariant={args.lightBoxVariant}
            >
              <SidePanelHeader
                addBorder={true}
                closeButtonAriaLabel={'close side panel'}
                onCloseButtonClick={(e) => {
                  action('onCloseButtonClick')(e);
                  updateArgs({ open: false });
                }}
                id="side-panel-header-id"
              >
                <h3 id="side-panel-heading-id" className="evrHeading3">
                  Heading
                </h3>
              </SidePanelHeader>
              <SidePanelBody id="side-panel-body-id">
                <p className="evrBodyText">Hello world!</p>
              </SidePanelBody>
              <SidePanelFooter id="side-panel-footer-id">
                <p className="evrBodyText">Footer</p>
              </SidePanelFooter>
            </SidePanel>
          </div>
        </>
      );
    }}
  </Story>
</Canvas>

## Props

### Side Panel Props

<ArgsTable story="SidePanel" />

### SidePanelHeader Props

`addBorder` is an Optional prop. It allows to add/remove a border to the header of the side panel.
In cases like <LinkTo kind="Components/Tabs/Tab Group">TabGroup</LinkTo>, it is recommended to set it to `false`.

<ArgsTable of={SidePanelHeader} />

### SidePanelBody Props

<ArgsTable of={SidePanelBody} />

### SidePanelFooter Props

<ArgsTable of={SidePanelFooter} />
