import React, { PropsWithChildren } from 'react';

import { SidePanelBase, ISidePanelBase } from '../side-panel-base';

export type ISidePanel = ISidePanelBase;

export const SidePanel = React.forwardRef<HTMLDivElement, PropsWithChildren<ISidePanel>>((props, ref): JSX.Element => {
  const {
    id,
    testId,
    children,
    open,
    onOpen,
    onClose,
    ariaLabel,
    ariaLabelledBy,
    ariaDescribedBy,
    anchor = 'right',
    size,
    lightBoxVariant,
  } = props;

  return (
    <SidePanelBase
      id={id}
      testId={testId}
      open={open}
      onOpen={onOpen}
      onClose={onClose}
      ariaLabel={ariaLabel}
      ariaLabelledBy={ariaLabelledBy}
      ariaDescribedBy={ariaDescribedBy}
      anchor={anchor}
      size={size}
      lightBoxVariant={lightBoxVariant}
      ref={ref}
    >
      {children}
    </SidePanelBase>
  );
});

SidePanel.displayName = 'SidePanel';
