import { SimpleFilterControlGroup, SimpleFilterMultiSelect, SimpleFilterToggle, useSimpleFiltersState } from '../';
import { SimpleFilterTagGroup } from '../../simple-filter-tag-group';
import { Tag } from '../../tag';
import { CodeExample } from '../../../../.storybook/doc-blocks/example/example.tsx';
import { LinkTo } from '../../../../.storybook/docs/shared/link-to.tsx';

export const scope = {
  SimpleFilterTagGroup,
  Tag,
  SimpleFilterControlGroup,
  SimpleFilterMultiSelect,
  SimpleFilterToggle,
  useSimpleFiltersState
};

This example showcases usage of the SimpleFilter-related pieces:
1. <LinkTo kind="Components/SimpleFilter/SimpleFilterControlGroup/Overview">SimpleFilterControlGroup</LinkTo> component and its associated hook, <LinkTo kind="Components/SimpleFilter/SimpleFilterControlGroup/Overview">useSimpleFiltersState</LinkTo>
1. <LinkTo kind="Components/SimpleFilter/SimpleFilterTagGroup">SimpleFilterTagGroup</LinkTo> component

export const defaultCode = `() => {
  const firstSimpleFilterMultiSelectRef = useRef<ISimpleFilterMultiSelectRef>(null);
  // Unified filter definitions for the demo purposes. Data fetched from an API will most likely have completely different structure
  const optionsFilterDefinitions: Record<string, { label: string; options: ISimpleFilterCollectionControlItem[] }> =
    React.useMemo(
      () => ({
        skills: {
          label: 'Skills',
          options: [
            { id: 'react', label: 'React' },
            { id: 'angular', label: 'Angular' },
            { id: 'vue', label: 'Vue' },
            { id: 'cicd', label: 'CI/CD' },
            { id: 'nodejs', label: 'Node.js' },
            { id: 'figma', label: 'Figma' },
            { id: 'swift', label: 'Swift' },
            { id: 'javascript', label: 'Javascript' },
            { id: 'python', label: 'Python' },
            { id: 'design', label: 'UI/UX Design' },
            { id: 'pm', label: 'Project Management' },
            { id: 'git', label: 'Git' },
            { id: 'playwright', label: 'Playwright' },
            { id: 'chromatic', label: 'Chromatic' },
            { id: 'webpack', label: 'Webpack' },
            { id: 'graphql', label: 'GraphQL' },
          ],
        },
        position: {
          label: 'Position',
          options: [
            { level: 'entry', label: 'Entry Level' },
            { level: 'mid', label: 'Mid Level' },
            { level: 'senior', label: 'Senior Level' },
            { level: 'lead', label: 'Developer Lead' },
            { level: 'architect', label: 'Architect' },
            { level: 'staff', label: 'Staff' },
          ],
        },
        status: {
          label: 'Status',
          options: [
            { statusId: 1, label: 'Applied' },
            { statusId: 2, label: 'Screening' },
            { statusId: 3, label: 'Interview' },
            { statusId: 4, label: 'Offer' },
            { statusId: 5, label: 'Rejected' },
            { statusId: 6, label: 'Accepted' },
          ],
        },
      }),
      []
    );

  const toggleOptionsFilterDefinitions = useMemo(
    () => ({
      isRemote: {
        label: 'Remote',
        count: 23,
      },
      isReferred: {
        label: 'Referred',
        count: 9,
      },
      isTopQualified: {
        label: 'Top Qualified',
        count: 5,
      },
    }),
    []
  );
  const onFiltersChange = (filters: ISimpleFilterControlState[]) => {
    console.log('Filters updated:', filters);
  };

  const initialState: ISimpleFilterControlState[] = [
    {
      name: 'skills',
      value: optionsFilterDefinitions.skills.options.filter((option, index) => index < 3),
    },
    { name: 'status', value: [optionsFilterDefinitions.status.options[0]] },
    { name: 'isRemote', value: true },
  ];

  const { filters, getFilter, updateFilter, resetFilters, clearFilters } = useSimpleFiltersState(
    initialState,
    onFiltersChange
  );

  const onRemoveTag = (
    filterState: ISimpleFilterControlState<ISimpleFilterCollectionControlItem[]>,
    filterValue: ISimpleFilterCollectionControlItem
  ): void => {
    const updatedFilters = filterState.value.filter((v) => v.label !== filterValue.label);
    const allFiltersUpdated = updateFilter(filterState.name, updatedFilters);

    const hasArrayFiltersWithValues = allFiltersUpdated.some((f) => Array.isArray(f.value) && f.value.length > 0);

    if (!hasArrayFiltersWithValues) {
      firstSimpleFilterMultiSelectRef.current?.triggerButton?.focus();
    }
  };

  const onClearFilters = (): void => {
    clearFilters();
    firstSimpleFilterMultiSelectRef.current?.triggerButton?.focus();
  };

  return (
    <div style={{paddingInline: '30px', width: '100%'}}>
      <SimpleFilterControlGroup
        id="typed-group"
        ariaLabel="candidate filters"
        onFiltersCountChange={console.log}
      >
        {Object.entries(optionsFilterDefinitions).map(([key, { label, options }], ind) => (
          <SimpleFilterMultiSelect
            key={key}
            // when filters are cleared, we will use this ref to bring the focus to the first filter control
            ref={ind === 0 ? firstSimpleFilterMultiSelectRef : undefined}
            id={key + "-selection-menu"}
            options={options}
            onApply={(selected) => updateFilter(key, selected)}
            selectedOptions={getFilter<ISimpleFilterCollectionControlItem[]>(key)?.value}
            textMap={{
              applyButtonLabel: 'Apply',
              clearButtonLabel: 'Clear',
              listAriaLabel: label,
              selectionClearedMessage: 'Selection cleared',
            }}
          >
            {label}
          </SimpleFilterMultiSelect>
        ))}

        {Object.entries(toggleOptionsFilterDefinitions).map(([key, { label, count }]) => (
          <SimpleFilterToggle
            key={key}
            id={key + "-toggle"}
            onClick={() => updateFilter(key, !getFilter<boolean>(key)?.value)}
            selected={getFilter<boolean>(key)?.value}
          >
            {label + "(" + count + ")"}
          </SimpleFilterToggle>
        ))}
      </SimpleFilterControlGroup>

      <SimpleFilterTagGroup
        onClearAll={onClearFilters}
        id="tag-group"
        textMap={{
          showXMoreButtonLabel: '+{0} more',
          showLessButtonLabel: 'Show less',
          clearAllButtonLabel: 'Clear all',
          filtersClearedMessage: 'Filters cleared',
        }}
      >
        {filters
          .filter(
            (filterState): filterState is ISimpleFilterControlState<ISimpleFilterCollectionControlItem[]> =>
              Array.isArray(filterState.value) // Type guard for array values
          )
          .flatMap((filterState) => {
            const filterLabel = optionsFilterDefinitions[filterState.name]?.label || filterState.name;

            return filterState.value.map((filterValue, index) => (
              <Tag
                id={filterState.name + "-" + index + "-" + filterValue.label}
                key={filterState.name + "-" + index + "-" + filterValue.label}
                label={filterLabel + ": " + filterValue.label}
                onRemove={function () { return onRemoveTag(filterState, filterValue); }}
                removeButtonAriaLabel={"Remove filter " + filterLabel + ": " + filterValue.label}
              />
            ));
          })}
      </SimpleFilterTagGroup>
    </div>
  );
}`;

<CodeExample scope={scope} code={defaultCode} />