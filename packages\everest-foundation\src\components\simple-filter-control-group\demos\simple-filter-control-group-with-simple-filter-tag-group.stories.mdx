import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from '@storybook/addon-docs';
import { SimpleFilterControlGroup, SimpleFilterMultiSelect, SimpleFilterToggle, useSimpleFiltersState } from '../index.ts';
import { SimpleFilterControlGroupDemoWithHookTyped } from './simple-filter-control-group-with-hook-typed.tsx'
import { LinkTo } from '../../../../.storybook/docs/shared/link-to.tsx';
import Examples from './simple-filter-control-group-with-simple-filter-tag-group.examples.mdx';

<Meta
  title="Components/SimpleFilter/Example"
  component={SimpleFilterControlGroup}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&m=dev',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    ariaLabel: {
      type: 'string',
      description:
        "Aria-label for the componenent's container.",
    },
    onFiltersCountChange: {
      control: '-',
      description:
        "Fires when number of the displayed controls changes passing the current number.",
    },
  }}
  args={{
    id: 'basic-simple-filter-control-group-demo',
    testId: 'basic-simple-filter-control-group-demo-test-id',
    ariaLabel: 'Filtering controls',
  }}
/>

# SimpleFilter

<Examples/>

## Live Demo

<Canvas>
  <Story name="Example" args={{showHookControls: false}}>
    {(args) => {
      return (
        <div style={{border: '2px dashed lightgrey'}}>
          <SimpleFilterControlGroupDemoWithHookTyped {...args} showTags />
        </div>
      );
    }}
  </Story>
</Canvas>