import { <PERSON>a, <PERSON>, <PERSON><PERSON> } from '@storybook/addon-docs';
import { SimpleFilterControlGroup, SimpleFilterMultiSelect, SimpleFilterToggle, useSimpleFiltersState } from '../';
import { SimpleFilterControlGroupDemoWithHookTyped } from './simple-filter-control-group-with-hook-typed.tsx';
import { LinkTo } from '../../../../.storybook/docs/shared/link-to.tsx';
import Examples from './simple-filter-control-group-with-useSimpleFiltersState.examples.mdx';

<Meta
  title="Components/SimpleFilter/SimpleFilterControlGroup/Basic Example (With useSimpleFiltersState)"
  component={SimpleFilterControlGroup}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&m=dev',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    ariaLabel: {
      type: 'string',
      description: "Aria-label for the componenent's container.",
    },
    onFiltersCountChange: {
      control: '-',
      description: 'Fires when number of the displayed controls changes passing the current number.',
    },
  }}
  args={{
    id: 'basic-simple-filter-control-group-demo',
    testId: 'basic-simple-filter-control-group-demo-test-id',
    ariaLabel: 'Filtering controls',
  }}
/>

# SimpleFilterControlGroup

<Examples />

## Live Demo

To explore more about filtering state, enable the **showHookControls** control on the <LinkTo kind="Components/SimpleFilter/SimpleFilterControlGroup/Basic Example (With useSimpleFiltersState)" story="basic-example-with-use-simple-filters-state">demo page</LinkTo>, which provides additional insights and controls for managing the filtering state.

The `useSimpleFiltersState` hook's documentation can be found on the <LinkTo kind="Components/SimpleFilter/SimpleFilterControlGroup/Overview">overview page</LinkTo>

<Canvas>
  <Story name="Basic Example (With useSimpleFiltersState)" args={{ showHookControls: false }}>
    {(args) => {
      return (
        <div style={{ border: '2px dashed lightgrey' }}>
          <SimpleFilterControlGroupDemoWithHookTyped {...args} />
        </div>
      );
    }}
  </Story>
</Canvas>
