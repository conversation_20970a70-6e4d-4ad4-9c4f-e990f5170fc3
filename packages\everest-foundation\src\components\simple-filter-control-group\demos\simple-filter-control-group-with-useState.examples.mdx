import { SimpleFilterControlGroup, SimpleFilterMultiSelect, SimpleFilterToggle } from '../';
import { CodeExample } from '../../../../.storybook/doc-blocks/example/example.tsx';

export const scope = {
  SimpleFilterControlGroup,
  SimpleFilterMultiSelect,
  SimpleFilterToggle,
}

## SimpleFilterControlGroup Example Using useState Hook

export const defaultCode = `() => {
  const skillsOptions = useMemo(() => [
    { id: 'react', label: 'React' },
    { id: 'angular', label: 'Angular' },
    { id: 'vue', label: 'Vue' },
    { id: 'cicd', label: 'CI/CD' },
    { id: 'nodejs', label: 'Node.js' },
    { id: 'figma', label: 'Figma' },
    { id: 'swift', label: 'Swift' },
    { id: 'javascript', label: 'Javascript' },
    { id: 'python', label: 'Python' },
    { id: 'design', label: 'UI/UX Design' },
    { id: 'pm', label: 'Project Management' },
    { id: 'git', label: 'Git' },
    { id: 'playwright', label: 'Playwright' },
    { id: 'chromatic', label: 'Chromatic' },
    { id: 'webpack', label: 'Webpack' },
    { id: 'graphql', label: 'GraphQL' },
  ], []);
  const [selectedSkills, setSelectedSkills] = useState([]);

  const positionOptions = useMemo(() => [
    { level: 'entry', label: 'Entry Level' },
    { level: 'mid', label: 'Mid Level' },
    { level: 'senior', label: 'Senior Level' },
    { level: 'lead', label: 'Developer Lead' },
    { level: 'architect', label: 'Architect' },
    { level: 'staff', label: 'Staff' },
  ], []);
  const [selectedPositions, setSelectedPositions] = useState([]);

  const statusOptions = useMemo(() => [
    { statusId: 1, label: 'Applied' },
    { statusId: 2, label: 'Screening' },
    { statusId: 3, label: 'Interview' },
    { statusId: 4, label: 'Offer' },
    { statusId: 5, label: 'Rejected' },
    { statusId: 6, label: 'Accepted' },
  ], []);
  const [selectedStatus, setSelectedStatus] = useState([]);

  const [isRemoteApplicant, setIsRemoteApplicant] = useState(false);
  const [isTopQualifiedApplicant, setIsTopQualifiedApplicant] = useState(false);
  const [isReferredApplicant, setIsReferredApplicant] = useState(false);

  const filtersCountChangeHandler = (count: number) => {
    // Use this to decide when more complete filtering view should be updated to add any relevant filtering controls removed from SimpleFilterControlGroup
    console.log('Displayed filters count:', count);
  }

  return (
    <div style={{paddingInline: '30px', width: '100%'}}>
      <SimpleFilterControlGroup id='demo-group' onFiltersCountChange={filtersCountChangeHandler}>

        <SimpleFilterMultiSelect
          id='skills-selection-menu'
          options={skillsOptions}
          onApply={options => setSelectedSkills(options)}
          selectedOptions={selectedSkills}
          textMap={{
            applyButtonLabel:'Apply',
            clearButtonLabel: 'Clear',
            listAriaLabel: 'Skills options',
            selectionClearedMessage: 'Selection cleared'
          }}
        >
          Skills
        </SimpleFilterMultiSelect>

        <SimpleFilterMultiSelect
          id="position-selection-menu"
          options={positionOptions}
          onApply={options => setSelectedPositions(options)}
          selectedOptions={selectedPositions}
          textMap={{
            applyButtonLabel: 'Apply',
            clearButtonLabel: 'Clear',
            listAriaLabel: 'Position',
            selectionClearedMessage: 'Selection cleared'
          }}
        >
          Position
        </SimpleFilterMultiSelect>

        <SimpleFilterMultiSelect
          id='status-selection-menu'
          options={statusOptions}
          onApply={options => setSelectedStatus(options)}
          selectedOptions={selectedStatus}
          textMap={{
            applyButtonLabel:'Apply',
            clearButtonLabel: 'Clear',
            listAriaLabel: 'Status options',
            selectionClearedMessage: 'Selection cleared'
          }}
        >
          Status
        </SimpleFilterMultiSelect>
        
        <SimpleFilterToggle id='is-top-qualified-toggle' selected={isTopQualifiedApplicant} onClick={() => setIsTopQualifiedApplicant(!isTopQualifiedApplicant)}>Top Qualified (5)</SimpleFilterToggle>
        <SimpleFilterToggle id='is-remote-toggle' selected={isRemoteApplicant} onClick={() => setIsRemoteApplicant(!isRemoteApplicant)}>Remote (23)</SimpleFilterToggle>
        <SimpleFilterToggle id='is-referred-toggle' selected={isReferredApplicant} onClick={() => setIsReferredApplicant(!isReferredApplicant)}>Referred (9)</SimpleFilterToggle>

      </SimpleFilterControlGroup>
    </div>
  );
}`;

<CodeExample scope={scope} code={defaultCode} />