import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { action } from '@storybook/addon-actions';
import { SimpleFilterControlGroup, SimpleFilterMultiSelect, SimpleFilterToggle } from '../';
import Examples from './simple-filter-control-group-with-useState.examples.mdx';

<Meta
  title="Components/SimpleFilter/SimpleFilterControlGroup/Basic Example (With useState)"
  component={SimpleFilterControlGroup}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&m=dev',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    ariaLabel: {
      type: 'string',
      description: "Aria-label for the componenent's container.",
    },
    onFiltersCountChange: {
      control: '-',
      description: 'Fires when number of the displayed controls changes passing the current number.',
    },
  }}
  args={{
    id: 'basic-simple-filter-control-group-demo',
    testId: 'basic-simple-filter-control-group-demo-test-id',
    ariaLabel: 'Filtering controls',
  }}
/>

# SimpleFilterControlGroup

<Examples />

## Live Demo

<Canvas>
  <Story name="Basic Example (with useState)">
    {(args) => {
      // Selection Controls` state
      const skillsOptions = React.useMemo(() => [
       { id: 'react', label: 'React' },
        { id: 'angular', label: 'Angular' },
        { id: 'vue', label: 'Vue' },
        { id: 'cicd', label: 'CI/CD' },
        { id: 'nodejs', label: 'Node.js' },
        { id: 'figma', label: 'Figma' },
        { id: 'swift', label: 'Swift' },
        { id: 'javascript', label: 'Javascript' },
        { id: 'python', label: 'Python' },
        { id: 'design', label: 'UI/UX Design' },
        { id: 'pm', label: 'Project Management' },
        { id: 'git', label: 'Git' },
        { id: 'playwright', label: 'Playwright' },
        { id: 'chromatic', label: 'Chromatic' },
        { id: 'webpack', label: 'Webpack' },
        { id: 'graphql', label: 'GraphQL' },
      ], []);
      const [selectedSkills, setSelectedSkills] = React.useState([]);
      
      const positionOptions = React.useMemo(() => [
        { level: 'entry', label: 'Entry Level' },
        { level: 'mid', label: 'Mid Level' },
        { level: 'senior', label: 'Senior Level' },
        { level: 'lead', label: 'Developer Lead' },
        { level: 'architect', label: 'Architect' },
        { level: 'staff', label: 'Staff' },
      ], []);
      const [selectedPositions, setSelectedPositions] = React.useState([]);
      
      const statusOptions = React.useMemo(() => [
        { statusId: 1, label: 'Applied' },
        { statusId: 2, label: 'Screening' },
        { statusId: 3, label: 'Interview' },
        { statusId: 4, label: 'Offer' },
        { statusId: 5, label: 'Rejected' },
        { statusId: 6, label: 'Accepted' },
      ], []);
      const [selectedStatus, setSelectedStatus] = React.useState([]);

      // Toggle buttons` state
      const [isRemoteApplicant, setIsRemoteApplicant] = React.useState(false);
      const [isTopQualifiedApplicant, setIsTopQualifiedApplicant] = React.useState(false);
      const [isReferredApplicant, setIsReferredApplicant] = React.useState(false);

      const filtersCountChangeHandler = (count) => {
        // Use this to decide when more complete filtering view should be updated to add any relevant filtering controls removed from SimpleFilterControlGroup
        action('onFiltersCountChange')(count);
      }
      return (
        <div style={{border: '2px dashed lightgrey'}}>
          <SimpleFilterControlGroup {...args} onFiltersCountChange={filtersCountChangeHandler}>

            <SimpleFilterMultiSelect
              id='skills-selection-menu'
              options={skillsOptions}
              onApply={options => setSelectedSkills(options)}
              selectedOptions={selectedSkills}
              textMap={{
                applyButtonLabel:'Apply',
                clearButtonLabel: 'Clear',
                listAriaLabel: 'Skills options',
                selectionClearedMessage: 'Selection cleared'
              }}
            >
              Skills
            </SimpleFilterMultiSelect>

            <SimpleFilterMultiSelect
              id="position-selection-menu"
              options={positionOptions}
              onApply={options => setSelectedPositions(options)}
              selectedOptions={selectedPositions}
              textMap={{
                applyButtonLabel: 'Apply',
                clearButtonLabel: 'Clear',
                listAriaLabel: 'Position',
                selectionClearedMessage: 'Selection cleared'
              }}
            >
              Position
            </SimpleFilterMultiSelect>

            <SimpleFilterMultiSelect
              id='status-selection-menu'
              options={statusOptions}
              onApply={options => setSelectedStatus(options)}
              selectedOptions={selectedStatus}
              textMap={{
                applyButtonLabel:'Apply',
                clearButtonLabel: 'Clear',
                listAriaLabel: 'Status options',
                selectionClearedMessage: 'Selection cleared'
              }}
            >
              Status
            </SimpleFilterMultiSelect>

            <SimpleFilterToggle id='is-top-qualified-toggle' selected={isTopQualifiedApplicant} onClick={() => setIsTopQualifiedApplicant(!isTopQualifiedApplicant)}>Top Qualified (5)</SimpleFilterToggle>
            <SimpleFilterToggle id='is-remote-toggle' selected={isRemoteApplicant} onClick={() => setIsRemoteApplicant(!isRemoteApplicant)}>Remote (23)</SimpleFilterToggle>
            <SimpleFilterToggle id='is-referred-toggle' selected={isReferredApplicant} onClick={() => setIsReferredApplicant(!isReferredApplicant)}>Referred (9)</SimpleFilterToggle>

          </SimpleFilterControlGroup>
        </div>
      )}
    }

  </Story>
</Canvas>
