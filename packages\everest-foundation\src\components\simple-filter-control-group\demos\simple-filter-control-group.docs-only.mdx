
import { SimpleFilterControlGroup } from '../simple-filter-control-group';
import { LinkTo } from '../../../../.storybook/docs/shared/link-to';
import { Meta, ArgsTable } from '@storybook/addon-docs';


export const scope = { SimpleFilterControlGroup };

<Meta title="Components/SimpleFilter/SimpleFilterControlGroup/Overview" />

## SimpleFilterControlGroup

`SimpleFilterControlGroup` component organizes multiple filtering controls within a unified layout.
It is designed to work alongside a more comprehensive filtering component, where the visibility of controls in the `SimpleFilterControlGroup` can influence which controls are visible in the more complete filtering view.


### Key Features

* **Dynamic Adjustment**: Automatically adjusts visible controls based on available horizontal space.
* **Event Handling**: Triggers `onFiltersCountChange` whenever the count of visible controls changes, which can be used to determine which filtering options should be shown in the more complete filtering view.

### Supported Children

* <LinkTo kind="Components/SimpleFilter/SimpleFilterControlGroup/Templates/SimpleFilterToggle" story="docs">SimpleFilterToggle</LinkTo>: An on/off toggle button for activating/deactivating filters.
* <LinkTo kind="Components/SimpleFilter/SimpleFilterControlGroup/Templates/SimpleFilterMultiSelect" story="docs">SimpleFilterMultiSelect</LinkTo>: A multi-select list component for choosing multiple filter values.
* <LinkTo kind="Components/SimpleFilter/SimpleFilterControlGroup/Templates/SimpleFilterCustomControl" story="docs">SimpleFilterCustomControl</LinkTo>: A generic component to create custom filtering controls.

## Managing State Using useSimpleFiltersState Hook

While state can be managed independently, the `useSimpleFiltersState` hook simplifies this by centralizing the state of all filters into an array of objects, using the `SimpleFilterControlState` type.

```jsx
type ISimpleFilterControlState<T = unknown> = {
  name: string; // Unique identifier for the filter
  value: T; // The state value associated with the filter
};
```

### Supported State Types

* Boolean: For controls like `SimpleFilterToggle`.
* Array: For controls like `SimpleFilterMultiSelect` (e.g., `ISimpleFilterCollectionControlItem[]`).
* Custom: Any type required for custom controls, such as `number` or `date`.

Basic usage example:

```jsx
const { filters, getFilter, updateFilter, resetFilters } = useSimpleFiltersState(initialState, onFiltersChange);
```

### Arguments

| Argument           | Type                                           | Description                                             |
| ------------------ | -----------------------------------------------| --------------------------------------------------------|
| **initialState**   | ISimpleFilterControlState[]                    | Initial state array for filter controls. 
| **onFiltersChange** | (filters: ISimpleFilterControlState[]) => void | Callback invoked when the state of filters is updated.

### Returns

| Property          | Type                                                                             | Description                                                                                                             |
| ----------------  | -------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------- |
| **filters**       | `ISimpleFilterControlState[]`                                                    | Array representing the current state of all filters.
| **getFilter**     | `<T>(name: string) => ISimpleFilterControlState<T> \| undefined`                 | Retrieves the state of a specific filter by its name.
| **updateFilter**  | `<T>(name: string, value: T) => void;`                                           | Updates the state of a specific filter.
| **clearFilters**  | `() => void`                                                                     | Removes all the filters.
| **resetFilter**   | `() => void`                                                                     | Resets the state of all filters to the initial state.

### Basic Example

```jsx
const { filters, getFilter, updateFilter, resetFilters } = useSimpleFiltersState([], onFiltersChange);

const onFiltersChange = (filters: ISimpleFilterControlState[]) => {
  console.log('Filters updated:', filters);
};

// Wrap your options in useMemo to ensure the objects references will not change
const statusOptions = React.useMemo(
    () => [
      // ids are not required.
      // We only require a label property to be present to display it in the UI, but other properties will be stored too
      { statusId: 1, label: 'Applied' },
      { statusId: 2, label: 'Screening' },
    ],
    []
  );

<SimpleFilterControlGroup id='demo-group'>
  <SimpleFilterMultiSelect
    id="status-selection-menu"
    options={statusOptions}
    onApply={(options) => updateFilter('status', options)}
    selectedOptions={getFilter<ISimpleFilterCollectionControlItem[]>('status')?.value}
    textMap={{
      applyButtonLabel: 'Apply',
      clearButtonLabel: 'Clear',
      listAriaLabel: 'Status',
      selectionClearedMessage: 'Selection cleared',
    }}
  >
    Status
  </SimpleFilterMultiSelect>

  <SimpleFilterToggle
    id="is-top-qualified-toggle"
    onClick={() => updateFilter('isTopQualified', !getFilter<boolean>('isTopQualified')?.value)}
    selected={getFilter<boolean>('isTopQualified')?.value}
  >
    Top Qualified
  </SimpleFilterToggle>
</SimpleFilterControlGroup>
```

## Accessibility

The `ariaLabel` property is <strong>required</strong> for an accessible screen reader experience. Use a short and descriptive label, e.g. "Skills"

### SimpleFilterControlGroup Props

<ArgsTable of={SimpleFilterControlGroup} />