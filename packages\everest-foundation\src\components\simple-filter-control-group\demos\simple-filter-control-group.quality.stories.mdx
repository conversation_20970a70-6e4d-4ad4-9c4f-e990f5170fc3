import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, defaultModes } from '../../../../chromatic';
import { SimpleFilterControlGroup } from '../simple-filter-control-group';

<Meta
  title="Testing/Automation Test Cases/SimpleFilterControlGroup"
  component={SimpleFilterControlGroup}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
/>

<Canvas>
  <Story name="Render All Children">
    {(args) => (
      <div style={{ width: '350px', border: '2px dashed lightgrey' }}>
        <SimpleFilterControlGroup id="basic-example-1" ariaLabel="group name">
          <div style={{ width: '100px', height: '20px', border: '1px solid lightgreen' }}></div>
          <div style={{ width: '100px', height: '20px', border: '1px solid lightgreen' }}></div>
          <div style={{ width: '100px', height: '20px', border: '1px solid lightgreen' }}></div>
        </SimpleFilterControlGroup>
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Render Only Children That Fit">
    {(args) => (
      <div style={{ width: '250px', border: '2px dashed lightgrey' }}>
        <SimpleFilterControlGroup id="basic-example-2" ariaLabel="group name">
          <div style={{ width: '100px', height: '20px', border: '1px solid lightgreen' }}></div>
          <div style={{ width: '100px', height: '20px', border: '1px solid lightgreen' }}></div>
          <div style={{ width: '100px', height: '20px', border: '1px solid lightgreen' }}></div>
        </SimpleFilterControlGroup>
      </div>
    )}
  </Story>
</Canvas>
