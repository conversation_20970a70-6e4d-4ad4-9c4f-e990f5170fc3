import { renderHook, act } from '@testing-library/react-hooks';

import { ISimpleFilterCollectionControlItem } from '../templates';

import { useSimpleFiltersState, ISimpleFilterControlState } from '.';

describe('[useSimpleFiltersState]', () => {
  const initialState: ISimpleFilterControlState[] = [
    { name: 'filter1', value: true },
    { name: 'filter2', value: [{ id: 1, label: 'Option 1' }] },
  ];

  const mockOnFiltersChange = jest.fn();

  it('should initialize with the correct filters', () => {
    const { result } = renderHook(() => useSimpleFiltersState(initialState, mockOnFiltersChange));

    expect(result.current.filters).toEqual(initialState);
  });

  it('should return the correct filter value using getFilter', () => {
    const { result } = renderHook(() => useSimpleFiltersState(initialState, mockOnFiltersChange));

    // boolean type
    expect(result.current.getFilter<boolean>('filter1')?.value).toBe(true);

    // ISimpleFilterCollectionControlItem[] type
    expect(result.current.getFilter<ISimpleFilterCollectionControlItem[]>('filter2')?.value).toEqual([
      { id: 1, label: 'Option 1' },
    ]);
  });

  it('should update an existing filters state', () => {
    const { result } = renderHook(() => useSimpleFiltersState(initialState, mockOnFiltersChange));

    act(() => {
      result.current.updateFilter('filter1', false);
    });

    expect(result.current.filters).toEqual([
      { name: 'filter1', value: false },
      { name: 'filter2', value: [{ id: 1, label: 'Option 1' }] },
    ]);
  });

  it('should fire the onFiltersChange when updating', () => {
    const { result } = renderHook(() => useSimpleFiltersState(initialState, mockOnFiltersChange));

    act(() => {
      result.current.updateFilter('filter1', false);
    });

    expect(mockOnFiltersChange).toHaveBeenCalledWith([
      { name: 'filter1', value: false },
      { name: 'filter2', value: [{ id: 1, label: 'Option 1' }] },
    ]);
  });

  it('should add a new filter when the filter does not exist', () => {
    const { result } = renderHook(() => useSimpleFiltersState(initialState, mockOnFiltersChange));

    const newFilter: ISimpleFilterControlState = { name: 'filter3', value: true };

    act(() => {
      result.current.updateFilter('filter3', true);
    });

    expect(result.current.filters).toContainEqual(newFilter);
    expect(mockOnFiltersChange).toHaveBeenCalledWith([...initialState, newFilter]);
  });

  it('should reset filters to the initial state', () => {
    const { result } = renderHook(() => useSimpleFiltersState(initialState, mockOnFiltersChange));

    // update one of the filters
    act(() => {
      result.current.updateFilter('filter1', false);
    });

    // check if it updates the state
    expect(result.current.filters).toEqual([
      { name: 'filter1', value: false },
      { name: 'filter2', value: [{ id: 1, label: 'Option 1' }] },
    ]);

    // reset
    act(() => {
      result.current.resetFilters();
    });

    // check if it updates the state
    expect(result.current.filters).toEqual(initialState);
    expect(mockOnFiltersChange).toHaveBeenCalledWith(initialState);
  });

  it('should call onFiltersChange when resetting filters', () => {
    const { result } = renderHook(() => useSimpleFiltersState(initialState, mockOnFiltersChange));

    act(() => {
      result.current.updateFilter('filter1', false);
      result.current.resetFilters();
    });

    expect(mockOnFiltersChange).toHaveBeenCalledWith(initialState);
  });
});
