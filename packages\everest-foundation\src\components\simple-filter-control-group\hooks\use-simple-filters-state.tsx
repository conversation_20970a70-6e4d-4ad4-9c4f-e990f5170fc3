import { useState, useCallback } from 'react';

export type ISimpleFilterControlState<T = unknown> = {
  name: string;
  value: T;
};

export type UseSimpleFilterStateHandlers = {
  filters: ISimpleFilterControlState[];
  getFilter: <T>(key: string) => ISimpleFilterControlState<T> | undefined;
  updateFilter: <T>(key: string, value: T) => ISimpleFilterControlState[];
  resetFilters: () => void;
  clearFilters: () => void;
};

export const useSimpleFiltersState = (
  initialState: ISimpleFilterControlState[],
  onFiltersChange: (newFilters: ISimpleFilterControlState[]) => void
): UseSimpleFilterStateHandlers => {
  const [filters, setFilters] = useState<ISimpleFilterControlState[]>(initialState);

  const getFilter = useCallback(
    <T,>(key: string): ISimpleFilterControlState<T> | undefined => {
      return filters.find((filter) => filter.name === key) as ISimpleFilterControlState<T> | undefined;
    },
    [filters]
  );

  const updateFilter = useCallback(
    <T,>(name: string, value: T): ISimpleFilterControlState[] => {
      let updatedFilters: ISimpleFilterControlState[];

      setFilters((prev) => {
        const indexOfExistingFilter = prev.findIndex((filter) => filter.name === name);
        updatedFilters = [...prev];

        if (indexOfExistingFilter !== -1) {
          updatedFilters[indexOfExistingFilter] = { name, value };
        } else {
          updatedFilters.push({ name, value });
        }

        onFiltersChange(updatedFilters);
        return updatedFilters;
      });

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      return updatedFilters!;
    },
    [onFiltersChange]
  );

  const resetFilters = useCallback(() => {
    setFilters(initialState);
    onFiltersChange(initialState);
  }, [initialState, onFiltersChange]);

  const clearFilters = useCallback(() => {
    setFilters([]);
    onFiltersChange([]);
  }, [onFiltersChange]);

  return {
    filters,
    getFilter,
    updateFilter,
    resetFilters,
    clearFilters,
  };
};
