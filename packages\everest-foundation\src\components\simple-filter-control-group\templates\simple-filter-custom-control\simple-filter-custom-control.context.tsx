import { createContext, RefObject } from 'react';

export interface ISimpleFilterCustomControlContext {
  triggerRef: RefObject<HTMLButtonElement>;
  open?: boolean;
  id?: string;
  testId?: string;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export const SimpleFilterCustomControlContext = createContext<ISimpleFilterCustomControlContext>({
  open: undefined,
  triggerRef: { current: null },
});
