import { ArgsTable } from '@storybook/addon-docs';
import { SimpleFilterCustomControl, SimpleFilterPopover, SimpleFilterTriggerButton } from '../';
import { CodeExample } from '../../../../../.storybook/doc-blocks/example/example';
import { Button } from '../../../button';

export const scope = { SimpleFilterCustomControl, SimpleFilterTriggerButton, SimpleFilterPopover, Button };

## Building Custom Filtering Controls Using SimpleFilterCustomControl

The `SimpleFilterCustomControl` component can be used for building custom filtering controls by allowing customization of both the trigger button and the popover content. 
It accepts two specific child components:

* `SimpleFilterTriggerButton` 
* `SimpleFilterPopover`

Basic example:
```jsx
<SimpleFilterCustomControl id="basic-example" open={isOpen}>

    <SimpleFilterTriggerButton id='trigger' onClick={() => setIsOpen(true)} ariaControls='popover'>
        Label
    </SimpleFilterTriggerButton>

    <SimpleFilterPopover id='popover' onClose={() => setIsOpen(false)}>
        // Popover Content
    </SimpleFilterPopover>

</SimpleFilterCustomControl> 
```

### SimpleFilterCustomControl

The `SimpleFilterCustomControl` serves as a wrapper that passes necessary data between children.

#### SimpleFilterCustomControl API

| Property Name | Type    | Description                                                                                                                         |
| ------------- | ------- | ----------------------------------------------------------------------------------------------------------------------------------- |
| id            | string  | Sets the id on the root element.                                                                                                    |
| testId?       | string  | Sets the testId on the root element.                                                                                                |
| open          | boolean | Dictates visibility of the SimpleFilterPopover and the visibility and orientation of the chevron in the SimpleFilterTriggerButton.  |

### SimpleFilterCustomControl Trigger Button

The `SimpleFilterTriggerButton` serves as the trigger to open and close the overlay. 
It offers several properties for customization, ensuring adherence to UX guidelines. 
Below are examples illustrating different configurations:

export const triggerButton = `() => {

    return (
        <div style={{display: 'flex', flexWrap: 'wrap', padding: '16px', gap: '16px'}}>

            {/* Basic configuration without modifications */}
            <SimpleFilterCustomControl id="basic-example">
                <SimpleFilterTriggerButton id="basic-trigger">Basic</SimpleFilterTriggerButton>
            </SimpleFilterCustomControl>

            {/* Open state with chevron pointing up */}
            <SimpleFilterCustomControl id="open-example" open>
                <SimpleFilterTriggerButton id="open-trigger">Open</SimpleFilterTriggerButton>
            </SimpleFilterCustomControl>
            
            {/* Closed state with chevron pointing down */}
            <SimpleFilterCustomControl id="closed-example" open={false}>
                <SimpleFilterTriggerButton id="closed-trigger">Closed</SimpleFilterTriggerButton>
            </SimpleFilterCustomControl>
            
            {/* Selected state with a checkmark and background color change*/}
            <SimpleFilterCustomControl id="selected-example">
                <SimpleFilterTriggerButton id="selected-trigger" selected>Selected</SimpleFilterTriggerButton>
            </SimpleFilterCustomControl>
            
            {/* Selected state with a selection count badge */}
            <SimpleFilterCustomControl id="badge-example" open>
                <SimpleFilterTriggerButton id="badge-trigger" selected selectionCount={3}>Selected With Badge</SimpleFilterTriggerButton>
            </SimpleFilterCustomControl> 
        </div>
    );
}`;

<CodeExample scope={scope} code={triggerButton} />

### Reference to the Trigger Button

`SimpleFilterTriggerButton` provides programatic element access via `ref`. This can be useful when the button needs to be focused programatically.

export const buttonWithRef = `() => {

    const buttonRef = React.useRef<HTMLButtonElement>(null);

    return (
        <SimpleFilterCustomControl id="ref-example">
            <SimpleFilterTriggerButton id="basic-trigger" ref={buttonRef} onClick={() => console.log(buttonRef)}>Click to Log</SimpleFilterTriggerButton>
        </SimpleFilterCustomControl>
    );
}`;

<CodeExample scope={scope} code={buttonWithRef} />

#### SimpleFilterTriggerButton API

| Property Name    | Type       | Description                                                          |
| ---------------- | ---------- | -------------------------------------------------------------------- |
| id               | string     | Sets the id.                                                         |
| onClick          | () => void | Fires when user clicks on the button.                                |
| testId?          | string     | Sets the testId.                                                     |
| selected?        | boolean    | Marks the button as selected updating its appearance.                |
| selectionCount?  | number     | When provided, adds a badge displaying number passed as argument.    |
| ariaControls?    | string     | Should be set to the `id` passed to `SimpleFilterPopover`            |

##### Additional information:

* The `selectionCount` badge should be used in combination with the `selected` property. When `selectionCount` is set, ensure `selected` is also `true`.
* The visibility and orientation of the chevron are managed by the `open` property on the `SimpleFilter` component.


### SimpleFilterPopover

The `SimpleFilterPopover` serves as a container for the content that should be displayed inside the popover.

#### SimpleFilterPopover API

| Property Name    | Type                                                                                                                                                                             | Description                                                           |
| ---------------- | --------------------------------- | -------------------------------------------------------------------- |
| id               | string                            | Sets the id.                                                         |
| testId?          | string                            | Sets the testId.                                                     |
| onClose          | (e?: IDialogCloseEvent) => void   | Callback that runs when the popover closes.                          |
| placement?       | 'bottomLeft' \| 'bottomRight'     | Specifies the popover's position in relation to the trigger element. |

##### Additional information:

* visibility of the popover is controlled by `open` property set on the `SimpleFilterCustomControl` component.

### Accessibility

The `id` value of `SimpleFilterPopover` should be passed to the `ariaControls` property of `SimpleFilterTriggerButton` to ensure proper accessibility.

## Basic Example

export const withPopover = `() => {
    const [isOpen, setIsOpen] = React.useState(false);
    
    return (
        <SimpleFilterCustomControl id="basic-example" open={isOpen}>
            <SimpleFilterTriggerButton id='trigger' ariaControls='popover' onClick={() => setIsOpen(true)} selectionCount={3} selected>Education Level</SimpleFilterTriggerButton>
            <SimpleFilterPopover id='popover' onClose={() => setIsOpen(false)}>
                <div style={{ padding: '24px' }}>
                    <Button id='example' label='Close' onClick={() => setIsOpen(false)}/>
                </div>
            </SimpleFilterPopover>
        </SimpleFilterCustomControl> 
    );
}`;

<CodeExample scope={scope} code={withPopover} />