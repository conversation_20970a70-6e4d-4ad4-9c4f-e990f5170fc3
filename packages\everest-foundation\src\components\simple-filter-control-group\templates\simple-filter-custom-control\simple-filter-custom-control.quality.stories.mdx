import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, defaultModes } from '../../../../../chromatic';
import { useState } from 'react';
import { SimpleFilterCustomControl, SimpleFilterTriggerButton, SimpleFilterPopover } from '../';

<Meta
  title="Testing/Automation Test Cases/SimpleFilterControlGroup/SimpleFilterCustomControl"
  component={SimpleFilterCustomControl}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
/>

<Canvas>
  <Story name="Basic Render">
    {(args) => (
      <SimpleFilterCustomControl id="basic-example" open={true}>
        <SimpleFilterTriggerButton id='trigger' ariaControls='popover' onClick={console.log}>
          Label
        </SimpleFilterTriggerButton>

        <SimpleFilterPopover id='popover'>
          Popover Content
        </SimpleFilterPopover>
      </SimpleFilterCustomControl>
    )}

  </Story>
</Canvas>

<Canvas>
  <Story
    name="[Playwright] - Interactions"
    parameters={{ chromatic: Chromatic.DISABLE }}
  >
    {(args) => {
      const [open, setOpen] = useState(false);
      return (
        <SimpleFilterCustomControl id="basic-example-move-focus" open={open}>
          <SimpleFilterTriggerButton
            id="trigger-move-focus"
            testId="test-trigger-move-focus"
            ariaControls="popover-move-focus"
            onClick={() => setOpen(true)}
          >
            Label
          </SimpleFilterTriggerButton>

          <SimpleFilterPopover id="popover-move-focus" testId="test-popover-move-focus">
            <button data-testid="test-popover-content-button" onClick={() => setOpen(false)}>
              Close
            </button>
          </SimpleFilterPopover>
        </SimpleFilterCustomControl>
      );
    }}

  </Story>
</Canvas>
