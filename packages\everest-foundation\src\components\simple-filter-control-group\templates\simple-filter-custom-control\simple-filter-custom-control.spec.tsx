import React from 'react';
import { render, screen } from '@testing-library/react';

import { SimpleFilterPopover, SimpleFilterTriggerButton, SimpleFilterCustomControl } from '../';

describe('[SimpleFilterCustomControl]', () => {
  const defaultProps = {
    id: 'filter-id',
    testId: 'test-filter-id',
    open: true,
  };
  const getSimpleFilterCustomControl = () => screen.getByTestId(defaultProps.testId);

  it('renders with the correct id and testId', () => {
    render(<SimpleFilterCustomControl {...defaultProps} />);

    expect(getSimpleFilterCustomControl()).toBeInTheDocument();
    expect(getSimpleFilterCustomControl()).toHaveAttribute('id', defaultProps.id);
  });

  it('renders any children', () => {
    jest.spyOn(console, 'warn').mockImplementation(() => undefined);

    const { container } = render(
      <SimpleFilterCustomControl {...defaultProps}>
        <SimpleFilterTriggerButton id="trigger" onClick={jest.fn()} />
        <SimpleFilterPopover id="popover" />
        <div>Custom Child</div>
      </SimpleFilterCustomControl>
    );

    expect(container.querySelector('div')).toBeInTheDocument();
    expect(screen.queryByText('Custom Child')).toBeInTheDocument();
  });
});
