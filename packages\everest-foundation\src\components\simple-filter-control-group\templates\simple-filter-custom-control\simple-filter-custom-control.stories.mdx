import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import {
  SimpleFilterControlGroup,
  SimpleFilterCustomControl,
  SimpleFilterPopover,
  SimpleFilterTriggerButton,
} from '../';
import { Button } from '../../../button';
import { action } from '@storybook/addon-actions';

import Examples from './simple-filter-custom-control.examples.mdx';

<Meta
  title="Components/SimpleFilter/SimpleFilterControlGroup/Templates/SimpleFilterCustomControl"
  component={SimpleFilterCustomControl}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&m=dev',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    open: {
      description: 'Controls visibility of the popover and orientation of the chevron in the trigger button.',
      table: {
        defaultValue: { summary: false },
      },
    },
  }}
  args={{
    id: 'basic-simple-filter-control-group-demo',
    testId: 'basic-simple-filter-control-group-demo-test-id',
    open: false,
  }}
/>

<Examples />

## Live Demo

<Canvas>
  <Story name="SimpleFilterCustomControl">
    {(args) => {
      const [{ open }, updateArgs] = useArgs();

      const onClickHandler = () => {
        updateArgs({ open: true });
      };
      const onCloseHandler = () => {
        updateArgs({ open: false });
      };
      return (
        <SimpleFilterCustomControl {...args} open={open}>
          <SimpleFilterTriggerButton
            onClick={onClickHandler}
            selectionCount={3}
            selected
            id="trigger"
            ariaControls="popover"
          >
            Education Level
          </SimpleFilterTriggerButton>
          <SimpleFilterPopover id="popover" onClose={onCloseHandler}>
            <div
              style={{
                height: '160px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                padding: '24px',
              }}
            >
              Custom Content
              <Button id="example" label="Close" onClick={() => updateArgs({ open: false })} />
            </div>
          </SimpleFilterPopover>
        </SimpleFilterCustomControl>
      );
    }}
  </Story>
</Canvas>

<ArgsTable story="SimpleFilterCustomControl" />
