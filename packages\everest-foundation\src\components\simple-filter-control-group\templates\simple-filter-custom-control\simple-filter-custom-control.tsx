import React, { PropsWithChildren, useRef } from 'react';

import { SimpleFilterCustomControlContext } from './simple-filter-custom-control.context';

export interface ISimpleFilterCustomControl {
  id: string;
  open?: boolean;
  testId?: string;
}

export const SimpleFilterCustomControl = ({
  id,
  testId,
  children,
  open,
}: PropsWithChildren<ISimpleFilterCustomControl>): JSX.Element => {
  const triggerRef = useRef<HTMLButtonElement>(null);

  return (
    <SimpleFilterCustomControlContext.Provider value={{ open, triggerRef, id, testId }}>
      <div id={id} data-testid={testId}>
        {children}
      </div>
    </SimpleFilterCustomControlContext.Provider>
  );
};

SimpleFilterCustomControl.displayName = 'SimpleFilterCustomControl';
