import React from 'react';
import { render, screen } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { act } from 'react-dom/test-utils';

import { SimpleFilterMultiSelect } from './simple-filter-multiselect';

describe('[SimpleFilterMultiSelect]', () => {
  const options = [{ label: 'Option 1' }, { label: 'Option 2' }, { label: 'Option 3' }];

  const defaultProps = {
    id: 'el-id',
    testId: 'test-id',
    options,
    textMap: {
      applyButtonLabel: 'Apply',
      clearButtonLabel: 'Clear',
      listAriaLabel: 'Filter options',
      selectionClearedMessage: 'Selection cleared',
    },
    onApply: jest.fn(),
    children: 'Multiselect',
  };
  const getTriggerButton = () => screen.getByRole('button', { name: defaultProps.children });
  const getApplyButton = () => screen.getByRole('button', { name: defaultProps.textMap.applyButtonLabel });
  const getClearButton = () => screen.getByRole('button', { name: defaultProps.textMap.clearButtonLabel });
  const getLiveRegion = async () => screen.findByText(defaultProps.textMap.selectionClearedMessage); // text gets set after a delay
  const getNOption = (ind: number) => screen.getByText(options[ind].label);
  const getPopover = () => screen.queryByRole('dialog');

  it('renders the component with children', () => {
    render(<SimpleFilterMultiSelect {...defaultProps} />);

    expect(getTriggerButton()).toBeInTheDocument();
  });

  it('opens the popover on button click', async () => {
    render(<SimpleFilterMultiSelect {...defaultProps} />);

    await userEvent.click(getTriggerButton());

    const listbox = screen.getByRole('listbox');
    expect(listbox).toBeInTheDocument();
  });

  it('selects and deselects options on click', async () => {
    render(<SimpleFilterMultiSelect {...defaultProps} />);

    await userEvent.click(getTriggerButton());

    const option1 = getNOption(0);
    await userEvent.click(option1);
    expect(option1).toHaveAttribute('aria-selected', 'true');

    await userEvent.click(option1);
    expect(option1).toHaveAttribute('aria-selected', 'false');
  });

  it('applies selected options when clicking Apply button', async () => {
    render(<SimpleFilterMultiSelect {...defaultProps} />);

    await userEvent.click(getTriggerButton());
    await userEvent.click(getNOption(1));

    await userEvent.click(getApplyButton());

    expect(defaultProps.onApply).toHaveBeenCalledWith([options[1]]);
  });

  it('clears selections when clicking Clear button', async () => {
    render(<SimpleFilterMultiSelect {...defaultProps} />);

    await userEvent.click(getTriggerButton());
    await userEvent.click(getNOption(2));

    await userEvent.click(getClearButton());

    expect(await getLiveRegion()).toBeInTheDocument();
  });

  it('closes the popover when clicking outside', async () => {
    render(<SimpleFilterMultiSelect {...defaultProps} />);

    await userEvent.click(getTriggerButton());
    expect(getPopover()).toBeInTheDocument();

    await act(async () => {
      await userEvent.keyboard('{Escape}');
    });
    expect(getPopover()).not.toBeInTheDocument();
  });
});
