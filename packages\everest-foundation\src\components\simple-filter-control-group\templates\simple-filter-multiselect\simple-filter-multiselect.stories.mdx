import { Meta, Story, <PERSON>vas, ArgsTable } from '@storybook/addon-docs';
import { action } from '@storybook/addon-actions';
import { SimpleFilterMultiSelect } from './';
import Examples from './simple-filter-multiselect.examples.mdx';

<Meta
  title="Components/SimpleFilter/SimpleFilterControlGroup/Templates/SimpleFilterMultiSelect"
  component={SimpleFilterMultiSelect}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&m=dev',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    textMap: {
      type: 'object',
      control: 'object',
      description: 'Object containing localized text to be used in the UI and for accessibility.',
    },
    options: {
      description: 'An array of options to be displayed.',
    },
    onApply: {
      description: 'Fires when user clicks `Apply` button passing an array of options selected by the user.',
    },
    selectedOptions: {
      description: 'An array of objects from `options` to be marked as selected.',
      control: '-',
    },
    placement: {
      description: "Specifies the popover's position in relation to the trigger element.",
    },
  }}
  args={{
    id: 'basic-simple-filter-control-group-demo',
    testId: 'basic-simple-filter-control-group-demo-test-id',
    onApply: action('onApply'),
    textMap: {
      applyButtonLabel: 'Apply',
      clearButtonLabel: 'Clear',
      listAriaLabel: 'Department options',
      selectionClearedMessage: 'Selection cleared',
    },
    options: [
      { id: 1, label: 'Sales' },
      { id: 2, label: 'Marketing' },
      { id: 3, label: 'Tech Support' },
      { id: 4, label: 'Human Resources' },
      { id: 5, label: 'Product & Technology' },
    ],
    placement: 'bottomLeft',
  }}
/>

<Examples />

## Live Demo

<Canvas>
  <Story name="SimpleFilterMultiSelect">
    {(args) => {
      const [selectedOptions, setSelectedOptions] = React.useState([]);

      // Wrap your options in useMemo to ensure the objects references will not change
      const options = React.useMemo(
        () => args.options,
        [args.options]
      );

      const onApplyHandler = (e) => {
        setSelectedOptions(e);
        action('onApply')(e);
      }
      return (
        <SimpleFilterMultiSelect
          {...args}
          options={options}
          onApply={onApplyHandler}
          selectedOptions={selectedOptions}
        >
          Departments
        </SimpleFilterMultiSelect>
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="SimpleFilterMultiSelect" />
