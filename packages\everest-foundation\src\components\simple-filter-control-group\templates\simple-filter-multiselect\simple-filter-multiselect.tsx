import React, {
  <PERSON>ps<PERSON><PERSON><PERSON>hildren,
  useState,
  KeyboardEvent,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';

import { SimpleFilterCustomControl, SimpleFilterPopover, SimpleFilterTriggerButton } from '../';
import { IUseKeyboardNavigationConfig, useKeyboardNavigation } from '../../../../utils';
import { useSelection } from '../../../../utils/use-selection';
import { Button } from '../../../button';
import { CheckboxBase } from '../../../checkbox-base';

import styles from './simple-filter-multiselect.module.scss';

export interface ISimpleFilterMultiSelectRef {
  triggerButton: HTMLButtonElement | null;
  // creating a type with even one prop helps future-proofing the component,
  // as we can add more functionality without breaking anyone
}

export interface ISimpleFilterCollectionControlItem {
  // Using generic "collection" in the name as we may be using this for other controls with multiple options like radio buttons
  // 'id' not part of this interface, because we won't be using it for anything anyways. That's because we can't expect the 'id' to be a string
  // to set 'id' attribute on the list items we use the 'label' instead.
  label: string;
  [key: string]: unknown; // allows passing any additional props they may have/need
}

export interface ISimpleFilterMultiSelect {
  id: string;
  options: ISimpleFilterCollectionControlItem[];
  selectedOptions?: ISimpleFilterCollectionControlItem[];
  onApply: (options: ISimpleFilterCollectionControlItem[]) => void;
  placement?: 'bottomLeft' | 'bottomRight';
  testId?: string;
  textMap: {
    applyButtonLabel: string;
    clearButtonLabel: string;
    listAriaLabel: string;
    selectionClearedMessage: string;
  };
}
export const SimpleFilterMultiSelect = forwardRef<
  ISimpleFilterMultiSelectRef,
  PropsWithChildren<ISimpleFilterMultiSelect>
>(
  (
    { id, options, selectedOptions = [], onApply, testId, placement = 'bottomLeft', textMap, children },
    ref
  ): JSX.Element => {
    const [isOpen, setIsOpen] = useState(false);
    const [announcement, setAnnouncement] = useState('');
    const liRefs = useRef<(HTMLLIElement | null)[]>([]);
    const applyBtnRef = useRef<HTMLButtonElement | null>(null);
    const triggerButtonRef = useRef<HTMLButtonElement | null>(null);
    // used in conjunction with useEffect to keep the selectedOptions in sync with the selection from the useSelection hook
    const userInteracted = useRef(false);

    useImperativeHandle(
      ref,
      (): ISimpleFilterMultiSelectRef => ({
        triggerButton: triggerButtonRef.current,
      })
    );

    // this serves as temporary selection until changes are applied/reverted
    const { selection, toggle, select, isSelected, clear } = useSelection<ISimpleFilterCollectionControlItem>(
      selectedOptions,
      true
    );

    const navigationConfig: IUseKeyboardNavigationConfig = {
      length: options.length,
      orientation: 'vertical',
      onIndexChange: (index: number) => {
        liRefs.current[index]?.focus();
      },
    };
    const { onKeyDownHandler, setIndex, index } = useKeyboardNavigation(navigationConfig);

    const keyDownHandler = (option: ISimpleFilterCollectionControlItem, event: KeyboardEvent) => {
      userInteracted.current = true;
      if ([' ', 'Enter'].includes(event.key)) {
        toggle([option]);
        event.preventDefault(); // prevent spacebar scrolling
      }
      if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {
        event.preventDefault(); // prevent scrolling
      }
      onKeyDownHandler(event);
    };

    const onApplyHandler = () => {
      onApply(selection);
      setIsOpen(false);
    };

    const onItemClick = (option: ISimpleFilterCollectionControlItem, ind: number) => {
      userInteracted.current = true;
      toggle([option]);
      setIndex(ind);
    };

    const onPopoverCloseHandler = () => {
      clear();
      select(selectedOptions);
      setIsOpen(false);
    };

    // the state of `selection` in the `useSelection` hook can be set from within the component via user interaction OR from outside via `selectedOptions` prop
    // the code below ensures that the `selection` is always in sync with the `selectedOptions` prop, but also that `selection` updates from user interaction wont be overwritten by the current value of `selectedOptions` prop
    useEffect(() => {
      if (!userInteracted.current) {
        if (selectedOptions.length !== selection.length || selectedOptions.some((opt) => !selection.includes(opt))) {
          clear();
          select(selectedOptions);
        }
      }
      userInteracted.current = false;
    }, [selectedOptions, clear, select, selection]);

    return (
      <SimpleFilterCustomControl id={id} testId={testId} open={isOpen}>
        <SimpleFilterTriggerButton
          onClick={() => {
            setIsOpen(true);
            setIndex(0); // reset keyboard navigation to always start with first item when popover opens
          }}
          selectionCount={selectedOptions.length || undefined}
          selected={selectedOptions.length > 0 || undefined}
          id={`${id}-trigger`}
          testId={testId ? `${testId}-trigger` : undefined}
          ariaControls={`${id}-popover`}
          ref={triggerButtonRef}
        >
          {children}
        </SimpleFilterTriggerButton>
        <SimpleFilterPopover
          id={`${id}-popover`}
          testId={testId ? `${testId}-popover` : undefined}
          onClose={onPopoverCloseHandler}
          onOpen={() => liRefs.current[0]?.focus()} // focus first list item as per A11y
          placement={placement}
        >
          <div className={styles.evrContentWrapper}>
            <ul className={styles.listWrapper} role="listbox" aria-label={textMap.listAriaLabel}>
              {options.map((option, ind) => {
                return (
                  <li
                    key={`${id}-key-${option.label}`}
                    id={`${id}-option-${option.label}`}
                    data-testid={testId ? `${testId}-trigger` : undefined}
                    ref={(el) => (liRefs.current[ind] = el)}
                    className={styles.listItem}
                    onClick={() => onItemClick(option, ind)}
                    onKeyDown={(e) => keyDownHandler(option, e)}
                    role="option"
                    aria-selected={isSelected(option)}
                    tabIndex={ind === index ? 0 : -1}
                  >
                    <CheckboxBase
                      id={`${id}-checkbox`}
                      testId={testId ? `${testId}-checkbox` : undefined}
                      checkedState={isSelected(option) ? 'checked' : 'unchecked'}
                      presentation
                      className={styles.checkboxOverrides}
                    />
                    {option.label}
                  </li>
                );
              })}
            </ul>
            <div className={styles.buttonsWrapper}>
              {selection.length > 0 && (
                <Button
                  id={`${id}-clear-button`}
                  testId={testId ? `${testId}-clear` : undefined}
                  variant="tertiaryNeutral"
                  label={textMap.clearButtonLabel}
                  onClick={() => {
                    userInteracted.current = true;
                    clear(); // clears selection
                    requestAnimationFrame(() => {
                      // after clearing selection this button will disappear so lets move focus to Apply button to speed things up for the user
                      applyBtnRef.current?.focus(); // This button will be immediately announced after it gets focus. VoiceOver would automatically move focus even if we wouldn't do it manually
                      setAnnouncement(''); // clear any content of the live region
                      setTimeout(() => {
                        // the SR needs this extra time to wait fo the apply button's announcement before it announces "selectionClearedMessage" label
                        // Otherwise the apply button's label announcement would be cut off.
                        setAnnouncement(textMap.selectionClearedMessage);
                      }, 500);
                    });
                  }}
                />
              )}
              <Button
                ref={applyBtnRef}
                id={`${id}-apply-button`}
                testId={testId ? `${testId}-apply` : undefined}
                label={textMap.applyButtonLabel}
                onClick={onApplyHandler}
              />
            </div>
            {/* When I use our `announce` function from utils to announce `selection cleared` then it only announces it AFTER the
          popover closes. My theory is that SRs are smart enough to realize that user is in a `dialog` and won't react
          to live-region changes that are OUTSIDE of that dialog until it closes. Adding a simple live region to the
          INSIDE of the dialog ensures that announcement is made as soon as it changes in the DOM */}
            <div aria-live="polite" className={styles.srOnly}>
              {announcement}
            </div>
          </div>
        </SimpleFilterPopover>
      </SimpleFilterCustomControl>
    );
  }
);

SimpleFilterMultiSelect.displayName = 'SimpleFilterMultiSelect';
