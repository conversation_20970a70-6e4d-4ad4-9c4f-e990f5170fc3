import { Popover, PopoverBody, Button } from '@ceridianhcm/components';
import React, { useRef, useState } from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ContainerContext } from '../ContainerContext/ContainerContext';
import { ContainerHeader } from './ContainerHeader';
import { ContainerContextType, ActionType } from '../../types';

const PopoverButton = () => {
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button id="test-trigger" label="Add" ref={triggerRef} onClick={() => setIsOpen(true)} />
      <Popover id="test-popover" open={isOpen} triggerRef={triggerRef} onClose={() => setIsOpen(false)}>
        <PopoverBody id="test-popover-body">Popover Content</PopoverBody>
      </Popover>
    </>
  );
};

describe('ContainerHeader Component', () => {
  const defaultContextValue: ContainerContextType = {
    baseId: 'test-container',
    title: 'Test Header',
    editButton: false,
    onEditClick: jest.fn(),
  };

  const renderWithContext = (contextValue: ContainerContextType = defaultContextValue) => {
    return render(
      <ContainerContext.Provider value={contextValue}>
        <ContainerHeader />
      </ContainerContext.Provider>,
    );
  };

  it('renders header with title', () => {
    renderWithContext();
    expect(screen.getByText('Test Header')).toBeInTheDocument();
  });

  it('renders edit button when editButton is true', () => {
    const onEditClick = jest.fn();
    renderWithContext({
      ...defaultContextValue,
      editButton: true,
      onEditClick,
    });

    const editButton = screen.getByLabelText('Edit');
    expect(editButton).toBeInTheDocument();

    fireEvent.click(editButton);
    expect(onEditClick).toHaveBeenCalled();
  });

  it('renders custom icon button when iconProps are provided', () => {
    const onIconClick = jest.fn();
    renderWithContext({
      ...defaultContextValue,
      iconProps: {
        name: 'add',
        onClick: onIconClick,
      },
    });

    const iconButton = screen.getByRole('button');
    fireEvent.click(iconButton);
    expect(onIconClick).toHaveBeenCalled();
  });

  it('applies custom className', () => {
    render(
      <ContainerContext.Provider value={defaultContextValue}>
        <ContainerHeader className="custom-header" />
      </ContainerContext.Provider>,
    );

    const header = screen.getByRole('heading', { level: 3 }).parentElement;
    expect(header).toHaveClass('custom-header');
  });

  it('throws error when used outside of ContainerContext', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<ContainerHeader />);
    }).toThrow();

    consoleSpy.mockRestore();
  });

  it('renders multiple actions when actionProps is provided', () => {
    const onAddClick = jest.fn();
    const onEditClick = jest.fn();

    renderWithContext({
      ...defaultContextValue,
      actionProps: {
        actions: [
          {
            name: 'add',
            'aria-label': 'Add Item',
            onClick: onAddClick,
          },
          {
            name: 'edit',
            'aria-label': 'Edit Item',
            onClick: onEditClick,
          },
        ],
      },
    });

    const addButton = screen.getByLabelText('Add Item');
    const editButton = screen.getByLabelText('Edit Item');

    expect(addButton).toBeInTheDocument();
    expect(editButton).toBeInTheDocument();

    fireEvent.click(addButton);
    expect(onAddClick).toHaveBeenCalled();

    fireEvent.click(editButton);
    expect(onEditClick).toHaveBeenCalled();
  });

  it('renders custom component when provided in actions', () => {
    renderWithContext({
      ...defaultContextValue,
      actionProps: {
        actions: [
          {
            name: 'add',
            'aria-label': 'Add Item',
            component: <PopoverButton />,
          },
        ],
      },
    });

    expect(screen.getByText('Add')).toBeInTheDocument();
  });

  it('renders add action with actionType ADD (currently renders as standard button)', () => {
    const onAddClick = jest.fn();
    renderWithContext({
      ...defaultContextValue,
      actionProps: {
        actions: [
          {
            name: 'add',
            'aria-label': 'Add Item',
            onClick: onAddClick,
            actionType: ActionType.ADD,
          },
        ],
      },
    });

    const addButton = screen.getByLabelText('Add Item');
    expect(addButton).toBeInTheDocument();

    // Click the add button - currently just calls onClick handler
    fireEvent.click(addButton);
    expect(onAddClick).toHaveBeenCalled();

    // Note: Popover functionality for ActionType.ADD is not yet implemented
    // The current implementation renders a standard IconButton regardless of actionType
  });

  it('renders add action as popover when actionType is ADD', () => {
    const onAddClick = jest.fn();
    renderWithContext({
      ...defaultContextValue,
      actionProps: {
        actions: [
          {
            name: 'add',
            'aria-label': 'Add Item',
            onClick: onAddClick,
            actionType: ActionType.ADD,
            component: (
              <div>
                <button aria-label="Add Item">Add Button</button>
                <div>Option 1</div>
                <div>Option 2</div>
              </div>
            ),
          },
        ],
      },
    });

    const addButton = screen.getByLabelText('Add Item');
    expect(addButton).toBeInTheDocument();

    // Check if popover menu items are rendered
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('applies custom spacing between actions', () => {
    renderWithContext({
      ...defaultContextValue,
      actionProps: {
        spacing: 16,
        actions: [
          {
            name: 'add',
            'aria-label': 'Add Item',
          },
          {
            name: 'edit',
            'aria-label': 'Edit Item',
          },
        ],
      },
    });

    const actionsContainer = screen.getByLabelText('Add Item').closest('.container-header-actions');
    expect(actionsContainer).toHaveStyle('gap: 16px');
  });
});
