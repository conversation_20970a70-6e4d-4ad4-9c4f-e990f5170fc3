import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useFormWrapper } from './useFormWrapper';
import { z } from 'zod';

// Mock the SidePanel components
jest.mock('@components/SidePanel', () => ({
  SidePanelButtonType: {
    Primary: 'Primary',
    Secondary: 'Secondary',
  },
}));

const testSchema = z.object({
  name: z.string().min(1, 'Name is required'),
});

type TestFormData = z.infer<typeof testSchema>;

const TestComponent: React.FC<{
  onSubmit: (data: TestFormData) => void | Promise<void>;
  onCancel?: () => void;
}> = ({ onSubmit, onCancel }) => {
  const { actionButtons, handleSubmit } = useFormWrapper({
    id: 'test-form',
    schema: testSchema,
    defaultValues: { name: '' },
    onSubmit,
    onCancel,
  });

  return (
    <div>
      <button data-testid="submit-button" onClick={handleSubmit} disabled={actionButtons.Primary?.disabled}>
        {actionButtons.Primary?.label}
      </button>
      {actionButtons.Secondary && (
        <button
          data-testid="cancel-button"
          onClick={actionButtons.Secondary.onClick}
          disabled={actionButtons.Secondary.disabled}
        >
          {actionButtons.Secondary.label}
        </button>
      )}
    </div>
  );
};

describe('useFormWrapper', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('creates action buttons with correct properties', () => {
    render(<TestComponent onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

    const submitButton = screen.getByTestId('submit-button');
    const cancelButton = screen.getByTestId('cancel-button');

    expect(submitButton).toBeInTheDocument();
    expect(cancelButton).toBeInTheDocument();
    expect(submitButton).toHaveTextContent('Save changes');
    expect(cancelButton).toHaveTextContent('Cancel');
  });

  it('handleSubmit function returns void (not Promise)', () => {
    const TestComponentForReturnType: React.FC = () => {
      const { handleSubmit } = useFormWrapper({
        id: 'test-form',
        schema: testSchema,
        defaultValues: { name: '' },
        onSubmit: mockOnSubmit,
      });

      // This should not cause TypeScript errors since handleSubmit returns void
      const handleClick = () => {
        const result = handleSubmit();
        // result should be void, not Promise<void>
        expect(result).toBeUndefined();
      };

      return (
        <button data-testid="test-return-type" onClick={handleClick}>
          Test Return Type
        </button>
      );
    };

    render(<TestComponentForReturnType />);
    const button = screen.getByTestId('test-return-type');
    fireEvent.click(button);
  });

  it('handles cancel action correctly', () => {
    render(<TestComponent onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);

    const cancelButton = screen.getByTestId('cancel-button');
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('works without cancel handler', () => {
    render(<TestComponent onSubmit={mockOnSubmit} />);

    const submitButton = screen.getByTestId('submit-button');
    expect(submitButton).toBeInTheDocument();
    expect(screen.queryByTestId('cancel-button')).not.toBeInTheDocument();
  });
});
