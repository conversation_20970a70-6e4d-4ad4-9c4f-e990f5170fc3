import React from 'react';
import { SectionPanel, SectionPanelRow } from '@ceridianhcm/components';
import { ProfileHeader } from './components/ProfileHeader';
import {
  ContactView,
  PayrollView,
  PersonalInformationView,
  WorkInformationView,
  AddressView,
  EmergencyContactsView,
} from './views';
import { AddressProvider } from './components/Address/context/AddressContext';

interface AboutMeProps {
  id?: string;
}

export const AboutMe = ({ id = 'about-me' }: AboutMeProps) => {
  return (
    <SectionPanel id={`${id}-panel`} testId="about-me-panel-test-id">
      <SectionPanelRow id={`${id}-row-profile-header`} testId="section-profile-header-row-test-id">
        <ProfileHeader />
      </SectionPanelRow>
      <SectionPanelRow id={`${id}-row-address`} testId="section-address-row-test-id">
        <AddressProvider>
          <AddressView />
        </AddressProvider>
      </SectionPanelRow>
      <SectionPanelRow id={`${id}-row-contact`} testId="section-contact-row-test-id">
        <ContactView />
      </SectionPanelRow>
      <SectionPanelRow id={`${id}-row-emergency-contacts`} testId="section-emergency-contacts-row-test-id">
        <EmergencyContactsView />
      </SectionPanelRow>
      <SectionPanelRow id={`${id}-row-work-information`} testId="section-work-information-row-test-id">
        <WorkInformationView />
      </SectionPanelRow>
      <SectionPanelRow id={`${id}-row-personal-information`} testId="section-personal-information-row-test-id">
        <PersonalInformationView />
      </SectionPanelRow>
      <SectionPanelRow id={`${id}-row-payroll`} testId="section-payroll-row-test-id">
        <PayrollView />
      </SectionPanelRow>
    </SectionPanel>
  );
};

export default AboutMe;
