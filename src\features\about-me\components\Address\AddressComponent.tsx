import React, { useCallback } from 'react';
import { Container } from '@components';
import { PopoverMenu, PopoverMenuItem, TIconName } from '@ceridianhcm/components';
import { sanitizeHtml } from '@utils/sanitizeUtils';
import { AddressType } from './mocks';
import { useAddress } from './context/AddressContext';

export interface AddressComponentProps {
  /** Custom action handler for address type selection from popover */
  onAddressTypeSelect?: (addressType: AddressType) => void;
  /** Custom aria label for the address type popover */
  popoverAriaLabel?: string;
  /** Children components to render inside the address container */
  children?: React.ReactNode;
}

export const AddressPrimaryResidence = () => {
  const { primaryAddress } = useAddress();
  return (
    <Container.Section sectionTitle="Primary Residence" divider role="table" aria-label="Primary Residence">
      {primaryAddress?.AddressType && primaryAddress?.FormattedAddress && (
        <Container.Row
          label={primaryAddress.AddressType}
          value={sanitizeHtml(primaryAddress.FormattedAddress).replace(/\n/g, ', ')}
          role="row"
        />
      )}
    </Container.Section>
  );
};

export const AddressOtherAddresses = () => {
  const { otherAddresses } = useAddress();
  return (
    <Container.Section sectionTitle="Other Addresses" role="table" aria-label="Other Addresses">
      {otherAddresses?.map((address, index) => {
        const key = `${address.AddressTypeXrefCode}-${index}`;
        return (
          <Container.Row
            key={key}
            label={address.AddressType}
            value={sanitizeHtml(address.FormattedAddress).replace(/\n/g, ', ')}
            role="row"
          />
        );
      })}
    </Container.Section>
  );
};

/**
 * Custom AddressTypePopover component that uses PopoverMenu with proper configuration
 * Shows only the two main address type categories: Primary Residence and Other Addresses
 */

const AddressTypePopover: React.FC<{
  onSelect: (type: AddressType) => void;
  ariaLabel?: string;
  iconName?: TIconName;
  menuId?: string;
}> = ({ onSelect, ariaLabel, iconName = 'add', menuId = 'address-type-menu' }) => {
  // Simplified address type options - only showing the two main categories
  const addressTypeOptions: AddressType[] = [
    {
      ContactInformationTypeId: 1,
      ShortName: 'Primary',
      LongName: 'Primary Residence',
      XRefCode: 'PrimaryResidence',
      IsRequired: true,
      IsPersonal: true,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 2,
      ShortName: 'Other',
      LongName: 'Other Addresses',
      XRefCode: 'OtherAddresses',
      IsRequired: false,
      IsPersonal: true,
      IncludeMilitaryStates: false,
    },
  ];

  const handleSelect = ({ id }: { id: string }) => {
    const selectedType = addressTypeOptions.find((type) => type.XRefCode === id);
    if (selectedType) {
      onSelect(selectedType);
    }
  };

  return (
    <div data-testid="address-type-popover">
      <PopoverMenu
        id={menuId}
        triggerOption="iconButton"
        triggerProps={{
          variant: 'tertiaryNeutral',
          iconName: iconName,
          square: false,
          size: 'medium',
        }}
        buttonAriaLabel={ariaLabel || 'Select Address Type'}
        onChange={handleSelect}
      >
        {addressTypeOptions.map((type) => (
          <PopoverMenuItem key={type.XRefCode} id={type.XRefCode}>
            {type.LongName}
          </PopoverMenuItem>
        ))}
      </PopoverMenu>
    </div>
  );
};

export const AddressComponent: React.FC<AddressComponentProps> = ({
  onAddressTypeSelect,
  popoverAriaLabel = 'Add Address Type',
  children,
}) => {
  const handleEditAddressTypeChange = useCallback(
    (selectedType: AddressType) => {
      if (onAddressTypeSelect) {
        onAddressTypeSelect(selectedType);
      }
    },
    [onAddressTypeSelect],
  );

  return (
    <Container
      id="about-me-address"
      title="Addresses"
      className="about-me-address-class"
      ariaLabel="Address Information"
      dataTestId="about-me-address-testid"
      actionProps={{
        spacing: 8, // Match Contact Information component spacing
        actions: [
          {
            name: 'add',
            'aria-label': popoverAriaLabel,
            component: (
              <AddressTypePopover
                onSelect={handleEditAddressTypeChange}
                ariaLabel={popoverAriaLabel}
                iconName="add"
                menuId="address-add-menu"
              />
            ),
          },
          {
            name: 'edit',
            'aria-label': 'Edit Address Type',
            component: (
              <AddressTypePopover
                onSelect={handleEditAddressTypeChange}
                ariaLabel="Edit Address Type"
                iconName="edit"
                menuId="address-edit-menu"
              />
            ),
          },
        ],
      }}
    >
      {children}
    </Container>
  );
};
