import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AddressComponent, AddressPrimaryResidence, AddressOtherAddresses } from './AddressComponent';
import { AddressProvider } from './context/AddressContext';
import { EmployeePersonalAddressMockData } from './mocks/employeeAddressMockData';

const mockPrimaryAddress = EmployeePersonalAddressMockData.Result.PrimaryAddress;
const mockOtherAddresses = EmployeePersonalAddressMockData.Result.OtherAddresses;

// Mock scrollIntoView to fix JSDOM issues with PopoverMenu
Object.defineProperty(Element.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

// Expected address type options that should appear in the popover menu
const expectedAddressTypeOptions = [
  { LongName: 'Primary Residence', XRefCode: 'PrimaryResidence' },
  { LongName: 'Other Addresses', XRefCode: 'OtherAddresses' },
];

// Helper to DRY up rendering AddressComponent with provider and children
const renderAddressComponent = (children: React.ReactNode, props: any = {}) => {
  return render(
    <AddressProvider>
      <AddressComponent {...props}>{children}</AddressComponent>
    </AddressProvider>,
  );
};

describe('AddressComponent', () => {
  test('renders the component with enhanced Container actions', () => {
    renderAddressComponent(
      <>
        <AddressPrimaryResidence />
        <AddressOtherAddresses />
      </>,
    );

    const containerElement = screen.getByTestId('about-me-address-testid');
    expect(containerElement).toBeInTheDocument();

    // Check for title
    expect(screen.getByText('Addresses')).toBeInTheDocument();

    // Check for add button (custom AddressTypePopover component)
    const addButton = screen.getByLabelText('Add Address Type');
    expect(addButton).toBeInTheDocument();

    // Check for edit button
    const editButton = screen.getByLabelText('Edit Address Type');
    expect(editButton).toBeInTheDocument();
  });

  test('opens popover when add button is clicked', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    // Should render add button
    const addButton = screen.getByLabelText('Add Address Type');
    expect(addButton).toBeInTheDocument();

    // Click the add button to open popover
    fireEvent.click(addButton);

    // Check if popover is opened (address types should be visible)
    // Use getAllByText to handle multiple elements with same text
    const primaryResidenceOptions = screen.getAllByText('Primary Residence');
    const otherAddressesOptions = screen.getAllByText('Other Addresses');
    expect(primaryResidenceOptions.length).toBeGreaterThan(0);
    expect(otherAddressesOptions.length).toBeGreaterThan(0);
  });

  test('calls onAddressTypeSelect when popover selection changes', () => {
    const mockOnAddressTypeSelect = jest.fn();
    renderAddressComponent(<AddressPrimaryResidence />, { onAddressTypeSelect: mockOnAddressTypeSelect });

    // Should render add button
    const addButton = screen.getByLabelText('Add Address Type');
    expect(addButton).toBeInTheDocument();

    // Open popover and select an option
    fireEvent.click(addButton);
    // Use getAllByText and select the first one (from popover menu)
    const options = screen.getAllByText('Primary Residence');
    fireEvent.click(options[options.length - 1]); // Click the last one (likely the popover option)

    // Verify the callback was called
    expect(mockOnAddressTypeSelect).toHaveBeenCalled();
  });

  test('selects address type from popover and triggers callback', () => {
    const mockOnAddressTypeSelect = jest.fn();
    renderAddressComponent(<AddressPrimaryResidence />, { onAddressTypeSelect: mockOnAddressTypeSelect });

    // Open popover by clicking add button (which now has the popover)
    const addButton = screen.getByLabelText('Add Address Type');
    fireEvent.click(addButton);

    // Get first address type from expected options
    const firstAddressType = expectedAddressTypeOptions[0];
    const options = screen.getAllByText(firstAddressType.LongName);
    fireEvent.click(options[options.length - 1]); // Click the last one (likely the popover option)

    // The callback should be called with the full AddressType object, not just the simplified version
    expect(mockOnAddressTypeSelect).toHaveBeenCalledWith(
      expect.objectContaining({
        LongName: firstAddressType.LongName,
        XRefCode: firstAddressType.XRefCode,
      }),
    );
  });

  test('handles address type selection when no callback is provided', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    // Open popover by clicking add button (which now has the popover)
    const addButton = screen.getByLabelText('Add Address Type');
    fireEvent.click(addButton);

    // Get first address type from expected options
    const firstAddressType = expectedAddressTypeOptions[0];
    const options = screen.getAllByText(firstAddressType.LongName);

    // Should not throw error when clicking without callback
    expect(() => fireEvent.click(options[options.length - 1])).not.toThrow();
  });

  test('renders add popover with address type options when opened', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    // Check that add button is rendered
    const addButton = screen.getByLabelText('Add Address Type');
    expect(addButton).toBeInTheDocument();

    // Open the popover
    fireEvent.click(addButton);

    // Check that address type options are rendered
    expectedAddressTypeOptions.forEach((type) => {
      const elements = screen.getAllByText(type.LongName);
      expect(elements.length).toBeGreaterThan(0);
    });
  });

  test('uses custom popover aria label when provided', () => {
    renderAddressComponent(<AddressPrimaryResidence />, { popoverAriaLabel: 'Custom Popover Label' });

    // The custom aria label should be applied to the add button
    const addButton = screen.getByLabelText('Custom Popover Label');
    expect(addButton).toBeInTheDocument();

    // The edit button should still have its default label
    const editButton = screen.getByLabelText('Edit Address Type');
    expect(editButton).toBeInTheDocument();
  });

  test('renders with proper Container action layout', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    // Check that Container header actions are rendered
    const addButton = screen.getByLabelText('Add Address Type');
    const editButton = screen.getByLabelText('Edit Address Type');

    expect(addButton).toBeInTheDocument();
    expect(editButton).toBeInTheDocument();
  });

  test('handles edge case when onAddressTypeSelect is not provided', () => {
    // This test ensures the component doesn't crash when callback is not provided
    renderAddressComponent(<AddressPrimaryResidence />);

    const addButton = screen.getByLabelText('Add Address Type');
    expect(addButton).toBeInTheDocument();

    // Component should render without errors even without callback
    expect(screen.getByText('Addresses')).toBeInTheDocument();
  });

  test('renders edit popover with address type options when opened', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    // Check that edit button is rendered
    const editButton = screen.getByLabelText('Edit Address Type');
    expect(editButton).toBeInTheDocument();

    // Open the popover
    fireEvent.click(editButton);

    // Check that address type options are rendered
    expectedAddressTypeOptions.forEach((type) => {
      const elements = screen.getAllByText(type.LongName);
      expect(elements.length).toBeGreaterThan(0);
    });
  });

  test('popover menu shows only the two expected address type options', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    // Open the add popover (which now has the actual popover functionality)
    const addButton = screen.getByLabelText('Add Address Type');
    fireEvent.click(addButton);

    // Verify exactly 2 options are present (using getAllByText to handle duplicates)
    const primaryResidenceElements = screen.getAllByText('Primary Residence');
    const otherAddressesElements = screen.getAllByText('Other Addresses');
    expect(primaryResidenceElements.length).toBeGreaterThan(0);
    expect(otherAddressesElements.length).toBeGreaterThan(0);

    // Verify that old mock data options are not present
    expect(screen.queryByText('Secondary')).not.toBeInTheDocument();
    expect(screen.queryByText('Temporary')).not.toBeInTheDocument();
    expect(screen.queryByText('Business Address')).not.toBeInTheDocument();
    expect(screen.queryByText('Personal Address')).not.toBeInTheDocument();
  });

  test('handles edit button click', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    const editButton = screen.getByLabelText('Edit Address Type');
    expect(editButton).toBeInTheDocument();

    // Should not throw error when clicked
    fireEvent.click(editButton);
    expect(screen.getByText('Addresses')).toBeInTheDocument();
  });
});

describe('AddressPrimaryResidence', () => {
  beforeEach(() => {
    jest.spyOn(require('./context/AddressContext'), 'useAddress').mockImplementation(() => ({
      primaryAddress: mockPrimaryAddress,
      setPrimaryAddress: jest.fn(),
    }));
  });

  test('renders primary residence section with mock data', () => {
    renderAddressComponent(<AddressPrimaryResidence />);

    const sectionTitles = screen.getAllByText('Primary Residence');
    expect(sectionTitles.length).toBeGreaterThan(0);
    expect(screen.getByText('1001 Eastern Ave, Toronto, ON, M4L 3Z5, Canada')).toBeInTheDocument();
  });
});

describe('AddressOtherAddresses', () => {
  beforeEach(() => {
    jest.spyOn(require('./context/AddressContext'), 'useAddress').mockImplementation(() => ({
      otherAddresses: mockOtherAddresses,
      setOtherAddresses: jest.fn(),
    }));
  });

  test('renders other addresses section with mock data', () => {
    renderAddressComponent(<AddressOtherAddresses />);

    // Check for section title
    const sectionTitles = screen.getAllByText('Other Addresses');
    expect(sectionTitles.length).toBeGreaterThan(0);

    // Check for mailing address
    expect(screen.getByText('Mailing')).toBeInTheDocument();
    expect(screen.getByText('128A Sterling Rd, Toronto, ON, M6R 0C6, Canada')).toBeInTheDocument();
  });
});

describe('AddressComponent Integration', () => {
  beforeEach(() => {
    jest.spyOn(require('./context/AddressContext'), 'useAddress').mockImplementation(() => ({
      primaryAddress: mockPrimaryAddress,
      otherAddresses: mockOtherAddresses,
      setPrimaryAddress: jest.fn(),
      setOtherAddresses: jest.fn(),
    }));
  });

  test('renders complete address component with all sections', () => {
    renderAddressComponent(
      <>
        <AddressPrimaryResidence />
        <AddressOtherAddresses />
      </>,
    );

    // Check main container
    expect(screen.getByTestId('about-me-address-testid')).toBeInTheDocument();
    expect(screen.getByText('Addresses')).toBeInTheDocument();

    // Check sections (using getAllByText to handle duplicates)
    const primaryResidenceElements = screen.getAllByText('Primary Residence');
    const otherAddressesElements = screen.getAllByText('Other Addresses');
    expect(primaryResidenceElements.length).toBeGreaterThan(0);
    expect(otherAddressesElements.length).toBeGreaterThan(0);

    // Check address data
    expect(screen.getByText('1001 Eastern Ave, Toronto, ON, M4L 3Z5, Canada')).toBeInTheDocument();
    expect(screen.getByText('128A Sterling Rd, Toronto, ON, M6R 0C6, Canada')).toBeInTheDocument();
  });
});
