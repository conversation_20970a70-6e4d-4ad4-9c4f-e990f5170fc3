import { TApiResponse } from '../../../../../typings/api';
import { handleApiRequest } from '../../../../../api/baseApi';
import { API_ENDPOINTS } from '../../../../../api/apiConfig';

import { IEmployeeAddressResult } from '../models';
import { EmployeePersonalAddressMockData } from '../mocks';
import { getAddresses } from './personalAddressApi';

// Mock the handleApiRequest function
jest.mock('../../../../../api/baseApi', () => ({
  handleApiRequest: jest.fn(),
}));

// Mock API_ENDPOINTS
jest.mock('../../../../../api/apiConfig', () => ({
  API_ENDPOINTS: {
    PERSONAL_ADDRESS: '/api/personal/address',
  },
}));

const mockHandleApiRequest = handleApiRequest as jest.MockedFunction<typeof handleApiRequest>;

describe('Personal Address API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAddresses', () => {
    it('should call handleApiRequest with correct parameters', async () => {
      const mockResponse: TApiResponse<IEmployeeAddressResult> = EmployeePersonalAddressMockData;
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getAddresses();

      expect(mockHandleApiRequest).toHaveBeenCalledWith(
        'POST',
        API_ENDPOINTS.PERSONAL_ADDRESS,
        EmployeePersonalAddressMockData,
        'Failed to fetch personal address.',
      );
      expect(result).toEqual(mockResponse);
    });
    it('should handle API request failure', async () => {
      const mockError = new Error('Network error');
      mockHandleApiRequest.mockRejectedValue(mockError);

      await expect(getAddresses()).rejects.toThrow('Network error');
    });
  });
});
