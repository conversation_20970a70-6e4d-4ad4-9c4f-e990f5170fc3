import { handleApiRequest } from '../../../../../api/baseApi';
import type { TApiResponse } from '../../../../../typings/api';
import { API_ENDPOINTS } from '../../../../../api/apiConfig';
import { EmployeePersonalAddressMockData } from '../mocks';
import { IEmployeeAddressResult } from '../models';

export const getAddresses = async (): Promise<TApiResponse<IEmployeeAddressResult>> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.PERSONAL_ADDRESS,
    EmployeePersonalAddressMockData,
    'Failed to fetch personal address.',
  );
};
