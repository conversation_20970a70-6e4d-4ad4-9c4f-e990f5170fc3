import React, { createContext, useContext, useState, useMemo, ReactNode } from 'react';
import { IAddress } from '../models';

interface AddressContextType {
  primaryAddress: IAddress | null;
  otherAddresses: IAddress[] | null;
  setPrimaryAddress: React.Dispatch<React.SetStateAction<IAddress | null>>;
  setOtherAddresses: React.Dispatch<React.SetStateAction<IAddress[] | null>>;
}

const AddressContext = createContext<AddressContextType | null>(null);

export const AddressProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [primaryAddress, setPrimaryAddress] = useState<IAddress | null>(null);
  const [otherAddresses, setOtherAddresses] = useState<IAddress[] | null>(null);

  const value = useMemo(
    () => ({
      primaryAddress,
      otherAddresses,
      setPrimaryAddress,
      setOtherAddresses,
    }),
    [primaryAddress, otherAddresses],
  );

  return <AddressContext.Provider value={value}>{children}</AddressContext.Provider>;
};

export const useAddress = (): AddressContextType => {
  const context = useContext(AddressContext);
  if (!context) {
    throw new Error('useAddress must be used within a AddressProvider');
  }
  return context;
};
