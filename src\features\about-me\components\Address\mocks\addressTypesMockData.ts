import { IDataItem } from '@components/Dropdown';

export interface AddressType {
  ContactInformationTypeId: number;
  ShortName: string;
  LongName: string;
  XRefCode: string;
  IsRequired: boolean;
  IsPersonal: boolean | null;
  IncludeMilitaryStates: boolean;
}

// Mock data for available address types based on the existing mock data
export const AddressTypesMockData: AddressType[] = [
  {
    ContactInformationTypeId: 17,
    ShortName: 'Secondary',
    LongName: 'Secondary',
    XRefCode: 'Secondary',
    IsRequired: false,
    IsPersonal: null,
    IncludeMilitaryStates: false,
  },
  {
    ContactInformationTypeId: 28,
    ShortName: 'Temporary',
    LongName: 'Temporary',
    XRefCode: 'TemporaryAddress',
    IsRequired: false,
    IsPersonal: null,
    IncludeMilitaryStates: false,
  },
  {
    ContactInformationTypeId: 36,
    ShortName: 'Business',
    LongName: 'Business Address',
    XRefCode: 'ATBusinessAddress',
    IsRequired: false,
    IsPersonal: null,
    IncludeMilitaryStates: false,
  },
  {
    ContactInformationTypeId: 37,
    ShortName: 'Personal',
    LongName: 'Personal Address',
    XRefCode: 'ATPersonalAddress',
    IsRequired: false,
    IsPersonal: null,
    IncludeMilitaryStates: false,
  },
  {
    ContactInformationTypeId: 55,
    ShortName: 'Business Alt',
    LongName: 'Business Address 1',
    XRefCode: 'ATBusinessAddress1',
    IsRequired: false,
    IsPersonal: null,
    IncludeMilitaryStates: false,
  },
  {
    ContactInformationTypeId: 56,
    ShortName: 'Business Alt 2',
    LongName: 'Business Address 2',
    XRefCode: 'ATBusinessAddress2',
    IsRequired: false,
    IsPersonal: null,
    IncludeMilitaryStates: false,
  },
  {
    ContactInformationTypeId: 57,
    ShortName: 'Personal Alt',
    LongName: 'Personal Address 1',
    XRefCode: 'ATPersonalAddress1',
    IsRequired: false,
    IsPersonal: null,
    IncludeMilitaryStates: false,
  },
];

// Convert address types to dropdown format
export const AddressTypesDropdownData: IDataItem[] = AddressTypesMockData.map((addressType) => ({
  id: addressType.XRefCode,
  title: addressType.LongName,
}));
