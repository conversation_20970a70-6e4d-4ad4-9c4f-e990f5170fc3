import type { TApiResponse } from '@typings/api';
import { IEmployeeAddressResult } from '../models';

const EmployeeAddressResult: IEmployeeAddressResult = {
  PrimaryAddress: {
    FormattedAddress: '1001 Eastern Ave\nToronto, ON\nM4L 3Z5\nCanada',
    AddressType: 'Primary Residence',
    AddressTypeXrefCode: 'PrimaryResidence',
    IsPayrollMailing: null,
    IsPrimaryResidence: true,
    FutureChangesCount: 0,
    IncludeMilitaryStates: false,
  },
  OtherAddresses: [
    {
      FormattedAddress: '128A Sterling Rd\nToronto, ON\nM6R 0C6\nCanada',
      AddressType: 'Mailing',
      AddressTypeXrefCode: 'Mailing',
      IsPayrollMailing: null,
      IsPrimaryResidence: null,
      FutureChangesCount: 0,
      IncludeMilitaryStates: false,
    },
  ],
  PayrollMailingAddress: {
    FormattedAddress: '150 Symes Rd\nToronto, ON\nM6N 0B1\nCanada',
    AddressType: 'Primary Residence',
    AddressTypeXrefCode: 'PrimaryResidence',
    IsPayrollMailing: null,
    IsPrimaryResidence: null,
    FutureChangesCount: null,
    IncludeMilitaryStates: false,
  },
  IsAddressChangeRequestInProcess: false,
  CanWithdrawChangeRequest: false,
  CanAddNewAddressType: true,
  AvailableAddressTypes: [
    {
      ContactInformationTypeId: 17,
      ShortName: 'Secondary',
      LongName: 'Secondary',
      XRefCode: 'Secondary',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 28,
      ShortName: 'Temporary',
      LongName: 'Temporary',
      XRefCode: 'TemporaryAddress',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 36,
      ShortName: 'ATBusinessAddress',
      LongName: 'ATBusinessAddress',
      XRefCode: 'ATBusinessAddress',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 37,
      ShortName: 'ATPersonalAddress',
      LongName: 'ATPersonalAddress',
      XRefCode: 'ATPersonalAddress',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 54,
      ShortName: 'Sanitized Image',
      LongName: 'Sanitized Image',
      XRefCode: 'Sanitized Image',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 55,
      ShortName: 'ATBusinessAddress1',
      LongName: 'ATBusinessAddress1',
      XRefCode: 'ATBusinessAddress1',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 56,
      ShortName: 'ATBusinessAddress2',
      LongName: 'ATBusinessAddress2',
      XRefCode: 'ATBusinessAddress2',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
    {
      ContactInformationTypeId: 57,
      ShortName: 'ATPersonalAddress1',
      LongName: 'ATPersonalAddress1',
      XRefCode: 'ATPersonalAddress1',
      IsRequired: false,
      IsPersonal: null,
      IncludeMilitaryStates: false,
    },
  ],
  ForceUseWorkflowForm: false,
  CanManagePayrollMailingAddress: false,
};

export const EmployeePersonalAddressMockData: TApiResponse<IEmployeeAddressResult> = {
  SecuredPropertiesKey: '00000000-0000-0000-0000-000000000000',
  Success: true,
  ErrorMessage: null,
  Errors: [],
  Warnings: [],
  Informationals: [],
  Result: EmployeeAddressResult,
};
