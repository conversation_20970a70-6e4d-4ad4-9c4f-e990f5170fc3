export interface IAddress {
  FormattedAddress: string;
  AddressType: string;
  AddressTypeXrefCode: string;
  IsPayrollMailing: boolean | null;
  IsPrimaryResidence: boolean | null;
  FutureChangesCount: number | null;
  IncludeMilitaryStates: boolean;
}

export interface IAvailableAddressType {
  ContactInformationTypeId: number;
  ShortName: string;
  LongName: string;
  XRefCode: string;
  IsRequired: boolean;
  IsPersonal: boolean | null;
  IncludeMilitaryStates: boolean;
}

export interface IEmployeeAddressResult {
  PrimaryAddress: IAddress;
  OtherAddresses: IAddress[];
  PayrollMailingAddress: IAddress;
  IsAddressChangeRequestInProcess: boolean;
  CanWithdrawChangeRequest: boolean;
  CanAddNewAddressType: boolean;
  AvailableAddressTypes: IAvailableAddressType[];
  ForceUseWorkflowForm: boolean;
  CanManagePayrollMailingAddress: boolean;
}

export interface IEmployeeAddressResponse {
  SecuredPropertiesKey: string;
  Success: boolean;
  ErrorMessage: string | null;
  Errors: string[];
  Warnings: string[];
  Informationals: string[];
  Result: IEmployeeAddressResult;
}
