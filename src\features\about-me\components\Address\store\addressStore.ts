import { BaseStore } from '@store/baseStore';
import { TApiResponse } from '@/typings';

import * as AddressApi from '../api/personalAddressApi';
import { IEmployeeAddressResult } from '../models';

export class AddressStore extends BaseStore {
  destroy() {
    // clean up
  }

  async fetchEmployeeAddresses(): Promise<TApiResponse<IEmployeeAddressResult>> {
    return AddressApi.getAddresses();
  }
}
