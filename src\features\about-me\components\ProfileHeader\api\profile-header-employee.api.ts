import { API_ENDPOINTS } from '@api/apiConfig';
import { handleApiRequest } from '@api/baseApi';

import { IEmployeeProfilePronoun } from '../models/profile-header-employee.types';
import { EmployeePronounsMockData } from '../mocks/profile-header-employee.mock';

/**
 * API functions for employee-related data in ProfileHeader feature
 */

/**
 * Fetches available employee pronouns
 * @returns Promise containing array of employee pronoun options
 */
export const getEmployeePronouns = async (): Promise<IEmployeeProfilePronoun[]> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.EMPLOYEE_PRONOUNS,
    EmployeePronounsMockData,
    'Failed to fetch employee pronouns.',
  );
};
