import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { ProfileEditForm } from './ProfileEditForm';
import { ProfileEditProvider } from './context/ProfileEditContext';
import { useProfileEditForm } from './hooks/useProfileEditForm';

// Mock only our custom hooks, not react-hook-form
jest.mock('./hooks/useProfileEditForm', () => ({
  useProfileEditForm: jest.fn(),
}));

// Mock external API calls and services
jest.mock('../../api/profile-header-employee.api', () => ({
  getEmployeePronouns: jest.fn().mockResolvedValue([]),
}));

jest.mock('./services/profileImageService', () => ({
  profileImageService: {
    uploadImage: jest.fn().mockResolvedValue({ imageData: 'mock-image-data' }),
    removeImage: jest.fn().mockResolvedValue({ success: true }),
  },
}));

describe('ProfileEditForm', () => {
  const defaultProps = {
    testId: 'test-profile-edit-form',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useProfileEditForm with simple return values
    (useProfileEditForm as jest.Mock).mockReturnValue({
      state: {
        isLoading: false,
        error: null,
        photoData: null,
        pronounOptions: [
          { id: 'they-them', title: 'They/Them' },
          { id: 'she-her', title: 'She/Her' },
          { id: 'he-him', title: 'He/Him' },
        ],
      },
      actions: {
        handlePhotoUpload: jest.fn().mockResolvedValue('new-photo-data'),
        handlePhotoRemove: jest.fn(),
        submitProfile: jest.fn(),
      },
    });
  });

  it('renders photo section', () => {
    render(
      <ProfileEditProvider initialData={{ profilePhoto: '', biography: '', pronouns: '' }}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    expect(screen.getByTestId('profile-photo-photo')).toBeInTheDocument();
    expect(screen.getByTestId('upload-photo-button')).toBeInTheDocument();
    expect(screen.getByTestId('remove-photo-button')).toBeInTheDocument();
  });

  it('renders pronouns dropdown with options', () => {
    render(
      <ProfileEditProvider initialData={{ profilePhoto: '', biography: '', pronouns: '' }}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    expect(screen.getByTestId('pronouns-dropdown')).toBeInTheDocument();
  });

  it('handles photo upload', async () => {
    const mockHandlePhotoUpload = jest.fn().mockResolvedValue('new-photo-data');
    (useProfileEditForm as jest.Mock).mockReturnValue({
      state: {
        isLoading: false,
        error: null,
        photoData: null,
        pronounOptions: [],
      },
      actions: {
        handlePhotoUpload: mockHandlePhotoUpload,
        handlePhotoRemove: jest.fn(),
        submitProfile: jest.fn(),
      },
    });

    render(
      <ProfileEditProvider initialData={{ profilePhoto: '', biography: '', pronouns: '' }}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = screen.getByTestId('photo-upload-input');

    await act(async () => {
      fireEvent.change(input, { target: { files: [file] } });
    });

    expect(mockHandlePhotoUpload).toHaveBeenCalledWith(file);
  });

  it('handles photo remove', async () => {
    const mockHandlePhotoRemove = jest.fn();
    (useProfileEditForm as jest.Mock).mockReturnValue({
      state: {
        isLoading: false,
        error: null,
        photoData: 'existing-photo-data',
        pronounOptions: [],
      },
      actions: {
        handlePhotoUpload: jest.fn(),
        handlePhotoRemove: mockHandlePhotoRemove,
        submitProfile: jest.fn(),
      },
    });

    render(
      <ProfileEditProvider initialData={{ profilePhoto: 'test.jpg', biography: '', pronouns: '' }}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    const removeButton = screen.getByTestId('remove-photo-button');

    await act(async () => {
      fireEvent.click(removeButton);
    });

    expect(mockHandlePhotoRemove).toHaveBeenCalled();
  });

  it('submits form with profile data', async () => {
    const mockSubmitProfile = jest.fn();
    (useProfileEditForm as jest.Mock).mockReturnValue({
      state: {
        isLoading: false,
        error: null,
        photoData: null,
        pronounOptions: [],
      },
      actions: {
        handlePhotoUpload: jest.fn(),
        handlePhotoRemove: jest.fn(),
        submitProfile: mockSubmitProfile,
      },
    });

    const testData = {
      profilePhoto: 'test.jpg',
      biography: 'Test bio',
      pronouns: 'they/them',
    };

    render(
      <ProfileEditProvider initialData={testData}>
        <ProfileEditForm {...defaultProps} />
      </ProfileEditProvider>,
    );

    const form = screen.getByTestId('test-profile-edit-form');

    await act(async () => {
      fireEvent.submit(form);
    });

    // The form submission is handled by the ProfileEditProvider and useFormWrapper
    // We can verify that the component renders without errors during submission
    expect(form).toBeInTheDocument();
  });
});
