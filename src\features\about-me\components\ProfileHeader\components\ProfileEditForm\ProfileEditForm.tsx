import React from 'react';
import { FormWrapper, FormDropdown } from '@components/forms';
import { useFormWrapper } from '@components/forms/hooks/useFormWrapper';
import { Button } from '@components/Button';
import { ProfileEditAvatar } from '../ProfileEditAvatar';
import { useProfileEditForm } from './hooks/useProfileEditForm';
import { profileEditFormSchema, ProfileEditFormData } from './profile-edit-form-schema';
import './profile-edit-form.scss';

interface ProfileEditFormProps {
  testId?: string;
}

export const ProfileEditForm: React.FC<ProfileEditFormProps> = ({ testId = 'edit-profile-form' }) => {
  const { methods: formMethods } = useFormWrapper<ProfileEditFormData>({
    id: 'edit-profile-form',
    schema: profileEditFormSchema,
    defaultValues: {},
    onSubmit: async (data) => {
      console.log('Submitting profile:', data);
      // Placeholder - actual submission handled by Form<PERSON><PERSON>per's onSubmit prop
    },
  });

  const {
    state: { isLoading, photoData, pronounOptions },
    actions: { handlePhotoUpload, handlePhotoRemove, submitProfile },
  } = useProfileEditForm(formMethods);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        const imageData = await handlePhotoUpload(file);
        formMethods.setValue('profilePhoto', imageData);
      } catch (error) {
        console.error('Photo upload failed:', error);
      }
    }
  };

  const handlePhotoRemoveClick = async () => {
    await handlePhotoRemove();
    formMethods.setValue('profilePhoto', '');
  };

  return (
    <FormWrapper
      id="edit-profile-form"
      form={formMethods}
      schema={profileEditFormSchema}
      onSubmit={submitProfile}
      className="edit-profile-form"
      testId={testId}
    >
      <div className="edit-profile-form__content">
        <div className="edit-profile-form__media-section">
          <div className="edit-profile-form__photo-section">
            <ProfileEditAvatar testId="profile-photo" photo={photoData || formMethods.watch('profilePhoto')} />
            <div className="edit-profile-form__photo-actions">
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
                id="photo-upload-input"
                data-testid="photo-upload-input"
              />
              <Button
                id="upload-photo-button"
                testId="upload-photo-button"
                label="Upload a new photo"
                variant="primary"
                onClick={() => document.getElementById('photo-upload-input')?.click()}
                disabled={isLoading}
                size="medium"
              />
              <Button
                id="remove-photo-button"
                testId="remove-photo-button"
                label="Remove"
                variant="secondary"
                onClick={handlePhotoRemoveClick}
                disabled={isLoading || !(photoData || formMethods.watch('profilePhoto'))}
                size="medium"
              />
            </div>
          </div>
          <div className="edit-profile-form__pronouns-section">
            <FormDropdown
              id="pronouns"
              name="pronouns"
              control={formMethods.control}
              label="Pronouns"
              testId="pronouns-dropdown"
              options={pronounOptions}
              noResultsText="No pronouns found"
            />
          </div>
        </div>
      </div>
    </FormWrapper>
  );
};

ProfileEditForm.displayName = 'ProfileEditForm';
