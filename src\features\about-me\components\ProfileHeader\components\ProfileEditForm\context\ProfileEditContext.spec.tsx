import React from 'react';
import { render, screen } from '@testing-library/react';

import { ProfileEditProvider, useProfileEditContext } from './ProfileEditContext';

const TestComponent = () => {
  const context = useProfileEditContext();
  return (
    <div>
      <div data-testid="loading">{context.state.isLoading.toString()}</div>
      <div data-testid="error">{context.state.error || 'no error'}</div>
      <div data-testid="photo-data">{context.state.photoData || 'no photo'}</div>
    </div>
  );
};

describe('ProfileEditContext', () => {
  it('provides initial context values', () => {
    render(
      <ProfileEditProvider>
        <TestComponent />
      </ProfileEditProvider>,
    );

    expect(screen.getByTestId('loading')).toHaveTextContent('false');
    expect(screen.getByTestId('error')).toHaveTextContent('no error');
    expect(screen.getByTestId('photo-data')).toHaveTextContent('no photo');
  });
});
