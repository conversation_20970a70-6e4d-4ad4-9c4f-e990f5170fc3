import { IEmployeeProfilePronoun } from '../models/profile-header-employee.types';

/**
 * Mock data for employee pronouns used in ProfileHeader feature
 */
export const EmployeePronounsMockData: IEmployeeProfilePronoun[] = [
  {
    PronounId: 119,
    LongName: 'She/Her/Hers',
    ShortName: 'She/Her/Hers',
    XrefCode: 'SHEHERHERS',
  },
  {
    PronounId: 124,
    LongName: 'He/Him/His',
    ShortName: 'He/Him/His',
    XrefCode: 'HEHIMHIS',
  },
  {
    PronounId: 129,
    LongName: 'They/Them/Theirs',
    ShortName: 'They/Them/Theirs',
    XrefCode: 'THEYTHEMTHEIRS',
  },
  {
    PronounId: 134,
    LongName: 'Ze/Per/Hir/They',
    ShortName: 'Ze/Per/Hir/They',
    XrefCode: 'ZEPERHIRTHEY',
  },
];
