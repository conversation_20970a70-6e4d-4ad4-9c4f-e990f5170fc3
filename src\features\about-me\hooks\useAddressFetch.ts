import { useFetchConfig } from '@hooks/useFetchConfig';
import { useStore } from '@hooks/useStore';
import { useAddress } from '../components/Address/context/AddressContext';
import { AddressStore } from '../components/Address/store/addressStore';
import { TApiResponse } from '@/typings';
import { IEmployeeAddressResult } from '../components/Address/models';

/**
 * Custom hook to fetch and set employee address data using AddressStore and AddressContext.
 * Handles primary and other addresses population.
 */
export const useAddressFetch = () => {
  const { setPrimaryAddress, setOtherAddresses } = useAddress();
  const addressStore = useStore(AddressStore);

  const fetchConfigAddressInfo = {
    addresses: {
      fetch: addressStore.fetchEmployeeAddresses,
      onSuccess: (res: TApiResponse<IEmployeeAddressResult>) => {
        setPrimaryAddress(res.Result.PrimaryAddress);
        setOtherAddresses(res.Result.OtherAddresses ?? []);
      },
      errorMessages: 'Failed to fetch primary and/or other addresses.',
    },
  };

  useFetchConfig(fetchConfigAddressInfo);
};
