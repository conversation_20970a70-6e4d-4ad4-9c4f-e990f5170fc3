import React from 'react';
import { AddressComponent, AddressPrimaryResidence, AddressOtherAddresses } from '../components/Address';
import { useAddressFetch } from '../hooks/useAddressFetch';

export const AddressView = () => {
  useAddressFetch();
  return (
    <AddressComponent>
      <AddressPrimaryResidence />
      <AddressOtherAddresses />
    </AddressComponent>
  );
};

export const AddressViewWithCustomActions = () => {
  useAddressFetch();
  return (
    <AddressComponent>
      <AddressPrimaryResidence />
      <AddressOtherAddresses />
    </AddressComponent>
  );
};

export default AddressView;
