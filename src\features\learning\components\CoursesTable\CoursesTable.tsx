import React from 'react';
import { usePageShell } from '@components/PageShell';
import { Table } from '@components/Table';
import { TableCellLayout } from '@ceridianhcm/components';
import { ICourseTableVM } from '../../utils';

interface ICoursesTableProps {
  id?: string;
  dataTestId?: string;
  data: ICourseTableVM[];
  rowsPerPage?: number;
}

export const CoursesTable: React.FC<ICoursesTableProps> = ({
  id = 'courses-table',
  dataTestId = 'courses-table-test-id',
  data,
  rowsPerPage = 5,
}) => {
  const { isMobile } = usePageShell();

  const defaultTextCell = (cellContext: any) => (
    <TableCellLayout flexDirection="row" flexGap>
      <span className="cell-text-dark">{cellContext.getValue ? cellContext.getValue() : cellContext}</span>
    </TableCellLayout>
  );

  const columnDefinitions = [
    { field: 'course', label: 'Course', sortable: true, cellTemplate: defaultTextCell },
    { field: 'learningPlan', label: 'Learning Plan', sortable: true, cellTemplate: defaultTextCell },
    { field: 'status', label: 'Status', sortable: true, cellTemplate: defaultTextCell },
    { field: 'startDate', label: 'Start Date', sortable: true, cellTemplate: defaultTextCell },
    { field: 'completionDate', label: 'Completion Date', sortable: true, cellTemplate: defaultTextCell },
    { field: 'credits', label: 'Credits', sortable: true, cellTemplate: defaultTextCell },
    { field: 'score', label: 'Score', sortable: true, cellTemplate: defaultTextCell },
  ];

  return (
    <Table<ICourseTableVM>
      id={id}
      dataTestId={dataTestId}
      data={data}
      mobile={isMobile}
      ariaLabel="Courses Table"
      rowsPerPage={rowsPerPage}
      hasTablePagination
      columnDefinitions={columnDefinitions}
    />
  );
};
