import React from 'react';
import { render, screen } from '@testing-library/react';
import { CoursesTable } from './CoursesTable';
import { ICourseTableVM } from '../../utils';

// Mock the dependencies
jest.mock('@components/PageShell', () => ({
  usePageShell: jest.fn(() => ({
    isMobile: false,
    breakpoint: 'lg',
    leftPanelExpanded: true,
    leftPanelPersistent: true,
    setLeftPanelExpanded: jest.fn(),
    sidePanelSize: 'md',
  })),
}));

jest.mock('@components/Table', () => ({
  Table: ({ dataTestId, data, columnDefinitions }: any) => (
    <div data-testid={dataTestId}>
      <table>
        <thead>
          <tr>
            {columnDefinitions.map((col: any) => (
              <th key={col.field}>{col.label}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row: any) => (
            <tr key={row.id}>
              {columnDefinitions.map((col: any) => (
                <td key={`${row.id}-${col.field}`} data-testid={`cell-${col.field}`}>
                  {row[col.field]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
}));

describe('CoursesTable', () => {
  const mockData: ICourseTableVM[] = [
    {
      id: 1,
      course: 'Introduction to React',
      learningPlan: 'Development Plan',
      status: 'Completed',
      startDate: '6/1/2025',
      completionDate: '6/15/2025',
      credits: '3 credits',
      score: '95%',
    },
    {
      id: 2,
      course: 'Advanced TypeScript',
      learningPlan: 'N/A',
      status: 'In Progress',
      startDate: '6/10/2025',
      completionDate: null,
      credits: '4 credits',
      score: null,
    },
  ];

  it('renders the table with default props', () => {
    render(<CoursesTable data={mockData} />);
    expect(screen.getByTestId('courses-table-test-id')).toBeInTheDocument();
  });

  it('renders the table with correct column headers', () => {
    render(<CoursesTable data={mockData} />);
    expect(screen.getByText('Course')).toBeInTheDocument();
    expect(screen.getByText('Learning Plan')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Start Date')).toBeInTheDocument();
    expect(screen.getByText('Completion Date')).toBeInTheDocument();
    expect(screen.getByText('Credits')).toBeInTheDocument();
    expect(screen.getByText('Score')).toBeInTheDocument();
  });

  it('renders with custom props', () => {
    render(<CoursesTable id="custom-id" dataTestId="custom-test-id" data={mockData} rowsPerPage={10} />);
    expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
  });

  it('displays data correctly', () => {
    render(<CoursesTable data={mockData} />);
    expect(screen.getByText('Introduction to React')).toBeInTheDocument();
    expect(screen.getByText('Development Plan')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByText('6/1/2025')).toBeInTheDocument();
    expect(screen.getByText('6/15/2025')).toBeInTheDocument();
    expect(screen.getByText('3 credits')).toBeInTheDocument();
    expect(screen.getByText('95%')).toBeInTheDocument();
  });
});
