import React, { useState } from 'react';
import { AccordionDetail, AccordionSummary, AccordionContent, TableCellLayout } from '@ceridianhcm/components';
import { usePageShell } from '@components/PageShell';
import { Container, Table } from '@components';

import { ILearningPlanGroupVM, ICourseTableVM } from '../../utils';

interface ILearningPlanGroupProps {
  learningPlan: ILearningPlanGroupVM;
}

// TODO: create table cell component
const defaultTextCell = (cellContext: any) => (
  <TableCellLayout flexDirection="row" flexGap>
    <span className="cell-text-dark">{cellContext.getValue ? cellContext.getValue() : cellContext}</span>
  </TableCellLayout>
);

export const LearningPlanGroup: React.FC<ILearningPlanGroupProps> = ({ learningPlan }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { isMobile } = usePageShell();

  const columnDefinitions = [
    { field: 'course', label: 'Course', sortable: true, cellTemplate: defaultTextCell },
    { field: 'status', label: 'Status', sortable: true, cellTemplate: defaultTextCell },
    { field: 'startDate', label: 'Start Date', sortable: true, cellTemplate: defaultTextCell },
    { field: 'completionDate', label: 'Completion Date', sortable: true, cellTemplate: defaultTextCell },
    { field: 'credits', label: 'Credits', sortable: true, cellTemplate: defaultTextCell },
    { field: 'score', label: 'Score', sortable: true, cellTemplate: defaultTextCell },
  ];

  return (
    <AccordionDetail
      id={`learning-plan-accordion-detail-${learningPlan.id}`}
      open={isOpen}
      openIconName="chevronUpSmall"
      closeIconName="chevronDownSmall"
      iconPosition="startTop"
      onToggle={() => {
        setIsOpen(!isOpen);
      }}
    >
      <AccordionSummary>
        <div>
          <h3 className="evrHeading3">{learningPlan.title}</h3>
          <p className="evrBodyText">
            {learningPlan.courseCount} course{learningPlan.courseCount !== 1 ? 's' : ''}
            {learningPlan.totalTime && ` - ${learningPlan.totalTime}`}
            {learningPlan.percentComplete && ` - ${learningPlan.percentComplete} complete`}
          </p>
        </div>
      </AccordionSummary>
      <AccordionContent>
        <Container className="learning-plan-group-container" id={`learning-plan-group-${learningPlan.id}`}>
          <Table<ICourseTableVM>
            id={`learning-table-${learningPlan.id}`}
            data={learningPlan.courses}
            ariaLabel="Learning Plan Courses Table"
            mobile={isMobile}
            rowsPerPage={10}
            hasTablePagination
            columnDefinitions={columnDefinitions}
          />
        </Container>
      </AccordionContent>
    </AccordionDetail>
  );
};
