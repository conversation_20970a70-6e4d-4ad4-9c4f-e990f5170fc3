import { IEmployeeLearningPlan } from '../models';

export const EmployeeLearningPlansMockData: IEmployeeLearningPlan[] = [
  {
    LMSLearningPlanEnrollmentId: 41337,
    EmployeeId: 1115,
    LearningPlanId: 435,
    LearningPlan: 'Administrative Support Int LP',
    LearningPlanCode: 'ASLP',
    EffectiveStart: '2021-12-16T05:00:00',
    EffectiveEnd: null,
    CompletionDate: null,
    EmployeeRating: null,
    PercentageCompletion: null,
    TotalTime: 0,
    LMSEnrollmentStatusId: 1,
    LMSEnrollmentStatus: 'In progress',
    EmployeeLMSCourseEnrollment: [
      {
        LMSCourseEnrollmentId: 237839,
        EmployeeId: 1115,
        LMSLearningPlanEnrollmentId: null,
        CourseId: 1790,
        Course: 'Administrative Support L1',
        CourseCode: 'ASL1',
        XRefCode: '280_34666',
        CourseEffectiveStart: null,
        LMSEnrollmentStatusId: 3,
        LMSEnrollmentStatus: 'Enrolled',
        LMSEnrollmentStatusXRefCode: 'enrolled',
        EffectiveStart: '2021-10-11T05:28:23',
        EffectiveEnd: null,
        CompletionDate: null,
        TotalTime: 0,
        Score: '0',
        LearningPlanId: null,
        LearningPlan: null,
        LMSLearningPlanEnrollmentXRefCode: null,
        LMSCourseEnrollmentLevelId: 1,
        LMSCourseEnrollmentLevelName: 'Learner',
        CourseAssignmentReason: null,
        LMSAssignmentMethodId: 4,
        IsMandatory: null,
        LastModifiedTimestampCourseEnrollment: null,
        LastModifiedUserId: 1095,
        EmployeeRating: null,
        PercentageCompletion: null,
        CertificationExpiryDate: null,
        CertificationNumber: null,
        Comment: null,
        PassFail: null,
        CourseProvider: null,
        Credits: '0',
        CompletionDateBefore: null,
        PassFailBefore: null,
        PassFailId: null,
        Cost: null,
        CostCurrencyCode: null,
        IsLMS: true,
        LMSVendorXrefCode: 'DOCEBO',
        ClientEntityId: null,
        EntityState: 0,
        LastModifiedTimestamp: '2021-10-11T01:58:49.003',
        LocalizedName: '',
        LocalizedDescription: '',
        PrimaryKeyId: null,
        OriginalValues: null,
        ExtendedProperties: [],
      },
      {
        LMSCourseEnrollmentId: 244449,
        EmployeeId: 1115,
        LMSLearningPlanEnrollmentId: null,
        CourseId: 1791,
        Course: 'Administrative Support L2',
        CourseCode: 'ASL2',
        XRefCode: '281_34666',
        CourseEffectiveStart: null,
        LMSEnrollmentStatusId: 3,
        LMSEnrollmentStatus: 'Enrolled',
        LMSEnrollmentStatusXRefCode: 'enrolled',
        EffectiveStart: '2021-10-11T05:28:23',
        EffectiveEnd: null,
        CompletionDate: null,
        TotalTime: 12,
        Score: '74',
        LearningPlanId: null,
        LearningPlan: null,
        LMSLearningPlanEnrollmentXRefCode: null,
        LMSCourseEnrollmentLevelId: 1,
        LMSCourseEnrollmentLevelName: 'Learner',
        CourseAssignmentReason: null,
        LMSAssignmentMethodId: 4,
        IsMandatory: null,
        LastModifiedTimestampCourseEnrollment: null,
        LastModifiedUserId: 1095,
        EmployeeRating: null,
        PercentageCompletion: null,
        CertificationExpiryDate: null,
        CertificationNumber: null,
        Comment: null,
        PassFail: null,
        CourseProvider: null,
        Credits: '4',
        CompletionDateBefore: null,
        PassFailBefore: null,
        PassFailId: null,
        Cost: null,
        CostCurrencyCode: null,
        IsLMS: true,
        LMSVendorXrefCode: 'DOCEBO',
        ClientEntityId: null,
        EntityState: 0,
        LastModifiedTimestamp: '2021-10-11T01:58:49.003',
        LocalizedName: '',
        LocalizedDescription: '',
        PrimaryKeyId: null,
        OriginalValues: null,
        ExtendedProperties: [],
      },
    ],
    NumberOfCourses: 2,
    CompletedCourse: 0,
    XRefCode: '35_34666',
    IsLMS: true,
    LMSVendorXrefCode: 'DOCEBO',
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: '2021-12-08T05:31:54.917',
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    LMSLearningPlanEnrollmentId: 69797,
    EmployeeId: 1115,
    LearningPlanId: 562,
    LearningPlan: '867qahr300040 - Cyber Security LP',
    LearningPlanCode: '',
    EffectiveStart: '2024-07-31T10:02:21',
    EffectiveEnd: null,
    CompletionDate: null,
    EmployeeRating: null,
    PercentageCompletion: null,
    TotalTime: 32,
    LMSEnrollmentStatusId: 3,
    LMSEnrollmentStatus: 'Enrolled',
    EmployeeLMSCourseEnrollment: [
      {
        LMSCourseEnrollmentId: 518719,
        EmployeeId: 1115,
        LMSLearningPlanEnrollmentId: null,
        CourseId: 12648,
        Course: '867qahr300040 - Cyber Security 1 - Post Fix',
        CourseCode: null,
        XRefCode: '8483_34666',
        CourseEffectiveStart: null,
        LMSEnrollmentStatusId: 3,
        LMSEnrollmentStatus: 'Enrolled',
        LMSEnrollmentStatusXRefCode: 'enrolled',
        EffectiveStart: '2024-07-31T10:02:21',
        EffectiveEnd: null,
        CompletionDate: null,
        TotalTime: 12,
        Score: '85',
        LearningPlanId: null,
        LearningPlan: null,
        LMSLearningPlanEnrollmentXRefCode: null,
        LMSCourseEnrollmentLevelId: 1,
        LMSCourseEnrollmentLevelName: 'Learner',
        CourseAssignmentReason: null,
        LMSAssignmentMethodId: 4,
        IsMandatory: null,
        LastModifiedTimestampCourseEnrollment: null,
        LastModifiedUserId: 1095,
        EmployeeRating: null,
        PercentageCompletion: null,
        CertificationExpiryDate: null,
        CertificationNumber: null,
        Comment: null,
        PassFail: null,
        CourseProvider: null,
        Credits: '2',
        CompletionDateBefore: null,
        PassFailBefore: null,
        PassFailId: null,
        Cost: null,
        CostCurrencyCode: null,
        IsLMS: true,
        LMSVendorXrefCode: 'DOCEBO',
        ClientEntityId: null,
        EntityState: 0,
        LastModifiedTimestamp: '2024-08-01T02:52:44.267',
        LocalizedName: '',
        LocalizedDescription: '',
        PrimaryKeyId: null,
        OriginalValues: null,
        ExtendedProperties: [],
      },
      {
        LMSCourseEnrollmentId: 518721,
        EmployeeId: 1115,
        LMSLearningPlanEnrollmentId: null,
        CourseId: 12649,
        Course: 'Cyber Security 2 - Post Fix',
        CourseCode: null,
        XRefCode: '8484_34666',
        CourseEffectiveStart: null,
        LMSEnrollmentStatusId: 3,
        LMSEnrollmentStatus: 'Enrolled',
        LMSEnrollmentStatusXRefCode: 'enrolled',
        EffectiveStart: '2024-07-31T10:02:21',
        EffectiveEnd: null,
        CompletionDate: null,
        TotalTime: 10,
        Score: '90',
        LearningPlanId: null,
        LearningPlan: null,
        LMSLearningPlanEnrollmentXRefCode: null,
        LMSCourseEnrollmentLevelId: 1,
        LMSCourseEnrollmentLevelName: 'Learner',
        CourseAssignmentReason: null,
        LMSAssignmentMethodId: 4,
        IsMandatory: null,
        LastModifiedTimestampCourseEnrollment: null,
        LastModifiedUserId: 1095,
        EmployeeRating: null,
        PercentageCompletion: null,
        CertificationExpiryDate: null,
        CertificationNumber: null,
        Comment: null,
        PassFail: null,
        CourseProvider: null,
        Credits: '4',
        CompletionDateBefore: null,
        PassFailBefore: null,
        PassFailId: null,
        Cost: null,
        CostCurrencyCode: null,
        IsLMS: true,
        LMSVendorXrefCode: 'DOCEBO',
        ClientEntityId: null,
        EntityState: 0,
        LastModifiedTimestamp: '2024-08-01T02:52:44.267',
        LocalizedName: '',
        LocalizedDescription: '',
        PrimaryKeyId: null,
        OriginalValues: null,
        ExtendedProperties: [],
      },
    ],
    NumberOfCourses: 2,
    CompletedCourse: 0,
    XRefCode: '126_34666',
    IsLMS: true,
    LMSVendorXrefCode: 'DOCEBO',
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: '2024-08-01T02:52:38.867',
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
];
