import { formatCredits, formatDateString, formatPercentComplete, formatScore, formatTotalTime } from './common-utils';

describe('Common Utils', () => {
  describe('formatCredits', () => {
    it('should format credits with suffix', () => {
      expect(formatCredits('5')).toBe('5 credits');
    });

    it('should handle null credits', () => {
      expect(formatCredits(null)).toBe('0 credits');
    });
  });

  describe('formatScore', () => {
    it('should format score with percentage', () => {
      expect(formatScore('85')).toBe('85%');
    });

    it('should return null for zero score', () => {
      expect(formatScore('0')).toBeNull();
    });

    it('should return null for null score', () => {
      expect(formatScore(null)).toBeNull();
    });
  });

  describe('formatDateString', () => {
    it('should format date string to localized date', () => {
      const dateString = '2023-07-15T10:00:00';
      const expected = new Date(dateString).toLocaleDateString();
      expect(formatDateString(dateString)).toBe(expected);
    });

    it('should return null for null date', () => {
      expect(formatDateString(null)).toBeNull();
    });
  });

  describe('formatTotalTime', () => {
    it('should format minutes less than 60', () => {
      expect(formatTotalTime(30)).toBe('30 mins');
    });

    it('should format exactly one hour', () => {
      expect(formatTotalTime(60)).toBe('1 hr');
    });

    it('should format multiple hours with no minutes', () => {
      expect(formatTotalTime(120)).toBe('2 hrs');
    });

    it('should format hours and minutes', () => {
      expect(formatTotalTime(65)).toBe('1 hr 5 mins');
    });

    it('should format multiple hours and multiple minutes', () => {
      expect(formatTotalTime(125)).toBe('2 hrs 5 mins');
    });

    it('should handle zero minutes', () => {
      expect(formatTotalTime(0)).toBe('0 mins');
    });
  });

  describe('formatPercentComplete', () => {
    it('should format percentage with % symbol', () => {
      expect(formatPercentComplete(75)).toBe('75%');
    });

    it('should return null for null percentage', () => {
      expect(formatPercentComplete(null)).toBeNull();
    });
  });
});
