/**
 * Common utility functions shared between course and learning plan utilities
 */

/**
 * Format credits for display
 * @param credits - Credits value as string
 * @returns Formatted credits string
 */
export const formatCredits = (credits: string | null): string => {
  const creditsValue = credits || '0';
  return `${creditsValue} credit${creditsValue === '1' ? '' : 's'}`;
};

/**
 * Format score percentage for display
 * @param score - Score value as string
 * @returns Formatted score percentage or null if no valid score
 */
export const formatScore = (score: string | null): string | null => {
  if (!score || score === '0') return null;
  return `${score}%`;
};

/**
 * Format date string to localized date
 * @param dateString - ISO date string
 * @returns Formatted localized date string or null
 */
export const formatDateString = (dateString: string | null): string | null => {
  if (!dateString) return null;
  return new Date(dateString).toLocaleDateString();
};

/**
 * Format total time in minutes to human-readable format
 * @param minutes - Total minutes
 * @returns Formatted time string (e.g., "1 hr 5 mins", "30 mins")
 */
export const formatTotalTime = (minutes: number): string => {
  if (minutes < 1) return '0 mins';
  if (minutes < 60) return `${minutes} min${minutes === 1 ? '' : 's'}`;

  const hours = Math.floor(minutes / 60);
  const remainingMins = minutes % 60;

  if (remainingMins === 0) return `${hours} hr${hours > 1 ? 's' : ''}`;
  return `${hours} hr${hours > 1 ? 's' : ''} ${remainingMins} min${remainingMins > 1 ? 's' : ''}`;
};

/**
 * Format percentage value for display
 * @param percent - Percentage value
 * @returns Formatted percentage string or null
 */
export const formatPercentComplete = (percent: number | null): string | null => {
  if (percent === null) return null;
  return `${percent}%`;
};
