import { mapCoursesToTableVM } from './course-utils';
import { IEmployeeCourse } from '../models';
import * as CommonUtils from './common-utils';

// Create spies for the common utils to isolate course utils testing
jest.mock('./common-utils', () => ({
  formatCredits: jest.fn((credits) => `${credits || '0'} credits`),
  formatScore: jest.fn((score) => (score && score !== '0' ? `${score}%` : null)),
  formatDateString: jest.fn((dateString) => (dateString ? 'formatted-date' : null)),
}));

describe('Course Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('mapCoursesToTableVM', () => {
    it('should correctly map course data to table view model', () => {
      // Arrange
      const mockCourses: IEmployeeCourse[] = [
        {
          LMSCourseEnrollmentId: 371222,
          EmployeeId: 1115,
          LMSLearningPlanEnrollmentId: null,
          CourseId: 12323,
          Course: 'DOJO Session Course ILT',
          CourseCode: null,
          XRefCode: '8230_34666',
          CourseEffectiveStart: null,
          LMSEnrollmentStatusId: 3,
          LMSEnrollmentStatus: 'Enrolled',
          LMSEnrollmentStatusXRefCode: 'enrolled',
          EffectiveStart: '2023-04-27T05:00:00',
          EffectiveEnd: null,
          CompletionDate: '2023-05-15T14:30:00',
          TotalTime: 0,
          Score: '85',
          LearningPlanId: null,
          LearningPlan: 'Development Plan',
          LMSLearningPlanEnrollmentXRefCode: null,
          LMSCourseEnrollmentLevelId: 3,
          LMSCourseEnrollmentLevelName: 'Instructor',
          CourseAssignmentReason: null,
          LMSAssignmentMethodId: 4,
          IsMandatory: null,
          LastModifiedTimestampCourseEnrollment: null,
          LastModifiedUserId: 1095,
          EmployeeRating: null,
          PercentageCompletion: null,
          CertificationExpiryDate: null,
          CertificationNumber: null,
          Comment: null,
          PassFail: null,
          CourseProvider: null,
          Credits: '3',
          CompletionDateBefore: null,
          PassFailBefore: null,
          PassFailId: null,
          Cost: null,
          CostCurrencyCode: null,
          IsLMS: true,
          LMSVendorXrefCode: 'DOCEBO',
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: '2023-04-24T02:50:53.363',
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
        {
          LMSCourseEnrollmentId: 377847,
          EmployeeId: 1115,
          LMSLearningPlanEnrollmentId: null,
          CourseId: 12324,
          Course: 'Leadership Training',
          CourseCode: 'LT-101',
          XRefCode: '8230_34667',
          CourseEffectiveStart: null,
          LMSEnrollmentStatusId: 4,
          LMSEnrollmentStatus: 'Completed',
          LMSEnrollmentStatusXRefCode: 'completed',
          EffectiveStart: '2023-03-15T05:00:00',
          EffectiveEnd: '2023-06-15T05:00:00',
          CompletionDate: null,
          TotalTime: 120,
          Score: '95',
          LearningPlanId: null,
          LearningPlan: null,
          LMSLearningPlanEnrollmentXRefCode: null,
          LMSCourseEnrollmentLevelId: 1,
          LMSCourseEnrollmentLevelName: 'Learner',
          CourseAssignmentReason: 'Required for promotion',
          LMSAssignmentMethodId: 1,
          IsMandatory: true,
          LastModifiedTimestampCourseEnrollment: null,
          LastModifiedUserId: 1095,
          EmployeeRating: 4,
          PercentageCompletion: 100,
          CertificationExpiryDate: null,
          CertificationNumber: null,
          Comment: null,
          PassFail: 'Pass',
          CourseProvider: 'Internal',
          Credits: '5',
          CompletionDateBefore: null,
          PassFailBefore: null,
          PassFailId: null,
          Cost: null,
          CostCurrencyCode: null,
          IsLMS: true,
          LMSVendorXrefCode: 'DOCEBO',
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: '2023-04-24T02:50:53.363',
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
      ];

      // Act
      const result = mapCoursesToTableVM(mockCourses);

      // Assert
      expect(result).toHaveLength(2);

      // Check first course mapping
      expect(result[0]).toEqual({
        id: 371222,
        course: 'DOJO Session Course ILT',
        learningPlan: 'Development Plan',
        status: 'Enrolled',
        startDate: 'formatted-date',
        completionDate: 'formatted-date',
        credits: '3 credits',
        score: '85%',
      });

      // Check second course mapping
      expect(result[1]).toEqual({
        id: 377847,
        course: 'Leadership Training',
        learningPlan: 'N/A',
        status: 'Completed',
        startDate: 'formatted-date',
        completionDate: null,
        credits: '5 credits',
        score: '95%',
      });

      // Verify the utility functions were called with the correct parameters
      expect(CommonUtils.formatDateString).toHaveBeenCalledWith('2023-04-27T05:00:00');
      expect(CommonUtils.formatDateString).toHaveBeenCalledWith('2023-05-15T14:30:00');
      expect(CommonUtils.formatCredits).toHaveBeenCalledWith('3');
      expect(CommonUtils.formatCredits).toHaveBeenCalledWith('5');
      expect(CommonUtils.formatScore).toHaveBeenCalledWith('85');
      expect(CommonUtils.formatScore).toHaveBeenCalledWith('95');
    });

    it('should handle empty courses array', () => {
      // Arrange
      const emptyCourses: IEmployeeCourse[] = [];

      // Act
      const result = mapCoursesToTableVM(emptyCourses);

      // Assert
      expect(result).toEqual([]);
    });
  });
});
