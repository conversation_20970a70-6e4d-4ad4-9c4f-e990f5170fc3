import { IEmployeeCourse } from '../models';
import { formatCredits, formatDateString, formatScore } from './common-utils';

/**
 * View model for course table display
 */
export interface ICourseTableVM {
  id: number;
  course: string;
  learningPlan: string;
  status: string;
  startDate: string | null;
  completionDate: string | null;
  credits: string | null;
  score: string | null;
}

/**
 * Maps API course data to view model for table display
 * @param courses - Array of employee course data from API
 * @returns Formatted course view models for table display
 */
export const mapCoursesToTableVM = (courses: IEmployeeCourse[]): ICourseTableVM[] => {
  return courses.map((course) => ({
    id: course.LMSCourseEnrollmentId,
    course: course.Course,
    learningPlan: course.LearningPlan || 'N/A',
    status: course.LMSEnrollmentStatus,
    startDate: formatDateString(course.EffectiveStart),
    completionDate: formatDateString(course.CompletionDate),
    credits: formatCredits(course.Credits),
    score: formatScore(course.Score),
  }));
};
