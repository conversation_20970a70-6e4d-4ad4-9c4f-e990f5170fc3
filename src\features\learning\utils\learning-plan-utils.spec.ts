import { mapLearningPlansToGroupVM } from './learning-plan-utils';
import { EmployeeLearningPlansMockData } from '../mocks/employeeLearningPlansMockData';
import * as CommonUtils from './common-utils';

// Create spies for the common utils to isolate learning plan utils testing
jest.mock('./common-utils', () => ({
  formatCredits: jest.fn((credits) => `${credits || '0'} credits`),
  formatScore: jest.fn((score) => (score && score !== '0' ? `${score}%` : null)),
  formatDateString: jest.fn((dateString) => (dateString ? 'formatted-date' : null)),
  formatPercentComplete: jest.fn((percent) => (percent !== null ? `${percent}%` : null)),
  formatTotalTime: jest.fn((minutes) => {
    if (minutes === 12) return '12 mins';
    if (minutes === 65) return '1 hr 5 mins';
    return '0 mins';
  }),
}));

describe('Learning Plan Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('mapLearningPlansToGroupVM', () => {
    it('should correctly map learning plans to group view models', () => {
      // Arrange & Act
      const result = mapLearningPlansToGroupVM(EmployeeLearningPlansMockData);

      // Assert
      // Check we have the correct number of groups
      expect(result.length).toBe(2);

      // Check first learning plan
      const firstPlan = result[0];
      expect(firstPlan.id).toBe(41337);
      expect(firstPlan.title).toBe('Administrative Support Int LP');
      expect(firstPlan.code).toBe('ASLP');
      expect(firstPlan.status).toBe('In progress');
      expect(firstPlan.courseCount).toBe(2);
      expect(firstPlan.totalTime).toBe('12 mins');

      // Check courses in first plan
      expect(firstPlan.courses.length).toBe(2);
      expect(firstPlan.courses[0].course).toBe('Administrative Support L1');
      expect(firstPlan.courses[1].course).toBe('Administrative Support L2');

      // Check second learning plan
      const secondPlan = result[1];
      expect(secondPlan.id).toBe(69797);
      expect(secondPlan.title).toBe('867qahr300040 - Cyber Security LP');
      expect(secondPlan.status).toBe('Enrolled');

      // Verify the formatTotalTime was called with the correct total minutes
      expect(CommonUtils.formatTotalTime).toHaveBeenCalledWith(12);
    });

    it('should format totalTime correctly', () => {
      // Create a test plan with 65 minutes
      const testPlan = JSON.parse(JSON.stringify(EmployeeLearningPlansMockData[0]));
      testPlan.EmployeeLMSCourseEnrollment[0].TotalTime = 5;
      testPlan.EmployeeLMSCourseEnrollment[1].TotalTime = 60;

      // Act
      const result = mapLearningPlansToGroupVM([testPlan]);

      // Assert
      expect(result[0].totalTime).toBe('1 hr 5 mins');
      expect(CommonUtils.formatTotalTime).toHaveBeenCalledWith(65);
    });

    it('should handle empty learning plans array', () => {
      const result = mapLearningPlansToGroupVM([]);
      expect(result).toEqual([]);
    });
  });
});
