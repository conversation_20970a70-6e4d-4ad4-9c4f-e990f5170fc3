import { IEmployeeLearningPlan } from '../models';
import { formatCredits, formatDateString, formatPercentComplete, formatScore, formatTotalTime } from './common-utils';
import { ICourseTableVM } from './course-utils';

/**
 * View model for learning plan group display
 */
export interface ILearningPlanGroupVM {
  id: number;
  title: string;
  code: string;
  status: string;
  startDate: string | null;
  completionDate: string | null;
  percentComplete: string | null;
  totalTime: string;
  courseCount: number;
  courses: ICourseTableVM[];
}

/**
 * Maps API learning plan data to view model for group display
 * @param learningPlans - Array of employee learning plan data from API
 * @returns Formatted learning plan view models for group display
 */
export const mapLearningPlansToGroupVM = (learningPlans: IEmployeeLearningPlan[]): ILearningPlanGroupVM[] => {
  return learningPlans.map((plan) => {
    // Calculate total time from all courses
    const totalMinutes = plan.EmployeeLMSCourseEnrollment.reduce((total, course) => total + (course.TotalTime || 0), 0);

    // Map courses to table view models
    const coursesVM = plan.EmployeeLMSCourseEnrollment.map((course) => ({
      id: course.LMSCourseEnrollmentId,
      course: course.Course,
      learningPlan: plan.LearningPlan,
      status: course.LMSEnrollmentStatus,
      startDate: formatDateString(course.EffectiveStart),
      completionDate: formatDateString(course.CompletionDate),
      credits: formatCredits(course.Credits),
      score: formatScore(course.Score),
    }));

    return {
      id: plan.LMSLearningPlanEnrollmentId,
      title: plan.LearningPlan,
      code: plan.LearningPlanCode || '',
      status: plan.LMSEnrollmentStatus,
      startDate: formatDateString(plan.EffectiveStart),
      completionDate: formatDateString(plan.CompletionDate),
      percentComplete: formatPercentComplete(plan.PercentageCompletion),
      totalTime: formatTotalTime(totalMinutes),
      courseCount: plan.NumberOfCourses || plan.EmployeeLMSCourseEnrollment.length,
      courses: coursesVM,
    };
  });
};
