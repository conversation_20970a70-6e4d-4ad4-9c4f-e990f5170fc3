import React from 'react';
import { ViewSection } from '@components/ViewSection';
import { EmployeeCoursesMockData } from '../mocks';
import { CoursesTable } from '../components';

import { ICourseTableVM, mapCoursesToTableVM } from '../utils';

const mockData: ICourseTableVM[] = mapCoursesToTableVM(EmployeeCoursesMockData);

export interface CoursesViewProps {
  dataTestId?: string;
}

export const CoursesView = ({ dataTestId = 'courses-view-test-id' }: CoursesViewProps) => {
  return (
    <ViewSection testId={dataTestId} title="Courses">
      <CoursesTable data={mockData} />
    </ViewSection>
  );
};

export default CoursesView;
