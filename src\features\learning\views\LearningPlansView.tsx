import React, { useMemo } from 'react';
import { ViewSection } from '@components/ViewSection';

import { EmployeeLearningPlansMockData } from '../mocks/employeeLearningPlansMockData';
import { mapLearningPlansToGroupVM } from '../utils';
import { LearningPlanGroup } from '../components';

export interface LearningPlansViewProps {
  dataTestId?: string;
}

export const LearningPlansView = ({ dataTestId = 'learning-plans-view-test-id' }: LearningPlansViewProps) => {
  const learningPlanGroups = useMemo(() => mapLearningPlansToGroupVM(EmployeeLearningPlansMockData), []);

  return (
    <ViewSection testId={dataTestId} title="Learning plans">
      {learningPlanGroups.length === 0 ? (
        <div className="empty-state">
          <p>You have no assigned learning plans.</p>
        </div>
      ) : (
        learningPlanGroups.map((planGroup) => <LearningPlanGroup key={planGroup.id} learningPlan={planGroup} />)
      )}
    </ViewSection>
  );
};

export default LearningPlansView;
