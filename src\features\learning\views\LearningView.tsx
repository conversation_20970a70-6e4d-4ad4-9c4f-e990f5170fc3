import React from 'react';
import { Container } from '@components';
import { Tab, TabGroup } from '@components/Tab';
import { useTab } from '@components/Tab/hooks';
import { CoursesView, LearningPlansView } from '.';

import './learning-view.scss';

export enum TabType {
  CoursesTabName = 'courses-tab',
  LearningPlansTabName = 'learning-plans-tab',
}

export interface LearningViewProps {
  id?: string;
  dataTestId?: string;
}

export const LearningView = ({ id = 'learning-view', dataTestId = 'learning-view-test-id' }: LearningViewProps) => {
  const { currentTab, handleTabChange } = useTab<TabType>(TabType.CoursesTabName);

  const onTabChange = (newTabId: string) => {
    if (newTabId) {
      handleTabChange(newTabId);
    }
  };

  return (
    <Container
      id={`${id}-container`}
      ariaLabel="Learning Tab Group"
      dataTestId={`${dataTestId}-container`}
      className="learning-tab-group-container"
    >
      <TabGroup
        id={`${id}-tab-group`}
        activeId={currentTab}
        onActiveIdChange={onTabChange}
        testId={`${dataTestId}-tab-group`}
        textMap={{
          overflowButtonNextAriaLabel: 'Scroll to next',
          overflowButtonPrevAriaLabel: 'Scroll to previous',
          viewAllAriaLabel: 'View All Tabs',
        }}
      >
        <Tab id={TabType.CoursesTabName} label="Courses" testId={`${dataTestId}-courses-tab`}>
          {currentTab === TabType.CoursesTabName && <CoursesView />}
        </Tab>

        <Tab id={TabType.LearningPlansTabName} label="Learning plans" testId={`${dataTestId}-learning-plans-tab`}>
          {currentTab === TabType.LearningPlansTabName && <LearningPlansView />}
        </Tab>
      </TabGroup>
    </Container>
  );
};

export default LearningView;
