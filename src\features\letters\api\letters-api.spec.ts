import { EmployeeLetterMockData } from '@mocks/employee';
import { handleApiRequest } from '../../../api/baseApi';
import { API_ENDPOINTS } from '../../../api/apiConfig';

import { getEmployeeLetters } from './lettersApi';
import type { TEmployeeIdParam } from '@/typings';

// Mock the handleApiRequest function
jest.mock('../../../api/baseApi', () => ({
  handleApiRequest: jest.fn(),
}));

// Mock API_ENDPOINTS
jest.mock('../../../api/apiConfig', () => ({
  API_ENDPOINTS: {
    EMPLOYEE_LETTERS: '/api/employee/letters',
  },
}));

const mockHandleApiRequest = handleApiRequest as jest.MockedFunction<typeof handleApiRequest>;
const mockEmployeeIdPayload: TEmployeeIdParam = { employeeId: 12345 };

describe('getEmployeeLetters', () => {
  it('should call handleApiRequest with correct parameters', async () => {
    const mockResponse = EmployeeLetterMockData;
    mockHandleApiRequest.mockResolvedValue(mockResponse);

    const result = await getEmployeeLetters(mockEmployeeIdPayload);

    expect(mockHandleApiRequest).toHaveBeenCalledWith(
      'POST',
      API_ENDPOINTS.EMPLOYEE_LETTERS,
      EmployeeLetterMockData,
      'Failed to fetch employee letters.',
      mockEmployeeIdPayload,
    );
    expect(result).toEqual(mockResponse);
  });

  it('should handle API request failure', async () => {
    const mockError = new Error('Server error');
    mockHandleApiRequest.mockRejectedValue(mockError);

    await expect(getEmployeeLetters(mockEmployeeIdPayload)).rejects.toThrow('Server error');
  });

  it('should handle unexpected response format', async () => {
    const mockResponse = null; // Simulate unexpected response
    mockHandleApiRequest.mockResolvedValue(mockResponse);

    const result = await getEmployeeLetters(mockEmployeeIdPayload);

    expect(result).toBeNull();
  });
});
