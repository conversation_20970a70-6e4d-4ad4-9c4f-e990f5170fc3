import { EmployeeLetterMockData } from '@mocks/employee';
import type { TEmployeeIdParam } from '@/typings';
import type { IEmployeeLetter } from '@models/employee';

import { handleApiRequest } from '../../../api/baseApi';
import { API_ENDPOINTS } from '../../../api/apiConfig';

export const getEmployeeLetters = async (payload: TEmployeeIdParam): Promise<IEmployeeLetter[]> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.EMPLOYEE_LETTERS,
    EmployeeLetterMockData,
    'Failed to fetch employee letters.',
    payload,
  );
};
