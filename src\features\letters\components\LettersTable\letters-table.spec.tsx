import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LettersTable } from './LettersTable';
import type { ILetterTableVM } from '../../utils/letters-utils';

// Mock child components
jest.mock('./components/TableCells', () => ({
  SubjectCell: (context: any, callback: any) => (
    <a data-testid={`letter-row-${context.rowIndex}-test-id`} onClick={() => callback(context.rowIndex)}>
      {context.getValue()}
    </a>
  ),
  StatusCell: (context: any) => <span data-testid="status-cell">{context.getValue()}</span>,
}));

// Mock the Table component
jest.mock('@components/Table', () => ({
  Table: ({ columnDefinitions, data, ...props }: any) => (
    <div data-testid={props.dataTestId}>
      <table>
        <thead>
          <tr>
            {columnDefinitions.map((col: any) => (
              <th key={col.field}>{col.label}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row: any, rowIndex: number) => (
            <tr key={rowIndex}>
              {columnDefinitions.map((col: any) => (
                <td key={`${rowIndex}-${col.field}`}>
                  {col.cellTemplate({
                    getValue: () => row[col.field],
                    rowIndex,
                    rowData: row,
                  })}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
}));

describe('LettersTable', () => {
  const mockTableData: ILetterTableVM[] = [
    {
      id: 1,
      subject: 'Test Letter 1',
      sentTimestamp: '2025-01-01',
      status: 'Accepted',
      recipientEmail: '<EMAIL>',
    },
    {
      id: 2,
      subject: 'Test Letter 2',
      sentTimestamp: '2025-01-02',
      status: 'Rejected',
      recipientEmail: '<EMAIL>',
    },
  ];

  const defaultProps = {
    id: 'letters-table',
    dataTestId: 'letters-table-test-id',
    data: mockTableData,
    onSelectedLetterChange: jest.fn(),
    rowsPerPage: 5,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with default props', () => {
      render(<LettersTable {...defaultProps} />);
      expect(screen.getByTestId('letters-table-test-id')).toBeInTheDocument();
    });

    it('renders all column headers', () => {
      render(<LettersTable {...defaultProps} />);

      const expectedHeaders = ['Letter', 'Date Received', 'Status'];
      expectedHeaders.forEach((header) => {
        expect(screen.getByText(header)).toBeInTheDocument();
      });
    });

    it('renders correct number of rows', () => {
      render(<LettersTable {...defaultProps} />);
      const rows = screen.getAllByRole('row');
      // +1 for header row
      expect(rows).toHaveLength(mockTableData.length + 1);
    });
  });

  describe('Column Templates', () => {
    it('renders subject cell with correct link', async () => {
      render(<LettersTable {...defaultProps} />);
      const user = userEvent.setup();

      const subjectLink = screen.getByTestId('letter-row-0-test-id');
      await user.click(subjectLink);

      expect(defaultProps.onSelectedLetterChange).toHaveBeenCalledWith(0);
      expect(subjectLink).toHaveTextContent(mockTableData[0].subject);
    });

    it('renders date cell with correct format', () => {
      render(<LettersTable {...defaultProps} />);
      mockTableData.forEach((row) => {
        expect(screen.getByText(row.sentTimestamp)).toBeInTheDocument();
      });
    });

    it('renders status cell with correct content', () => {
      render(<LettersTable {...defaultProps} />);
      const statusCells = screen.getAllByTestId('status-cell');
      expect(statusCells).toHaveLength(mockTableData.length);
      expect(statusCells[0]).toHaveTextContent(mockTableData[0].status);
    });
  });

  describe('Props Handling', () => {
    it('uses default values when optional props are not provided', () => {
      const { id, dataTestId, rowsPerPage, ...requiredProps } = defaultProps;
      render(<LettersTable {...requiredProps} />);

      expect(screen.getByTestId('letters-table-test-id')).toBeInTheDocument();
    });

    it('applies custom id and dataTestId', () => {
      const customProps = {
        ...defaultProps,
        id: 'custom-table',
        dataTestId: 'custom-test-id',
      };

      render(<LettersTable {...customProps} />);
      expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles empty data array', () => {
      render(<LettersTable {...defaultProps} data={[]} />);
      expect(screen.getByTestId('letters-table-test-id')).toBeInTheDocument();
      expect(screen.queryAllByRole('row')).toHaveLength(1); // Just header row
    });

    it('handles undefined optional props', () => {
      const { id, dataTestId, rowsPerPage, ...requiredProps } = defaultProps;
      expect(() => render(<LettersTable {...requiredProps} />)).not.toThrow();
    });
  });
});
