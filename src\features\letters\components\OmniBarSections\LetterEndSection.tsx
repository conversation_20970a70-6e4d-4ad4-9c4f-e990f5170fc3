import React, { useState } from 'react';
import { PopoverMenu, PopoverMenuItem } from '@ceridianhcm/components';
import { usePageShell } from '@components/PageShell';
import { IconButton } from '@components/IconButton';

export const POPOVER_LABELS: Record<string, string> = {
  buttonLabel: 'Download',
  buttonAriaLabel: 'Menu',
  letterOnly: 'Letter only',
  letterWithBanner: 'Letter with banner',
  sectionAriaLabel: 'Letter End Section',
  refreshAriaLabel: 'Refresh',
  printAriaLabel: 'Print',
};

export const LetterEndSection = () => {
  const initialState = '';
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(initialState);
  const { isMobile } = usePageShell();

  const handleDownloadMenuChange = ({ id }: { id: string }) => {
    setSelectedMenuItemId(id);
    console.log(`Selected menu item ID: ${selectedMenuItemId}`);
  };

  const handleRefreshClick = () => {
    console.log('Refresh button clicked');
  };

  const handlePrintClick = () => {
    console.log('Print button clicked');
  };

  const styles = {
    btnGroup: {
      display: 'flex',
      flexWrap: 'wrap' as const,
      justifyContent: 'space-around',
      alignItems: 'center',
      gap: 'var(--evr-spacing-2xs)',
    },
  };

  return (
    <div id="btn-layout-id" style={styles.btnGroup} aria-label={POPOVER_LABELS.sectionAriaLabel} role="group">
      {!isMobile && (
        <>
          <IconButton
            id="refresh-button"
            testId="refresh-button-test"
            ariaLabel={POPOVER_LABELS.refreshAriaLabel}
            iconName="refresh"
            variant="tertiaryNeutral"
            size="large"
            onClick={handleRefreshClick}
          />
          <IconButton
            id="print-button"
            testId="print-button-test"
            ariaLabel={POPOVER_LABELS.printAriaLabel}
            iconName="print"
            variant="tertiaryNeutral"
            size="large"
            onClick={handlePrintClick}
          />
        </>
      )}

      <PopoverMenu
        id="download-menu"
        buttonAriaLabel={POPOVER_LABELS.buttonAriaLabel}
        triggerOption={isMobile ? 'iconButton' : 'button'}
        triggerProps={{
          variant: isMobile ? 'secondaryNeutral' : 'primary',
          endIcon: 'chevronDownSmall',
          size: 'large',
        }}
        buttonLabel={POPOVER_LABELS.buttonLabel}
        onChange={handleDownloadMenuChange}
      >
        <PopoverMenuItem id="download-item-1" role="menuitemcheckbox">
          {POPOVER_LABELS.letterOnly}
        </PopoverMenuItem>
        <PopoverMenuItem id="download-item-2" role="menuitemcheckbox">
          {POPOVER_LABELS.letterWithBanner}
        </PopoverMenuItem>
      </PopoverMenu>
    </div>
  );
};
