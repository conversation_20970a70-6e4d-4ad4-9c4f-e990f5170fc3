import React from 'react';
import { MediaObject, TIconName, IconButton, Tag, TTagStatus } from '@ceridianhcm/components';
import { useNavigation } from '@context/NavigationContext';
import { getTagStatus, getBannerTagStatusLabel, getBannerTagIcon } from '../../utils';

interface ILetterStartSectionProps {
  iconName: TIconName;
}

/**
 * Expected navigation data structure from context
 */
interface LetterNavigationData {
  /** Letter subject/title */
  subject: string;
  /** Timestamp when letter was viewed/received */
  timestamp: string;
  /** Letter status (e.g., 'Accepted', 'Rejected', etc.) */
  status: string;
  /** Status color for visual indication */
  statusColor: string;
  /** Status icon name */
  statusIcon: string;
}

export const LetterStartSection: React.FC<ILetterStartSectionProps> = ({ iconName = 'arrowLeft' }) => {
  const { navigateTo, getNavigationData } = useNavigation();
  const pageData = getNavigationData() as LetterNavigationData | undefined;

  // Use default values to prevent undefined properties
  const subject = pageData?.subject || 'Letter Details';
  const timestamp = pageData?.timestamp || '';
  const status = pageData?.status || 'No response needed';

  // Only call these functions if we have valid data
  const tagStatusLabel = status ? getBannerTagStatusLabel(status, timestamp) : '';
  const tagStatus = status ? getTagStatus(status) : 'info';
  const tagStatusIcon = status ? getBannerTagIcon(status) : 'information';

  const handleNavClick = () => {
    try {
      navigateTo('letters');
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback navigation if needed
    }
  };

  return (
    <MediaObject
      media={
        <IconButton
          key="omni-single-letter-start-button"
          id="omni-single-letter-start-button"
          testId="omni-single-letter-start-button-test"
          ariaLabel="Back to letters"
          iconName={iconName}
          variant="tertiaryNeutral"
          size="large"
          onClick={handleNavClick}
        />
      }
      id={`omni-start-section`}
      title={
        <h3 className="evrHeading3">
          {subject}
          {tagStatusLabel && (
            <Tag
              label={tagStatusLabel}
              status={tagStatus as TTagStatus}
              startIcon={tagStatusIcon as TIconName}
              testId="omni-tag-status-test-id"
            />
          )}
        </h3>
      }
      subtitle="Letter"
      gap="--evr-spacing-sm"
      mediaAlignment="center"
      className="omni-media-object"
    />
  );
};

LetterStartSection.displayName = 'LetterStartSection';
