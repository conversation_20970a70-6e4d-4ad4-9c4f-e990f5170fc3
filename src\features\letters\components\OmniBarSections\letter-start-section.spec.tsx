import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { LetterStartSection } from './LetterStartSection';

// Mock the NavigationContext
jest.mock('@context/NavigationContext', () => ({
  useNavigation: () => ({
    navigateTo: jest.fn(),
    getNavigationData: () => ({
      subject: 'Test Letter Subject',
      timestamp: '2025-01-01',
      status: 'Accepted',
      statusColor: 'success',
      statusIcon: 'checkmark',
    }),
  }),
}));

// Mock the utility functions
jest.mock('../../utils', () => ({
  getTagStatus: jest.fn().mockReturnValue('success'),
  getBannerTagStatusLabel: jest.fn().mockReturnValue('Accepted'),
  getBannerTagIcon: jest.fn().mockReturnValue('checkmark'),
}));

// Mock the Everest components
jest.mock('@ceridianhcm/components', () => ({
  MediaObject: ({ media, title, testId }: any) => (
    <div data-testid={testId}>
      {media}
      {title}
    </div>
  ),
  IconButton: ({ onClick, testId, iconName }: any) => (
    <button data-testid={testId} onClick={onClick} data-icon={iconName}>
      Back Button
    </button>
  ),
  Tag: ({ label, testId }: any) => (
    <span data-testid={testId} className="mock-tag">
      {label}
    </span>
  ),
  TTagStatus: 'info',
  TIconName: 'information',
}));

describe('LetterStartSection Component', () => {
  const defaultProps = {
    iconName: 'arrowLeft' as const,
    omniTitle: 'Test Title',
  };

  const mockNavigateTo = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Update the mock implementation for each test
    jest.spyOn(require('@context/NavigationContext'), 'useNavigation').mockImplementation(() => ({
      navigateTo: mockNavigateTo,
      getNavigationData: () => ({
        subject: 'Test Letter Subject',
        timestamp: '2025-01-01',
        status: 'Accepted',
        statusColor: 'success',
        statusIcon: 'checkmark',
      }),
    }));
  });

  it('renders without crashing', () => {
    render(<LetterStartSection {...defaultProps} />);
    expect(screen.getByText('Test Letter Subject')).toBeInTheDocument();
  });

  it('renders with default props when none provided', () => {
    render(<LetterStartSection iconName="arrowLeft" />);
    expect(screen.getByText('Test Letter Subject')).toBeInTheDocument();
    expect(screen.getByTestId('omni-single-letter-start-button-test')).toBeInTheDocument();
  });

  it('renders with custom icon name', () => {
    render(<LetterStartSection {...defaultProps} iconName="copy" />);
    const button = screen.getByTestId('omni-single-letter-start-button-test');
    expect(button).toHaveAttribute('data-icon', 'copy');
  });

  it('navigates to letters view when back button is clicked', () => {
    render(<LetterStartSection {...defaultProps} />);
    const backButton = screen.getByTestId('omni-single-letter-start-button-test');

    fireEvent.click(backButton);

    expect(mockNavigateTo).toHaveBeenCalledWith('letters');
    expect(mockNavigateTo).toHaveBeenCalledTimes(1);
  });

  describe('Navigation Data Handling', () => {
    it('displays letter subject from navigation data', () => {
      jest.spyOn(require('@context/NavigationContext'), 'useNavigation').mockImplementation(() => ({
        navigateTo: mockNavigateTo,
        getNavigationData: () => ({
          subject: 'Custom Letter Subject',
          timestamp: '2025-01-01',
          status: 'Accepted',
        }),
      }));

      render(<LetterStartSection {...defaultProps} />);
      expect(screen.getByText('Custom Letter Subject')).toBeInTheDocument();
    });

    it('displays tag with proper status', () => {
      render(<LetterStartSection {...defaultProps} />);
      expect(screen.getByTestId('omni-tag-status-test-id')).toBeInTheDocument();
    });

    it('handles missing status gracefully', () => {
      // Mock utility functions to return empty values for empty status
      const utilsModule = require('../../utils');
      utilsModule.getBannerTagStatusLabel.mockReturnValue('');

      // Mock context with missing status
      jest.spyOn(require('@context/NavigationContext'), 'useNavigation').mockImplementation(() => ({
        navigateTo: mockNavigateTo,
        getNavigationData: () => ({
          subject: 'No Status Letter',
          timestamp: '2025-01-01',
          status: '',
        }),
      }));

      render(<LetterStartSection {...defaultProps} />);
      expect(screen.getByText('No Status Letter')).toBeInTheDocument();
      // No tag should be rendered when tag label is empty
      expect(screen.queryByTestId('omni-tag-status-test-id')).toBeNull();
    });

    it('handles undefined navigation data gracefully', () => {
      // Mock context with undefined navigation data
      jest.spyOn(require('@context/NavigationContext'), 'useNavigation').mockImplementation(() => ({
        navigateTo: mockNavigateTo,
        getNavigationData: () => undefined,
      }));

      render(<LetterStartSection {...defaultProps} />);
      // Should use default subject when navigation data is undefined
      expect(screen.getByText('Letter Details')).toBeInTheDocument();
    });
  });
});
