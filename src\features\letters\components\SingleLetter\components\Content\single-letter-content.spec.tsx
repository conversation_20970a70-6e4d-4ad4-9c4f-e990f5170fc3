/* eslint-disable react/display-name */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { TBreakpoint } from '@components/PageShell/usePageShell';
// Import the mocked hook to control it in tests
import { usePageShell } from '@components/PageShell';
import { IEmployeeLetter } from '@/models';

import { SingleLetterContent } from './SingleLetterContent';
import { getLetterDimensions } from '../single-letter.constants';

// Mock sanitizeHtml utility
jest.mock('@utils/sanitizeUtils', () => ({
  sanitizeHtml: jest.fn().mockImplementation((html) => html),
}));

// Mock PageShell hook
jest.mock('@components/PageShell', () => ({
  usePageShell: jest.fn().mockReturnValue({
    breakpoint: 'lg',
  }),
}));

// Mock all external components simply
jest.mock('@ceridianhcm/components', () => ({
  Image: ({ id, alt }: { id: string; alt: string }) => <img data-testid={id} alt={alt} />,
  SectionPanel: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SectionPanelRow: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('@components', () => {
  const Container = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
  Container.Body = ({ children, testId }: { children: React.ReactNode; testId: string }) => (
    <div data-testid={testId}>{children}</div>
  );
  return { Container };
});

jest.mock('@components/Wrapper', () => ({
  Wrapper: ({
    children,
    testId,
    minWidth,
    maxWidth,
    verticalPadding,
    horizontalPadding,
  }: {
    children: React.ReactNode;
    testId?: string;
    minWidth?: string;
    maxWidth?: string;
    verticalPadding?: string;
    horizontalPadding?: string;
  }) => (
    <div
      data-testid={testId}
      data-min-width={minWidth}
      data-max-width={maxWidth}
      data-vertical-padding={verticalPadding}
      data-horizontal-padding={horizontalPadding}
    >
      {children}
    </div>
  ),
}));

describe('SingleLetterContent', () => {
  const defaultProps = {
    id: 'test-content',
    testId: 'test-content-id',
    selectedLetter: {
      SubmittedLetterHtml: '<p>Test content</p>',
    } as IEmployeeLetter,
  };

  describe('Basic Rendering', () => {
    it('renders the company logo', () => {
      render(<SingleLetterContent {...defaultProps} />);
      expect(screen.getByTestId('dayforce-logo')).toBeInTheDocument();
    });

    it('renders the letter content', () => {
      render(<SingleLetterContent {...defaultProps} />);
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content).toBeInTheDocument();
      expect(content.innerHTML).toBe(defaultProps.selectedLetter.SubmittedLetterHtml);
    });

    it('renders with empty content', () => {
      render(
        <SingleLetterContent
          {...defaultProps}
          selectedLetter={{ ...defaultProps.selectedLetter, SubmittedLetterHtml: '' }}
        />,
      );
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content.innerHTML).toBe('');
    });

    it('renders with long content', () => {
      const longHtml = '<p>'.concat('Very long content. '.repeat(100), '</p>');
      render(
        <SingleLetterContent
          {...defaultProps}
          selectedLetter={{ ...defaultProps.selectedLetter, SubmittedLetterHtml: longHtml }}
        />,
      );
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content.innerHTML).toBe(longHtml);
    });

    it('renders nested blockquotes properly', () => {
      const blockquoteHtml = `
        <div>
          <blockquote>
            <blockquote>
              <blockquote>
                <b>Private and Confidential</b>
              </blockquote>
            </blockquote>
          </blockquote>
          <div>Test content after blockquotes</div>
        </div>
      `;
      render(
        <SingleLetterContent
          {...defaultProps}
          selectedLetter={{ ...defaultProps.selectedLetter, SubmittedLetterHtml: blockquoteHtml }}
        />,
      );
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content).toBeInTheDocument();
      expect(content.innerHTML).toContain('Private and Confidential');

      // Get the rendered blockquote elements
      const blockquotes = content.querySelectorAll('blockquote');
      expect(blockquotes.length).toBeGreaterThan(0);
    });

    it('renders deeply nested blockquotes with multiple levels', () => {
      const deepBlockquoteHtml = `
        <div><blockquote><blockquote><blockquote><blockquote><blockquote><blockquote><blockquote><blockquote>
        <b>Private and Confidential<br>&nbsp;</b>
        </blockquote></blockquote></blockquote></blockquote></blockquote></blockquote></blockquote></blockquote></div>
      `;
      render(
        <SingleLetterContent
          {...defaultProps}
          selectedLetter={{ ...defaultProps.selectedLetter, SubmittedLetterHtml: deepBlockquoteHtml }}
        />,
      );
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content).toBeInTheDocument();
      expect(content.innerHTML).toContain('Private and Confidential');

      // Verify we have multiple nested blockquotes
      const blockquotes = content.querySelectorAll('blockquote');
      expect(blockquotes.length).toBeGreaterThan(1);
    });
  });

  describe('Responsive Dimensions', () => {
    it.each<TBreakpoint>(['xs', 'sm', 'md', 'lg', 'xl'])(
      'renders with correct dimensions for %s breakpoint',
      (breakpoint) => {
        // Mock the breakpoint
        (usePageShell as jest.Mock).mockReturnValue({
          breakpoint,
        });

        // Get expected dimensions for this breakpoint
        const dimensions = getLetterDimensions(breakpoint);

        render(<SingleLetterContent {...defaultProps} />);

        // Check container wrapper has correct dimensions
        const containerWrapper = screen.getByTestId(`${defaultProps.id}-container-panel-wrapper`);
        expect(containerWrapper).toHaveAttribute('data-min-width', dimensions.containerWidth);
        expect(containerWrapper).toHaveAttribute('data-max-width', dimensions.containerWidth);

        // Check inner content wrapper has correct dimensions
        const contentInnerWrapper = screen.getByTestId(`${defaultProps.id}-content-section-panel-wrapper-inner`);
        expect(contentInnerWrapper).toHaveAttribute('data-min-width', dimensions.contentWidth);
        expect(contentInnerWrapper).toHaveAttribute('data-max-width', dimensions.contentWidth);
      },
    );
  });

  describe('Component Structure', () => {
    it('renders all required wrapper components', () => {
      render(<SingleLetterContent {...defaultProps} />);

      // Verify main container wrapper
      expect(screen.getByTestId(`${defaultProps.id}-container-panel-wrapper`)).toBeInTheDocument();

      // Verify content container
      expect(screen.getByTestId(`${defaultProps.testId}-container-body`)).toBeInTheDocument();

      // Verify inner wrapper
      expect(screen.getByTestId(`${defaultProps.id}-content-section-panel-wrapper-inner`)).toBeInTheDocument();
    });

    it('renders content in correct order', () => {
      render(<SingleLetterContent {...defaultProps} />);
      const contentWrapper = screen.getByTestId(`${defaultProps.id}-content-section-panel-wrapper-inner`);
      const children = contentWrapper.childNodes;

      // First child should be the logo wrapper
      expect(children[0]).toHaveClass('company-logo-wrapper');

      // Second child should be the letter content
      expect(children[1]).toHaveAttribute('data-testid', `${defaultProps.testId}-letter-content`);
    });
  });

  describe('Props Handling', () => {
    it('handles missing testId prop', () => {
      const { id, selectedLetter } = defaultProps;
      render(<SingleLetterContent id={id} testId="" selectedLetter={selectedLetter} />);
      expect(screen.getByTestId(`${id}-container-panel-wrapper`)).toBeInTheDocument();
    });

    it('handles special characters in content', () => {
      const specialContent = '<p>&amp; &lt; &gt; " \'</p>';
      render(
        <SingleLetterContent
          {...defaultProps}
          selectedLetter={{ ...defaultProps.selectedLetter, SubmittedLetterHtml: specialContent }}
        />,
      );
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content.innerHTML).toBe(specialContent);
    });
  });
});
