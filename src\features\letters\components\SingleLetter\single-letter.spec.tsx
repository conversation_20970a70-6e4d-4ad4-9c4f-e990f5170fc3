import React from 'react';
import { render, screen } from '@testing-library/react';
import { IEmployeeLetter } from '@/models';
import { SingleLetter } from './SingleLetter';

// Mock sub-components
jest.mock('./components/Content/SingleLetterContent', () => ({
  SingleLetterContent: ({ testId, sanitizedHtml }: { testId: string; sanitizedHtml: string }) => (
    <div data-testid={testId}>
      <div data-testid={`${testId}-letter-content`}>{sanitizedHtml}</div>
    </div>
  ),
}));

jest.mock('./components/NotficationBanner/SingleLetterNotificationBanner', () => ({
  SingleLetterNotificationBanner: ({ testId, selectedLetter }: { testId: string; selectedLetter: IEmployeeLetter }) => (
    <div data-testid={testId}>Notification: {selectedLetter.Subject}</div>
  ),
}));

// Mock utilities
jest.mock('@utils/sanitizeUtils', () => ({
  sanitizeHtml: jest.fn((html) => html),
}));

// Mock external components
jest.mock('@ceridianhcm/components', () => ({
  SectionPanel: ({ children, testId }: { children: React.ReactNode; testId?: string }) => (
    <div data-testid={testId}>{children}</div>
  ),
  SectionPanelRow: ({ children, testId }: { children: React.ReactNode; testId?: string }) => (
    <div data-testid={testId}>{children}</div>
  ),
}));

describe('SingleLetter', () => {
  const mockLetter: IEmployeeLetter = {
    SubmittedLetterHtml: '<p>Test letter content</p>',
    Subject: 'Test Letter',
    AcceptTimestamp: null,
    RejectTimestamp: null,
    ExpirationTimestamp: '2025-12-31T23:59:59Z',
  } as IEmployeeLetter;

  const defaultProps = {
    id: 'test-letter',
    testId: 'test-letter-id',
    selectedLetter: mockLetter,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with default props', () => {
      render(<SingleLetter {...defaultProps} />);
      expect(screen.getByTestId('test-letter-id')).toBeInTheDocument();
    });

    it('shows "No letter selected" when selectedLetter is null', () => {
      render(<SingleLetter {...defaultProps} selectedLetter={null} />);
      expect(screen.getByText('No letter selected')).toBeInTheDocument();
    });

    it('shows "No letter selected" when selectedLetter is undefined', () => {
      render(<SingleLetter {...defaultProps} selectedLetter={undefined} />);
      expect(screen.getByText('No letter selected')).toBeInTheDocument();
    });

    it('renders correct section panels and rows', () => {
      render(<SingleLetter {...defaultProps} />);

      expect(screen.getByTestId('test-letter-id-section-panel')).toBeInTheDocument();
      expect(screen.getByTestId('test-letter-id-notification-panel-row')).toBeInTheDocument();
    });
  });
});
