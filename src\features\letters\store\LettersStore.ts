import { register } from '@ceridianhcm/platform-state';
import type { IEmployeeLetter } from '@models/employee';
import type { TEmployeeIdParam } from '@/typings';
import { getEmployeeLetters } from '../api/lettersApi';
import { ILetterTableVM, mapLettersToTableVM } from '../utils';

/**
 * Platform State store for Letters functionality
 */
export class LettersStoreClass {
  public letters: ILetterTableVM[] = [];
  public isLoading = false;
  public error: string | null = null;

  // Raw data for internal use
  private rawLetters: IEmployeeLetter[] = [];

  /**
   * Initialize method is called by Platform State when the store is registered
   * This runs before any component tries to use the store
   */
  initialize() {
    console.log('LettersStore initialized');
    // We don't need to fetch data here - components should explicitly call fetch
  }

  /**
   * Fetch employee letters data and update store
   * @param employeeId Optional employee ID (defaults to 1046)
   * @returns Processed letters view model data
   */
  public async fetchEmployeeLetters(employeeId?: number): Promise<ILetterTableVM[]> {
    // set loading state - (trigger reactivity)
    this.isLoading = true;
    this.error = null;

    try {
      const payload: TEmployeeIdParam = {
        employeeId: employeeId || 1046,
      };

      const response = await getEmployeeLetters(payload);

      // store the raw data
      this.rawLetters = response;

      // transform data to view models
      const mappedData = mapLettersToTableVM(response);

      // Update the public property - (trigger a re-render)
      this.letters = mappedData;

      return mappedData;
    } catch (error) {
      console.error('Error fetching employee letters:', error);

      // Set error state - (trigger reactivity)
      this.error = 'Failed to fetch employee letters';
      throw new Error(this.error);
    } finally {
      // Update loading state - (trigger reactivity)
      this.isLoading = false;
    }
  }

  /**
   * Get a specific letter by index
   * @param index Letter index
   * @returns The original letter data
   */
  getOriginalLetterByIndex(index: number): IEmployeeLetter | undefined {
    return this.rawLetters[index];
  }
}

// Register the store with Platform State
export const LettersStore = register(LettersStoreClass, 'LettersStore');
