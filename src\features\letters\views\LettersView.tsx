import React, { useEffect } from 'react';
import { useSource } from '@ceridianhcm/platform-state';
import { ViewSection } from '@components/ViewSection';
import { EmployeeLetterMockData } from '@mocks/employee';
import { useNavigation } from '@context/NavigationContext';
import { LettersTable } from '../components';
import { useLetters } from '../context/LettersContext';
import { LettersStore } from '../store';

export interface LettersViewProps {
  dataTestId?: string;
}

export const LettersView = ({ dataTestId = 'letters-view-test-id' }: LettersViewProps) => {
  const { navigateTo } = useNavigation();
  const { setSelectedLetter } = useLetters();

  // access the LettersStore using platform state
  const { letters, fetchEmployeeLetters } = useSource(LettersStore);

  // Initialize the store on component mount
  useEffect(() => {
    fetchEmployeeLetters();
  }, []);

  const onSelectedLetterChange = (rowIndex: number) => {
    // Get original data for context (if needed)
    const selectedLetter = EmployeeLetterMockData[rowIndex];
    if (!selectedLetter) {
      console.error('Selected letter not found for row index:', rowIndex);
      return;
    }

    setSelectedLetter(selectedLetter);

    // Get the VM from the store's letters property
    const selectedLetterVm = letters[rowIndex];
    if (!selectedLetterVm) {
      console.error('Selected letter VM not found for row index:', rowIndex);
      return;
    }

    const { subject, sentTimestamp, status } = selectedLetterVm;

    navigateTo('single-letter', {
      subject,
      timestamp: sentTimestamp,
      status,
    });
  };

  return (
    <ViewSection testId={dataTestId} title="Letters">
      <LettersTable data={letters} onSelectedLetterChange={onSelectedLetterChange} />
    </ViewSection>
  );
};

export default LettersView;
