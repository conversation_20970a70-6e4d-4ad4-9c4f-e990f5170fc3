import React from 'react';
import { render, screen } from '@testing-library/react';
import { NavigationProvider } from '@context/NavigationContext';
import { LettersProvider } from '../context/LettersContext';
import { LettersView } from './LettersView';
import { type LettersViewProps } from './LettersView';
import userEvent from '@testing-library/user-event';
import { EmployeeLetterMockData } from '@mocks/employee';

// Mock the Platform State
jest.mock('@ceridianhcm/platform-state', () => {
  const mockLettersStore = {
    letters: [{ subject: 'Test Letter 1', sentTimestamp: '2023-01-01', status: 'Completed' }],
    isLoading: false,
    error: null,
    fetchEmployeeLetters: jest
      .fn()
      .mockResolvedValue([{ subject: 'Test Letter 1', sentTimestamp: '2023-01-01', status: 'Completed' }]),
  };

  return {
    useSource: jest.fn().mockImplementation(() => {
      return mockLettersStore;
    }),
    register: jest.fn().mockReturnValue('MockedStore'),
  };
});

// Mock the components
jest.mock('../components', () => ({
  LettersTable: ({ onSelectedLetterChange }: { onSelectedLetterChange: (index: number) => void }) => (
    <div data-testid="letters-table-mock">
      Mock Letters Table
      <button data-testid="select-letter-btn" onClick={() => onSelectedLetterChange(0)}>
        Select First Letter
      </button>
      <button data-testid="select-invalid-letter-btn" onClick={() => onSelectedLetterChange(999)}>
        Select Invalid Letter
      </button>
      <button data-testid="select-letter-with-missing-vm-btn" onClick={() => onSelectedLetterChange(1)}>
        Select Letter With Missing VM
      </button>
    </div>
  ),
}));

jest.mock('@components/ViewSection', () => ({
  ViewSection: ({ testId, title, children }: { testId: string; title: string; children: React.ReactNode }) => (
    <div data-testid={testId}>
      <h1>{title}</h1>
      {children}
    </div>
  ),
}));

// Mock for employee letter data
jest.mock('@mocks/employee', () => ({
  EmployeeLetterMockData: [
    { id: 'letter-1', subject: 'Test Letter 1', status: 'Completed' },
    { id: 'letter-2', subject: 'Test Letter 2', status: 'In Progress' },
  ],
}));

// Mock the letters context
const mockSetSelectedLetter = jest.fn();
jest.mock('../context/LettersContext', () => ({
  ...jest.requireActual('../context/LettersContext'),
  LettersProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useLetters: () => ({
    setSelectedLetter: mockSetSelectedLetter,
  }),
}));

// Mock navigation hook with a spy
const mockNavigateTo = jest.fn();
jest.mock('@context/NavigationContext', () => ({
  ...jest.requireActual('@context/NavigationContext'),
  NavigationProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useNavigation: () => ({
    navigateTo: mockNavigateTo,
    currentPage: { id: 'letters' },
    pages: [],
    getNavigationData: jest.fn(),
  }),
}));

// Mock utils
jest.mock('../utils', () => {
  const originalModule = jest.requireActual('../utils');

  return {
    ...originalModule,
    mapLettersToTableVM: jest
      .fn()
      .mockReturnValue([{ subject: 'Test Letter 1', sentTimestamp: '2023-01-01', status: 'Completed' }]),
  };
});

// For testing VM not found scenario
const mockUtils = require('../utils');

// Mock console.error for testing error paths
const originalConsoleError = console.error;
const mockConsoleError = jest.fn();

describe('LettersView Component', () => {
  const defaultProps: LettersViewProps = {
    dataTestId: 'letters-view-test-id',
  };

  beforeAll(() => {
    console.error = mockConsoleError;
  });

  afterAll(() => {
    console.error = originalConsoleError;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockUtils.mapLettersToTableVM.mockReturnValue([
      { subject: 'Test Letter 1', sentTimestamp: '2023-01-01', status: 'Completed' },
    ]);
  });

  const renderWithProviders = (props: Partial<LettersViewProps> = {}) => {
    return render(
      <NavigationProvider>
        <LettersProvider>
          <LettersView {...defaultProps} {...props} />
        </LettersProvider>
      </NavigationProvider>,
    );
  };

  it('renders without crashing and displays expected elements', () => {
    renderWithProviders();

    expect(screen.getByTestId('letters-view-test-id')).toBeInTheDocument();
    expect(screen.getByText('Letters')).toBeInTheDocument();
    expect(screen.getByTestId('letters-table-mock')).toBeInTheDocument();
  });

  it('renders with default props when no props are provided', () => {
    renderWithProviders();

    expect(screen.getByTestId('letters-view-test-id')).toBeInTheDocument();
    expect(screen.getByText('Letters')).toBeInTheDocument();
    expect(screen.getByTestId('letters-table-mock')).toBeInTheDocument();
  });

  it('renders with custom dataTestId prop', () => {
    renderWithProviders({
      dataTestId: 'custom-view-test-id',
    });

    expect(screen.getByTestId('custom-view-test-id')).toBeInTheDocument();
    expect(screen.getByText('Letters')).toBeInTheDocument();
    expect(screen.getByTestId('letters-table-mock')).toBeInTheDocument();
  });

  it('renders ViewSection with correct title and children', () => {
    renderWithProviders();

    const viewSection = screen.getByTestId('letters-view-test-id');
    expect(viewSection).toBeInTheDocument();
    expect(screen.getByText('Letters')).toBeInTheDocument();
    expect(screen.getByTestId('letters-table-mock')).toBeInTheDocument();
  });

  it('navigates to single-letter page on valid letter selection', async () => {
    renderWithProviders();

    await userEvent.click(screen.getByTestId('select-letter-btn'));

    // Verify the correct letter is selected
    expect(mockSetSelectedLetter).toHaveBeenCalledWith(EmployeeLetterMockData[0]);

    // Verify navigation with correct parameters
    expect(mockNavigateTo).toHaveBeenCalledWith('single-letter', {
      subject: 'Test Letter 1',
      timestamp: '2023-01-01',
      status: 'Completed',
    });
  });

  it('handles invalid letter selection gracefully', async () => {
    renderWithProviders();

    await userEvent.click(screen.getByTestId('select-invalid-letter-btn'));

    // Should log error but not crash
    expect(mockConsoleError).toHaveBeenCalledWith('Selected letter not found for row index:', 999);

    // Should not call these functions for invalid selection
    expect(mockSetSelectedLetter).not.toHaveBeenCalled();
    expect(mockNavigateTo).not.toHaveBeenCalled();
  });

  it('handles missing letter VM data gracefully', async () => {
    // Mock the VM data to not include index 1
    mockUtils.mapLettersToTableVM.mockReturnValue([
      { subject: 'Test Letter 1', sentTimestamp: '2023-01-01', status: 'Completed' },
      // No entry for index 1
    ]);

    renderWithProviders();

    await userEvent.click(screen.getByTestId('select-letter-with-missing-vm-btn'));

    // Should call setSelectedLetter with the found letter
    expect(mockSetSelectedLetter).toHaveBeenCalledWith(EmployeeLetterMockData[1]);

    // Should log error about missing VM
    expect(mockConsoleError).toHaveBeenCalledWith('Selected letter VM not found for row index:', 1);

    // Should not navigate with invalid VM
    expect(mockNavigateTo).not.toHaveBeenCalled();
  });
});
