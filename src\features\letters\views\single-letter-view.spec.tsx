import React from 'react';
import { render, screen } from '@testing-library/react';
import { SingleLetterView } from './SingleLetterView';
import { LettersProvider } from '../context/LettersContext';
import { IEmployeeLetter } from '@/models';

// Mock the components
jest.mock('@components/Wrapper', () => ({
  Wrapper: ({ children, testId }: { children: React.ReactNode; testId: string }) => (
    <div data-testid={testId}>{children}</div>
  ),
}));

jest.mock('../components', () => ({
  SingleLetter: ({ selectedLetter, testId }: { selectedLetter?: IEmployeeLetter; testId: string }) => (
    <div data-testid={testId}>{selectedLetter ? `Letter: ${selectedLetter.Subject}` : 'No letter selected'}</div>
  ),
}));

// Mock letter data
const mockLetter: IEmployeeLetter = {
  Subject: 'Test Letter',
  SentTimestamp: '2025-01-01',
  OfferESignatureDeclinedReason: null,
  Documents: [],
  Approvers: [],
  TerminatedEmployeeLetter: false,
  HasBeenResolved: false,
  AcceptTimestamp: null,
  BrandingMgmtLogoInformationId: null,
  CancelledTimestamp: null,
  CCRecipientGuid: null,
  CCUserId: null,
  ClientId: 1,
  CultureId: 1,
  DFWorkFlowDataId: 1,
  DFWorkflowTemplateId: 1,
  EmployeeId: 1,
  ESignatureOrderId: null,
  ExpirationTimestamp: '2025-12-31',
  FromUserId: 1,
  InitiatedTimestamp: '2025-01-01',
  IsAcknowledged: false,
  IsAutomatedLetter: false,
  IsWorkFlowFailed: false,
  JobDescription: null,
  JobPostingApplicationId: null,
  LastModifiedTimestamp: '2025-01-01',
  LastModifiedUserId: 1,
  LettersGuid: null,
  LettersId: 1,
  LettersTemplateId: 1,
  OfferExpiresAfterXDays: 30,
  RecipientEmail: '<EMAIL>',
  RejectTimestamp: null,
  RescindTimestamp: null,
  SubmittedLetterHtml: '<div>Test</div>',
  SubmitterId: 1,
  SubmitToWorkflowTimestamp: '2025-01-01',
  TemplateLetterHtml: null,
  WorkflowComments: null,
  WorkflowRejectedTimestamp: null,
};

describe('SingleLetterView Component', () => {
  const defaultProps = {
    dataTestId: 'single-letter-view',
  };

  const renderWithProvider = (props = defaultProps, initialLetter: IEmployeeLetter | null = null) => {
    const MockLettersProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => (
      <LettersProvider>{children}</LettersProvider>
    );

    jest.spyOn(require('../context/LettersContext'), 'useLetters').mockImplementation(() => ({
      selectedLetter: initialLetter,
      setSelectedLetter: jest.fn(),
      clearSelectedLetter: jest.fn(),
    }));

    return render(
      <MockLettersProvider>
        <SingleLetterView {...props} />
      </MockLettersProvider>,
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with default props', () => {
      renderWithProvider();
      expect(screen.getByTestId('single-letter-view-wrapper')).toBeInTheDocument();
      expect(screen.getByTestId('single-letter-view')).toBeInTheDocument();
    });

    it('renders with custom testId', () => {
      renderWithProvider({ dataTestId: 'custom-test-id' });
      expect(screen.getByTestId('single-letter-view-wrapper')).toBeInTheDocument();
      expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
    });

    it('renders Wrapper with correct props', () => {
      renderWithProvider();
      const wrapper = screen.getByTestId('single-letter-view-wrapper');
      expect(wrapper).toBeInTheDocument();
    });
  });

  describe('Letter Content', () => {
    it('renders SingleLetter component with no selected letter', () => {
      renderWithProvider();
      const singleLetter = screen.getByTestId('single-letter-view');
      expect(singleLetter).toBeInTheDocument();
      expect(singleLetter).toHaveTextContent('No letter selected');
    });

    it('renders SingleLetter component with selected letter', () => {
      renderWithProvider(defaultProps, mockLetter);
      const singleLetter = screen.getByTestId('single-letter-view');
      expect(singleLetter).toBeInTheDocument();
      expect(singleLetter).toHaveTextContent(`Letter: ${mockLetter.Subject}`);
    });
  });

  describe('Error Handling', () => {
    it('handles null selected letter gracefully', () => {
      renderWithProvider(defaultProps, null);
      const singleLetter = screen.getByTestId('single-letter-view');
      expect(singleLetter).toBeInTheDocument();
      expect(singleLetter).toHaveTextContent('No letter selected');
    });

    it('handles undefined selected letter gracefully', () => {
      renderWithProvider(defaultProps, undefined as unknown as IEmployeeLetter);
      const singleLetter = screen.getByTestId('single-letter-view');
      expect(singleLetter).toBeInTheDocument();
      expect(singleLetter).toHaveTextContent('No letter selected');
    });
  });

  describe('Component Structure', () => {
    it('maintains proper nesting order', () => {
      renderWithProvider(defaultProps, mockLetter);
      const wrapper = screen.getByTestId('single-letter-view-wrapper');
      const letter = screen.getByTestId('single-letter-view');

      expect(wrapper).toContainElement(letter);
    });

    it('passes correct props to SingleLetter', () => {
      renderWithProvider(defaultProps, mockLetter);
      const letter = screen.getByTestId('single-letter-view');
      expect(letter).toHaveTextContent(`Letter: ${mockLetter.Subject}`);
    });
  });
});
