import {
  CertificationGroupByStatusMockData,
  EmployeeCertificationsMockData,
  EmployeeCertificationsNoteMockData,
  EmployeeSkillsMockData,
} from '../mocks';
import type {
  ICertificationGroupByStatus,
  IEmployeeCertification,
  IEmployeeCertificationNoteResponse,
  IEmployeeSkill,
} from '../models';

import type { TCertificationStatusParam, TEmployeeIdParam } from '@/typings';
import { handleApiRequest } from '../../../api/baseApi';
import { API_ENDPOINTS } from '../../../api/apiConfig';

export const getEmployeeCertifications = async (payload: TEmployeeIdParam): Promise<IEmployeeCertification[]> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.QUALIFICATIONS_CERTIFICATIONS,
    EmployeeCertificationsMockData,
    'Failed to fetch employee certifications.',
    payload,
  );
};

export const getEmployeeCertificationsGroupByStatus = async (
  payload: TCertificationStatusParam,
): Promise<ICertificationGroupByStatus[]> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.QUALIFICATIONS_CERTIFICATIONS_GROUP_BY_STATUS,
    CertificationGroupByStatusMockData,
    'Failed to fetch certification group by status.',
    payload,
  );
};

export const getEmployeeCertificationNotes = async (
  payload: TEmployeeIdParam,
): Promise<IEmployeeCertificationNoteResponse> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.QUALIFICATIONS_NOTES_MODEL,
    EmployeeCertificationsNoteMockData,
    'Failed to fetch employee certification notes.',
    payload,
  );
};

export const getEmployeeSkills = async (payload: TEmployeeIdParam): Promise<IEmployeeSkill[]> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.QUALIFICATIONS_SKILLS,
    EmployeeSkillsMockData,
    'Failed to fetch employee skills.',
    payload,
  );
};
