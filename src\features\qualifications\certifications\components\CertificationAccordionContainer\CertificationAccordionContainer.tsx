import React from 'react';
import { Accordion, Container } from '@components';
import './certification-accordion-container.scss';

interface ICertificationAccordionContainerProps {
  id: string;
  title: string;
  isOpen: boolean;
  setOpen: (isOpen: boolean) => void;
  ariaLabel: string;
  children: React.ReactNode;
  dataTestId?: string;
}

export const CertificationAccordionContainer: React.FC<ICertificationAccordionContainerProps> = ({
  id,
  title,
  isOpen,
  setOpen,
  ariaLabel,
  children,
  dataTestId,
}) => (
  <Container
    id={`${id}-container`}
    ariaLabel={ariaLabel}
    dataTestId={`${dataTestId}-container`}
    className="certifications-accordion-container"
  >
    <Accordion open={isOpen} setOpen={setOpen} id={`${id}-accordion`} title={title}>
      {children}
    </Accordion>
  </Container>
);
